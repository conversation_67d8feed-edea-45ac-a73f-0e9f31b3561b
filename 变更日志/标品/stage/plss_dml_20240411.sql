-- --------------------------------
-- postgresql in docker container(postgresql运行在docker容器中时，需要修改dockerName为当前容器名和sql文件路径):
-- docker exec dockerName psql -d postgres -U postgres -f /var/lib/postgresql/data/plss_dml_20240411.sql

-- postgresql psql command(可以直接执行psql工具时，需要修改sql文件路径):
-- psql -d postgres -U postgres -f /var/lib/postgresql/data/plss_dml_20240411.sql

-- 忽略最后的 commit 警告 WARNING:  there is no transaction in progress
-- --------------------------------

-- 用query工具时，注释下面两行 
\connect - suwell;
\connect plss;

-- rc_permission
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (1, '仅看标题', 1, 'view_title_only', 1);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (2, '查看正文', 2, 'view_content', 1);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (3, '复制/添加素材', 4, 'copy_add_materials', 1);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (4, '打印', 8, 'print', 1);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (5, '下载/另存', 16, 'download_save', 1);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (6, '评论', 32, 'comment', 0);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (7, '新建/上传/重命名', 64, 'new_upload_rename', 0);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (8, '编辑', 128, 'edit', 0);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (9, '分享', 256, 'share', 0);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (10, '移动/删除', 512, 'move_delete', 0);
INSERT INTO "plss-record".rc_permission (id, name, pvalue, memo, project_use) VALUES (11, '权限设置', 1024, 'permission_set', 0);

-- rc_permission_group
INSERT INTO "plss-record".rc_permission_group (id, name, pvalue, memo, preset, del_flag) VALUES (1514955246085, '仅查看', 1, 'view_only', 1, 1);
INSERT INTO "plss-record".rc_permission_group (id, name, pvalue, memo, preset, del_flag) VALUES (1514972297989, '可查看', 3, 'can_view', 1, 0);
INSERT INTO "plss-record".rc_permission_group (id, name, pvalue, memo, preset, del_flag) VALUES (1515036998661, '可复制', 7, 'can_copy', 1, 0);
INSERT INTO "plss-record".rc_permission_group (id, name, pvalue, memo, preset, del_flag) VALUES (1515050227461, '可下载', 31, 'can_download', 1, 0);

-- rc_metadata
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (2471741050885, '入库位置', NULL, NULL, 1, '^.{0,2048}$', NULL, 1, 1, 0, '2023-12-01 20:00:51.64', 1612860642053, '2023-12-01 20:00:51.64', 1612860642053, 1, 1, NULL, 1, '{"len":2048,"selectItems":[],"unit":"","valueType":1}', 'rukuweizhi', 'rkwz', 1, 1, 1, 1, '文件入库位置', 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (3486077161733, '知识提取', 'Knowledge', NULL, 1, '^.{0,4096}$', NULL, 1, 1, 0, '2024-01-05 14:38:08.97', 1191407656453, '2024-01-05 14:38:08.97', 1191407656453, 1, 1, NULL, 1, '{"len":4096,"selectItems":[],"unit":"","valueType":1}', 'zhishitiqu', 'zstq', 1, 1, 1, 1, '', 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121716339461, '系统分类', 'SystemCategory', NULL, 1, '^.{0,4096}$', NULL, 2, 1, 0, '2023-09-20 17:08:24.451', 1, '2023-09-20 17:08:24.451', 1, 1, 1, NULL, 2, '{"valueType":1,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'xitongfenlei', 'xtfl', 1, 1, 1, 1, NULL, 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121745177861, '实体人物', 'PERSON', NULL, 8, '^.{0,4096}$', NULL, 2, 1, 0, '2023-09-20 17:10:17.101', 1, '2023-09-20 17:10:17.101', 1, 1, 1, NULL, 2, '{"valueType":8,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'shitirenwu', 'strw', 1, 1, 1, 1, NULL, 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121757193221, '实体地点', 'LOCATION', NULL, 8, '^.{0,4096}$', NULL, 2, 1, 0, '2023-09-20 17:11:04.036', 1, '2023-09-20 17:11:04.036', 1, 1, 1, NULL, 2, '{"valueType":8,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'shitididian', 'stdd', 1, 1, 1, 1, NULL, 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121765356037, '实体日期', 'DATE', NULL, 8, '^.{0,4096}$', NULL, 2, 1, 0, '2023-09-20 17:11:35.922', 1, '2023-09-20 17:11:35.922', 1, 1, 1, NULL, 2, '{"valueType":8,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'shitiriqi', 'strq', 1, 1, 1, 1, NULL, 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121736301573, '实体机构', 'ORGANIZATION', NULL, 8, '^.{0,4096}$', NULL, 2, 1, 0, '2023-09-20 17:09:42.428', 1, '2023-09-20 17:09:42.428', 1, 1, 1, NULL, 2, '{"valueType":8,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'shitijigou', 'stjg', 1, 1, 1, 1, NULL, 1);
INSERT INTO "plss-record".rc_metadata (id, name, name_en, definition, value_type, value_range, short_name, search_flag, status, orderby, create_time, create_by, modified_time, modified_by, opt_edit, opt_view, value_type_range, required, value_rule, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark, fixed_data) VALUES (1121727166725, '关键词', 'Keyword', NULL, 8, '^.{0,4096}$', NULL, 1, 1, 0, '2023-09-20 17:09:06.745', 1, '2023-11-17 19:45:16.23', 1191407656453, 1, 1, NULL, 1, '{"valueType":8,"len":4096,"max":100,"regex":"","unit":"","selectItems":[]}', 'guanjianci', 'gjc', 1, 1, 1, 1, NULL, 1);


-- rc_metadata_category
INSERT INTO "plss-record".rc_metadata_category (id, name, status, create_time, create_by, modified_time, modified_by) VALUES (1160482904581, '关联关系元数据', 1, '2023-09-22 11:12:16.345', 1, '2023-09-22 11:12:16.345', 1);
INSERT INTO "plss-record".rc_metadata_category (id, name, status, create_time, create_by, modified_time, modified_by) VALUES (1160484769029, '智能分析元数据', 1, '2023-09-22 11:12:23.628', 1, '2023-09-22 11:12:23.628', 1);

-- rc_metadata_category_metadata
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121716339461);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121727166725);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121736301573);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121745177861);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121757193221);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 1121765356037);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160482904581, 2471741050885);
INSERT INTO "plss-record".rc_metadata_category_metadata (category_id, md_id) VALUES (1160484769029, 3486077161733);


-- rc_node
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490549730565, '文件转换', 4, '{"checkBoxButtonList":[{"candidateCode":[501,-1],"code":-1,"desc":"转换成OFD格式"},{"candidateCode":[500,-1],"code":-1,"desc":"扫描件双层OFD处理"}],"nodeType":4,"switchBoxButton":{"candidateCode":[500,-1],"code":500,"desc":"文件转换"}}', '2', '6', 1, '文件转换', '2023-11-21 14:25:09.885', '2023-11-21 14:25:09.885', 1);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490549826053, '元数据自动提取', 6, '{"nodeType":6,"switchBoxButton":{"candidateCode":[601,-1],"code":601,"desc":"元数据自动提取"}}', '6', '7,4,5', 1, '按照文档类型和模型提取元数据', '2023-11-21 14:25:10.256', '2023-11-21 14:25:10.256', 3);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490549920517, '标引信息自动提取', 7, '{"checkBoxButtonList":[{"candidateCode":[700,-1],"code":700,"desc":"摘要提取"},{"candidateCode":[701,-1],"code":701,"desc":"主题分类"},{"candidateCode":[703,-1],"code":703,"desc":"主题词提取"},{"candidateCode":[704,-1],"code":704,"desc":"实体识别"}],"nodeType":7,"switchBoxButton":{"candidateCode":[706,-1],"code":706,"desc":"标引信息自动提取"}}', '7', '9,10,11,12,13,14', 1, '提取文档摘要、关键词、实体、自动分类等', '2023-11-21 14:25:10.627', '2023-11-21 14:25:10.627', 4);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490549730566, '知识提取', 12, '{"nodeType":12,"switchBoxButton":{"candidateCode":[707,-1],"code":707,"desc":"知识提取"}}', '', '', 1, '知识提取', '2024-01-05 14:25:09.885', '2024-01-05 14:25:09.885', 5);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490550011653, '人工核对与填充', 11, '{"nodeType":11,"switchBoxButton":{"candidateCode":[1800,-1],"code":1800,"desc":"人工核对与填充"}}', '2', '2', 1, '核对元数据和标引信息提取内容是否准确；也支持人工填充元数据和标引信息。', '2023-11-21 14:25:10.981', '2023-11-21 14:25:10.981', 6);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (3250934729733, '人工审核', 8, '{"nodeType":8,"popupBoxButton":{"candidateCode":[1401,-1],"code":1401,"desc":"配置审核人","idList":[],"sp":{"p":[],"rid":[]}},"switchBoxButton":{"candidateCode":[1400,-1],"code":1400,"desc":"人工审核"}}', '2', NULL, 1, '审核数据提取的准确性、是否涉及敏感内容', '2023-12-25 23:29:23.747', '2023-12-25 23:29:23.747', 7);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (4499920072453, '文件拆分', 5, '{"nodeType":5,"radioBoxButton":{"desc":"","radioBoxList":[{"candidateCode":[1702,-1],"code":-1,"desc":"自动拆分"},{"candidateCode":[1703,-1],"code":-1,"desc":"人工拆分"}]},"switchBoxButton":{"candidateCode":[1700,-1],"code":-1,"desc":"文件拆分"}}', '2', '6', 1, '书籍、刊物等按照文章进行拆分入库', '2024-02-20 10:43:32.769', '2024-02-20 10:43:32.769', 2);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490550124037, '入库', 9, '{"checkBoxButtonList":[{"candidateCode":[1501,-1],"code":-1,"desc":"系统自动生成封面"}],"nodeType":9,"switchBoxButton":{"candidateCode":[1500,-1],"code":1500,"desc":"入库"}}', '15,6,4', NULL, 1, '入库操作', '2023-11-21 14:25:11.418', '2023-11-21 14:25:11.418', 8);
INSERT INTO "plss-record".rc_node (id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num) VALUES (2490549604613, '文件上传', 1, '{"checkBoxButtonList":[{"candidateCode":[1105,-1],"code":1105,"desc":"允许文件入库人员修改入库位置"}],"nodeType":1,"repoPositionButton":{"candidateCode":[1101,-1],"code":1101,"desc":"设置默认入库位置：","repoIdList":[]},"switchBoxButton":{"candidateCode":[1100,-1],"code":1100,"desc":"文件上传"}}', '1', '2,3,4,15', 1, '上传需要入库的文件', '2023-11-21 14:25:09.34', '2023-11-21 14:25:09.34', 0);



-- 初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(5280182697221, 1, '文档入库-查看-本人创建数据权限', 1, 1, '1', '2024-03-26 17:21:53.646', '2024-03-26 17:21:53.646', 5140969202693, 1);
INSERT INTO "plss-system".sys_opt_data_pms(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(5280186761733, 2, '入库任务-查看-本人创建数据权限', 1, 1, '1', '2024-03-26 17:22:09.533', '2024-03-26 17:22:09.533', 5140969202693, 1);
INSERT INTO "plss-system".sys_opt_data_pms(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(5280190505221, 3, '入库方案配置-查看-本租户数据权限', 6, 1, '1', '2024-03-26 17:22:24.156', '2024-03-26 17:22:24.156', 5140969202693, 1);

-- 入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms(role_id, data_pms_id) VALUES(7, 5280182697221);
INSERT INTO "plss-system".sys_role_data_pms(role_id, data_pms_id) VALUES(7, 5280186761733);
INSERT INTO "plss-system".sys_role_data_pms(role_id, data_pms_id) VALUES(7, 5280190505221);

INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2, '系统-帐号安全-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 1, '2023-06-29 09:09:22.145263', '1', '2023-10-30 18:55:58.978468', '001', '初始化密码: 123456', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (4, '系统-帐号安全-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 1, '2023-06-29 09:11:04.595302', '1', '2023-06-29 09:11:04.595302', '1', '是否开启注册用户功能（true开启，false关闭）', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (5, '系统-账号安全-黑名单列表', 'sys.login.blackIPList', '', 'Y', 1, '2023-06-29 09:11:27.340841', '1', '2023-06-29 09:11:27.340841', '1', '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (13, '系统-访问域名', 'sys.visitdomain', '', 'Y', 1, '2023-09-06 11:10:38.854266', '1', '2023-09-06 11:10:38.854266', '1', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (811839495429, '系统外观设置', 'SYS_APPEARANCE_MAIN', '{"iconBucketName":"sysimgbucket","iconRelativePath":"2023/09/06/file_20230906173428A006.png","title":"123"}', 'Y', 1, '2023-09-06 16:54:08.079648', 'admin', '2023-09-06 17:34:30.244619', 'admin', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (849473740037, '外观-主题', 'sys.appearance.theme', '#C54334', 'Y', 1, '2023-09-08 09:44:16.595564', 'admin', '2024-03-15 15:30:26.007', '001', '请选择颜色', 'color', '主题颜色');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (849581427717, '外观-侧边栏主题', 'sys.appearance.side.theme', '#FFFFFF', 'Y', 1, '2023-09-08 09:51:17.246142', 'admin', '2023-11-22 14:16:40.752', '001', '请选择颜色', 'color', '侧边栏主题颜色');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (851053656325, '外观-facico', 'sys.appearance.fac.ico', '3465071322629', 'Y', 1, '2023-09-08 11:27:08.123255', 'admin', '2024-01-04 15:50:53.126', '001', '(图片尺寸为24x24pt,支持bmp、png、jpg格式,不超过2mb)', 'img', '网站icon');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853083002117, '外观-系统加载动画', 'sys.appearance.animation', '5122112206853', 'Y', 1, '2023-09-08 13:39:15.229922', 'admin', '2024-03-19 13:50:55.136', '001', '(图片尺寸为24x24pt,支持jpg、png、gif格式,不超过2mb)', 'img', '系统加载动画');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853104588549, '外观-系统网站标题', 'sys.appearance.title', '单位名称', 'Y', 1, '2023-09-08 13:40:39.545057', 'admin', '2024-01-12 11:42:14.888', '001', '请选择输入标题', 'text', '系统网站标题');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853127741957, '外观-logo', 'sys.appearance.logo', '5034682882309', 'Y', 1, '2023-09-08 13:42:09.986476', 'admin', '2024-03-15 14:58:59.188', '001', '(图片尺寸为24x24pt,支持bmp、png、jpg格式,不超过2mb)', 'img', 'logo图标');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853154462213, '外观-登录背景图', 'sys.appearance.background', '5034684164357', 'Y', 1, '2023-09-08 13:43:54.36263', 'admin', '2024-03-15 14:58:59.188', '001', '(图片尺寸为24x24pt,支持bmp、png、jpg格式,不超过3mb)', 'img', '登录背景图');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853183142405, '外观-默认分类图标', 'sys.appearance.category', '3465113896709', 'Y', 1, '2023-09-08 13:45:46.395381', 'admin', '2024-01-04 15:53:21.607', '001', '(图片尺寸为24x24pt,支持bmp、png、jpg格式,不超过2mb)', 'img', '默认分类图标');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (853342601989, '外观-OFD占位图', 'sys.appearance.ofdpreview', '3641466006021', 'Y', 1, '2023-09-08 13:56:09.283299', 'admin', '2024-01-12 15:14:38.006', '001', '(图片尺寸为9:16,支持bmp、png、jpg格式,不超过3mb)', 'img', '外观-OFD占位图');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (1147939787781, '文件服务-来源', 'PLSS_RECORD:RECORD_INFO:ORIGIN_LIST', '[{"origin":1,"originName":"OA推送"},{"origin":2,"originName":"文库管理员上传"},{"origin":3,"originName":"用户导入", "display":false},{"origin":999,"originName":"外采"},{"origin":8,"originName":"普通用户上传"},{"origin":5,"originName":"weboffice", "display":false},{"origin":6,"originName":"personal", "display":false}]', 'Y', 1, '2023-09-21 21:35:39.801397', 'admin', '2024-02-01 17:16:40.969997', '001', '文件基础服务的来源', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (1930170661381, '文件服务-文件状态', 'record.server.record.statusList', '[{"code":100,"desc":"待处理","subs":[{"code":101,"desc":"补充元数据"}]},{"code":200,"desc":"处理中","subs":[{"code":201,"desc":"转换中"},{"code":202,"desc":"OCR识别中"},{"code":203,"desc":"AI处理中"},{"code":204,"desc":"系统检测中"}]},{"code":400,"desc":"已入库","subs":[{"code":999,"desc":"文件处理完毕"}]},{"code":500,"desc":"异常制度","subs":[{"code":501,"desc":"转换异常"},{"code":502,"desc":"OCR异常"},{"code":503,"desc":"AI异常"},{"code":504,"desc":"文件异常"}]},{"code":600,"desc":"服务异常","subs":[{"code":601,"desc":"OCR服务异常"},{"code":602,"desc":"转换服务异常"},{"code":603,"desc":"AI服务异常"}]}]', 'Y', 1, '2023-10-27 06:22:09.152843', '001', '2023-11-04 09:10:12.229806', '001', '文件服务-文件状态', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2159252298245, '网关服务-白名单', 'ignore.whites', '/record/v1/readCallBack/info,/record/v1/document/checkUploaded', 'Y', 1, '2023-11-06 14:56:19.296238', '001', '2024-01-29 15:26:05.361748', '001', '使用逗号倾巢分隔多个', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2229137626885, 'system服务', 'system.tableheader.tableNames', 'rc_record', 'Y', 1, '2023-11-09 18:46:08.859496', '001', '2023-11-16 14:59:13.56321', '001', '单项表头配置默认表名称', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2246911952389, 'system服务', 'system.tableheader.repoConfigId', '2398227485701', 'Y', 1, '2023-11-10 14:03:19.820056', '001', '2023-11-17 10:59:07.634163', '001', '单项表头业务字段默认配置id', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2340297705733, 'open服务', 'open.sign.verify.enable', 'true', 'Y', 1, '2023-11-14 19:23:07.916284', '001', '2023-11-14 22:38:38.405336', '001', '开放平台签名验证开关', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2342602108677, '文件服务-文件类型顶级节点分组ID', 'record.type.groupId', '2340447010821', 'Y', 1, '2023-11-14 21:53:09.488549', '001', '2023-11-29 17:03:14.338896', '001', '文件服务-文件类型顶级节点分组ID', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2351361438213, '文件服务-文件类型分组默认未分组ID', 'record.type.defaultGroupId', '2340466262277', 'Y', 1, '2023-11-15 07:23:25.619526', '001', '2023-11-29 17:02:38.423823', '001', '文件服务-文件类型分组默认未分组ID', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2359783618565, 'record模块', 'record.borrow.borrowPermissionGroupId', '0', 'Y', 1, '2023-11-15 16:31:44.762527', '001', '2023-11-15 16:31:44.762527', '', '借阅权限组id', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2455299252485, '文件基础服务功能配置', 'record.feature.config.featureList', '[{"eventType":500,"eventTypeState":2},{"eventType":600,"eventTypeState":2},{"eventType":601,"eventTypeState":2},{"eventType":700,"eventTypeState":2},{"eventType":701,"eventTypeState":2},{"eventType":703,"eventTypeState":2},{"eventType":704,"eventTypeState":2}]', 'Y', 1, '2023-11-20 00:10:12.710453', '001', '2023-11-26 02:11:35.25206', '001', 'eventTypeState=1(同步调用)/2(异步调用)/3(关闭)；eventType=500(转OFD)/[600(提取正文:常规提取,若未提取出来,则依赖ocr和转换服务进行再次提取)/603(提取正文:常规提取,不依赖ocr和转换服务)]/601(提取元数据)/700(提取摘要)/701(提取分类)/703(提取关键词)/704(提取实体)', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2540719406597, '阅读器info回调地址前缀', 'sys.readercallback.url', '/record/v1/readCallBack/info', 'Y', 1, '2023-11-23 20:51:25.183475', '001', '2024-04-09 18:05:15.856159', '001', '阅读器info回调地址前缀', NULL, NULL);
-- INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2623124214123, '首页区域配置', 'sys.home.section', '{"filterRepositorys":[{"name":"全部","subjectLibraryId":0},{"name":"政策文件","subjectLibraryId":1699979465090961408,"filterMetadatas":[{"metadataId":"123123123","metadataName":"发文机关"},{"metadataId":"124654646","metadataName":"发文字号"}]},{"name":"公文样例","subjectLibraryId":4},{"name":"公文模板","subjectLibraryId":5}],"sections":[{"name":"工作","show":true,"nodes":[{"subjectLibraryId":"1699979465090961408","name":"公文","type":"4","sort":"1","icon":"","show":true},{"id":"3","name":"我的单位库","type":"3","sort":"2","icon":"","show":true}]},{"name:":"参考","show":true,"nodes":[{"id":"1637118929267564544","name":"领导讲话","type":"1","sort":"1","icon":"","show":true},{"id":"1637118929498261264","name":"政策文件","type":"1","sort":"2","icon":"","show":true},{"id":"1637118929498271264","name":"法律法规","type":"1","sort":"3","icon":"","show":true},{"id":"1702520761195548672","name":"公开文库","type":"1","sort":"4","icon":"","show":true},{"id":"1702520761192348672","name":"公文样例","type":"1","sort":"5","icon":"","show":true},{"id":"1702520761436768672","name":"公文模板","type":"1","sort":"6","icon":"","show":true}]},{"name:":"人民数科","show":true,"nodes":[{"id":"1700445326803484672","name":"时政评论","type":"1","sort":"1","icon":"","show":true},{"id":"1700445382013108224","name":"党政资料","type":"1","sort":"2","icon":"","show":true},{"id":"1700445441706442752","name":"人民日报","type":"1","sort":"3","icon":"","show":true},{"id":"1700445501139730432","name":"民生周刊","type":"1","sort":"4","icon":"","show":true}]}]}', 'Y', 1, '2023-11-28 21:13:03', '001', '2023-11-28 21:12:15', '001', '首页区域配置', NULL, NULL);
-- INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2632492375893, '主题库配置', 'sys.topic.library', '[{"repoId":"1","subjectLibraryId":2,"module":"wps-client","alias":"总书记讲话"},{"repoId":"2","subjectLibraryId":9,"module":"wps-client","alias":"法律法规"},{"repoId":"3","subjectLibraryId":8,"module":"wps-client","alias":"政策文件"},{"repoId":"4","subjectLibraryId":10,"module":"wps-client","alias":"人民日报"},{"repoId":"5","subjectLibraryId":11,"module":"wps-client","alias":"习近平用典"},{"repoId":"6","subjectLibraryId":0,"module":"wps-client","alias":"时政评论"},{"repoId":"7","subjectLibraryId":0,"module":"wps-client","alias":"政策解读"},{"repoId":"8","subjectLibraryId":0,"module":"wps-client","alias":"民生周刊"}]', 'Y', 1, '2023-11-28 21:46:32', '001', '2023-11-28 21:46:36', '001', '主题库配置', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2641626483461, 'weboffice', 'plugin.web-office.config', '{"url":"/office","appId":"OZNTZRFEEJPWCMWJ","accessKey":"OZNTZRFEEJPWCMWJ","secretKey":"SKwgtgkzwhenzrsq","version":"7.x"}', 'Y', 1, '2023-11-28 10:20:53.456461', '001', '2024-04-09 22:04:57.586367', '001', 'url:weboffice地址
appId:应用id[固定]
accessKey: ak
secretKey: sk
version: 版本分为6.x和7.x', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2647996221701, '文件服务-元数据的值映射转换类型配置', 'record.server.metadatavalueMaptype.mvMapTypeJson', '{"mdQueryNames":["发文字号","标题"],"mdVName":"效用状态","mdValueMapTypes":[{"mdValue":"有效","mdValueMapType":1},{"mdValue":"失效","mdValueMapType":2}]}', 'Y', 1, '2023-11-28 17:15:35.243394', '001', '2023-11-28 20:31:20.389326', '001', '功能需求:根据那多个元数据项查文件的关联的其他元数据项值的状态【例如: 根据发文字号、标题 查询文件的元数据值的效用状态:有效or失效】mdQueryNames=查询的元数据项的中文名称，mdVName=定位元数据项名称，通过名称查找具体元数据值的映射类型关系,mdValue=元数据项的值。元数据映射的含义类型，主要正对是非类型和固定值类型的元数据类型 映射为 存储值具体含义含义【例如：mdValue= 有效/失效，则 mdValueMapTyp=1/2】', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2651163710213, '组织机构层级', 'sys_org_level', '[{"levelCode":1,"levelName":"国家级"},{"levelCode":2,"levelName":"直属单位"},{"levelCode":3,"levelName":"省级"},{"levelCode":4,"levelName":"市级"},{"levelCode":5,"levelName":"县级"}]', 'Y', 1, '2023-11-28 20:41:48.24664', '001', '2023-11-29 21:12:13.749553', '001', '组织机构行政区域层级', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2669511064581, '搜索文档的配置', 'search.record.search.config', '{"enableVector":false,"termCount":2000,"contentMaxLen":50,"knn":{"kwVectorTimeOut":3,"k":5000,"candidates":10000}}', 'Y', 1, '2023-11-29 16:36:17.598975', '001', '2023-11-29 17:38:08.079686', '001', '{
"enableVector": false,
"termCount": 0,
"contentMaxLen": 0,
"knn": {
"kwVectorTimeOut": 0,
"k": 0,
"candidates": 0,
"@comment": {
"kwVectorTimeOut": "/**\n         * 搜索词', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2698732063492, '文档详细页面卡片默认配置', 'record.card.config.defaultCardConfig', '{"recordTypeName":"总书记重要讲话","cardList":[{"name":"基本信息","cardId":"1","children":[]},{"name":"主题文档","cardId":"3"}]}', 'Y', 1, '2023-12-01 00:18:42.127', '001', '2023-12-06 17:59:46.026', '001', '1 基本信息（暂不支持） 2 同期文档 3主题文档 4 知识提取（暂不支持）5系列文章', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2777934857733, '登陆设备验证开关', 'gatway.login.device.enable', 'false', 'Y', 1, '2023-12-04 14:15:08.041054', '001', '2023-12-05 13:52:12.247107', '001', '全局登陆设备验证开关', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2849380969221, 'ai请求超时时间', 'sys-ai-timeout', '30000', 'Y', 1, '2023-12-07 19:46:34.416978', '001', '2023-12-07 19:46:34.416978', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2849390668293, 'ai轮询流程超时时间', 'sys-ai-process-timeout', '600000', 'Y', 1, '2023-12-07 19:47:12.299107', '001', '2023-12-07 19:47:12.299107', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2849400632325, 'ai请求间隔时间', 'sys-ai-gap-time', '3000', 'Y', 1, '2023-12-07 19:47:51.221082', '001', '2023-12-07 19:47:51.221082', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2958278060549, '文件服务-个人库上传文件', 'record.server.personal.metadata.config', '[{"mdName":"诊疗标识","orderNum":1},{"mdName":"患者姓名","orderNum":2},{"mdName":"就诊日期","orderNum":3},{"mdName":"诊断","orderNum":4}]', 'Y', 1, '2023-12-12 17:56:13.678232', '001', '2023-12-12 17:56:13.678232', '', '文件服务-个人库上传文件
mdName=元数据名称
orderNum=排序号', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2959420849925, '文件服务-个人库携带编辑元数据上传', 'record.server.personal.recordTypeId', '2960804851205', 'Y', 1, '2023-12-12 19:10:37.69792', '001', '2023-12-12 20:43:40.680237', '001', '文件服务-个人库携带编辑元数据上传', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (2999500798981, '阅读回调版本', 'readCallBack.Version', '1', 'Y', 1, '2023-12-14 14:39:59.999545', '001', '2023-12-14 14:39:59.999545', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3092938874117, '数科授权服务地址', 'suwell.slc.url', 'http://172.16.16.17:8080', 'N', 1, '2023-12-18 20:03:12.480572', '001', '2024-04-09 17:03:20.263283', '001', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3092947063557, '数科授权服务授权名称', 'suwell.slc.license.name', 'gwk2', 'N', 1, '2023-12-18 20:03:44.468093', '001', '2023-12-18 20:12:16.86754', '001', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3128922592261, '文件服务-个人库上传入库是否同步ES', 'record.server.personal.switchBox', 'true', 'Y', 1, '2023-12-20 11:05:53.879987', '001', '2023-12-20 11:07:40.821758', '001', '文件服务-个人库上传入库是否同步ES', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3133740537349, '系统初始化角色信息', 'sys.isAdmin', 'true', 'Y', 1, '2023-12-20 16:19:33.978291', '001', '2023-12-20 16:19:33.978291', '', '服务是超管配置还是三员配置，超管配置填true，三员配置填false', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3138044069637, '系统初始化用户角色信息', 'sys.init.userRole', '{"2":"admin","3":"secadmin","4":"audadmin","5":""}', 'Y', 1, '2023-12-20 20:59:44.649262', '001', '2023-12-20 21:08:36.960113', '001', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3151239132933, '租户最大数量', 'sys.tenant.max.count', '999999', 'Y', 1, '2023-12-21 11:18:47.864066', '001', '2023-12-21 11:39:45.154873', '001', '租户最大数量，默认为9999', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3173575134213, '搜索关键词高亮颜色', 'sys.appearance.color.keyword', '#FFD700', 'Y', 1, '2023-12-22 11:32:57.872173', '001', '2023-12-22 11:33:46.426752', '001', '搜索关键词高亮颜色', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3173582454277, '搜索元数据高亮', 'sys.appearance.color.searchmeatdata', '#F4A460', 'Y', 1, '2023-12-22 11:33:26.463314', '001', '2023-12-22 11:33:33.661931', '001', '搜索元数据高亮颜色 ', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3196755979525, '文库目录图标', 'record.repository.icon', 'hide', 'Y', 1, '2023-12-23 12:42:08.050534', '001', '2023-12-23 12:49:57.220169', '001', 'show: 展示
其他值：隐藏', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3267460137477, '设置库管理员的最大数据量', 'sys.repo.admin.maxCount', '50', 'Y', 1, '2023-12-26 17:25:16.164834', '001', '2023-12-26 17:25:16.164834', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3295405276165, '文档处理服务-功能事件实现方式配置', 'dp.feature.impl.configKey', '[{"eventType":500,"implWay":1,"invokeWay":3,"platformWay":2,"state":1},{"eventType":501,"implWay":1,"invokeWay":1,"platformWay":1,"state":2},{"eventType":600,"implWay":1,"invokeWay":3,"platformWay":2,"state":1},{"eventType":601,"implWay":1,"invokeWay":3,"platformWay":2,"state":1},{"eventType":603,"implWay":2,"invokeWay":1,"platformWay":2,"state":2},{"eventType":700,"implWay":1,"invokeWay":3,"platformWay":4,"state":2},{"eventType":701,"implWay":1,"invokeWay":3,"platformWay":4,"state":2},{"eventType":703,"implWay":1,"invokeWay":3,"platformWay":4,"state":2},{"eventType":704,"implWay":1,"invokeWay":3,"platformWay":4,"state":2},{"eventType":705,"implWay":1,"invokeWay":3,"platformWay":4,"state":2},{"eventType":799,"implWay":1,"invokeWay":2,"platformWay":3,"state":1},{"eventType":707,"implWay":1,"invokeWay":1,"platformWay":4,"state":1}]', 'Y', 1, '2023-12-27 23:44:36.863332', '001', '2024-01-22 17:24:13.165984', '001', 'eventType=事件状态码(500-转换双层OFD，600-提取正文，603-提取正文(不依赖ocr服务)，601-提取元数据,700-摘要，701-分类，703-关键词，704-实体，705-向量化,799-AI全部事件)
implWay=实现方式(1-自研,2-内置开源库)
invokeWay=调用方式(1-SDK,2-MQ,3-HTTP回调)
platformWay=平台服务(1-转换平台服务 2-OCR平台服务 3-AI中台服务 4-AI算法平台服务)
state=状态开关(1-启用,2-禁用)', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3485194330117, '密码强度等级', 'sys.password.level', '1', 'Y', 1, '2024-01-05 13:40:40.361846', '001', '2024-01-05 16:24:36.948034', '001', '1-3的数字。
1代表至少包含字母+数字
2代表至少包含大小写字母+数字
3代表至少包含大小写数字+符号
默认为3。', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3485210247941, '密码最小长度', 'sys.password.length.min', '6', 'Y', 1, '2024-01-05 13:41:42.534324', '001', '2024-01-05 15:35:12.583184', '001', '密码要求的最小长度 6-10的数字。默认为6。', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3485309743109, '密码强制修改周期', 'config_force_update_password_cycle', '0', 'Y', 1, '2024-01-05 13:48:11.185379', '001', '2024-01-24 16:38:31.025486', '001', '0-99的数字。单位为月。输入0代表不要求修改。默认为0.注意只能输入规定数字，否则登陆报错。', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3552765801733, '文件服务-入库方案租户隔离列表', 'record.storage.plan.tenantConfigKey', '[]', 'Y', 1, '2024-01-08 14:59:51.414908', '001', '2024-01-08 14:59:51.414908', '', '文件服务-入库方案租户隔离列表', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3597894811909, '金山对接相关参数', 'kingsoft.v7.jsonParams', '{"gwbp":{"tenantId":1190120620295,"url":"http://172.16.19.95","AK":"DSRYWHEMZGIAVCFN","SK":"SKznrtpkpwxcpuhb","callbackUrl":"http://*************:8061/v1/kingSoft/redirect","userScope":"kso.user.base.read","frontUrl":"http://*************","mgtUrl":"http://*************:81"}}', 'Y', 1, '2024-01-10 15:57:56.612732', '001', '2024-01-10 16:07:46.677715', '001', '支持多个', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3648221496837, '切换api5提取封面方法', 'dp.api5.type', '2', 'Y', 1, '2024-01-12 22:34:25.225906', '001', '2024-01-12 22:34:25.225906', '', '1-文件   2-字节数组', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3729342909701, '系统初始化的角色id', 'sys.init.roleType', '[5]', 'Y', 1, '2024-01-16 14:35:45.745198', '001', '2024-01-29 10:31:33.17814', '001', '当系统为三员管理系统时填入：[2,3,4]    ，当系统为超级管理员系统时填入：[5]', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3731221007365, '登陆验证码', 'sys.captcha.turn', 'false', 'Y', 1, '2024-01-16 16:38:02.037', '001', '2024-03-15 17:20:14.702917', '001', '登录验证码', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3864463331077, '文档处理服务-功能对接其他配置', 'dp.feature.impl.buttJointOtherKey', '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":300,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply"}', 'Y', 1, '2024-01-22 17:12:39.892249', '001', '2024-04-10 10:23:51.936235', '001', 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3869415164165, '系统初始化角色信息', 'sys.init.roleInfo', '[
{"dateType": "1","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[],"delPowerRoleTypeList":[1]},
{"dateType": "2","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "3","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "4","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "5","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[5],"delPowerRoleTypeList":[1]},
{"dateType": "6","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "7","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "98","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "99","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1,2,5],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1,2,5]}
]', 'Y', 1, '2024-01-22 22:35:02.987829', '001', '2024-04-03 17:07:22.754123', 'admin', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (3980502479109, '系统默认元数据', 'sys.default.metadta', '[]', 'Y', 1, '2024-01-27 23:07:17.813352', '001', '2024-02-21 16:09:35.329071', '001', '控制文档类型中元数据默认状态（选中、禁用）。
required：必填
searchFlag：可被搜索
searchResultView：检索结果展示
detailsView：详情信息展示
optEdit：可编辑
optView：编辑页可见', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (4082438140677, '素材分类根节点', 'material.classify.categoryId', '4082410414597', 'Y', 1, '2024-02-01 13:43:43.993559', '001', '2024-02-01 13:43:43.993559', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (4082446525701, '素材标签根节点', 'material.tag.categoryId', '2774994106373', 'Y', 1, '2024-02-01 13:44:16.742534', '001', '2024-02-01 13:44:16.742534', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (4503458955013, '文档处理服务-线程池配置', 'dp.feature.thread-pool.configKey', '[{"threadName":"data-storage-callback","coreSize":50,"maximumPoolSize":500,"keepAliveTime":30,"blockingQueueSize":2000}]', 'Y', 1, '2024-02-20 14:33:56.548354', '001', '2024-02-20 16:34:26.276757', '001', 'threadName=线程名称
coreSize=核心线程数
maximumPoolSize=最大线程数
keepAliveTime=销毁时间(秒)
blockingQueueSize=队列深度', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (*************, '服务证书显示配置', 'plss.license.config', '{"gwkversion":"V1","V1":{"accountFlag":true,"wpsAccountFlag":true,"tenantNumNumFlag":true}}', 'Y', 1, '2024-02-23 11:03:55.572391', '001', '2024-02-23 11:03:55.572391', '', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (*************, '文件服务-入库文件格式类型效验', 'record.warehousing.uploadFileType', '["docx","doc","wps","xls","ofd","pdf","xlsx","dot","dotx","dotm","ppt","pot","pps","potm","ppsm","txt","tif","tiff","jpg","jpeg","png","zip","bmp","html"]', 'Y', 1, '2024-03-07 16:27:49.194659', '001', '2024-03-07 18:04:53.551309', '001', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (*************, '文件服务-入库文件格式异常提示语', 'record.warehousing.uploadErrorFileType', '该格式文件存在风险，无法入库。', 'Y', 1, '2024-03-07 16:28:33.863036', '001', '2024-03-07 19:09:11.466703', '001', '文件类型格式错误提示语', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (4945151599365, '文件服务-数据大屏标签统计', 'record.base.dataScreenTagCountSwitch', 'true', 'Y', 1, '2024-03-11 13:49:58.438181', '001', '2024-03-14 17:20:42.074817', '001', '文件服务-数据大屏标签开关', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178804941956101, '系统当前服务IP+端口', 'sys.host.port', 'http://*************:80', 'Y', 1, '2024-04-09 14:59:35.141', '001', '2024-04-09 15:58:37.428715', '001', '系统当前服务IP+端口', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178808093426693, '系统当前请求上下文路径', 'sys.host.context.path', '/plss', 'Y', 1, '2024-04-09 15:05:59.842', '001', '2024-04-09 20:56:22.381797', '001', '系统当前请求上下文路径', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178809385026565, 'AI中台平台IP+端口', 'ai.middle.host.port', 'http://************:8086', 'Y', 1, '2024-04-09 15:08:37.508', '001', '2024-04-09 15:14:02.629985', '001', 'AI中台平台IP+端口', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178810861773829, 'AI算法平台IP+端口', 'ai.algorithm.host.port', 'http://*************:8800', 'Y', 1, '2024-04-09 15:11:37.775', '001', '2024-04-09 15:11:37.775', '001', 'AI算法平台IP+端口', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178847794725893, 'WebOffice平台IP+端口', 'wo.host.port', 'http://************:80', 'Y', 1, '2024-04-09 16:26:46.192', '001', '2024-04-09 16:26:46.192', '001', 'WebOffice平台IP+端口', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (178991803384837, '系统当前请求API前缀', 'sys.api.prefix', '/prod-api', 'Y', 1, '2024-04-09 21:19:45.374', '001', '2024-04-09 21:19:45.374', '001', '系统当前请求API前缀', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES (179554578212357, '是否演示环境', 'sys.demo.environment.enable', '{"enable": false,"url": ""}', 'Y', 1, '2024-04-10 16:24:43.473', '001', '2024-04-10 16:34:14.937159', '001', '默认false  如开启需要填写注册文件链接如：{"enable": true,"url": "https://f.kdocs.cn/g/kMr9KioR/"}', NULL, NULL);


INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.564897', '停用状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4, 1, '显示', '1', 'sys_show_hide', '', 'primary', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:43.849125', '显示菜单');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (7, 2, '停用', '2', 'sys_normal_disable', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:44.496599', '停用状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.716879', '删除操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.865783', '授权操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.985232', '导出操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.114574', '导入操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:44.690362', '正常状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:44.865613', '停用状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.050818', '默认分组');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.163988', '系统分组');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.250223', '系统默认是');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.275146', '系统默认否');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.279961', '通知');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.287256', '公告');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.294071', '正常状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.381523', '关闭状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.473397', '其他操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.539621', '新增操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:45.627596', '修改操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.22073', '强退操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.333155', '生成操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.401502', '清空操作');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '1', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:43:46.487357', '正常状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (2688361371653, 0, '失败', '2', 'sys_logs_status', '', 'danger', 'N', '1', '001', '2023-11-30 13:03:31.609934', '001', '2023-11-30 13:16:10.069655', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (1, 1, '男', '0', 'sys_user_sex', '', 'primary', 'Y', '1', '1', '2023-05-19 00:40:19', '001', '2023-11-30 13:16:44.555354', '性别男');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (2, 2, '女', '1', 'sys_user_sex', '', 'warning', 'N', '1', '1', '2023-05-19 00:40:19', '001', '2023-11-30 13:17:03.911096', '性别女');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (5, 2, '隐藏', '2', 'sys_show_hide', '', 'danger', 'N', '1', '1', '2023-05-19 00:40:19', '001', '2023-11-30 13:24:02.536675', '隐藏菜单');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (2707290224901, 0, '禁用', '0', 'sys_sensitive_status', '', 'info', 'N', '1', '001', '2023-12-01 09:35:52.443852', '', '2023-12-01 09:35:52.443852', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (2707292741125, 0, '启用', '1', 'sys_sensitive_status', '', 'success', 'N', '1', '001', '2023-12-01 09:36:02.271893', '001', '2023-12-01 09:36:09.773652', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858611437061, 3, 'docx', 'docx', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:55:50.927824', '', '2024-03-07 15:55:50.927824', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858607570437, 2, 'doc', 'doc', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:55:35.824083', '001', '2024-03-07 15:55:59.120365', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (3094497999365, 0, '系统初始化', '1', 'sys_role_type', '', 'default', 'N', '1', '001', '2023-12-18 21:44:42.81273', '', '2023-12-18 21:44:42.81273', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (3094502995205, 1, '用户自定义', '2', 'sys_role_type', '', 'default', 'N', '1', '001', '2023-12-18 21:45:02.327362', '', '2023-12-18 21:45:02.327362', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4019381607685, 0, '协同提醒', '2', 'sys_message_type', '', 'default', 'N', '1', '001', '2024-01-29 17:18:29.406', '', '2024-01-29 17:18:29.406', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (3, 3, '未知', '2', 'sys_user_sex', '', 'info', 'N', '1', '1', '2023-05-19 00:40:19', '001', '2023-12-25 10:54:32.365425', '性别未知');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (6, 1, '启用', '1', 'sys_normal_disable', '', 'success', 'Y', '1', '1', '2023-05-19 00:40:19', '001', '2024-01-10 15:04:57.903865', '正常状态');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4019384489733, 0, '入库审核', '3', 'sys_message_type', '', 'default', 'N', '1', '001', '2024-01-29 17:18:40.664', '', '2024-01-29 17:18:40.664', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (2688359441157, 0, '成功', '1', 'sys_logs_status', '', 'success', 'N', '1', '001', '2023-11-30 13:03:24.071897', '001', '2024-01-29 16:50:44.555015', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4019387652613, 0, '入库驳回', '4', 'sys_message_type', '', 'default', 'N', '1', '001', '2024-01-29 17:18:53.019', '', '2024-01-29 17:18:53.019', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4019390280965, 0, '文档移除', '5', 'sys_message_type', '', 'default', 'N', '1', '001', '2024-01-29 17:19:03.286', '', '2024-01-29 17:19:03.286', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4019378222597, 0, '变更提醒', '1', 'sys_message_type', '', 'default', 'N', '1', '001', '2024-01-29 17:18:16.184', '', '2024-01-29 17:18:16.184', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593162214661, 0, '讨论', '1', 'sys_borrow_purpose', '', 'default', 'N', '1', '001', '2024-02-24 15:53:59.945239', '', '2024-02-24 15:53:59.945241', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593165020933, 1, '学习', '2', 'sys_borrow_purpose', '', 'default', 'N', '1', '001', '2024-02-24 15:54:10.901035', '', '2024-02-24 15:54:10.901038', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593169653509, 2, '科研', '3', 'sys_borrow_purpose', '', 'default', 'N', '1', '001', '2024-02-24 15:54:28.997867', '', '2024-02-24 15:54:28.997871', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593173245957, 3, '其他', '4', 'sys_borrow_purpose', '', 'default', 'N', '1', '001', '2024-02-24 15:54:43.032072', '001', '2024-02-24 15:55:06.528659', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593474717701, 0, '查阅', '1', 'sys_borrow_usage', '', 'default', 'N', '1', '001', '2024-02-24 16:14:20.658178', '', '2024-02-24 16:14:20.658182', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4593480866309, 1, '打印', '2', 'sys_borrow_usage', '', 'default', 'N', '1', '001', '2024-02-24 16:14:44.675139', '', '2024-02-24 16:14:44.675143', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4598203293701, 0, '待审批', '1', 'sys_borrow_status', '', 'warning', 'N', '1', '001', '2024-02-24 21:22:11.654568', '001', '2024-02-24 21:24:00.763533', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4598212492037, 1, '已借出', '2', 'sys_borrow_status', '', 'success', 'N', '1', '001', '2024-02-24 21:22:47.583395', '001', '2024-02-24 22:23:05.32932', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4598219409157, 2, '已过期', '3', 'sys_borrow_status', '', 'info', 'N', '1', '001', '2024-02-24 21:23:14.604515', '001', '2024-02-24 22:23:08.375315', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4598227276805, 3, '已拒绝', '4', 'sys_borrow_status', '', 'danger', 'N', '1', '001', '2024-02-24 21:23:45.336331', '001', '2024-02-24 22:23:10.965355', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858618651909, 4, 'wps', 'wps', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:56:19.110981', '', '2024-03-07 15:56:19.110981', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4658928325893, 2, '个人库', '3', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-02-27 15:15:38.774454', '001', '2024-02-27 18:43:57.816725', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4658919742725, 0, '工作库', '1', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-02-27 15:15:05.250426', '001', '2024-02-27 18:44:00.783278', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858625480965, 5, 'xls', 'xls', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:56:45.787389', '', '2024-03-07 15:56:45.787389', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858599864069, 0, 'ofd', 'ofd', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:55:05.721865', '', '2024-03-07 15:55:05.721865', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858602364165, 1, 'pdf', 'pdf', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:55:15.487462', '', '2024-03-07 15:55:15.487462', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858628876293, 6, 'xlsx', 'xlsx', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:56:59.049533', '', '2024-03-07 15:56:59.049533', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4658923971589, 1, '参考库', '2', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-02-27 15:15:21.766068', '001', '2024-02-27 16:24:30.266173', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858651747333, 7, 'dot', 'dot', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:58:28.391038', '001', '2024-03-07 15:58:32.04083', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858655073541, 8, 'dotx', 'dotx', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:58:41.382345', '', '2024-03-07 15:58:41.382345', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858657844997, 9, 'dotm', 'dotm', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:58:52.208357', '', '2024-03-07 15:58:52.208357', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858667098373, 10, 'ppt', 'ppt', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:59:28.354886', '', '2024-03-07 15:59:28.354886', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858669335813, 11, 'pot', 'pot', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:59:37.094365', '', '2024-03-07 15:59:37.094365', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858671438597, 12, 'pps', 'pps', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:59:45.308909', '', '2024-03-07 15:59:45.308909', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858673985029, 13, 'potm', 'potm', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 15:59:55.255999', '', '2024-03-07 15:59:55.255999', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858676009989, 14, 'ppsm', 'ppsm', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:00:03.166874', '', '2024-03-07 16:00:03.166874', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858682653701, 0, 'txt', 'txt', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:00:29.117989', '', '2024-03-07 16:00:29.117989', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858687293701, 16, 'tif', 'tif', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:00:47.242481', '', '2024-03-07 16:00:47.242481', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858689075205, 17, 'tiff', 'tiff', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:00:54.201952', '', '2024-03-07 16:00:54.201952', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858692434949, 18, 'jpg', 'jpg', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:01:07.325055', '', '2024-03-07 16:01:07.325055', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858693820677, 19, 'jpeg', 'jpeg', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:01:12.738884', '', '2024-03-07 16:01:12.738884', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858695578373, 20, 'png', 'png', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:01:19.604552', '', '2024-03-07 16:01:19.604552', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858702662917, 0, 'zip', 'zip', 'warehousing_upload_type', '22', 'default', 'N', '1', '001', '2024-03-07 16:01:47.278907', '', '2024-03-07 16:01:47.278907', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858890747653, 0, '该格式文件存在风险，无法入库。', 'error', 'warehousing_upload_tip', '', 'default', 'N', '1', '001', '2024-03-07 16:14:01.984754', '', '2024-03-07 16:14:01.984754', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4858697315589, 21, 'bmp', 'bmp', 'warehousing_upload_type', '', 'default', 'N', '1', '001', '2024-03-07 16:01:26.391178', '001', '2024-03-07 16:20:55.882294', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967669801221, 2, '停用', '2', 'sys_data_pms_state', '', 'default', 'N', '1', '001', '2024-03-12 14:16:00.163812', '', '2024-03-12 14:16:00.163812', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967665044485, 1, '启用', '1', 'sys_data_pms_state', '', 'default', 'N', '1', '001', '2024-03-12 14:15:41.585949', '', '2024-03-12 14:15:41.585949', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967419264773, 4, '文库管理', '4', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-03-12 13:59:41.504893', '', '2024-03-12 13:59:41.504893', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967415041029, 3, '入库方案配置', '3', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-03-12 13:59:25.005817', '001', '2024-03-12 13:59:51.159095', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967410781189, 2, '入库任务', '2', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-03-12 13:59:08.365967', '', '2024-03-12 13:59:08.365967', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967406459653, 1, '文档入库', '1', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-03-12 13:58:51.485423', '', '2024-03-12 13:58:51.485423', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967362336517, 6, '本租户数据权限', '6', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:55:59.130224', '', '2024-03-12 13:55:59.130224', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967357261573, 5, '本单位及下级单位数据权限（租户内）', '5', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:55:39.305173', '', '2024-03-12 13:55:39.305173', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967352238597, 4, '本单位数据权限', '4', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:55:19.684193', '', '2024-03-12 13:55:19.684193', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967347981061, 3, '本部门及下级部门数据权限', '3', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:55:03.053338', '', '2024-03-12 13:55:03.053338', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967341934085, 2, '本部门数据权限', '2', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:54:39.433117', '', '2024-03-12 13:54:39.433117', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967336664581, 1, '本人创建数据权限', '1', 'sys_data_pms_type', '', 'default', 'N', '1', '001', '2024-03-12 13:54:18.848425', '', '2024-03-12 13:54:18.848425', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967302604805, 2, '编辑', '2', 'sys_button_opt_type', '', 'default', 'N', '2', '001', '2024-03-12 13:52:05.803892', '001', '2024-03-12 13:52:27.849047', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4967295411973, 1, '查看', '1', 'sys_button_opt_type', '', 'default', 'N', '1', '001', '2024-03-12 13:51:37.711463', '001', '2024-03-12 13:52:17.002192', '');


INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (2, '菜单状态', 'sys_show_hide', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:34.81077', '1', '菜单状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (3, '系统开关', 'sys_normal_disable', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:34.952499', '1', '系统开关列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4, '任务状态', 'sys_job_status', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:35.208334', '1', '任务状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (5, '任务分组', 'sys_job_group', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:35.593448', '1', '任务分组列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (6, '系统是否', 'sys_yes_no', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:36.032991', '1', '系统是否列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (7, '通知类型', 'sys_notice_type', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:36.393123', '1', '通知类型列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (8, '通知状态', 'sys_notice_status', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:36.786381', '1', '通知状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (9, '操作类型', 'sys_oper_type', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:36.830708', '1', '操作类型列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (10, '系统状态', 'sys_common_status', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:37.033331', '1', '登录状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (1, '用户性别', 'sys_user_sex', '1', '2023-05-19 00:40:19', '1', '2023-06-29 09:48:34.506973', '1', '用户性别列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (2688340436741, '日志状态', 'sys_logs_status', '1', '2023-11-30 13:02:09.83575', '001', '2023-11-30 13:02:26.137768', '001', '日志状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (2707273703685, '敏感词状态', 'sys_sensitive_status', '1', '2023-12-01 09:34:47.908071', '001', '2023-12-01 09:34:47.908071', '', '敏感词状态列表');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (3094474759173, '角色类型', 'sys_role_type', '1', '2023-12-18 21:43:12.031167', '001', '2023-12-18 21:43:12.031167', '', '角色类型：1系统初始化2用户自定义');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4019341262597, '消息类型', 'sys_message_type', '1', '2024-01-29 17:15:51.809', '001', '2024-01-29 17:15:51.809', '', '消息列表的消息类型');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4593149963781, '借阅目的', 'sys_borrow_purpose', '1', '2024-02-24 15:53:12.089992', '001', '2024-02-24 15:53:12.089994', '', '借阅目的列表
申请借阅时展示');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4593156338693, '借阅权限', 'sys_borrow_usage', '1', '2024-02-24 15:53:36.985617', '001', '2024-02-24 15:53:36.985621', '', '借阅权限列表
申请借阅时展示');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4598156546821, '借阅状态', 'sys_borrow_status', '1', '2024-02-24 21:19:09.054859', '001', '2024-02-24 21:19:22.06198', '001', '借阅状态');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4658898178821, '库类型', 'rc_repo_type', '1', '2024-02-27 15:13:41.012969', '001', '2024-02-27 15:13:41.012969', '', '');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4858594566917, '入库上传格式', 'warehousing_upload_type', '1', '2024-03-07 15:54:45.029785', '001', '2024-03-07 15:54:45.029785', '', '');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4858882142213, '入库上传提示', 'warehousing_upload_tip', '1', '2024-03-07 16:13:28.369918', '001', '2024-03-07 16:13:28.369918', '', '');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4967326647813, '数据权限类型', 'sys_data_pms_type', '1', '2024-03-12 13:53:39.721203', '001', '2024-03-12 13:53:39.721203', '', '数据权限类型');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4967380660485, '数据权限业务模块类型', 'sys_biz_module_type', '1', '2024-03-12 13:57:10.707172', '001', '2024-03-12 13:57:10.707172', '', '数据权限业务模块类型');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4965230796293, '数据权限操作按钮类型', 'sys_button_opt_type', '1', '2024-03-12 11:37:12.804229', '001', '2024-03-12 13:58:14.041192', '001', '数据权限操作按钮类型');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES (4967655557893, '数据权限状态', 'sys_data_pms_state', '1', '2024-03-12 14:15:04.525772', '001', '2024-03-12 14:15:04.525772', '', '数据权限状态');


INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3992318260997, 699369213445, '导出', 3, '', '', 'metadata-category/index', '2', '1', 'F', '1', '1', 'system:metadata:export', '#', '001', '2024-01-28 11:56:33.216526', '', '2024-01-28 11:56:33.216526', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (966252410629, 0, '标签管理', 3, 'label', '', '', '1', '1', 'M', '1', '1', '', 'labels', 'admin', '2023-09-13 16:27:02.365619', '001', '2024-01-30 09:37:09.997787', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1, 0, '系统管理', 9, 'system', '', '', '1', '1', 'M', '1', '1', '', 'systemManage', '1', '2023-05-19 00:40:18', '001', '2024-01-30 09:37:42.783934', '系统管理目录', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3992321745157, 699369213445, '导入', 4, '', '', 'metadata-category/index', '2', '1', 'F', '1', '1', 'system:metadata:import', '#', '001', '2024-01-28 11:56:46.823081', '001', '2024-01-28 12:44:42.843707', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3866189144325, 1032351195909, '删除', 1, '', '', 'orgs/index', '2', '1', 'F', '1', '1', 'system:org:delete', '#', '001', '2024-01-22 19:05:01.350411', '001', '2024-01-28 11:43:09.622562', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867381804805, 102, '修改', 1, '', '', 'system/menu/index', '2', '1', 'F', '1', '1', 'system:menu:edit', '#', '001', '2024-01-22 20:22:40.181661', '001', '2024-01-28 11:36:53.775416', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (102, 1, '菜单管理', 1, 'menu', 'system/menu/index', '', '1', '1', 'C', '1', '1', 'system:menu:list', '#', '1', '2023-05-19 00:40:18', '001', '2024-01-28 11:23:12.779465', '菜单管理菜单', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867398950149, 102, '删除', 2, '', '', 'system/menu/index', '2', '1', 'F', '1', '1', 'system:menu:delete', '#', '001', '2024-01-22 20:23:47.154443', '001', '2024-01-28 11:36:59.208666', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867541541381, 2486597096453, '添加位置', 5, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:addAttr', '#', '001', '2024-01-22 20:33:04.149716', '001', '2024-01-30 15:23:13.634388', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4544078825989, 680207111173, '启用', 4, '', '', '', '2', '1', 'F', '1', '1', 'system:record-type:use', '#', '001', '2024-02-22 10:38:27.926729', '', '2024-02-22 10:38:27.926729', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (105, 1, '字典管理', 2, 'dict', 'system/dict/index', '', '1', '1', 'C', '1', '1', 'system:dict:list', '#', '1', '2023-05-19 00:40:18', 'admin', '2023-09-16 17:26:18.572445', '字典管理菜单', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033335487237, 1033218025477, '操作日志', 1, 'operlog', 'monitor/operlog/index', '', '1', '1', 'C', '1', '1', '', '#', 'admin', '2023-09-16 17:14:25.85186', '', '2023-09-16 17:14:25.85186', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033482585605, 1033218025477, '登录日志', 2, 'logininfor', 'monitor/logininfor/index', '', '1', '1', 'C', '1', '1', '', '#', 'admin', '2023-09-16 17:24:00.449451', '', '2023-09-16 17:24:00.449451', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033070990853, 1032745234437, '用户管理', 1, 'user', 'users/user/index', '', '1', '1', 'C', '1', '1', '', '#', 'admin', '2023-09-16 16:57:12.671741', '001', '2024-01-08 16:43:14.847321', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4045111323653, 3289178213637, '新增', 0, '', '', '', '2', '1', 'F', '1', '1', 'tenant:manage:add', '#', '001', '2024-01-30 21:13:36.119389', '', '2024-01-30 21:13:36.119389', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4045116362245, 3289178213637, '编辑', 1, '', '', '', '2', '1', 'F', '1', '1', 'tenant:manage:edit', '#', '001', '2024-01-30 21:13:55.794046', '', '2024-01-30 21:13:55.794046', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969264544517, 4969222513925, '数据权限', 2, 'dataPermission', 'permission-manage/dataPermission/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-03-12 15:59:49.628743', '', '2024-03-12 15:59:49.628743', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (788959642629, 1, '外观设置', 4, 'appearance', 'appearance/index', '', '2', '2', 'C', '1', '1', '', '#', 'admin', '2023-09-05 16:04:32.755514', 'admin', '2023-09-19 19:36:16.128413', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969269602053, 4969264544517, '新建', 0, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:add', '#', '001', '2024-03-12 16:00:09.384139', '', '2024-03-12 16:00:09.384139', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969273612805, 4969264544517, '编辑', 1, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:edit', '#', '001', '2024-03-12 16:00:25.051496', '', '2024-03-12 16:00:25.051496', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2486597096453, 2486547558661, '文档入库', 1, 'document-warehousing', 'doc-manage/docToLib', '', '2', '1', 'C', '1', '1', '', '#', '001', '2023-11-21 10:07:49.915771', '001', '2024-01-28 11:17:02.428011', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2486620490501, 2486547558661, '入库任务', 2, 'document-task', 'task', '', '2', '1', 'C', '1', '1', '', '#', '001', '2023-11-21 10:09:21.295987', '001', '2024-01-28 11:17:17.348001', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969282418693, 4969264544517, '详情', 2, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:detail', '#', '001', '2024-03-12 16:00:59.449381', '', '2024-03-12 16:00:59.449381', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867109873669, 1033070990853, '查看', 0, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:detail', '#', '001', '2024-01-22 20:04:57.952859', '001', '2024-01-28 11:39:13.365336', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867141308677, 1033070990853, '导入', 5, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:import', '#', '001', '2024-01-22 20:07:00.743353', '001', '2024-01-28 11:40:40.76055', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867116717573, 1033070990853, '重置密码', 1, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:reset', '#', '001', '2024-01-22 20:05:24.684332', '001', '2024-01-28 11:39:29.581155', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867122862853, 1033070990853, '编辑', 2, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:edit', '#', '001', '2024-01-22 20:05:48.688244', '001', '2024-01-28 11:39:42.355824', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867488198661, 2486627572997, '新建', 0, '', '', 'doc-manage/config/index', '2', '1', 'F', '1', '1', 'warehousing:config:add', '#', '001', '2024-01-22 20:29:35.824871', '001', '2024-01-28 11:49:08.730618', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3866184399109, 1032351195909, '修改', 2, '', '', 'orgs/index', '2', '1', 'F', '1', '1', 'system:org:edit', '#', '001', '2024-01-22 19:04:42.81553', '001', '2024-01-28 11:43:23.294679', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867423019269, 105, '修改', 1, '', '', 'system/dict/index', '2', '1', 'F', '1', '1', 'system:dict:edit', '#', '001', '2024-01-22 20:25:21.174109', '001', '2024-01-28 11:51:41.368161', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867349619205, 1033139247621, '新增', 0, '', '', 'post/index', '2', '1', 'F', '1', '1', 'system:post:add', '#', '001', '2024-01-22 20:20:34.455536', '001', '2024-01-28 11:52:34.856505', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867132472837, 1033070990853, '停用', 3, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:stop', '#', '001', '2024-01-22 20:06:26.22724', '001', '2024-01-28 11:40:08.664469', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867360055301, 1033139247621, '修改', 1, '', '', 'post/index', '2', '1', 'F', '1', '1', 'system:post:edit', '#', '001', '2024-01-22 20:21:15.220456', '001', '2024-01-28 11:52:46.357966', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969287104773, 4969264544517, '删除', 3, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:delete', '#', '001', '2024-03-12 16:01:17.754363', '', '2024-03-12 16:01:17.754363', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867136146437, 1033070990853, '删除', 4, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:delete', '#', '001', '2024-01-22 20:06:40.576577', '001', '2024-01-28 11:40:23.848328', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969291539461, 4969264544517, '停用', 4, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:stop', '#', '001', '2024-03-12 16:01:35.076801', '', '2024-03-12 16:01:35.076801', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1032351195909, 0, '组织架构管理', 5, 'org', 'orgs/index', '', '1', '1', 'C', '1', '1', '', 'orgs', 'admin', '2023-09-16 16:10:21.041051', '001', '2024-01-30 09:37:20.047822', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1032745234437, 0, '用户管理', 6, 'users', '', '', '1', '1', 'M', '1', '1', '', 'users', 'admin', '2023-09-16 16:36:00.204064', '001', '2024-01-30 09:37:24.843958', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033218025477, 0, '日志管理', 11, 'log', '', '', '1', '1', 'M', '1', '1', '', 'logManage', 'admin', '2023-09-16 17:06:47.01836', '001', '2024-01-30 09:37:51.781646', '', 1);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969296229637, 4969264544517, '启用', 5, '', '', '', '2', '1', 'F', '1', '1', 'system:dataPermission:start', '#', '001', '2024-03-12 16:01:53.398463', '', '2024-03-12 16:01:53.398463', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033087273221, 4969222513925, '角色管理', 1, 'role', 'permission-manage/role/index', '', '1', '1', 'C', '1', '1', '', '#', 'admin', '2024-03-12 15:57:42.099', '', '2024-03-12 15:57:42.099', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867412990981, 105, '新增', 0, '', '', 'system/dict/index', '2', '1', 'F', '1', '1', 'system:dict:add', '#', '001', '2024-01-22 20:24:42.000269', '001', '2024-01-28 11:36:03.662104', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867374213637, 102, '新增', 0, '', '', 'system/menu/index', '2', '1', 'F', '1', '1', 'system:menu:add', '#', '001', '2024-01-22 20:22:10.526386', '001', '2024-01-28 11:36:46.222858', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3091322456325, 1033087273221, '删除', 3, '', '', 'users/role/index', '2', '1', 'F', '1', '1', 'system:role:delete', '#', '001', '2023-12-18 18:17:58.355598', '001', '2024-01-28 11:42:15.675598', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3091485816325, 1033087273221, '新增', 4, '', '', 'users/role/index', '2', '1', 'F', '1', '1', 'system:role:add', '#', '001', '2023-12-18 18:28:36.477966', '001', '2024-01-28 11:42:34.664559', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4969222513925, 0, '权限管理', 8, 'permission-manage', '', '', '1', '1', 'M', '1', '1', '', 'password', '001', '2024-03-12 15:57:05.448496', 'admin', '2024-04-11 15:38:31.170117', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (695650007301, 0, '文库管理', 4, 'repository', 'repository/index', '', '1', '0', 'C', '1', '1', '', 'repository', 'admin', '2023-09-01 10:49:42.868603', 'admin', '2024-04-11 15:34:17.358761', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (680207111173, 0, '文档类型管理', 2, 'document-type', 'record-type/index', '', '1', '0', 'C', '1', '1', '', '#', 'admin', '2023-08-31 18:04:18.64034', 'admin', '2024-04-11 15:33:44.490977', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2514537072133, 0, '借阅管理', 7, 'borrow', 'borrow/index', '', '1', '1', 'C', '1', '1', '', 'borrow', '001', '2023-11-22 16:26:50.448282', '001', '2024-01-30 09:37:30.197136', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3267084230149, 1, '授权信息查看', 5, 'accredit', 'system/accredit/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2023-12-26 17:00:47.796653', '', '2023-12-26 17:00:47.796653', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867440394757, 788959642629, '保存', 0, '', '', 'appearance/index', '2', '1', 'F', '1', '1', 'system:appearance:save', '#', '001', '2024-01-22 20:26:29.046551', '001', '2024-01-28 11:35:37.163271', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3091306296069, 1033087273221, '查看', 0, '', '', 'users/role/index', '2', '1', 'F', '1', '1', 'system:role:view', '#', '001', '2023-12-18 18:16:55.226184', '001', '2024-01-28 11:41:30.013922', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3091308461573, 1033087273221, '编辑', 1, '', '', 'users/role/index', '2', '1', 'F', '1', '1', 'system:role:edit', '#', '001', '2023-12-18 18:17:03.685666', '001', '2024-01-28 11:41:42.345002', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3091317844229, 1033087273221, '分配用户', 2, '', '', 'users/role/index', '2', '1', 'F', '1', '1', 'system:role:allocation', '#', '001', '2023-12-18 18:17:40.336838', '001', '2024-01-28 11:42:00.366919', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2693184678661, 2693062754821, '全局敏感词配置', 2, 'global-sensitive-words', 'sensitive-config/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2023-11-30 18:17:32.66004', '001', '2024-01-28 10:54:43.311011', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3866068407557, 1032351195909, '新增', 0, '', '', 'orgs/index', '2', '1', 'F', '1', '1', 'system:org:add', '#', '001', '2024-01-22 18:57:09.724897', '001', '2024-01-28 11:42:53.351588', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868012433157, 2650569407493, '新增', 0, '', '', 'sensitive/index', '2', '1', 'F', '1', '1', 'system:sensitive:add', '#', '001', '2024-01-22 21:03:43.575191', '001', '2024-01-28 11:43:52.786556', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2486627572997, 2486547558661, '入库方案配置', 3, 'document-config', 'doc-manage/config/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2023-11-21 10:09:48.961497', '001', '2024-01-28 11:17:25.616174', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (966372199941, 966252410629, '分类标签', 1, 'category', 'tag/category/index', '{"categoryId":0}', '2', '1', 'C', '1', '1', '', '#', 'admin', '2023-09-13 16:34:50.281022', '001', '2024-01-28 11:21:53.061016', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868017813253, 2650569407493, '编辑', 1, '', '', 'sensitive/index', '2', '1', 'F', '1', '1', 'system:sensitive:edit', '#', '001', '2024-01-22 21:04:04.586404', '001', '2024-01-28 11:44:14.731338', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868022305797, 2650569407493, '删除', 2, '', '', 'sensitive/index', '2', '1', 'F', '1', '1', 'system:sensitive:delete', '#', '001', '2024-01-22 21:04:22.139506', '001', '2024-01-28 11:44:27.902692', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2650569407493, 2693062754821, '敏感词管理', 1, 'sensitive-word', 'sensitive/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2023-11-28 20:03:06.757223', '001', '2024-01-28 11:22:07.594854', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868027036421, 2650569407493, '导入', 3, '', '', 'sensitive/index', '2', '1', 'F', '1', '1', 'system:sensitive:import', '#', '001', '2024-01-22 21:04:40.614618', '001', '2024-01-28 11:44:40.437896', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2845358664709, 2486597096453, '上传文件', 0, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:upload', '#', '001', '2023-12-07 15:24:42.291664', '001', '2024-01-28 12:21:24.16239', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867513291013, 2486597096453, '查看', 1, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:view', '#', '001', '2024-01-22 20:31:13.799965', '001', '2024-01-28 12:22:15.625947', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867516845061, 2486597096453, '编辑', 2, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:edit', '#', '001', '2024-01-22 20:31:27.680062', '001', '2024-01-28 12:22:59.591157', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867524563717, 2486597096453, '重试', 3, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:retry', '#', '001', '2024-01-22 20:31:57.831962', '001', '2024-01-28 12:23:19.786068', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867553717765, 2486597096453, '审核', 6, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:examin', '#', '001', '2024-01-22 20:33:51.713698', '001', '2024-01-28 12:24:34.738281', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2693062754821, 0, '敏感词库管理', 4, 'sensitive-vocabulary', '', '', '1', '1', 'M', '1', '1', '', 'eye', '001', '2023-11-30 18:09:36.400803', '001', '2024-01-30 10:53:24.169685', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867492055301, 2486627572997, '配置', 1, '', '', 'doc-manage/config/index', '2', '1', 'F', '1', '1', 'warehousing:config:edit', '#', '001', '2024-01-22 20:29:50.845781', '001', '2024-01-28 11:49:19.007172', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867494969605, 2486627572997, '停用', 2, '', '', 'doc-manage/config/index', '2', '1', 'F', '1', '1', 'warehousing:config:stop', '#', '001', '2024-01-22 20:30:02.229432', '001', '2024-01-28 11:49:31.463182', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867468217093, 2486620490501, '查看', 0, '', '', 'task', '2', '1', 'F', '1', '1', 'warehousing:task:view', '#', '001', '2024-01-22 20:28:17.728113', '001', '2024-01-28 11:50:05.152266', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867427001605, 105, '删除', 2, '', '', 'system/dict/index', '2', '1', 'F', '1', '1', 'system:dict:delete', '#', '001', '2024-01-22 20:25:36.729871', '001', '2024-01-28 11:51:48.856282', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867534052101, 2486597096453, '人工处理', 4, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:handle', '#', '001', '2024-01-22 20:32:34.8947', '001', '2024-01-28 12:23:44.534579', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867557989637, 2486597096453, '删除', 7, '', '', 'doc-manage/docToLib', '2', '1', 'F', '1', '1', 'document:warehousing:delete', '#', '001', '2024-01-22 20:34:08.402421', '001', '2024-01-28 12:24:54.621573', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (4035862272005, 2486597096453, '全局移除', 8, '', '', '', '2', '1', 'F', '1', '1', 'document:warehousing:globaldelete', '#', '001', '2024-01-30 11:11:27.009', '001', '2024-01-30 16:05:39.095', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868089044485, 966372199941, '编辑', 1, '', '', 'tag/category/index', '2', '1', 'F', '1', '1', 'system:label:edit', '#', '001', '2024-01-22 21:08:42.833822', '001', '2024-01-28 11:46:13.557093', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868731898117, 699369213445, '删除', 2, '', '', 'metadata-category/index', '2', '1', 'F', '1', '1', 'system:metadata:delete', '#', '001', '2024-01-22 21:50:33.985703', '001', '2024-01-28 11:55:39.442456', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868535695621, 966372199941, '导入', 3, '', '', 'tag/category/index', '2', '1', 'F', '1', '1', 'system:label:import', '#', '001', '2024-01-22 21:37:47.568441', '001', '2024-01-28 11:46:34.962998', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868723818757, 699369213445, '新增', 0, '', '', 'metadata-category/index', '2', '1', 'F', '1', '1', 'system:metadata:add', '#', '001', '2024-01-22 21:50:02.426205', '001', '2024-01-29 09:57:18.464217', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868378096133, 1033335487237, '导出', 1, '', '', 'monitor/operlog/index', '2', '1', 'F', '1', '1', 'system:operLog:export', '#', '001', '2024-01-22 21:27:31.942796', '', '2024-01-22 21:27:31.942796', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868397821189, 1033482585605, '导出', 2, '', '', 'monitor/logininfor/index', '2', '1', 'F', '1', '1', 'system:log:export', '#', '001', '2024-01-22 21:28:48.996735', '', '2024-01-22 21:28:48.996735', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868557324037, 966372199941, '导出', 4, '', '', 'tag/category/index', '2', '1', 'F', '1', '1', 'system:label:export', '#', '001', '2024-01-22 21:39:12.055074', '001', '2024-01-28 11:46:46.979678', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868117296901, 966372199941, '删除', 2, '', '', 'tag/category/index', '2', '1', 'F', '1', '1', 'system:label:delete', '#', '001', '2024-01-22 21:10:33.208293', '001', '2024-01-29 09:59:48.445671', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868765636613, 680207111173, '新建', 0, '', '', 'record-type/index', '2', '1', 'F', '1', '1', 'system:record-type:add', '#', '001', '2024-01-22 21:52:45.77629', '001', '2024-01-28 11:47:09.725959', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868770451461, 680207111173, '编辑', 1, '', '', 'record-type/index', '2', '1', 'F', '1', '1', 'system:record-type:edit', '#', '001', '2024-01-22 21:53:04.581425', '001', '2024-01-28 11:47:20.082879', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (1033139247621, 0, '职务管理', 4, 'post', 'post/index', '', '1', '1', 'C', '1', '1', '', 'posts', 'admin', '2023-09-16 17:01:39.295505', 'admin', '2024-04-11 15:36:30.251953', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (2486547558661, 0, '入库管理', 0, 'document', '', '', '1', '1', 'M', '1', '1', '', 'docToLib', '001', '2023-11-21 10:04:36.411562', 'admin', '2024-04-11 15:19:35.322058', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3289178213637, 0, '租户管理', 8, 'tenant', 'tenant/index', '{"version": 2}', '1', '1', 'C', '1', '1', '', 'peoples', '001', '2023-12-27 16:59:12.411436', 'admin', '2024-04-11 15:38:11.803396', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868775889669, 680207111173, '删除', 2, '', '', 'record-type/index', '2', '1', 'F', '1', '1', 'system:record-type:delete', '#', '001', '2024-01-22 21:53:25.826926', '001', '2024-01-28 11:47:34.197785', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3869284419333, 1033070990853, '新增', 6, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:add', '#', '001', '2024-01-22 22:26:32.274973', '001', '2024-01-28 11:40:55.396498', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3869299902469, 1033070990853, '导出', 7, '', '', 'users/user/index', '2', '1', 'F', '1', '1', 'system:user:export', '#', '001', '2024-01-22 22:27:32.755105', '001', '2024-01-28 11:41:12.558991', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868043954181, 2693184678661, '新增', 0, '', '', 'sensitive-config/index', '2', '1', 'F', '1', '1', 'system:sensitive-config:add', '#', '001', '2024-01-22 21:05:46.699825', '001', '2024-01-28 11:44:56.933142', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868049243141, 2693184678661, '查看', 1, '', '', 'sensitive-config/index', '2', '1', 'F', '1', '1', 'system:sensitive-config:detail', '#', '001', '2024-01-22 21:06:07.363406', '001', '2024-01-28 11:45:10.423001', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868054677509, 2693184678661, '编辑', 2, '', '', 'sensitive-config/index', '2', '1', 'F', '1', '1', 'system:sensitive-config:edit', '#', '001', '2024-01-22 21:06:28.592646', '001', '2024-01-28 11:45:21.256811', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868059498245, 2693184678661, '删除', 3, '', '', 'sensitive-config/index', '2', '1', 'F', '1', '1', 'system:sensitive-config:delete', '#', '001', '2024-01-22 21:06:47.425545', '001', '2024-01-28 11:45:33.981248', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868784745221, 680207111173, '移动', 3, '', '', 'record-type/index', '2', '1', 'F', '1', '1', 'system:record-type:move', '#', '001', '2024-01-22 21:54:00.420225', '001', '2024-01-28 11:48:12.001459', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3882825979653, 2486627572997, '删除', 3, '', '', 'doc-manage/config/index', '2', '1', 'F', '1', '1', 'warehousing:config:delete', '#', '001', '2024-01-23 13:08:08.987351', '001', '2024-01-28 11:49:41.42144', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3867364889349, 1033139247621, '删除', 2, '', '', 'post/index', '2', '1', 'F', '1', '1', 'system:post:delete', '#', '001', '2024-01-22 20:21:34.103781', '001', '2024-01-28 11:52:55.616382', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3882394998789, 2514537072133, '查看', 0, '', '', 'borrow/index', '2', '1', 'F', '1', '1', 'system:borrow:detail', '#', '001', '2024-01-23 12:40:05.478354', '001', '2024-01-28 11:53:04.138757', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3882406483973, 2514537072133, '审批通过', 1, '', '', 'borrow/index', '2', '1', 'F', '1', '1', 'system:borrow:approve', '#', '001', '2024-01-23 12:40:50.335694', '001', '2024-01-28 11:53:12.146413', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3882415630597, 2514537072133, '审批驳回', 2, '', '', 'borrow/index', '2', '1', 'F', '1', '1', 'system:borrow:reject', '#', '001', '2024-01-23 12:41:26.064671', '001', '2024-01-28 11:53:19.443802', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868727505413, 699369213445, '编辑', 1, '', '', 'metadata-category/index', '2', '1', 'F', '1', '1', 'system:metadata:edit', '#', '001', '2024-01-22 21:50:16.825008', '001', '2024-01-28 11:55:33.984663', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (3868085615877, 966372199941, '新建', 0, '', '', 'tag/category/index', '2', '1', 'F', '1', '1', 'system:label:add', '#', '001', '2024-01-22 21:08:29.440752', '001', '2024-01-28 11:46:02.558842', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES (699369213445, 0, '元数据管理', 1, 'metadata-category', 'metadata-category/index', '{"version": 2}', '1', '0', 'C', '1', '1', '', 'edit', 'admin', '2023-09-01 14:51:50.9942', 'admin', '2024-04-11 15:26:14.545284', '', 2);



INSERT INTO "plss-system".sys_org (org_id, parent_id, ancestors, org_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time, remark, org_type, org_code, src_id, access_key, placeholder, tenant_id, exist_type, org_level, full_name, region_id, file_show_status) VALUES (636815934469, 0, '0', '使用单位名称', 1, '使用单位名称', '15888888888', '<EMAIL>', '1', '1', 'admin', '2023-08-29 19:02:14', '001', '2023-08-29 19:02:14', '', '1', '001', NULL, 'ED0D90ACCB94492EB728A3A9DA86AA98', 0, 3752384299781, 0, 1, '使用单位名称	', 1, '1');


INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (7, '入库管理员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', 'admin', '2024-03-21 18:07:04.785', '系统初始化入库管理员', 0, 7);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (4, '安全审计员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', '001', '2024-01-24 15:32:32.944618', '安全审计员', 0, 4);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (5, '超级管理员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', 'admin', '2024-03-26 15:09:05.661142', '超级管理员', 0, 5);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (2, '系统管理员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', '001', '2024-01-25 10:59:07.638993', '系统管理员', 0, 2);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (3, '安全管理员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', '001', '2024-01-25 11:01:27.814648', '安全管理员', 0, 3);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (6, '普通角色', 'common', 0, '2', '1', '1', '2', '1', '1', '2023-05-19 00:40:18', '001', '2024-01-23 16:02:34.038227', '普通角色', 0, 6);
INSERT INTO "plss-system".sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, org_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark, tenant_id, role_type) VALUES (1, '运营管理员', '', 0, '1', '1', '1', '1', '1', 'admin', '2022-12-18 19:49:16.146', 'admin', '2024-03-21 18:07:04.785687', '系统初始化配置管理员', 0, 1);


INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 680207111173);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868765636613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868770451461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868784745221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 699369213445);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868723818757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868727505413);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868731898117);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 966252410629);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 966372199941);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868085615877);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868089044485);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868117296901);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868535695621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868557324037);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 2693062754821);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 2650569407493);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868012433157);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868017813253);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868022305797);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868027036421);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 2693184678661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868043954181);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868049243141);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868054677509);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3868059498245);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1032351195909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1032745234437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1033070990853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867141308677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3869299902469);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1033087273221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3091306296069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3091308461573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3091322456325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3091485816325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1033139247621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867349619205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867360055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867364889349);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 1);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 102);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867374213637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867381804805);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (2, 3867398950149);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1032351195909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1033070990853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867141308677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3869299902469);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3091306296069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3091317844229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1033139247621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867349619205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867360055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3867364889349);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1033218025477);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1033335487237);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3868378096133);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 1033482585605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (3, 3868397821189);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867141308677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3869299902469);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 1033087273221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867349619205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867360055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3867364889349);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 102);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 1033218025477);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 1033335487237);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3868378096133);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 1033482585605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (4, 3868397821189);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 2486547558661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 2486627572997);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867488198661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867492055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867494969605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3882825979653);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 680207111173);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868765636613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868770451461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868784745221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 699369213445);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868723818757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868727505413);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868731898117);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 966252410629);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 966372199941);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868085615877);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868089044485);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868117296901);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868535695621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868557324037);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 2693062754821);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 2650569407493);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868012433157);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868017813253);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868022305797);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868027036421);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 2693184678661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868043954181);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868049243141);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868054677509);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3868059498245);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1032351195909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1032745234437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1033070990853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867141308677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3869299902469);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1033087273221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3091306296069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3091308461573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3091317844229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3091322456325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3091485816325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1033139247621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867349619205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867360055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867364889349);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 1);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 102);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867374213637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867381804805);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867398950149);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 105);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867412990981);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867423019269);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867427001605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 788959642629);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3867440394757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3267084230149);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 3289178213637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969222513925);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969264544517);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969269602053);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969273612805);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969282418693);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969287104773);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969291539461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (1, 4969296229637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 2486547558661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 2486597096453);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 2845358664709);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867513291013);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867516845061);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867524563717);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867534052101);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867541541381);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867553717765);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867557989637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 4035862272005);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 2486620490501);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867468217093);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 2486627572997);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867488198661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867492055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3867494969605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (7, 3882825979653);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2486547558661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2486620490501);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867468217093);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2486627572997);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867488198661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867492055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867494969605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3882825979653);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 699369213445);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868723818757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868727505413);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868731898117);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3992318260997);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3992321745157);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 680207111173);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868765636613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868770451461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868784745221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 966252410629);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 966372199941);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868085615877);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868089044485);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868117296901);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868535695621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868557324037);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 695650007301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033139247621);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867349619205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867360055301);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867364889349);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2693062754821);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2650569407493);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868012433157);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868017813253);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868022305797);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868027036421);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2693184678661);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868043954181);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868049243141);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868054677509);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868059498245);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1032351195909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1032745234437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033070990853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867141308677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3869299902469);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 2514537072133);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3882394998789);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3882406483973);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3882415630597);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3289178213637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4045111323653);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4045116362245);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969222513925);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033087273221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3091306296069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3091308461573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3091317844229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3091322456325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3091485816325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969264544517);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969269602053);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969273612805);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969282418693);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969287104773);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969291539461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 4969296229637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 102);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867374213637);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867381804805);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867398950149);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 105);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867412990981);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867423019269);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867427001605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 788959642629);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3867440394757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3267084230149);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033218025477);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033335487237);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868378096133);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 1033482585605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES (5, 3868397821189);


INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273202949, 2398227485701, 1, 'resourceId', '资源id(可能为库、目录、文件的id)', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.7', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273204229, 2398227485701, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.705', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273204741, 2398227485701, 1, 'source', '资源来源(仅文件有效)', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.707', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273205253, 2398227485701, 1, 'resourceType', '资源类型(1：库、2：目录、3：文件)', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.709', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273205765, 2398227485701, 1, 'visitType', '1:继承， 2: 私有', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.711', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273206533, 2398227485701, 1, 'resourceCreateId', '资源创建人id', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.714', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273206789, 2398227485701, 1, 'resourceCreator', '资源创建人姓名', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.715', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273207557, 2398227485701, 1, 'resourceStatus', '资源状态', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.717', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273207813, 2398227485701, 1, 'resourceStatusDesc', '资源状态描述', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.719', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273208325, 2398227485701, 1, 'createTime', '创建时间', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.72', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2398273208581, 2398227485701, 1, 'modifyTime', '修改时间', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-17 10:17:34.721', '', '2023-11-17 10:18:41.242669', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4065158299141, 4064767479557, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称1', 0, '', 0, 1, '1', '1191407656453', '2024-01-31 18:58:44.607', '', '2024-01-31 18:58:44.600355', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4065158299397, 4064767479557, 1, 'createTime', '创建时间', '创建时间', 0, '', 1, 1, '1', '1191407656453', '2024-01-31 18:58:44.608', '', '2024-01-31 18:58:44.600355', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4065158299653, 4064767479557, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 2, 1, '1', '1191407656453', '2024-01-31 18:58:44.609', '', '2024-01-31 18:58:44.600355', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4065158299909, 4064767479557, 1, 'source', '资源来源(仅文件有效)', '制度来源', 0, '', 3, 1, '1', '1191407656453', '2024-01-31 18:58:44.609', '', '2024-01-31 18:58:44.600355', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113104883717, 3113104880389, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1191407656453', '2023-12-19 17:56:05.953', '', '2023-12-19 17:56:05.933574', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113104885509, 3113104880389, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 1, 1, '1', '1191407656453', '2023-12-19 17:56:05.96', '', '2023-12-19 17:56:05.933574', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113104885765, 3113104880389, 1, 'createTime', '创建时间', '创建时间', 0, '', 2, 1, '1', '1191407656453', '2023-12-19 17:56:05.961', '', '2023-12-19 17:56:05.933574', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113104886021, 3113104880389, 1, 'modifyTime', '修改时间', '修改时间', 0, '', 3, 1, '1', '1191407656453', '2023-12-19 17:56:05.962', '', '2023-12-19 17:56:05.933574', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617617413, 2863536490757, 1, 'recordName', '文件名称', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.569', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617617925, 2863536490757, 1, 'borrowName', '借阅用户名称', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.571', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617618181, 2863536490757, 1, 'borrowDate', '借阅日期', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.571', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617618437, 2863536490757, 1, 'orgName', '借阅用户所属单位名称', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.573', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617618693, 2863536490757, 1, 'borrowDays', '借阅时间', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.574', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617618949, 2863536490757, 1, 'borrowPurpose', '借阅目的（1讨论、2学习、3科研、4其他）', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.575', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617619205, 2863536490757, 1, 'borrowFor', '借阅原因', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.576', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617619461, 2863536490757, 1, 'usage', '利用方式（多选项：1查阅、2打印）', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.577', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338181, 2534271251205, 1, 'recordName', '文件名称', '名称', 0, '', 0, 1, '1', '1191407656453', '2024-02-21 15:26:44.447', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617619717, 2863536490757, 1, 'auditName', '审核用户名称', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.578', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617619973, 2863536490757, 1, 'auditDate', '审核日期', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.578', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617620229, 2863536490757, 1, 'status', '状态（1待审批、2已借出、3已归还、4已拒绝）', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.579', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617620485, 2863536490757, 1, 'rejectReason', '驳回原因', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.58', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617620486, 2863536490757, 1, 'browseNum', '浏览次数', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.581', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3420617620741, 2863536490757, 1, 'expirationDate', '到期时间', '', 0, '', 1, 0, '1', '1191407656453', '2024-01-02 15:36:27.582', '', '2024-01-02 15:36:27.562057', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338437, 2534271251205, 1, 'status', '状态（1待审批、2已借出、3已归还、4已拒绝）', '借阅状态', 0, '', 1, 1, '1', '1191407656453', '2024-02-21 15:26:44.447', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338693, 2534271251205, 1, 'borrowName', '借阅用户名称', '申请人', 0, '', 2, 1, '1', '1191407656453', '2024-02-21 15:26:44.448', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338694, 2534271251205, 1, 'borrowDate', '借阅日期', '申请时间', 0, '', 3, 1, '1', '1191407656453', '2024-02-21 15:26:44.448', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338949, 2534271251205, 1, 'borrowDays', '借阅时间', '借阅天数', 0, '', 4, 1, '1', '1191407656453', '2024-02-21 15:26:44.449', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622962181, 4042571624453, 1, 'resourceId', '资源id(可能为库、目录、文件的id)', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.947', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622962693, 4042571624453, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.948', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622962694, 4042571624453, 1, 'source', '资源来源(仅文件有效)', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.949', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622962949, 4042571624453, 1, 'resourceType', '资源类型(1：库、2：目录、3：文件)', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.95', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622963205, 4042571624453, 1, 'visitType', '1:继承， 2: 私有', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.95', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622963461, 4042571624453, 1, 'resourceCreateId', '资源创建人id', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.951', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622963462, 4042571624453, 1, 'resourceCreator', '资源创建人姓名', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.952', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622963717, 4042571624453, 1, 'resourceStatus', '资源状态', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.952', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622964229, 4042571624453, 1, 'resourceStatusDesc', '资源状态描述', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.954', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622964485, 4042571624453, 1, 'createTime', '创建时间', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.955', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4042622964741, 4042571624453, 1, 'modifyTime', '修改时间', '', 0, '', 1, 0, '1', '4040797687045', '2024-01-30 18:31:35.956', '', '2024-01-30 18:31:35.942171', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4526388338950, 2534271251205, 1, 'expirationDate', '到期时间', '到期时间', 0, '', 5, 1, '1', '1191407656453', '2024-02-21 15:26:44.449', '', '2024-02-21 15:26:44.44578', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052230405, 2863536490757, 1, 'borrowName', '借阅用户名称', '借阅人', 0, '', 0, 1, '1', '1191407656453', '2023-12-08 11:41:44.026', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231173, 2863536490757, 1, 'orgName', '借阅用户所属单位名称', '借阅人单位', 0, '', 1, 1, '1', '1191407656453', '2023-12-08 11:41:44.028', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231174, 2863536490757, 1, 'recordName', '文件名称', '文件名称', 0, '', 2, 1, '1', '1191407656453', '2023-12-08 11:41:44.029', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231429, 2863536490757, 2, '1121369818629', '标题', '文件标题', 1160480783109, '基础元数据', 3, 1, '1', '1191407656453', '2023-12-08 11:41:44.029', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231685, 2863536490757, 1, 'borrowDate', '借阅日期', '申请日期', 0, '', 4, 1, '1', '1191407656453', '2023-12-08 11:41:44.03', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231941, 2863536490757, 1, 'borrowDays', '借阅时间', '申请天数', 0, '', 5, 1, '1', '1191407656453', '2023-12-08 11:41:44.031', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052231942, 2863536490757, 1, 'status', '状态（1待审批、2已借出、3已归还、4已拒绝）', '状态', 0, '', 6, 1, '1', '1191407656453', '2023-12-08 11:41:44.031', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052232197, 2863536490757, 1, 'auditName', '审核用户名称', '审核人', 0, '', 7, 1, '1', '1191407656453', '2023-12-08 11:41:44.032', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052232198, 2863536490757, 1, 'auditDate', '审核日期', '审核日期', 0, '', 8, 1, '1', '1191407656453', '2023-12-08 11:41:44.033', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052232453, 2863536490757, 1, 'usage', '利用方式（多选项：1查阅、2打印）', '利用方式', 0, '', 9, 1, '1', '1191407656453', '2023-12-08 11:41:44.033', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2864052232709, 2863536490757, 1, 'browseNum', '浏览次数', '浏览次数', 0, '', 10, 1, '1', '1191407656453', '2023-12-08 11:41:44.034', '', '2023-12-08 11:41:44.023855', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328279813, 3089328278021, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1191407656453', '2023-12-18 16:08:08.593', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328280069, 3089328278021, 1, 'source', '资源来源(仅文件有效)', '制度来源', 0, '', 1, 1, '1', '1191407656453', '2023-12-18 16:08:08.594', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328280325, 3089328278021, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 2, 1, '1', '1191407656453', '2023-12-18 16:08:08.595', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328280326, 3089328278021, 1, 'visitType', '1:继承， 2: 私有', '1:继承， 2: 私有', 0, '', 3, 1, '1', '1191407656453', '2023-12-18 16:08:08.595', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328280581, 3089328278021, 1, 'createTime', '创建时间', '创建时间', 0, '', 4, 1, '1', '1191407656453', '2023-12-18 16:08:08.596', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3089328280582, 3089328278021, 1, 'modifyTime', '修改时间', '修改时间', 0, '', 5, 1, '1', '1191407656453', '2023-12-18 16:08:08.597', '', '2023-12-18 16:08:08.583796', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113147915525, 3113147914757, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1191407656453', '2023-12-19 17:58:54.045', '', '2023-12-19 17:58:54.041301', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113147915781, 3113147914757, 1, 'source', '资源来源(仅文件有效)', '制度来源', 0, '', 1, 1, '1', '1191407656453', '2023-12-19 17:58:54.047', '', '2023-12-19 17:58:54.041301', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113147916037, 3113147914757, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 2, 1, '1', '1191407656453', '2023-12-19 17:58:54.049', '', '2023-12-19 17:58:54.041301', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113147916805, 3113147914757, 1, 'createTime', '创建时间', '创建时间', 0, '', 3, 1, '1', '1191407656453', '2023-12-19 17:58:54.05', '', '2023-12-19 17:58:54.041301', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (3113147917061, 3113147914757, 1, 'modifyTime', '修改时间', '修改时间', 0, '', 4, 1, '1', '1191407656453', '2023-12-19 17:58:54.051', '', '2023-12-19 17:58:54.041301', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434677765, 2534271251205, 1, 'recordName', '文件名称', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.712', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434678533, 2534271251205, 1, 'borrowName', '借阅用户名称', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.713', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434679045, 2534271251205, 1, 'borrowDate', '借阅日期', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.715', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434679301, 2534271251205, 1, 'orgName', '借阅用户所属单位名称', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.717', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434679813, 2534271251205, 1, 'borrowDays', '借阅时间', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.719', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680069, 2534271251205, 1, 'borrowPurpose', '借阅目的（1讨论、2学习、3科研、4其他）', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.72', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680325, 2534271251205, 1, 'borrowFor', '借阅原因', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.72', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680326, 2534271251205, 1, 'usage', '利用方式（多选项：1查阅、2打印）', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.72', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680581, 2534271251205, 1, 'auditName', '审核用户名称', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.721', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680582, 2534271251205, 1, 'auditDate', '审核日期', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.722', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434680837, 2534271251205, 1, 'status', '状态（1待审批、2已借出、3已归还、4已拒绝）', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.722', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434681093, 2534271251205, 1, 'rejectReason', '驳回原因', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.723', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434681349, 2534271251205, 1, 'browseNum', '浏览次数', '', 0, '', 1, 0, '1', '1191407656453', '2023-11-23 15:07:21.724', '', '2023-11-23 15:07:22.28571', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (2535434677766, 2534271251205, 1, 'expirationDate', '到期时间', '', 0, '', 1, 0, '1', '1191407656453', '2023-12-08 13:52:33.632', '', '2023-12-08 13:52:33.623588', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587834117, 2398227485701, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1612860642053', '2024-03-04 18:27:38.727', '', '2024-03-04 18:27:38.733312', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587835141, 2398227485701, 1, 'source', '资源来源(仅文件有效)', '制度来源', 0, '', 1, 1, '1', '1612860642053', '2024-03-04 18:27:38.731', '', '2024-03-04 18:27:38.733373', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587835397, 2398227485701, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 2, 1, '1', '1612860642053', '2024-03-04 18:27:38.732', '', '2024-03-04 18:27:38.733418', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587835653, 2398227485701, 1, 'resourceStatus', '资源状态', '状态', 0, '', 3, 1, '1', '1612860642053', '2024-03-04 18:27:38.733', '', '2024-03-04 18:27:38.733451', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587835654, 2398227485701, 1, 'visitType', '1:继承， 2: 私有', '是否私有', 0, '', 4, 1, '1', '1612860642053', '2024-03-04 18:27:38.734', '', '2024-03-04 18:27:38.733463', '');
INSERT INTO "plss-system".sys_table_header (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (4794587835909, 2398227485701, 1, 'createTime', '创建时间', '创建时间', 0, '', 5, 1, '1', '1612860642053', '2024-03-04 18:27:38.734', '', '2024-03-04 18:27:38.733476', '');


INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (4064767479557, '文库管理', '库的目录', 'rc_record', 4, 1, 3884707298821, 1, '1', '1191407656453', '2024-01-31 18:33:17.967', '', '2024-01-31 18:33:17.963773', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (2534271251205, '借阅管理', '审核列表', 'rc_record_borrow', 6, 0, 0, 1, '1', '1191407656453', '2023-11-23 13:51:37.076', '', '2023-11-23 13:51:37.577961', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (2398227485701, '文库管理', '库的目录', 'rc_record', 6, 0, 0, 1, '1', '1191407656453', '2023-11-17 10:14:36.117', '', '2023-11-17 10:15:42.674084', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (2863536490757, '借阅管理', '我的借阅', 'rc_record_borrow', 11, 0, 0, 1, '1', '1191407656453', '2023-12-08 11:08:09.421', '', '2023-12-08 11:08:09.42277', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (2398229492485, '借阅管理', '借阅列表', 'rc_record_borrow', 0, 0, 0, 1, '1', '1191407656453', '2023-11-17 10:14:43.956', '', '2023-11-17 10:15:50.507523', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (3089328278021, '文库管理', '库的目录', 'rc_record', 6, 1, 2026458082565, 1, '1', '1191407656453', '2023-12-18 16:08:08.587', '', '2023-12-18 16:08:08.583796', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (3113104880389, '文库管理', '库的目录', 'rc_record', 4, 1, 3089644620805, 1, '1', '1191407656453', '2023-12-19 17:56:05.94', '', '2023-12-19 17:56:05.933574', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (3113147914757, '文库管理', '库的目录', 'rc_record', 5, 1, 3088971178501, 1, '1', '1191407656453', '2023-12-19 17:58:54.043', '', '2023-12-19 17:58:54.041301', '', NULL);
INSERT INTO "plss-system".sys_table_header_config (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (4042571624453, '文库管理', '我的文库', 'rc_record', 0, 0, 0, 1, '1', '4040797687045', '2024-01-30 18:28:15.409', '', '2024-01-30 18:28:15.41045', '', NULL);



INSERT INTO "plss-system".sys_tenant (id, name, status, create_by, create_time, update_by, update_time, remark, del_flag) VALUES (3752384299781, '使用单位名称', 1, '1191407656453', '2024-01-17 15:35:51.172', '', '2024-01-17 15:35:51.142793', '', 1);


INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969222513925);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2486547558661);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2486597096453);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2845358664709);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867513291013);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867516845061);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867524563717);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867534052101);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867541541381);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867553717765);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867557989637);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4035862272005);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2486620490501);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867468217093);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2486627572997);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867488198661);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867492055301);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867494969605);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3882825979653);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 695650007301);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 680207111173);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868765636613);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868770451461);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868775889669);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868784745221);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4544078825989);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 699369213445);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868723818757);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868727505413);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868731898117);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3992318260997);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3992321745157);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 966252410629);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 966372199941);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868085615877);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868089044485);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868117296901);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868535695621);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868557324037);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2693062754821);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2650569407493);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868012433157);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868017813253);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868022305797);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868027036421);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2693184678661);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868043954181);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868049243141);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868054677509);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868059498245);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1032351195909);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3866068407557);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3866189144325);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3866184399109);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1032745234437);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033070990853);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867109873669);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867116717573);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867122862853);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867132472837);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867136146437);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867141308677);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3869284419333);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3869299902469);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033087273221);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3091306296069);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3091308461573);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3091317844229);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3091322456325);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3091485816325);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 2514537072133);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3882394998789);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3882406483973);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3882415630597);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033139247621);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867349619205);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867360055301);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867364889349);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 102);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867374213637);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867381804805);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867398950149);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 105);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867412990981);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867423019269);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867427001605);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 788959642629);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3867440394757);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3267084230149);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3289178213637);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4045111323653);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4045116362245);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033218025477);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033335487237);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868378096133);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 1033482585605);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 3868397821189);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969264544517);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969269602053);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969273612805);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969282418693);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969287104773);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969291539461);
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id) VALUES (3752384299781, 4969296229637);


INSERT INTO "plss-system".sys_user (user_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark, org_id, pwd_update_time, src_id, access_key, order_num, wps_auth) VALUES (1, 'admin', '配置管理员', '00', '<EMAIL>', '9156d6fbe8990613a7fe4eeb555da14f', '1', '', '$2a$10$6yGh0ZQTzW0RCKsjbt9Z7uGM6JAnpBNIfMefdnIGOkIuaVefPdZVS', '1', '1', '127.0.0.1', '2023-05-19 00:40:18', '1', '2023-05-19 00:40:18', '1', '2023-09-16 15:38:38.769', '管理员', 103, '2022-08-24 10:11:38.955', NULL, NULL, '0', '2');
INSERT INTO "plss-system".sys_user (user_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark, org_id, pwd_update_time, src_id, access_key, order_num, wps_auth) VALUES (1191407656453, '001', '使用单位名称', '00', '', '', '3', '', '$2a$10$9zA4xqvcx7NZ2prZkbh/KOgLd.W/D/aeHb04QSYmDQ2ZUmtrarO32', '1', '1', '', '2023-09-23 20:45:35.604825', '', '2023-09-23 20:45:35.604825', '001', '2024-03-29 18:21:13.13565', '', 0, '2024-01-21 19:50:19.913', NULL, NULL, '1', '2');


INSERT INTO "plss-system".sys_user_org (org_id, user_id, tenant_id, status, order_by) VALUES (636815934469, 1191407656453, 3752384299781, 1, 1);


INSERT INTO "plss-system".sys_user_role (role_id, user_id, tenant_id, status) VALUES (5, 1191407656453, 3752384299781, 1);
INSERT INTO "plss-system".sys_user_role (role_id, user_id, tenant_id, status) VALUES (1, 1, 0, 1);



commit;