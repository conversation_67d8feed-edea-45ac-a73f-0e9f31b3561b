-- --------------------------------
-- postgresql in docker container(postgresql运行在docker容器中时，需要修改dockerName为当前容器名和sql文件路径):
-- docker exec dockerName psql -d postgres -U postgres -f /var/lib/postgresql/data/plss_ddl_20240411.sql

-- postgresql psql command(可以直接执行psql工具时，需要修改sql文件路径):
-- psql -d postgres -U postgres -f /var/lib/postgresql/data/plss_ddl_20240411.sql

-- 忽略最后的 commit 警告 WARNING:  there is no transaction in progress
-- --------------------------------

-- ******** 不建义用query工具执行 *************


--
-- PostgreSQL database dump
--

-- Dumped from database version 15.3 (Debian 15.3-1.pgdg120+1)
-- Dumped by pg_dump version 15.3

-- Started on 2024-04-11 09:00:56 CST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

CREATE ROLE suwell WITH LOGIN NOSUPERUSER INHERIT NOCREATEDB NOCREATEROLE NOREPLICATION PASSWORD 'SW_1357924680';

--
-- TOC entry 4794 (class 1262 OID 24577)
-- Name: plss; Type: DATABASE; Schema: -; Owner: suwell
--

CREATE DATABASE plss WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';

ALTER DATABASE plss OWNER TO suwell;

\connect plss
\connect - suwell;


SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 4795 (class 0 OID 0)
-- Dependencies: 4794
-- Name: DATABASE plss; Type: COMMENT; Schema: -; Owner: suwell
--

COMMENT ON DATABASE plss IS 'Privatization LLM Service System';


--
-- TOC entry 5 (class 2615 OID 24578)
-- Name: plss-open; Type: SCHEMA; Schema: -; Owner: suwell
--

CREATE SCHEMA "plss-open";


ALTER SCHEMA "plss-open" OWNER TO suwell;

--
-- TOC entry 4797 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA "plss-open"; Type: COMMENT; Schema: -; Owner: suwell
--

COMMENT ON SCHEMA "plss-open" IS '开放平台';


--
-- TOC entry 14 (class 2615 OID 28245)
-- Name: plss-plugin; Type: SCHEMA; Schema: -; Owner: suwell
--

CREATE SCHEMA "plss-plugin";


ALTER SCHEMA "plss-plugin" OWNER TO suwell;

--
-- TOC entry 6 (class 2615 OID 24579)
-- Name: plss-record; Type: SCHEMA; Schema: -; Owner: suwell
--

CREATE SCHEMA "plss-record";


ALTER SCHEMA "plss-record" OWNER TO suwell;

--
-- TOC entry 4798 (class 0 OID 0)
-- Dependencies: 6
-- Name: SCHEMA "plss-record"; Type: COMMENT; Schema: -; Owner: suwell
--

COMMENT ON SCHEMA "plss-record" IS '文件管理';


--
-- TOC entry 7 (class 2615 OID 24580)
-- Name: plss-system; Type: SCHEMA; Schema: -; Owner: suwell
--

CREATE SCHEMA "plss-system";


ALTER SCHEMA "plss-system" OWNER TO suwell;

--
-- TOC entry 4799 (class 0 OID 0)
-- Dependencies: 7
-- Name: SCHEMA "plss-system"; Type: COMMENT; Schema: -; Owner: suwell
--

COMMENT ON SCHEMA "plss-system" IS '管理系统';


--
-- TOC entry 403 (class 1255 OID 27072)
-- Name: upd_timestamp(); Type: FUNCTION; Schema: plss-system; Owner: suwell
--

CREATE FUNCTION "plss-system".upd_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
  new.update_time = current_timestamp;
  return new;
end
$$;


ALTER FUNCTION "plss-system".upd_timestamp() OWNER TO suwell;


--
-- TOC entry 396 (class 1259 OID 188009)
-- Name: op_abutment_api_settings; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_api_settings (
    id bigint NOT NULL,
    config_code character varying(100) NOT NULL,
    manufacturer_id bigint NOT NULL,
    sketch character varying(150),
    server_host character varying(300),
    abutment_uri character varying(300),
    version_num character varying(50),
    flow_type smallint NOT NULL,
    api_method character varying(20) NOT NULL,
    data_transfer_format smallint NOT NULL,
    input_args character varying(500),
    input_reflection character varying(1000),
    output_reflection character varying(1000),
    act_result_path character varying(300),
    success_call_id bigint,
    failed_call_id bigint,
    additional character varying(1000),
    enable smallint DEFAULT 1 NOT NULL,
    spare1 character varying(300),
    spare2 character varying(300),
    spare3 character varying(300),
    create_by bigint,
    create_time timestamp without time zone NOT NULL,
    update_by bigint,
    update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_api_settings OWNER TO suwell;

--
-- TOC entry 4834 (class 0 OID 0)
-- Dependencies: 396
-- Name: TABLE op_abutment_api_settings; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_api_settings IS '对接接口配置表';


--
-- TOC entry 4835 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.id IS '数据id';


--
-- TOC entry 4836 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.config_code; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.config_code IS '数据code';


--
-- TOC entry 4837 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.manufacturer_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.manufacturer_id IS '厂商id';


--
-- TOC entry 4838 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.sketch; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.sketch IS '接口简述';


--
-- TOC entry 4839 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.server_host; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.server_host IS '服务地址(ip和端口或者域名)';


--
-- TOC entry 4840 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.abutment_uri; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.abutment_uri IS '接口uri';


--
-- TOC entry 4841 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.version_num; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.version_num IS '版本号';


--
-- TOC entry 4842 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.flow_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.flow_type IS '数据流向类型（推送/拉取） 1：产商推送；2：公文库拉取';


--
-- TOC entry 4843 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.api_method; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.api_method IS '接口方法类型 post/get';


--
-- TOC entry 4844 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.data_transfer_format; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.data_transfer_format IS '数据传递类型（xml/普通字符串json） 1：xml；2：普通入参（get对应query，post对应body的json） 3:表单';


--
-- TOC entry 4845 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.input_args; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.input_args IS '输入参数值';


--
-- TOC entry 4846 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.input_reflection; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.input_reflection IS '输入参数映射';


--
-- TOC entry 4847 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.output_reflection; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.output_reflection IS '输出参数映射';


--
-- TOC entry 4848 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.act_result_path; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.act_result_path IS '实际结果值链路';


--
-- TOC entry 4849 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.success_call_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.success_call_id IS '成功回调请求配置id';


--
-- TOC entry 4850 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.failed_call_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.failed_call_id IS '失败回调请求配置id';


--
-- TOC entry 4851 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.additional; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.additional IS '额外的配置条件';


--
-- TOC entry 4852 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.enable; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.enable IS '启用状态 0:关闭；1：启用';


--
-- TOC entry 4853 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare1 IS '备用1';


--
-- TOC entry 4854 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare2 IS '备用2';


--
-- TOC entry 4855 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare3 IS '备用3';


--
-- TOC entry 4856 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.create_by IS '创建人';


--
-- TOC entry 4857 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.create_time IS '创建时间';


--
-- TOC entry 4858 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.update_by IS '更新人';


--
-- TOC entry 4859 (class 0 OID 0)
-- Dependencies: 396
-- Name: COLUMN op_abutment_api_settings.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.update_time IS '更新时间';


--
-- TOC entry 397 (class 1259 OID 188017)
-- Name: op_abutment_manufacturer; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_manufacturer (
    id bigint NOT NULL,
    app_id character varying(50),
    manufacturer character varying(150) NOT NULL,
    manufacturer_code character varying(150) NOT NULL,
    business_type character varying(150) NOT NULL,
    server_host character varying(300),
    basic_condition character varying(1000),
    spare1 character varying(300),
    spare2 character varying(300),
    spare3 character varying(300),
    create_time timestamp without time zone NOT NULL,
    update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_manufacturer OWNER TO suwell;

--
-- TOC entry 4860 (class 0 OID 0)
-- Dependencies: 397
-- Name: TABLE op_abutment_manufacturer; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_manufacturer IS '对接厂商表';


--
-- TOC entry 4861 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.id IS '数据id';


--
-- TOC entry 4862 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.app_id IS '应用id';


--
-- TOC entry 4863 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.manufacturer; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.manufacturer IS '厂商';


--
-- TOC entry 4864 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.manufacturer_code; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.manufacturer_code IS '厂商编号';


--
-- TOC entry 4865 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.business_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.business_type IS '业务类型';


--
-- TOC entry 4866 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.server_host; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.server_host IS '服务地址（ip端口或域名）';


--
-- TOC entry 4867 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.basic_condition; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.basic_condition IS '基础配置';


--
-- TOC entry 4868 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare1 IS '备用1';


--
-- TOC entry 4869 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare2 IS '备用2';


--
-- TOC entry 4870 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare3 IS '备用3';


--
-- TOC entry 4871 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.create_time IS '创建时间';


--
-- TOC entry 4872 (class 0 OID 0)
-- Dependencies: 397
-- Name: COLUMN op_abutment_manufacturer.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.update_time IS '更新时间';


--
-- TOC entry 398 (class 1259 OID 188024)
-- Name: op_abutment_plan; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_plan (
    id bigint NOT NULL,
    plan_name character varying(300) NOT NULL,
    manufacturer_config character varying(1000) NOT NULL,
    spare1 character varying(300),
    spare2 character varying(300),
    spare3 character varying(300),
    create_by bigint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    update_by bigint NOT NULL,
    update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_plan OWNER TO suwell;

--
-- TOC entry 4873 (class 0 OID 0)
-- Dependencies: 398
-- Name: TABLE op_abutment_plan; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_plan IS '对接预置计划表';


--
-- TOC entry 4874 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.id IS '数据id';


--
-- TOC entry 4875 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.plan_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.plan_name IS '集成对接方案名';


--
-- TOC entry 4876 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.manufacturer_config; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.manufacturer_config IS '具体集成厂商与接口设定';


--
-- TOC entry 4877 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare1 IS '备用1';


--
-- TOC entry 4878 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare2 IS '备用2';


--
-- TOC entry 4879 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare3 IS '备用3';


--
-- TOC entry 4880 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.create_by IS '创建人';


--
-- TOC entry 4881 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.create_time IS '创建时间';


--
-- TOC entry 4882 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.update_by IS '更新人';


--
-- TOC entry 4883 (class 0 OID 0)
-- Dependencies: 398
-- Name: COLUMN op_abutment_plan.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.update_time IS '更新时间';


--
-- TOC entry 334 (class 1259 OID 31658)
-- Name: op_app_info; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_app_info (
    id bigint NOT NULL,
    app_name character varying(50) NOT NULL,
    app_id character varying(50) NOT NULL,
    org_id bigint DEFAULT 0 NOT NULL,
    type smallint DEFAULT 1 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_app_info OWNER TO suwell;

--
-- TOC entry 4884 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.app_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.app_name IS '应用名称';


--
-- TOC entry 4885 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.app_id IS '应用id';


--
-- TOC entry 4886 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.org_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.org_id IS '单位id';


--
-- TOC entry 4887 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.type IS '类型';


--
-- TOC entry 4888 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.status; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.status IS '状态';


--
-- TOC entry 4889 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.del_flag IS '删除标识';


--
-- TOC entry 4890 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.create_by IS '创建人';


--
-- TOC entry 4891 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.create_time IS '创建时间';


--
-- TOC entry 4892 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.update_by IS '修改人';


--
-- TOC entry 4893 (class 0 OID 0)
-- Dependencies: 334
-- Name: COLUMN op_app_info.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.update_time IS '修改时间';


--
-- TOC entry 335 (class 1259 OID 31687)
-- Name: op_app_ram; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_app_ram (
    id bigint NOT NULL,
    app_id character varying(64) NOT NULL,
    access_key character varying(255) NOT NULL,
    secret_key character varying(255) NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_app_ram OWNER TO suwell;

--
-- TOC entry 4894 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.app_id IS 'appid';


--
-- TOC entry 4895 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.access_key IS '公钥';


--
-- TOC entry 4896 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.secret_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.secret_key IS '密钥';


--
-- TOC entry 4897 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.del_flag IS '删除标识';


--
-- TOC entry 4898 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.create_by IS '创建人';


--
-- TOC entry 4899 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.create_time IS '创建时间';


--
-- TOC entry 4900 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.update_by IS '修改人';


--
-- TOC entry 4901 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.update_time IS '修改时间';


--
-- TOC entry 4902 (class 0 OID 0)
-- Dependencies: 335
-- Name: COLUMN op_app_ram.remark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.remark IS '备注';


--
-- TOC entry 376 (class 1259 OID 66994)
-- Name: op_preview; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_preview (
    id bigint NOT NULL,
    file_name character varying NOT NULL,
    file_url character varying NOT NULL,
    water_mark character varying NOT NULL,
    expire_date timestamp without time zone NOT NULL,
    permission character varying NOT NULL,
    create_time timestamp without time zone NOT NULL,
    app_id character varying NOT NULL
);


ALTER TABLE "plss-open".op_preview OWNER TO suwell;

--
-- TOC entry 4903 (class 0 OID 0)
-- Dependencies: 376
-- Name: TABLE op_preview; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_preview IS '生成轻阅读预览链接';


--
-- TOC entry 4904 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.file_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.file_name IS '预览文件名称';


--
-- TOC entry 4905 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.file_url; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.file_url IS '文件url';


--
-- TOC entry 4906 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.water_mark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.water_mark IS '水印';


--
-- TOC entry 4907 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.expire_date; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.expire_date IS '过期时间';


--
-- TOC entry 4908 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.permission; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.permission IS '权限';


--
-- TOC entry 4909 (class 0 OID 0)
-- Dependencies: 376
-- Name: COLUMN op_preview.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.create_time IS '创建时间';


--
-- TOC entry 336 (class 1259 OID 31701)
-- Name: op_uniform_resource; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_uniform_resource (
    id bigint NOT NULL,
    module_name character varying(255) NOT NULL,
    ur_name character varying(255) NOT NULL,
    ur_path character varying(255) NOT NULL,
    ur_desc character varying(255) DEFAULT ''::character varying NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_uniform_resource OWNER TO suwell;

--
-- TOC entry 4910 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.module_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.module_name IS '模块名称';


--
-- TOC entry 4911 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.ur_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_name IS '接口名称';


--
-- TOC entry 4912 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.ur_path; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_path IS '接口路径';


--
-- TOC entry 4913 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.ur_desc; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_desc IS '接口描述';


--
-- TOC entry 4914 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.status; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.status IS '接口状态';


--
-- TOC entry 4915 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.del_flag IS '删除标识';


--
-- TOC entry 4916 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.create_by IS '创建人';


--
-- TOC entry 4917 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.create_time IS '创建时间';


--
-- TOC entry 4918 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.update_by IS '修改人';


--
-- TOC entry 4919 (class 0 OID 0)
-- Dependencies: 336
-- Name: COLUMN op_uniform_resource.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.update_time IS '修改时间';


--
-- TOC entry 338 (class 1259 OID 31732)
-- Name: op_ur_log; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_ur_log (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    user_name character varying(64) NOT NULL,
    access_key character varying(64) NOT NULL,
    visit_ip character varying(64) DEFAULT ''::character varying NOT NULL,
    request_id character varying(64) NOT NULL,
    ur_id bigint NOT NULL,
    ur_name character varying(64) NOT NULL,
    ur_result character varying(255) NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_ur_log OWNER TO suwell;

--
-- TOC entry 4920 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.user_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.user_id IS '用户id';


--
-- TOC entry 4921 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.user_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.user_name IS '用户名称';


--
-- TOC entry 4922 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.access_key IS '公钥';


--
-- TOC entry 4923 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.visit_ip; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.visit_ip IS '访问ip';


--
-- TOC entry 4924 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.request_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.request_id IS '请求id';


--
-- TOC entry 4925 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.ur_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_id IS '接口id';


--
-- TOC entry 4926 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.ur_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_name IS '接口名称';


--
-- TOC entry 4927 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.ur_result; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_result IS '接口结果';


--
-- TOC entry 4928 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.del_flag IS '删除标识';


--
-- TOC entry 4929 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.create_by IS '创建人';


--
-- TOC entry 4930 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.create_time IS '创建时间';


--
-- TOC entry 4931 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.update_by IS '修改人';


--
-- TOC entry 4932 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.update_time IS '修改时间';


--
-- TOC entry 4933 (class 0 OID 0)
-- Dependencies: 338
-- Name: COLUMN op_ur_log.remark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.remark IS '备注';


--
-- TOC entry 337 (class 1259 OID 31716)
-- Name: op_ur_permission; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_ur_permission (
    id bigint NOT NULL,
    access_key character varying(64) NOT NULL,
    ur_id bigint NOT NULL,
    permission_type smallint NOT NULL,
    visit_num integer DEFAULT 0 NOT NULL,
    visit_days integer DEFAULT 0 NOT NULL,
    visit_ip character varying(64) DEFAULT ''::character varying NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_ur_permission OWNER TO suwell;

--
-- TOC entry 4934 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.access_key IS 'ak';


--
-- TOC entry 4935 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.ur_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.ur_id IS '接口id';


--
-- TOC entry 4936 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.permission_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.permission_type IS '权限类型：1访问2测试3vip';


--
-- TOC entry 4937 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.visit_num; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_num IS '访问次数';


--
-- TOC entry 4938 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.visit_days; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_days IS '访问天数';


--
-- TOC entry 4939 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.visit_ip; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_ip IS '访问ip';


--
-- TOC entry 4940 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.del_flag IS '删除标识';


--
-- TOC entry 4941 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.create_by IS '创建人';


--
-- TOC entry 4942 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.create_time IS '创建时间';


--
-- TOC entry 4943 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.update_by IS '修改人';


--
-- TOC entry 4944 (class 0 OID 0)
-- Dependencies: 337
-- Name: COLUMN op_ur_permission.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.update_time IS '修改时间';


--
-- TOC entry 358 (class 1259 OID 33230)
-- Name: ae_analysis_req; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".ae_analysis_req (
    id bigint NOT NULL,
    doc_id bigint NOT NULL,
    ofd_bucket character varying(50) NOT NULL,
    ofd_object_name character varying(150) NOT NULL,
    req_body text,
    task_no character varying(256),
    task_result_status smallint NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted smallint DEFAULT 0 NOT NULL
);


ALTER TABLE "plss-plugin".ae_analysis_req OWNER TO suwell;

--
-- TOC entry 4945 (class 0 OID 0)
-- Dependencies: 358
-- Name: TABLE ae_analysis_req; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON TABLE "plss-plugin".ae_analysis_req IS '文档ai提取结果记录表';


--
-- TOC entry 4946 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.id IS '数据id';


--
-- TOC entry 4947 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.doc_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.doc_id IS '文档id';


--
-- TOC entry 4948 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.ofd_bucket; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.ofd_bucket IS 'ofd文件存储的桶';


--
-- TOC entry 4949 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.ofd_object_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.ofd_object_name IS 'ofd文件存储对象名称';


--
-- TOC entry 4950 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.req_body; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.req_body IS '请求参数';


--
-- TOC entry 4951 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.task_no; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.task_no IS '任务号';


--
-- TOC entry 4952 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.task_result_status; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.task_result_status IS '任务结果状态';


--
-- TOC entry 4953 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.create_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.create_time IS '创建时间';


--
-- TOC entry 4954 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.update_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.update_time IS '修改时间';


--
-- TOC entry 4955 (class 0 OID 0)
-- Dependencies: 358
-- Name: COLUMN ae_analysis_req.deleted; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_req.deleted IS '数据是否删除：0-未删，1-已删';


--
-- TOC entry 359 (class 1259 OID 33240)
-- Name: ae_analysis_result; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".ae_analysis_result (
    id bigint NOT NULL,
    req_id bigint NOT NULL,
    doc_id bigint NOT NULL,
    task_no character varying(256) NOT NULL,
    req_body text,
    result_body text,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted smallint DEFAULT 0 NOT NULL
);


ALTER TABLE "plss-plugin".ae_analysis_result OWNER TO suwell;

--
-- TOC entry 4956 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.id IS '数据id';


--
-- TOC entry 4957 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.req_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.req_id IS '请求id';


--
-- TOC entry 4958 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.doc_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.doc_id IS '文档id';


--
-- TOC entry 4959 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.task_no; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.task_no IS '任务号';


--
-- TOC entry 4960 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.req_body; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.req_body IS '请求参数';


--
-- TOC entry 4961 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.result_body; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.result_body IS '提取结果（不包含向量数据）';


--
-- TOC entry 4962 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.create_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.create_time IS '创建时间';


--
-- TOC entry 4963 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.update_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.update_time IS '修改时间';


--
-- TOC entry 4964 (class 0 OID 0)
-- Dependencies: 359
-- Name: COLUMN ae_analysis_result.deleted; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_analysis_result.deleted IS '数据是否删除：0-未删，1-已删';


--
-- TOC entry 360 (class 1259 OID 33266)
-- Name: ae_resource_publish; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".ae_resource_publish (
    id bigint NOT NULL,
    doc_id bigint,
    publish_type smallint NOT NULL,
    publish_content character varying(800),
    remake character varying(500),
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE "plss-plugin".ae_resource_publish OWNER TO suwell;

--
-- TOC entry 4965 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.id IS '数据id';


--
-- TOC entry 4966 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.doc_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.doc_id IS '文档id';


--
-- TOC entry 4967 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.publish_type; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.publish_type IS '发布类型：0-新增文档；1-删除文档；2-修改文档';


--
-- TOC entry 4968 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.publish_content; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.publish_content IS '内容';


--
-- TOC entry 4969 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.remake; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.remake IS '备注';


--
-- TOC entry 4970 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.create_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.create_time IS '创建时间';


--
-- TOC entry 4971 (class 0 OID 0)
-- Dependencies: 360
-- Name: COLUMN ae_resource_publish.update_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".ae_resource_publish.update_time IS '修改时间';


--
-- TOC entry 383 (class 1259 OID 162795)
-- Name: bj_proofread_result; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_proofread_result (
    id bigint NOT NULL,
    task_id bigint NOT NULL,
    task_no character varying(128) NOT NULL,
    result text NOT NULL,
    check_result character varying(3900) NOT NULL,
    create_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-plugin".bj_proofread_result OWNER TO suwell;

--
-- TOC entry 384 (class 1259 OID 162800)
-- Name: bj_proofread_task; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_proofread_task (
    id bigint NOT NULL,
    task_no character varying(64) NOT NULL,
    business_no character varying(50) NOT NULL,
    content character varying(3900) NOT NULL,
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    update_time timestamp without time zone NOT NULL,
    line integer NOT NULL,
    user_id character varying(128) NOT NULL,
    params text NOT NULL
);


ALTER TABLE "plss-plugin".bj_proofread_task OWNER TO suwell;

--
-- TOC entry 385 (class 1259 OID 162805)
-- Name: bj_sensite_lib; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_sensite_lib (
    id bigint NOT NULL,
    wrong_lib character varying(15) NOT NULL,
    suggest_lib character varying(15) NOT NULL,
    sensite_flag smallint NOT NULL,
    create_by bigint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    update_by bigint NOT NULL,
    update_time timestamp without time zone NOT NULL,
    sensite_lib character varying(31) NOT NULL
);


ALTER TABLE "plss-plugin".bj_sensite_lib OWNER TO suwell;

--
-- TOC entry 386 (class 1259 OID 162808)
-- Name: bj_user_info; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_user_info (
    uuid character varying(50),
    userid bigint,
    username character varying(50),
    userstate character varying(50),
    authwordscount bigint,
    checkwordscount bigint,
    remainingwordscount bigint,
    authstarttime character varying(50),
    authendtime character varying(50),
    registertime character varying(50),
    "upDATETIME" character varying(50),
    id bigint NOT NULL
);


ALTER TABLE "plss-plugin".bj_user_info OWNER TO suwell;

--
-- TOC entry 387 (class 1259 OID 162811)
-- Name: bj_user_lib; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_user_lib (
    id bigint NOT NULL,
    user_lib character varying(15) NOT NULL,
    user_lib_flag smallint NOT NULL,
    create_by bigint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    update_by bigint NOT NULL,
    update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-plugin".bj_user_lib OWNER TO suwell;

--
-- TOC entry 388 (class 1259 OID 162814)
-- Name: bj_user_token; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".bj_user_token (
    id bigint NOT NULL,
    uuid character varying(50),
    username character varying(50),
    token character varying(1000),
    authno character varying(50),
    expiretime character varying(50)
);


ALTER TABLE "plss-plugin".bj_user_token OWNER TO suwell;

--
-- TOC entry 309 (class 1259 OID 28296)
-- Name: cma_attachment; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".cma_attachment (
    id bigint NOT NULL,
    doc_id bigint NOT NULL,
    file_name character varying NOT NULL,
    share_file_name character varying NOT NULL,
    url character varying NOT NULL,
    ctype smallint NOT NULL
);


ALTER TABLE "plss-plugin".cma_attachment OWNER TO suwell;

--
-- TOC entry 4972 (class 0 OID 0)
-- Dependencies: 309
-- Name: COLUMN cma_attachment.share_file_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_attachment.share_file_name IS '文件名称供获取临时下载链接使用';


--
-- TOC entry 4973 (class 0 OID 0)
-- Dependencies: 309
-- Name: COLUMN cma_attachment.url; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_attachment.url IS '文件路径';


--
-- TOC entry 4974 (class 0 OID 0)
-- Dependencies: 309
-- Name: COLUMN cma_attachment.ctype; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_attachment.ctype IS '正文0、附件1';


--
-- TOC entry 308 (class 1259 OID 28289)
-- Name: cma_doc_metadata; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".cma_doc_metadata (
    id bigint NOT NULL,
    doc_id bigint NOT NULL,
    doc_content text NOT NULL
);


ALTER TABLE "plss-plugin".cma_doc_metadata OWNER TO suwell;

--
-- TOC entry 4975 (class 0 OID 0)
-- Dependencies: 308
-- Name: COLUMN cma_doc_metadata.doc_content; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_doc_metadata.doc_content IS '文件内容';


--
-- TOC entry 307 (class 1259 OID 28282)
-- Name: cma_document; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".cma_document (
    id bigint NOT NULL,
    file_name character varying NOT NULL,
    status smallint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    origin character varying NOT NULL
);


ALTER TABLE "plss-plugin".cma_document OWNER TO suwell;

--
-- TOC entry 4976 (class 0 OID 0)
-- Dependencies: 307
-- Name: COLUMN cma_document.status; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_document.status IS '未处理0、已处理1';


--
-- TOC entry 4977 (class 0 OID 0)
-- Dependencies: 307
-- Name: COLUMN cma_document.origin; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_document.origin IS '来源';


--
-- TOC entry 306 (class 1259 OID 28269)
-- Name: cma_institutional_classification; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".cma_institutional_classification (
    id bigint NOT NULL,
    code character varying(25) NOT NULL,
    name character varying(64) NOT NULL,
    dept_id character varying(64) NOT NULL,
    type integer NOT NULL,
    pid character varying(25) NOT NULL,
    level integer DEFAULT 0 NOT NULL,
    orders integer DEFAULT 0 NOT NULL,
    status smallint DEFAULT 0 NOT NULL,
    del_flag smallint DEFAULT 1 NOT NULL,
    create_emp character varying,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_emp character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    org_code character varying,
    hide character(1),
    create_org_code character varying,
    code_num character varying,
    folder_id character varying,
    tenant_id bigint,
    repo_id bigint,
    org_owner_id bigint,
    org_id bigint,
    org_name character varying(64),
    org_level character varying(5)
);


ALTER TABLE "plss-plugin".cma_institutional_classification OWNER TO suwell;

--
-- TOC entry 4978 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.code; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.code IS '分类code';


--
-- TOC entry 4979 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.name IS '分类名称';


--
-- TOC entry 4980 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.dept_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.dept_id IS '分类部门id';


--
-- TOC entry 4981 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.pid; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.pid IS '父id';


--
-- TOC entry 4982 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.level; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.level IS '分类层级';


--
-- TOC entry 4983 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.orders; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.orders IS '序号';


--
-- TOC entry 4984 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.status; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.status IS '状态';


--
-- TOC entry 4985 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.org_code; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.org_code IS '单位code';


--
-- TOC entry 4986 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.create_org_code; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.create_org_code IS '创建单位';


--
-- TOC entry 4987 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.folder_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.folder_id IS 'record目录id';


--
-- TOC entry 4988 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.tenant_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.tenant_id IS '租户id';


--
-- TOC entry 4989 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.repo_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.repo_id IS 'record库id';


--
-- TOC entry 4990 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.org_owner_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.org_owner_id IS '单位onwer';


--
-- TOC entry 4991 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.org_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.org_id IS '单位id';


--
-- TOC entry 4992 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.org_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.org_name IS '单位名称';


--
-- TOC entry 4993 (class 0 OID 0)
-- Dependencies: 306
-- Name: COLUMN cma_institutional_classification.org_level; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".cma_institutional_classification.org_level IS '单位层级';


--
-- TOC entry 311 (class 1259 OID 28421)
-- Name: med_patient_info; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".med_patient_info (
    id bigint NOT NULL,
    id_number character varying(255) NOT NULL,
    patient_name character varying(255) NOT NULL,
    patient_dob character varying(255) NOT NULL,
    patient_sex_name character varying(255) NOT NULL,
    patient_age character varying(255),
    event_type character varying(255) NOT NULL,
    hospitalization_frequency character varying(255),
    admission_date_time character varying(255),
    admission_dept_name character varying(255),
    discharge_date_time character varying(255),
    dept_name character varying(255),
    discharge_method character varying(255),
    attending_doctor_name character varying(255),
    responsible_nuse character varying(255),
    medicare_category_name character varying(255),
    admission_ward_name character varying(255),
    medical_record_no character varying(255),
    register_category character varying(255),
    admission_number character varying(255),
    outpatient_number character varying(255),
    discharge_ward_name character varying(255),
    diagnostic_information character varying(255),
    unique_dentification character varying(255),
    event_number character varying(255)
);


ALTER TABLE "plss-plugin".med_patient_info OWNER TO suwell;

--
-- TOC entry 4994 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.id IS '患者唯一标识';


--
-- TOC entry 4995 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.id_number; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.id_number IS '患者ID';


--
-- TOC entry 4996 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.patient_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.patient_name IS '患者姓名';


--
-- TOC entry 4997 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.patient_dob; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.patient_dob IS '出生日期';


--
-- TOC entry 4998 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.patient_sex_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.patient_sex_name IS '性别';


--
-- TOC entry 4999 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.patient_age; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.patient_age IS '就诊年龄';


--
-- TOC entry 5000 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.event_type; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.event_type IS '诊疗事件类型';


--
-- TOC entry 5001 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.hospitalization_frequency; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.hospitalization_frequency IS '住院次数';


--
-- TOC entry 5002 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.admission_date_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.admission_date_time IS '就诊/入院时间';


--
-- TOC entry 5003 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.admission_dept_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.admission_dept_name IS '就诊/入院科室';


--
-- TOC entry 5004 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.discharge_date_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.discharge_date_time IS '出院时间';


--
-- TOC entry 5005 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.dept_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.dept_name IS '出院科室';


--
-- TOC entry 5006 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.discharge_method; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.discharge_method IS '出院方式';


--
-- TOC entry 5007 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.attending_doctor_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.attending_doctor_name IS '主治医生';


--
-- TOC entry 5008 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.responsible_nuse; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.responsible_nuse IS '责任护士';


--
-- TOC entry 5009 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.medicare_category_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.medicare_category_name IS '付费类型';


--
-- TOC entry 5010 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.admission_ward_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.admission_ward_name IS '就诊/入院病区';


--
-- TOC entry 5011 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.medical_record_no; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.medical_record_no IS '病案号';


--
-- TOC entry 5012 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.register_category; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.register_category IS '挂号类型';


--
-- TOC entry 5013 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.admission_number; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.admission_number IS '住院号';


--
-- TOC entry 5014 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.outpatient_number; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.outpatient_number IS '门诊号';


--
-- TOC entry 5015 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.discharge_ward_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.discharge_ward_name IS '出院病区';


--
-- TOC entry 5016 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.diagnostic_information; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.diagnostic_information IS '诊断信息';


--
-- TOC entry 5017 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.unique_dentification; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.unique_dentification IS '患者唯一标识';


--
-- TOC entry 5018 (class 0 OID 0)
-- Dependencies: 311
-- Name: COLUMN med_patient_info.event_number; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".med_patient_info.event_number IS '诊疗标识';


--
-- TOC entry 354 (class 1259 OID 32852)
-- Name: web_office_file_record; Type: TABLE; Schema: plss-plugin; Owner: suwell
--

CREATE TABLE "plss-plugin".web_office_file_record (
    id bigint NOT NULL,
    file_name character varying(256),
    bucket_name character varying(50) NOT NULL,
    object_name character varying(256) NOT NULL,
    file_size bigint,
    module_tag character varying(256),
    remake character varying(500),
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    create_by bigint,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_by bigint,
    deleted smallint DEFAULT 0,
    file_id bigint
);


ALTER TABLE "plss-plugin".web_office_file_record OWNER TO suwell;

--
-- TOC entry 5019 (class 0 OID 0)
-- Dependencies: 354
-- Name: TABLE web_office_file_record; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON TABLE "plss-plugin".web_office_file_record IS '在线操作保存文档记录表';


--
-- TOC entry 5020 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.id IS '数据id';


--
-- TOC entry 5021 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.file_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.file_name IS '文件名';


--
-- TOC entry 5022 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.bucket_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.bucket_name IS '存储桶';


--
-- TOC entry 5023 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.object_name; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.object_name IS '存储路径';


--
-- TOC entry 5024 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.file_size; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.file_size IS '文件size';


--
-- TOC entry 5025 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.module_tag; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.module_tag IS '业务模块说明';


--
-- TOC entry 5026 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.remake; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.remake IS '备注';


--
-- TOC entry 5027 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.create_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.create_time IS '创建时间';


--
-- TOC entry 5028 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.create_by; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.create_by IS '创建者';


--
-- TOC entry 5029 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.update_time; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.update_time IS '修改时间';


--
-- TOC entry 5030 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.update_by; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.update_by IS '修改者';


--
-- TOC entry 5031 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.deleted; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.deleted IS '数据是否删除：0-未删，1-已删';


--
-- TOC entry 5032 (class 0 OID 0)
-- Dependencies: 354
-- Name: COLUMN web_office_file_record.file_id; Type: COMMENT; Schema: plss-plugin; Owner: suwell
--

COMMENT ON COLUMN "plss-plugin".web_office_file_record.file_id IS '文件存储id';


--
-- TOC entry 370 (class 1259 OID 39777)
-- Name: rc_doc_audit; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_doc_audit (
    id bigint NOT NULL,
    record_id bigint NOT NULL,
    user_id bigint NOT NULL,
    user_nick_name character varying(256) NOT NULL,
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_doc_audit OWNER TO suwell;

--
-- TOC entry 5033 (class 0 OID 0)
-- Dependencies: 370
-- Name: TABLE rc_doc_audit; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_doc_audit IS '文档审批表';


--
-- TOC entry 5034 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.id IS '主键';


--
-- TOC entry 5035 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.record_id IS '文件id';


--
-- TOC entry 5036 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.user_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.user_id IS '用户id';


--
-- TOC entry 5037 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.user_nick_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.user_nick_name IS '用户昵称';


--
-- TOC entry 5038 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.status IS '状态：1-通过 2-拒绝';


--
-- TOC entry 5039 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.create_time IS '创建时间';


--
-- TOC entry 5040 (class 0 OID 0)
-- Dependencies: 370
-- Name: COLUMN rc_doc_audit.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_audit.modified_time IS '修改时间';


--
-- TOC entry 341 (class 1259 OID 31821)
-- Name: rc_doc_process; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_doc_process (
    id bigint NOT NULL,
    batch_id bigint NOT NULL,
    task_doc_id bigint NOT NULL,
    record_id bigint,
    name character varying(256) NOT NULL,
    record_type_id bigint NOT NULL,
    origin smallint NOT NULL,
    process_status smallint NOT NULL,
    process_sub_status smallint NOT NULL,
    audit_status smallint,
    deleted smallint NOT NULL,
    create_by bigint NOT NULL,
    create_by_name character varying(256) NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    plan_id bigint NOT NULL,
    ctype smallint DEFAULT 1,
    tenant_id bigint,
    title character varying(512),
    task_id bigint,
    real_origin integer,
    pre_record_id bigint,
    store_way integer DEFAULT 1
);


ALTER TABLE "plss-record".rc_doc_process OWNER TO suwell;

--
-- TOC entry 5041 (class 0 OID 0)
-- Dependencies: 341
-- Name: TABLE rc_doc_process; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_doc_process IS '文档处理表';


--
-- TOC entry 5042 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.id IS '主键';


--
-- TOC entry 5043 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.batch_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.batch_id IS '批次id，rc_task_batch表主键';


--
-- TOC entry 5044 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.task_doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.task_doc_id IS '任务文件id，rc_task_doc表主键';


--
-- TOC entry 5045 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.record_id IS '文件id，rc_record表主键';


--
-- TOC entry 5046 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.name IS '文件名称';


--
-- TOC entry 5047 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.record_type_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.record_type_id IS '文件类型id';


--
-- TOC entry 5048 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.origin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.origin IS '文档来源';


--
-- TOC entry 5049 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.process_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.process_status IS '处理状态';


--
-- TOC entry 5050 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.process_sub_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.process_sub_status IS '处理子状态';


--
-- TOC entry 5051 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.audit_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.audit_status IS '审核状态';


--
-- TOC entry 5052 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.deleted; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.deleted IS '1-正常 2-删除';


--
-- TOC entry 5053 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.create_by IS '操作人员';


--
-- TOC entry 5054 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.create_by_name IS '操作人员姓名';


--
-- TOC entry 5055 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.create_time IS '上传时间';


--
-- TOC entry 5056 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.modified_time IS '修改时间';


--
-- TOC entry 5057 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.plan_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.plan_id IS '方案id';


--
-- TOC entry 5058 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.ctype IS '1-主文件 2-子文件';


--
-- TOC entry 5059 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.tenant_id IS '租户id';


--
-- TOC entry 5060 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.title; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.title IS '文档标题';


--
-- TOC entry 5061 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.task_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.task_id IS '任务id';


--
-- TOC entry 5062 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.real_origin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.real_origin IS '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传';


--
-- TOC entry 5063 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.pre_record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.pre_record_id IS '对应的个人库record_id';


--
-- TOC entry 5064 (class 0 OID 0)
-- Dependencies: 341
-- Name: COLUMN rc_doc_process.store_way; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_doc_process.store_way IS '入库方式：1-后台  2-前台';


--
-- TOC entry 248 (class 1259 OID 25555)
-- Name: rc_document; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_document (
    id bigint NOT NULL,
    record_id bigint NOT NULL,
    name character varying(256) NOT NULL,
    ctype smallint NOT NULL,
    file_size bigint,
    file_md5 character varying(256),
    bucket_name character varying(256),
    object_name character varying(256),
    ofd_file_size bigint,
    ofd_file_md5 character varying(256),
    ofd_bucket_name character varying(256),
    ofd_object_name character varying(256),
    txt_file_size bigint,
    txt_file_md5 character varying(256),
    txt_bucket_name character varying(256),
    txt_object_name character varying(256),
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    file_id bigint,
    ofd_file_id bigint,
    txt_file_id bigint,
    attachment_type integer
);


ALTER TABLE "plss-record".rc_document OWNER TO suwell;

--
-- TOC entry 5065 (class 0 OID 0)
-- Dependencies: 248
-- Name: TABLE rc_document; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_document IS '文档表';


--
-- TOC entry 5066 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.id IS '唯一标识';


--
-- TOC entry 5067 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.record_id IS '文件id';


--
-- TOC entry 5068 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.name IS '文档名称';


--
-- TOC entry 5069 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ctype IS '文档类型(1:主文档, 2:附件)';


--
-- TOC entry 5070 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.file_size IS '文档大小';


--
-- TOC entry 5071 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.file_md5 IS '文档MD5值';


--
-- TOC entry 5072 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.bucket_name IS '存储桶名称';


--
-- TOC entry 5073 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.object_name IS '存储对象名';


--
-- TOC entry 5074 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ofd_file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ofd_file_size IS 'ofd文档大小';


--
-- TOC entry 5075 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ofd_file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ofd_file_md5 IS 'ofd文档MD5值';


--
-- TOC entry 5076 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ofd_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ofd_bucket_name IS 'ofd存储桶名称';


--
-- TOC entry 5077 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ofd_object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ofd_object_name IS 'ofd存储对象名';


--
-- TOC entry 5078 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.txt_file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.txt_file_size IS 'txt文档大小';


--
-- TOC entry 5079 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.txt_file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.txt_file_md5 IS 'txt文档MD5值';


--
-- TOC entry 5080 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.txt_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.txt_bucket_name IS 'txt存储桶名称';


--
-- TOC entry 5081 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.txt_object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.txt_object_name IS 'txt存储对象名';


--
-- TOC entry 5082 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5083 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.create_time IS '创建时间';


--
-- TOC entry 5084 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.create_by IS '创建人';


--
-- TOC entry 5085 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.modified_time IS '修改时间';


--
-- TOC entry 5086 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.modified_by IS '修改人';


--
-- TOC entry 5087 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.file_id IS '原件在对象存储中文件id';


--
-- TOC entry 5088 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.ofd_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.ofd_file_id IS 'ofd在对象存储中文件id';


--
-- TOC entry 5089 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.txt_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.txt_file_id IS 'txt在对象存储中文件id';


--
-- TOC entry 5090 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN rc_document.attachment_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';


--
-- TOC entry 243 (class 1259 OID 25388)
-- Name: rc_document_history; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_document_history (
    id bigint NOT NULL,
    doc_id bigint NOT NULL,
    name character varying(256) NOT NULL,
    file_size bigint,
    file_md5 character varying(256),
    bucket_name character varying(256),
    object_name character varying(256),
    ofd_file_size bigint,
    ofd_file_md5 character varying(256),
    ofd_bucket_name character varying(256),
    ofd_object_name character varying(256),
    txt_file_size bigint,
    txt_file_md5 character varying(256),
    txt_bucket_name character varying(256),
    txt_object_name character varying(256),
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    file_id bigint,
    ofd_file_id bigint,
    txt_file_id bigint
);


ALTER TABLE "plss-record".rc_document_history OWNER TO suwell;

--
-- TOC entry 5091 (class 0 OID 0)
-- Dependencies: 243
-- Name: TABLE rc_document_history; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_document_history IS '文档表';


--
-- TOC entry 5092 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.id IS '唯一标识';


--
-- TOC entry 5093 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.doc_id IS '文档id';


--
-- TOC entry 5094 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.name IS '文档名称';


--
-- TOC entry 5095 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.file_size IS '文档大小';


--
-- TOC entry 5096 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.file_md5 IS '文档MD5值';


--
-- TOC entry 5097 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.bucket_name IS '存储桶名称';


--
-- TOC entry 5098 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.object_name IS '存储对象名';


--
-- TOC entry 5099 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.ofd_file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.ofd_file_size IS 'ofd文档大小';


--
-- TOC entry 5100 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.ofd_file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.ofd_file_md5 IS 'ofd文档MD5值';


--
-- TOC entry 5101 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.ofd_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.ofd_bucket_name IS 'ofd存储桶名称';


--
-- TOC entry 5102 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.ofd_object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.ofd_object_name IS 'ofd存储对象名';


--
-- TOC entry 5103 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.txt_file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.txt_file_size IS 'txt文档大小';


--
-- TOC entry 5104 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.txt_file_md5; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.txt_file_md5 IS 'txt文档MD5值';


--
-- TOC entry 5105 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.txt_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.txt_bucket_name IS 'txt存储桶名称';


--
-- TOC entry 5106 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.txt_object_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.txt_object_name IS 'txt存储对象名';


--
-- TOC entry 5107 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5108 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.create_time IS '创建时间';


--
-- TOC entry 5109 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.create_by IS '创建人';


--
-- TOC entry 5110 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.modified_time IS '修改时间';


--
-- TOC entry 5111 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.modified_by IS '修改人';


--
-- TOC entry 5112 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.file_id IS '原件在对象存储中文件id';


--
-- TOC entry 5113 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.ofd_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.ofd_file_id IS 'ofd在对象存储中文件id';


--
-- TOC entry 5114 (class 0 OID 0)
-- Dependencies: 243
-- Name: COLUMN rc_document_history.txt_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_document_history.txt_file_id IS 'txt在对象存储中文件id';


--
-- TOC entry 373 (class 1259 OID 52941)
-- Name: rc_export_filter; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_export_filter (
    id bigint NOT NULL,
    task_id bigint NOT NULL,
    repo_id bigint,
    repo_name character varying(64),
    put_lib_min timestamp without time zone,
    put_lib_max timestamp without time zone,
    md_value_json character varying(2048),
    limit_record integer NOT NULL,
    total_record integer
);


ALTER TABLE "plss-record".rc_export_filter OWNER TO suwell;

--
-- TOC entry 5115 (class 0 OID 0)
-- Dependencies: 373
-- Name: TABLE rc_export_filter; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_export_filter IS '数据导出过滤器表';


--
-- TOC entry 5116 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.id IS 'id';


--
-- TOC entry 5117 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.task_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.task_id IS '任务id';


--
-- TOC entry 5118 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.repo_id IS '库id';


--
-- TOC entry 5119 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.repo_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.repo_name IS '库名称';


--
-- TOC entry 5120 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.put_lib_min; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.put_lib_min IS '入库时间-开始';


--
-- TOC entry 5121 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.put_lib_max; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.put_lib_max IS '入库时间-截止';


--
-- TOC entry 5122 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.md_value_json; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.md_value_json IS '元数据项过滤';


--
-- TOC entry 5123 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.limit_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.limit_record IS '指定输出记录数';


--
-- TOC entry 5124 (class 0 OID 0)
-- Dependencies: 373
-- Name: COLUMN rc_export_filter.total_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_filter.total_record IS '实际输出记录数';


--
-- TOC entry 372 (class 1259 OID 52932)
-- Name: rc_export_task; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_export_task (
    id bigint NOT NULL,
    name character varying(256) NOT NULL,
    status smallint NOT NULL,
    start_time timestamp without time zone,
    finish_time timestamp without time zone,
    dir_path character varying(256) NOT NULL,
    gmt_create timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_export_task OWNER TO suwell;

--
-- TOC entry 5125 (class 0 OID 0)
-- Dependencies: 372
-- Name: TABLE rc_export_task; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_export_task IS '数据导出任务表';


--
-- TOC entry 5126 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.id IS 'id';


--
-- TOC entry 5127 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.name IS '任务名称';


--
-- TOC entry 5128 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.status IS '状态(1:等待导出, 2:正在导出, 3:导出完成)';


--
-- TOC entry 5129 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.start_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.start_time IS '开始时间';


--
-- TOC entry 5130 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.finish_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.finish_time IS '完成时间';


--
-- TOC entry 5131 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.dir_path; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.dir_path IS '导出目录';


--
-- TOC entry 5132 (class 0 OID 0)
-- Dependencies: 372
-- Name: COLUMN rc_export_task.gmt_create; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_export_task.gmt_create IS '创建时间';


--
-- TOC entry 361 (class 1259 OID 33294)
-- Name: rc_file; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_file (
    id bigint NOT NULL,
    platform character varying(30) NOT NULL,
    file_type smallint NOT NULL,
    origin_name character varying(255) NOT NULL,
    file_name character varying(255) NOT NULL,
    md5_hex character varying(64) NOT NULL,
    file_size integer NOT NULL,
    file_ext character varying(255) NOT NULL,
    relative_path character varying(255) NOT NULL,
    encryption integer NOT NULL,
    encrypt_version integer,
    encrypt_password character varying(255),
    encrypt_magic_number character varying(32),
    create_by bigint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_file OWNER TO suwell;

--
-- TOC entry 5133 (class 0 OID 0)
-- Dependencies: 361
-- Name: TABLE rc_file; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_file IS '文件存储';


--
-- TOC entry 5134 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.id IS 'id';


--
-- TOC entry 5135 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.platform; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.platform IS '存储平台代码';


--
-- TOC entry 5136 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.file_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.file_type IS '文件类型，1项目数据，2内置数据';


--
-- TOC entry 5137 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.origin_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.origin_name IS '文件原始名';


--
-- TOC entry 5138 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.file_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.file_name IS '文件名';


--
-- TOC entry 5139 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.md5_hex; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.md5_hex IS '文件md5';


--
-- TOC entry 5140 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.file_size IS '文件大小';


--
-- TOC entry 5141 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.file_ext; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.file_ext IS '文件扩展名';


--
-- TOC entry 5142 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.relative_path; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.relative_path IS '相对路径';


--
-- TOC entry 5143 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.encryption; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.encryption IS '是否加密';


--
-- TOC entry 5144 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.encrypt_version; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.encrypt_version IS '加密版本号';


--
-- TOC entry 5145 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.encrypt_password; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.encrypt_password IS '基础密码';


--
-- TOC entry 5146 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.encrypt_magic_number; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.encrypt_magic_number IS '魔数';


--
-- TOC entry 5147 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.create_by IS '创建人id';


--
-- TOC entry 5148 (class 0 OID 0)
-- Dependencies: 361
-- Name: COLUMN rc_file.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_file.create_time IS '创建时间';


--
-- TOC entry 246 (class 1259 OID 25419)
-- Name: rc_folder; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_folder (
    id bigint NOT NULL,
    repo_id bigint,
    name character varying(256) NOT NULL,
    status smallint NOT NULL,
    image_url character varying(1024),
    visit_type smallint,
    org_id bigint,
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    create_by_name character varying(255)
);


ALTER TABLE "plss-record".rc_folder OWNER TO suwell;

--
-- TOC entry 5149 (class 0 OID 0)
-- Dependencies: 246
-- Name: TABLE rc_folder; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_folder IS '目录表';


--
-- TOC entry 5150 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.id IS '唯一标识';


--
-- TOC entry 5151 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.repo_id IS '库id';


--
-- TOC entry 5152 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.name IS '名称';


--
-- TOC entry 5153 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5154 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.image_url; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.image_url IS '图标url';


--
-- TOC entry 5155 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.visit_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.visit_type IS '访问类型(1:public, 2:private, 3:protect)';


--
-- TOC entry 5156 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.org_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.org_id IS '组织机构id';


--
-- TOC entry 5157 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.create_time IS '创建时间';


--
-- TOC entry 5158 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.create_by IS '创建人';


--
-- TOC entry 5159 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.modified_time IS '修改时间';


--
-- TOC entry 5160 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.modified_by IS '修改人';


--
-- TOC entry 5161 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.tenant_id IS '租户id';


--
-- TOC entry 5162 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN rc_folder.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder.create_by_name IS '创建人姓名';


--
-- TOC entry 223 (class 1259 OID 24613)
-- Name: rc_folder_record; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_folder_record (
    folder_id bigint NOT NULL,
    record_id bigint NOT NULL,
    create_by bigint,
    create_by_name character varying(255),
    create_time timestamp without time zone
);


ALTER TABLE "plss-record".rc_folder_record OWNER TO suwell;

--
-- TOC entry 5163 (class 0 OID 0)
-- Dependencies: 223
-- Name: TABLE rc_folder_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_folder_record IS '目录-文件关联表';


--
-- TOC entry 5164 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN rc_folder_record.folder_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_record.folder_id IS '目录id';


--
-- TOC entry 5165 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN rc_folder_record.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_record.record_id IS '文件id';


--
-- TOC entry 5166 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN rc_folder_record.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_record.create_by IS '创建人id';


--
-- TOC entry 5167 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN rc_folder_record.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_record.create_by_name IS '创建人姓名';


--
-- TOC entry 5168 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN rc_folder_record.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_record.create_time IS '创建时间';


--
-- TOC entry 224 (class 1259 OID 24616)
-- Name: rc_folder_rel; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_folder_rel (
    ancestor_id bigint NOT NULL,
    descendant_id bigint NOT NULL,
    distance integer NOT NULL,
    orderby integer,
    create_by bigint,
    create_by_name character varying(255),
    create_time timestamp without time zone,
    tenant_id bigint
);


ALTER TABLE "plss-record".rc_folder_rel OWNER TO suwell;

--
-- TOC entry 5169 (class 0 OID 0)
-- Dependencies: 224
-- Name: TABLE rc_folder_rel; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_folder_rel IS '目录-目录关联表';


--
-- TOC entry 5170 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.ancestor_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.ancestor_id IS '父辈id';


--
-- TOC entry 5171 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.descendant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.descendant_id IS '后代id';


--
-- TOC entry 5172 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.distance; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.distance IS '层级步数';


--
-- TOC entry 5173 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.orderby; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.orderby IS '排序';


--
-- TOC entry 5174 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.create_by IS '创建人id';


--
-- TOC entry 5175 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.create_by_name IS '创建人姓名';


--
-- TOC entry 5176 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.create_time IS '创建时间';


--
-- TOC entry 5177 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN rc_folder_rel.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_folder_rel.tenant_id IS '租户id';


--
-- TOC entry 350 (class 1259 OID 32737)
-- Name: rc_log_recordview; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_log_recordview (
    id bigint,
    record_id bigint NOT NULL,
    record_name character varying(256),
    user_id bigint NOT NULL,
    user_name character varying(256),
    view_ip character varying(256),
    view_useragent character varying(256),
    view_os character varying(256),
    view_browser character varying(256),
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    invalid_status smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-record".rc_log_recordview OWNER TO suwell;

--
-- TOC entry 5178 (class 0 OID 0)
-- Dependencies: 350
-- Name: COLUMN rc_log_recordview.invalid_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_log_recordview.invalid_status IS '效用状态(1有效2无效)';


--
-- TOC entry 357 (class 1259 OID 33210)
-- Name: rc_masking_config; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_masking_config (
    id bigint NOT NULL,
    config_type smallint DEFAULT 1 NOT NULL,
    repo_id bigint DEFAULT 0 NOT NULL,
    process_mode smallint DEFAULT 0 NOT NULL,
    metadata_status smallint DEFAULT 1 NOT NULL,
    word_status smallint DEFAULT 1 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_masking_config OWNER TO suwell;

--
-- TOC entry 5179 (class 0 OID 0)
-- Dependencies: 357
-- Name: TABLE rc_masking_config; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_masking_config IS '脱敏配置表';


--
-- TOC entry 5180 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.config_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.config_type IS '配置类型（0全局1库）';


--
-- TOC entry 5181 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.repo_id IS '库id';


--
-- TOC entry 5182 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.process_mode; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.process_mode IS '处理方式';


--
-- TOC entry 5183 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.metadata_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.metadata_status IS '元数据状态';


--
-- TOC entry 5184 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.word_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.word_status IS '敏感词状态';


--
-- TOC entry 5185 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.status IS '状态';


--
-- TOC entry 5186 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.del_flag IS '删除状态';


--
-- TOC entry 5187 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.create_by IS '创建人';


--
-- TOC entry 5188 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.create_time IS '创建日期';


--
-- TOC entry 5189 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.update_by IS '修改人';


--
-- TOC entry 5190 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.update_time IS '修改日期';


--
-- TOC entry 5191 (class 0 OID 0)
-- Dependencies: 357
-- Name: COLUMN rc_masking_config.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_config.remark IS '备注';


--
-- TOC entry 353 (class 1259 OID 32814)
-- Name: rc_masking_metadata; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_masking_metadata (
    id bigint NOT NULL,
    masking_config_id bigint NOT NULL,
    md_category_id bigint NOT NULL,
    md_id bigint NOT NULL,
    md_name character varying(512) DEFAULT ''::character varying,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_masking_metadata OWNER TO suwell;

--
-- TOC entry 5192 (class 0 OID 0)
-- Dependencies: 353
-- Name: TABLE rc_masking_metadata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_masking_metadata IS '脱敏元数据表';


--
-- TOC entry 5193 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.masking_config_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.masking_config_id IS '脱敏配置id';


--
-- TOC entry 5194 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.md_category_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.md_category_id IS '元数据分类id';


--
-- TOC entry 5195 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.md_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.md_id IS '元数据项id';


--
-- TOC entry 5196 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.md_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.md_name IS '元数据项名称';


--
-- TOC entry 5197 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.del_flag IS '删除状态';


--
-- TOC entry 5198 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.create_by IS '创建人';


--
-- TOC entry 5199 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.create_time IS '创建日期';


--
-- TOC entry 5200 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.update_by IS '修改人';


--
-- TOC entry 5201 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.update_time IS '修改日期';


--
-- TOC entry 5202 (class 0 OID 0)
-- Dependencies: 353
-- Name: COLUMN rc_masking_metadata.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_metadata.remark IS '备注';


--
-- TOC entry 352 (class 1259 OID 32799)
-- Name: rc_masking_range; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_masking_range (
    id bigint NOT NULL,
    masking_config_id bigint NOT NULL,
    range_type smallint DEFAULT 0 NOT NULL,
    range_id bigint NOT NULL,
    range_name character varying(512) DEFAULT ''::character varying,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_masking_range OWNER TO suwell;

--
-- TOC entry 5203 (class 0 OID 0)
-- Dependencies: 352
-- Name: TABLE rc_masking_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_masking_range IS '脱敏范围表';


--
-- TOC entry 5204 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.masking_config_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.masking_config_id IS '脱敏配置id';


--
-- TOC entry 5205 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.range_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.range_type IS '范围类型（0用户1角色2架构）';


--
-- TOC entry 5206 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.range_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.range_id IS '范围id(用户id角色id)';


--
-- TOC entry 5207 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.range_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.range_name IS '范围名称(用户名称角色名称)';


--
-- TOC entry 5208 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.del_flag IS '删除状态';


--
-- TOC entry 5209 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.create_by IS '创建人';


--
-- TOC entry 5210 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.create_time IS '创建日期';


--
-- TOC entry 5211 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.update_by IS '修改人';


--
-- TOC entry 5212 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.update_time IS '修改日期';


--
-- TOC entry 5213 (class 0 OID 0)
-- Dependencies: 352
-- Name: COLUMN rc_masking_range.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_range.remark IS '备注';


--
-- TOC entry 356 (class 1259 OID 33194)
-- Name: rc_masking_word; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_masking_word (
    id bigint NOT NULL,
    masking_config_id bigint NOT NULL,
    sensitive_categorty_id bigint NOT NULL,
    sensitive_categorty_name character varying(512) DEFAULT ''::character varying,
    sensitive_word_id bigint NOT NULL,
    sensitive_word_name character varying(1024) DEFAULT ''::character varying,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_masking_word OWNER TO suwell;

--
-- TOC entry 5214 (class 0 OID 0)
-- Dependencies: 356
-- Name: TABLE rc_masking_word; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_masking_word IS '脱敏-敏感词关联表';


--
-- TOC entry 5215 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.masking_config_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.masking_config_id IS '脱敏配置id';


--
-- TOC entry 5216 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.sensitive_categorty_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.sensitive_categorty_id IS '敏感词分类id';


--
-- TOC entry 5217 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.sensitive_categorty_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.sensitive_categorty_name IS '敏感词分类名称';


--
-- TOC entry 5218 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.sensitive_word_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.sensitive_word_id IS '敏感词id';


--
-- TOC entry 5219 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.sensitive_word_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.sensitive_word_name IS '敏感词名称';


--
-- TOC entry 5220 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.del_flag IS '删除状态';


--
-- TOC entry 5221 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.create_by IS '创建人';


--
-- TOC entry 5222 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.create_time IS '创建日期';


--
-- TOC entry 5223 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.update_by IS '修改人';


--
-- TOC entry 5224 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.update_time IS '修改日期';


--
-- TOC entry 5225 (class 0 OID 0)
-- Dependencies: 356
-- Name: COLUMN rc_masking_word.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_masking_word.remark IS '备注';


--
-- TOC entry 345 (class 1259 OID 32439)
-- Name: rc_material; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_material (
    id bigint NOT NULL,
    name character varying(64),
    material_type smallint NOT NULL,
    content text NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag smallint DEFAULT 1 NOT NULL,
    from_type smallint DEFAULT 1 NOT NULL,
    relation_id bigint,
    relation_name character varying(256),
    rep_id bigint NOT NULL,
    rep_name character varying(64) NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    modified_time timestamp without time zone DEFAULT now() NOT NULL,
    create_by bigint NOT NULL,
    create_user character varying(64) NOT NULL,
    reason character varying(1024),
    reason_type smallint
);


ALTER TABLE "plss-record".rc_material OWNER TO suwell;

--
-- TOC entry 5226 (class 0 OID 0)
-- Dependencies: 345
-- Name: TABLE rc_material; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_material IS '素材表';


--
-- TOC entry 5227 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.id IS '主键';


--
-- TOC entry 5228 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.name IS '素材名称';


--
-- TOC entry 5229 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.material_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.material_type IS '素材类型：1 通用素材；2 项目素材；3 个人素材；';


--
-- TOC entry 5230 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.content; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.content IS '素材内容';


--
-- TOC entry 5231 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.status IS '素材状态：1 可用；2 禁用';


--
-- TOC entry 5232 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.del_flag IS '删除标记：1 未删除；2 已删除';


--
-- TOC entry 5233 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.from_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.from_type IS '素材来源：1 直接新增；2 文件划词';


--
-- TOC entry 5234 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.relation_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.relation_id IS '素材来源业务id：from_type等于2时对应文件id';


--
-- TOC entry 5235 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.relation_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.relation_name IS '素材来源业务名称';


--
-- TOC entry 5236 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.rep_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.rep_id IS '来源库id：从哪个库直接或者划词新增的素材';


--
-- TOC entry 5237 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.rep_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.rep_name IS '来源库名称';


--
-- TOC entry 5238 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.create_time IS '创建时间';


--
-- TOC entry 5239 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.modified_time IS '更新时间';


--
-- TOC entry 5240 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.create_by IS '创建者id';


--
-- TOC entry 5241 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.create_user; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.create_user IS '创建者姓名';


--
-- TOC entry 5242 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.reason; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.reason IS '最新的变更原因';


--
-- TOC entry 5243 (class 0 OID 0)
-- Dependencies: 345
-- Name: COLUMN rc_material.reason_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material.reason_type IS '变更原因类型';


--
-- TOC entry 348 (class 1259 OID 32511)
-- Name: rc_material_category; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_material_category (
    id bigint NOT NULL,
    material_id bigint NOT NULL,
    category_id bigint NOT NULL,
    category_name character varying(64) NOT NULL,
    category_type smallint NOT NULL
);


ALTER TABLE "plss-record".rc_material_category OWNER TO suwell;

--
-- TOC entry 5244 (class 0 OID 0)
-- Dependencies: 348
-- Name: TABLE rc_material_category; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_material_category IS '素材与分类关系';


--
-- TOC entry 5245 (class 0 OID 0)
-- Dependencies: 348
-- Name: COLUMN rc_material_category.material_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_category.material_id IS '素材id';


--
-- TOC entry 5246 (class 0 OID 0)
-- Dependencies: 348
-- Name: COLUMN rc_material_category.category_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_category.category_id IS '分类id';


--
-- TOC entry 5247 (class 0 OID 0)
-- Dependencies: 348
-- Name: COLUMN rc_material_category.category_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_category.category_name IS '分类名称';


--
-- TOC entry 5248 (class 0 OID 0)
-- Dependencies: 348
-- Name: COLUMN rc_material_category.category_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_category.category_type IS '1 分类标签；2 素材标签';


--
-- TOC entry 346 (class 1259 OID 32495)
-- Name: rc_material_change; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_material_change (
    id bigint NOT NULL,
    material_id bigint NOT NULL,
    content_before text NOT NULL,
    content_after text NOT NULL,
    reason character varying(1024),
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    modified_time timestamp without time zone DEFAULT now() NOT NULL,
    create_by bigint NOT NULL,
    create_user character varying(64) NOT NULL,
    reason_type smallint NOT NULL
);


ALTER TABLE "plss-record".rc_material_change OWNER TO suwell;

--
-- TOC entry 5249 (class 0 OID 0)
-- Dependencies: 346
-- Name: TABLE rc_material_change; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_material_change IS '素材变更';


--
-- TOC entry 5250 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.id IS '主键';


--
-- TOC entry 5251 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.material_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.material_id IS '素材id';


--
-- TOC entry 5252 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.content_before; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.content_before IS '变更前内容';


--
-- TOC entry 5253 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.content_after; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.content_after IS '变更后内容';


--
-- TOC entry 5254 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.reason; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.reason IS '变更原因';


--
-- TOC entry 5255 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.create_time IS '创建时间';


--
-- TOC entry 5256 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.modified_time IS '更新时间';


--
-- TOC entry 5257 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.create_by IS '创建者id';


--
-- TOC entry 5258 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.create_user; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.create_user IS '创建者姓名';


--
-- TOC entry 5259 (class 0 OID 0)
-- Dependencies: 346
-- Name: COLUMN rc_material_change.reason_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_change.reason_type IS '变更原因类型：1-用户需求变更；2-设计错误；3-其他';


--
-- TOC entry 347 (class 1259 OID 32504)
-- Name: rc_material_quote; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_material_quote (
    id bigint NOT NULL,
    material_id bigint NOT NULL,
    record_id bigint NOT NULL,
    record_name character varying(256) NOT NULL,
    create_time timestamp without time zone DEFAULT now(),
    modified_time timestamp without time zone DEFAULT now() NOT NULL,
    create_by bigint NOT NULL,
    create_user character varying(64) NOT NULL
);


ALTER TABLE "plss-record".rc_material_quote OWNER TO suwell;

--
-- TOC entry 5260 (class 0 OID 0)
-- Dependencies: 347
-- Name: TABLE rc_material_quote; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_material_quote IS '素材引用';


--
-- TOC entry 5261 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.id IS '主键';


--
-- TOC entry 5262 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.material_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.material_id IS '素材id';


--
-- TOC entry 5263 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.record_id IS '引用素材的文件id';


--
-- TOC entry 5264 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.record_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.record_name IS '引用素材的文件名称';


--
-- TOC entry 5265 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.create_by IS '创建者id';


--
-- TOC entry 5266 (class 0 OID 0)
-- Dependencies: 347
-- Name: COLUMN rc_material_quote.create_user; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_material_quote.create_user IS '姓名';


--
-- TOC entry 390 (class 1259 OID 171965)
-- Name: rc_metadata; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_metadata (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    name_en character varying(64),
    definition character varying(128),
    value_type smallint,
    value_range character varying(256),
    short_name character varying(32),
    search_flag smallint,
    status smallint NOT NULL,
    orderby smallint DEFAULT 0 NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    modified_by bigint NOT NULL,
    opt_edit smallint,
    opt_view smallint,
    value_type_range character varying(256),
    required smallint,
    value_rule character varying(4096),
    pinyin character varying(64),
    mnemonic character varying(64),
    borrow_view smallint DEFAULT 1,
    details_view smallint DEFAULT 1,
    search_result_view smallint DEFAULT 1,
    search_field_way smallint DEFAULT 1,
    remark character varying(128),
    fixed_data smallint DEFAULT 2
);


ALTER TABLE "plss-record".rc_metadata OWNER TO suwell;

--
-- TOC entry 5267 (class 0 OID 0)
-- Dependencies: 390
-- Name: TABLE rc_metadata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_metadata IS '元数据项表';


--
-- TOC entry 5268 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.id IS '唯一标识';


--
-- TOC entry 5269 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.name IS '名称';


--
-- TOC entry 5270 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.name_en; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.name_en IS '英文名';


--
-- TOC entry 5271 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.definition; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.definition IS '定义';


--
-- TOC entry 5272 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.value_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.value_type IS '值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)';


--
-- TOC entry 5273 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.value_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.value_range IS '值域';


--
-- TOC entry 5274 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.short_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.short_name IS '短名';


--
-- TOC entry 5275 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.search_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.search_flag IS '是否可作为检索条件(1:启用, 2:禁用)';


--
-- TOC entry 5276 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5277 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.orderby; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.orderby IS '排序码';


--
-- TOC entry 5278 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.create_time IS '创建时间';


--
-- TOC entry 5279 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.create_by IS '创建人';


--
-- TOC entry 5280 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.modified_time IS '修改时间';


--
-- TOC entry 5281 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.modified_by IS '修改人';


--
-- TOC entry 5282 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.opt_edit; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.opt_edit IS '是否编辑1-可编辑2-不可编辑';


--
-- TOC entry 5283 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.opt_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.opt_view IS '是否展示1-前端显示2-前端不显示';


--
-- TOC entry 5284 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.value_type_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.value_type_range IS '值域正则的配置可选值';


--
-- TOC entry 5285 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.required; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.required IS '元数据值是否必填(1-必填2-非必填)';


--
-- TOC entry 5286 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.value_rule; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.value_rule IS '值定义规则';


--
-- TOC entry 5287 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.pinyin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.pinyin IS '全拼';


--
-- TOC entry 5288 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.mnemonic; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.mnemonic IS '助记词';


--
-- TOC entry 5289 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.borrow_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.borrow_view IS '借阅列表展示 1，展示  2，不展示';


--
-- TOC entry 5290 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.details_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.details_view IS '详情页信息展示1-展示2-不展示';


--
-- TOC entry 5291 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.search_result_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.search_result_view IS '检索结果信息展示1-展示2-不展示';


--
-- TOC entry 5292 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.search_field_way; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.search_field_way IS '搜索字段查询方式: 1-模糊,2-精准,默认模糊';


--
-- TOC entry 5293 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.remark IS '备注:替换comment属性';


--
-- TOC entry 5294 (class 0 OID 0)
-- Dependencies: 390
-- Name: COLUMN rc_metadata.fixed_data; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata.fixed_data IS '是否内置固定数据1-内置2-非内置';


--
-- TOC entry 391 (class 1259 OID 171978)
-- Name: rc_metadata_category; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_metadata_category (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    status smallint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    modified_by bigint NOT NULL
);


ALTER TABLE "plss-record".rc_metadata_category OWNER TO suwell;

--
-- TOC entry 5295 (class 0 OID 0)
-- Dependencies: 391
-- Name: TABLE rc_metadata_category; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_metadata_category IS '元数据分类表';


--
-- TOC entry 5296 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.id IS '唯一标识';


--
-- TOC entry 5297 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.name IS '名称';


--
-- TOC entry 5298 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5299 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.create_time IS '创建时间';


--
-- TOC entry 5300 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.create_by IS '创建人';


--
-- TOC entry 5301 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.modified_time IS '修改时间';


--
-- TOC entry 5302 (class 0 OID 0)
-- Dependencies: 391
-- Name: COLUMN rc_metadata_category.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category.modified_by IS '修改人';


--
-- TOC entry 392 (class 1259 OID 171984)
-- Name: rc_metadata_category_metadata; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_metadata_category_metadata (
    category_id bigint NOT NULL,
    md_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_metadata_category_metadata OWNER TO suwell;

--
-- TOC entry 5303 (class 0 OID 0)
-- Dependencies: 392
-- Name: TABLE rc_metadata_category_metadata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_metadata_category_metadata IS '元数据分类-元数据关联表';


--
-- TOC entry 5304 (class 0 OID 0)
-- Dependencies: 392
-- Name: COLUMN rc_metadata_category_metadata.category_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category_metadata.category_id IS '元数据分类id';


--
-- TOC entry 5305 (class 0 OID 0)
-- Dependencies: 392
-- Name: COLUMN rc_metadata_category_metadata.md_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_category_metadata.md_id IS '元数据id';


--
-- TOC entry 254 (class 1259 OID 26234)
-- Name: rc_metadata_value; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_metadata_value (
    id bigint NOT NULL,
    md_id bigint NOT NULL,
    md_value character varying(2048),
    create_time timestamp(6) without time zone NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    record_id bigint NOT NULL,
    md_name character varying(64),
    data_type smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-record".rc_metadata_value OWNER TO suwell;

--
-- TOC entry 5306 (class 0 OID 0)
-- Dependencies: 254
-- Name: TABLE rc_metadata_value; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_metadata_value IS '元数据键值对表';


--
-- TOC entry 5307 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.id IS '唯一标识';


--
-- TOC entry 5308 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.md_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.md_id IS '元数据项id';


--
-- TOC entry 5309 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.md_value; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.md_value IS '元数据值';


--
-- TOC entry 5310 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.create_time IS '创建时间';


--
-- TOC entry 5311 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.modified_time IS '修改时间';


--
-- TOC entry 5312 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.record_id IS '文件id';


--
-- TOC entry 5313 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN rc_metadata_value.md_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_metadata_value.md_name IS '元数据项名称';


--
-- TOC entry 389 (class 1259 OID 171946)
-- Name: rc_node; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_node (
    id bigint NOT NULL,
    name character varying(256) NOT NULL,
    node_type smallint NOT NULL,
    defined_config_json character varying(4096) NOT NULL,
    input_scope character varying(256),
    output_scope character varying(256),
    status smallint NOT NULL,
    remark character varying(256),
    create_time timestamp(6) without time zone NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    order_num smallint NOT NULL
);


ALTER TABLE "plss-record".rc_node OWNER TO suwell;

--
-- TOC entry 5314 (class 0 OID 0)
-- Dependencies: 389
-- Name: TABLE rc_node; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_node IS '节点表';


--
-- TOC entry 5315 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.name IS '节点名称';


--
-- TOC entry 5316 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.node_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.node_type IS '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库';


--
-- TOC entry 5317 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.defined_config_json; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.defined_config_json IS '配置详情定义';


--
-- TOC entry 5318 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.input_scope; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.input_scope IS '输入参数';


--
-- TOC entry 5319 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.output_scope; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.output_scope IS '输出参数';


--
-- TOC entry 5320 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.status IS '状态 1-有效  2-无效';


--
-- TOC entry 5321 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.remark IS '描述';


--
-- TOC entry 5322 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.create_time IS '创建时间';


--
-- TOC entry 5323 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.modified_time IS '修改时间';


--
-- TOC entry 5324 (class 0 OID 0)
-- Dependencies: 389
-- Name: COLUMN rc_node.order_num; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_node.order_num IS '序号';


--
-- TOC entry 242 (class 1259 OID 25362)
-- Name: rc_permission; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_permission (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    pvalue integer NOT NULL,
    memo character varying(64),
    project_use integer NOT NULL
);


ALTER TABLE "plss-record".rc_permission OWNER TO suwell;

--
-- TOC entry 5325 (class 0 OID 0)
-- Dependencies: 242
-- Name: TABLE rc_permission; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_permission IS '权限字典表';


--
-- TOC entry 5326 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN rc_permission.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission.id IS '唯一标识';


--
-- TOC entry 5327 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN rc_permission.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission.name IS '名称';


--
-- TOC entry 5328 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN rc_permission.pvalue; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission.pvalue IS '权限值(2的n次方 )';


--
-- TOC entry 5329 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN rc_permission.memo; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission.memo IS '备注';


--
-- TOC entry 5330 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN rc_permission.project_use; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission.project_use IS '项目中是否使用';


--
-- TOC entry 249 (class 1259 OID 25567)
-- Name: rc_permission_group; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_permission_group (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    pvalue integer NOT NULL,
    memo character varying(64),
    preset integer NOT NULL,
    del_flag integer NOT NULL
);


ALTER TABLE "plss-record".rc_permission_group OWNER TO suwell;

--
-- TOC entry 5331 (class 0 OID 0)
-- Dependencies: 249
-- Name: TABLE rc_permission_group; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_permission_group IS '权限组表';


--
-- TOC entry 5332 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.id IS '唯一标识';


--
-- TOC entry 5333 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.name IS '名称';


--
-- TOC entry 5334 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.pvalue; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.pvalue IS '权限值组合';


--
-- TOC entry 5335 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.memo; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.memo IS '备注';


--
-- TOC entry 5336 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.preset; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.preset IS '是否是内置权限组，内置权限组不允许修改';


--
-- TOC entry 5337 (class 0 OID 0)
-- Dependencies: 249
-- Name: COLUMN rc_permission_group.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_permission_group.del_flag IS '是否删除';


--
-- TOC entry 327 (class 1259 OID 31419)
-- Name: rc_plan; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_plan (
    id bigint NOT NULL,
    name character varying(256) NOT NULL,
    record_type_id bigint NOT NULL,
    remark character varying(256),
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    del_flag smallint NOT NULL,
    last_flag smallint NOT NULL,
    tenant_id bigint NOT NULL,
    common_flag smallint DEFAULT 1 NOT NULL,
    show_front smallint,
    create_by bigint
);


ALTER TABLE "plss-record".rc_plan OWNER TO suwell;

--
-- TOC entry 5338 (class 0 OID 0)
-- Dependencies: 327
-- Name: TABLE rc_plan; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_plan IS '入库方案表';


--
-- TOC entry 5339 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.id IS '主键';


--
-- TOC entry 5340 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.name IS '入库方案名称';


--
-- TOC entry 5341 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.record_type_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.record_type_id IS '文件类型id';


--
-- TOC entry 5342 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.remark IS '描述';


--
-- TOC entry 5343 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.status IS '状态 1-有效 2-无效';


--
-- TOC entry 5344 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.create_time IS '创建时间';


--
-- TOC entry 5345 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.modified_time IS '修改时间';


--
-- TOC entry 5346 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.del_flag IS '标记删除1-正常2-删除';


--
-- TOC entry 5347 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.last_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.last_flag IS '最新方案标记1-最新方案2-历史方案';


--
-- TOC entry 5348 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.tenant_id IS '租户id';


--
-- TOC entry 5349 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.common_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.common_flag IS '通用配置方案1-普通;2-通用配置方案';


--
-- TOC entry 5350 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.show_front; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.show_front IS '前台是否可见：1可见，2不可见';


--
-- TOC entry 5351 (class 0 OID 0)
-- Dependencies: 327
-- Name: COLUMN rc_plan.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan.create_by IS '创建人id';


--
-- TOC entry 380 (class 1259 OID 102857)
-- Name: rc_plan_front_range; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_plan_front_range (
    id bigint NOT NULL,
    plan_id bigint NOT NULL,
    range_type smallint NOT NULL,
    range_id bigint NOT NULL,
    range_name character varying(64)
);


ALTER TABLE "plss-record".rc_plan_front_range OWNER TO suwell;

--
-- TOC entry 5352 (class 0 OID 0)
-- Dependencies: 380
-- Name: TABLE rc_plan_front_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_plan_front_range IS '入库方案前台可见范围';


--
-- TOC entry 5353 (class 0 OID 0)
-- Dependencies: 380
-- Name: COLUMN rc_plan_front_range.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_front_range.id IS '主键';


--
-- TOC entry 5354 (class 0 OID 0)
-- Dependencies: 380
-- Name: COLUMN rc_plan_front_range.plan_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_front_range.plan_id IS '方案id';


--
-- TOC entry 5355 (class 0 OID 0)
-- Dependencies: 380
-- Name: COLUMN rc_plan_front_range.range_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_front_range.range_type IS '1:人员，2:角色，3:组织机构';


--
-- TOC entry 5356 (class 0 OID 0)
-- Dependencies: 380
-- Name: COLUMN rc_plan_front_range.range_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_front_range.range_id IS '类型下对应的具体id';


--
-- TOC entry 5357 (class 0 OID 0)
-- Dependencies: 380
-- Name: COLUMN rc_plan_front_range.range_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_front_range.range_name IS '范围id对应业务的名称';


--
-- TOC entry 328 (class 1259 OID 31426)
-- Name: rc_plan_node; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_plan_node (
    plan_id bigint NOT NULL,
    node_id bigint NOT NULL,
    defined_config_json character varying(4096) NOT NULL,
    order_num smallint NOT NULL,
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    node_type smallint
);


ALTER TABLE "plss-record".rc_plan_node OWNER TO suwell;

--
-- TOC entry 5358 (class 0 OID 0)
-- Dependencies: 328
-- Name: TABLE rc_plan_node; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_plan_node IS '入库方案节点关联表';


--
-- TOC entry 5359 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.plan_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.plan_id IS '入库方案id';


--
-- TOC entry 5360 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.node_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.node_id IS '节点id';


--
-- TOC entry 5361 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.defined_config_json; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.defined_config_json IS '配置详情按钮json';


--
-- TOC entry 5362 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.order_num; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.order_num IS '序号';


--
-- TOC entry 5363 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.status IS '状态 1-开启  2-关闭';


--
-- TOC entry 5364 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.create_time IS '创建时间';


--
-- TOC entry 5365 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.modified_time IS '修改时间';


--
-- TOC entry 5366 (class 0 OID 0)
-- Dependencies: 328
-- Name: COLUMN rc_plan_node.node_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_plan_node.node_type IS '节点类型';


--
-- TOC entry 377 (class 1259 OID 67506)
-- Name: rc_reader_preview; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_reader_preview (
    id bigint NOT NULL,
    fname character varying NOT NULL,
    fdata character varying NOT NULL,
    fsource smallint DEFAULT 1 NOT NULL,
    fperm character varying,
    fmark character varying,
    expire_date timestamp without time zone,
    create_time timestamp without time zone NOT NULL,
    create_by character varying NOT NULL
);


ALTER TABLE "plss-record".rc_reader_preview OWNER TO suwell;

--
-- TOC entry 5367 (class 0 OID 0)
-- Dependencies: 377
-- Name: TABLE rc_reader_preview; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_reader_preview IS '轻阅读预览文件表';


--
-- TOC entry 5368 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.fname; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.fname IS '预览文件名称';


--
-- TOC entry 5369 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.fdata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.fdata IS '文件url、docId';


--
-- TOC entry 5370 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.fsource; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.fsource IS '文件来源（1外部url 2公文库docId ）';


--
-- TOC entry 5371 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.fperm; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.fperm IS '权限';


--
-- TOC entry 5372 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.fmark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.fmark IS '水印';


--
-- TOC entry 5373 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.expire_date; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.expire_date IS '过期时间';


--
-- TOC entry 5374 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.create_time IS '创建时间';


--
-- TOC entry 5375 (class 0 OID 0)
-- Dependencies: 377
-- Name: COLUMN rc_reader_preview.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reader_preview.create_by IS '创建人、开放平台应用id';


--
-- TOC entry 245 (class 1259 OID 25407)
-- Name: rc_record; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record (
    id bigint NOT NULL,
    recordtype_id bigint,
    name character varying(256) NOT NULL,
    title character varying(512),
    digest character varying(2048),
    status smallint NOT NULL,
    origin smallint DEFAULT 0 NOT NULL,
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    process_status smallint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    create_by_name character varying(255) DEFAULT ''::character varying NOT NULL,
    modified_by_name character varying(255) DEFAULT ''::character varying NOT NULL,
    record_status smallint DEFAULT 100 NOT NULL,
    put_lib_time timestamp without time zone,
    visit_type smallint DEFAULT 1,
    real_origin integer,
    store_process integer DEFAULT 1
);


ALTER TABLE "plss-record".rc_record OWNER TO suwell;

--
-- TOC entry 5376 (class 0 OID 0)
-- Dependencies: 245
-- Name: TABLE rc_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record IS '文件表(文档合集)';


--
-- TOC entry 5377 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.id IS '唯一标识';


--
-- TOC entry 5378 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.recordtype_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.recordtype_id IS '文件类型id';


--
-- TOC entry 5379 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.name IS '文件名称';


--
-- TOC entry 5380 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.title; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.title IS '标题';


--
-- TOC entry 5381 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.digest; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.digest IS '摘要';


--
-- TOC entry 5382 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.status IS '启用状态(1:启用, 2:禁用)';


--
-- TOC entry 5383 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.origin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.origin IS '来源:接入方标识';


--
-- TOC entry 5384 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.create_time IS '创建时间';


--
-- TOC entry 5385 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.create_by IS '创建人';


--
-- TOC entry 5386 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.modified_time IS '修改时间';


--
-- TOC entry 5387 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.modified_by IS '修改人';


--
-- TOC entry 5388 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.process_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.process_status IS '文件加工状态:【待处理】101补充元数据,102指定归档目录;【处理中】201-转换中,202-OC识别中,203-AI处理中,204-系统检测中;【已入库】501-转换异常;502-OCR异常';


--
-- TOC entry 5389 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.tenant_id IS '租户id';


--
-- TOC entry 5390 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.create_by_name IS '创建人姓名';


--
-- TOC entry 5391 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.modified_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.modified_by_name IS '修改人姓名';


--
-- TOC entry 5392 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.record_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.record_status IS '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档';


--
-- TOC entry 5393 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.put_lib_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.put_lib_time IS '入库时间';


--
-- TOC entry 5394 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.visit_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.visit_type IS '1:继承，2：私有';


--
-- TOC entry 5395 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.real_origin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.real_origin IS '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传';


--
-- TOC entry 5396 (class 0 OID 0)
-- Dependencies: 245
-- Name: COLUMN rc_record.store_process; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record.store_process IS '1-没走入库流程  2-走了入库流程';


--
-- TOC entry 349 (class 1259 OID 32678)
-- Name: rc_record_borrow; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record_borrow (
    id bigint NOT NULL,
    borrow_id bigint NOT NULL,
    record_id bigint NOT NULL,
    record_name character varying(256),
    record_tenant_id bigint NOT NULL,
    borrow_by bigint NOT NULL,
    borrow_by_name character varying(64) DEFAULT ''::character varying NOT NULL,
    borrow_date timestamp(6) without time zone DEFAULT now() NOT NULL,
    org_id bigint NOT NULL,
    org_name character varying(64) DEFAULT ''::character varying NOT NULL,
    borrow_days integer DEFAULT 0 NOT NULL,
    borrow_purpose smallint DEFAULT 0 NOT NULL,
    borrow_for character varying(512) DEFAULT ''::character varying,
    usage character varying(11) DEFAULT ''::character varying NOT NULL,
    audit_by bigint,
    audit_by_name character varying(64) DEFAULT ''::character varying,
    audit_date timestamp(6) without time zone,
    status smallint DEFAULT 1 NOT NULL,
    reject_reason character varying(512) DEFAULT ''::character varying,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    invalid_status smallint DEFAULT 1 NOT NULL,
    withdraw_date timestamp(6) without time zone
);


ALTER TABLE "plss-record".rc_record_borrow OWNER TO suwell;

--
-- TOC entry 5397 (class 0 OID 0)
-- Dependencies: 349
-- Name: TABLE rc_record_borrow; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record_borrow IS '文件借阅表';


--
-- TOC entry 5398 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_id IS '借阅业务id';


--
-- TOC entry 5399 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.record_id IS '借阅文件id';


--
-- TOC entry 5400 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.record_tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.record_tenant_id IS '借阅文件所属租户id';


--
-- TOC entry 5401 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_by IS '借阅用户id';


--
-- TOC entry 5402 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_by_name IS '借阅用户名称';


--
-- TOC entry 5403 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_date; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_date IS '借阅日期';


--
-- TOC entry 5404 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.org_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.org_id IS '借阅用户所属单位id';


--
-- TOC entry 5405 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.org_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.org_name IS '借阅用户所属单位名称';


--
-- TOC entry 5406 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_days; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_days IS '借阅时间';


--
-- TOC entry 5407 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_purpose; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_purpose IS '借阅目的（1讨论、2学习、3科研、4其他）';


--
-- TOC entry 5408 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.borrow_for; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.borrow_for IS '借阅原因';


--
-- TOC entry 5409 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.usage; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.usage IS '利用方式（多选项：1查阅、2打印）';


--
-- TOC entry 5410 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.audit_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.audit_by IS '审核用户id';


--
-- TOC entry 5411 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.audit_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.audit_by_name IS '审核用户名称';


--
-- TOC entry 5412 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.audit_date; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.audit_date IS '审核日期';


--
-- TOC entry 5413 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.status IS '状态（1待审批、2已借出、3已归还、4已拒绝）';


--
-- TOC entry 5414 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.reject_reason; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.reject_reason IS '驳回原因';


--
-- TOC entry 5415 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.del_flag IS '删除状态';


--
-- TOC entry 5416 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.create_by IS '创建人';


--
-- TOC entry 5417 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.create_time IS '创建日期';


--
-- TOC entry 5418 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.update_by IS '修改人';


--
-- TOC entry 5419 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.update_time IS '修改日期';


--
-- TOC entry 5420 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.remark IS '备注';


--
-- TOC entry 5421 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.invalid_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.invalid_status IS '效用状态(1有效2无效)';


--
-- TOC entry 5422 (class 0 OID 0)
-- Dependencies: 349
-- Name: COLUMN rc_record_borrow.withdraw_date; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_borrow.withdraw_date IS '收回时间';


--
-- TOC entry 315 (class 1259 OID 28640)
-- Name: rc_record_process_detail; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record_process_detail (
    id bigint NOT NULL,
    record_id bigint NOT NULL,
    record_name character varying(256) NOT NULL,
    begin_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone,
    record_process_type smallint NOT NULL,
    create_by bigint NOT NULL,
    create_by_name character varying(64) NOT NULL,
    record_process_status smallint NOT NULL,
    doc_name character varying(256),
    doc_id bigint
);


ALTER TABLE "plss-record".rc_record_process_detail OWNER TO suwell;

--
-- TOC entry 5423 (class 0 OID 0)
-- Dependencies: 315
-- Name: TABLE rc_record_process_detail; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record_process_detail IS '文件处理详情';


--
-- TOC entry 5424 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.id IS '唯一标识';


--
-- TOC entry 5425 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.record_id IS '文件id';


--
-- TOC entry 5426 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.record_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.record_name IS '文件名称';


--
-- TOC entry 5427 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.begin_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.begin_time IS '开始时间';


--
-- TOC entry 5428 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.end_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.end_time IS '结束时间';


--
-- TOC entry 5429 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.record_process_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.record_process_type IS '文件处理类型';


--
-- TOC entry 5430 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.create_by IS '创建人';


--
-- TOC entry 5431 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.create_by_name IS '创建人名称';


--
-- TOC entry 5432 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.record_process_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.record_process_status IS '文件处理类型状态:1-启用2-禁用';


--
-- TOC entry 5433 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.doc_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.doc_name IS '文档名称';


--
-- TOC entry 5434 (class 0 OID 0)
-- Dependencies: 315
-- Name: COLUMN rc_record_process_detail.doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_process_detail.doc_id IS '文档id';


--
-- TOC entry 247 (class 1259 OID 25426)
-- Name: rc_record_rel; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record_rel (
    refer_id bigint NOT NULL,
    record_id bigint NOT NULL,
    ctype smallint NOT NULL,
    distance integer NOT NULL,
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    refer_rel_name character varying(64),
    record_rel_name character varying(64)
);


ALTER TABLE "plss-record".rc_record_rel OWNER TO suwell;

--
-- TOC entry 5435 (class 0 OID 0)
-- Dependencies: 247
-- Name: TABLE rc_record_rel; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record_rel IS '文件-文件关联表';


--
-- TOC entry 5436 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.refer_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.refer_id IS '引用文件id';


--
-- TOC entry 5437 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.record_id IS '文件id';


--
-- TOC entry 5438 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.ctype IS '引用类型(1:依托,  2:沿革, 3:参考)';


--
-- TOC entry 5439 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.distance; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.distance IS '层级步数';


--
-- TOC entry 5440 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.create_time IS '创建时间';


--
-- TOC entry 5441 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.create_by IS '创建人';


--
-- TOC entry 5442 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.modified_time IS '修改时间';


--
-- TOC entry 5443 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.modified_by IS '修改人';


--
-- TOC entry 5444 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.refer_rel_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.refer_rel_name IS '上级关联名称';


--
-- TOC entry 5445 (class 0 OID 0)
-- Dependencies: 247
-- Name: COLUMN rc_record_rel.record_rel_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_rel.record_rel_name IS '下级关联名称';


--
-- TOC entry 301 (class 1259 OID 27178)
-- Name: rc_record_type; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record_type (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    status smallint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    modified_by bigint NOT NULL,
    remark character varying(255) DEFAULT ''::character varying,
    front_view smallint DEFAULT 2,
    group_type_id bigint
);


ALTER TABLE "plss-record".rc_record_type OWNER TO suwell;

--
-- TOC entry 5446 (class 0 OID 0)
-- Dependencies: 301
-- Name: TABLE rc_record_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record_type IS '文件类型表';


--
-- TOC entry 5447 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.id IS '唯一标识';


--
-- TOC entry 5448 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.name IS '名称';


--
-- TOC entry 5449 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5450 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.create_time IS '创建时间';


--
-- TOC entry 5451 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.create_by IS '创建人';


--
-- TOC entry 5452 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.modified_time IS '修改时间';


--
-- TOC entry 5453 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.modified_by IS '修改人';


--
-- TOC entry 5454 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.remark IS '备注';


--
-- TOC entry 5455 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.front_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.front_view IS '前台独立展示1-展示-2关闭';


--
-- TOC entry 5456 (class 0 OID 0)
-- Dependencies: 301
-- Name: COLUMN rc_record_type.group_type_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type.group_type_id IS '文件类型分组id(关联分类标签的id)';


--
-- TOC entry 302 (class 1259 OID 27183)
-- Name: rc_record_type_metadata; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_record_type_metadata (
    recordtype_id bigint NOT NULL,
    md_id bigint NOT NULL,
    value_type smallint,
    value_range character varying(256),
    search_flag smallint,
    mdcategory_id bigint,
    orderby integer DEFAULT 0,
    required smallint DEFAULT 2 NOT NULL,
    opt_edit smallint DEFAULT 1,
    opt_view smallint DEFAULT 1,
    value_type_range character varying(256),
    value_rule character varying(4096),
    md_name character varying(64),
    pinyin character varying(64),
    mnemonic character varying(64),
    borrow_view smallint DEFAULT 1 NOT NULL,
    details_view smallint DEFAULT 1,
    search_result_view smallint DEFAULT 1,
    search_field_way smallint DEFAULT 1,
    remark character varying(128)
);


ALTER TABLE "plss-record".rc_record_type_metadata OWNER TO suwell;

--
-- TOC entry 5457 (class 0 OID 0)
-- Dependencies: 302
-- Name: TABLE rc_record_type_metadata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_record_type_metadata IS '文件类型-元数据表';


--
-- TOC entry 5458 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.recordtype_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.recordtype_id IS '文件类型id';


--
-- TOC entry 5459 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.md_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.md_id IS '元数据id';


--
-- TOC entry 5460 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.value_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.value_type IS '值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)';


--
-- TOC entry 5461 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.value_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.value_range IS '值域';


--
-- TOC entry 5462 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.search_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.search_flag IS '是否可作为检索条件(1:启用, 2:禁用)';


--
-- TOC entry 5463 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.mdcategory_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.mdcategory_id IS '元数据分类id';


--
-- TOC entry 5464 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.orderby; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.orderby IS '排序码';


--
-- TOC entry 5465 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.required; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.required IS '元数据值是否必填(1-必填2-非必填)';


--
-- TOC entry 5466 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.opt_edit; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.opt_edit IS '是否编辑1-可编辑2-不可编辑';


--
-- TOC entry 5467 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.opt_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.opt_view IS '是否展示1-编辑页显示2-编辑页不显示';


--
-- TOC entry 5468 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.value_type_range; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.value_type_range IS '值域正则的配置可选值';


--
-- TOC entry 5469 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.value_rule; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.value_rule IS '值域规则定义json字符串';


--
-- TOC entry 5470 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.md_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.md_name IS '元数据项名称';


--
-- TOC entry 5471 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.pinyin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.pinyin IS '全拼';


--
-- TOC entry 5472 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.mnemonic; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.mnemonic IS '助记词';


--
-- TOC entry 5473 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.borrow_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.borrow_view IS '借阅列表展示 1，展示  2，不展示';


--
-- TOC entry 5474 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.details_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.details_view IS '详情页信息展示1-展示2-不展示';


--
-- TOC entry 5475 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.search_result_view; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.search_result_view IS '检索结果信息展示1-展示2-不展示';


--
-- TOC entry 5476 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.search_field_way; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.search_field_way IS '搜索字段查询方式: 1-模糊,2-精准,默认模糊';


--
-- TOC entry 5477 (class 0 OID 0)
-- Dependencies: 302
-- Name: COLUMN rc_record_type_metadata.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_record_type_metadata.remark IS '备注';


--
-- TOC entry 394 (class 1259 OID 187930)
-- Name: rc_reference_record; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_reference_record (
    id bigint NOT NULL,
    record_id bigint,
    reference_record_id bigint,
    reference_record_name character varying(64),
    status smallint DEFAULT 1,
    create_time timestamp(6) without time zone DEFAULT now(),
    modified_time timestamp(6) without time zone DEFAULT now(),
    create_by bigint,
    create_user character varying(64),
    repo_type smallint
);


ALTER TABLE "plss-record".rc_reference_record OWNER TO suwell;

--
-- TOC entry 5478 (class 0 OID 0)
-- Dependencies: 394
-- Name: TABLE rc_reference_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_reference_record IS '参考表';


--
-- TOC entry 5479 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.id IS '主键';


--
-- TOC entry 5480 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.record_id IS '文档id';


--
-- TOC entry 5481 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.reference_record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.reference_record_id IS '参考文档id';


--
-- TOC entry 5482 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.reference_record_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.reference_record_name IS '参考文档名称';


--
-- TOC entry 5483 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.status IS '状态：1 正常；2 删除';


--
-- TOC entry 5484 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.create_time IS '创建时间';


--
-- TOC entry 5485 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.modified_time IS '更新时间';


--
-- TOC entry 5486 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.create_by IS '创建者id';


--
-- TOC entry 5487 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.create_user; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.create_user IS '创建者姓名';


--
-- TOC entry 5488 (class 0 OID 0)
-- Dependencies: 394
-- Name: COLUMN rc_reference_record.repo_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_reference_record.repo_type IS '库类型 1.内置库 2.模板库 3.范文样例 4.个人库';


--
-- TOC entry 323 (class 1259 OID 31309)
-- Name: rc_repo_metadata; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_repo_metadata (
    id bigint NOT NULL,
    repo_id bigint NOT NULL,
    type smallint NOT NULL,
    retrieve_type smallint,
    json_value text NOT NULL,
    create_by bigint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    modify_by bigint NOT NULL,
    modify_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_repo_metadata OWNER TO suwell;

--
-- TOC entry 5489 (class 0 OID 0)
-- Dependencies: 323
-- Name: TABLE rc_repo_metadata; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_repo_metadata IS '库-元数据项值表';


--
-- TOC entry 5490 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.id IS '唯一标识';


--
-- TOC entry 5491 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.repo_id IS '库id';


--
-- TOC entry 5492 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.type IS '类型,1:文档类型,2:检索类型';


--
-- TOC entry 5493 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.retrieve_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.retrieve_type IS '检索类型,1:元数据,2:分类标签,3:组织架构,4:目录';


--
-- TOC entry 5494 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.json_value; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.json_value IS '值域';


--
-- TOC entry 5495 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.create_by IS '创建人id';


--
-- TOC entry 5496 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.create_time IS '创建时间';


--
-- TOC entry 5497 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.modify_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.modify_by IS '修改人id';


--
-- TOC entry 5498 (class 0 OID 0)
-- Dependencies: 323
-- Name: COLUMN rc_repo_metadata.modify_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_metadata.modify_time IS '修改时间';


--
-- TOC entry 371 (class 1259 OID 52443)
-- Name: rc_repo_record; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_repo_record (
    repo_id bigint NOT NULL,
    record_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_repo_record OWNER TO suwell;

--
-- TOC entry 5499 (class 0 OID 0)
-- Dependencies: 371
-- Name: TABLE rc_repo_record; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_repo_record IS '库-文件关联表';


--
-- TOC entry 5500 (class 0 OID 0)
-- Dependencies: 371
-- Name: COLUMN rc_repo_record.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_record.repo_id IS '库id';


--
-- TOC entry 5501 (class 0 OID 0)
-- Dependencies: 371
-- Name: COLUMN rc_repo_record.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repo_record.record_id IS '文件id';


--
-- TOC entry 244 (class 1259 OID 25400)
-- Name: rc_repository; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_repository (
    id bigint NOT NULL,
    name character varying(256) NOT NULL,
    ctype smallint,
    share_type smallint,
    status smallint NOT NULL,
    image_url character varying(1024),
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    owner_id bigint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    show_front integer
);


ALTER TABLE "plss-record".rc_repository OWNER TO suwell;

--
-- TOC entry 5502 (class 0 OID 0)
-- Dependencies: 244
-- Name: TABLE rc_repository; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_repository IS '库';


--
-- TOC entry 5503 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.id IS '唯一标识';


--
-- TOC entry 5504 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.name IS '库名称';


--
-- TOC entry 5505 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.ctype IS '库类型(1:内部库, 2:公共库, 3:个人库)';


--
-- TOC entry 5506 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.share_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.share_type IS '共享类型(1:无条件共享, 2:有条件共享, 3:不共享)';


--
-- TOC entry 5507 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5508 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.image_url; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.image_url IS '图标url';


--
-- TOC entry 5509 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.create_time IS '创建时间';


--
-- TOC entry 5510 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.create_by IS '创建人';


--
-- TOC entry 5511 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.modified_time IS '修改时间';


--
-- TOC entry 5512 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.modified_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.modified_by IS '修改人';


--
-- TOC entry 5513 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.owner_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.owner_id IS '所有者id';


--
-- TOC entry 5514 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.tenant_id IS '租户id';


--
-- TOC entry 5515 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN rc_repository.show_front; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_repository.show_front IS '是否前台展示';


--
-- TOC entry 329 (class 1259 OID 31436)
-- Name: rc_resource_manager; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_resource_manager (
    id bigint NOT NULL,
    resource_id bigint NOT NULL,
    rtype smallint NOT NULL,
    visitor_id bigint NOT NULL,
    vtype smallint NOT NULL,
    manager_type smallint NOT NULL,
    create_by bigint NOT NULL,
    create_by_name character varying(50) NOT NULL,
    create_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_resource_manager OWNER TO suwell;

--
-- TOC entry 5516 (class 0 OID 0)
-- Dependencies: 329
-- Name: TABLE rc_resource_manager; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_resource_manager IS '资源管理员表';


--
-- TOC entry 5517 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.id IS 'id';


--
-- TOC entry 5518 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.resource_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.resource_id IS '资源id(可以为库、目录、文件)';


--
-- TOC entry 5519 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.rtype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.rtype IS '资源类型(1:库, 2:目录, 3:文件)';


--
-- TOC entry 5520 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.visitor_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.visitor_id IS '管理员id（可以为单位、人员、角色）';


--
-- TOC entry 5521 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.vtype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.vtype IS '访问者类型(1:组织机构, 2:用户, 3:角色)';


--
-- TOC entry 5522 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.manager_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.manager_type IS '管理员类型(1:owner, 2:协同管理员)';


--
-- TOC entry 5523 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.create_by IS '创建人id';


--
-- TOC entry 5524 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.create_by_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.create_by_name IS '创建人姓名';


--
-- TOC entry 5525 (class 0 OID 0)
-- Dependencies: 329
-- Name: COLUMN rc_resource_manager.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_manager.create_time IS '创建时间';


--
-- TOC entry 314 (class 1259 OID 28626)
-- Name: rc_resource_permission; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_resource_permission (
    id bigint NOT NULL,
    visitor_id bigint NOT NULL,
    vtype smallint NOT NULL,
    resource_id bigint NOT NULL,
    rtype smallint NOT NULL,
    pvalue integer NOT NULL,
    permission_group_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_resource_permission OWNER TO suwell;

--
-- TOC entry 5526 (class 0 OID 0)
-- Dependencies: 314
-- Name: TABLE rc_resource_permission; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_resource_permission IS '库-目录-文件权限表';


--
-- TOC entry 5527 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.id IS '唯一标识';


--
-- TOC entry 5528 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.visitor_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.visitor_id IS '访问者id';


--
-- TOC entry 5529 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.vtype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.vtype IS '访问者类型(1:组织机构, 2:用户, 3:角色)';


--
-- TOC entry 5530 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.resource_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.resource_id IS '资源id (库-目录-文件)';


--
-- TOC entry 5531 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.rtype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.rtype IS '资源类型(1:库, 2:目录, 3:文件)';


--
-- TOC entry 5532 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.pvalue; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.pvalue IS '权限值';


--
-- TOC entry 5533 (class 0 OID 0)
-- Dependencies: 314
-- Name: COLUMN rc_resource_permission.permission_group_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_permission.permission_group_id IS '权限组id';


--
-- TOC entry 310 (class 1259 OID 28409)
-- Name: rc_resource_tenant_visible; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_resource_tenant_visible (
    resource_id bigint NOT NULL,
    tenant_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_resource_tenant_visible OWNER TO suwell;

--
-- TOC entry 5534 (class 0 OID 0)
-- Dependencies: 310
-- Name: TABLE rc_resource_tenant_visible; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_resource_tenant_visible IS '租户对资源的可见态';


--
-- TOC entry 5535 (class 0 OID 0)
-- Dependencies: 310
-- Name: COLUMN rc_resource_tenant_visible.resource_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_tenant_visible.resource_id IS '资源id（库id，目录，文件id)';


--
-- TOC entry 5536 (class 0 OID 0)
-- Dependencies: 310
-- Name: COLUMN rc_resource_tenant_visible.tenant_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_resource_tenant_visible.tenant_id IS '租户id';


--
-- TOC entry 351 (class 1259 OID 32780)
-- Name: rc_sample_template_catalogue; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_sample_template_catalogue (
    catalogue_id bigint NOT NULL,
    catalogue_pr_id bigint NOT NULL,
    catalogue_name character varying(500) NOT NULL,
    business_type smallint NOT NULL,
    ancestors character varying(500),
    sort integer DEFAULT 1 NOT NULL,
    create_by bigint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    update_by bigint NOT NULL,
    update_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_sample_template_catalogue OWNER TO suwell;

--
-- TOC entry 5537 (class 0 OID 0)
-- Dependencies: 351
-- Name: TABLE rc_sample_template_catalogue; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_sample_template_catalogue IS '范文的目录层级';


--
-- TOC entry 5538 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.catalogue_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.catalogue_id IS '目录id';


--
-- TOC entry 5539 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.catalogue_pr_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.catalogue_pr_id IS '目录的父id';


--
-- TOC entry 5540 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.catalogue_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.catalogue_name IS '目录名称';


--
-- TOC entry 5541 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.business_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.business_type IS '业务类型:1-范文 2-模板';


--
-- TOC entry 5542 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.ancestors; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.ancestors IS '层级列表';


--
-- TOC entry 5543 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.create_by IS '创建人';


--
-- TOC entry 5544 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.create_time IS '创建时间';


--
-- TOC entry 5545 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.update_by IS '更新人';


--
-- TOC entry 5546 (class 0 OID 0)
-- Dependencies: 351
-- Name: COLUMN rc_sample_template_catalogue.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_catalogue.update_time IS '更新时间';


--
-- TOC entry 369 (class 1259 OID 33405)
-- Name: rc_sample_template_file; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_sample_template_file (
    id bigint NOT NULL,
    catalogue_id bigint NOT NULL,
    file_name character varying(500) NOT NULL,
    file_path character varying(500),
    file_size bigint NOT NULL,
    file_bucket_name character varying(500),
    cover_file_path character varying(500),
    cover_file_bucket_name character varying(500),
    ofd_name character varying(500),
    ofd_file_path character varying(500),
    ofd_file_bucket_name character varying(500),
    state smallint,
    create_by bigint NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    update_by bigint NOT NULL,
    update_time timestamp(6) without time zone NOT NULL,
    original_file_id bigint,
    ofd_file_id bigint,
    cover_file_id bigint
);


ALTER TABLE "plss-record".rc_sample_template_file OWNER TO suwell;

--
-- TOC entry 5547 (class 0 OID 0)
-- Dependencies: 369
-- Name: TABLE rc_sample_template_file; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_sample_template_file IS '范文/模板文件';


--
-- TOC entry 5548 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.id IS '主键Id';


--
-- TOC entry 5549 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.catalogue_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.catalogue_id IS '目录id';


--
-- TOC entry 5550 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.file_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.file_name IS '文件名';


--
-- TOC entry 5551 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.file_path; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.file_path IS '文件的存储名';


--
-- TOC entry 5552 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.file_size; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.file_size IS '文件大小,单位bytes';


--
-- TOC entry 5553 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.file_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.file_bucket_name IS '文件的存储桶';


--
-- TOC entry 5554 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.cover_file_path; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.cover_file_path IS '封面文件存储名';


--
-- TOC entry 5555 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.cover_file_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.cover_file_bucket_name IS '封面文件存储桶';


--
-- TOC entry 5556 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.ofd_file_path; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.ofd_file_path IS 'ofd对象存储名';


--
-- TOC entry 5557 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.ofd_file_bucket_name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.ofd_file_bucket_name IS 'OFD文件的存储桶';


--
-- TOC entry 5558 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.state; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.state IS '状态（1-正常 2-删除）';


--
-- TOC entry 5559 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.create_by IS '创建人';


--
-- TOC entry 5560 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.create_time IS '创建时间';


--
-- TOC entry 5561 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.update_by IS '更新人';


--
-- TOC entry 5562 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.update_time IS '更新时间';


--
-- TOC entry 5563 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.original_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.original_file_id IS '原件在对象存储中文件id';


--
-- TOC entry 5564 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.ofd_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.ofd_file_id IS 'ofd在对象存储中文件id';


--
-- TOC entry 5565 (class 0 OID 0)
-- Dependencies: 369
-- Name: COLUMN rc_sample_template_file.cover_file_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sample_template_file.cover_file_id IS '封面图片在对象存储中文件id';


--
-- TOC entry 320 (class 1259 OID 31195)
-- Name: rc_security_policy; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_security_policy (
    id bigint NOT NULL,
    policy_type character varying(1) NOT NULL,
    policy_id bigint NOT NULL,
    role_id bigint,
    dimension character varying(1) NOT NULL,
    object_id bigint
);


ALTER TABLE "plss-record".rc_security_policy OWNER TO suwell;

--
-- TOC entry 5566 (class 0 OID 0)
-- Dependencies: 320
-- Name: TABLE rc_security_policy; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_security_policy IS '安全策略表';


--
-- TOC entry 5567 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN rc_security_policy.policy_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_security_policy.policy_type IS '安全策略类型（水印、脱敏）';


--
-- TOC entry 5568 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN rc_security_policy.policy_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_security_policy.policy_id IS '安全策略id';


--
-- TOC entry 5569 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN rc_security_policy.role_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_security_policy.role_id IS '角色id';


--
-- TOC entry 5570 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN rc_security_policy.dimension; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_security_policy.dimension IS '库、文件、全部';


--
-- TOC entry 5571 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN rc_security_policy.object_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_security_policy.object_id IS '库或文件id';


--
-- TOC entry 343 (class 1259 OID 32292)
-- Name: rc_sensitive_category; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_sensitive_category (
    id bigint NOT NULL,
    name character varying(50) NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_sensitive_category OWNER TO suwell;

--
-- TOC entry 5572 (class 0 OID 0)
-- Dependencies: 343
-- Name: TABLE rc_sensitive_category; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_sensitive_category IS '敏感词分类表';


--
-- TOC entry 5573 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.id IS 'id';


--
-- TOC entry 5574 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.name IS '敏感类型名称';


--
-- TOC entry 5575 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.status IS '状态';


--
-- TOC entry 5576 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.del_flag IS '删除标识';


--
-- TOC entry 5577 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.create_by IS '创建人';


--
-- TOC entry 5578 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.create_time IS '创建时间';


--
-- TOC entry 5579 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.update_by IS '修改人';


--
-- TOC entry 5580 (class 0 OID 0)
-- Dependencies: 343
-- Name: COLUMN rc_sensitive_category.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category.update_time IS '修改时间';


--
-- TOC entry 344 (class 1259 OID 32333)
-- Name: rc_sensitive_category_word; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_sensitive_category_word (
    id bigint NOT NULL,
    category_id bigint NOT NULL,
    word_id bigint NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_sensitive_category_word OWNER TO suwell;

--
-- TOC entry 5581 (class 0 OID 0)
-- Dependencies: 344
-- Name: TABLE rc_sensitive_category_word; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_sensitive_category_word IS '分类-敏感词关联表';


--
-- TOC entry 5582 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.category_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.category_id IS '敏感类型id';


--
-- TOC entry 5583 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.word_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.word_id IS '敏感词id';


--
-- TOC entry 5584 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.del_flag IS '删除状态';


--
-- TOC entry 5585 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.create_by IS '创建人';


--
-- TOC entry 5586 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.create_time IS '创建日期';


--
-- TOC entry 5587 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.update_by IS '修改人';


--
-- TOC entry 5588 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.update_time IS '修改日期';


--
-- TOC entry 5589 (class 0 OID 0)
-- Dependencies: 344
-- Name: COLUMN rc_sensitive_category_word.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_category_word.remark IS '备注';


--
-- TOC entry 342 (class 1259 OID 32278)
-- Name: rc_sensitive_word; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_sensitive_word (
    id bigint NOT NULL,
    name character varying(1024) NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-record".rc_sensitive_word OWNER TO suwell;

--
-- TOC entry 5590 (class 0 OID 0)
-- Dependencies: 342
-- Name: TABLE rc_sensitive_word; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_sensitive_word IS '敏感词表';


--
-- TOC entry 5591 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.id IS 'id';


--
-- TOC entry 5592 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.name IS '敏感词名称';


--
-- TOC entry 5593 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.status IS '状态';


--
-- TOC entry 5594 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.del_flag IS '删除标识';


--
-- TOC entry 5595 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.create_by IS '创建人';


--
-- TOC entry 5596 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.create_time IS '创建时间';


--
-- TOC entry 5597 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.update_by IS '修改人';


--
-- TOC entry 5598 (class 0 OID 0)
-- Dependencies: 342
-- Name: COLUMN rc_sensitive_word.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_sensitive_word.update_time IS '修改时间';


--
-- TOC entry 375 (class 1259 OID 60273)
-- Name: rc_statistic_data; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_statistic_data (
    id bigint NOT NULL,
    data_type bigint NOT NULL,
    business_date timestamp without time zone NOT NULL,
    quantity bigint DEFAULT 0 NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_statistic_data OWNER TO suwell;

--
-- TOC entry 5599 (class 0 OID 0)
-- Dependencies: 375
-- Name: TABLE rc_statistic_data; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_statistic_data IS '数据大屏的分析结果表';


--
-- TOC entry 5600 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.id IS '主键';


--
-- TOC entry 5601 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.data_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.data_type IS '数据类型';


--
-- TOC entry 5602 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.business_date; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.business_date IS '业务日期';


--
-- TOC entry 5603 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.quantity; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.quantity IS '业务数量';


--
-- TOC entry 5604 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.create_time IS '创建时间';


--
-- TOC entry 5605 (class 0 OID 0)
-- Dependencies: 375
-- Name: COLUMN rc_statistic_data.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_data.modified_time IS '修改时间';


--
-- TOC entry 374 (class 1259 OID 60260)
-- Name: rc_statistic_tag; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_statistic_tag (
    id bigint NOT NULL,
    tag_id bigint NOT NULL,
    tag_modified_time timestamp without time zone NOT NULL,
    tag_num bigint DEFAULT 0 NOT NULL,
    record_id bigint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_statistic_tag OWNER TO suwell;

--
-- TOC entry 5606 (class 0 OID 0)
-- Dependencies: 374
-- Name: TABLE rc_statistic_tag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_statistic_tag IS '标签的分析结果表';


--
-- TOC entry 5607 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.id IS '主键';


--
-- TOC entry 5608 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.tag_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.tag_id IS '标签id';


--
-- TOC entry 5609 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.tag_modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.tag_modified_time IS '标签的修改时间';


--
-- TOC entry 5610 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.tag_num; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.tag_num IS '标签的使用频次数量';


--
-- TOC entry 5611 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.record_id IS '文件id';


--
-- TOC entry 5612 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.create_time IS '创建时间';


--
-- TOC entry 5613 (class 0 OID 0)
-- Dependencies: 374
-- Name: COLUMN rc_statistic_tag.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_statistic_tag.modified_time IS '修改时间';


--
-- TOC entry 330 (class 1259 OID 31455)
-- Name: rc_task; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_task (
    id bigint NOT NULL,
    name character varying(512) NOT NULL,
    batch_id bigint NOT NULL,
    plan_id bigint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_task OWNER TO suwell;

--
-- TOC entry 5614 (class 0 OID 0)
-- Dependencies: 330
-- Name: TABLE rc_task; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_task IS '任务表';


--
-- TOC entry 5615 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.id IS '主键';


--
-- TOC entry 5616 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.name IS '任务名称（主文件名称）';


--
-- TOC entry 5617 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.batch_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.batch_id IS '批次id';


--
-- TOC entry 5618 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.plan_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.plan_id IS '入库方案id';


--
-- TOC entry 5619 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.create_time IS '创建时间';


--
-- TOC entry 5620 (class 0 OID 0)
-- Dependencies: 330
-- Name: COLUMN rc_task.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task.modified_time IS '修改时间';


--
-- TOC entry 340 (class 1259 OID 31811)
-- Name: rc_task_batch; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_task_batch (
    id bigint NOT NULL,
    name character varying(256) NOT NULL,
    plan_id bigint NOT NULL,
    record_type_id bigint NOT NULL,
    origin smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_task_batch OWNER TO suwell;

--
-- TOC entry 5621 (class 0 OID 0)
-- Dependencies: 340
-- Name: TABLE rc_task_batch; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_task_batch IS '批量任务表';


--
-- TOC entry 5622 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.id IS '主键';


--
-- TOC entry 5623 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.name IS '批次名称';


--
-- TOC entry 5624 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.plan_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.plan_id IS '入库方案id';


--
-- TOC entry 5625 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.record_type_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.record_type_id IS '文件类型id';


--
-- TOC entry 5626 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.origin; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.origin IS '文档来源';


--
-- TOC entry 5627 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.create_time IS '创建时间';


--
-- TOC entry 5628 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN rc_task_batch.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_batch.modified_time IS '修改时间';


--
-- TOC entry 331 (class 1259 OID 31462)
-- Name: rc_task_doc; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_task_doc (
    id bigint NOT NULL,
    name character varying(512) NOT NULL,
    task_id bigint NOT NULL,
    ctype smallint NOT NULL,
    doc_id bigint,
    storage_info character varying(2048),
    record_id bigint,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-record".rc_task_doc OWNER TO suwell;

--
-- TOC entry 5629 (class 0 OID 0)
-- Dependencies: 331
-- Name: TABLE rc_task_doc; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_task_doc IS '任务文件表';


--
-- TOC entry 5630 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.id IS '主键';


--
-- TOC entry 5631 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.name; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.name IS '文件名称';


--
-- TOC entry 5632 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.task_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.task_id IS '任务表id，rc_task表的主键';


--
-- TOC entry 5633 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.ctype IS '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件';


--
-- TOC entry 5634 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.doc_id IS '文档id，rc_document表的主键';


--
-- TOC entry 5635 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.storage_info; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.storage_info IS '上传至存储网关返回的信息：
{
    "fileName":"",
    "fileSize":,
    "fileMd5":"",
    "objectName":"",
}';


--
-- TOC entry 5636 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.record_id IS '文件id，rc_record表的主键';


--
-- TOC entry 5637 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.create_time IS '创建时间';


--
-- TOC entry 5638 (class 0 OID 0)
-- Dependencies: 331
-- Name: COLUMN rc_task_doc.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_doc.modified_time IS '修改时间';


--
-- TOC entry 332 (class 1259 OID 31469)
-- Name: rc_task_flow; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_task_flow (
    id bigint NOT NULL,
    task_doc_id bigint NOT NULL,
    task_id bigint NOT NULL,
    ctype smallint NOT NULL,
    node_id bigint NOT NULL,
    status smallint NOT NULL,
    deleted smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    node_type smallint NOT NULL,
    record_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_task_flow OWNER TO suwell;

--
-- TOC entry 5639 (class 0 OID 0)
-- Dependencies: 332
-- Name: TABLE rc_task_flow; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_task_flow IS '任务文件流转表';


--
-- TOC entry 5640 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.id IS '主键';


--
-- TOC entry 5641 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.task_doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.task_doc_id IS '任务文件表id，rc_task_doc表的主键';


--
-- TOC entry 5642 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.task_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.task_id IS '任务表id，rc_task表的主键';


--
-- TOC entry 5643 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.ctype IS '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件';


--
-- TOC entry 5644 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.node_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.node_id IS '所处节点id';


--
-- TOC entry 5645 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.status IS '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理';


--
-- TOC entry 5646 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.deleted; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.deleted IS '1-正常 2-删除';


--
-- TOC entry 5647 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.create_time IS '创建时间';


--
-- TOC entry 5648 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.modified_time IS '修改时间';


--
-- TOC entry 5649 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.node_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.node_type IS '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库';


--
-- TOC entry 5650 (class 0 OID 0)
-- Dependencies: 332
-- Name: COLUMN rc_task_flow.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow.record_id IS '文件id';


--
-- TOC entry 333 (class 1259 OID 31474)
-- Name: rc_task_flow_history; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_task_flow_history (
    id bigint NOT NULL,
    task_doc_id bigint NOT NULL,
    task_id bigint NOT NULL,
    ctype smallint NOT NULL,
    node_id bigint NOT NULL,
    status smallint NOT NULL,
    deleted smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    node_type smallint NOT NULL,
    record_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_task_flow_history OWNER TO suwell;

--
-- TOC entry 5651 (class 0 OID 0)
-- Dependencies: 333
-- Name: TABLE rc_task_flow_history; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_task_flow_history IS '任务文件流转历史表';


--
-- TOC entry 5652 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.id IS '主键';


--
-- TOC entry 5653 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.task_doc_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.task_doc_id IS '任务文件表id，rc_task_doc表的主键';


--
-- TOC entry 5654 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.task_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.task_id IS '任务表id，rc_task表的主键';


--
-- TOC entry 5655 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.ctype; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.ctype IS '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件';


--
-- TOC entry 5656 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.node_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.node_id IS '所处节点id';


--
-- TOC entry 5657 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.status IS '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理';


--
-- TOC entry 5658 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.deleted; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.deleted IS '1-正常 2-删除';


--
-- TOC entry 5659 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.create_time IS '创建时间';


--
-- TOC entry 5660 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.modified_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.modified_time IS '修改时间';


--
-- TOC entry 5661 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.node_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.node_type IS '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库';


--
-- TOC entry 5662 (class 0 OID 0)
-- Dependencies: 333
-- Name: COLUMN rc_task_flow_history.record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_task_flow_history.record_id IS '文件id';


--
-- TOC entry 321 (class 1259 OID 31200)
-- Name: rc_watermark_setting; Type: TABLE; Schema: plss-record; Owner: suwell
--

CREATE TABLE "plss-record".rc_watermark_setting (
    id bigint NOT NULL,
    watermark_json character varying(255),
    watermark_type character varying(255),
    example_url character varying(255),
    value character varying(255),
    enable_status character(1),
    del_flag character(1),
    create_by character varying(255),
    create_time timestamp(6) without time zone,
    update_by character varying(255),
    update_time timestamp(6) without time zone,
    remark character varying(255),
    repo_id bigint NOT NULL
);


ALTER TABLE "plss-record".rc_watermark_setting OWNER TO suwell;

--
-- TOC entry 5663 (class 0 OID 0)
-- Dependencies: 321
-- Name: TABLE rc_watermark_setting; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON TABLE "plss-record".rc_watermark_setting IS '水印设置表';


--
-- TOC entry 5664 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.watermark_json; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.watermark_json IS '水印json字符串';


--
-- TOC entry 5665 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.watermark_type; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.watermark_type IS '水印类型:0-自定义水印1-用户id 2-用户名称3-用户ip，支持多选';


--
-- TOC entry 5666 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.example_url; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.example_url IS '水印样例文件';


--
-- TOC entry 5667 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.value; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.value IS '水印内容';


--
-- TOC entry 5668 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.enable_status; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.enable_status IS '开关, 0-关 1-开';


--
-- TOC entry 5669 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.del_flag; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.del_flag IS '删除标识';


--
-- TOC entry 5670 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.create_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.create_by IS '创建人';


--
-- TOC entry 5671 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.create_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.create_time IS '创建时间';


--
-- TOC entry 5672 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.update_by; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.update_by IS '更新人';


--
-- TOC entry 5673 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.update_time; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.update_time IS '更新时间';


--
-- TOC entry 5674 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.remark; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.remark IS '备注';


--
-- TOC entry 5675 (class 0 OID 0)
-- Dependencies: 321
-- Name: COLUMN rc_watermark_setting.repo_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON COLUMN "plss-record".rc_watermark_setting.repo_id IS '库id，全局水印该字段为0';


--
-- TOC entry 381 (class 1259 OID 103734)
-- Name: sys_archive; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_archive (
    id bigint NOT NULL,
    business_type integer NOT NULL,
    store_type integer NOT NULL,
    store_start_time timestamp without time zone NOT NULL,
    store_end_time timestamp without time zone NOT NULL,
    expire_time timestamp without time zone,
    store_size bigint NOT NULL,
    serial_type integer NOT NULL,
    file_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_archive OWNER TO suwell;

--
-- TOC entry 5676 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.id IS 'id';


--
-- TOC entry 5677 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.business_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.business_type IS '业务类型，1：登录日志，2：操作日志';


--
-- TOC entry 5678 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.store_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.store_type IS '存储类型，1：永久存储，2临时存储';


--
-- TOC entry 5679 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.store_start_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.store_start_time IS '存储内容的开始时间';


--
-- TOC entry 5680 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.store_end_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.store_end_time IS '存储内容的结束时间';


--
-- TOC entry 5681 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.expire_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.expire_time IS '临时存储的过期时间';


--
-- TOC entry 5682 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.store_size; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.store_size IS '存储内容大小';


--
-- TOC entry 5683 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.serial_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.serial_type IS '存储序列化类型，1：json。2：excel';


--
-- TOC entry 5684 (class 0 OID 0)
-- Dependencies: 381
-- Name: COLUMN sys_archive.file_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_archive.file_id IS '文件id';


--
-- TOC entry 225 (class 1259 OID 24667)
-- Name: sys_category; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_category (
    id bigint NOT NULL,
    name character varying(64) DEFAULT ''::character varying NOT NULL,
    status character varying(1) NOT NULL,
    order_by smallint NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    ctype smallint DEFAULT 1,
    remark character varying(512),
    visit_type smallint DEFAULT 1,
    org_id bigint NOT NULL,
    image_url character varying(512),
    fixed_data smallint DEFAULT 2
);


ALTER TABLE "plss-system".sys_category OWNER TO suwell;

--
-- TOC entry 5685 (class 0 OID 0)
-- Dependencies: 225
-- Name: TABLE sys_category; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_category IS '分类表';


--
-- TOC entry 5686 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.id IS '唯一标识';


--
-- TOC entry 5687 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.name IS '名称';


--
-- TOC entry 5688 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5689 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.order_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.order_by IS '排序码';


--
-- TOC entry 5690 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.create_time IS '创建时间';


--
-- TOC entry 5691 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.create_by IS '创建人';


--
-- TOC entry 5692 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.update_by IS '更新人';


--
-- TOC entry 5693 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.update_time IS '修改时间';


--
-- TOC entry 5694 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.ctype; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.ctype IS '分类(1:文件, 2:库)';


--
-- TOC entry 5695 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.remark IS '备注';


--
-- TOC entry 5696 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.visit_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.visit_type IS '访问类型(1:public, 2:private, 3:protect)';


--
-- TOC entry 5697 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.org_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.org_id IS '组织机构id';


--
-- TOC entry 5698 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.image_url; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.image_url IS '图标url';


--
-- TOC entry 5699 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN sys_category.fixed_data; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category.fixed_data IS '是否内置固定数据1-内置2-非内置';


--
-- TOC entry 241 (class 1259 OID 25004)
-- Name: sys_category_relation; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_category_relation (
    descendant_id bigint NOT NULL,
    ancestor_id bigint DEFAULT 0 NOT NULL,
    distance smallint NOT NULL
);


ALTER TABLE "plss-system".sys_category_relation OWNER TO suwell;

--
-- TOC entry 5700 (class 0 OID 0)
-- Dependencies: 241
-- Name: TABLE sys_category_relation; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_category_relation IS '分类关联表';


--
-- TOC entry 5701 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN sys_category_relation.descendant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category_relation.descendant_id IS '后代id';


--
-- TOC entry 5702 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN sys_category_relation.ancestor_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category_relation.ancestor_id IS '父级id';


--
-- TOC entry 5703 (class 0 OID 0)
-- Dependencies: 241
-- Name: COLUMN sys_category_relation.distance; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_category_relation.distance IS '层级步数';


--
-- TOC entry 313 (class 1259 OID 28477)
-- Name: sys_collect; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_collect (
    id bigint NOT NULL,
    record_id bigint,
    record_name character varying(255),
    collect_time timestamp(6) without time zone,
    url character varying(255),
    user_id bigint,
    folder character varying(255),
    invalid_status smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-system".sys_collect OWNER TO suwell;

--
-- TOC entry 5704 (class 0 OID 0)
-- Dependencies: 313
-- Name: TABLE sys_collect; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_collect IS '文件收藏表';


--
-- TOC entry 5705 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.id IS '收藏主键id';


--
-- TOC entry 5706 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.record_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.record_id IS '文档ID';


--
-- TOC entry 5707 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.record_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.record_name IS '文档名称';


--
-- TOC entry 5708 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.collect_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.collect_time IS '收藏时间';


--
-- TOC entry 5709 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.url; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.url IS '访问链接';


--
-- TOC entry 5710 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.user_id IS 'userId';


--
-- TOC entry 5711 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.folder; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.folder IS '库/目录';


--
-- TOC entry 5712 (class 0 OID 0)
-- Dependencies: 313
-- Name: COLUMN sys_collect.invalid_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_collect.invalid_status IS '效用状态(1有效2无效)';


--
-- TOC entry 226 (class 1259 OID 24676)
-- Name: sys_config; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_config (
    config_id bigint NOT NULL,
    config_name character varying(128) DEFAULT ''::character varying NOT NULL,
    config_key character varying(128) DEFAULT ''::character varying NOT NULL,
    config_value text DEFAULT ''::character varying NOT NULL,
    config_type character(1) DEFAULT 'N'::bpchar NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    remark character varying(8192) DEFAULT ''::character varying,
    element_type character varying(64),
    element_name character varying(256)
);


ALTER TABLE "plss-system".sys_config OWNER TO suwell;

--
-- TOC entry 5713 (class 0 OID 0)
-- Dependencies: 226
-- Name: TABLE sys_config; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_config IS '参数配置表';


--
-- TOC entry 5714 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.config_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.config_id IS '参数主键';


--
-- TOC entry 5715 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.config_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.config_name IS '参数名称';


--
-- TOC entry 5716 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.config_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.config_key IS '参数键名';


--
-- TOC entry 5717 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.config_value; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.config_value IS '参数键值';


--
-- TOC entry 5718 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.config_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.config_type IS '系统内置（Y是 N否）';


--
-- TOC entry 5719 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5720 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.create_time IS '创建时间';


--
-- TOC entry 5721 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.create_by IS '创建人';


--
-- TOC entry 5722 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.update_time IS '修改时间';


--
-- TOC entry 5723 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.update_by IS '修改人';


--
-- TOC entry 5724 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.remark IS '备注';


--
-- TOC entry 5725 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.element_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.element_type IS '配置对应元素类型';


--
-- TOC entry 5726 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN sys_config.element_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_config.element_name IS '外观对应元素名称';


--
-- TOC entry 395 (class 1259 OID 187995)
-- Name: sys_config_20240415; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_config_20240415 (
    config_id bigint NOT NULL,
    config_name character varying(128) NOT NULL,
    config_key character varying(128) NOT NULL,
    config_value text NOT NULL,
    config_type character(1) NOT NULL,
    status smallint NOT NULL,
    create_time timestamp without time zone NOT NULL,
    create_by character varying(64) NOT NULL,
    update_time timestamp without time zone NOT NULL,
    update_by character varying(64) NOT NULL,
    remark character varying(8192),
    element_type character varying(64),
    element_name character varying(256)
);


ALTER TABLE "plss-system".sys_config_20240415 OWNER TO suwell;

--
-- TOC entry 227 (class 1259 OID 24691)
-- Name: sys_dict_data; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_dict_data (
    dict_code bigint NOT NULL,
    dict_sort integer DEFAULT 1 NOT NULL,
    dict_label character varying(128) DEFAULT ''::character varying NOT NULL,
    dict_value character varying(128) DEFAULT ''::character varying NOT NULL,
    dict_type character varying(128) DEFAULT ''::character varying NOT NULL,
    css_class character varying(128) DEFAULT ''::character varying,
    list_class character varying(128) DEFAULT ''::character varying,
    is_default character(1) DEFAULT 'N'::bpchar NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-system".sys_dict_data OWNER TO suwell;

--
-- TOC entry 5727 (class 0 OID 0)
-- Dependencies: 227
-- Name: TABLE sys_dict_data; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_dict_data IS '字典数据表';


--
-- TOC entry 5728 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.dict_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.dict_code IS '字典编码';


--
-- TOC entry 5729 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.dict_sort; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.dict_sort IS '字典排序';


--
-- TOC entry 5730 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.dict_label; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.dict_label IS '字典标签';


--
-- TOC entry 5731 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.dict_value; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.dict_value IS '字典键值';


--
-- TOC entry 5732 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.dict_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.dict_type IS '字典类型';


--
-- TOC entry 5733 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.css_class; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.css_class IS '样式属性（其他样式扩展）';


--
-- TOC entry 5734 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.list_class; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.list_class IS '表格回显样式';


--
-- TOC entry 5735 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.is_default; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.is_default IS '是否默认（Y是 N否）';


--
-- TOC entry 5736 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5737 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.create_by IS '创建者';


--
-- TOC entry 5738 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.create_time IS '创建时间';


--
-- TOC entry 5739 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.update_by IS '更新者';


--
-- TOC entry 5740 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.update_time IS '更新时间';


--
-- TOC entry 5741 (class 0 OID 0)
-- Dependencies: 227
-- Name: COLUMN sys_dict_data.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_data.remark IS '备注';


--
-- TOC entry 228 (class 1259 OID 24709)
-- Name: sys_dict_type; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_dict_type (
    dict_id bigint NOT NULL,
    dict_name character varying(128) DEFAULT ''::character varying NOT NULL,
    dict_type character varying(128) DEFAULT ''::character varying NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-system".sys_dict_type OWNER TO suwell;

--
-- TOC entry 5742 (class 0 OID 0)
-- Dependencies: 228
-- Name: TABLE sys_dict_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_dict_type IS '字典类型表';


--
-- TOC entry 5743 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.dict_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.dict_id IS '字典主键';


--
-- TOC entry 5744 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.dict_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.dict_name IS '字典名称';


--
-- TOC entry 5745 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.dict_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.dict_type IS '字典类型';


--
-- TOC entry 5746 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5747 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.create_time IS '创建时间';


--
-- TOC entry 5748 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.create_by IS '创建人';


--
-- TOC entry 5749 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.update_time IS '修改时间';


--
-- TOC entry 5750 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.update_by IS '修改人';


--
-- TOC entry 5751 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN sys_dict_type.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_dict_type.remark IS '备注';


--
-- TOC entry 339 (class 1259 OID 31746)
-- Name: sys_export_history; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_export_history (
    id bigint NOT NULL,
    export_path text,
    export_json text,
    status character varying(32) NOT NULL,
    create_by bigint,
    create_time timestamp(6) without time zone,
    tenant_id bigint,
    type character varying(255) NOT NULL,
    check_by bigint,
    check_time timestamp without time zone
);


ALTER TABLE "plss-system".sys_export_history OWNER TO suwell;

--
-- TOC entry 5752 (class 0 OID 0)
-- Dependencies: 339
-- Name: TABLE sys_export_history; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_export_history IS '导出历史表';


--
-- TOC entry 5753 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.export_path; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.export_path IS '检索路径';


--
-- TOC entry 5754 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.export_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.export_json IS '搜索参数';


--
-- TOC entry 5755 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.status IS '审核状态';


--
-- TOC entry 5756 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.create_by IS '创建人';


--
-- TOC entry 5757 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.create_time IS '创建时间';


--
-- TOC entry 5758 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.type IS '导出类型';


--
-- TOC entry 5759 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.check_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.check_by IS '审核人';


--
-- TOC entry 5760 (class 0 OID 0)
-- Dependencies: 339
-- Name: COLUMN sys_export_history.check_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_export_history.check_time IS '审核时间';


--
-- TOC entry 355 (class 1259 OID 32906)
-- Name: sys_literary_collect; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_literary_collect (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    literary_id bigint NOT NULL,
    literary_type smallint NOT NULL,
    state smallint DEFAULT 1 NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    update_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-system".sys_literary_collect OWNER TO suwell;

--
-- TOC entry 5761 (class 0 OID 0)
-- Dependencies: 355
-- Name: TABLE sys_literary_collect; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_literary_collect IS '文采收藏';


--
-- TOC entry 5762 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.id IS '主键id';


--
-- TOC entry 5763 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.user_id IS '用户id';


--
-- TOC entry 5764 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.literary_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.literary_id IS '文采id';


--
-- TOC entry 5765 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.literary_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.literary_type IS '目录名称';


--
-- TOC entry 5766 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.state; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.state IS '状态:1-正常 2删除';


--
-- TOC entry 5767 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.create_time IS '创建时间';


--
-- TOC entry 5768 (class 0 OID 0)
-- Dependencies: 355
-- Name: COLUMN sys_literary_collect.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_literary_collect.update_time IS '更新时间';


--
-- TOC entry 229 (class 1259 OID 24722)
-- Name: sys_logininfor; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_logininfor (
    info_id bigint NOT NULL,
    user_name character varying(64) DEFAULT ''::character varying NOT NULL,
    ipaddr character varying(128) DEFAULT ''::character varying NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    msg character varying(256) DEFAULT ''::character varying NOT NULL,
    access_time timestamp without time zone DEFAULT now() NOT NULL,
    os character varying(64),
    signature character varying(2048),
    check_sign smallint DEFAULT 1
);


ALTER TABLE "plss-system".sys_logininfor OWNER TO suwell;

--
-- TOC entry 5769 (class 0 OID 0)
-- Dependencies: 229
-- Name: TABLE sys_logininfor; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_logininfor IS '系统访问记录';


--
-- TOC entry 5770 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.info_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.info_id IS '访问ID';


--
-- TOC entry 5771 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.user_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.user_name IS '用户账号';


--
-- TOC entry 5772 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.ipaddr; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.ipaddr IS '登录IP地址';


--
-- TOC entry 5773 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.status IS '状态(1:成功，2:失败)';


--
-- TOC entry 5774 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.msg; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.msg IS '提示信息';


--
-- TOC entry 5775 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.access_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.access_time IS '访问时间';


--
-- TOC entry 5776 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.os; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.os IS '操作系统';


--
-- TOC entry 5777 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.signature; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.signature IS '签名';


--
-- TOC entry 5778 (class 0 OID 0)
-- Dependencies: 229
-- Name: COLUMN sys_logininfor.check_sign; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_logininfor.check_sign IS '验签结果:1未验签，2通过，3不通过';


--
-- TOC entry 230 (class 1259 OID 24730)
-- Name: sys_menu; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_menu (
    menu_id bigint NOT NULL,
    parent_id bigint DEFAULT 0 NOT NULL,
    menu_name character varying(64) DEFAULT ''::character varying NOT NULL,
    order_num integer DEFAULT 0 NOT NULL,
    path character varying(256) DEFAULT ''::character varying,
    component character varying(256) DEFAULT ''::character varying,
    query character varying(256) DEFAULT ''::character varying,
    is_frame character varying(1) DEFAULT 1 NOT NULL,
    is_cache character varying(1) DEFAULT 1 NOT NULL,
    menu_type character(1) DEFAULT ''::bpchar NOT NULL,
    visible character(1) DEFAULT 1 NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    perms character varying(128) DEFAULT ''::character varying,
    icon character varying(128) DEFAULT '#'::character varying,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    system_menu_flag smallint DEFAULT 2
);


ALTER TABLE "plss-system".sys_menu OWNER TO suwell;

--
-- TOC entry 5779 (class 0 OID 0)
-- Dependencies: 230
-- Name: TABLE sys_menu; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_menu IS '菜单表';


--
-- TOC entry 5780 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.menu_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.menu_id IS '菜单ID';


--
-- TOC entry 5781 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.parent_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.parent_id IS '父菜单ID';


--
-- TOC entry 5782 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.menu_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.menu_name IS '菜单名称';


--
-- TOC entry 5783 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.order_num; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.order_num IS '显示顺序';


--
-- TOC entry 5784 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.path; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.path IS '路由地址';


--
-- TOC entry 5785 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.component; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.component IS '组件路径';


--
-- TOC entry 5786 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.query; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.query IS '路由参数';


--
-- TOC entry 5787 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.is_frame; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.is_frame IS '是否为外链（1是 2否）';


--
-- TOC entry 5788 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.is_cache; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.is_cache IS '是否缓存（1缓存 2不缓存）';


--
-- TOC entry 5789 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.menu_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.menu_type IS '菜单类型（M目录 C菜单 F按钮）';


--
-- TOC entry 5790 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.visible; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.visible IS '菜单状态（1显示 2隐藏）';


--
-- TOC entry 5791 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.status IS '状态(1:启用, 2:禁用)';


--
-- TOC entry 5792 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.perms; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.perms IS '权限标识';


--
-- TOC entry 5793 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.icon; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.icon IS '菜单图标';


--
-- TOC entry 5794 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.create_by IS '创建者';


--
-- TOC entry 5795 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.create_time IS '创建时间';


--
-- TOC entry 5796 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.update_by IS '更新者';


--
-- TOC entry 5797 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.update_time IS '更新时间';


--
-- TOC entry 5798 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.remark IS '备注';


--
-- TOC entry 5799 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN sys_menu.system_menu_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_menu.system_menu_flag IS '是否是系统菜单(admin仅展示系统菜单，其他用户规则不变):1是 2否';


--
-- TOC entry 303 (class 1259 OID 27618)
-- Name: sys_message; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_message (
    id bigint NOT NULL,
    type smallint NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    title character varying(256) NOT NULL,
    send_time timestamp without time zone NOT NULL,
    content text NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    del_flag smallint DEFAULT 1 NOT NULL,
    accept_person_id bigint NOT NULL,
    send_person_id bigint NOT NULL,
    record_id bigint
);


ALTER TABLE "plss-system".sys_message OWNER TO suwell;

--
-- TOC entry 5800 (class 0 OID 0)
-- Dependencies: 303
-- Name: TABLE sys_message; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_message IS '消息表';


--
-- TOC entry 5801 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.id IS '主键';


--
-- TOC entry 5802 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.type IS '消息类型：1变更提醒，2协同提醒';


--
-- TOC entry 5803 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.status IS '消息状态：1未读，2已读';


--
-- TOC entry 5804 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.title; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.title IS '消息标题';


--
-- TOC entry 5805 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.send_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.send_time IS '发送日期';


--
-- TOC entry 5806 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.content; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.content IS '消息内容';


--
-- TOC entry 5807 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.del_flag IS '删除标记：1未删除，2已删除';


--
-- TOC entry 5808 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.accept_person_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.accept_person_id IS '收件人id，用于查询我的消息';


--
-- TOC entry 5809 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.send_person_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.send_person_id IS '发送人id';


--
-- TOC entry 5810 (class 0 OID 0)
-- Dependencies: 303
-- Name: COLUMN sys_message.record_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_message.record_id IS '文件id';


--
-- TOC entry 231 (class 1259 OID 24753)
-- Name: sys_notice; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_notice (
    notice_id bigint NOT NULL,
    notice_title character varying(64) DEFAULT ''::character varying NOT NULL,
    notice_type character(1) DEFAULT 1 NOT NULL,
    notice_content character varying(1024) DEFAULT ''::character varying NOT NULL,
    status character(1) DEFAULT 0 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(256) DEFAULT ''::character varying
);


ALTER TABLE "plss-system".sys_notice OWNER TO suwell;

--
-- TOC entry 5811 (class 0 OID 0)
-- Dependencies: 231
-- Name: TABLE sys_notice; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_notice IS '通知公告表';


--
-- TOC entry 5812 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.notice_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.notice_id IS '公告ID';


--
-- TOC entry 5813 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.notice_title; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.notice_title IS '公告标题';


--
-- TOC entry 5814 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.notice_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.notice_type IS '公告类型（1通知 2公告）';


--
-- TOC entry 5815 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.notice_content; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.notice_content IS '公告内容';


--
-- TOC entry 5816 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.status IS '公告状态（1正常，2:关闭 ）';


--
-- TOC entry 5817 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.create_by IS '创建者';


--
-- TOC entry 5818 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.create_time IS '创建时间';


--
-- TOC entry 5819 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.update_by IS '更新者';


--
-- TOC entry 5820 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.update_time IS '更新时间';


--
-- TOC entry 5821 (class 0 OID 0)
-- Dependencies: 231
-- Name: COLUMN sys_notice.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_notice.remark IS '备注';


--
-- TOC entry 399 (class 1259 OID 188031)
-- Name: sys_oa_org; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_oa_org (
    id bigint NOT NULL,
    org_uid character varying(100) NOT NULL,
    org_parent_uid character varying(100) NOT NULL,
    details_json text,
    operation_status smallint DEFAULT 0,
    deleted smallint DEFAULT 0,
    create_time timestamp(6) without time zone,
    update_time timestamp(6) without time zone
);


ALTER TABLE "plss-system".sys_oa_org OWNER TO suwell;

--
-- TOC entry 5822 (class 0 OID 0)
-- Dependencies: 399
-- Name: TABLE sys_oa_org; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_oa_org IS 'oa系统组织机构表';


--
-- TOC entry 5823 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.id IS '主键';


--
-- TOC entry 5824 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.org_uid; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.org_uid IS '对接系统方的组织机构唯一标识';


--
-- TOC entry 5825 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.org_parent_uid; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.org_parent_uid IS '对接系统方的组织机构父级标识';


--
-- TOC entry 5826 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.details_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.details_json IS '详情数据';


--
-- TOC entry 5827 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.operation_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.operation_status IS '同步状态（0：未同步，1：已同步）';


--
-- TOC entry 5828 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.deleted; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.deleted IS '删除状态（0：未删，1：已删）';


--
-- TOC entry 5829 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.create_time IS '创建时间';


--
-- TOC entry 5830 (class 0 OID 0)
-- Dependencies: 399
-- Name: COLUMN sys_oa_org.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_org.update_time IS '更新时间';


--
-- TOC entry 400 (class 1259 OID 188040)
-- Name: sys_oa_user; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_oa_user (
    id bigint NOT NULL,
    user_uid character varying(100),
    details_json text,
    operation_status smallint DEFAULT 0,
    deleted smallint DEFAULT 0,
    create_time timestamp(6) without time zone,
    update_time timestamp(6) without time zone
);


ALTER TABLE "plss-system".sys_oa_user OWNER TO suwell;

--
-- TOC entry 5831 (class 0 OID 0)
-- Dependencies: 400
-- Name: TABLE sys_oa_user; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_oa_user IS 'oa系统用户表';


--
-- TOC entry 5832 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.user_uid; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.user_uid IS '对接系统方用户唯一标识';


--
-- TOC entry 5833 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.details_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.details_json IS '详情数据';


--
-- TOC entry 5834 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.operation_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.operation_status IS '同步状态（0：未同步，1：已同步）';


--
-- TOC entry 5835 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.deleted; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.deleted IS '删除状态（0：未删，1：已删）';


--
-- TOC entry 5836 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.create_time IS '创建时间';


--
-- TOC entry 5837 (class 0 OID 0)
-- Dependencies: 400
-- Name: COLUMN sys_oa_user.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oa_user.update_time IS '更新时间';


--
-- TOC entry 232 (class 1259 OID 24767)
-- Name: sys_oper_log; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_oper_log (
    oper_id bigint NOT NULL,
    title character varying(64) DEFAULT ''::character varying NOT NULL,
    business_type smallint DEFAULT 0 NOT NULL,
    method character varying(128) DEFAULT ''::character varying NOT NULL,
    request_method character varying(16) DEFAULT ''::character varying NOT NULL,
    operator_type smallint DEFAULT 0 NOT NULL,
    oper_name character varying(64) DEFAULT ''::character varying NOT NULL,
    org_name character varying(64) DEFAULT ''::character varying,
    oper_url character varying(256) DEFAULT ''::character varying NOT NULL,
    oper_ip character varying(128) DEFAULT ''::character varying NOT NULL,
    oper_location character varying(256) DEFAULT ''::character varying,
    oper_param character varying(2048) DEFAULT ''::character varying,
    json_result character varying(2048) DEFAULT ''::character varying NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    error_msg character varying(2048) DEFAULT ''::character varying,
    oper_time timestamp without time zone DEFAULT now() NOT NULL,
    cost_time bigint DEFAULT 0 NOT NULL,
    signature character varying(2048),
    check_sign smallint DEFAULT 1
);


ALTER TABLE "plss-system".sys_oper_log OWNER TO suwell;

--
-- TOC entry 5838 (class 0 OID 0)
-- Dependencies: 232
-- Name: TABLE sys_oper_log; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_oper_log IS '操作日志记录';


--
-- TOC entry 5839 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_id IS '日志主键';


--
-- TOC entry 5840 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.title; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.title IS '模块标题';


--
-- TOC entry 5841 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.business_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.business_type IS '业务类型（0其它 1新增 2修改 3删除）';


--
-- TOC entry 5842 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.method; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.method IS '方法名称';


--
-- TOC entry 5843 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.request_method; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.request_method IS '请求方式';


--
-- TOC entry 5844 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.operator_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.operator_type IS '操作类别（0其它 1后台用户 2手机端用户）';


--
-- TOC entry 5845 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_name IS '操作人员';


--
-- TOC entry 5846 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.org_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.org_name IS '机构名称';


--
-- TOC entry 5847 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_url; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_url IS '请求URL';


--
-- TOC entry 5848 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_ip; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_ip IS '主机地址';


--
-- TOC entry 5849 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_location; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_location IS '操作地点';


--
-- TOC entry 5850 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_param; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_param IS '请求参数';


--
-- TOC entry 5851 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.json_result; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.json_result IS '返回参数';


--
-- TOC entry 5852 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.status IS '操作状态（1：正常，2：异常 ）';


--
-- TOC entry 5853 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.error_msg; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.error_msg IS '错误消息';


--
-- TOC entry 5854 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.oper_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.oper_time IS '操作时间';


--
-- TOC entry 5855 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.cost_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.cost_time IS '消耗时间，单位毫秒';


--
-- TOC entry 5856 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.signature; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.signature IS '签名';


--
-- TOC entry 5857 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN sys_oper_log.check_sign; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_oper_log.check_sign IS '验签结果:1未验签，2通过，3不通过';


--
-- TOC entry 378 (class 1259 OID 88949)
-- Name: sys_opt_data_pms; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_opt_data_pms (
    id bigint NOT NULL,
    module_type integer NOT NULL,
    pms_name character varying(512) NOT NULL,
    pms_type integer NOT NULL,
    status integer NOT NULL,
    operate_type character varying(512) NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    modified_time timestamp(6) without time zone NOT NULL,
    tenant_id bigint NOT NULL,
    rtype integer DEFAULT 2
);


ALTER TABLE "plss-system".sys_opt_data_pms OWNER TO suwell;

--
-- TOC entry 5858 (class 0 OID 0)
-- Dependencies: 378
-- Name: TABLE sys_opt_data_pms; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_opt_data_pms IS '数据权限操作';


--
-- TOC entry 5859 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.id IS '主键';


--
-- TOC entry 5860 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.module_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.module_type IS '模块类型1-文档入库2-入库任务3-入库方案配置4-文库管理';


--
-- TOC entry 5861 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.pms_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.pms_name IS '权限名称';


--
-- TOC entry 5862 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.pms_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.pms_type IS '权限类型1-本人创建数据权限2-本部门数据权限3-本部门及下级部门数据权限4-本单位数据权限5-本单位及下级单位数据权限（租户内）6-本租户数据权限';


--
-- TOC entry 5863 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.status IS '状态1-正常2-禁用';


--
-- TOC entry 5864 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.operate_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.operate_type IS '数据范围操作类型(多只逗号分隔)1-查看2-编辑';


--
-- TOC entry 5865 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.create_time IS '创建时间';


--
-- TOC entry 5866 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.modified_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.modified_time IS '修改时间';


--
-- TOC entry 5867 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.tenant_id IS '租户id';


--
-- TOC entry 5868 (class 0 OID 0)
-- Dependencies: 378
-- Name: COLUMN sys_opt_data_pms.rtype; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_opt_data_pms.rtype IS '1-内置  2-其他';


--
-- TOC entry 233 (class 1259 OID 24788)
-- Name: sys_org; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_org (
    org_id bigint NOT NULL,
    parent_id bigint DEFAULT 0 NOT NULL,
    ancestors character varying(1024) DEFAULT ''::character varying NOT NULL,
    org_name character varying(64) DEFAULT ''::character varying NOT NULL,
    order_num integer DEFAULT 0 NOT NULL,
    leader character varying(64) DEFAULT ''::character varying,
    phone character varying(11) DEFAULT ''::character varying,
    email character varying(64) DEFAULT ''::character varying,
    status character(1) DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512),
    org_type character(1),
    org_code character varying(64),
    src_id bigint,
    access_key character varying(128),
    placeholder smallint,
    tenant_id bigint,
    exist_type smallint,
    org_level smallint,
    full_name character varying(256),
    region_id bigint,
    file_show_status character(1) DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-system".sys_org OWNER TO suwell;

--
-- TOC entry 5869 (class 0 OID 0)
-- Dependencies: 233
-- Name: TABLE sys_org; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_org IS '机构表';


--
-- TOC entry 5870 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.org_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.org_id IS '机构id';


--
-- TOC entry 5871 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.parent_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.parent_id IS '父机构id';


--
-- TOC entry 5872 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.ancestors; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.ancestors IS '祖级列表';


--
-- TOC entry 5873 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.org_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.org_name IS '机构名称';


--
-- TOC entry 5874 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.order_num; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.order_num IS '显示顺序';


--
-- TOC entry 5875 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.leader; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.leader IS '负责人';


--
-- TOC entry 5876 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.phone; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.phone IS '联系电话';


--
-- TOC entry 5877 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.email; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.email IS '邮箱';


--
-- TOC entry 5878 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.status IS '机构状态（1：正常，2：停用）';


--
-- TOC entry 5879 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.del_flag IS '删除标志（1：代表存在，2：代表删除）';


--
-- TOC entry 5880 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.create_by IS '创建者';


--
-- TOC entry 5881 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.create_time IS '创建时间';


--
-- TOC entry 5882 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.update_by IS '更新者';


--
-- TOC entry 5883 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.update_time IS '更新时间';


--
-- TOC entry 5884 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.org_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.org_type IS '组织机构类型，1-单位，2-部门';


--
-- TOC entry 5885 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.org_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.org_code IS '导入组织机构时的组织机构编码';


--
-- TOC entry 5886 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.src_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.src_id IS '来源id';


--
-- TOC entry 5887 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.access_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.access_key IS '来源系统唯一标识';


--
-- TOC entry 5888 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.placeholder; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.placeholder IS '自动创建占位符(1:是, 2:否)';


--
-- TOC entry 5889 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.tenant_id IS '租户id';


--
-- TOC entry 5890 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.exist_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.exist_type IS '部门类型（省局单位，内设机构等等......）';


--
-- TOC entry 5891 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.org_level; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.org_level IS '部门等级（国家级，直属单位等）';


--
-- TOC entry 5892 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.region_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.region_id IS '行政区域id';


--
-- TOC entry 5893 (class 0 OID 0)
-- Dependencies: 233
-- Name: COLUMN sys_org.file_show_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_org.file_show_status IS '文件展示状态(1展示2不展示)';


--
-- TOC entry 318 (class 1259 OID 30384)
-- Name: sys_policy_rel; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_policy_rel (
    role_id bigint NOT NULL,
    dimension character varying(255) NOT NULL,
    object_id bigint,
    security_policy_id bigint NOT NULL,
    id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_policy_rel OWNER TO suwell;

--
-- TOC entry 5894 (class 0 OID 0)
-- Dependencies: 318
-- Name: TABLE sys_policy_rel; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_policy_rel IS '安全策略关联表';


--
-- TOC entry 5895 (class 0 OID 0)
-- Dependencies: 318
-- Name: COLUMN sys_policy_rel.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_policy_rel.role_id IS '角色id';


--
-- TOC entry 5896 (class 0 OID 0)
-- Dependencies: 318
-- Name: COLUMN sys_policy_rel.dimension; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_policy_rel.dimension IS '库、文件、全部';


--
-- TOC entry 5897 (class 0 OID 0)
-- Dependencies: 318
-- Name: COLUMN sys_policy_rel.object_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_policy_rel.object_id IS '库或文件id';


--
-- TOC entry 5898 (class 0 OID 0)
-- Dependencies: 318
-- Name: COLUMN sys_policy_rel.security_policy_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_policy_rel.security_policy_id IS '策略id';


--
-- TOC entry 234 (class 1259 OID 24806)
-- Name: sys_post; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_post (
    post_id bigint NOT NULL,
    post_code character varying(64) DEFAULT ''::character varying NOT NULL,
    post_name character varying(64) DEFAULT ''::character varying NOT NULL,
    post_sort integer DEFAULT 1 NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying NOT NULL,
    tenant_id bigint
);


ALTER TABLE "plss-system".sys_post OWNER TO suwell;

--
-- TOC entry 5899 (class 0 OID 0)
-- Dependencies: 234
-- Name: TABLE sys_post; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_post IS '岗位信息表';


--
-- TOC entry 5900 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.post_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.post_id IS '岗位ID';


--
-- TOC entry 5901 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.post_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.post_code IS '岗位编码';


--
-- TOC entry 5902 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.post_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.post_name IS '岗位名称';


--
-- TOC entry 5903 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.post_sort; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.post_sort IS '显示顺序';


--
-- TOC entry 5904 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.status IS '状态（ 1：正常，2：停用）';


--
-- TOC entry 5905 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.create_by IS '创建者';


--
-- TOC entry 5906 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.create_time IS '创建时间';


--
-- TOC entry 5907 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.update_by IS '更新者';


--
-- TOC entry 5908 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.update_time IS '更新时间';


--
-- TOC entry 5909 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.remark IS '备注';


--
-- TOC entry 5910 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN sys_post.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_post.tenant_id IS '租户id';


--
-- TOC entry 316 (class 1259 OID 28925)
-- Name: sys_region; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_region (
    id bigint NOT NULL,
    name character varying(128) NOT NULL,
    deep bigint,
    pid bigint
);


ALTER TABLE "plss-system".sys_region OWNER TO suwell;

--
-- TOC entry 5911 (class 0 OID 0)
-- Dependencies: 316
-- Name: TABLE sys_region; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_region IS '行政区域表';


--
-- TOC entry 5912 (class 0 OID 0)
-- Dependencies: 316
-- Name: COLUMN sys_region.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_region.id IS '唯一主键id';


--
-- TOC entry 5913 (class 0 OID 0)
-- Dependencies: 316
-- Name: COLUMN sys_region.name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_region.name IS '行政区域名称';


--
-- TOC entry 5914 (class 0 OID 0)
-- Dependencies: 316
-- Name: COLUMN sys_region.deep; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_region.deep IS '行政区域层级，1：国家级，2：直属单位，3：省级，4：市级，5：县级';


--
-- TOC entry 5915 (class 0 OID 0)
-- Dependencies: 316
-- Name: COLUMN sys_region.pid; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_region.pid IS '上级id';


--
-- TOC entry 401 (class 1259 OID 188049)
-- Name: sys_resource_push_record; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_resource_push_record (
    id bigint NOT NULL,
    manufacturer_code character varying(100) NOT NULL,
    business_type character varying(150) NOT NULL,
    business_data_id bigint NOT NULL,
    business_data_update_time timestamp(6) without time zone NOT NULL,
    create_time timestamp(6) without time zone NOT NULL,
    update_time timestamp(6) without time zone NOT NULL
);


ALTER TABLE "plss-system".sys_resource_push_record OWNER TO suwell;

--
-- TOC entry 5916 (class 0 OID 0)
-- Dependencies: 401
-- Name: TABLE sys_resource_push_record; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_resource_push_record IS 'plss系统资源推送记录表';


--
-- TOC entry 5917 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.id IS '主键';


--
-- TOC entry 5918 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.manufacturer_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.manufacturer_code IS '对接厂商code';


--
-- TOC entry 5919 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.business_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.business_type IS '业务类型：user-用户，org-组织，role-角色';


--
-- TOC entry 5920 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.business_data_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.business_data_id IS '业务数据id';


--
-- TOC entry 5921 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.business_data_update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.business_data_update_time IS '推送时的业务数据更新时间';


--
-- TOC entry 5922 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.create_time IS '创建时间';


--
-- TOC entry 5923 (class 0 OID 0)
-- Dependencies: 401
-- Name: COLUMN sys_resource_push_record.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_resource_push_record.update_time IS '更新时间';


--
-- TOC entry 235 (class 1259 OID 24820)
-- Name: sys_role; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_role (
    role_id bigint NOT NULL,
    role_name character varying(36) DEFAULT ''::character varying NOT NULL,
    role_key character varying(128) DEFAULT ''::character varying,
    role_sort integer DEFAULT 0,
    data_scope character(1) DEFAULT 1 NOT NULL,
    menu_check_strictly character(1) DEFAULT 1 NOT NULL,
    org_check_strictly character(1) DEFAULT 1 NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    tenant_id bigint DEFAULT 0 NOT NULL,
    role_type smallint DEFAULT 0 NOT NULL
);


ALTER TABLE "plss-system".sys_role OWNER TO suwell;

--
-- TOC entry 5924 (class 0 OID 0)
-- Dependencies: 235
-- Name: TABLE sys_role; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_role IS '角色表';


--
-- TOC entry 5925 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.role_id IS '角色ID';


--
-- TOC entry 5926 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.role_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.role_name IS '角色名称';


--
-- TOC entry 5927 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.role_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.role_key IS '角色权限字符串';


--
-- TOC entry 5928 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.role_sort; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.role_sort IS '显示顺序';


--
-- TOC entry 5929 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.data_scope; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.data_scope IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';


--
-- TOC entry 5930 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.menu_check_strictly; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.menu_check_strictly IS '菜单树选择项是否关联显示';


--
-- TOC entry 5931 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.org_check_strictly; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.org_check_strictly IS '机构树选择项是否关联显示';


--
-- TOC entry 5932 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.status IS '角色状态（1：正常， 2：停用）';


--
-- TOC entry 5933 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.del_flag IS '删除标志（ 1代表存在，2代表删除）';


--
-- TOC entry 5934 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.create_by IS '创建者';


--
-- TOC entry 5935 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.create_time IS '创建时间';


--
-- TOC entry 5936 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.update_by IS '更新者';


--
-- TOC entry 5937 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.update_time IS '更新时间';


--
-- TOC entry 5938 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.remark IS '备注';


--
-- TOC entry 5939 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.tenant_id IS '租户id';


--
-- TOC entry 5940 (class 0 OID 0)
-- Dependencies: 235
-- Name: COLUMN sys_role.role_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role.role_type IS '角色数据类型1.配置管理员，2.系统管理员，3.安全管理员，4.安全审计员，5.超级管理员，6.普通用户，7.配置管理员创建的全局角色，99.用户自定义角色';


--
-- TOC entry 379 (class 1259 OID 92762)
-- Name: sys_role_data_pms; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_role_data_pms (
    role_id bigint NOT NULL,
    data_pms_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_role_data_pms OWNER TO suwell;

--
-- TOC entry 5941 (class 0 OID 0)
-- Dependencies: 379
-- Name: TABLE sys_role_data_pms; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_role_data_pms IS '角色关联数据权限表';


--
-- TOC entry 5942 (class 0 OID 0)
-- Dependencies: 379
-- Name: COLUMN sys_role_data_pms.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_data_pms.role_id IS '角色id';


--
-- TOC entry 5943 (class 0 OID 0)
-- Dependencies: 379
-- Name: COLUMN sys_role_data_pms.data_pms_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_data_pms.data_pms_id IS '数据权限id';


--
-- TOC entry 236 (class 1259 OID 24838)
-- Name: sys_role_menu; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_role_menu (
    role_id bigint NOT NULL,
    menu_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_role_menu OWNER TO suwell;

--
-- TOC entry 5944 (class 0 OID 0)
-- Dependencies: 236
-- Name: TABLE sys_role_menu; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_role_menu IS '角色-菜单关联表';


--
-- TOC entry 5945 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN sys_role_menu.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_menu.role_id IS '角色id';


--
-- TOC entry 5946 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN sys_role_menu.menu_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_menu.menu_id IS '菜单id';


--
-- TOC entry 237 (class 1259 OID 24841)
-- Name: sys_role_org; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_role_org (
    role_id bigint NOT NULL,
    org_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_role_org OWNER TO suwell;

--
-- TOC entry 5947 (class 0 OID 0)
-- Dependencies: 237
-- Name: TABLE sys_role_org; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_role_org IS '角色和机构关联表';


--
-- TOC entry 5948 (class 0 OID 0)
-- Dependencies: 237
-- Name: COLUMN sys_role_org.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_org.role_id IS '角色ID';


--
-- TOC entry 5949 (class 0 OID 0)
-- Dependencies: 237
-- Name: COLUMN sys_role_org.org_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_org.org_id IS '机构ID';


--
-- TOC entry 319 (class 1259 OID 30389)
-- Name: sys_role_policy; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_role_policy (
    id bigint NOT NULL,
    policy_type character varying(255) NOT NULL,
    policy_id bigint NOT NULL,
    role_id bigint NOT NULL,
    dimension character varying(255) NOT NULL,
    object_id bigint
);


ALTER TABLE "plss-system".sys_role_policy OWNER TO suwell;

--
-- TOC entry 5950 (class 0 OID 0)
-- Dependencies: 319
-- Name: TABLE sys_role_policy; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_role_policy IS '角色-安全策略表';


--
-- TOC entry 5951 (class 0 OID 0)
-- Dependencies: 319
-- Name: COLUMN sys_role_policy.policy_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_policy.policy_type IS '安全策略类型（水印、脱敏）';


--
-- TOC entry 5952 (class 0 OID 0)
-- Dependencies: 319
-- Name: COLUMN sys_role_policy.policy_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_policy.policy_id IS '安全策略id';


--
-- TOC entry 5953 (class 0 OID 0)
-- Dependencies: 319
-- Name: COLUMN sys_role_policy.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_policy.role_id IS '角色id';


--
-- TOC entry 5954 (class 0 OID 0)
-- Dependencies: 319
-- Name: COLUMN sys_role_policy.dimension; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_policy.dimension IS '库、文件、全部';


--
-- TOC entry 5955 (class 0 OID 0)
-- Dependencies: 319
-- Name: COLUMN sys_role_policy.object_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_role_policy.object_id IS '库或文件id';


--
-- TOC entry 326 (class 1259 OID 31343)
-- Name: sys_search_collect; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_search_collect (
    id bigint NOT NULL,
    name character varying(255),
    search_path text,
    search_json text,
    create_by bigint,
    create_time timestamp(6) without time zone
);


ALTER TABLE "plss-system".sys_search_collect OWNER TO suwell;

--
-- TOC entry 5956 (class 0 OID 0)
-- Dependencies: 326
-- Name: TABLE sys_search_collect; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_search_collect IS '搜索收藏表';


--
-- TOC entry 5957 (class 0 OID 0)
-- Dependencies: 326
-- Name: COLUMN sys_search_collect.name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_collect.name IS '搜索助记';


--
-- TOC entry 5958 (class 0 OID 0)
-- Dependencies: 326
-- Name: COLUMN sys_search_collect.search_path; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_collect.search_path IS '搜索路径';


--
-- TOC entry 5959 (class 0 OID 0)
-- Dependencies: 326
-- Name: COLUMN sys_search_collect.search_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_collect.search_json IS '搜索条件';


--
-- TOC entry 5960 (class 0 OID 0)
-- Dependencies: 326
-- Name: COLUMN sys_search_collect.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_collect.create_by IS '创建人';


--
-- TOC entry 5961 (class 0 OID 0)
-- Dependencies: 326
-- Name: COLUMN sys_search_collect.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_collect.create_time IS '创建时间';


--
-- TOC entry 325 (class 1259 OID 31336)
-- Name: sys_search_history; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_search_history (
    id bigint NOT NULL,
    search_path text,
    search_json text,
    create_by bigint,
    create_time timestamp(6) without time zone
);


ALTER TABLE "plss-system".sys_search_history OWNER TO suwell;

--
-- TOC entry 5962 (class 0 OID 0)
-- Dependencies: 325
-- Name: TABLE sys_search_history; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_search_history IS '搜索历史表';


--
-- TOC entry 5963 (class 0 OID 0)
-- Dependencies: 325
-- Name: COLUMN sys_search_history.search_path; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_history.search_path IS '检索路径';


--
-- TOC entry 5964 (class 0 OID 0)
-- Dependencies: 325
-- Name: COLUMN sys_search_history.search_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_history.search_json IS '搜索参数';


--
-- TOC entry 5965 (class 0 OID 0)
-- Dependencies: 325
-- Name: COLUMN sys_search_history.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_history.create_by IS '创建人';


--
-- TOC entry 5966 (class 0 OID 0)
-- Dependencies: 325
-- Name: COLUMN sys_search_history.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_search_history.create_time IS '创建时间';


--
-- TOC entry 393 (class 1259 OID 173199)
-- Name: sys_search_phrases; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_search_phrases (
    id bigint NOT NULL,
    phrases character varying NOT NULL,
    frequency integer,
    create_time date
);


ALTER TABLE "plss-system".sys_search_phrases OWNER TO suwell;

--
-- TOC entry 300 (class 1259 OID 27141)
-- Name: sys_source; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_source (
    id bigint NOT NULL,
    name character varying(128) NOT NULL,
    token character varying(128) NOT NULL,
    remark character varying(128),
    config character varying(128),
    create_time timestamp without time zone NOT NULL,
    create_by bigint NOT NULL,
    modified_time timestamp without time zone NOT NULL,
    modified_by bigint NOT NULL,
    secret_key character varying(128)
);


ALTER TABLE "plss-system".sys_source OWNER TO suwell;

--
-- TOC entry 5967 (class 0 OID 0)
-- Dependencies: 300
-- Name: TABLE sys_source; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_source IS '数据同步来源';


--
-- TOC entry 5968 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.id IS '唯一标识';


--
-- TOC entry 5969 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.name IS '名称';


--
-- TOC entry 5970 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.token; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.token IS '来源身份';


--
-- TOC entry 5971 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.remark IS '备注';


--
-- TOC entry 5972 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.config; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.config IS '其他配置';


--
-- TOC entry 5973 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.create_time IS '创建时间';


--
-- TOC entry 5974 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.create_by IS '创建人';


--
-- TOC entry 5975 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.modified_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.modified_time IS '修改时间';


--
-- TOC entry 5976 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.modified_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.modified_by IS '修改人';


--
-- TOC entry 5977 (class 0 OID 0)
-- Dependencies: 300
-- Name: COLUMN sys_source.secret_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_source.secret_key IS '密钥';


--
-- TOC entry 322 (class 1259 OID 31230)
-- Name: sys_table_header; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_table_header (
    id bigint NOT NULL,
    config_id bigint NOT NULL,
    field_type smallint DEFAULT 1 NOT NULL,
    field_key character varying(50) DEFAULT ''::character varying NOT NULL,
    field_name character varying(50) DEFAULT ''::character varying NOT NULL,
    field_show_name character varying(50) DEFAULT ''::character varying,
    metadata_category_id bigint DEFAULT 0 NOT NULL,
    metadata_category_name character varying(512) DEFAULT ''::character varying,
    order_by smallint NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-system".sys_table_header OWNER TO suwell;

--
-- TOC entry 5978 (class 0 OID 0)
-- Dependencies: 322
-- Name: TABLE sys_table_header; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_table_header IS '表头展示表';


--
-- TOC entry 5979 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.config_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.config_id IS '配置id';


--
-- TOC entry 5980 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.field_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.field_type IS '字段类型（1，业务字段2，元数据）';


--
-- TOC entry 5981 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.field_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.field_key IS '字段key(类型1存字段英文名，类型2存元数据id)';


--
-- TOC entry 5982 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.field_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.field_name IS '字段名称';


--
-- TOC entry 5983 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.field_show_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.field_show_name IS '字段展示名称';


--
-- TOC entry 5984 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.metadata_category_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.metadata_category_id IS '元数据分类id';


--
-- TOC entry 5985 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.metadata_category_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.metadata_category_name IS '元数据分类名称';


--
-- TOC entry 5986 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.order_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.order_by IS '排序字段';


--
-- TOC entry 5987 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.status IS '状态（0禁用1启用）';


--
-- TOC entry 5988 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.del_flag IS '删除状态';


--
-- TOC entry 5989 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.create_by IS '创建人';


--
-- TOC entry 5990 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.create_time IS '创建时间';


--
-- TOC entry 5991 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.update_by IS '修改人';


--
-- TOC entry 5992 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.update_time IS '修改时间';


--
-- TOC entry 5993 (class 0 OID 0)
-- Dependencies: 322
-- Name: COLUMN sys_table_header.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header.remark IS '备注';


--
-- TOC entry 324 (class 1259 OID 31316)
-- Name: sys_table_header_config; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_table_header_config (
    id bigint NOT NULL,
    function_module character varying(50) DEFAULT ''::character varying NOT NULL,
    table_type character varying(50) DEFAULT ''::character varying NOT NULL,
    table_names character varying(50) DEFAULT ''::character varying NOT NULL,
    field_count integer DEFAULT 0 NOT NULL,
    config_type smallint DEFAULT 1 NOT NULL,
    repo_id bigint DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    code character varying(50)
);


ALTER TABLE "plss-system".sys_table_header_config OWNER TO suwell;

--
-- TOC entry 5994 (class 0 OID 0)
-- Dependencies: 324
-- Name: TABLE sys_table_header_config; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_table_header_config IS '表头配置表';


--
-- TOC entry 5995 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.function_module; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.function_module IS '功能模块';


--
-- TOC entry 5996 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.table_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.table_type IS '表格类型';


--
-- TOC entry 5997 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.table_names; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.table_names IS '表名称多个用,分开';


--
-- TOC entry 5998 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.field_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.field_count IS '字段数量';


--
-- TOC entry 5999 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.config_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.config_type IS '配置类型（0全局配置1库配置）';


--
-- TOC entry 6000 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.repo_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.repo_id IS '库id';


--
-- TOC entry 6001 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.status IS '状态';


--
-- TOC entry 6002 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.del_flag IS '删除状态';


--
-- TOC entry 6003 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.create_by IS '创建人';


--
-- TOC entry 6004 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.create_time IS '创建时间';


--
-- TOC entry 6005 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.update_by IS '修改人';


--
-- TOC entry 6006 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.update_time IS '修改时间';


--
-- TOC entry 6007 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.remark IS '备注';


--
-- TOC entry 6008 (class 0 OID 0)
-- Dependencies: 324
-- Name: COLUMN sys_table_header_config.code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_table_header_config.code IS '唯一标识';


--
-- TOC entry 304 (class 1259 OID 27676)
-- Name: sys_tenant; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_tenant (
    id bigint NOT NULL,
    name character varying(64) NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying NOT NULL,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying NOT NULL,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    del_flag smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-system".sys_tenant OWNER TO suwell;

--
-- TOC entry 6009 (class 0 OID 0)
-- Dependencies: 304
-- Name: TABLE sys_tenant; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_tenant IS '租户表';


--
-- TOC entry 6010 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.id IS '主键';


--
-- TOC entry 6011 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.name IS '租户名称';


--
-- TOC entry 6012 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.status IS '租户状态：1启用；2禁用';


--
-- TOC entry 6013 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.create_by IS '创建者';


--
-- TOC entry 6014 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.create_time IS '创建时间';


--
-- TOC entry 6015 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.update_by IS '更新者';


--
-- TOC entry 6016 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.update_time IS '更新时间';


--
-- TOC entry 6017 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.remark IS '备注';


--
-- TOC entry 6018 (class 0 OID 0)
-- Dependencies: 304
-- Name: COLUMN sys_tenant.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant.del_flag IS '删除标志：1未删除，2已删除';


--
-- TOC entry 305 (class 1259 OID 27689)
-- Name: sys_tenant_menu; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_tenant_menu (
    tenant_id bigint NOT NULL,
    menu_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_tenant_menu OWNER TO suwell;

--
-- TOC entry 6019 (class 0 OID 0)
-- Dependencies: 305
-- Name: TABLE sys_tenant_menu; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_tenant_menu IS '租户-菜单表';


--
-- TOC entry 6020 (class 0 OID 0)
-- Dependencies: 305
-- Name: COLUMN sys_tenant_menu.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant_menu.tenant_id IS '租户id';


--
-- TOC entry 6021 (class 0 OID 0)
-- Dependencies: 305
-- Name: COLUMN sys_tenant_menu.menu_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_tenant_menu.menu_id IS '菜单id';


--
-- TOC entry 238 (class 1259 OID 24844)
-- Name: sys_user; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_user (
    user_id bigint NOT NULL,
    user_name character varying(64) NOT NULL,
    nick_name character varying(64) DEFAULT ''::character varying NOT NULL,
    user_type character varying(4) DEFAULT '00'::character varying,
    email character varying(64) DEFAULT ''::character varying,
    phonenumber character varying(256) DEFAULT ''::character varying,
    sex character(1) DEFAULT 1 NOT NULL,
    avatar character varying(128) DEFAULT ''::character varying,
    password character varying(128) DEFAULT ''::character varying NOT NULL,
    status character(1) DEFAULT 1 NOT NULL,
    del_flag character(1) DEFAULT 1 NOT NULL,
    login_ip character varying(128) DEFAULT ''::character varying,
    login_date timestamp without time zone DEFAULT now() NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone DEFAULT now() NOT NULL,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone DEFAULT now() NOT NULL,
    remark character varying(512) DEFAULT ''::character varying,
    org_id bigint DEFAULT 0,
    pwd_update_time timestamp without time zone DEFAULT now() NOT NULL,
    src_id character varying(128),
    access_key character varying(128),
    order_num character varying(64) DEFAULT 0,
    wps_auth character varying(1) DEFAULT 1,
    validity_period timestamp(6) without time zone
);


ALTER TABLE "plss-system".sys_user OWNER TO suwell;

--
-- TOC entry 6022 (class 0 OID 0)
-- Dependencies: 238
-- Name: TABLE sys_user; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_user IS '用户表';


--
-- TOC entry 6023 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.user_id IS '用户ID';


--
-- TOC entry 6024 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.user_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.user_name IS '用户账号';


--
-- TOC entry 6025 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.nick_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.nick_name IS '用户昵称';


--
-- TOC entry 6026 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.user_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.user_type IS '用户类型（00系统用户）';


--
-- TOC entry 6027 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.email; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.email IS '用户邮箱';


--
-- TOC entry 6028 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.phonenumber; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.phonenumber IS '手机号码';


--
-- TOC entry 6029 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.sex; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.sex IS '用户性别（1男 2女 3未知）';


--
-- TOC entry 6030 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.avatar; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.avatar IS '头像地址';


--
-- TOC entry 6031 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.password; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.password IS '密码';


--
-- TOC entry 6032 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.status IS '帐号状态（ 1：正常，2：停用）';


--
-- TOC entry 6033 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.del_flag IS '删除标志（1：代表存在，2：代表删除）';


--
-- TOC entry 6034 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.login_ip; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.login_ip IS '最后登录IP';


--
-- TOC entry 6035 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.login_date; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.login_date IS '最后登录时间';


--
-- TOC entry 6036 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.create_by IS '创建者';


--
-- TOC entry 6037 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.create_time IS '创建时间';


--
-- TOC entry 6038 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.update_by IS '更新者';


--
-- TOC entry 6039 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.update_time IS '更新时间';


--
-- TOC entry 6040 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.remark IS '备注';


--
-- TOC entry 6041 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.org_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.org_id IS '机构ID';


--
-- TOC entry 6042 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.pwd_update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.pwd_update_time IS '密码修改时间';


--
-- TOC entry 6043 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.src_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.src_id IS '来源id';


--
-- TOC entry 6044 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.access_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.access_key IS '来源系统唯一标识';


--
-- TOC entry 6045 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.order_num; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.order_num IS '排序字段';


--
-- TOC entry 6046 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.wps_auth; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.wps_auth IS '是否授权wps端,1：已授权，2：未授权';


--
-- TOC entry 6047 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN sys_user.validity_period; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user.validity_period IS '用户有效期';


--
-- TOC entry 382 (class 1259 OID 104312)
-- Name: sys_user_org; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_user_org (
    org_id bigint NOT NULL,
    user_id bigint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    order_by smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-system".sys_user_org OWNER TO suwell;

--
-- TOC entry 6048 (class 0 OID 0)
-- Dependencies: 382
-- Name: TABLE sys_user_org; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_user_org IS '用户-机构关联表';


--
-- TOC entry 6049 (class 0 OID 0)
-- Dependencies: 382
-- Name: COLUMN sys_user_org.org_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_org.org_id IS '组织机构id';


--
-- TOC entry 6050 (class 0 OID 0)
-- Dependencies: 382
-- Name: COLUMN sys_user_org.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_org.user_id IS '用户id';


--
-- TOC entry 6051 (class 0 OID 0)
-- Dependencies: 382
-- Name: COLUMN sys_user_org.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_org.tenant_id IS '租户id';


--
-- TOC entry 6052 (class 0 OID 0)
-- Dependencies: 382
-- Name: COLUMN sys_user_org.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_org.status IS '状态';


--
-- TOC entry 6053 (class 0 OID 0)
-- Dependencies: 382
-- Name: COLUMN sys_user_org.order_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_org.order_by IS '排序';


--
-- TOC entry 239 (class 1259 OID 24869)
-- Name: sys_user_post; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_user_post (
    user_id bigint NOT NULL,
    post_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_user_post OWNER TO suwell;

--
-- TOC entry 6054 (class 0 OID 0)
-- Dependencies: 239
-- Name: TABLE sys_user_post; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_user_post IS '用户与岗位关联表';


--
-- TOC entry 6055 (class 0 OID 0)
-- Dependencies: 239
-- Name: COLUMN sys_user_post.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_post.user_id IS '用户ID';


--
-- TOC entry 6056 (class 0 OID 0)
-- Dependencies: 239
-- Name: COLUMN sys_user_post.post_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_post.post_id IS '岗位ID';


--
-- TOC entry 240 (class 1259 OID 24872)
-- Name: sys_user_role; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_user_role (
    role_id bigint NOT NULL,
    user_id bigint NOT NULL,
    tenant_id bigint DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL
);


ALTER TABLE "plss-system".sys_user_role OWNER TO suwell;

--
-- TOC entry 6057 (class 0 OID 0)
-- Dependencies: 240
-- Name: TABLE sys_user_role; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_user_role IS '用户-角色关联表';


--
-- TOC entry 6058 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN sys_user_role.role_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_role.role_id IS '角色id';


--
-- TOC entry 6059 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN sys_user_role.user_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_role.user_id IS '用户id';


--
-- TOC entry 6060 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN sys_user_role.tenant_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_role.tenant_id IS '租户id';


--
-- TOC entry 6061 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN sys_user_role.status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_user_role.status IS '状态';


--
-- TOC entry 317 (class 1259 OID 30172)
-- Name: sys_watermark_setting; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".sys_watermark_setting (
    id bigint NOT NULL,
    watermark_json character varying(255),
    watermark_type character varying(255),
    example_url character varying(255),
    value character varying(255),
    enable_status character(1),
    del_flag character(1),
    create_by character varying(255),
    create_time timestamp without time zone,
    update_by character varying(255),
    update_time timestamp without time zone,
    remark character varying(255),
    repo_id bigint NOT NULL
);


ALTER TABLE "plss-system".sys_watermark_setting OWNER TO suwell;

--
-- TOC entry 6062 (class 0 OID 0)
-- Dependencies: 317
-- Name: TABLE sys_watermark_setting; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".sys_watermark_setting IS '水印设置表';


--
-- TOC entry 6063 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.watermark_json; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.watermark_json IS '水印json字符串';


--
-- TOC entry 6064 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.watermark_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.watermark_type IS '水印类型:0-自定义水印1-用户id 2-用户名称3-用户ip，支持多选';


--
-- TOC entry 6065 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.example_url; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.example_url IS '水印样例文件';


--
-- TOC entry 6066 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.value; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.value IS '水印内容';


--
-- TOC entry 6067 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.enable_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.enable_status IS '开关, 0-关 1-开';


--
-- TOC entry 6068 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.del_flag; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.del_flag IS '删除标识';


--
-- TOC entry 6069 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.create_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.create_by IS '创建人';


--
-- TOC entry 6070 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.create_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.create_time IS '创建时间';


--
-- TOC entry 6071 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.update_by; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.update_by IS '更新人';


--
-- TOC entry 6072 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.update_time IS '更新时间';


--
-- TOC entry 6073 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.remark IS '备注';


--
-- TOC entry 6074 (class 0 OID 0)
-- Dependencies: 317
-- Name: COLUMN sys_watermark_setting.repo_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".sys_watermark_setting.repo_id IS '库id，全局水印该字段为0';


--
-- TOC entry 296 (class 1259 OID 27107)
-- Name: xxl_job_group; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_group (
    id integer NOT NULL,
    app_name character varying(200) NOT NULL,
    title character varying(200) NOT NULL,
    address_type integer DEFAULT 0 NOT NULL,
    address_list character varying(512) DEFAULT NULL::character varying,
    update_time timestamp without time zone
);


ALTER TABLE "plss-system".xxl_job_group OWNER TO suwell;

--
-- TOC entry 6075 (class 0 OID 0)
-- Dependencies: 296
-- Name: TABLE xxl_job_group; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_group IS '任务分组表';


--
-- TOC entry 6076 (class 0 OID 0)
-- Dependencies: 296
-- Name: COLUMN xxl_job_group.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_group.id IS '主键';


--
-- TOC entry 6077 (class 0 OID 0)
-- Dependencies: 296
-- Name: COLUMN xxl_job_group.app_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_group.app_name IS '执行器AppName';


--
-- TOC entry 6078 (class 0 OID 0)
-- Dependencies: 296
-- Name: COLUMN xxl_job_group.title; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_group.title IS '执行器名称';


--
-- TOC entry 6079 (class 0 OID 0)
-- Dependencies: 296
-- Name: COLUMN xxl_job_group.address_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_group.address_type IS '执行器地址类型：0=自动注册、1=手动录入';


--
-- TOC entry 6080 (class 0 OID 0)
-- Dependencies: 296
-- Name: COLUMN xxl_job_group.address_list; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_group.address_list IS '执行器地址列表，多地址逗号分隔';


--
-- TOC entry 295 (class 1259 OID 27106)
-- Name: xxl_job_group_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_group_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_group_id_seq OWNER TO suwell;

--
-- TOC entry 6081 (class 0 OID 0)
-- Dependencies: 295
-- Name: xxl_job_group_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_group_id_seq OWNED BY "plss-system".xxl_job_group.id;


--
-- TOC entry 362 (class 1259 OID 33323)
-- Name: xxl_job_group_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_group ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_group_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 286 (class 1259 OID 27031)
-- Name: xxl_job_info; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_info (
    id integer NOT NULL,
    job_group integer NOT NULL,
    job_desc character varying(255) NOT NULL,
    add_time timestamp without time zone,
    update_time timestamp without time zone,
    author character varying(64) DEFAULT NULL::character varying,
    alarm_email character varying(255) DEFAULT NULL::character varying,
    schedule_type character varying(50) DEFAULT 'NONE'::character varying NOT NULL,
    schedule_conf character varying(128) DEFAULT NULL::character varying,
    misfire_strategy character varying(50) DEFAULT 'DO_NOTHING'::character varying NOT NULL,
    executor_route_strategy character varying(50) DEFAULT NULL::character varying,
    executor_handler character varying(255) DEFAULT NULL::character varying,
    executor_param character varying(512) DEFAULT NULL::character varying,
    executor_block_strategy character varying(50) DEFAULT NULL::character varying,
    executor_timeout integer DEFAULT 0 NOT NULL,
    executor_fail_retry_count integer DEFAULT 0 NOT NULL,
    glue_type character varying(50) NOT NULL,
    glue_source text,
    glue_remark character varying(128) DEFAULT NULL::character varying,
    glue_updatetime timestamp without time zone,
    child_job_id character varying(255) DEFAULT NULL::character varying,
    trigger_status integer DEFAULT 0 NOT NULL,
    trigger_last_time bigint DEFAULT '0'::bigint NOT NULL,
    trigger_next_time bigint DEFAULT '0'::bigint NOT NULL
);


ALTER TABLE "plss-system".xxl_job_info OWNER TO suwell;

--
-- TOC entry 6082 (class 0 OID 0)
-- Dependencies: 286
-- Name: TABLE xxl_job_info; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_info IS '任务信息表';


--
-- TOC entry 6083 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.id IS '主键';


--
-- TOC entry 6084 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.job_group; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.job_group IS '执行器主键ID';


--
-- TOC entry 6085 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.job_desc; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.job_desc IS '任务描述';


--
-- TOC entry 6086 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.add_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.add_time IS '任务创建时间';


--
-- TOC entry 6087 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.update_time IS '任务更新时间';


--
-- TOC entry 6088 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.author; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.author IS '作者';


--
-- TOC entry 6089 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.alarm_email; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.alarm_email IS '报警邮件';


--
-- TOC entry 6090 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.schedule_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.schedule_type IS '调度类型';


--
-- TOC entry 6091 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.schedule_conf; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.schedule_conf IS '调度配置，值含义取决于调度类型';


--
-- TOC entry 6092 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.misfire_strategy; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.misfire_strategy IS '调度过期策略';


--
-- TOC entry 6093 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_route_strategy; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_route_strategy IS '执行器路由策略';


--
-- TOC entry 6094 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_handler; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_handler IS '执行器任务handler';


--
-- TOC entry 6095 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_param; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_param IS '执行器任务参数';


--
-- TOC entry 6096 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_block_strategy; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_block_strategy IS '阻塞处理策略';


--
-- TOC entry 6097 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_timeout; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_timeout IS '任务执行超时时间，单位秒';


--
-- TOC entry 6098 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.executor_fail_retry_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.executor_fail_retry_count IS '失败重试次数';


--
-- TOC entry 6099 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.glue_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.glue_type IS 'GLUE类型';


--
-- TOC entry 6100 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.glue_source; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.glue_source IS 'GLUE源代码';


--
-- TOC entry 6101 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.glue_remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.glue_remark IS 'GLUE备注';


--
-- TOC entry 6102 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.glue_updatetime; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.glue_updatetime IS 'GLUE更新时间';


--
-- TOC entry 6103 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.child_job_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.child_job_id IS '子任务ID，多个逗号分隔';


--
-- TOC entry 6104 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.trigger_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.trigger_status IS '调度状态：0-停止，1-运行';


--
-- TOC entry 6105 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.trigger_last_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.trigger_last_time IS '上次调度时间';


--
-- TOC entry 6106 (class 0 OID 0)
-- Dependencies: 286
-- Name: COLUMN xxl_job_info.trigger_next_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_info.trigger_next_time IS '下次调度时间';


--
-- TOC entry 285 (class 1259 OID 27030)
-- Name: xxl_job_info_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_info_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_info_id_seq OWNER TO suwell;

--
-- TOC entry 6107 (class 0 OID 0)
-- Dependencies: 285
-- Name: xxl_job_info_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_info_id_seq OWNED BY "plss-system".xxl_job_info.id;


--
-- TOC entry 363 (class 1259 OID 33324)
-- Name: xxl_job_info_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_info ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_info_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 299 (class 1259 OID 27126)
-- Name: xxl_job_lock; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_lock (
    lock_name character varying(50) NOT NULL
);


ALTER TABLE "plss-system".xxl_job_lock OWNER TO suwell;

--
-- TOC entry 6108 (class 0 OID 0)
-- Dependencies: 299
-- Name: TABLE xxl_job_lock; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_lock IS '任务锁表';


--
-- TOC entry 6109 (class 0 OID 0)
-- Dependencies: 299
-- Name: COLUMN xxl_job_lock.lock_name; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_lock.lock_name IS '锁名称';


--
-- TOC entry 288 (class 1259 OID 27056)
-- Name: xxl_job_log; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_log (
    id integer NOT NULL,
    job_group integer NOT NULL,
    job_id integer NOT NULL,
    executor_address character varying(255) DEFAULT NULL::character varying,
    executor_handler character varying(255) DEFAULT NULL::character varying,
    executor_param character varying(512) DEFAULT NULL::character varying,
    executor_sharding_param character varying(20) DEFAULT NULL::character varying,
    executor_fail_retry_count integer DEFAULT 0 NOT NULL,
    trigger_time timestamp without time zone,
    trigger_code integer DEFAULT 0 NOT NULL,
    trigger_msg text,
    handle_time timestamp without time zone,
    handle_code integer NOT NULL,
    handle_msg text,
    alarm_status integer DEFAULT 0 NOT NULL
);


ALTER TABLE "plss-system".xxl_job_log OWNER TO suwell;

--
-- TOC entry 6110 (class 0 OID 0)
-- Dependencies: 288
-- Name: TABLE xxl_job_log; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_log IS '任务日志表';


--
-- TOC entry 6111 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.id IS '主键';


--
-- TOC entry 6112 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.job_group; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.job_group IS '执行器主键ID';


--
-- TOC entry 6113 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.job_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.job_id IS '任务，主键ID';


--
-- TOC entry 6114 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.executor_address; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.executor_address IS '执行器地址，本次执行的地址';


--
-- TOC entry 6115 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.executor_handler; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.executor_handler IS '执行器任务handler';


--
-- TOC entry 6116 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.executor_param; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.executor_param IS '执行器任务参数';


--
-- TOC entry 6117 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.executor_sharding_param; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.executor_sharding_param IS '执行器任务分片参数，格式如 1/2';


--
-- TOC entry 6118 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.executor_fail_retry_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.executor_fail_retry_count IS '失败重试次数';


--
-- TOC entry 6119 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.trigger_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.trigger_time IS '调度-时间';


--
-- TOC entry 6120 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.trigger_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.trigger_code IS '调度-结果';


--
-- TOC entry 6121 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.trigger_msg; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.trigger_msg IS '调度-日志';


--
-- TOC entry 6122 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.handle_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.handle_time IS '执行-时间';


--
-- TOC entry 6123 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.handle_code; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.handle_code IS '执行-状态';


--
-- TOC entry 6124 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.handle_msg; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.handle_msg IS '执行-日志';


--
-- TOC entry 6125 (class 0 OID 0)
-- Dependencies: 288
-- Name: COLUMN xxl_job_log.alarm_status; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log.alarm_status IS '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败';


--
-- TOC entry 287 (class 1259 OID 27055)
-- Name: xxl_job_log_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_log_id_seq OWNER TO suwell;

--
-- TOC entry 6126 (class 0 OID 0)
-- Dependencies: 287
-- Name: xxl_job_log_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_log_id_seq OWNED BY "plss-system".xxl_job_log.id;


--
-- TOC entry 364 (class 1259 OID 33325)
-- Name: xxl_job_log_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_log ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_log_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 292 (class 1259 OID 27085)
-- Name: xxl_job_log_report; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_log_report (
    id integer NOT NULL,
    trigger_day timestamp without time zone,
    running_count integer DEFAULT 0 NOT NULL,
    suc_count integer DEFAULT 0 NOT NULL,
    fail_count integer DEFAULT 0 NOT NULL,
    update_time timestamp without time zone
);


ALTER TABLE "plss-system".xxl_job_log_report OWNER TO suwell;

--
-- TOC entry 6127 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.id IS '主键';


--
-- TOC entry 6128 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.trigger_day; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.trigger_day IS '调度-时间';


--
-- TOC entry 6129 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.running_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.running_count IS '运行中-日志数量';


--
-- TOC entry 6130 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.suc_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.suc_count IS '执行成功-日志数量';


--
-- TOC entry 6131 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.fail_count; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.fail_count IS '执行失败-日志数量';


--
-- TOC entry 6132 (class 0 OID 0)
-- Dependencies: 292
-- Name: COLUMN xxl_job_log_report.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_log_report.update_time IS '更新时间';


--
-- TOC entry 291 (class 1259 OID 27084)
-- Name: xxl_job_log_report_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_log_report_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_log_report_id_seq OWNER TO suwell;

--
-- TOC entry 6133 (class 0 OID 0)
-- Dependencies: 291
-- Name: xxl_job_log_report_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_log_report_id_seq OWNED BY "plss-system".xxl_job_log_report.id;


--
-- TOC entry 365 (class 1259 OID 33327)
-- Name: xxl_job_log_report_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_log_report ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_log_report_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 290 (class 1259 OID 27074)
-- Name: xxl_job_logglue; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_logglue (
    id integer NOT NULL,
    job_id integer NOT NULL,
    glue_type character varying(50) DEFAULT NULL::character varying,
    glue_source text,
    glue_remark character varying(128) NOT NULL,
    add_time timestamp without time zone,
    update_time timestamp without time zone
);


ALTER TABLE "plss-system".xxl_job_logglue OWNER TO suwell;

--
-- TOC entry 6134 (class 0 OID 0)
-- Dependencies: 290
-- Name: TABLE xxl_job_logglue; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_logglue IS '任务GLUE日志表';


--
-- TOC entry 6135 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.id IS '主键';


--
-- TOC entry 6136 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.job_id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.job_id IS '任务，主键ID';


--
-- TOC entry 6137 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.glue_type; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.glue_type IS 'GLUE类型';


--
-- TOC entry 6138 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.glue_source; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.glue_source IS 'GLUE源代码';


--
-- TOC entry 6139 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.glue_remark; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.glue_remark IS 'GLUE备注';


--
-- TOC entry 6140 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.add_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.add_time IS '创建时间';


--
-- TOC entry 6141 (class 0 OID 0)
-- Dependencies: 290
-- Name: COLUMN xxl_job_logglue.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_logglue.update_time IS '修改时间';


--
-- TOC entry 289 (class 1259 OID 27073)
-- Name: xxl_job_logglue_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_logglue_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_logglue_id_seq OWNER TO suwell;

--
-- TOC entry 6142 (class 0 OID 0)
-- Dependencies: 289
-- Name: xxl_job_logglue_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_logglue_id_seq OWNED BY "plss-system".xxl_job_logglue.id;


--
-- TOC entry 366 (class 1259 OID 33328)
-- Name: xxl_job_logglue_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_logglue ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_logglue_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 294 (class 1259 OID 27095)
-- Name: xxl_job_registry; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_registry (
    id integer NOT NULL,
    registry_group character varying(255) NOT NULL,
    registry_key character varying(255) NOT NULL,
    registry_value character varying(255) NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE "plss-system".xxl_job_registry OWNER TO suwell;

--
-- TOC entry 6143 (class 0 OID 0)
-- Dependencies: 294
-- Name: TABLE xxl_job_registry; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_registry IS '任务注册表';


--
-- TOC entry 6144 (class 0 OID 0)
-- Dependencies: 294
-- Name: COLUMN xxl_job_registry.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_registry.id IS '主键';


--
-- TOC entry 6145 (class 0 OID 0)
-- Dependencies: 294
-- Name: COLUMN xxl_job_registry.registry_group; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_registry.registry_group IS '注册分组';


--
-- TOC entry 6146 (class 0 OID 0)
-- Dependencies: 294
-- Name: COLUMN xxl_job_registry.registry_key; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_registry.registry_key IS '注册键';


--
-- TOC entry 6147 (class 0 OID 0)
-- Dependencies: 294
-- Name: COLUMN xxl_job_registry.registry_value; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_registry.registry_value IS '注册值';


--
-- TOC entry 6148 (class 0 OID 0)
-- Dependencies: 294
-- Name: COLUMN xxl_job_registry.update_time; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_registry.update_time IS '更新时间';


--
-- TOC entry 293 (class 1259 OID 27094)
-- Name: xxl_job_registry_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_registry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_registry_id_seq OWNER TO suwell;

--
-- TOC entry 6149 (class 0 OID 0)
-- Dependencies: 293
-- Name: xxl_job_registry_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_registry_id_seq OWNED BY "plss-system".xxl_job_registry.id;


--
-- TOC entry 367 (class 1259 OID 33329)
-- Name: xxl_job_registry_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_registry ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_registry_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 298 (class 1259 OID 27118)
-- Name: xxl_job_user; Type: TABLE; Schema: plss-system; Owner: suwell
--

CREATE TABLE "plss-system".xxl_job_user (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(50) NOT NULL,
    role integer NOT NULL,
    permission character varying(255) DEFAULT NULL::character varying
);


ALTER TABLE "plss-system".xxl_job_user OWNER TO suwell;

--
-- TOC entry 6150 (class 0 OID 0)
-- Dependencies: 298
-- Name: TABLE xxl_job_user; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON TABLE "plss-system".xxl_job_user IS '任务用户表';


--
-- TOC entry 6151 (class 0 OID 0)
-- Dependencies: 298
-- Name: COLUMN xxl_job_user.id; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_user.id IS '主键';


--
-- TOC entry 6152 (class 0 OID 0)
-- Dependencies: 298
-- Name: COLUMN xxl_job_user.username; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_user.username IS '账号';


--
-- TOC entry 6153 (class 0 OID 0)
-- Dependencies: 298
-- Name: COLUMN xxl_job_user.password; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_user.password IS '密码';


--
-- TOC entry 6154 (class 0 OID 0)
-- Dependencies: 298
-- Name: COLUMN xxl_job_user.role; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_user.role IS '角色：0-普通用户、1-管理员';


--
-- TOC entry 6155 (class 0 OID 0)
-- Dependencies: 298
-- Name: COLUMN xxl_job_user.permission; Type: COMMENT; Schema: plss-system; Owner: suwell
--

COMMENT ON COLUMN "plss-system".xxl_job_user.permission IS '权限：执行器ID列表，多个逗号分割';


--
-- TOC entry 297 (class 1259 OID 27117)
-- Name: xxl_job_user_id_seq; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

CREATE SEQUENCE "plss-system".xxl_job_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "plss-system".xxl_job_user_id_seq OWNER TO suwell;

--
-- TOC entry 6156 (class 0 OID 0)
-- Dependencies: 297
-- Name: xxl_job_user_id_seq; Type: SEQUENCE OWNED BY; Schema: plss-system; Owner: suwell
--

ALTER SEQUENCE "plss-system".xxl_job_user_id_seq OWNED BY "plss-system".xxl_job_user.id;


--
-- TOC entry 368 (class 1259 OID 33330)
-- Name: xxl_job_user_id_seq1; Type: SEQUENCE; Schema: plss-system; Owner: suwell
--

ALTER TABLE "plss-system".xxl_job_user ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "plss-system".xxl_job_user_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- TOC entry 4634 (class 2606 OID 188016)
-- Name: op_abutment_api_settings op_abutment_api_settings_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_api_settings
    ADD CONSTRAINT op_abutment_api_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4636 (class 2606 OID 188023)
-- Name: op_abutment_manufacturer op_abutment_manufacturer_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_manufacturer
    ADD CONSTRAINT op_abutment_manufacturer_pkey PRIMARY KEY (id);


--
-- TOC entry 4638 (class 2606 OID 188030)
-- Name: op_abutment_plan op_abutment_plan_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_plan
    ADD CONSTRAINT op_abutment_plan_pkey PRIMARY KEY (id);


--
-- TOC entry 4517 (class 2606 OID 31673)
-- Name: op_app_info op_app_info_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_app_info
    ADD CONSTRAINT op_app_info_pkey PRIMARY KEY (id);


--
-- TOC entry 4519 (class 2606 OID 31699)
-- Name: op_app_ram op_app_ram_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_app_ram
    ADD CONSTRAINT op_app_ram_pkey PRIMARY KEY (id);


--
-- TOC entry 4521 (class 2606 OID 31715)
-- Name: op_uniform_resource op_uniform_resource_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_uniform_resource
    ADD CONSTRAINT op_uniform_resource_pkey PRIMARY KEY (id);


--
-- TOC entry 4525 (class 2606 OID 31745)
-- Name: op_ur_log op_ur_log_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_ur_log
    ADD CONSTRAINT op_ur_log_pkey PRIMARY KEY (id);


--
-- TOC entry 4523 (class 2606 OID 31731)
-- Name: op_ur_permission op_ur_permission_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_ur_permission
    ADD CONSTRAINT op_ur_permission_pkey PRIMARY KEY (id);


--
-- TOC entry 4457 (class 2606 OID 29957)
-- Name: med_patient_info MED_PATIENT_INFO_pkey; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".med_patient_info
    ADD CONSTRAINT "MED_PATIENT_INFO_pkey" PRIMARY KEY (id);


--
-- TOC entry 4578 (class 2606 OID 33239)
-- Name: ae_analysis_req ae_analysis_req_pk; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".ae_analysis_req
    ADD CONSTRAINT ae_analysis_req_pk PRIMARY KEY (id);


--
-- TOC entry 4580 (class 2606 OID 33249)
-- Name: ae_analysis_result ae_analysis_result_pk; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".ae_analysis_result
    ADD CONSTRAINT ae_analysis_result_pk PRIMARY KEY (id);


--
-- TOC entry 4582 (class 2606 OID 33274)
-- Name: ae_resource_publish ae_resource_publish_pk; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".ae_resource_publish
    ADD CONSTRAINT ae_resource_publish_pk PRIMARY KEY (id);


--
-- TOC entry 4453 (class 2606 OID 28302)
-- Name: cma_attachment cma_attachment_pkey; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".cma_attachment
    ADD CONSTRAINT cma_attachment_pkey PRIMARY KEY (id);


--
-- TOC entry 4451 (class 2606 OID 28295)
-- Name: cma_doc_metadata cma_doc_metadata_pkey; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".cma_doc_metadata
    ADD CONSTRAINT cma_doc_metadata_pkey PRIMARY KEY (id);


--
-- TOC entry 4449 (class 2606 OID 28288)
-- Name: cma_document cma_document_pkey; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".cma_document
    ADD CONSTRAINT cma_document_pkey PRIMARY KEY (id);


--
-- TOC entry 4447 (class 2606 OID 28281)
-- Name: cma_institutional_classification cma_institutional_classification_pkey; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".cma_institutional_classification
    ADD CONSTRAINT cma_institutional_classification_pkey PRIMARY KEY (id);


--
-- TOC entry 4570 (class 2606 OID 32861)
-- Name: web_office_file_record web_office_file_record_pk; Type: CONSTRAINT; Schema: plss-plugin; Owner: suwell
--

ALTER TABLE ONLY "plss-plugin".web_office_file_record
    ADD CONSTRAINT web_office_file_record_pk PRIMARY KEY (id);


--
-- TOC entry 4367 (class 2606 OID 33413)
-- Name: rc_metadata_value idx_rid_mid; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_metadata_value
    ADD CONSTRAINT idx_rid_mid UNIQUE (record_id, md_id);


--
-- TOC entry 6290 (class 0 OID 0)
-- Dependencies: 4367
-- Name: CONSTRAINT idx_rid_mid ON rc_metadata_value; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON CONSTRAINT idx_rid_mid ON "plss-record".rc_metadata_value IS '文件id与元数据id';


--
-- TOC entry 4588 (class 2606 OID 39781)
-- Name: rc_doc_audit rc_doc_audit_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_doc_audit
    ADD CONSTRAINT rc_doc_audit_pkey PRIMARY KEY (id);


--
-- TOC entry 4536 (class 2606 OID 31827)
-- Name: rc_doc_process rc_doc_process_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_doc_process
    ADD CONSTRAINT rc_doc_process_pkey PRIMARY KEY (id);


--
-- TOC entry 4341 (class 2606 OID 25394)
-- Name: rc_document_history rc_document_history_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_document_history
    ADD CONSTRAINT rc_document_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4353 (class 2606 OID 25561)
-- Name: rc_document rc_document_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_document
    ADD CONSTRAINT rc_document_pkey PRIMARY KEY (id);


--
-- TOC entry 4597 (class 2606 OID 52947)
-- Name: rc_export_filter rc_export_filter_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_export_filter
    ADD CONSTRAINT rc_export_filter_pkey PRIMARY KEY (id);


--
-- TOC entry 4595 (class 2606 OID 52938)
-- Name: rc_export_task rc_export_task_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_export_task
    ADD CONSTRAINT rc_export_task_pkey PRIMARY KEY (id);


--
-- TOC entry 4584 (class 2606 OID 33300)
-- Name: rc_file rc_file_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_file
    ADD CONSTRAINT rc_file_pkey PRIMARY KEY (id);


--
-- TOC entry 4299 (class 2606 OID 24882)
-- Name: rc_folder_record rc_fold_record_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_folder_record
    ADD CONSTRAINT rc_fold_record_pkey PRIMARY KEY (folder_id, record_id);


--
-- TOC entry 4301 (class 2606 OID 24884)
-- Name: rc_folder_rel rc_fold_rel_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_folder_rel
    ADD CONSTRAINT rc_fold_rel_pkey PRIMARY KEY (ancestor_id, descendant_id);


--
-- TOC entry 4349 (class 2606 OID 25425)
-- Name: rc_folder rc_folder_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_folder
    ADD CONSTRAINT rc_folder_pkey PRIMARY KEY (id);


--
-- TOC entry 4455 (class 2606 OID 28415)
-- Name: rc_resource_tenant_visible rc_folder_record_copy1_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_resource_tenant_visible
    ADD CONSTRAINT rc_folder_record_copy1_pkey PRIMARY KEY (resource_id, tenant_id);


--
-- TOC entry 4576 (class 2606 OID 33228)
-- Name: rc_masking_config rc_masking_config_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_masking_config
    ADD CONSTRAINT rc_masking_config_pkey PRIMARY KEY (id);


--
-- TOC entry 4568 (class 2606 OID 32827)
-- Name: rc_masking_metadata rc_masking_metadata_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_masking_metadata
    ADD CONSTRAINT rc_masking_metadata_pkey PRIMARY KEY (id);


--
-- TOC entry 4566 (class 2606 OID 32813)
-- Name: rc_masking_range rc_masking_range_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_masking_range
    ADD CONSTRAINT rc_masking_range_pkey PRIMARY KEY (id);


--
-- TOC entry 4574 (class 2606 OID 33208)
-- Name: rc_masking_word rc_masking_word_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_masking_word
    ADD CONSTRAINT rc_masking_word_pkey PRIMARY KEY (id);


--
-- TOC entry 4558 (class 2606 OID 32515)
-- Name: rc_material_category rc_material_category_pk; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_material_category
    ADD CONSTRAINT rc_material_category_pk PRIMARY KEY (id);


--
-- TOC entry 4554 (class 2606 OID 32503)
-- Name: rc_material_change rc_material_change_pk; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_material_change
    ADD CONSTRAINT rc_material_change_pk PRIMARY KEY (id);


--
-- TOC entry 4552 (class 2606 OID 32450)
-- Name: rc_material rc_material_pk; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_material
    ADD CONSTRAINT rc_material_pk PRIMARY KEY (id);


--
-- TOC entry 4556 (class 2606 OID 32510)
-- Name: rc_material_quote rc_material_quote_pk; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_material_quote
    ADD CONSTRAINT rc_material_quote_pk PRIMARY KEY (id);


--
-- TOC entry 4626 (class 2606 OID 171988)
-- Name: rc_metadata_category_metadata rc_metadata_category_metadata_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_metadata_category_metadata
    ADD CONSTRAINT rc_metadata_category_metadata_pkey PRIMARY KEY (category_id, md_id);


--
-- TOC entry 4624 (class 2606 OID 171982)
-- Name: rc_metadata_category rc_metadata_category_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_metadata_category
    ADD CONSTRAINT rc_metadata_category_pkey PRIMARY KEY (id);


--
-- TOC entry 4369 (class 2606 OID 26240)
-- Name: rc_metadata_value rc_metadata_keyvalue_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_metadata_value
    ADD CONSTRAINT rc_metadata_keyvalue_pkey PRIMARY KEY (id);


--
-- TOC entry 4622 (class 2606 OID 171977)
-- Name: rc_metadata rc_metadata_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_metadata
    ADD CONSTRAINT rc_metadata_pkey PRIMARY KEY (id);


--
-- TOC entry 4618 (class 2606 OID 171955)
-- Name: rc_node rc_node_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_node
    ADD CONSTRAINT rc_node_pkey PRIMARY KEY (id);


--
-- TOC entry 4355 (class 2606 OID 25571)
-- Name: rc_permission_group rc_permission_group_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_permission_group
    ADD CONSTRAINT rc_permission_group_pkey PRIMARY KEY (id);


--
-- TOC entry 4339 (class 2606 OID 25366)
-- Name: rc_permission rc_permission_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_permission
    ADD CONSTRAINT rc_permission_pkey PRIMARY KEY (id);


--
-- TOC entry 4610 (class 2606 OID 102861)
-- Name: rc_plan_front_range rc_plan_front_range_pk; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_plan_front_range
    ADD CONSTRAINT rc_plan_front_range_pk PRIMARY KEY (id);


--
-- TOC entry 4488 (class 2606 OID 31425)
-- Name: rc_plan rc_plan_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_plan
    ADD CONSTRAINT rc_plan_pkey PRIMARY KEY (id);


--
-- TOC entry 4439 (class 2606 OID 27187)
-- Name: rc_record_type_metadata rc_rdcate_metadata_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record_type_metadata
    ADD CONSTRAINT rc_rdcate_metadata_pkey PRIMARY KEY (recordtype_id, md_id);


--
-- TOC entry 4562 (class 2606 OID 32700)
-- Name: rc_record_borrow rc_record_borrow_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record_borrow
    ADD CONSTRAINT rc_record_borrow_pkey PRIMARY KEY (id);


--
-- TOC entry 4345 (class 2606 OID 25413)
-- Name: rc_record rc_record_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record
    ADD CONSTRAINT rc_record_pkey PRIMARY KEY (id);


--
-- TOC entry 4467 (class 2606 OID 28644)
-- Name: rc_record_process_detail rc_record_process_detail_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record_process_detail
    ADD CONSTRAINT rc_record_process_detail_pkey PRIMARY KEY (id);


--
-- TOC entry 4351 (class 2606 OID 30248)
-- Name: rc_record_rel rc_record_rel_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record_rel
    ADD CONSTRAINT rc_record_rel_pkey PRIMARY KEY (refer_id, record_id, ctype);


--
-- TOC entry 4436 (class 2606 OID 27182)
-- Name: rc_record_type rc_record_type_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_record_type
    ADD CONSTRAINT rc_record_type_pkey PRIMARY KEY (id);


--
-- TOC entry 4632 (class 2606 OID 187937)
-- Name: rc_reference_record rc_reference_record_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_reference_record
    ADD CONSTRAINT rc_reference_record_pkey PRIMARY KEY (id);


--
-- TOC entry 4480 (class 2606 OID 31315)
-- Name: rc_repo_metadata rc_repo_metadata_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_repo_metadata
    ADD CONSTRAINT rc_repo_metadata_pkey PRIMARY KEY (id);


--
-- TOC entry 4593 (class 2606 OID 52447)
-- Name: rc_repo_record rc_repo_record_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_repo_record
    ADD CONSTRAINT rc_repo_record_pkey PRIMARY KEY (repo_id, record_id);


--
-- TOC entry 4343 (class 2606 OID 25406)
-- Name: rc_repository rc_repository_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_repository
    ADD CONSTRAINT rc_repository_pkey PRIMARY KEY (id);


--
-- TOC entry 4492 (class 2606 OID 31440)
-- Name: rc_resource_manager rc_resource_manager_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_resource_manager
    ADD CONSTRAINT rc_resource_manager_pkey PRIMARY KEY (id);


--
-- TOC entry 4463 (class 2606 OID 28631)
-- Name: rc_resource_permission rc_resource_permission_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_resource_permission
    ADD CONSTRAINT rc_resource_permission_pkey PRIMARY KEY (id);


--
-- TOC entry 4564 (class 2606 OID 32787)
-- Name: rc_sample_template_catalogue rc_sample_template_catalogue_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_sample_template_catalogue
    ADD CONSTRAINT rc_sample_template_catalogue_pkey PRIMARY KEY (catalogue_id);


--
-- TOC entry 4586 (class 2606 OID 33411)
-- Name: rc_sample_template_file rc_sample_template_file_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_sample_template_file
    ADD CONSTRAINT rc_sample_template_file_pkey PRIMARY KEY (id);


--
-- TOC entry 4476 (class 2606 OID 33134)
-- Name: rc_security_policy rc_security_policy_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_security_policy
    ADD CONSTRAINT rc_security_policy_pkey PRIMARY KEY (id);


--
-- TOC entry 4548 (class 2606 OID 32305)
-- Name: rc_sensitive_category rc_sensitive_category_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_sensitive_category
    ADD CONSTRAINT rc_sensitive_category_pkey PRIMARY KEY (id);


--
-- TOC entry 4550 (class 2606 OID 32345)
-- Name: rc_sensitive_category_word rc_sensitive_type_word_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_sensitive_category_word
    ADD CONSTRAINT rc_sensitive_type_word_pkey PRIMARY KEY (id);


--
-- TOC entry 4546 (class 2606 OID 32291)
-- Name: rc_sensitive_word rc_sensitive_word_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_sensitive_word
    ADD CONSTRAINT rc_sensitive_word_pkey PRIMARY KEY (id);


--
-- TOC entry 4604 (class 2606 OID 60278)
-- Name: rc_statistic_data rc_statistic_data_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_statistic_data
    ADD CONSTRAINT rc_statistic_data_pkey PRIMARY KEY (id);


--
-- TOC entry 4600 (class 2606 OID 60265)
-- Name: rc_statistic_tag rc_statistic_tag_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_statistic_tag
    ADD CONSTRAINT rc_statistic_tag_pkey PRIMARY KEY (id);


--
-- TOC entry 4528 (class 2606 OID 31815)
-- Name: rc_task_batch rc_task_batch_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_task_batch
    ADD CONSTRAINT rc_task_batch_pkey PRIMARY KEY (id);


--
-- TOC entry 4501 (class 2606 OID 31468)
-- Name: rc_task_doc rc_task_doc_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_task_doc
    ADD CONSTRAINT rc_task_doc_pkey PRIMARY KEY (id);


--
-- TOC entry 4515 (class 2606 OID 31478)
-- Name: rc_task_flow_history rc_task_flow_history_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_task_flow_history
    ADD CONSTRAINT rc_task_flow_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4509 (class 2606 OID 31473)
-- Name: rc_task_flow rc_task_flow_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_task_flow
    ADD CONSTRAINT rc_task_flow_pkey PRIMARY KEY (id);


--
-- TOC entry 4496 (class 2606 OID 31461)
-- Name: rc_task rc_task_pkey; Type: CONSTRAINT; Schema: plss-record; Owner: suwell
--

ALTER TABLE ONLY "plss-record".rc_task
    ADD CONSTRAINT rc_task_pkey PRIMARY KEY (id);


--
-- TOC entry 4606 (class 2606 OID 88955)
-- Name: sys_opt_data_pms rc_data_pms_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_opt_data_pms
    ADD CONSTRAINT rc_data_pms_pkey PRIMARY KEY (id);


--
-- TOC entry 4612 (class 2606 OID 103738)
-- Name: sys_archive sys_archive_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_archive
    ADD CONSTRAINT sys_archive_pkey PRIMARY KEY (id);


--
-- TOC entry 4303 (class 2606 OID 24914)
-- Name: sys_category sys_category_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_category
    ADD CONSTRAINT sys_category_pkey PRIMARY KEY (id);


--
-- TOC entry 4461 (class 2606 OID 28483)
-- Name: sys_collect sys_collect_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_collect
    ADD CONSTRAINT sys_collect_pkey PRIMARY KEY (id);


--
-- TOC entry 4305 (class 2606 OID 24916)
-- Name: sys_config sys_config_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_config
    ADD CONSTRAINT sys_config_pkey PRIMARY KEY (config_id);


--
-- TOC entry 4307 (class 2606 OID 24918)
-- Name: sys_dict_data sys_dict_data_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_dict_data
    ADD CONSTRAINT sys_dict_data_pkey PRIMARY KEY (dict_code);


--
-- TOC entry 4310 (class 2606 OID 24920)
-- Name: sys_dict_type sys_dict_type_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_dict_type
    ADD CONSTRAINT sys_dict_type_pkey PRIMARY KEY (dict_id);


--
-- TOC entry 4572 (class 2606 OID 32911)
-- Name: sys_literary_collect sys_literary_collect_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_literary_collect
    ADD CONSTRAINT sys_literary_collect_pkey PRIMARY KEY (id);


--
-- TOC entry 4312 (class 2606 OID 24922)
-- Name: sys_logininfor sys_logininfor_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_logininfor
    ADD CONSTRAINT sys_logininfor_pkey PRIMARY KEY (info_id);


--
-- TOC entry 4314 (class 2606 OID 24924)
-- Name: sys_menu sys_menu_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_menu
    ADD CONSTRAINT sys_menu_pkey PRIMARY KEY (menu_id);


--
-- TOC entry 4441 (class 2606 OID 27632)
-- Name: sys_message sys_message_pk; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_message
    ADD CONSTRAINT sys_message_pk PRIMARY KEY (id);


--
-- TOC entry 4316 (class 2606 OID 24926)
-- Name: sys_notice sys_notice_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_notice
    ADD CONSTRAINT sys_notice_pkey PRIMARY KEY (notice_id);


--
-- TOC entry 4640 (class 2606 OID 188039)
-- Name: sys_oa_org sys_oa_org_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_oa_org
    ADD CONSTRAINT sys_oa_org_pkey PRIMARY KEY (id);


--
-- TOC entry 4642 (class 2606 OID 188048)
-- Name: sys_oa_user sys_oa_user_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_oa_user
    ADD CONSTRAINT sys_oa_user_pkey PRIMARY KEY (id);


--
-- TOC entry 4318 (class 2606 OID 24928)
-- Name: sys_oper_log sys_oper_log_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_oper_log
    ADD CONSTRAINT sys_oper_log_pkey PRIMARY KEY (oper_id);


--
-- TOC entry 4321 (class 2606 OID 24930)
-- Name: sys_org sys_org_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_org
    ADD CONSTRAINT sys_org_pkey PRIMARY KEY (org_id);


--
-- TOC entry 4472 (class 2606 OID 30411)
-- Name: sys_policy_rel sys_policy_rel_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_policy_rel
    ADD CONSTRAINT sys_policy_rel_pkey PRIMARY KEY (id);


--
-- TOC entry 4323 (class 2606 OID 24932)
-- Name: sys_post sys_post_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_post
    ADD CONSTRAINT sys_post_pkey PRIMARY KEY (post_id);


--
-- TOC entry 4644 (class 2606 OID 188053)
-- Name: sys_resource_push_record sys_resource_push_record_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_resource_push_record
    ADD CONSTRAINT sys_resource_push_record_pkey PRIMARY KEY (id);


--
-- TOC entry 4608 (class 2606 OID 92772)
-- Name: sys_role_data_pms sys_role_data_pms_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_role_data_pms
    ADD CONSTRAINT sys_role_data_pms_pkey PRIMARY KEY (role_id, data_pms_id);


--
-- TOC entry 4327 (class 2606 OID 24934)
-- Name: sys_role_menu sys_role_menu_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_role_menu
    ADD CONSTRAINT sys_role_menu_pkey PRIMARY KEY (role_id, menu_id);


--
-- TOC entry 4329 (class 2606 OID 24936)
-- Name: sys_role_org sys_role_org_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_role_org
    ADD CONSTRAINT sys_role_org_pkey PRIMARY KEY (role_id, org_id);


--
-- TOC entry 4325 (class 2606 OID 24938)
-- Name: sys_role sys_role_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_role
    ADD CONSTRAINT sys_role_pkey PRIMARY KEY (role_id);


--
-- TOC entry 4486 (class 2606 OID 31349)
-- Name: sys_search_collect sys_search_collect_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_search_collect
    ADD CONSTRAINT sys_search_collect_pkey PRIMARY KEY (id);


--
-- TOC entry 4484 (class 2606 OID 31342)
-- Name: sys_search_history sys_search_history_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_search_history
    ADD CONSTRAINT sys_search_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4628 (class 2606 OID 173205)
-- Name: sys_search_phrases sys_search_phrases_pk; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_search_phrases
    ADD CONSTRAINT sys_search_phrases_pk PRIMARY KEY (id);


--
-- TOC entry 4630 (class 2606 OID 173207)
-- Name: sys_search_phrases sys_search_phrases_un; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_search_phrases
    ADD CONSTRAINT sys_search_phrases_un UNIQUE (phrases);


--
-- TOC entry 4474 (class 2606 OID 30401)
-- Name: sys_role_policy sys_security_policy_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_role_policy
    ADD CONSTRAINT sys_security_policy_pkey PRIMARY KEY (id);


--
-- TOC entry 4434 (class 2606 OID 27147)
-- Name: sys_source sys_source_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_source
    ADD CONSTRAINT sys_source_pkey PRIMARY KEY (id);


--
-- TOC entry 4482 (class 2606 OID 31335)
-- Name: sys_table_header_config sys_table_header_config_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_table_header_config
    ADD CONSTRAINT sys_table_header_config_pkey PRIMARY KEY (id);


--
-- TOC entry 4478 (class 2606 OID 31249)
-- Name: sys_table_header sys_table_header_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_table_header
    ADD CONSTRAINT sys_table_header_pkey PRIMARY KEY (id);


--
-- TOC entry 4445 (class 2606 OID 28417)
-- Name: sys_tenant_menu sys_tenant_menu_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_tenant_menu
    ADD CONSTRAINT sys_tenant_menu_pkey PRIMARY KEY (tenant_id, menu_id);


--
-- TOC entry 4443 (class 2606 OID 28326)
-- Name: sys_tenant sys_tenant_pk; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_tenant
    ADD CONSTRAINT sys_tenant_pk PRIMARY KEY (id);


--
-- TOC entry 4614 (class 2606 OID 172709)
-- Name: sys_user_org sys_user_org_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_user_org
    ADD CONSTRAINT sys_user_org_pkey PRIMARY KEY (org_id, user_id, tenant_id);


--
-- TOC entry 4331 (class 2606 OID 24942)
-- Name: sys_user sys_user_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_user
    ADD CONSTRAINT sys_user_pkey PRIMARY KEY (user_id);


--
-- TOC entry 4335 (class 2606 OID 24944)
-- Name: sys_user_post sys_user_post_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_user_post
    ADD CONSTRAINT sys_user_post_pkey PRIMARY KEY (user_id, post_id);


--
-- TOC entry 4337 (class 2606 OID 60298)
-- Name: sys_user_role sys_user_role_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_user_role
    ADD CONSTRAINT sys_user_role_pkey PRIMARY KEY (role_id, user_id, tenant_id);


--
-- TOC entry 4333 (class 2606 OID 28633)
-- Name: sys_user sys_user_un; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_user
    ADD CONSTRAINT sys_user_un UNIQUE (access_key);


--
-- TOC entry 4470 (class 2606 OID 30178)
-- Name: sys_watermark_setting sys_watermark_setting_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".sys_watermark_setting
    ADD CONSTRAINT sys_watermark_setting_pkey PRIMARY KEY (id);


--
-- TOC entry 4427 (class 2606 OID 27116)
-- Name: xxl_job_group xxl_job_group_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_group
    ADD CONSTRAINT xxl_job_group_pkey PRIMARY KEY (id);


--
-- TOC entry 4413 (class 2606 OID 27054)
-- Name: xxl_job_info xxl_job_info_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_info
    ADD CONSTRAINT xxl_job_info_pkey PRIMARY KEY (id);


--
-- TOC entry 4432 (class 2606 OID 27130)
-- Name: xxl_job_lock xxl_job_lock_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_lock
    ADD CONSTRAINT xxl_job_lock_pkey PRIMARY KEY (lock_name);


--
-- TOC entry 4417 (class 2606 OID 27069)
-- Name: xxl_job_log xxl_job_log_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_log
    ADD CONSTRAINT xxl_job_log_pkey PRIMARY KEY (id);


--
-- TOC entry 4421 (class 2606 OID 27093)
-- Name: xxl_job_log_report xxl_job_log_report_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_log_report
    ADD CONSTRAINT xxl_job_log_report_pkey PRIMARY KEY (id);


--
-- TOC entry 4419 (class 2606 OID 27082)
-- Name: xxl_job_logglue xxl_job_logglue_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_logglue
    ADD CONSTRAINT xxl_job_logglue_pkey PRIMARY KEY (id);


--
-- TOC entry 4425 (class 2606 OID 27103)
-- Name: xxl_job_registry xxl_job_registry_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_registry
    ADD CONSTRAINT xxl_job_registry_pkey PRIMARY KEY (id);


--
-- TOC entry 4430 (class 2606 OID 27124)
-- Name: xxl_job_user xxl_job_user_pkey; Type: CONSTRAINT; Schema: plss-system; Owner: suwell
--

ALTER TABLE ONLY "plss-system".xxl_job_user
    ADD CONSTRAINT xxl_job_user_pkey PRIMARY KEY (id);


--
-- TOC entry 4559 (class 1259 OID 32701)
-- Name: idx_borrow_id; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX idx_borrow_id ON "plss-record".rc_record_borrow USING btree (borrow_id);


--
-- TOC entry 6291 (class 0 OID 0)
-- Dependencies: 4559
-- Name: INDEX idx_borrow_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON INDEX "plss-record".idx_borrow_id IS 'borrow业务主键';


--
-- TOC entry 4364 (class 1259 OID 60266)
-- Name: idx_md_name; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX idx_md_name ON "plss-record".rc_metadata_value USING btree (md_name);


--
-- TOC entry 4465 (class 1259 OID 52968)
-- Name: idx_recordId_code_psta; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX "idx_recordId_code_psta" ON "plss-record".rc_record_process_detail USING btree (record_id, record_process_type, record_process_status);


--
-- TOC entry 4365 (class 1259 OID 52966)
-- Name: idx_recordId_mdId; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX "idx_recordId_mdId" ON "plss-record".rc_metadata_value USING btree (record_id, md_id);


--
-- TOC entry 4437 (class 1259 OID 52969)
-- Name: idx_recordTypeId; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX "idx_recordTypeId" ON "plss-record".rc_record_type_metadata USING btree (recordtype_id);


--
-- TOC entry 4560 (class 1259 OID 32702)
-- Name: idx_record_id; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX idx_record_id ON "plss-record".rc_record_borrow USING btree (record_id);


--
-- TOC entry 6292 (class 0 OID 0)
-- Dependencies: 4560
-- Name: INDEX idx_record_id; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON INDEX "plss-record".idx_record_id IS 'record主键';


--
-- TOC entry 4598 (class 1259 OID 60281)
-- Name: idx_rid_tagid; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE UNIQUE INDEX idx_rid_tagid ON "plss-record".rc_statistic_tag USING btree (record_id, tag_id);


--
-- TOC entry 4620 (class 1259 OID 171975)
-- Name: idx_stat_name_en; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX idx_stat_name_en ON "plss-record".rc_metadata USING btree (status, name_en);


--
-- TOC entry 4589 (class 1259 OID 39782)
-- Name: rc_doc_audit_record_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_audit_record_id_idx ON "plss-record".rc_doc_audit USING btree (record_id);


--
-- TOC entry 4590 (class 1259 OID 39784)
-- Name: rc_doc_audit_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_audit_status_idx ON "plss-record".rc_doc_audit USING btree (status);


--
-- TOC entry 4591 (class 1259 OID 39783)
-- Name: rc_doc_audit_user_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_audit_user_id_idx ON "plss-record".rc_doc_audit USING btree (user_id);


--
-- TOC entry 4531 (class 1259 OID 33170)
-- Name: rc_doc_process_audit_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_audit_status_idx ON "plss-record".rc_doc_process USING btree (audit_status);


--
-- TOC entry 4532 (class 1259 OID 33163)
-- Name: rc_doc_process_batch_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_batch_id_idx ON "plss-record".rc_doc_process USING btree (batch_id);


--
-- TOC entry 4533 (class 1259 OID 33171)
-- Name: rc_doc_process_deleted_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_deleted_idx ON "plss-record".rc_doc_process USING btree (deleted);


--
-- TOC entry 4534 (class 1259 OID 33167)
-- Name: rc_doc_process_origin_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_origin_idx ON "plss-record".rc_doc_process USING btree (origin);


--
-- TOC entry 4537 (class 1259 OID 33172)
-- Name: rc_doc_process_plan_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_plan_id_idx ON "plss-record".rc_doc_process USING btree (plan_id);


--
-- TOC entry 4538 (class 1259 OID 33168)
-- Name: rc_doc_process_process_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_process_status_idx ON "plss-record".rc_doc_process USING btree (process_status);


--
-- TOC entry 4539 (class 1259 OID 33169)
-- Name: rc_doc_process_process_sub_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_process_sub_status_idx ON "plss-record".rc_doc_process USING btree (process_sub_status);


--
-- TOC entry 4540 (class 1259 OID 187902)
-- Name: rc_doc_process_real_origin_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_real_origin_idx ON "plss-record".rc_doc_process USING btree (real_origin);


--
-- TOC entry 4541 (class 1259 OID 33165)
-- Name: rc_doc_process_record_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_record_id_idx ON "plss-record".rc_doc_process USING btree (record_id);


--
-- TOC entry 4542 (class 1259 OID 33166)
-- Name: rc_doc_process_record_type_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_record_type_id_idx ON "plss-record".rc_doc_process USING btree (record_type_id);


--
-- TOC entry 4543 (class 1259 OID 33164)
-- Name: rc_doc_process_task_doc_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_task_doc_id_idx ON "plss-record".rc_doc_process USING btree (task_doc_id);


--
-- TOC entry 4544 (class 1259 OID 52923)
-- Name: rc_doc_process_task_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_doc_process_task_id_idx ON "plss-record".rc_doc_process USING btree (task_id);


--
-- TOC entry 4615 (class 1259 OID 171951)
-- Name: rc_node_node_type_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_node_node_type_idx ON "plss-record".rc_node USING btree (node_type);


--
-- TOC entry 4616 (class 1259 OID 171952)
-- Name: rc_node_order_num_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_node_order_num_idx ON "plss-record".rc_node USING btree (order_num);


--
-- TOC entry 4619 (class 1259 OID 171953)
-- Name: rc_node_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_node_status_idx ON "plss-record".rc_node USING btree (status);


--
-- TOC entry 4489 (class 1259 OID 33156)
-- Name: rc_plan_node_node_type_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_plan_node_node_type_idx ON "plss-record".rc_plan_node USING btree (node_type);


--
-- TOC entry 4490 (class 1259 OID 33155)
-- Name: rc_plan_node_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_plan_node_status_idx ON "plss-record".rc_plan_node USING btree (status);


--
-- TOC entry 4346 (class 1259 OID 187901)
-- Name: rc_record_real_origin_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_record_real_origin_idx ON "plss-record".rc_record USING btree (real_origin);


--
-- TOC entry 4347 (class 1259 OID 187919)
-- Name: rc_record_store_process_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_record_store_process_idx ON "plss-record".rc_record USING btree (store_process);


--
-- TOC entry 4601 (class 1259 OID 60280)
-- Name: rc_statistic_data_business_date_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_statistic_data_business_date_idx ON "plss-record".rc_statistic_data USING btree (business_date);


--
-- TOC entry 4602 (class 1259 OID 60279)
-- Name: rc_statistic_data_data_type_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_statistic_data_data_type_idx ON "plss-record".rc_statistic_data USING btree (data_type);


--
-- TOC entry 4494 (class 1259 OID 33176)
-- Name: rc_task_batch_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_batch_id_idx ON "plss-record".rc_task USING btree (batch_id);


--
-- TOC entry 4526 (class 1259 OID 33175)
-- Name: rc_task_batch_origin_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_batch_origin_idx ON "plss-record".rc_task_batch USING btree (origin);


--
-- TOC entry 4529 (class 1259 OID 33173)
-- Name: rc_task_batch_plan_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_batch_plan_id_idx ON "plss-record".rc_task_batch USING btree (plan_id);


--
-- TOC entry 4530 (class 1259 OID 33174)
-- Name: rc_task_batch_record_type_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_batch_record_type_id_idx ON "plss-record".rc_task_batch USING btree (record_type_id);


--
-- TOC entry 4498 (class 1259 OID 33181)
-- Name: rc_task_doc_ctype_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_doc_ctype_idx ON "plss-record".rc_task_doc USING btree (ctype);


--
-- TOC entry 4499 (class 1259 OID 33182)
-- Name: rc_task_doc_doc_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_doc_doc_id_idx ON "plss-record".rc_task_doc USING btree (doc_id);


--
-- TOC entry 4502 (class 1259 OID 33183)
-- Name: rc_task_doc_record_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_doc_record_id_idx ON "plss-record".rc_task_doc USING btree (record_id);


--
-- TOC entry 4503 (class 1259 OID 33180)
-- Name: rc_task_doc_task_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_doc_task_id_idx ON "plss-record".rc_task_doc USING btree (task_id);


--
-- TOC entry 4504 (class 1259 OID 33186)
-- Name: rc_task_flow_ctype_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_ctype_idx ON "plss-record".rc_task_flow USING btree (ctype);


--
-- TOC entry 4505 (class 1259 OID 33189)
-- Name: rc_task_flow_deleted_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_deleted_idx ON "plss-record".rc_task_flow USING btree (deleted);


--
-- TOC entry 4506 (class 1259 OID 33187)
-- Name: rc_task_flow_node_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_node_id_idx ON "plss-record".rc_task_flow USING btree (node_id);


--
-- TOC entry 4507 (class 1259 OID 33190)
-- Name: rc_task_flow_node_type_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_node_type_idx ON "plss-record".rc_task_flow USING btree (node_type);


--
-- TOC entry 4510 (class 1259 OID 33191)
-- Name: rc_task_flow_record_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_record_id_idx ON "plss-record".rc_task_flow USING btree (record_id);


--
-- TOC entry 4511 (class 1259 OID 33188)
-- Name: rc_task_flow_status_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_status_idx ON "plss-record".rc_task_flow USING btree (status);


--
-- TOC entry 4512 (class 1259 OID 33184)
-- Name: rc_task_flow_task_doc_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_task_doc_id_idx ON "plss-record".rc_task_flow USING btree (task_doc_id);


--
-- TOC entry 4513 (class 1259 OID 33185)
-- Name: rc_task_flow_task_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_flow_task_id_idx ON "plss-record".rc_task_flow USING btree (task_id);


--
-- TOC entry 4497 (class 1259 OID 33177)
-- Name: rc_task_plan_id_idx; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE INDEX rc_task_plan_id_idx ON "plss-record".rc_task USING btree (plan_id);


--
-- TOC entry 4493 (class 1259 OID 31441)
-- Name: unique_sign; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE UNIQUE INDEX unique_sign ON "plss-record".rc_resource_manager USING btree (resource_id, visitor_id);


--
-- TOC entry 4464 (class 1259 OID 28629)
-- Name: unique_v_r_index; Type: INDEX; Schema: plss-record; Owner: suwell
--

CREATE UNIQUE INDEX unique_v_r_index ON "plss-record".rc_resource_permission USING btree (visitor_id, resource_id);


--
-- TOC entry 6293 (class 0 OID 0)
-- Dependencies: 4464
-- Name: INDEX unique_v_r_index; Type: COMMENT; Schema: plss-record; Owner: suwell
--

COMMENT ON INDEX "plss-record".unique_v_r_index IS '使用者资源唯一索引';


--
-- TOC entry 4422 (class 1259 OID 27104)
-- Name: i_g_k_v; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE INDEX i_g_k_v ON "plss-system".xxl_job_registry USING btree (registry_group, registry_key, registry_value);


--
-- TOC entry 4414 (class 1259 OID 27071)
-- Name: i_handle_code; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE INDEX i_handle_code ON "plss-system".xxl_job_log USING btree (handle_code);


--
-- TOC entry 4415 (class 1259 OID 162013)
-- Name: i_trigger_time; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE INDEX i_trigger_time ON "plss-system".xxl_job_log USING btree (trigger_time);


--
-- TOC entry 4423 (class 1259 OID 27105)
-- Name: i_u; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE INDEX i_u ON "plss-system".xxl_job_registry USING btree (update_time);


--
-- TOC entry 4428 (class 1259 OID 27125)
-- Name: i_username; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE UNIQUE INDEX i_username ON "plss-system".xxl_job_user USING btree (username);


--
-- TOC entry 4308 (class 1259 OID 24947)
-- Name: rc_metadata_category_pkey; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE UNIQUE INDEX rc_metadata_category_pkey ON "plss-system".sys_dict_type USING btree (dict_id);


--
-- TOC entry 4319 (class 1259 OID 28625)
-- Name: sys_org_access_key_idx; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE UNIQUE INDEX sys_org_access_key_idx ON "plss-system".sys_org USING btree (access_key);


--
-- TOC entry 4468 (class 1259 OID 28932)
-- Name: sys_region_id_idx; Type: INDEX; Schema: plss-system; Owner: suwell
--

CREATE UNIQUE INDEX sys_region_id_idx ON "plss-system".sys_region USING btree (id);


--
-- TOC entry 4646 (class 2620 OID 27083)
-- Name: xxl_job_logglue t_xxl_job_logglue_update_time; Type: TRIGGER; Schema: plss-system; Owner: suwell
--

CREATE TRIGGER t_xxl_job_logglue_update_time BEFORE UPDATE ON "plss-system".xxl_job_logglue FOR EACH ROW EXECUTE FUNCTION "plss-system".upd_timestamp();



-- Completed on 2024-04-11 09:01:05 CST

--
-- PostgreSQL database dump complete
--

