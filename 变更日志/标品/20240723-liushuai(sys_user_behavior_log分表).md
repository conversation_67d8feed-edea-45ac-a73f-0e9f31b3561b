### sys_user_behavior_log分表md

#### nacos配置

```yaml
spring:
  # 分库分表配置【增量配置】     此配置在system模块
  shardingsphere:
    datasource:
      names: logic-system
      logic-system:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: ${spring.datasource.dynamic.datasource.master.driver-class-name}
        url: ${spring.datasource.dynamic.datasource.master.url}
        username: ${spring.datasource.dynamic.datasource.master.username}
        password: ${spring.datasource.dynamic.datasource.master.password}
        initial-size: ${spring.datasource.dynamic.druid.initial-size}
        min-idle: ${spring.datasource.dynamic.druid.min-idle}
        max-active: ${spring.datasource.dynamic.druid.max-active}
        max-wait: ${spring.datasource.dynamic.druid.max-wait}
        time-between-eviction-runs-millis: ${spring.datasource.dynamic.druid.time-between-eviction-runs-millis}
        min-evictable-idle-time-millis: ${spring.datasource.dynamic.druid.min-evictable-idle-time-millis}
        validation-query: ${spring.datasource.dynamic.druid.validation-query}
        test-while-idle: ${spring.datasource.dynamic.druid.test-while-idle}
        test-on-borrow: ${spring.datasource.dynamic.druid.test-on-borrow}
        test-on-return: ${spring.datasource.dynamic.druid.test-on-return}
        pool-prepared-statements: ${spring.datasource.dynamic.druid.pool-prepared-statements}
        max-pool-prepared-statement-per-connection-size: ${spring.datasource.dynamic.druid.max-pool-prepared-statement-per-connection-size}
        # filters: ${spring.datasource.dynamic.druid.filters}
        connection-properties:
          druid:
            stat:
              mergeSql: ${spring.datasource.dynamic.druid.connection-properties.druid.stat.mergeSql}
              slowSqlMillis: ${spring.datasource.dynamic.druid.connection-properties.druid.stat.slowSqlMillis}
    props:
      sql-show: true
    rules:
      sharding:
        sharding-algorithms:
          userBehaviorlogSharding:
            type: plss-user-behavior-log-sharding
        tables:
          sys_user_behavior_log:
            actual-data-nodes: logic-system.sys_user_behavior_log
            table-strategy:
              complex:
                sharding-columns: create_time
                shardingAlgorithmName: userBehaviorlogSharding
```

#### pg

```sql
-- sys_user_behavior_log分表
create table if not exists "plss-system".sys_user_behavior_log_202407
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202407_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202407_id" ON "plss-system".sys_user_behavior_log_202407 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202408
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202408_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202408_id" ON "plss-system".sys_user_behavior_log_202408 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202409
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202409_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202409_id" ON "plss-system".sys_user_behavior_log_202409 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202410
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202410_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202410_id" ON "plss-system".sys_user_behavior_log_202410 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202411
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202411_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202411_id" ON "plss-system".sys_user_behavior_log_202411 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202412
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202412_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202412_id" ON "plss-system".sys_user_behavior_log_202412 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202501
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202501_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202501_id" ON "plss-system".sys_user_behavior_log_202501 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202502
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202502_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202502_id" ON "plss-system".sys_user_behavior_log_202502 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202503
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202503_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202503_id" ON "plss-system".sys_user_behavior_log_202503 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202504
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202504_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202504_id" ON "plss-system".sys_user_behavior_log_202504 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202505
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202505_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202505_id" ON "plss-system".sys_user_behavior_log_202505 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202506
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202506_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202506_id" ON "plss-system".sys_user_behavior_log_202506 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202507
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202507_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202507_id" ON "plss-system".sys_user_behavior_log_202507 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202508
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202508_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202508_id" ON "plss-system".sys_user_behavior_log_202508 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202509
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202509_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202509_id" ON "plss-system".sys_user_behavior_log_202509 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202510
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202510_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202510_id" ON "plss-system".sys_user_behavior_log_202510 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202511
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202511_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202511_id" ON "plss-system".sys_user_behavior_log_202511 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202512
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202512_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202512_id" ON "plss-system".sys_user_behavior_log_202512 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202601
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202601_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202601_id" ON "plss-system".sys_user_behavior_log_202601 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202602
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202602_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202602_id" ON "plss-system".sys_user_behavior_log_202602 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202603
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202603_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202603_id" ON "plss-system".sys_user_behavior_log_202603 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202604
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202604_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202604_id" ON "plss-system".sys_user_behavior_log_202604 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202605
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202605_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202605_id" ON "plss-system".sys_user_behavior_log_202605 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202606
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202606_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202606_id" ON "plss-system".sys_user_behavior_log_202606 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202607
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202607_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202607_id" ON "plss-system".sys_user_behavior_log_202607 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202608
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202608_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202608_id" ON "plss-system".sys_user_behavior_log_202608 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202609
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202609_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202609_id" ON "plss-system".sys_user_behavior_log_202609 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202610
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202610_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202610_id" ON "plss-system".sys_user_behavior_log_202610 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202611
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202611_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202611_id" ON "plss-system".sys_user_behavior_log_202611 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202612
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202612_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202612_id" ON "plss-system".sys_user_behavior_log_202612 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202701
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202701_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202701_id" ON "plss-system".sys_user_behavior_log_202701 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202702
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202702_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202702_id" ON "plss-system".sys_user_behavior_log_202702 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202703
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202703_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202703_id" ON "plss-system".sys_user_behavior_log_202703 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202704
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202704_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202704_id" ON "plss-system".sys_user_behavior_log_202704 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202705
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202705_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202705_id" ON "plss-system".sys_user_behavior_log_202705 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202706
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202706_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202706_id" ON "plss-system".sys_user_behavior_log_202706 USING btree ( user_id );
create table if not exists "plss-system".sys_user_behavior_log_202707
(
    ID              INT8         NOT NULL,
    project_name    VARCHAR(64)  NOT NULL,
    gwk_version     VARCHAR(64)  NOT NULL,
    event_name      VARCHAR(256) NOT NULL,
    user_id         INT8         NOT NULL,
    user_name       VARCHAR(64)  NOT NULL,
    model           VARCHAR(64),
    os              VARCHAR(64),
    os_version      VARCHAR(64),
    browser         VARCHAR(64),
    browser_version VARCHAR(64),
    screen_height   INT4         NOT NULL,
    screen_width    INT4         NOT NULL,
    ip              VARCHAR(64)  NOT NULL,
    DATA            TEXT         NOT NULL,
    create_year     INT4         NOT NULL,
    create_month    INT4         NOT NULL,
    create_day      INT4         NOT NULL,
    create_hour     INT4         NOT NULL,
    create_minute   INT4         NOT NULL,
    create_second   INT4         NOT NULL,
    create_time     TIMESTAMP(6) NOT NULL,
    status          INT4         NOT NULL DEFAULT 1,
    del_flag        INT4         NOT NULL DEFAULT 1,
    remark          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202707_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_user_202707_id" ON "plss-system".sys_user_behavior_log_202707 USING btree ( user_id );



```

#### dm

```sql
-- sys_user_behavior_log分表
CREATE TABLE "plss-system"."sys_user_behavior_log_202407"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202407_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202407_idx ON "plss-system".sys_user_behavior_log_202407 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202408"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202408_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202408_idx ON "plss-system".sys_user_behavior_log_202408 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202409"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202409_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202409_idx ON "plss-system".sys_user_behavior_log_202409 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202410"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202410_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202410_idx ON "plss-system".sys_user_behavior_log_202410 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202411"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202411_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202411_idx ON "plss-system".sys_user_behavior_log_202411 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202412"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202412_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202412_idx ON "plss-system".sys_user_behavior_log_202412 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202501"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202501_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202501_idx ON "plss-system".sys_user_behavior_log_202501 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202502"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202502_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202502_idx ON "plss-system".sys_user_behavior_log_202502 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202503"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202503_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202503_idx ON "plss-system".sys_user_behavior_log_202503 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202504"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202504_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202504_idx ON "plss-system".sys_user_behavior_log_202504 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202505"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202505_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202505_idx ON "plss-system".sys_user_behavior_log_202505 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202506"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202506_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202506_idx ON "plss-system".sys_user_behavior_log_202506 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202507"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202507_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202507_idx ON "plss-system".sys_user_behavior_log_202507 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202508"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202508_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202508_idx ON "plss-system".sys_user_behavior_log_202508 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202509"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202509_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202509_idx ON "plss-system".sys_user_behavior_log_202509 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202510"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202510_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202510_idx ON "plss-system".sys_user_behavior_log_202510 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202511"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202511_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202511_idx ON "plss-system".sys_user_behavior_log_202511 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202512"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202512_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202512_idx ON "plss-system".sys_user_behavior_log_202512 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202601"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202601_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202601_idx ON "plss-system".sys_user_behavior_log_202601 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202602"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202602_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202602_idx ON "plss-system".sys_user_behavior_log_202602 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202603"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202603_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202603_idx ON "plss-system".sys_user_behavior_log_202603 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202604"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202604_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202604_idx ON "plss-system".sys_user_behavior_log_202604 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202605"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202605_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202605_idx ON "plss-system".sys_user_behavior_log_202605 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202606"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202606_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202606_idx ON "plss-system".sys_user_behavior_log_202606 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202607"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202607_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202607_idx ON "plss-system".sys_user_behavior_log_202607 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202608"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202608_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202608_idx ON "plss-system".sys_user_behavior_log_202608 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202609"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202609_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202609_idx ON "plss-system".sys_user_behavior_log_202609 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202610"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202610_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202610_idx ON "plss-system".sys_user_behavior_log_202610 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202611"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202611_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202611_idx ON "plss-system".sys_user_behavior_log_202611 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202612"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202612_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202612_idx ON "plss-system".sys_user_behavior_log_202612 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202701"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202701_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202701_idx ON "plss-system".sys_user_behavior_log_202701 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202702"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202702_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202702_idx ON "plss-system".sys_user_behavior_log_202702 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202703"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202703_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202703_idx ON "plss-system".sys_user_behavior_log_202703 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202704"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202704_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202704_idx ON "plss-system".sys_user_behavior_log_202704 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202705"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202705_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202705_idx ON "plss-system".sys_user_behavior_log_202705 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202706"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202706_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202706_idx ON "plss-system".sys_user_behavior_log_202706 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202707"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202707_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202707_idx ON "plss-system".sys_user_behavior_log_202707 (user_id);
```