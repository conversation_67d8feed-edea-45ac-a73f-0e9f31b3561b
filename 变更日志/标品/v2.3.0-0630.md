# 上线功能清单

## 一. 数据库脚本

### 1. postgresql 库sql脚本

```sql

-- 其它环境字段补充
ALTER TABLE "plss-system".sys_org ADD area_sign varchar(256);

COMMENT ON COLUMN "plss-system".sys_org.area_sign IS '区域标志';

-- -- 入库日志事件类型和事件状态
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES (244695126124293, '入库日志记录事件类型', 'lib_event_type', '1', '2024-07-11 17:13:30.515911', '001', '2024-07-11 17:13:30.515911', '', '入库日志记录事件类型');
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES (244708067944197, '入库日志记录事件状态', 'lib_event_state', '1', '2024-07-11 17:39:50.326238', '001', '2024-07-11 17:39:50.326238', '', '入库日志记录事件状态');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708617168645, 3, '处理失败', '3', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:57.369', '', '2024-07-11 17:40:57.369', '处理失败');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708457858821, 2, '处理成功', '2', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:37.922', '', '2024-07-11 17:40:37.922', '处理成功');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708313138949, 1, '处理中', '1', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:20.257', '', '2024-07-11 17:40:20.257', '处理中');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244735222458117, 15, '文件拆分', '1704', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 18:35:05.085', '', '2024-07-11 18:35:05.085', '文件拆分');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244735070562053, 14, '生成缩略图', '1501', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 18:34:46.543', '', '2024-07-11 18:34:46.543', '生成缩略图');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244712486258437, 13, '古诗词提取', '707', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:48:49.67', '', '2024-07-11 17:48:49.67', '古诗词提取');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244712152475397, 12, '提取正文处理,不依赖转换服务和ocr提取,直接提取doc或docx', '603', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:48:08.925', '', '2024-07-11 17:48:08.925', '提取正文处理,不依赖转换服务和ocr提取,直接提取doc或docx');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244707478005509, 11, '提取实体', '704', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:38:38.311', '', '2024-07-11 17:38:38.311', '提取实体');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244707333121797, 10, '提取关键词', '703', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:38:20.625', '', '2024-07-11 17:38:20.625', '提取关键词');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244706972436229, 9, '提取分类', '701', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:37:36.596', '', '2024-07-11 17:37:36.596', '提取分类');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244706783643397, 8, '提取摘要', '700', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:37:13.55', '001', '2024-07-11 17:37:42.21998', '提取摘要');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705913440005, 7, '提取元数据', '601', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:35:27.324', '', '2024-07-11 17:35:27.324', '提取元数据');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705755318021, 6, '提取正文处理', '600', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:35:08.022', '', '2024-07-11 17:35:08.022', '提取正文处理');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705251952389, 4, '转换单层OFD', '501', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:34:06.576', '001', '2024-07-11 17:34:31.844357', '转换单层OFD');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705002235653, 5, 'AI中台提取关键词&实体&分类&摘要&向量化', '799', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:33:36.093', '001', '2024-07-11 17:34:23.947001', 'AI中台提取关键词&实体&分类&摘要&向量化');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244704828376837, 3, '转换双层OFD', '500', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:33:14.87', '001', '2024-07-11 17:34:41.125174', '转换双层OFD');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244699780081413, 2, '通过模型提取元数据', '730', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:22:58.623', '', '2024-07-11 17:22:58.623', '通过模型提取元数据');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244699607246597, 1, '知识工程', '720', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:22:37.525', '', '2024-07-11 17:22:37.525', '知识工程');


-- 驳回原因审核
ALTER TABLE "plss-record"."rc_doc_audit" 
  ADD COLUMN "reject_reason" varchar(516);
COMMENT ON COLUMN "plss-record"."rc_doc_audit"."reject_reason" IS '拒绝:驳回原因';

insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331141, '总书记重要讲话', '1', 106, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331142, '经济', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331143, '论经济建设', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331144, '论经济新常态与供给侧结构性改革', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339333, '论全面深化改革', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339334, '论新发展理念', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339335, '党建', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339336, '论党对一切工作的领导', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339337, '论党风廉政建设和反腐败斗争', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339338, '论干部队伍建设', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339339, '论坚定理想信念', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339340, '论理论和哲学社会科学工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339341, '论调查研究', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339342, '论全面从严治党', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339343, '论群众工作', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339344, '论新时代中国共产党的历史使命', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339345, '论中国共产党历史', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339346, '论组织工作', '1', 11, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339347, '论“不忘初心、牢记使命”', '1', 12, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339348, '文化', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339349, '论互联网宣传工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339350, '论精神文明建设工作', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339351, '论民族和宗教工作', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339352, '论文化', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339353, '论新闻舆论工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339354, '论宣传思想工作', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339355, '论中国精神', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339356, '论中国梦', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339357, '论“四个自信”', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339358, '国防', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339359, '论海洋强国', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339360, '论坚持总体国家安全观', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347525, '论强军兴军', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347526, '外交', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347527, '论人类命运共同体', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347528, '论中国特色大国外交', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347529, '论"一带一路"', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347530, '政治', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347531, '论“一国两制”和推进祖国统一', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347532, '论爱国统一战线', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347533, '论发展社会主义民主政治', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347534, '论工人阶级', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347535, '论坚持和发展中国特色社会主义', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347536, '论坚持人民当家作主', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347537, '论社会主义核心价值体系', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347538, '论全面依法治国', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347539, '论深化党和国家机构改革', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347540, '论“五位一体”和“四个全面”', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347541, '论以人民为中心', '1', 11, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347542, '科技', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347543, '论互联网建设与管理', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347544, '论科技创新', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347545, '民生', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347546, '论妇女儿童和妇联工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347547, '论全面建成小康社会', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347548, '论脱贫攻坚', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355717, '论社会建设和保障民生', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355718, '论卫生和健康工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355719, '论尊重和保障人权', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355720, '论“三农”工作和乡村振兴战略', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355721, '生态', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355722, '论社会主义生态文明建设', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363909, '教育', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363910, '论青年工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363911, '论教育工作', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363912, '论人才工作', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363913, '论学习', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363914, '论学习榜样', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363915, '论马克思', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);




insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331141, 0, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331142, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331143, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331144, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339333, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339334, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339335, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339336, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339337, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339338, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339339, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339340, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339341, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339342, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339343, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339344, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339345, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339346, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339347, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339348, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339349, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339350, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339351, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339352, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339353, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339354, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339355, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339356, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339357, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339358, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339359, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339360, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347525, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347526, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347527, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347528, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347529, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347530, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347531, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347532, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347533, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347534, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347535, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347536, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347537, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347538, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347539, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347540, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347541, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347542, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347543, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347544, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347545, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347546, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347547, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347548, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355717, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355718, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355719, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355720, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355721, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355722, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363909, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363910, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363911, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363912, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363913, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363914, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363915, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331143, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331144, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339333, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339334, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339336, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339337, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339338, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339339, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339340, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339341, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339342, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339343, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339344, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339345, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339346, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339347, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339349, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339350, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339351, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339352, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339353, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339354, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339355, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339356, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339357, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339359, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339360, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347525, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347527, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347528, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347529, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347531, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347532, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347533, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347534, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347535, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347536, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347537, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347538, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347539, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347540, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347541, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347543, 229182064347542, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347544, 229182064347542, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347546, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347547, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347548, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355717, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355718, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355719, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355720, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355722, 229182064355721, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363910, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363911, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363912, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363913, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363914, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363915, 229182064363909, 1);


INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name")
VALUES (229146515556741, '总书记讲话分类id', 'eod.chairman.categoryId', '229182064331141', 'Y', 1, '2024-06-19 17:59:46.762000', 'admin', '2024-06-20 10:33:14.420405', 'admin', '', null, null);
-- 入库链路日志记录表
CREATE TABLE "plss-record"."rc_pipeline_trace_log" (
  "id" int8 NOT NULL,
  "record_id" int8 NOT NULL,
  "in_param" text,
  "out_param" text,
  "event_type" int4,
  "event_state" int2,
  "except_reason" text,
  "reason_type" int2,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_pipeline_trace_log_pkey" PRIMARY KEY ("id")
)
;


CREATE INDEX "idx_crt" ON "plss-record"."rc_pipeline_trace_log" USING btree (
  "create_time"
);

CREATE INDEX "idx_etype" ON "plss-record"."rc_pipeline_trace_log" USING btree (
  "event_type"
);

CREATE INDEX "idx_rid" ON "plss-record"."rc_pipeline_trace_log" USING btree (
  "record_id"
);

CREATE INDEX "idx_rid_etype_crt" ON "plss-record"."rc_pipeline_trace_log" USING btree (
  "record_id",
  "event_type",
  "create_time" 
);

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."id" IS '主键';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."record_id" IS '文件id';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."in_param" IS '入参';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."out_param" IS '出参';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."event_type" IS '事件任务';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."event_state" IS '事件状态1-处理中2-成功3-失败';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."except_reason" IS '异常原因';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."reason_type" IS '原因类型';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_pipeline_trace_log" IS '入库流程记录错误日志';

-- 入库链路异常字典配置
insert into "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (219214497629381, '入库异常原因类型', 'lib_reason_type', '1', '2024-06-05 17:13:02.233528',
        '001', '2024-06-05 17:13:02.233528', '', '入库异常原因类型');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (219215451989189, 2, '系统内部', '2', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-06-05 17:14:58.730000', '001', '2024-07-11 10:32:17.281893', '系统内部');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (219215299019973, 1, 'AI中台对接', '1', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-06-05 17:14:40.056000', '001', '2024-07-11 13:54:50.852662', 'AI中台对接');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (244497777691269, 3, 'OFD转换对接', '3', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-07-11 10:32:00.128000', '001', '2024-07-11 10:34:37.373842', 'OFD转换对接');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (244498231921285, 4, 'OCR对接', '4', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-07-11 10:32:55.576000', '001', '2024-07-11 13:55:19.964166', 'OCR对接');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244659545089797, 5, 'API5对接', '5', 'lib_reason_type', '', 'default', 'N', '1', 'admin', '2024-07-11 16:01:07.125', '', '2024-07-11 16:01:07.125', 'API5对接');

        

-- sharding 元数据值分表
CREATE TABLE "plss-record"."rc_record_metadata_value_0" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_0_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_0" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_1" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_1_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_1" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_2" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_2_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_2" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_3" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_3_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_3" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_4" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_4_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_4" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_5" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_5_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_5" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_6" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_6_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_6" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_7" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_7_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_7" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_8" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_8_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_8" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_9" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_9_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_9" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_10" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_10_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_10" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_11" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_11_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_11" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_12" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_12_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_12" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_13" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_13_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_13" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_14" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_14_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_14" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_15" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_15_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_15" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_16" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_16_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_16" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_17" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_17_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_17" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_18" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_18_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_18" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_19" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_19_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_19" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_20" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_20_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_20" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_21" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_21_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_21" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_22" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_22_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_22" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_23" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_23_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_23" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_24" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_24_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_24" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_25" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_25_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_25" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_26" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_26_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_26" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_27" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_27_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_27" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_28" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_28_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_28" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_29" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_29_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_29" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_30" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_30_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_30" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_31" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_31_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_31" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_32" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_32_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_32" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_33" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_33_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_33" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_34" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_34_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_34" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_35" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_35_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_35" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_36" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_36_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_36" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_37" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_37_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_37" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_38" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_38_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_38" IS '文件关联元数据值表';

CREATE TABLE "plss-record"."rc_record_metadata_value_39" (
  "record_id" int8 NOT NULL,
  "metadata_value_json" text NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "modified_time" timestamp(6) NOT NULL,
  CONSTRAINT "rc_record_metadata_value_39_pkey" PRIMARY KEY ("record_id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."record_id" IS '文件recordid';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]
';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."modified_time" IS '修改时间';

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_39" IS '文件关联元数据值表';




--知识工程属性配置表
CREATE TABLE "plss-record"."rc_knowledge_attribute" (
                                                        "id" int8 NOT NULL,
                                                        "name" varchar(255) ,
                                                        "value_type" int2,
                                                        "description" varchar(2048) ,
                                                        "create_time" timestamp(6),
                                                        "create_by" int8,
                                                        "update_time" timestamp(6),
                                                        "update_by" int8,
                                                        CONSTRAINT "rc_knowledge_attribute_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."name" IS '属性名';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."value_type" IS '属性值类型 1字符 2数值 3日期';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."description" IS '属性描述';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_attribute" IS '知识工程属性表';


--知识工程概念配置表
CREATE TABLE "plss-record"."rc_knowledge_concept" (
                                                      "id" int8 NOT NULL,
                                                      "name" varchar(255) ,
                                                      "description" varchar(2048) ,
                                                      "create_time" timestamp(6),
                                                      "create_by" int8,
                                                      "update_time" timestamp(6),
                                                      "update_by" int8,
                                                      CONSTRAINT "rc_knowledge_concept_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."name" IS '概念名称';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."description" IS '概念描述';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_concept" IS '知识工程概念表';


--知识工程概念属性表
CREATE TABLE "plss-record"."rc_knowledge_concept_attribute" (
                                                                "id" int8 NOT NULL,
                                                                "concept_id" int8,
                                                                "attribute_id" int8,
                                                                "create_time" timestamp(6),
                                                                "create_by" int8,
                                                                "update_time" timestamp(6),
                                                                "update_by" int8,
                                                                CONSTRAINT "rc_knowledge_concept_attribute_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."concept_id" IS '概念id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."attribute_id" IS '属性id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_concept_attribute" IS '知识工程概念属性表';


--知识工程模型表
CREATE TABLE "plss-record"."rc_knowledge_model" (
                                                    "id" int8 NOT NULL,
                                                    "name" varchar(255) ,
                                                    "description" varchar(2048) ,
                                                    "status" int2,
                                                    "model_setting" text ,
                                                    "create_time" timestamp(6),
                                                    "create_by" int8,
                                                    "update_time" timestamp(6),
                                                    "update_by" int8,
                                                    CONSTRAINT "rc_knowledge_model_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."name" IS '模型名称';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."description" IS '模型描述';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."status" IS '状态 1正常 2删除';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."model_setting" IS '模型配置 json格式包含概念、关系、属性';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_model" IS '知识工程模型表';


--知识工程关系配置表
CREATE TABLE "plss-record"."rc_knowledge_relation" (
                                                       "id" int8 NOT NULL,
                                                       "from_concept_id" int8,
                                                       "to_concept_id" int8,
                                                       "name" varchar(255) ,
                                                       "create_time" timestamp(6),
                                                       "create_by" int8,
                                                       "update_time" timestamp(6),
                                                       "update_by" int8,
                                                       CONSTRAINT "rc_knowledge_relation_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."from_concept_id" IS '主语概念id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."to_concept_id" IS '宾语概念id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."name" IS '关系名称';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_relation" IS '知识工程关系表';


--知识工程关系属性表
CREATE TABLE "plss-record"."rc_knowledge_relation_attribute" (
                                                                 "id" int8 NOT NULL,
                                                                 "relation_id" int8,
                                                                 "attribute_id" int8,
                                                                 "create_time" timestamp(6),
                                                                 "create_by" int8,
                                                                 "update_time" timestamp(6),
                                                                 "update_by" int8,
                                                                 CONSTRAINT "rc_knowledge_relation_attribute_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."relation_id" IS '关系id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."attribute_id" IS '属性id';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."update_by" IS '更新人';

COMMENT ON TABLE "plss-record"."rc_knowledge_relation_attribute" IS '知识工程关系属性表';


--知识工程文档提取表
DROP TABLE IF EXISTS "plss-record".RC_RECORD_KNOWLEDGE;
CREATE TABLE "plss-record"."rc_record_knowledge" (
                                                     "id" int8 NOT NULL,
                                                     "record_id" int8 NOT NULL,
                                                     "title" varchar(255) ,
                                                     "content_json" text ,
                                                     "create_time" timestamp(6),
                                                     "update_time" timestamp(6),
                                                     "model_setting" text ,
                                                     "align_way" int2,
                                                     "update_by" int8,
                                                     CONSTRAINT "rc_record_knowledge_pkey" PRIMARY KEY ("id")
)
;


CREATE INDEX "idx_record_kg_id" ON "plss-record"."rc_record_knowledge" USING btree (
    "record_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."id" IS '主键';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."record_id" IS '文档id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."title" IS '文档标题';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."content_json" IS '知识提取内容';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."update_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."model_setting" IS '模型配置';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."align_way" IS '对齐方式 1自动对齐 2手动对齐';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."update_by" IS '更新人';


--知识工程文档节点属性表
CREATE TABLE "plss-record"."rc_record_knowledge_attribute" (
                                                               "id" int8 NOT NULL,
                                                               "node_id" int8 NOT NULL,
                                                               "attribute" text ,
                                                               "update_time" timestamp(6),
                                                               "update_by" int8,
                                                               "create_by" int8,
                                                               "type" int2 NOT NULL,
                                                               "create_time" timestamp(6),
                                                               CONSTRAINT "rc_record_knowledge_attrubute_pkey" PRIMARY KEY ("id")
)
;


CREATE UNIQUE INDEX "IDX_RECORD_KN_ATT_NODE" ON "plss-record"."rc_record_knowledge_attribute" USING btree (
    "node_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."node_id" IS '图数据库节点id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."attribute" IS '属性json';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."update_by" IS '更新人';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."type" IS '类型 2实例 4关系';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_attribute"."create_time" IS '创建时间';

COMMENT ON TABLE "plss-record"."rc_record_knowledge_attribute" IS '文档属性表';


--知识工程文档关系表
CREATE TABLE "plss-record"."rc_record_knowledge_relation" (
                                                              "id" int8 NOT NULL,
                                                              "record_id" int8,
                                                              "from_concept_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                              "from_concept_value" varchar(1024) COLLATE "pg_catalog"."default" NOT NULL,
                                                              "to_concept_name" varchar(255) COLLATE "pg_catalog"."default",
                                                              "to_concept_value" varchar(1024) COLLATE "pg_catalog"."default",
                                                              "relation_name" varchar(255) COLLATE "pg_catalog"."default",
                                                              "from_node_id" int8 NOT NULL,
                                                              "to_node_id" int8,
                                                              "create_time" timestamp(6),
                                                              "update_time" timestamp(6),
                                                              "update_by" int8,
                                                              "relation_node_id" int8,
                                                              CONSTRAINT "rc_record_knowledge_relation_pkey" PRIMARY KEY ("id")
)
;

CREATE INDEX "idx_record_kn_rel" ON "plss-record"."rc_record_knowledge_relation" USING btree (
    "record_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."record_id" IS '文档id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."from_concept_name" IS '主体概念名称';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."from_concept_value" IS '主体概念实例';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."to_concept_name" IS '客体概念名称';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."to_concept_value" IS '客体概念实例';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."relation_name" IS '关系名称';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."from_node_id" IS '主体节点id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."to_node_id" IS '客体节点id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."update_by" IS '更新人';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_relation"."relation_node_id" IS '关系节点id';


        
--知识工程文档节点冲突处理表
CREATE TABLE "plss-record"."rc_record_knowledge_conflict" (
                                                              "id" int8 NOT NULL,
                                                              "source_id" int8 NOT NULL,
                                                              "target_id" int8 NOT NULL,
                                                              "source_attribute" text COLLATE "pg_catalog"."default",
                                                              "target_attribute" text COLLATE "pg_catalog"."default",
                                                              "merge_attribute" text COLLATE "pg_catalog"."default",
                                                              "operate" int2,
                                                              "create_time" timestamp(6),
                                                              "create_by" int8,
                                                              "update_time" timestamp(6),
                                                              "update_by" int8,
                                                              CONSTRAINT "rc_record_knowledge_conflict_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."source_id" IS '源节点id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."target_id" IS '目标节点id';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."source_attribute" IS '源节点属性';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."target_attribute" IS '目标节点属性';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."merge_attribute" IS '合并后属性';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."operate" IS '操作 1不予合共 2合并冲突';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-record"."rc_record_knowledge_conflict"."update_by" IS '更新人';


--操作日志表添加数据可访问角色字段
ALTER TABLE "plss-system".sys_oper_log ADD role_type varchar(64) ;
COMMENT ON COLUMN "plss-system".sys_oper_log.role_type IS '数据可访问角色类型';
--操作日志表添加备注字段
ALTER TABLE "plss-system".sys_oper_log ADD remark varchar(512) NULL;
COMMENT ON COLUMN "plss-system".sys_oper_log.remark IS '备注';
--操作日志添加操作系统
ALTER TABLE "plss-system".sys_oper_log ADD os varchar(64) NULL;
COMMENT ON COLUMN "plss-system".sys_oper_log.os IS '操作系统';


-- sharding元数据值表任务
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (242453586332677, 2, '元数据值分表分发【每分钟执行一次】', '2024-07-08 13:13:05.046', '2024-07-08 13:13:35.079', 'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'shardingMetadataValue', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-08 13:13:05.046', NULL, 0, 0, 0);

-- 元数据提取节点配置修改
update "plss-record".rc_node set defined_config_json = '{"nodeType":6,"radioBoxButton":{"desc":"","radioBoxList":[{"candidateCode":[601,-1],"code":601,"desc":"ocr提取元数据"},{"candidateCode":[730,-1],"code":-1,"desc":"模型提取元数据"}]},"switchBoxButton":{"candidateCode":[601,-1],"code":601,"desc":"元数据自动提取"}}'
where node_type = 6;
update "plss-record".rc_plan_node set defined_config_json = '{"nodeType":6,"radioBoxButton":{"desc":"","radioBoxList":[{"candidateCode":[601,-1],"code":601,"desc":"ocr提取元数据"},{"candidateCode":[730,-1],"code":-1,"desc":"模型提取元数据"}]},"switchBoxButton":{"candidateCode":[601,-1],"code":601,"desc":"元数据自动提取"}}'
where node_type = 6;

-- 新增“知识提取”节点
INSERT INTO "plss-record".rc_node
(id, name, node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num)
values
(223412614187465, '知识提取', 13, '{"nodeType":13,"radioBoxButton":{"desc":"实体对齐","radioBoxList":[{"candidateCode":[1,-1],"code":1,"desc":"自动对齐"},{"candidateCode":[2,-1],"code":-1,"desc":"手动对齐"}]},"selectBoxButton":{"candidateCode":["-1"],"code":"-1","desc":"知识模型","selectBox":{"-1":"请选择知识模型"}},"switchBoxButton":{"candidateCode":[720,-1],"code":-1,"desc":"知识提取"}}', '6', '6', 1, '该节点开启后，会在文件入库后进行知识提取， 按照知识模型形成知识图谱。', '2024-06-11 15:42:00.000', '2024-06-11 15:42:00.000', 9);

-- 添加知识提取状态字段
ALTER TABLE "plss-record".rc_doc_process ADD knowledge_status int4 NULL;
COMMENT ON COLUMN "plss-record".rc_doc_process.knowledge_status IS '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取';

-- 消息表关键字段加索引
CREATE INDEX record_id_idx ON "plss-system".sys_message USING btree (record_id);
CREATE INDEX type_idx ON "plss-system".sys_message USING btree (type);


-- 添加定时器
INSERT INTO "plss-system".xxl_job_info (id,job_group, job_desc, add_time, update_time,
                                          author,
                                          alarm_email, schedule_type, schedule_conf,
                                          misfire_strategy,
                                          executor_route_strategy, executor_handler,
                                          executor_param,
                                          executor_block_strategy, executor_timeout,
                                          executor_fail_retry_count,
                                          glue_type, glue_source, glue_remark,
                                          glue_updatetime, child_job_id,
                                          trigger_status, trigger_last_time,
                                          trigger_next_time)
VALUES (238283013202245,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '删除已入库的文档入库回调日志【每2小时执行一次】', '2024-07-02 16:41:55',
        '2024-07-02 16:41:55', 'xks', NULL, 'CRON', '0 0 0/2 * * ?', 'DO_NOTHING', 'FIRST',
        'deletePipelineTraceLog', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-02 16:41:55', NULL, 1, 0, 0);

INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (248161322978821, 2, '我的文档重复数据修复', '2024-07-16 14:45:30.243', '2024-07-16 14:45:30.243', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *', 'DO_NOTHING', 'ROUND', 'fixMyFolderForRepeat', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-16 14:45:30.243', NULL, 0, 0, 0);

-- 添加唯一索引
CREATE UNIQUE INDEX rc_plan_node_plan_id_idx ON "plss-record".rc_plan_node (plan_id,node_id);

-- 添加定时器
INSERT INTO "plss-system".xxl_job_info (id,job_group, job_desc, add_time, update_time,
                                        author,
                                        alarm_email, schedule_type, schedule_conf,
                                        misfire_strategy,
                                        executor_route_strategy, executor_handler,
                                        executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count,
                                        glue_type, glue_source, glue_remark,
                                        glue_updatetime, child_job_id,
                                        trigger_status, trigger_last_time,
                                        trigger_next_time)
VALUES (238283013202246,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '文档重新生成封面', '2024-07-16 14:00:00',
        '2024-07-16 14:00:00', 'xks', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST',
        'recordUpdateCover', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-16 14:00:00', NULL, 0, 0, 0);

-- 资源授权记录表 添加资源名称字段
ALTER TABLE "plss-record".rc_resource_permission_log ADD resource_name VARCHAR(1024) NULL;
COMMENT ON COLUMN "plss-record".rc_resource_permission_log.resource_name IS '资源名称';

--新增知识模型管理的菜单
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223250632221381, 223248864592581, '概念库', 1, 'knowledge-concept', 'knowledge/concept', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:04:34.442', '001', '2024-06-11 10:08:47.616', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223253094138565, 223248864592581, '关系库', 2, 'knowledge-relation', 'knowledge/relation', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:09:34.970', '', '2024-06-11 10:09:34.970', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223253491237573, 223248864592581, '属性库', 3, 'knowledge-stats', 'knowledge/stats', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:10:23.443', '', '2024-06-11 10:10:23.443', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223254062097093, 223248864592581, '知识模型配置', 4, 'knowledge-modelconfig', 'knowledge/modelConfig', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:11:33.128', '', '2024-06-11 10:11:33.128', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223248864592581, 0, '知识模型管理', 14, 'knowledge', '', '', '1', '1', 'M', '1', '1', '', 'icon-zhishimoxing', '001', '2024-06-11 10:00:58.670', '001', '2024-06-12 14:52:07.193', '', 2);



INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, sm.menu_id FROM "plss-system".sys_tenant st, "plss-system".sys_menu sm WHERE sm.menu_id in (223248864592581,223254062097093,223253491237573,223253094138565,223250632221381);

--更新初始化角色状态
UPDATE "plss-system".sys_role SET role_name='入库管理员', role_key='', role_sort=0, data_scope='1', menu_check_strictly='1', org_check_strictly='1', status='1', del_flag='1', create_by='admin', create_time='2022-12-18 19:49:16.146', update_by='admin', update_time='2024-05-14 21:13:34.252', remark='系统初始化入库管理员', tenant_id=0, role_type=7 WHERE role_id=7;
UPDATE "plss-system".sys_role SET role_name='普通角色', role_key='common', role_sort=0, data_scope='2', menu_check_strictly='1', org_check_strictly='1', status='1', del_flag='1', create_by='1', create_time='2023-05-19 00:40:18.000', update_by='001', update_time='2024-01-23 16:02:34.038', remark='普通角色', tenant_id=0, role_type=6 WHERE role_id=6;

--更新角色配置
UPDATE "plss-system".sys_config SET config_name='系统初始化角色信息', config_key='sys.init.roleInfo', config_value='[
{"dateType": "1","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[],"delPowerRoleTypeList":[1]},
{"dateType": "2","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "3","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "4","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "5","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[5],"delPowerRoleTypeList":[1]},
{"dateType": "6","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "7","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "98","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "99","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1,2,5],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1,2,5]}
]', config_type='Y', status=1, create_time='2024-01-22 22:35:02.987', create_by='001', update_time='2024-04-03 17:07:22.754', update_by='admin', remark='', element_type=NULL, element_name=NULL WHERE config_key='sys.init.roleInfo';


-- 更新前端配置
UPDATE "plss-system"."sys_config"
SET "config_name"='前端参数配置', "config_key"='sys.front.config', "config_value"='[{"projectId":1,"config":{"platform":"ndrc","showNavTop":false}},{"projectId":5,"config":{"platform":"goxinbu","showFeekBack":{"img":"data:image/png;base64,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","feekbackUrl":"https://f.kdocs.cn/g/A2W3tH5r/"}}},{"projectId":4,"config":{"platform":"pudong","jumpUrl":"https://ythbgptuat.ywxt.sh.cegn.cn/"}},{"projectId":3,"config":{"platform":"neimeng","statisticsTimer":3000,"orgId":1610564159749,"tenantId":1612752027141,"statisticsPath":"statistics_nmg","statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadDocument":{"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".docx,.doc,.wps,.wpt,.xls,.ofd,.pdf,.xlsx,.dot,.dotx,.dotm,.ppt,.pot,.pps,.potm,.ppsm,.txt,.tif,.tiff,.jpg,.jpeg,.png,.zip,.bmp,.html","acceptError":"该格式文件存在风险，无法入库。"},"uploadAttachment":{"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""},"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}]}},{"projectId":0,"config":{"platform":"global","pasteCapture":false,"showArticle":true,"showHistory":true,"uploadDocument":{"multiple":true,"tip":"","maxSize":500,"maxSizeError":"已超出文件上传上限{0}M","accept":".docx,.doc,.wps,.wpt,.xls,.ofd,.pdf,.xlsx,.dot,.dotx,.dotm,.ppt,.pot,.pps,.potm,.ppsm,.txt,.tif,.tiff,.jpg,.jpeg,.png,.zip,.bmp,.html","acceptError":"该格式文件存在风险，无法入库。"},"uploadAttachment":{"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},"uploadSearchText":{"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},"uploadPerson":{"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},"uploadProofread":{"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},"showNavTop":true}}]', "config_type"='Y', "status"=1, "create_time"='2024-04-28 12:37:56.000', "create_by"='001', "update_time"='2024-07-09 14:53:37.770', "update_by"='admin', "remark"='此配置需要全量更新。
platform：平台名称
pasteCapture： 是否拦截粘贴
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
showFeekBack： 是否显示意见反馈
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
uploadDocument：文档入库上传
uploadAttachment:：附件上传
uploadSearchText：以文搜文上传
uploadPerson：个人库上传
uploadProofread：智能校对上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- maxSize：最大大小（MB）
- maxSizeError：超出大小提示
- accept：支持上传格式 
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpApp:扫码登录
isLoginUrl: 登录地址', "element_type"=NULL, "element_name"=NULL
WHERE "config_key"='sys.front.config';

-- 新增操作日志类型配置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(249475818212869, '操作日志类型', 'sys:operLog:type', '[{"code":1,"modeName":"前台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":2,"modeName":"前台-文档操作","bussineType":6007,"remark":"分享了文档#recordId"},{"code":3,"modeName":"前台-文档操作","bussineType":6002,"remark":"复制了文档#recordId的内容"},{"code":4,"modeName":"前台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"},{"code":11,"modeName":"后台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":14,"modeName":"后台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"}]', 'Y', 1, '2024-07-18 11:19:51.090', 'admin', '2024-07-25 13:53:06.335', 'admin', '', 'json', NULL);

--操作日志表添加数据可访问角色字段
ALTER TABLE "plss-system".sys_oper_log ADD role_type varchar(64) ;
COMMENT ON COLUMN "plss-system".sys_oper_log.role_type IS '数据可访问角色类型';

-- 更新定时任务状态
UPDATE "plss-system".xxl_job_info SET trigger_status=1 WHERE id=26;

-- 修改表新增字段 任务执行类型
ALTER TABLE "plss-record"."rc_pipeline_trace_log" 
  ADD COLUMN "event_task" int2;

COMMENT ON COLUMN "plss-record"."rc_pipeline_trace_log"."event_task" IS '事件任务执行:1-提交任务2-回调任务';

-- 数据导出详情表 添加字段
ALTER TABLE "plss-record".rc_export_filter
    ADD COLUMN folder_ids character varying(1024);
COMMENT ON COLUMN "plss-record".rc_export_filter.folder_ids
    IS '目录 ids';

ALTER TABLE IF EXISTS "plss-record".rc_export_filter
    ADD COLUMN category_ids character varying(1024);
COMMENT ON COLUMN "plss-record".rc_export_filter.category_ids
    IS '分类 ids';
    
-- 知识工程模型配置表添加租户id
ALTER TABLE "plss-record"."rc_knowledge_model" ADD "tenant_id" bigint not null default 0;
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."tenant_id" IS '租户id';

```



### 2. 达梦库sql脚本

```sql
-- 其它环境字段补充
ALTER TABLE "plss-system".sys_org ADD file_show_status varchar(1);

COMMENT ON COLUMN "plss-system".sys_org.file_show_status IS '文件展示状态(1展示2不展示)';

ALTER TABLE "plss-system".sys_org ADD area_sign varchar(256);

COMMENT ON COLUMN "plss-system".sys_org.area_sign IS '区域标志';
-- -- 入库日志事件类型和事件状态
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES (244695126124293, '入库日志记录事件类型', 'lib_event_type', '1', '2024-07-11 17:13:30.515911', '001', '2024-07-11 17:13:30.515911', '', '入库日志记录事件类型');
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES (244708067944197, '入库日志记录事件状态', 'lib_event_state', '1', '2024-07-11 17:39:50.326238', '001', '2024-07-11 17:39:50.326238', '', '入库日志记录事件状态');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708617168645, 3, '处理失败', '3', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:57.369', '', '2024-07-11 17:40:57.369', '处理失败');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708457858821, 2, '处理成功', '2', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:37.922', '', '2024-07-11 17:40:37.922', '处理成功');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244708313138949, 1, '处理中', '1', 'lib_event_state', '', 'default', 'N', '1', '001', '2024-07-11 17:40:20.257', '', '2024-07-11 17:40:20.257', '处理中');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244735222458117, 15, '文件拆分', '1704', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 18:35:05.085', '', '2024-07-11 18:35:05.085', '文件拆分');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244735070562053, 14, '生成缩略图', '1501', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 18:34:46.543', '', '2024-07-11 18:34:46.543', '生成缩略图');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244712486258437, 13, '古诗词提取', '707', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:48:49.67', '', '2024-07-11 17:48:49.67', '古诗词提取');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244712152475397, 12, '提取正文处理,不依赖转换服务和ocr提取,直接提取doc或docx', '603', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:48:08.925', '', '2024-07-11 17:48:08.925', '提取正文处理,不依赖转换服务和ocr提取,直接提取doc或docx');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244707478005509, 11, '提取实体', '704', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:38:38.311', '', '2024-07-11 17:38:38.311', '提取实体');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244707333121797, 10, '提取关键词', '703', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:38:20.625', '', '2024-07-11 17:38:20.625', '提取关键词');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244706972436229, 9, '提取分类', '701', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:37:36.596', '', '2024-07-11 17:37:36.596', '提取分类');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244706783643397, 8, '提取摘要', '700', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:37:13.55', '001', '2024-07-11 17:37:42.21998', '提取摘要');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705913440005, 7, '提取元数据', '601', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:35:27.324', '', '2024-07-11 17:35:27.324', '提取元数据');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705755318021, 6, '提取正文处理', '600', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:35:08.022', '', '2024-07-11 17:35:08.022', '提取正文处理');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705251952389, 4, '转换单层OFD', '501', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:34:06.576', '001', '2024-07-11 17:34:31.844357', '转换单层OFD');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244705002235653, 5, 'AI中台提取关键词&实体&分类&摘要&向量化', '799', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:33:36.093', '001', '2024-07-11 17:34:23.947001', 'AI中台提取关键词&实体&分类&摘要&向量化');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244704828376837, 3, '转换双层OFD', '500', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:33:14.87', '001', '2024-07-11 17:34:41.125174', '转换双层OFD');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244699780081413, 2, '通过模型提取元数据', '730', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:22:58.623', '', '2024-07-11 17:22:58.623', '通过模型提取元数据');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244699607246597, 1, '知识工程', '720', 'lib_event_type', '', 'default', 'N', '1', '001', '2024-07-11 17:22:37.525', '', '2024-07-11 17:22:37.525', '知识工程');


-- 驳回原因审核
ALTER TABLE "plss-record"."rc_doc_audit" 
  ADD COLUMN "reject_reason" varchar(516);
COMMENT ON COLUMN "plss-record"."rc_doc_audit"."reject_reason" IS '拒绝:驳回原因';

insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331141, '总书记重要讲话', '1', 106, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331142, '经济', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331143, '论经济建设', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064331144, '论经济新常态与供给侧结构性改革', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339333, '论全面深化改革', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339334, '论新发展理念', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339335, '党建', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339336, '论党对一切工作的领导', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339337, '论党风廉政建设和反腐败斗争', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339338, '论干部队伍建设', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339339, '论坚定理想信念', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339340, '论理论和哲学社会科学工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339341, '论调查研究', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339342, '论全面从严治党', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339343, '论群众工作', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339344, '论新时代中国共产党的历史使命', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339345, '论中国共产党历史', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339346, '论组织工作', '1', 11, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339347, '论“不忘初心、牢记使命”', '1', 12, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339348, '文化', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339349, '论互联网宣传工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339350, '论精神文明建设工作', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339351, '论民族和宗教工作', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339352, '论文化', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339353, '论新闻舆论工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339354, '论宣传思想工作', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339355, '论中国精神', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339356, '论中国梦', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339357, '论“四个自信”', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339358, '国防', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339359, '论海洋强国', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064339360, '论坚持总体国家安全观', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347525, '论强军兴军', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347526, '外交', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347527, '论人类命运共同体', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347528, '论中国特色大国外交', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347529, '论"一带一路"', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347530, '政治', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347531, '论“一国两制”和推进祖国统一', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347532, '论爱国统一战线', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347533, '论发展社会主义民主政治', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347534, '论工人阶级', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347535, '论坚持和发展中国特色社会主义', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347536, '论坚持人民当家作主', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347537, '论社会主义核心价值体系', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347538, '论全面依法治国', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347539, '论深化党和国家机构改革', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347540, '论“五位一体”和“四个全面”', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347541, '论以人民为中心', '1', 11, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347542, '科技', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347543, '论互联网建设与管理', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347544, '论科技创新', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347545, '民生', '1', 8, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347546, '论妇女儿童和妇联工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347547, '论全面建成小康社会', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064347548, '论脱贫攻坚', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355717, '论社会建设和保障民生', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355718, '论卫生和健康工作', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355719, '论尊重和保障人权', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355720, '论“三农”工作和乡村振兴战略', '1', 7, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355721, '生态', '1', 9, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064355722, '论社会主义生态文明建设', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363909, '教育', '1', 10, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363910, '论青年工作', '1', 1, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363911, '论教育工作', '1', 2, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363912, '论人才工作', '1', 3, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363913, '论学习', '1', 4, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363914, '论学习榜样', '1', 5, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);
insert into "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") values (229182064363915, '论马克思', '1', 6, '2024-06-19 19:12:06.195000', '001', '001', '2024-06-19 19:12:06.195000', 1, null, 1, 1610564159749, null, 2, 1);




insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331141, 0, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331142, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331143, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331144, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339333, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339334, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339335, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339336, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339337, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339338, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339339, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339340, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339341, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339342, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339343, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339344, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339345, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339346, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339347, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339348, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339349, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339350, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339351, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339352, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339353, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339354, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339355, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339356, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339357, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339358, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339359, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339360, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347525, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347526, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347527, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347528, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347529, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347530, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347531, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347532, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347533, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347534, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347535, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347536, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347537, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347538, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347539, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347540, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347541, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347542, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347543, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347544, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347545, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347546, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347547, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347548, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355717, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355718, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355719, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355720, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355721, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355722, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363909, 229182064331141, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363910, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363911, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363912, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363913, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363914, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363915, 229182064331141, 2);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331143, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064331144, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339333, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339334, 229182064331142, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339336, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339337, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339338, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339339, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339340, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339341, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339342, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339343, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339344, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339345, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339346, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339347, 229182064339335, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339349, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339350, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339351, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339352, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339353, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339354, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339355, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339356, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339357, 229182064339348, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339359, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064339360, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347525, 229182064339358, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347527, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347528, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347529, 229182064347526, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347531, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347532, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347533, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347534, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347535, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347536, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347537, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347538, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347539, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347540, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347541, 229182064347530, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347543, 229182064347542, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347544, 229182064347542, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347546, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347547, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064347548, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355717, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355718, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355719, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355720, 229182064347545, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064355722, 229182064355721, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363910, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363911, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363912, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363913, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363914, 229182064363909, 1);
insert into "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") values (229182064363915, 229182064363909, 1);


INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name")
VALUES (229146515556741, '总书记讲话分类id', 'eod.chairman.categoryId', '229182064331141', 'Y', 1, '2024-06-19 17:59:46.762000', 'admin', '2024-06-20 10:33:14.420405', 'admin', '', null, null);
-- 入库链路日志记录表
CREATE TABLE "plss-record"."rc_pipeline_trace_log"
(
    "id"            BIGINT                       NOT NULL,
    "record_id"     BIGINT                       NOT NULL,
    "in_param"      TEXT,
    "out_param"     TEXT,
    "event_type"    INT,
    "event_state"   INT,
    "except_reason" TEXT,
    "reason_type"   INT,
    "create_time"   TIMESTAMP(6)                 NOT NULL,
    "modified_time" TIMESTAMP(6)                 NOT NULL,
    CONSTRAINT      "rc_pipeline_trace_log_pkey" NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "plss-record"."rc_pipeline_trace_log" IS '入库流程记录错误日志';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."create_time" IS '创建时间';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."event_state" IS '事件状态1-处理中2-成功3-失败';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."event_type" IS '事件任务';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."except_reason" IS '异常原因';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."id" IS '主键';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."in_param" IS '入参';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."modified_time" IS '修改时间';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."out_param" IS '出参';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."reason_type" IS '原因类型';
COMMENT
ON COLUMN "plss-record"."rc_pipeline_trace_log"."record_id" IS '文件id';

CREATE
OR REPLACE  INDEX "idx_etype" ON "plss-record"."rc_pipeline_trace_log"("event_type" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE
OR REPLACE  INDEX "idx_crt" ON "plss-record"."rc_pipeline_trace_log"("create_time" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE
OR REPLACE  INDEX "idx_rid" ON "plss-record"."rc_pipeline_trace_log"("record_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE
OR REPLACE  INDEX "idx_rid_etype_crt" ON "plss-record"."rc_pipeline_trace_log"("record_id" ASC,"event_type" ASC,"create_time" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

-- 入库链路异常字典配置
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (219214497629381, '入库异常原因类型', 'lib_reason_type', '1', '2024-06-05 17:13:02.233528',
        '001', '2024-06-05 17:13:02.233528', '', '入库异常原因类型');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (219215451989189, 2, '系统内部', '2', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-06-05 17:14:58.730000', '001', '2024-07-11 10:32:17.281893', '系统内部');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (219215299019973, 1, 'AI中台对接', '1', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-06-05 17:14:40.056000', '001', '2024-07-11 13:54:50.852662', 'AI中台对接');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (244497777691269, 3, 'OFD转换对接', '3', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-07-11 10:32:00.128000', '001', '2024-07-11 10:34:37.373842', 'OFD转换对接');
insert into "plss-system"."sys_dict_data" ("dict_code","dict_sort","dict_label","dict_value","dict_type","css_class","list_class","is_default","status","create_by","create_time","update_by","update_time","remark") values (244498231921285, 4, 'OCR对接', '4', 'lib_reason_type', '', 'default', 'N', '1', '001', '2024-07-11 10:32:55.576000', '001', '2024-07-11 13:55:19.964166', 'OCR对接');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (244659545089797, 5, 'API5对接', '5', 'lib_reason_type', '', 'default', 'N', '1', 'admin', '2024-07-11 16:01:07.125', '', '2024-07-11 16:01:07.125', 'API5对接');

--文库列表去掉动态表头resourceStatus字段
DELETE
FROM "plss-system"."sys_table_header" t
WHERE field_key = 'resourceStatus'
  AND "field_key" = 'resourceStatusDesc';
  

-- sharding 元数据值分表
CREATE TABLE "plss-record"."rc_record_metadata_value_0"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_0" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_0"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_1"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_1" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_1"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_2"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_2" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_2"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_3"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_3" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_3"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_4"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_4" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_4"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_5"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_5" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_5"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_6"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_6" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_6"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_7"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_7" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_7"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_8"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_8" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_8"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_9"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_9" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_9"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_10"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_10" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_10"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_11"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_11" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_11"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_12"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_12" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_12"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_13"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_13" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_13"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_14"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_14" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_14"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_15"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_15" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_15"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_16"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_16" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_16"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_17"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_17" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_17"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_18"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_18" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_18"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_19"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_19" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_19"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_20"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_20" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_20"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_21"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_21" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_21"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_22"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_22" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_22"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_23"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_23" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_23"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_24"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_24" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_24"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_25"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_25" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_25"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_26"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_26" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_26"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_27"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_27" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_27"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_28"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_28" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_28"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_29"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_29" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_29"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_30"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_30" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_30"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_31"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_31" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_31"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_32"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_32" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_32"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_33"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_33" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_33"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_34"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_34" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_34"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_35"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_35" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_35"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_36"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_36" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_36"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_37"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_37" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_37"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_38"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_38" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_38"."record_id" IS '文件recordid';

CREATE TABLE "plss-record"."rc_record_metadata_value_39"
(
"record_id" BIGINT NOT NULL,
"metadata_value_json" TEXT NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"modified_time" TIMESTAMP(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("record_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_metadata_value_39" IS '文件关联元数据值表';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."metadata_value_json" IS '文件关联的元数数据json示例[{"createTime":"2024-06-13 16:54:19.89","dataType":2,"id":2804032686853,"mdId":199417399627077,"mdName":"标题","mdValue":"概要设计","modifiedTime":"2024-06-13 16:54:19.89","recordId":2804040896773}]';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."modified_time" IS '修改时间';
COMMENT ON COLUMN "plss-record"."rc_record_metadata_value_39"."record_id" IS '文件recordid';


--知识工程属性配置表
CREATE TABLE "plss-record"."rc_knowledge_attribute"
(
    "id" BIGINT NOT NULL,
    "name" VARCHAR(255),
    "value_type" SMALLINT DEFAULT 1,
    "description" VARCHAR(2048),
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_attribute" IS '知识工程属性表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."description" IS '属性描述';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."name" IS '属性名';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."update_time" IS '更新时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_attribute"."value_type" IS '属性值类型 1字符 2数值 3日期';



--知识工程概念配置表
CREATE TABLE "plss-record"."rc_knowledge_concept"
(
    "id" BIGINT NOT NULL,
    "name" VARCHAR(255),
    "description" VARCHAR(2048),
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_concept" IS '知识工程概念表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."description" IS '概念描述';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."name" IS '概念名称';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept"."update_time" IS '更新时间';


--知识工程概念属性表
CREATE TABLE "plss-record"."rc_knowledge_concept_attribute"
(
    "id" BIGINT NOT NULL,
    "concept_id" BIGINT,
    "attribute_id" BIGINT,
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_concept_attribute" IS '知识工程概念属性表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."attribute_id" IS '属性id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."concept_id" IS '概念id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_concept_attribute"."update_time" IS '更新时间';


--知识工程模型表
CREATE TABLE "plss-record"."rc_knowledge_model"
(
    "id" BIGINT NOT NULL,
    "name" VARCHAR(255),
    "description" VARCHAR(2048),
    "status" SMALLINT,
    "model_setting" TEXT,
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_model" IS '知识工程模型配置表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."description" IS '模型描述';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."model_setting" IS '模型配置 json格式包含概念、关系、属性';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."name" IS '模型名称';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."status" IS '状态 1正常 2删除';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."update_time" IS '更新时间';



--知识工程关系表
CREATE TABLE "plss-record"."rc_knowledge_relation"
(
    "id" BIGINT NOT NULL,
    "from_concept_id" BIGINT,
    "to_concept_id" BIGINT,
    "name" VARCHAR(255),
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_relation" IS '知识工程关系表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."from_concept_id" IS '主语概念id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."name" IS '关系名称';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."to_concept_id" IS '宾语概念id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation"."update_time" IS '更新时间';


--知识工程关系属性表
CREATE TABLE "plss-record"."rc_knowledge_relation_attribute"
(
    "id" BIGINT NOT NULL,
    "relation_id" BIGINT,
    "attribute_id" BIGINT,
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_knowledge_relation_attribute" IS '知识工程关系属性表';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."attribute_id" IS '属性id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."relation_id" IS '关系id';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_knowledge_relation_attribute"."update_time" IS '更新时间';


--知识工程文档提取表
DROP TABLE IF EXISTS "plss-record".RC_RECORD_KNOWLEDGE;
CREATE TABLE "plss-record"."rc_record_knowledge"
(
    "id" BIGINT NOT NULL,
    "record_id" BIGINT,
    "title" VARCHAR(255),
    "content_json" TEXT,
    "create_time" TIMESTAMP(6),
    "update_time" TIMESTAMP(6),
    "model_setting" TEXT,
    "align_way" SMALLINT,
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."align_way" IS '对齐方式 1自动对齐 2手动对齐';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."content_json" IS '知识提取内容';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."model_setting" IS '模型配置';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."record_id" IS '文档id';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."title" IS '文档标题';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_record_knowledge"."update_time" IS '更新时间';


CREATE OR REPLACE  INDEX "idx_kg_record" ON "plss-record"."rc_record_knowledge"("record_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


--知识工程文档节点属性表
CREATE TABLE "plss-record"."rc_record_knowledge_attribute"
(
    "id" BIGINT NOT NULL,
    "node_id" BIGINT,
    "attribute" TEXT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    "create_by" BIGINT,
    "type" INT,
    "create_time" TIMESTAMP(6),
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_knowledge_attribute" IS '文档属性表';


CREATE OR REPLACE UNIQUE  INDEX "IDX_RECORD_KN_ATT" ON "plss-record"."rc_record_knowledge_attribute"("node_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;


--知识工程冲突处理表
CREATE TABLE "plss-record"."rc_record_knowledge_conflict"
(
    "id" BIGINT NOT NULL,
    "source_id" BIGINT NOT NULL,
    "target_id" BIGINT NOT NULL,
    "source_attribute" TEXT,
    "target_attribute" TEXT,
    "merge_attribute" TEXT,
    "operate" INT,
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_knowledge_conflict" IS '文档知识冲突处理表';


--知识工程文档节点关系表
CREATE TABLE "plss-record"."rc_record_knowledge_relation"
(
    "id" BIGINT NOT NULL,
    "record_id" BIGINT,
    "from_concept_name" VARCHAR(255) NOT NULL,
    "from_concept_value" VARCHAR(1024) NOT NULL,
    "to_concept_name" VARCHAR(255),
    "to_concept_value" VARCHAR(1024),
    "relation_name" VARCHAR(255),
    "from_node_id" BIGINT NOT NULL,
    "to_node_id" BIGINT,
    "create_time" TIMESTAMP(6),
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    "relation_node_id" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_record_knowledge_relation" IS '文档知识关系表';


CREATE OR REPLACE  INDEX "IDX_RECORD_KN_REL" ON "plss-record"."rc_record_knowledge_relation"("record_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

--操作日志表添加数据可访问角色字段
ALTER TABLE "plss-system".sys_oper_log ADD role_type varchar(64) ;
COMMENT ON COLUMN "plss-system".sys_oper_log.role_type IS '数据可访问角色类型';
--操作日志表添加备注字段
ALTER TABLE "plss-system".sys_oper_log ADD remark varchar(512) NULL;
COMMENT ON COLUMN "plss-system".sys_oper_log.remark IS '备注';
--操作日志添加操作系统
ALTER TABLE "plss-system".sys_oper_log ADD os varchar(64) NULL;
COMMENT ON COLUMN "plss-system".sys_oper_log.os IS '操作系统';

-- sharding元数据值表任务
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (242453586332677, 2, '元数据值分表分发【每分钟执行一次】', '2024-07-08 13:13:05.046', '2024-07-08 13:13:35.079', 'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'shardingMetadataValue', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-08 13:13:05.046', NULL, 0, 0, 0);

-- 元数据提取节点配置修改
update "plss-record".rc_node set defined_config_json = '{"nodeType":6,"radioBoxButton":{"desc":"","radioBoxList":[{"candidateCode":[601,-1],"code":601,"desc":"ocr提取元数据"},{"candidateCode":[730,-1],"code":-1,"desc":"模型提取元数据"}]},"switchBoxButton":{"candidateCode":[601,-1],"code":601,"desc":"元数据自动提取"}}'
where node_type = 6;
update "plss-record".rc_plan_node set defined_config_json = '{"nodeType":6,"radioBoxButton":{"desc":"","radioBoxList":[{"candidateCode":[601,-1],"code":601,"desc":"ocr提取元数据"},{"candidateCode":[730,-1],"code":-1,"desc":"模型提取元数据"}]},"switchBoxButton":{"candidateCode":[601,-1],"code":601,"desc":"元数据自动提取"}}'
where node_type = 6;

-- 新增“知识提取”节点
INSERT INTO "plss-record".rc_node
(id, "name", node_type, defined_config_json, input_scope, output_scope, status, remark, create_time, modified_time, order_num)
values
(223412614187465, '知识提取', 13, '{"nodeType":13,"radioBoxButton":{"desc":"实体对齐","radioBoxList":[{"candidateCode":[1,-1],"code":1,"desc":"自动对齐"},{"candidateCode":[2,-1],"code":-1,"desc":"手动对齐"}]},"selectBoxButton":{"candidateCode":["-1"],"code":"-1","desc":"知识模型","selectBox":{"-1":"请选择知识模型"}},"switchBoxButton":{"candidateCode":[720,-1],"code":-1,"desc":"知识提取"}}', '6', '6', 1, '该节点开启后，会在文件入库后进行知识提取， 按照知识模型形成知识图谱。', '2024-06-11 15:42:00.000', '2024-06-11 15:42:00.000', 9);

-- 添加知识提取状态字段
ALTER TABLE "plss-record"."rc_doc_process" ADD knowledge_status int NULL;
COMMENT ON COLUMN "plss-record"."rc_doc_process"."knowledge_status" IS '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取';


-- 消息表关键字段加索引
CREATE INDEX record_id_idx ON "plss-system".sys_message (record_id);
CREATE INDEX type_idx ON "plss-system".sys_message (type);

-- 添加定时器
INSERT INTO "plss-system"."xxl_job_info" ("id","job_group", "job_desc", "add_time", "update_time",
                                          "author",
                                          "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy",
                                          "executor_route_strategy", "executor_handler",
                                          "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count",
                                          "glue_type", "glue_source", "glue_remark",
                                          "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (238283013202245,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '删除已入库的文档入库回调日志【每2小时执行一次】', '2024-07-02 16:41:55',
        '2024-07-02 16:41:55', 'xks', NULL, 'CRON', '0 0 0/2 * * ?', 'DO_NOTHING', 'FIRST',
        'deletePipelineTraceLog', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-02 16:41:55', NULL, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (248161322978821, 2, '我的文档重复数据修复', '2024-07-16 14:45:30.243', '2024-07-16 14:45:30.243', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *', 'DO_NOTHING', 'ROUND', 'fixMyFolderForRepeat', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-16 14:45:30.243', NULL, 0, 0, 0);
-- 添加唯一索引
CREATE UNIQUE INDEX RC_PLAN_NODE_PLAN_ID_IDX ON "plss-record"."rc_plan_node" ("plan_id","node_id");

-- 添加定时器
INSERT INTO "plss-system".xxl_job_info (id,job_group, job_desc, add_time, update_time,
                                        author,
                                        alarm_email, schedule_type, schedule_conf,
                                        misfire_strategy,
                                        executor_route_strategy, executor_handler,
                                        executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count,
                                        glue_type, glue_source, glue_remark,
                                        glue_updatetime, child_job_id,
                                        trigger_status, trigger_last_time,
                                        trigger_next_time)
VALUES (238283013202246,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '文档重新生成封面', '2024-07-16 14:00:00',
        '2024-07-16 14:00:00', 'xks', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST',
        'recordUpdateCover', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-07-16 14:00:00', NULL, 0, 0, 0);

-- 资源授权记录表 添加资源名称字段
alter table "plss-record"."rc_resource_permission_log" add column("resource_name" VARCHAR(1024));
comment on column "plss-record"."rc_resource_permission_log"."resource_name" is '资源名称';

--新增知识模型管理的菜单
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223250632221381, 223248864592581, '概念库', 1, 'knowledge-concept', 'knowledge/concept', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:04:34.442', '001', '2024-06-11 10:08:47.616', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223253094138565, 223248864592581, '关系库', 2, 'knowledge-relation', 'knowledge/relation', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:09:34.970', '', '2024-06-11 10:09:34.970', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223253491237573, 223248864592581, '属性库', 3, 'knowledge-stats', 'knowledge/stats', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:10:23.443', '', '2024-06-11 10:10:23.443', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223254062097093, 223248864592581, '知识模型配置', 4, 'knowledge-modelconfig', 'knowledge/modelConfig', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-06-11 10:11:33.128', '', '2024-06-11 10:11:33.128', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(223248864592581, 0, '知识模型管理', 14, 'knowledge', '', '', '1', '1', 'M', '1', '1', '', 'icon-zhishimoxing', '001', '2024-06-11 10:00:58.670', '001', '2024-06-12 14:52:07.193', '', 2);



INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, sm.menu_id FROM "plss-system".sys_tenant st, "plss-system".sys_menu sm WHERE sm.menu_id in (223248864592581,223254062097093,223253491237573,223253094138565,223250632221381);

--更新初始化角色状态
UPDATE "plss-system".sys_role SET role_name='入库管理员', role_key='', role_sort=0, data_scope='1', menu_check_strictly='1', org_check_strictly='1', status='1', del_flag='1', create_by='admin', create_time='2022-12-18 19:49:16.146', update_by='admin', update_time='2024-05-14 21:13:34.252', remark='系统初始化入库管理员', tenant_id=0, role_type=7 WHERE role_id=7;
UPDATE "plss-system".sys_role SET role_name='普通角色', role_key='common', role_sort=0, data_scope='2', menu_check_strictly='1', org_check_strictly='1', status='1', del_flag='1', create_by='1', create_time='2023-05-19 00:40:18.000', update_by='001', update_time='2024-01-23 16:02:34.038', remark='普通角色', tenant_id=0, role_type=6 WHERE role_id=6;

--更新角色配置
UPDATE "plss-system".sys_config SET config_name='系统初始化角色信息', config_key='sys.init.roleInfo', config_value='[
{"dateType": "1","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[],"delPowerRoleTypeList":[1]},
{"dateType": "2","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "3","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "4","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3],"delPowerRoleTypeList":[1]},
{"dateType": "5","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[5],"delPowerRoleTypeList":[1]},
{"dateType": "6","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "7","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "98","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1]},
{"dateType": "99","viewPowerRoleTypeList":[1,2,3,4,5,6,98,99],"editPowerRoleTypeList":[1,2,5],"authPowerRoleTypeList":[3,5],"delPowerRoleTypeList":[1,2,5]}
]', config_type='Y', status=1, create_time='2024-01-22 22:35:02.987', create_by='001', update_time='2024-04-03 17:07:22.754', update_by='admin', remark='', element_type=NULL, element_name=NULL WHERE config_key='sys.init.roleInfo';

-- 更新前端配置
UPDATE "plss-system"."sys_config"
SET "config_name"='前端参数配置', "config_key"='sys.front.config', "config_value"='[{"projectId":1,"config":{"platform":"ndrc","showNavTop":false}},{"projectId":5,"config":{"platform":"goxinbu","showFeekBack":{"img":"data:image/png;base64,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","feekbackUrl":"https://f.kdocs.cn/g/A2W3tH5r/"}}},{"projectId":4,"config":{"platform":"pudong","jumpUrl":"https://ythbgptuat.ywxt.sh.cegn.cn/"}},{"projectId":3,"config":{"platform":"neimeng","statisticsTimer":3000,"orgId":1610564159749,"tenantId":1612752027141,"statisticsPath":"statistics_nmg","statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadDocument":{"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".docx,.doc,.wps,.wpt,.xls,.ofd,.pdf,.xlsx,.dot,.dotx,.dotm,.ppt,.pot,.pps,.potm,.ppsm,.txt,.tif,.tiff,.jpg,.jpeg,.png,.zip,.bmp,.html","acceptError":"该格式文件存在风险，无法入库。"},"uploadAttachment":{"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""},"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}]}},{"projectId":0,"config":{"platform":"global","pasteCapture":false,"showArticle":true,"showHistory":true,"uploadDocument":{"multiple":true,"tip":"","maxSize":500,"maxSizeError":"已超出文件上传上限{0}M","accept":".docx,.doc,.wps,.wpt,.xls,.ofd,.pdf,.xlsx,.dot,.dotx,.dotm,.ppt,.pot,.pps,.potm,.ppsm,.txt,.tif,.tiff,.jpg,.jpeg,.png,.zip,.bmp,.html","acceptError":"该格式文件存在风险，无法入库。"},"uploadAttachment":{"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},"uploadSearchText":{"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},"uploadPerson":{"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},"uploadProofread":{"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},"showNavTop":true}}]', "config_type"='Y', "status"=1, "create_time"='2024-04-28 12:37:56.000', "create_by"='001', "update_time"='2024-07-09 14:53:37.770', "update_by"='admin', "remark"='此配置需要全量更新。
platform：平台名称
pasteCapture： 是否拦截粘贴
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
showFeekBack： 是否显示意见反馈
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
uploadDocument：文档入库上传
uploadAttachment:：附件上传
uploadSearchText：以文搜文上传
uploadPerson：个人库上传
uploadProofread：智能校对上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- maxSize：最大大小（MB）
- maxSizeError：超出大小提示
- accept：支持上传格式 
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpApp:扫码登录
isLoginUrl: 登录地址', "element_type"=NULL, "element_name"=NULL
WHERE "config_key"='sys.front.config';

-- 新增操作日志类型配置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(249475818212869, '操作日志类型', 'sys:operLog:type', '[{"code":1,"modeName":"前台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":2,"modeName":"前台-文档操作","bussineType":6007,"remark":"分享了文档#recordId"},{"code":3,"modeName":"前台-文档操作","bussineType":6002,"remark":"复制了文档#recordId的内容"},{"code":4,"modeName":"前台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"},{"code":11,"modeName":"后台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":14,"modeName":"后台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"}]', 'Y', 1, '2024-07-18 11:19:51.090', 'admin', '2024-07-25 13:53:06.335', 'admin', '', 'json', NULL);

ALTER TABLE "plss-system".sys_logininfor ADD role_type varchar(64) ;
COMMENT ON COLUMN "plss-system".sys_logininfor.role_type IS '数据可访问角色类型';

-- 更新定时任务状态
UPDATE "plss-system".xxl_job_info SET trigger_status=1 WHERE id=26;

-- 修改表新增字段 任务执行类型
alter table "plss-record"."rc_pipeline_trace_log" add column("event_task" INT);

comment on column "plss-record"."rc_pipeline_trace_log"."event_task" is '事件任务执行:1-提交任务2-回调任务';

--- 数据导出详情表 添加字段
ALTER TABLE "plss-record".rc_export_filter
    ADD COLUMN folder_ids VARCHAR(1024);
COMMENT ON COLUMN "plss-record".rc_export_filter.folder_ids
    IS '目录 ids';

ALTER TABLE "plss-record".rc_export_filter
    ADD COLUMN category_ids VARCHAR(1024);
COMMENT ON COLUMN "plss-record".rc_export_filter.category_ids
    IS '分类 ids';
    
-- 知识工程模型配置表添加租户id
ALTER TABLE "plss-record"."rc_knowledge_model" ADD "tenant_id" bigint not null default 0;
COMMENT ON COLUMN "plss-record"."rc_knowledge_model"."tenant_id" IS '租户id';

```


## 二. nacos 配置

### 1. common-redis.yml配置

```yaml
## 在common-redis.yml增量配置如下
spring:
  redis:
    cluster:
      #【！！！增量配置！！！】
      max-redirects: 3000  # 设置最大重定向次数
    #【！！！与password配置平级增量配置！！！】
    timeout: 3000 # 连接超时时间（毫秒）
    # 【！！！与cluster平级lettuce此节点全量覆盖！！！】
    lettuce:
      pool:
        max-active: 500 #连接池最大连接数（使用负数表示没有限制）默认8
        max-wait: -1  #连接池最大阻塞等待时间（使用负数表示没有限制）默认-1
        max-idle: 10 #连接池中的最大空闲连接 默认8
        min-idle: 0 #连接池中的最小空闲连接 默认0
        time-between-eviction-runs: 100 #多长时间进行一次空闲连接的检测（毫秒）默认-1
      cluster:
        refresh:
          adaptive: true
          period: 20 # 20秒自动刷新一次


  ## 【增量】靠左对齐最顶级配置
custom-redisson:
  connect-timeout: 3000
  timeout: 3000
  retry-attempts: 3000
  retry-interval: 3000
  idle-connection-timeout: 10000
  ping-connection-interval: 10000

## 【全量覆盖】jetcache 缓存配置 层级配置在最顶级，即与spring配置节点平级
jetcache: ## 靠左对齐最顶级配置
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
  remote:
    default:
      type: redisson
      redissonClient: redisson
      broadcastChannel: plss
      keyConvertor: fastjson2
      defaultExpireInMillis: 10000
      keyPrefix: PlSS_

```

### 2. common-mq.yml配置

```yaml
## 在common-mq.yml增量配置如下
spring:
  rabbitmq:
    listener:
      simple:
        ## 【！！与acknowledge-mode平级增量配置节点！！！】
        retry:
          enabled: true
          max-attempts: 3000  # 最大重试次数
          initial-interval: 3000  # 重试的初始间隔，单位毫秒
          max-interval: 60000  # 最大重试间隔，单位毫秒
          multiplier: 1.5 # 重试间隔乘数
          stateless: false
```

```yaml
## 在common-server.yml增量配置如下
server: ##  【增量】靠左对齐最顶级配置
  tomcat:
    max-connections: 10000
    accept-count: 1000
    max-keep-alive-requests: 1000
    keep-alive-timeout: 10000
    connection-timeout: 20000
    threads:
      max: 500
      min-spare: 100
```



### 3. plss-record.yml配置

## 新增Datasource的注释，为了根据实际情况来选择使用具体的Datasource
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
        ## 达梦的配置demo,按照实际情况将注释去掉使用，并修改里面的ip port和账号密码
#          driver-class-name: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://ip:端口?schema=""plss-record""&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
        ## pg的jdbc配置demo
#          driver-class-name: org.postgresql.Driver
#          url: **********************************************************************************************************************************************************************************************
#          username: 账号
#          password: 密码
        ## 人大金仓的jdbc配置demo
#          driver-class-name: com.kingbase8.Driver
#          url: *********************************************************,sys_catalog&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=false&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
  ## 分库分表配置【增量配置】
  shardingsphere:
    datasource:
      names: logic-record
      logic-record:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: ${spring.datasource.dynamic.datasource.master.driver-class-name}
        url: ${spring.datasource.dynamic.datasource.master.url}
        username: ${spring.datasource.dynamic.datasource.master.username}
        password: ${spring.datasource.dynamic.datasource.master.password}
        initial-size: ${spring.datasource.dynamic.druid.initial-size}
        min-idle: ${spring.datasource.dynamic.druid.min-idle}
        max-active: ${spring.datasource.dynamic.druid.max-active}
        max-wait: ${spring.datasource.dynamic.druid.max-wait}
        time-between-eviction-runs-millis: ${spring.datasource.dynamic.druid.time-between-eviction-runs-millis}
        min-evictable-idle-time-millis: ${spring.datasource.dynamic.druid.min-evictable-idle-time-millis}
        validation-query: ${spring.datasource.dynamic.druid.validation-query}
        test-while-idle: ${spring.datasource.dynamic.druid.test-while-idle}
        test-on-borrow: ${spring.datasource.dynamic.druid.test-on-borrow}
        test-on-return: ${spring.datasource.dynamic.druid.test-on-return}
        pool-prepared-statements: ${spring.datasource.dynamic.druid.pool-prepared-statements}
        max-pool-prepared-statement-per-connection-size: ${spring.datasource.dynamic.druid.max-pool-prepared-statement-per-connection-size}
        connection-properties:
          druid:
            stat:
              mergeSql: ${spring.datasource.dynamic.druid.connection-properties.druid.stat.mergeSql}
              slowSqlMillis: ${spring.datasource.dynamic.druid.connection-properties.druid.stat.slowSqlMillis}
    props:
      sql-show: true
    rules:
      sharding:
        sharding-algorithms:
          alg_inline_rc_record_metadata_value_recordId:
            props:
              algorithm-expression: rc_record_metadata_value_$->{record_id % 40}
              allow-range-query-with-inline-sharding: true
            type: INLINE
        tables:
          rc_record_metadata_value:
            actual-data-nodes: logic-record.rc_record_metadata_value_$->{0..39}
            table-strategy:
              standard:
                sharding-column: record_id
                sharding-algorithm-name: alg_inline_rc_record_metadata_value_recordId

## 这里的配置全量覆盖thread-pool
thread-pool:
  config:
    data-permission-check:
      core-size: 20
      maximum-pool-size: 50
      keep-alive-time: 30
      blocking-queue-size: 100
    folder-query:
      core-size: 30
      maximum-pool-size: 100
      keep-alive-time: 30
      blocking-queue-size: 2000
    data-storage-warehouse:
      core-size: 20
      maximum-pool-size: 50
      keep-alive-time: 30
      blocking-queue-size: 300
    detail-interface:
      core-size: 20
      maximum-pool-size: 50
      keep-alive-time: 30
      blocking-queue-size: 1000
```

### 4. plss-gateway.yml配置

```yaml
## 在plss-gateway.yml修改原来的配置，将xss开关打开，如果原来已经是true了，就忽略此项改动
# 安全配置
security:
  # 防止XSS攻击
  xss:
    enabled: true
```

### 5. plss-system.yml配置
## 新增Datasource的注释，为了根据实际情况来选择使用具体的Datasource
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          ## 达梦的配置demo,按照实际情况将注释去掉使用，并修改里面的ip port和账号密码
#          driver-class-name: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://ip:端口?schema=""plss-system""&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
          ## pg的jdbc配置demo
#          driver-class-name: org.postgresql.Driver
#          url: **********************************************************************************************************************************************************************************************
#          username: 账号
#          password: 密码
          ## 人大金仓的jdbc配置demo
#          driver-class-name: com.kingbase8.Driver
#          url: *********************************************************,sys_catalog&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=false&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
```

### 6. plss-plugin.yml配置
## 新增Datasource的注释，为了根据实际情况来选择使用具体的Datasource
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          ## 达梦的配置demo,按照实际情况将注释去掉使用，并修改里面的ip port和账号密码
#          driver-class-name: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://ip:端口?schema=""plss-plugin""&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
          ## pg的jdbc配置demo
#          driver-class-name: org.postgresql.Driver
#          url: **********************************************************************************************************************************************************************************************
#          username: 账号
#          password: 密码
          ## 人大金仓的jdbc配置demo
#          driver-class-name: com.kingbase8.Driver
#          url: *********************************************************,sys_catalog&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=false&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
```

### 6. plss-open.yml配置
## 新增Datasource的注释，为了根据实际情况来选择使用具体的Datasource
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          ## 达梦的配置demo,按照实际情况将注释去掉使用，并修改里面的ip port和账号密码
#          driver-class-name: dm.jdbc.driver.DmDriver
#          url: jdbc:dm://ip:端口?schema=""plss-open""&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
          ## pg的jdbc配置demo
#          driver-class-name: org.postgresql.Driver
#          url: ********************************************************************************************************************************************************************************************
#          username: 账号
#          password: 密码
          ## 人大金仓的jdbc配置demo
#          driver-class-name: com.kingbase8.Driver
#          url: *******************************************************,sys_catalog&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=false&useSSL=true&serverTimezone=GMT%2B8
#          username: 账号
#          password: 密码
```