# 上线功能清单

## 一. 数据库脚本

### 1. postgresql 库sql脚本

```sql
-- 文档类型新增内置固定数据字段
ALTER TABLE "plss-record"."rc_record_type"
    ADD COLUMN "fixed_data" int2 DEFAULT 2;

COMMENT
ON COLUMN "plss-record"."rc_record_type"."fixed_data" IS '是否内置固定数据1-内置2-非内置';

-- =================================内置文档类型【范文样例、模板】
INSERT INTO "plss-record"."rc_record_type" ("id", "name", "status", "create_time", "create_by",
                                            "modified_time", "modified_by", "remark", "front_view",
                                            "group_type_id", "fixed_data")
VALUES (187862082546373, '范文样例', 1, '2024-04-22 10:06:23.123', 1191407656453,
        '2024-04-22 10:46:30.476', 1191407656453, '', 1, 2340466262277, 1);
INSERT INTO "plss-record"."rc_record_type" ("id", "name", "status", "create_time", "create_by",
                                            "modified_time", "modified_by", "remark", "front_view",
                                            "group_type_id", "fixed_data")
VALUES (187861709343429, '模板', 1, '2024-04-22 10:05:37.566', 1191407656453,
        '2024-04-22 10:46:41.016', 1191407656453, '', 1, 2340466262277, 1);
-- 内置文档类型【范文样例、模板】关联元数据
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 2471741050885, 1, '^.{0,2048}$', 2, 1160482904581, 0, 1, 1, 1, NULL,
        '{"len":2048,"selectItems":[],"unit":"","valueType":1}', '入库位置', 'rukuweizhi', 'rkwz',
        1, 1, 2, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 3486077161733, 1, '^.{0,2048}$', 1, 1160484769029, 1, 2, 1, 1, NULL,
        '{"len":300,"selectItems":[],"unit":"","valueType":1}', '知识提取', 'zhishitiqu', 'zstq', 1,
        1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121765356037, 8, '^.{0,4096}$', 1, 1160484769029, 2, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体日期',
        'shitiriqi', 'strq', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121757193221, 8, '^.{0,4096}$', 1, 1160484769029, 3, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体地点',
        'shitididian', 'stdd', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121745177861, 8, '^.{0,4096}$', 1, 1160484769029, 4, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体人物',
        'shitirenwu', 'strw', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121736301573, 8, '^.{0,4096}$', 1, 1160484769029, 5, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体机构',
        'shitijigou', 'stjg', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121727166725, 8, '^.{0,4096}$', 1, 1160484769029, 6, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '关键词',
        'guanjianci', 'gjc', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121716339461, 1, '^.{0,4096}$', 1, 1160484769029, 7, 2, 1, 1, NULL,
        '{"len":512,"max":100,"regex":"","selectItems":[],"unit":"","valueType":1}', '系统分类',
        'xitongfenlei', 'xtfl', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 2471741050885, 1, '^.{0,2048}$', 2, 1160482904581, 0, 1, 1, 1, NULL,
        '{"len":2048,"selectItems":[],"unit":"","valueType":1}', '入库位置', 'rukuweizhi', 'rkwz',
        1, 2, 2, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121765356037, 8, '^.{0,4096}$', 1, 1160484769029, 1, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体日期',
        'shitiriqi', 'strq', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121757193221, 8, '^.{0,4096}$', 1, 1160484769029, 2, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体地点',
        'shitididian', 'stdd', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121745177861, 8, '^.{0,4096}$', 1, 1160484769029, 3, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体人物',
        'shitirenwu', 'strw', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121736301573, 8, '^.{0,4096}$', 1, 1160484769029, 4, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体机构',
        'shitijigou', 'stjg', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121727166725, 8, '^.{0,4096}$', 1, 1160484769029, 5, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '关键词',
        'guanjianci', 'gjc', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 3486077161733, 1, '^.{0,2048}$', 1, 1160484769029, 6, 2, 1, 1, NULL,
        '{"len":300,"selectItems":[],"unit":"","valueType":1}', '知识提取', 'zhishitiqu', 'zstq', 1,
        1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121716339461, 1, '^.{0,4096}$', 1, 1160484769029, 7, 2, 1, 1, NULL,
        '{"len":512,"max":100,"regex":"","selectItems":[],"unit":"","valueType":1}', '系统分类',
        'xitongfenlei', 'xtfl', 1, 1, 1, 1, '');

-- ============================内置系统分类数据【模板分类、范文样例分类】
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865553923845, 0, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865654161157, 0, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865779097349, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865779097349, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865965465349, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865965465349, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866513108741, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866513108741, 187865654161157, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866587909893, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866587909893, 187865654161157, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187868906966789, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703771026181, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703771026181, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703842992901, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703842992901, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703933678341, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703933678341, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188704006112005, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188704006112005, 187866587909893, 1);

INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187866587909893, '行政公文', '1', 2, '2024-04-22 10:15:33.091154', '001', '001',
        '2024-04-22 10:15:33.091154', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187866513108741, '事务公文', '1', 1, '2024-04-22 10:15:23.961152', '001', '001',
        '2024-04-22 10:15:23.961152', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865965465349, '行政公文', '1', 2, '2024-04-22 10:14:17.109306', '001', '001',
        '2024-04-22 10:14:17.109306', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865779097349, '事务公文', '1', 1, '2024-04-22 10:13:54.357617', '001', '001',
        '2024-04-22 10:13:54.357617', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865654161157, '范文样例分类', '1', 14, '2024-04-22 10:13:39.106993', '001', '001',
        '2024-04-22 10:13:39.106993', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865553923845, '模板分类', '1', 13, '2024-04-22 10:13:26.866053', '001', '001',
        '2024-04-22 10:13:26.866053', 1, NULL, 1, 636815997974, NULL, 1, 1);

--模板分类 3级分类
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252250966789, '领导讲话类', '1', 1, '2024-03-25 11:03:25.343062', '001', '001', '2024-03-25 11:03:25.343064', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251121413, '工作计划类', '1', 2, '2024-03-25 11:03:25.950922', '001', '001', '2024-03-25 11:03:25.950925', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251273221, '工作总结类', '1', 3, '2024-03-25 11:03:26.542684', '001', '001', '2024-03-25 11:03:26.542688', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251424005, '规章类', '1', 4, '2024-03-25 11:03:27.131435', '001', '001', '2024-03-25 11:03:27.131439', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251576069, '契约类', '1', 5, '2024-03-25 11:03:27.726199', '001', '001', '2024-03-25 11:03:27.726201', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251725317, '书信类', '1', 6, '2024-03-25 11:03:28.308137', '001', '001', '2024-03-25 11:03:28.308139', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251878661, '纪录类', '1', 7, '2024-03-25 11:03:28.907585', '001', '001', '2024-03-25 11:03:28.907589', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252029957, '告启类', '1', 8, '2024-03-25 11:03:29.497978', '001', '001', '2024-03-25 11:03:29.49798', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252180997, '行政问责类', '1', 9, '2024-03-25 11:03:30.089295', '001', '001', '2024-03-25 11:03:30.089298', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252332293, '声明类', '1', 10, '2024-03-25 11:03:30.679844', '001', '001', '2024-03-25 11:03:30.679849', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252483589, '公示类', '1', 11, '2024-03-25 11:03:31.271631', '001', '001', '2024-03-25 11:03:31.271635', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252635397, '规定类', '1', 12, '2024-03-25 11:03:31.863664', '001', '001', '2024-03-25 11:03:31.863668', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252940805, '办法类', '1', 14, '2024-03-25 11:03:33.055939', '001', '001', '2024-03-25 11:03:33.055941', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252253092613, '调研建议类', '1', 15, '2024-03-25 11:03:33.651395', '001', '001', '2024-03-25 11:03:33.651398', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252229884421, '意见', '1', 1, '2024-03-25 11:02:02.995263', '001', '001', '2024-03-25 11:02:02.995265', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230044677, '决议', '1', 2, '2024-03-25 11:02:03.623222', '001', '001', '2024-03-25 11:02:03.623225', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230198021, '纪要', '1', 3, '2024-03-25 11:02:04.219581', '001', '001', '2024-03-25 11:02:04.219585', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230648837, '通知', '1', 4, '2024-03-25 11:02:05.981308', '001', '001', '2024-03-25 11:02:05.981311', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230803461, '通报', '1', 5, '2024-03-25 11:02:06.583675', '001', '001', '2024-03-25 11:02:06.583679', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230955013, '函', '1', 6, '2024-03-25 11:02:07.175854', '001', '001', '2024-03-25 11:02:07.175857', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231108101, '公报', '1', 7, '2024-03-25 11:02:07.772917', '001', '001', '2024-03-25 11:02:07.772921', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231260165, '决定', '1', 8, '2024-03-25 11:02:08.368082', '001', '001', '2024-03-25 11:02:08.368086', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231415301, '公告', '1', 9, '2024-03-25 11:02:08.976545', '001', '001', '2024-03-25 11:02:08.976548', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231570949, '通告', '1', 10, '2024-03-25 11:02:09.582549', '001', '001', '2024-03-25 11:02:09.582551', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231726085, '报告', '1', 11, '2024-03-25 11:02:10.190352', '001', '001', '2024-03-25 11:02:10.190354', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231887109, '议案', '1', 12, '2024-03-25 11:02:10.817475', '001', '001', '2024-03-25 11:02:10.817477', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232044037, '请示', '1', 13, '2024-03-25 11:02:11.431715', '001', '001', '2024-03-25 11:02:11.431717', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232198149, '批复', '1', 14, '2024-03-25 11:02:12.034201', '001', '001', '2024-03-25 11:02:12.034204', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232353797, '命令', '1', 15, '2024-03-25 11:02:12.641761', '001', '001', '2024-03-25 11:02:12.641764', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252788997, '启事类', '1', 13, '2024-03-25 11:03:32.465', '001', '001', '2024-05-09 17:59:51.144', 1, NULL, 1, 1610564159749, NULL, 2, 1);


--模板分类 3级分类关联关系
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 187865965465349, 1);


--范文样例分类 3级分类
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416825367877, '书信', '1', 1, '2024-05-08 17:54:33.985167', '001', '001', '2024-05-08 17:54:33.985167', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416832412997, '倡议', '1', 2, '2024-05-08 17:54:35.022099', '001', '001', '2024-05-08 17:54:35.022099', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416839015749, '党史党建', '1', 3, '2024-05-08 17:54:35.814852', '001', '001', '2024-05-08 17:54:35.814852', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416844856645, '发言稿', '1', 4, '2024-05-08 17:54:36.540893', '001', '001', '2024-05-08 17:54:36.540893', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416851549509, '宣传文案', '1', 5, '2024-05-08 17:54:37.309382', '001', '001', '2024-05-08 17:54:37.309382', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416861535557, '工作材料', '1', 6, '2024-05-08 17:54:38.559658', '001', '001', '2024-05-08 17:54:38.559658', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416868547909, '心得体会', '1', 7, '2024-05-08 17:54:39.417911', '001', '001', '2024-05-08 17:54:39.417911', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416875035973, '总结', '1', 8, '2024-05-08 17:54:40.221609', '001', '001', '2024-05-08 17:54:40.221609', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416881343813, '指导意见', '1', 9, '2024-05-08 17:54:41.009276', '001', '001', '2024-05-08 17:54:41.009276', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416886635845, '方案', '1', 10, '2024-05-08 17:54:41.644069', '001', '001', '2024-05-08 17:54:41.644069', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416892992837, '经验交流', '1', 11, '2024-05-08 17:54:42.371715', '001', '001', '2024-05-08 17:54:42.371715', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416899980613, '观后感', '1', 12, '2024-05-08 17:54:43.158561', '001', '001', '2024-05-08 17:54:43.158561', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416907509061, '调研报告', '1', 13, '2024-05-08 17:54:44.165319', '001', '001', '2024-05-08 17:54:44.165319', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416913964357, '其他', '1', 14, '2024-05-08 17:54:44.972071', '001', '001', '2024-05-08 17:54:44.972071', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417374084421, '公告', '1', 1, '2024-05-08 17:55:41.137945', '001', '001', '2024-05-08 17:55:41.137945', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417383529797, '公报', '1', 2, '2024-05-08 17:55:42.311793', '001', '001', '2024-05-08 17:55:42.311793', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417389477189, '决定', '1', 3, '2024-05-08 17:55:43.025594', '001', '001', '2024-05-08 17:55:43.025594', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417394687301, '决议', '1', 4, '2024-05-08 17:55:43.668866', '001', '001', '2024-05-08 17:55:43.668866', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417399627077, '函', '1', 5, '2024-05-08 17:55:44.28136', '001', '001', '2024-05-08 17:55:44.28136', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417404656965, '命令', '1', 6, '2024-05-08 17:55:44.892907', '001', '001', '2024-05-08 17:55:44.892907', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417410809157, '意见', '1', 7, '2024-05-08 17:55:45.583931', '001', '001', '2024-05-08 17:55:45.583931', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417416543557, '批复', '1', 8, '2024-05-08 17:55:46.325714', '001', '001', '2024-05-08 17:55:46.325714', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417421917509, '报告', '1', 9, '2024-05-08 17:55:46.992287', '001', '001', '2024-05-08 17:55:46.992287', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417427152197, '纪要', '1', 10, '2024-05-08 17:55:47.62701', '001', '001', '2024-05-08 17:55:47.62701', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417432436037, '议案', '1', 11, '2024-05-08 17:55:48.271558', '001', '001', '2024-05-08 17:55:48.271558', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417437523269, '请示', '1', 12, '2024-05-08 17:55:48.899847', '001', '001', '2024-05-08 17:55:48.899847', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417442913605, '通告', '1', 13, '2024-05-08 17:55:49.550388', '001', '001', '2024-05-08 17:55:49.550388', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417448131909, '通报', '1', 14, '2024-05-08 17:55:50.197746', '001', '001', '2024-05-08 17:55:50.197746', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417453604165, '通知', '1', 15, '2024-05-08 17:55:50.864525', '001', '001', '2024-05-08 17:55:50.864525', 1, NULL, 1, 1610564159749, NULL, 2, 1);


--范文样例分类 3级分类关联关系
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 187866587909893, 1);



--用户表添加默认租户
ALTER TABLE "plss-system".sys_user
    ADD default_tenant_id int8 NULL;
COMMENT
ON COLUMN "plss-system".sys_user.default_tenant_id IS '默认租户id，可以为空';

--删除菜单表遗留的定时任务的数据
delete
from "plss-system"."sys_menu"
where parent_id = 2669934091781;


--修复修改租户信息导致的组织机构的和用户组织机构关系数据异常的问题
update
    "plss-system"."sys_org" s
set s.tenant_id = (select tenant_id from "plss-system"."sys_org" p where p.org_id = s.parent_id)
where s.tenant_id is null
  and s.org_type = '2';

update "plss-system"."sys_user_org" sur
set sur.tenant_id =
        (select p.tenant_id
         from "plss-system"."sys_org" p,
              "plss-system"."sys_org" s
         where p.org_id = s.parent_id
           and s.tenant_id is null
           and s.org_type = '2'
           and s.org_id = sur.org_id)
where org_id in
      (select org_id from "plss-system"."sys_org" where tenant_id is null and org_type = '2');

--插入用户批量操作按钮，组织机构导入导出按钮
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190099403927045, 1033070990853, '批量操作', 8, '', '', '', '2', '1', 'F', '1', '1',
        'system:user:patchhandle', '#', '001', '2024-04-25 13:58:13.643', '001',
        '2024-04-25 14:00:26.894', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190098293059077, 1032351195909, '导出', 4, '', '', '', '2', '1', 'F', '1', '1',
        'system:org:export', '#', '001', '2024-04-25 13:55:58.040', '', '2024-04-25 13:55:58.040',
        '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190098022960645, 1032351195909, '导入', 3, '', '', '', '2', '1', 'F', '1', '1',
        'system:org:import', '#', '001', '2024-04-25 13:55:25.072', '', '2024-04-25 13:55:25.072',
        '', 2);

--给有用户管理菜单权限的租户添加用户批量操作的按钮权限
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190099403927045
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1033070990853;

--给有组织机构管理菜单权限的租户添加导入导出按钮权限
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190098293059077
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1032351195909;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190098022960645
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1032351195909;

--给有用户管理菜单权限的角色添加用户批量操作的按钮权限
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190099403927045
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1033070990853;

--给有组织机构管理菜单权限的角色添加导入导出按钮权限
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190098293059077
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1032351195909;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190098022960645
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1032351195909;

--新增  文档类型-范文样例-模板 的配置，
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(188580695171845, '文档类型-范文样例-模板', 'document.type.limittype', '[{
  "id": 187862082546373,
  "name": "范文样例",
  "status": 1,
  "createTime": "2024-04-22 10:06:23",
"catetoryId":"187865654161157",
  "createBy": 1191407656453,
  "modifiedTime": "2024-04-22 10:46:30",
  "modifiedBy": 1191407656453,
  "remark": "",
  "frontView": 1,
  "groupTypeId": 2340466262277,
  "fixedData": 1
},{
  "id": 187861709343429,
  "name": "模板",
  "status": 1,
  "createTime": "2024-04-22 10:05:37",
"catetoryId":"187865553923845",
  "createBy": 1191407656453,
  "modifiedTime": "2024-04-22 10:46:41",
  "modifiedBy": 1191407656453,
  "remark": "",
  "frontView": 1,
  "groupTypeId": 2340466262277,
  "fixedData": 1
}]', 'Y', 1, '2024-04-23 10:28:24.391', '001', '2024-04-23 14:23:15.602', '001', '范文样例-模板id值设置', NULL, NULL);

--库类型字典添加  文档类型-范文样例-模板  的字典值
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(188035124694789, 4, '模板库', '5', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-04-22 15:58:26.432', '', '2024-04-22 15:58:26.432', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(188035246878469, 5, '范文样例库', '6', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-04-22 15:58:41.347', '', '2024-04-22 15:58:41.347', '');

--原工作库更名为业务库
update "plss-system"."sys_dict_data" set dict_label = '业务库' where dict_code = 4658919742725;

--更新字典值
UPDATE "plss-system".sys_dict_data SET dict_value='5',  status='1' WHERE dict_code=188035246878469;
UPDATE "plss-system".sys_dict_data SET dict_value='4',  status='1' WHERE dict_code=188035124694789;
UPDATE "plss-system".sys_dict_data SET dict_value='3',  status='2' WHERE dict_code=4658928325893;
UPDATE "plss-system".sys_dict_data SET dict_value='2',  status='2' WHERE dict_code=4658923971589;
UPDATE "plss-system".sys_dict_data SET dict_value='1',  status='1' WHERE dict_code=4658919742725;

--全局移除按钮更名为全库移除
UPDATE "plss-system".sys_menu SET menu_name='全库移除'  WHERE menu_id=4035862272005;


--更新所有菜单的icon图标
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033087273221;
UPDATE "plss-system".sys_menu SET  icon = 'docType' WHERE menu_id =680207111173;
UPDATE "plss-system".sys_menu SET  icon = 'docType' WHERE menu_id =1031626438917;
UPDATE "plss-system".sys_menu SET  icon = 'edit' WHERE menu_id =699369213445;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =105;
UPDATE "plss-system".sys_menu SET  icon = 'icon-xitong' WHERE menu_id =1;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033070990853;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033335487237;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033482585605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =788959642629;
UPDATE "plss-system".sys_menu SET  icon = 'icon-yonghu' WHERE menu_id =1032745234437;
UPDATE "plss-system".sys_menu SET  icon = 'icon-rizhi' WHERE menu_id =1033218025477;
UPDATE "plss-system".sys_menu SET  icon = 'icon-zuzhijiagouguanli' WHERE menu_id =1032351195909;
UPDATE "plss-system".sys_menu SET  icon = 'icon-biaoqianguanli' WHERE menu_id =2206732500229;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2206751772677;
UPDATE "plss-system".sys_menu SET  icon = 'icon-wenkuguanli' WHERE menu_id =695650007301;
UPDATE "plss-system".sys_menu SET  icon = 'icon-rukuguanli' WHERE menu_id =1097331411461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2804600262917;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =102;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2804605053701;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715883757061;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715888027653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715891596805;
UPDATE "plss-system".sys_menu SET  icon = 'icon-minganci' WHERE menu_id =2804594100997;
UPDATE "plss-system".sys_menu SET  icon = 'icon-zuhu' WHERE menu_id =1185128382981;
UPDATE "plss-system".sys_menu SET  icon = 'build' WHERE menu_id =2804631968005;
UPDATE "plss-system".sys_menu SET  icon = 'education' WHERE menu_id =2715879278341;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1097415414533;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2669934091781;
UPDATE "plss-system".sys_menu SET  icon = 'documentation' WHERE menu_id =2715874094853;
UPDATE "plss-system".sys_menu SET  icon = 'icon-jieyueguanli' WHERE menu_id =2932868278789;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3331323127557;
UPDATE "plss-system".sys_menu SET  icon = 'journal' WHERE menu_id =3946810468869;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3947066090757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866184399109;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091322456325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091485816325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866189144325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867109873669;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867116717573;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867122862853;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867132472837;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867136146437;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867141308677;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867488198661;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867349619205;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867360055301;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867374213637;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867381804805;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867398950149;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867412990981;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867423019269;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867468217093;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866068407557;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091306296069;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091308461573;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091317844229;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867427001605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867440394757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867492055301;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867494969605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2845358664709;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867513291013;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867516845061;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867524563717;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867534052101;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867541541381;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867553717765;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868017813253;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868022305797;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868027036421;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867557989637;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868043954181;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868049243141;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868054677509;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868059498245;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868085615877;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868089044485;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868117296901;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868378096133;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868397821189;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868723818757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868727505413;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868731898117;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868765636613;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868770451461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868775889669;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868784745221;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3869284419333;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3869299902469;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867364889349;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882394998789;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882406483973;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882825979653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882415630597;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012615359749;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012621021445;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012648108293;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012651475205;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012664635653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012771723013;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012783068933;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4107133079045;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4035862272005;
UPDATE "plss-system".sys_menu SET  icon = 'icon-quanxian' WHERE menu_id =4969222513925;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969231897093;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969264544517;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969269602053;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969273612805;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969282418693;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969287104773;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969291539461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969296229637;
UPDATE "plss-system".sys_menu SET  icon = 'list' WHERE menu_id =183031739862597;
UPDATE "plss-system".sys_menu SET  icon = 'icon-wenjianguanli' WHERE menu_id =183072446312005;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190099403927045;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190098293059077;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190098022960645;

-- 20240506 范文样例和模板库的【行政公文】改为【法定公文】
UPDATE "plss-system"."sys_category" SET "name" = '法定公文' WHERE "name" = '行政公文';

```

### 2. 达梦库sql脚本

```sql
-- 文档类型新增内置固定数据字段
alter table "plss-record"."rc_record_type" add column("fixed_data" INT default (2));

comment
on column "plss-record"."rc_record_type"."fixed_data" is '是否内置固定数据1-内置2-非内置';

-- =================================内置文档类型【范文样例、模板】
INSERT INTO "plss-record"."rc_record_type" ("id", "name", "status", "create_time", "create_by",
                                            "modified_time", "modified_by", "remark", "front_view",
                                            "group_type_id", "fixed_data")
VALUES (187862082546373, '范文样例', 1, '2024-04-22 10:06:23.123', 1191407656453,
        '2024-04-22 10:46:30.476', 1191407656453, '', 1, 2340466262277, 1);
INSERT INTO "plss-record"."rc_record_type" ("id", "name", "status", "create_time", "create_by",
                                            "modified_time", "modified_by", "remark", "front_view",
                                            "group_type_id", "fixed_data")
VALUES (187861709343429, '模板', 1, '2024-04-22 10:05:37.566', 1191407656453,
        '2024-04-22 10:46:41.016', 1191407656453, '', 1, 2340466262277, 1);
-- 内置文档类型【范文样例、模板】关联元数据
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 2471741050885, 1, '^.{0,2048}$', 2, 1160482904581, 0, 1, 1, 1, NULL,
        '{"len":2048,"selectItems":[],"unit":"","valueType":1}', '入库位置', 'rukuweizhi', 'rkwz',
        1, 1, 2, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 3486077161733, 1, '^.{0,2048}$', 1, 1160484769029, 1, 2, 1, 1, NULL,
        '{"len":300,"selectItems":[],"unit":"","valueType":1}', '知识提取', 'zhishitiqu', 'zstq', 1,
        1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121765356037, 8, '^.{0,4096}$', 1, 1160484769029, 2, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体日期',
        'shitiriqi', 'strq', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121757193221, 8, '^.{0,4096}$', 1, 1160484769029, 3, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体地点',
        'shitididian', 'stdd', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121745177861, 8, '^.{0,4096}$', 1, 1160484769029, 4, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体人物',
        'shitirenwu', 'strw', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121736301573, 8, '^.{0,4096}$', 1, 1160484769029, 5, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体机构',
        'shitijigou', 'stjg', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121727166725, 8, '^.{0,4096}$', 1, 1160484769029, 6, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '关键词',
        'guanjianci', 'gjc', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187861709343429, 1121716339461, 1, '^.{0,4096}$', 1, 1160484769029, 7, 2, 1, 1, NULL,
        '{"len":512,"max":100,"regex":"","selectItems":[],"unit":"","valueType":1}', '系统分类',
        'xitongfenlei', 'xtfl', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 2471741050885, 1, '^.{0,2048}$', 2, 1160482904581, 0, 1, 1, 1, NULL,
        '{"len":2048,"selectItems":[],"unit":"","valueType":1}', '入库位置', 'rukuweizhi', 'rkwz',
        1, 2, 2, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121765356037, 8, '^.{0,4096}$', 1, 1160484769029, 1, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体日期',
        'shitiriqi', 'strq', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121757193221, 8, '^.{0,4096}$', 1, 1160484769029, 2, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体地点',
        'shitididian', 'stdd', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121745177861, 8, '^.{0,4096}$', 1, 1160484769029, 3, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体人物',
        'shitirenwu', 'strw', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121736301573, 8, '^.{0,4096}$', 1, 1160484769029, 4, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '实体机构',
        'shitijigou', 'stjg', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121727166725, 8, '^.{0,4096}$', 1, 1160484769029, 5, 2, 1, 1, NULL,
        '{"len":4096,"max":100,"regex":"","selectItems":[],"unit":"","valueType":8}', '关键词',
        'guanjianci', 'gjc', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 3486077161733, 1, '^.{0,2048}$', 1, 1160484769029, 6, 2, 1, 1, NULL,
        '{"len":300,"selectItems":[],"unit":"","valueType":1}', '知识提取', 'zhishitiqu', 'zstq', 1,
        1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (187862082546373, 1121716339461, 1, '^.{0,4096}$', 1, 1160484769029, 7, 2, 1, 1, NULL,
        '{"len":512,"max":100,"regex":"","selectItems":[],"unit":"","valueType":1}', '系统分类',
        'xitongfenlei', 'xtfl', 1, 1, 1, 1, '');

-- 内置系统分类数据【模板分类、范文样例分类】
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865553923845, 0, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865654161157, 0, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865779097349, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865779097349, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865965465349, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187865965465349, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866513108741, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866513108741, 187865654161157, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866587909893, 0, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187866587909893, 187865654161157, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (187868906966789, 187865553923845, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703771026181, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703771026181, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703842992901, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703842992901, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703933678341, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188703933678341, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188704006112005, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance")
VALUES (188704006112005, 187866587909893, 1);

INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187866587909893, '行政公文', '1', 2, '2024-04-22 10:15:33.091154', '001', '001',
        '2024-04-22 10:15:33.091154', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187866513108741, '事务公文', '1', 1, '2024-04-22 10:15:23.961152', '001', '001',
        '2024-04-22 10:15:23.961152', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865965465349, '行政公文', '1', 2, '2024-04-22 10:14:17.109306', '001', '001',
        '2024-04-22 10:14:17.109306', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865779097349, '事务公文', '1', 1, '2024-04-22 10:13:54.357617', '001', '001',
        '2024-04-22 10:13:54.357617', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865654161157, '范文样例分类', '1', 14, '2024-04-22 10:13:39.106993', '001', '001',
        '2024-04-22 10:13:39.106993', 1, NULL, 1, 636815997974, NULL, 1, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time",
                                          "create_by", "update_by", "update_time", "ctype",
                                          "remark", "visit_type", "org_id", "image_url",
                                          "fixed_data", "prohibit_state")
VALUES (187865553923845, '模板分类', '1', 13, '2024-04-22 10:13:26.866053', '001', '001',
        '2024-04-22 10:13:26.866053', 1, NULL, 1, 636815997974, NULL, 1, 1);

--模板分类 3级分类
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252250966789, '领导讲话类', '1', 1, '2024-03-25 11:03:25.343062', '001', '001', '2024-03-25 11:03:25.343064', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251121413, '工作计划类', '1', 2, '2024-03-25 11:03:25.950922', '001', '001', '2024-03-25 11:03:25.950925', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251273221, '工作总结类', '1', 3, '2024-03-25 11:03:26.542684', '001', '001', '2024-03-25 11:03:26.542688', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251424005, '规章类', '1', 4, '2024-03-25 11:03:27.131435', '001', '001', '2024-03-25 11:03:27.131439', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251576069, '契约类', '1', 5, '2024-03-25 11:03:27.726199', '001', '001', '2024-03-25 11:03:27.726201', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251725317, '书信类', '1', 6, '2024-03-25 11:03:28.308137', '001', '001', '2024-03-25 11:03:28.308139', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252251878661, '纪录类', '1', 7, '2024-03-25 11:03:28.907585', '001', '001', '2024-03-25 11:03:28.907589', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252029957, '告启类', '1', 8, '2024-03-25 11:03:29.497978', '001', '001', '2024-03-25 11:03:29.49798', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252180997, '行政问责类', '1', 9, '2024-03-25 11:03:30.089295', '001', '001', '2024-03-25 11:03:30.089298', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252332293, '声明类', '1', 10, '2024-03-25 11:03:30.679844', '001', '001', '2024-03-25 11:03:30.679849', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252483589, '公示类', '1', 11, '2024-03-25 11:03:31.271631', '001', '001', '2024-03-25 11:03:31.271635', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252635397, '规定类', '1', 12, '2024-03-25 11:03:31.863664', '001', '001', '2024-03-25 11:03:31.863668', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252940805, '办法类', '1', 14, '2024-03-25 11:03:33.055939', '001', '001', '2024-03-25 11:03:33.055941', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252253092613, '调研建议类', '1', 15, '2024-03-25 11:03:33.651395', '001', '001', '2024-03-25 11:03:33.651398', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252229884421, '意见', '1', 1, '2024-03-25 11:02:02.995263', '001', '001', '2024-03-25 11:02:02.995265', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230044677, '决议', '1', 2, '2024-03-25 11:02:03.623222', '001', '001', '2024-03-25 11:02:03.623225', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230198021, '纪要', '1', 3, '2024-03-25 11:02:04.219581', '001', '001', '2024-03-25 11:02:04.219585', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230648837, '通知', '1', 4, '2024-03-25 11:02:05.981308', '001', '001', '2024-03-25 11:02:05.981311', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230803461, '通报', '1', 5, '2024-03-25 11:02:06.583675', '001', '001', '2024-03-25 11:02:06.583679', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252230955013, '函', '1', 6, '2024-03-25 11:02:07.175854', '001', '001', '2024-03-25 11:02:07.175857', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231108101, '公报', '1', 7, '2024-03-25 11:02:07.772917', '001', '001', '2024-03-25 11:02:07.772921', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231260165, '决定', '1', 8, '2024-03-25 11:02:08.368082', '001', '001', '2024-03-25 11:02:08.368086', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231415301, '公告', '1', 9, '2024-03-25 11:02:08.976545', '001', '001', '2024-03-25 11:02:08.976548', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231570949, '通告', '1', 10, '2024-03-25 11:02:09.582549', '001', '001', '2024-03-25 11:02:09.582551', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231726085, '报告', '1', 11, '2024-03-25 11:02:10.190352', '001', '001', '2024-03-25 11:02:10.190354', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252231887109, '议案', '1', 12, '2024-03-25 11:02:10.817475', '001', '001', '2024-03-25 11:02:10.817477', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232044037, '请示', '1', 13, '2024-03-25 11:02:11.431715', '001', '001', '2024-03-25 11:02:11.431717', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232198149, '批复', '1', 14, '2024-03-25 11:02:12.034201', '001', '001', '2024-03-25 11:02:12.034204', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252232353797, '命令', '1', 15, '2024-03-25 11:02:12.641761', '001', '001', '2024-03-25 11:02:12.641764', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (5252252788997, '启事类', '1', 13, '2024-03-25 11:03:32.465', '001', '001', '2024-05-09 17:59:51.144', 1, NULL, 1, 1610564159749, NULL, 2, 1);


--模板分类 3级分类关联关系
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252250966789, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251121413, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251273221, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251424005, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251576069, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251725317, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252251878661, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252029957, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252180997, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252332293, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252483589, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252635397, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252788997, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252252940805, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252253092613, 187865779097349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252229884421, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230044677, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230198021, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230648837, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230803461, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252230955013, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231108101, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231260165, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231415301, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231570949, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231726085, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252231887109, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232044037, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232198149, 187865965465349, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 187865553923845, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (5252232353797, 187865965465349, 1);


--范文样例分类 3级分类
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416825367877, '书信', '1', 1, '2024-05-08 17:54:33.985167', '001', '001', '2024-05-08 17:54:33.985167', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416832412997, '倡议', '1', 2, '2024-05-08 17:54:35.022099', '001', '001', '2024-05-08 17:54:35.022099', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416839015749, '党史党建', '1', 3, '2024-05-08 17:54:35.814852', '001', '001', '2024-05-08 17:54:35.814852', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416844856645, '发言稿', '1', 4, '2024-05-08 17:54:36.540893', '001', '001', '2024-05-08 17:54:36.540893', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416851549509, '宣传文案', '1', 5, '2024-05-08 17:54:37.309382', '001', '001', '2024-05-08 17:54:37.309382', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416861535557, '工作材料', '1', 6, '2024-05-08 17:54:38.559658', '001', '001', '2024-05-08 17:54:38.559658', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416868547909, '心得体会', '1', 7, '2024-05-08 17:54:39.417911', '001', '001', '2024-05-08 17:54:39.417911', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416875035973, '总结', '1', 8, '2024-05-08 17:54:40.221609', '001', '001', '2024-05-08 17:54:40.221609', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416881343813, '指导意见', '1', 9, '2024-05-08 17:54:41.009276', '001', '001', '2024-05-08 17:54:41.009276', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416886635845, '方案', '1', 10, '2024-05-08 17:54:41.644069', '001', '001', '2024-05-08 17:54:41.644069', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416892992837, '经验交流', '1', 11, '2024-05-08 17:54:42.371715', '001', '001', '2024-05-08 17:54:42.371715', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416899980613, '观后感', '1', 12, '2024-05-08 17:54:43.158561', '001', '001', '2024-05-08 17:54:43.158561', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416907509061, '调研报告', '1', 13, '2024-05-08 17:54:44.165319', '001', '001', '2024-05-08 17:54:44.165319', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199416913964357, '其他', '1', 14, '2024-05-08 17:54:44.972071', '001', '001', '2024-05-08 17:54:44.972071', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417374084421, '公告', '1', 1, '2024-05-08 17:55:41.137945', '001', '001', '2024-05-08 17:55:41.137945', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417383529797, '公报', '1', 2, '2024-05-08 17:55:42.311793', '001', '001', '2024-05-08 17:55:42.311793', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417389477189, '决定', '1', 3, '2024-05-08 17:55:43.025594', '001', '001', '2024-05-08 17:55:43.025594', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417394687301, '决议', '1', 4, '2024-05-08 17:55:43.668866', '001', '001', '2024-05-08 17:55:43.668866', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417399627077, '函', '1', 5, '2024-05-08 17:55:44.28136', '001', '001', '2024-05-08 17:55:44.28136', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417404656965, '命令', '1', 6, '2024-05-08 17:55:44.892907', '001', '001', '2024-05-08 17:55:44.892907', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417410809157, '意见', '1', 7, '2024-05-08 17:55:45.583931', '001', '001', '2024-05-08 17:55:45.583931', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417416543557, '批复', '1', 8, '2024-05-08 17:55:46.325714', '001', '001', '2024-05-08 17:55:46.325714', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417421917509, '报告', '1', 9, '2024-05-08 17:55:46.992287', '001', '001', '2024-05-08 17:55:46.992287', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417427152197, '纪要', '1', 10, '2024-05-08 17:55:47.62701', '001', '001', '2024-05-08 17:55:47.62701', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417432436037, '议案', '1', 11, '2024-05-08 17:55:48.271558', '001', '001', '2024-05-08 17:55:48.271558', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417437523269, '请示', '1', 12, '2024-05-08 17:55:48.899847', '001', '001', '2024-05-08 17:55:48.899847', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417442913605, '通告', '1', 13, '2024-05-08 17:55:49.550388', '001', '001', '2024-05-08 17:55:49.550388', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417448131909, '通报', '1', 14, '2024-05-08 17:55:50.197746', '001', '001', '2024-05-08 17:55:50.197746', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system"."sys_category" ("id", "name", "status", "order_by", "create_time", "create_by", "update_by", "update_time", "ctype", "remark", "visit_type", "org_id", "image_url", "fixed_data", "prohibit_state") VALUES (199417453604165, '通知', '1', 15, '2024-05-08 17:55:50.864525', '001', '001', '2024-05-08 17:55:50.864525', 1, NULL, 1, 1610564159749, NULL, 2, 1);


--范文样例分类 3级分类关联关系
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416825367877, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416832412997, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416839015749, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416844856645, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416851549509, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416861535557, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416868547909, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416875035973, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416881343813, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416886635845, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416892992837, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416899980613, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416907509061, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199416913964357, 187866513108741, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417374084421, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417383529797, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417389477189, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417394687301, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417399627077, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417404656965, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417410809157, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417416543557, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417421917509, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417427152197, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417432436037, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417437523269, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417442913605, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417448131909, 187866587909893, 1);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 0, 3);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 187865654161157, 2);
INSERT INTO "plss-system"."sys_category_relation" ("descendant_id", "ancestor_id", "distance") VALUES (199417453604165, 187866587909893, 1);


--用户表添加默认租户
ALTER TABLE "plss-system".sys_user
    ADD default_tenant_id BIGINT NULL;
COMMENT
ON COLUMN "plss-system".sys_user.default_tenant_id IS '默认租户id，可以为空';

--删除菜单表遗留的定时任务的数据
delete
from "plss-system"."sys_menu"
where parent_id = 2669934091781;

--修复修改租户信息导致的组织机构的和用户组织机构关系数据异常的问题
update
    "plss-system"."sys_org" s
set s.tenant_id = (select tenant_id from "plss-system"."sys_org" p where p.org_id = s.parent_id)
where s.tenant_id is null
  and s.org_type = '2';

update "plss-system"."sys_user_org" sur
set sur.tenant_id =
        (select p.tenant_id
         from "plss-system"."sys_org" p,
              "plss-system"."sys_org" s
         where p.org_id = s.parent_id
           and s.tenant_id is null
           and s.org_type = '2'
           and s.org_id = sur.org_id)
where org_id in
      (select org_id from "plss-system"."sys_org" where tenant_id is null and org_type = '2');

--插入用户批量操作按钮，组织机构导入导出按钮
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190099403927045, 1033070990853, '批量操作', 8, '', '', '', '2', '1', 'F', '1', '1',
        'system:user:patchhandle', '#', '001', '2024-04-25 13:58:13.643', '001',
        '2024-04-25 14:00:26.894', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190098293059077, 1032351195909, '导出', 4, '', '', '', '2', '1', 'F', '1', '1',
        'system:org:export', '#', '001', '2024-04-25 13:55:58.040', '', '2024-04-25 13:55:58.040',
        '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (190098022960645, 1032351195909, '导入', 3, '', '', '', '2', '1', 'F', '1', '1',
        'system:org:import', '#', '001', '2024-04-25 13:55:25.072', '', '2024-04-25 13:55:25.072',
        '', 2);

--给有用户管理菜单权限的租户添加用户批量操作的按钮权限
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190099403927045
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1033070990853;

--给有组织机构管理菜单权限的租户添加导入导出按钮权限
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190098293059077
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1032351195909;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT stm.tenant_id, 190098022960645
FROM "plss-system".sys_tenant_menu stm
WHERE stm.menu_id = 1032351195909;

--给有用户管理菜单权限的角色添加用户批量操作的按钮权限
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190099403927045
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1033070990853;

--给有组织机构管理菜单权限的角色添加导入导出按钮权限
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190098293059077
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1032351195909;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT stm.role_id, 190098022960645
FROM "plss-system".sys_role_menu stm
WHERE stm.menu_id = 1032351195909;

--新增  文档类型-范文样例-模板 的配置，
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(188580695171845, '文档类型-范文样例-模板', 'document.type.limittype', '[{
  "id": 187862082546373,
  "name": "范文样例",
  "status": 1,
  "createTime": "2024-04-22 10:06:23",
"catetoryId":"187865654161157",
  "createBy": 1191407656453,
  "modifiedTime": "2024-04-22 10:46:30",
  "modifiedBy": 1191407656453,
  "remark": "",
  "frontView": 1,
  "groupTypeId": 2340466262277,
  "fixedData": 1
},{
  "id": 187861709343429,
  "name": "模板",
  "status": 1,
  "createTime": "2024-04-22 10:05:37",
"catetoryId":"187865553923845",
  "createBy": 1191407656453,
  "modifiedTime": "2024-04-22 10:46:41",
  "modifiedBy": 1191407656453,
  "remark": "",
  "frontView": 1,
  "groupTypeId": 2340466262277,
  "fixedData": 1
}]', 'Y', 1, '2024-04-23 10:28:24.391', '001', '2024-04-23 14:23:15.602', '001', '范文样例-模板id值设置', NULL, NULL);

--库类型字典添加  文档类型-范文样例-模板  的字典值
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(188035124694789, 4, '模板库', '5', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-04-22 15:58:26.432', '', '2024-04-22 15:58:26.432', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(188035246878469, 5, '范文样例库', '6', 'rc_repo_type', '', 'default', 'N', '1', '001', '2024-04-22 15:58:41.347', '', '2024-04-22 15:58:41.347', '');

--原工作库更名为业务库
update "plss-system"."sys_dict_data" set dict_label = '业务库' where dict_code = 4658919742725;

--更新字典值
UPDATE "plss-system".sys_dict_data SET dict_value='5',  status='1' WHERE dict_code=188035246878469;
UPDATE "plss-system".sys_dict_data SET dict_value='4',  status='1' WHERE dict_code=188035124694789;
UPDATE "plss-system".sys_dict_data SET dict_value='3',  status='2' WHERE dict_code=4658928325893;
UPDATE "plss-system".sys_dict_data SET dict_value='2',  status='2' WHERE dict_code=4658923971589;
UPDATE "plss-system".sys_dict_data SET dict_value='1',  status='1' WHERE dict_code=4658919742725;


--全局移除按钮更名为全库移除
UPDATE "plss-system".sys_menu SET menu_name='全库移除'  WHERE menu_id=4035862272005;

--更新所有菜单的icon图标
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033087273221;
UPDATE "plss-system".sys_menu SET  icon = 'docType' WHERE menu_id =680207111173;
UPDATE "plss-system".sys_menu SET  icon = 'docType' WHERE menu_id =1031626438917;
UPDATE "plss-system".sys_menu SET  icon = 'edit' WHERE menu_id =699369213445;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =105;
UPDATE "plss-system".sys_menu SET  icon = 'icon-xitong' WHERE menu_id =1;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033070990853;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033335487237;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1033482585605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =788959642629;
UPDATE "plss-system".sys_menu SET  icon = 'icon-yonghu' WHERE menu_id =1032745234437;
UPDATE "plss-system".sys_menu SET  icon = 'icon-rizhi' WHERE menu_id =1033218025477;
UPDATE "plss-system".sys_menu SET  icon = 'icon-zuzhijiagouguanli' WHERE menu_id =1032351195909;
UPDATE "plss-system".sys_menu SET  icon = 'icon-biaoqianguanli' WHERE menu_id =2206732500229;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2206751772677;
UPDATE "plss-system".sys_menu SET  icon = 'icon-wenkuguanli' WHERE menu_id =695650007301;
UPDATE "plss-system".sys_menu SET  icon = 'icon-rukuguanli' WHERE menu_id =1097331411461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2804600262917;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =102;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2804605053701;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715883757061;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715888027653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2715891596805;
UPDATE "plss-system".sys_menu SET  icon = 'icon-minganci' WHERE menu_id =2804594100997;
UPDATE "plss-system".sys_menu SET  icon = 'icon-zuhu' WHERE menu_id =1185128382981;
UPDATE "plss-system".sys_menu SET  icon = 'build' WHERE menu_id =2804631968005;
UPDATE "plss-system".sys_menu SET  icon = 'education' WHERE menu_id =2715879278341;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =1097415414533;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2669934091781;
UPDATE "plss-system".sys_menu SET  icon = 'documentation' WHERE menu_id =2715874094853;
UPDATE "plss-system".sys_menu SET  icon = 'icon-jieyueguanli' WHERE menu_id =2932868278789;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3331323127557;
UPDATE "plss-system".sys_menu SET  icon = 'journal' WHERE menu_id =3946810468869;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3947066090757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866184399109;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091322456325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091485816325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866189144325;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867109873669;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867116717573;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867122862853;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867132472837;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867136146437;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867141308677;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867488198661;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867349619205;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867360055301;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867374213637;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867381804805;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867398950149;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867412990981;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867423019269;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867468217093;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3866068407557;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091306296069;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091308461573;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3091317844229;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867427001605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867440394757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867492055301;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867494969605;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =2845358664709;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867513291013;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867516845061;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867524563717;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867534052101;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867541541381;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867553717765;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868017813253;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868022305797;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868027036421;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867557989637;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868043954181;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868049243141;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868054677509;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868059498245;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868085615877;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868089044485;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868117296901;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868378096133;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868397821189;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868723818757;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868727505413;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868731898117;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868765636613;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868770451461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868775889669;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3868784745221;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3869284419333;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3869299902469;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3867364889349;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882394998789;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882406483973;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882825979653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =3882415630597;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012615359749;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012621021445;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012648108293;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012651475205;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012664635653;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012771723013;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4012783068933;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4107133079045;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4035862272005;
UPDATE "plss-system".sys_menu SET  icon = 'icon-quanxian' WHERE menu_id =4969222513925;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969231897093;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969264544517;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969269602053;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969273612805;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969282418693;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969287104773;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969291539461;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =4969296229637;
UPDATE "plss-system".sys_menu SET  icon = 'list' WHERE menu_id =183031739862597;
UPDATE "plss-system".sys_menu SET  icon = 'icon-wenjianguanli' WHERE menu_id =183072446312005;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190099403927045;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190098293059077;
UPDATE "plss-system".sys_menu SET  icon = '#' WHERE menu_id =190098022960645;

-- 20240506 范文样例和模板库的【行政公文】改为【法定公文】
UPDATE "plss-system"."sys_category" SET "name" = '法定公文' WHERE "name" = '行政公文';
```

## xxl自增id优化

### pg

```sql

DROP TABLE IF EXISTS "plss-system"."xxl_job_group";
CREATE TABLE "plss-system"."xxl_job_group"
(
    "id"           int8         NOT NULL,
    "app_name"     varchar(200) NOT NULL,
    "title"        varchar(200) NOT NULL,
    "address_type" int4         NOT NULL DEFAULT 0,
    "address_list" varchar(512),
    "update_time"  timestamp(6),
    CONSTRAINT "xxl_job_group_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_group"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."app_name" IS '执行器AppName';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."title" IS '执行器名称';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."address_type" IS '执行器地址类型：0=自动注册、1=手动录入';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."address_list" IS '执行器地址列表，多地址逗号分隔';

COMMENT
ON TABLE "plss-system"."xxl_job_group" IS '任务分组表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_info";
CREATE TABLE "plss-system"."xxl_job_info"
(
    "id"                        int8        NOT NULL,
    "job_group"                 int8        NOT NULL,
    "job_desc"                  varchar(255),
    "add_time"                  timestamp(6),
    "update_time"               timestamp(6),
    "author"                    varchar(64),
    "alarm_email"               varchar(255),
    "schedule_type"             varchar(50),
    "schedule_conf"             varchar(128),
    "misfire_strategy"          varchar(50),
    "executor_route_strategy"   varchar(50),
    "executor_handler"          varchar(255),
    "executor_param"            varchar(512),
    "executor_block_strategy"   varchar(50),
    "executor_timeout"          int4        NOT NULL DEFAULT 0,
    "executor_fail_retry_count" int4        NOT NULL DEFAULT 0,
    "glue_type"                 varchar(50) NOT NULL,
    "glue_source"               text,
    "glue_remark"               varchar(128),
    "glue_updatetime"           timestamp(6),
    "child_job_id"              varchar(255),
    "trigger_status"            int4        NOT NULL DEFAULT 0,
    "trigger_last_time"         int8        NOT NULL DEFAULT 0,
    "trigger_next_time"         int8        NOT NULL DEFAULT 0,
    CONSTRAINT "xxl_job_info_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_info"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."job_group" IS '执行器主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."job_desc" IS '任务描述';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."add_time" IS '任务创建时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."update_time" IS '任务更新时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."author" IS '作者';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."alarm_email" IS '报警邮件';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."schedule_type" IS '调度类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."schedule_conf" IS '调度配置，值含义取决于调度类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."misfire_strategy" IS '调度过期策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_route_strategy" IS '执行器路由策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_handler" IS '执行器任务handler';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_param" IS '执行器任务参数';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_block_strategy" IS '阻塞处理策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_timeout" IS '任务执行超时时间，单位秒';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_fail_retry_count" IS '失败重试次数';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_type" IS 'GLUE类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_source" IS 'GLUE源代码';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_remark" IS 'GLUE备注';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_updatetime" IS 'GLUE更新时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."child_job_id" IS '子任务ID，多个逗号分隔';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_status" IS '调度状态：0-停止，1-运行';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_last_time" IS '上次调度时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_next_time" IS '下次调度时间';

COMMENT
ON TABLE "plss-system"."xxl_job_info" IS '任务信息表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_log";
CREATE TABLE "plss-system"."xxl_job_log"
(
    "id"                        int8 NOT NULL,
    "job_group"                 int8 NOT NULL,
    "job_id"                    int8 NOT NULL,
    "executor_address"          varchar(255),
    "executor_handler"          varchar(255),
    "executor_param"            varchar(512),
    "executor_sharding_param"   varchar(20),
    "executor_fail_retry_count" int4 NOT NULL DEFAULT 0,
    "trigger_time"              timestamp(6),
    "trigger_code"              int4 NOT NULL DEFAULT 0,
    "trigger_msg"               text,
    "handle_time"               timestamp(6),
    "handle_code"               int4 NOT NULL,
    "handle_msg"                text,
    "alarm_status"              int4 NOT NULL DEFAULT 0,
    CONSTRAINT "xxl_job_log_pkey" PRIMARY KEY ("id")
);


CREATE INDEX "i_handle_code" ON "plss-system"."xxl_job_log" ("handle_code");

CREATE INDEX "i_trigger_time" ON "plss-system"."xxl_job_log" ("trigger_time");

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."job_group" IS '执行器主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."job_id" IS '任务，主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_address" IS '执行器地址，本次执行的地址';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_handler" IS '执行器任务handler';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_param" IS '执行器任务参数';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_sharding_param" IS '执行器任务分片参数，格式如 1/2';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_fail_retry_count" IS '失败重试次数';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_time" IS '调度-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_code" IS '调度-结果';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_msg" IS '调度-日志';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_time" IS '执行-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_code" IS '执行-状态';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_msg" IS '执行-日志';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."alarm_status" IS '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败';

COMMENT
ON TABLE "plss-system"."xxl_job_log" IS '任务日志表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_log_report";
CREATE TABLE "plss-system"."xxl_job_log_report"
(
    "id"            int8 NOT NULL,
    "trigger_day"   timestamp(6),
    "running_count" int4 NOT NULL DEFAULT 0,
    "suc_count"     int4 NOT NULL DEFAULT 0,
    "fail_count"    int4 NOT NULL DEFAULT 0,
    "update_time"   timestamp(6),
    CONSTRAINT "xxl_job_log_report_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."trigger_day" IS '调度-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."running_count" IS '运行中-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."suc_count" IS '执行成功-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."fail_count" IS '执行失败-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."update_time" IS '更新时间';


DROP TABLE IF EXISTS "plss-system"."xxl_job_logglue";
CREATE TABLE "plss-system"."xxl_job_logglue"
(
    "id"          int8         NOT NULL,
    "job_id"      int8         NOT NULL,
    "glue_type"   varchar(50),
    "glue_source" text,
    "glue_remark" varchar(128) NOT NULL,
    "add_time"    timestamp(6),
    "update_time" timestamp(6),
    CONSTRAINT "xxl_job_logglue_pkey" PRIMARY KEY ("id")
);

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."job_id" IS '任务，主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_type" IS 'GLUE类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_source" IS 'GLUE源代码';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_remark" IS 'GLUE备注';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."add_time" IS '创建时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."update_time" IS '修改时间';

COMMENT
ON TABLE "plss-system"."xxl_job_logglue" IS '任务GLUE日志表';


DROP TABLE IF EXISTS "plss-system"."xxl_job_registry";
CREATE TABLE "plss-system"."xxl_job_registry"
(
    "id"             int8         NOT NULL,
    "registry_group" varchar(255) NOT NULL,
    "registry_key"   varchar(255) NOT NULL,
    "registry_value" varchar(255) NOT NULL,
    "update_time"    timestamp(6) NOT NULL,
    CONSTRAINT "xxl_job_registry_pkey" PRIMARY KEY ("id")
);


CREATE INDEX "i_g_k_v" ON "plss-system"."xxl_job_registry" ("registry_group", "registry_key", "registry_value");

CREATE INDEX "i_u" ON "plss-system"."xxl_job_registry" ("update_time");

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_group" IS '注册分组';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_key" IS '注册键';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_value" IS '注册值';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."update_time" IS '更新时间';

COMMENT
ON TABLE "plss-system"."xxl_job_registry" IS '任务注册表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_user";
CREATE TABLE "plss-system"."xxl_job_user"
(
    "id"         int8        NOT NULL,
    "username"   varchar(50) NOT NULL,
    "password"   varchar(50) NOT NULL,
    "role"       int4        NOT NULL,
    "permission" varchar(255),
    CONSTRAINT "xxl_job_user_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "i_username" ON "plss-system"."xxl_job_user" ("username");

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."username" IS '账号';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."password" IS '密码';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."role" IS '角色：0-普通用户、1-管理员';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."permission" IS '权限：执行器ID列表，多个逗号分割';

COMMENT
ON TABLE "plss-system"."xxl_job_user" IS '任务用户表';


INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (2, 'plss_record', '文件服务', 0, null, '2024-04-22 10:46:34');
INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (3, 'plss-system', 'system定时任务执行器', 0, null, '2024-04-22 10:46:34');
INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (191033526781957, 'plss_search', '搜索服务', 0, null, '2024-04-26 21:38:42');


INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (191035844814853, 191033526781957, '清理es存在但是db不存在的脏数据', '2024-04-26 21:43:25',
        '2024-04-26 21:43:25', '杨俊', null, 'CRON', '0 0 0 1 * ?', 'DO_NOTHING', 'FIRST',
        'cleanRecordInEsNotInDb', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null,
        '2024-04-26 21:43:25', null, 0, 0, 0);


INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (10, 2, '操作日志归档', '2024-03-28 16:41:55', '2024-03-28 16:41:55', 'fs', null, 'CRON',
        '0 10 0 L * ?', 'DO_NOTHING', 'FIRST', 'archiveOperationLog', null, 'SERIAL_EXECUTION', 0,
        0, 'BEAN', null, null, '2024-03-28 16:41:55', null, 1, 0, 0);
INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (9, 2, '登录日志归档', '2024-03-28 16:41:55', '2024-03-28 16:41:55', 'fs', null, 'CRON',
        '0 10 0 L * ?', 'DO_NOTHING', 'FIRST', 'archiveLoginLog', null, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', null, null, '2024-03-28 16:41:55', null, 1, 0, 0);
INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (12, 2, '借阅归还', '2024-01-04 20:46:47', '2024-01-04 20:46:47', 'admin', null, 'CRON',
        '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'borrowReturn', null, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', null, null, '2024-01-04 20:46:47', null, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (25, 2, '数据大屏统计标签【每分钟执行一次】', '2024-04-17 14:53:49', '2024-04-17 14:53:57',
        'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'statisticTagJob', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-04-17 14:53:49', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (13, 2, '全量修复已入库数据同步ES中【每分钟执行一次】', '2024-01-09 18:56:01',
        '2024-01-09 18:56:01', 'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'resetFinishStorage', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-01-09 18:56:01', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (11, 2, 'AI重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:46:23', '2024-01-04 20:46:23',
        'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'resetAiRecordFinishStorage',
        NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:46:23', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (8, 2, 'OCR提取元数据重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:44:23',
        '2024-01-04 20:44:23', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetMetadata', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-01-04 20:44:23', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (15, 2, 'OCR提取正文重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:45:01',
        '2024-01-04 20:45:01', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetText', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:45:01',
        NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (16, 2, 'OCR转双侧OFD重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:45:40',
        '2024-01-04 20:45:40', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetOfd', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:45:40',
        NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (24, 2, '修复停滞于处理中状态的文档【每天0点执行一次】', '2024-04-09 16:41:55',
        '2024-04-09 16:41:55', 'xks', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST',
        'repairDocProcessStatus', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-04-09 16:41:55', NULL, 1, 0, 0);
        
insert into "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") values (26, 2, '统计日访问量[每天凌晨执行一次]', '2024-03-18 21:26:40.659000', '2024-03-18 21:26:45.930000', '吴梦蝶', null, 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FIRST', 'statisticVisitInfoJob', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-03-18 21:26:40.659000', null, 0, 0, 0);
        


```

### 达梦sql

```sql
DROP TABLE IF EXISTS "plss-system"."xxl_job_group";
CREATE TABLE "plss-system"."xxl_job_group"
(
    "id"           bigint       NOT NULL,
    "app_name"     varchar(200) NOT NULL,
    "title"        varchar(200) NOT NULL,
    "address_type" int          NOT NULL DEFAULT 0,
    "address_list" varchar(512),
    "update_time"  timestamp,
    CONSTRAINT "xxl_job_group_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_group"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."app_name" IS '执行器AppName';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."title" IS '执行器名称';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."address_type" IS '执行器地址类型：0=自动注册、1=手动录入';

COMMENT
ON COLUMN "plss-system"."xxl_job_group"."address_list" IS '执行器地址列表，多地址逗号分隔';

COMMENT
ON TABLE "plss-system"."xxl_job_group" IS '任务分组表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_info";
CREATE TABLE "plss-system"."xxl_job_info"
(
    "id"                        BIGINT      NOT NULL,
    "job_group"                 BIGINT      NOT NULL,
    "job_desc"                  varchar(255),
    "add_time"                  timestamp,
    "update_time"               timestamp,
    "author"                    varchar(64),
    "alarm_email"               varchar(255),
    "schedule_type"             varchar(50),
    "schedule_conf"             varchar(128),
    "misfire_strategy"          varchar(50),
    "executor_route_strategy"   varchar(50),
    "executor_handler"          varchar(255),
    "executor_param"            varchar(512),
    "executor_block_strategy"   varchar(50),
    "executor_timeout"          int         NOT NULL DEFAULT 0,
    "executor_fail_retry_count" int         NOT NULL DEFAULT 0,
    "glue_type"                 varchar(50) NOT NULL,
    "glue_source"               text,
    "glue_remark"               varchar(128),
    "glue_updatetime"           timestamp(6),
    "child_job_id"              varchar(255),
    "trigger_status"            int         NOT NULL DEFAULT 0,
    "trigger_last_time"         BIGINT      NOT NULL DEFAULT 0,
    "trigger_next_time"         BIGINT      NOT NULL DEFAULT 0,
    CONSTRAINT "xxl_job_info_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_info"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."job_group" IS '执行器主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."job_desc" IS '任务描述';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."add_time" IS '任务创建时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."update_time" IS '任务更新时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."author" IS '作者';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."alarm_email" IS '报警邮件';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."schedule_type" IS '调度类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."schedule_conf" IS '调度配置，值含义取决于调度类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."misfire_strategy" IS '调度过期策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_route_strategy" IS '执行器路由策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_handler" IS '执行器任务handler';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_param" IS '执行器任务参数';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_block_strategy" IS '阻塞处理策略';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_timeout" IS '任务执行超时时间，单位秒';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."executor_fail_retry_count" IS '失败重试次数';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_type" IS 'GLUE类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_source" IS 'GLUE源代码';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_remark" IS 'GLUE备注';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."glue_updatetime" IS 'GLUE更新时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."child_job_id" IS '子任务ID，多个逗号分隔';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_status" IS '调度状态：0-停止，1-运行';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_last_time" IS '上次调度时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_info"."trigger_next_time" IS '下次调度时间';

COMMENT
ON TABLE "plss-system"."xxl_job_info" IS '任务信息表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_log";
CREATE TABLE "plss-system"."xxl_job_log"
(
    "id"                        BIGINT NOT NULL,
    "job_group"                 BIGINT NOT NULL,
    "job_id"                    BIGINT NOT NULL,
    "executor_address"          varchar(255),
    "executor_handler"          varchar(255),
    "executor_param"            varchar(512),
    "executor_sharding_param"   varchar(20),
    "executor_fail_retry_count" int    NOT NULL DEFAULT 0,
    "trigger_time"              timestamp,
    "trigger_code"              int    NOT NULL DEFAULT 0,
    "trigger_msg"               text,
    "handle_time"               timestamp,
    "handle_code"               int    NOT NULL,
    "handle_msg"                text,
    "alarm_status"              int    NOT NULL DEFAULT 0,
    CONSTRAINT "xxl_job_log_pkey" PRIMARY KEY ("id")
);


CREATE INDEX "i_handle_code" ON "plss-system"."xxl_job_log" ("handle_code");

CREATE INDEX "i_trigger_time" ON "plss-system"."xxl_job_log" ("trigger_time");

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."job_group" IS '执行器主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."job_id" IS '任务，主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_address" IS '执行器地址，本次执行的地址';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_handler" IS '执行器任务handler';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_param" IS '执行器任务参数';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_sharding_param" IS '执行器任务分片参数，格式如 1/2';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."executor_fail_retry_count" IS '失败重试次数';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_time" IS '调度-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_code" IS '调度-结果';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."trigger_msg" IS '调度-日志';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_time" IS '执行-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_code" IS '执行-状态';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."handle_msg" IS '执行-日志';

COMMENT
ON COLUMN "plss-system"."xxl_job_log"."alarm_status" IS '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败';

COMMENT
ON TABLE "plss-system"."xxl_job_log" IS '任务日志表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_log_report";
CREATE TABLE "plss-system"."xxl_job_log_report"
(
    "id"            BIGINT NOT NULL,
    "trigger_day"   timestamp,
    "running_count" int    NOT NULL DEFAULT 0,
    "suc_count"     int    NOT NULL DEFAULT 0,
    "fail_count"    int    NOT NULL DEFAULT 0,
    "update_time"   timestamp,
    CONSTRAINT "xxl_job_log_report_pkey" PRIMARY KEY ("id")
);


COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."trigger_day" IS '调度-时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."running_count" IS '运行中-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."suc_count" IS '执行成功-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."fail_count" IS '执行失败-日志数量';

COMMENT
ON COLUMN "plss-system"."xxl_job_log_report"."update_time" IS '更新时间';


DROP TABLE IF EXISTS "plss-system"."xxl_job_logglue";
CREATE TABLE "plss-system"."xxl_job_logglue"
(
    "id"          BIGINT       NOT NULL,
    "job_id"      BIGINT       NOT NULL,
    "glue_type"   varchar(50),
    "glue_source" text,
    "glue_remark" varchar(128) NOT NULL,
    "add_time"    timestamp,
    "update_time" timestamp,
    CONSTRAINT "xxl_job_logglue_pkey" PRIMARY KEY ("id")
);

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."job_id" IS '任务，主键ID';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_type" IS 'GLUE类型';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_source" IS 'GLUE源代码';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."glue_remark" IS 'GLUE备注';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."add_time" IS '创建时间';

COMMENT
ON COLUMN "plss-system"."xxl_job_logglue"."update_time" IS '修改时间';

COMMENT
ON TABLE "plss-system"."xxl_job_logglue" IS '任务GLUE日志表';


DROP TABLE IF EXISTS "plss-system"."xxl_job_registry";
CREATE TABLE "plss-system"."xxl_job_registry"
(
    "id"             BIGINT       NOT NULL,
    "registry_group" varchar(255) NOT NULL,
    "registry_key"   varchar(255) NOT NULL,
    "registry_value" varchar(255) NOT NULL,
    "update_time"    timestamp    NOT NULL,
    CONSTRAINT "xxl_job_registry_pkey" PRIMARY KEY ("id")
);


CREATE INDEX "i_g_k_v" ON "plss-system"."xxl_job_registry" ("registry_group", "registry_key", "registry_value");

CREATE INDEX "i_u" ON "plss-system"."xxl_job_registry" ("update_time");

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_group" IS '注册分组';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_key" IS '注册键';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."registry_value" IS '注册值';

COMMENT
ON COLUMN "plss-system"."xxl_job_registry"."update_time" IS '更新时间';

COMMENT
ON TABLE "plss-system"."xxl_job_registry" IS '任务注册表';



DROP TABLE IF EXISTS "plss-system"."xxl_job_user";
CREATE TABLE "plss-system"."xxl_job_user"
(
    "id"         BIGINT      NOT NULL,
    "username"   varchar(50) NOT NULL,
    "password"   varchar(50) NOT NULL,
    "role"       int         NOT NULL,
    "permission" varchar(255),
    CONSTRAINT "xxl_job_user_pkey" PRIMARY KEY ("id")
);

CREATE UNIQUE INDEX "i_username" ON "plss-system"."xxl_job_user" ("username");

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."id" IS '主键';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."username" IS '账号';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."password" IS '密码';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."role" IS '角色：0-普通用户、1-管理员';

COMMENT
ON COLUMN "plss-system"."xxl_job_user"."permission" IS '权限：执行器ID列表，多个逗号分割';

COMMENT
ON TABLE "plss-system"."xxl_job_user" IS '任务用户表';



INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (2, 'plss_record', '文件服务', 0, null, '2024-04-22 10:46:34');
INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (3, 'plss-system', 'system定时任务执行器', 0, null, '2024-04-22 10:46:34');
INSERT INTO "plss-system".xxl_job_group (ID, APP_NAME, TITLE, ADDRESS_TYPE, ADDRESS_LIST,
                                         UPDATE_TIME)
VALUES (191033526781957, 'plss_search', '搜索服务', 0, null, '2024-04-26 21:38:42');


INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (191035844814853, 191033526781957, '清理es存在但是db不存在的脏数据', '2024-04-26 21:43:25',
        '2024-04-26 21:43:25', '杨俊', null, 'CRON', '0 0 0 1 * ?', 'DO_NOTHING', 'FIRST',
        'cleanRecordInEsNotInDb', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null,
        '2024-04-26 21:43:25', null, 0, 0, 0);

INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (10, 2, '操作日志归档', '2024-03-28 16:41:55', '2024-03-28 16:41:55', 'fs', null, 'CRON',
        '0 10 0 L * ?', 'DO_NOTHING', 'FIRST', 'archiveOperationLog', null, 'SERIAL_EXECUTION', 0,
        0, 'BEAN', null, null, '2024-03-28 16:41:55', null, 1, 0, 0);
INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (9, 2, '登录日志归档', '2024-03-28 16:41:55', '2024-03-28 16:41:55', 'fs', null, 'CRON',
        '0 10 0 L * ?', 'DO_NOTHING', 'FIRST', 'archiveLoginLog', null, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', null, null, '2024-03-28 16:41:55', null, 1, 0, 0);
INSERT INTO "plss-system".xxl_job_info (ID, JOB_GROUP, JOB_DESC, ADD_TIME, UPDATE_TIME, AUTHOR,
                                        ALARM_EMAIL, SCHEDULE_TYPE, SCHEDULE_CONF, MISFIRE_STRATEGY,
                                        EXECUTOR_ROUTE_STRATEGY, EXECUTOR_HANDLER, EXECUTOR_PARAM,
                                        EXECUTOR_BLOCK_STRATEGY, EXECUTOR_TIMEOUT,
                                        EXECUTOR_FAIL_RETRY_COUNT, GLUE_TYPE, GLUE_SOURCE,
                                        GLUE_REMARK, GLUE_UPDATETIME, CHILD_JOB_ID, TRIGGER_STATUS,
                                        TRIGGER_LAST_TIME, TRIGGER_NEXT_TIME)
VALUES (12, 2, '借阅归还', '2024-01-04 20:46:47', '2024-01-04 20:46:47', 'admin', null, 'CRON',
        '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'borrowReturn', null, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', null, null, '2024-01-04 20:46:47', null, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (25, 2, '数据大屏统计标签【每分钟执行一次】', '2024-04-17 14:53:49', '2024-04-17 14:53:57',
        'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'statisticTagJob', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-04-17 14:53:49', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (13, 2, '全量修复已入库数据同步ES中【每分钟执行一次】', '2024-01-09 18:56:01',
        '2024-01-09 18:56:01', 'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'resetFinishStorage', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-01-09 18:56:01', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (11, 2, 'AI重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:46:23', '2024-01-04 20:46:23',
        'zdh', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'resetAiRecordFinishStorage',
        NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:46:23', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (8, 2, 'OCR提取元数据重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:44:23',
        '2024-01-04 20:44:23', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetMetadata', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-01-04 20:44:23', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (15, 2, 'OCR提取正文重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:45:01',
        '2024-01-04 20:45:01', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetText', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:45:01',
        NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (16, 2, 'OCR转双侧OFD重新已入库数据跑批【每分钟执行一次】', '2024-01-04 20:45:40',
        '2024-01-04 20:45:40', '开胜', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'repeatGetOfd', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-01-04 20:45:40',
        NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info"("id", "job_group", "job_desc", "add_time", "update_time",
                                         "author", "alarm_email", "schedule_type", "schedule_conf",
                                         "misfire_strategy", "executor_route_strategy",
                                         "executor_handler", "executor_param",
                                         "executor_block_strategy", "executor_timeout",
                                         "executor_fail_retry_count", "glue_type", "glue_source",
                                         "glue_remark", "glue_updatetime", "child_job_id",
                                         "trigger_status", "trigger_last_time", "trigger_next_time")
VALUES (24, 2, '修复停滞于处理中状态的文档【每天0点执行一次】', '2024-04-09 16:41:55',
        '2024-04-09 16:41:55', 'xks', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST',
        'repairDocProcessStatus', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-04-09 16:41:55', NULL, 1, 0, 0);
        
insert into "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") values (26, 2, '统计日访问量[每天凌晨执行一次]', '2024-03-18 21:26:40.659000', '2024-03-18 21:26:45.930000', '吴梦蝶', null, 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FIRST', 'statisticVisitInfoJob', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-03-18 21:26:40.659000', null, 0, 0, 0);


```

## 新增字典数据-消息类型

### 1. pg

```sql
INSERT INTO "plss-system".sys_dict_data (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE,
                                         CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATE_BY,
                                         CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (192258864358341, 7, '授权策略变更', '7', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-04-28 15:11:39.654000', '', '2024-04-28 15:11:39.654000', '');
```

### 2. dm

```sql
INSERT INTO "plss-system".sys_dict_data (DICT_CODE, DICT_SORT, DICT_LABEL, DICT_VALUE, DICT_TYPE,
                                         CSS_CLASS, LIST_CLASS, IS_DEFAULT, STATUS, CREATE_BY,
                                         CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (192258864358341, 7, '授权策略变更', '7', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-04-28 15:11:39.654000', '', '2024-04-28 15:11:39.654000', '');
```


```GATEWAY网关新增白名单

/system/v1/config/front

```


## 对外域名  具体值依据环境

### 1. pg

```sql
insert into "plss-system"."sys_config" ("config_id","config_name","config_key","config_value","config_type",
                                        "status","create_time","create_by","update_time","update_by","remark",
                                        "element_type","element_name")
values (188109490924677, '系统ip对外域名', 'sys.domain.url', 'http://', 'Y', 1, '2024-04-22 18:29:44.341000', '001',
        '2024-04-26 21:22:55.216965', '001', '替代ip加端口', null, null);
```

### 2. dm

```sql
insert into "plss-system"."sys_config" ("config_id","config_name","config_key","config_value","config_type",
                                        "status","create_time","create_by","update_time","update_by","remark",
                                        "element_type","element_name")
values (188109490924677, '系统ip对外域名', 'sys.domain.url', 'http://', 'Y', 1, '2024-04-22 18:29:44.341000', '001',
        '2024-04-26 21:22:55.216965', '001', '替代ip加端口', null, null);
```


```nacos plss-search.yml添加nacos配置
xxl:
  job:
    enable: true
    executor:
      appname: plss_search
      address: ""
      ip: ""
      port: 0
      # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: /tmp/xxljob/logs
      logretentiondays: 10
    admin:
      addresses: http://{当前环境配置ip端口}/system/v1/xxlJob
    accessToken: default_token 

```


## 日志归档表sql补充
``` postgresql
CREATE TABLE "plss-system"."sys_archive" (
  "id" int8 NOT NULL,
  "business_type" int4 NOT NULL,
  "store_type" int4 NOT NULL,
  "store_start_time" timestamp(6) NOT NULL,
  "store_end_time" timestamp(6) NOT NULL,
  "expire_time" timestamp(6),
  "store_size" int8 NOT NULL,
  "serial_type" int4 NOT NULL,
  "file_id" int8 NOT NULL,
  CONSTRAINT "sys_archive_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "plss-system"."sys_archive"."id" IS 'id';

COMMENT ON COLUMN "plss-system"."sys_archive"."business_type" IS '业务类型，1：登录日志，2：操作日志';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_type" IS '存储类型，1：永久存储，2临时存储';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_start_time" IS '存储内容的开始时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_end_time" IS '存储内容的结束时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."expire_time" IS '临时存储的过期时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_size" IS '存储内容大小';

COMMENT ON COLUMN "plss-system"."sys_archive"."serial_type" IS '存储序列化类型，1：json。2：excel';

COMMENT ON COLUMN "plss-system"."sys_archive"."file_id" IS '文件id';
```


``` 达梦sql
CREATE TABLE "plss-system"."sys_archive" (
  "id" bigint NOT NULL,
  "business_type" int NOT NULL,
  "store_type" int NOT NULL,
  "store_start_time" timestamp NOT NULL,
  "store_end_time" timestamp NOT NULL,
  "expire_time" timestamp,
  "store_size" bigint NOT NULL,
  "serial_type" int NOT NULL,
  "file_id" bigint NOT NULL,
  CONSTRAINT "sys_archive_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "plss-system"."sys_archive"."id" IS 'id';

COMMENT ON COLUMN "plss-system"."sys_archive"."business_type" IS '业务类型，1：登录日志，2：操作日志';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_type" IS '存储类型，1：永久存储，2临时存储';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_start_time" IS '存储内容的开始时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_end_time" IS '存储内容的结束时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."expire_time" IS '临时存储的过期时间';

COMMENT ON COLUMN "plss-system"."sys_archive"."store_size" IS '存储内容大小';

COMMENT ON COLUMN "plss-system"."sys_archive"."serial_type" IS '存储序列化类型，1：json。2：excel';

COMMENT ON COLUMN "plss-system"."sys_archive"."file_id" IS '文件id';
```