# 0830


## postgresql
```sql
-- rc_doc_process新增”文件密级”字段
ALTER TABLE "plss-record".rc_doc_process ADD classified int4 NULL;
COMMENT ON COLUMN "plss-record".rc_doc_process.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- rc_document新增”文件密级”字段
ALTER TABLE "plss-record".rc_document ADD classified int4 NULL;
COMMENT ON COLUMN "plss-record".rc_document.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_plan ADD store_type int DEFAULT 1 NOT NULL;
COMMENT ON COLUMN "plss-record".rc_plan.store_type IS '入库类型：1-标准入库  2-安全入库';

-- rc_record新增”文件密级”字段
ALTER TABLE "plss-record".rc_record ADD classified int4 NULL;
COMMENT ON COLUMN "plss-record".rc_record.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- 新增字典
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272952112885509, 6, '安全入库任务', '6', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:22:29.717', '', '2024-08-20 15:22:29.717', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272951864602373, 5, '安全文档入库', '5', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:21:59.409', '', '2024-08-20 15:21:59.409', '');
       
-- 新增初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954309488389, 5, '安全文档入库-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:26:57.843', '2024-08-20 15:26:57.843', 1612752027141, 1);
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954614124293, 6, '安全入库任务-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:27:35.042', '2024-08-20 15:27:35.042', 1612752027141, 1);

-- 新增入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954309488389);
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954614124293);

-- 增加索引
CREATE INDEX rc_task_batch_create_time_idx ON "plss-record".rc_task_batch (create_time DESC);
```

## 达梦sql
```sql
-- rc_doc_process新增”文件密级”字段
ALTER TABLE "plss-record".rc_doc_process ADD classified int NULL;
COMMENT ON COLUMN "plss-record".rc_doc_process.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- rc_document新增”文件密级”字段
ALTER TABLE "plss-record".rc_document ADD classified int NULL;
COMMENT ON COLUMN "plss-record".rc_document.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_plan ADD store_type int DEFAULT 1 NOT NULL;
COMMENT ON COLUMN "plss-record".rc_plan.store_type IS '入库类型：1-标准入库  2-安全入库';

-- rc_record新增”文件密级”字段
ALTER TABLE "plss-record".rc_record ADD classified int NULL;
COMMENT ON COLUMN "plss-record".rc_record.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- 新增字典
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272952112885509, 6, '安全入库任务', '6', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:22:29.717', '', '2024-08-20 15:22:29.717', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272951864602373, 5, '安全文档入库', '5', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:21:59.409', '', '2024-08-20 15:21:59.409', '');
       
-- 新增初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954309488389, 5, '安全文档入库-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:26:57.843', '2024-08-20 15:26:57.843', 1612752027141, 1);
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954614124293, 6, '安全入库任务-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:27:35.042', '2024-08-20 15:27:35.042', 1612752027141, 1);

-- 新增入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954309488389);
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954614124293);

-- 增加索引
CREATE INDEX rc_task_batch_create_time_idx ON "plss-record".rc_task_batch (create_time DESC);
```

 
