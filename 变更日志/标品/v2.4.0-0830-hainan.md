## 一. 数据库脚本

### 1. pg sql脚本

```sql
-- 新增菜单及绑定对应租户角色
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263610569966213,
        1097331411461,
        '安全文档入库',
        1,
        'document-safety',
        'doc-manage/docToLib',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:37:04.656544',
        '001',
        '2024-08-14 17:26:35.768065',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263610569966213
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263610569966213);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263610569966213);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263611112309381,
        263610569966213,
        '上传文件',
        0,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:upload',
        '#',
        'admin',
        '2024-08-07 10:38:10.859939',
        'chenhong',
        '2024-08-09 16:46:17.410897',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263611112309381
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263611112309381);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263611112309381);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207756710405,
        263610569966213,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:view',
        '#',
        'chenhong',
        '2024-08-09 16:46:33.742199',
        '',
        '2024-08-09 16:46:33.742199',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207756710405
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207756710405);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207756710405);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207916249605,
        263610569966213,
        '编辑',
        2,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:edit',
        '#',
        'chenhong',
        '2024-08-09 16:46:53.215193',
        '',
        '2024-08-09 16:46:53.215193',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207916249605
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207916249605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207916249605);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208086520325,
        263610569966213,
        '重试',
        3,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:retry',
        '#',
        'chenhong',
        '2024-08-09 16:47:13.999868',
        '',
        '2024-08-09 16:47:13.999868',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208086520325
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208086520325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208086520325);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208287838725,
        263610569966213,
        '人工处理',
        4,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:handle',
        '#',
        'chenhong',
        '2024-08-09 16:47:38.575372',
        '',
        '2024-08-09 16:47:38.575372',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208287838725
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208287838725);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208287838725);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208551768581,
        263610569966213,
        '添加位置',
        5,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:addAttr',
        '#',
        'chenhong',
        '2024-08-09 16:48:10.792742',
        'chenhong',
        '2024-08-09 16:48:34.569421',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208551768581
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208551768581);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208551768581);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208839586309,
        263610569966213,
        '审核',
        6,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:examin',
        '#',
        'chenhong',
        '2024-08-09 16:48:45.926667',
        '',
        '2024-08-09 16:48:45.926667',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208839586309
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208839586309);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208839586309);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209027699205,
        263610569966213,
        '删除',
        7,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:delete',
        '#',
        'chenhong',
        '2024-08-09 16:49:08.890142',
        '',
        '2024-08-09 16:49:08.890142',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209027699205
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209027699205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209027699205);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209239986693,
        263610569966213,
        '全库移除',
        8,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:globaldelete',
        '#',
        'chenhong',
        '2024-08-09 16:49:34.804707',
        '',
        '2024-08-09 16:49:34.804707',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209239986693
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209239986693);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209239986693);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612063670917,
        1097331411461,
        '安全入库任务',
        2,
        'document-tasksafety',
        'task',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:40:06.99221',
        '001',
        '2024-08-07 13:40:09.418628',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612063670917
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612063670917);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612063670917);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612286747269,
        263612063670917,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'warehousing:task:view',
        '#',
        'admin',
        '2024-08-07 10:40:34.222801',
        '',
        '2024-08-07 10:40:34.222801',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612286747269
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612286747269);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612286747269);


-- 新增库类型字典
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (264308295205829, 6, '安全文库', '6', 'rc_repo_type', '', 'default', 'N', '1', '001',
        '2024-08-08 10:16:36.192', '001', '2024-08-08 10:22:36.443479', '存放安全文档');


-- 配置更新
delete
from "plss-system"."sys_config"
where config_key LIKE '%sys.front.config%';

INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (192183311754501, '前端参数配置', 'sys.front.config', '[
  {
    "projectId": 1,
    "config": {
      "platform": "ndrc",
      "showNavTop": false
    }
  },
  {
    "projectId": 5,
    "config": {
      "platform": "goxinbu",
      "showFeekBack": {
        "img": "data:image/png;base64,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",
        "feekbackUrl": "https://f.kdocs.cn/g/A2W3tH5r/"
      }
    }
  },
  {
    "projectId": 4,
    "config": {
      "platform": "pudong",
      "jumpUrl": "https://ythbgptuat.ywxt.sh.cegn.cn/"
    }
  },
  {
    "projectId": 3,
    "config": {
      "platform": "neimeng",
      "hideSelectPermissionRole": true,
      "statisticsTimer": 3000,
      "orgId": 1610564159749,
      "tenantId": 1612752027141,
      "statisticsPath": "statistics_nmg",
      "statisticslabelAlias": {
        "year": [
          "民生服务",
          "生产监管",
          "经济建设",
          "社会保障",
          "数字政府",
          "城乡建设"
        ],
        "month": [
          "党的建设",
          "安全法治",
          "农村建设",
          "经济发展",
          "民生服务",
          "社会治理"
        ],
        "week": [
          "城市管理",
          "行政执法",
          "安全监管",
          "综合经济",
          "社会保障",
          "经济建设"
        ]
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        }
      ],
      "ctypePermission": [
        {
          "ctype": 1,
          "hideSetWaterMark": true,
          "hideAddPermission": true,
          "hidePermissionMemo": [
            "can_download",
            "print",
            "download_save"
          ]
        },
        {
          "ctype": 5,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        },
        {
          "ctype": 6,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        }
      ],
      "condition": {
        "hideDownloadButton": "origin!=999",
        "hidePrintButton": "origin!=999"
      }
    }
  },
  {
    "projectId": 0,
    "config": {
      "platform": "global",
      "pasteCapture": false,
      "showArticle": true,
      "showHistory": true,
      "showNavTop": true,
      "loginConfig": {
        "backend": [
          "default",
        ],
        "front": [
          "default",
          "ukey"
        ],
        "type": "hainan",
        "value": ""
      },
      "showKnowledge": true,
      "showHotwords": false,
      "orcMetadataDefaultValue": {
        "密级": "非密"
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "fileCount": 50,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 3,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.pdf,.ofd,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 4,
          "multiple": false,
          "tip": "提示：总大小不超过{0}M",
          "maxSize": 100,
          "maxSizeError": "总大小不能超过{0}M",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 5,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 6,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        }
      ],
      "hotWords": [],
      "metadataPlaceholder": {
        "发文字号": "请输入完整发文字号，示例：“国函〔2024〕1号",
        "发文机关|发文机关标志": "请输入发文机关名称，示例：“国务院办公厅”",
        "发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间": "请输入日期，示例：“2023年01月01日”"
      },
      "isClassified":true
    }
  }
]', 'Y', 1, '2024-04-28 12:37:56', '001', '2024-08-14 17:44:09.54061', 'admin', '此配置需要全量更新。
platform：平台名称
pasteCapture： 是否拦截粘贴
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式 
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符', NULL, NULL);

delete
from "plss-system"."sys_config"
where config_key LIKE '%system.repository.config%';


INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (205798046516677, '系统文库页签配置', 'system.repository.config',
        '{"tabs":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"参考库","ctype":[2]},{"label":"模板&范文样例库","ctype":[4,5]},{"label":"安全文库","ctype":[6]}],"query":[]}',
        'Y', 1, '2024-05-17 18:17:11.85', 'admin', '2024-08-14 17:08:01.770487', 'admin', '备份: 内蒙不需要显示该查询条件
"query": [
    {
      "id": "publishTime",
      "component": "date",
      "type": "metadata",
      "label": "发布日期",
      "data": {
        "metadataId": "1121649841157",
        "metadataValue": "{publishTime}"
      }
    }
  ]', NULL, NULL);


-- 用户表添加人员密级字段
ALTER TABLE "plss-system".sys_user
    ADD classified int4;

COMMENT
ON COLUMN "plss-system"."sys_user"."classified" IS '人员密级';

-- 密级字典
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447217556037, '用户密级', 'sys_user_classified', '1', '2024-07-29 17:37:42.302288',
        '001', '2024-07-29 17:38:11.508552', '001', '用户密级');
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447641131589, '文件密级', 'rc_file_classified', '1', '2024-07-29 17:38:34.006048', '001',
        '2024-07-29 17:39:18.464625', '001', '文件密级');


INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257448772848197, 2, '一般涉密人员', '100', 'sys_user_classified', '', 'default', 'N', '1',
        '001', '2024-07-29 17:40:52.154', '001', '2024-07-30 09:58:10.926146', '');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257926153521733, 3, '重要涉密人员', '200', 'sys_user_classified', '', 'default', 'N', '1',
        '001', '2024-07-30 09:52:06.162', '001', '2024-07-30 09:58:16.65784', '');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (262298011395653, 1, '非密', '10', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-08-05 14:06:40.219', '001', '2024-08-05 14:38:42.039029', '非密');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257929911552581, 3, '机密', '200', 'rc_file_classified', '', 'default', 'N', '1', '001',
        '2024-07-30 09:59:44.906', '001', '2024-07-30 10:02:18.182983', '机密');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257929828657733, 2, '秘密', '100', 'rc_file_classified', '', 'default', 'N', '1', '001',
        '2024-07-30 09:59:34.787', '001', '2024-07-30 10:02:12.881834', '秘密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257930165135941, 4, '绝密', '300', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:00:15.861', '001', '2024-07-30 10:02:25.030443', '绝密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257931004627525, 5, '核心', '400', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:01:58.338', '', '2024-07-30 10:01:58.338', '机密-核心');


-- rc_doc_process新增”文件密级”字段
ALTER TABLE "plss-record".rc_doc_process
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_doc_process.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- rc_document新增”文件密级”字段
ALTER TABLE "plss-record".rc_document
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_document.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_plan
    ADD store_type int DEFAULT 1 NOT NULL;
COMMENT
ON COLUMN "plss-record".rc_plan.store_type IS '入库类型：1-标准入库  2-安全入库';

-- rc_record新增”文件密级”字段
ALTER TABLE "plss-record".rc_record
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_record.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_repository
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_repository.classified IS '密级';
ALTER TABLE "plss-record".rc_folder
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_folder.classified IS '密级';
ALTER TABLE "plss-record".rc_resource_permission
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_resource_permission.classified IS '密级';

-- 监管分析
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (267352032095813, '监管分析统计数据分布内置数据更新的日期范围',
        'record.supervise.analysis.weekMonthYear', '1', 'Y', 1, '2024-08-12 17:29:06.105', 'admin',
        '2024-08-12 17:29:06.105', 'admin', '1-本周更新的数据
2-本月更新的数据', 'text', NULL);

-- ai中台需要加密的字段配置
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (263599265514117, 'ES-es索引库加密字段', 'dp.es.encrypt.fields', '["title"]', 'Y', 1,
        '2024-08-07 10:14:04.716', 'admin', '2024-08-07 10:39:32.569453', 'admin',
        'ES-es索引库加密字段,没有加密字段则为空数组:[]', 'json', NULL);

-- 操作日志添加用户id字段
ALTER TABLE "plss-system".sys_oper_log
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_oper_log.user_id IS '用户ID';
--填充操作日志的user_id的数据
UPDATE "plss-system".sys_oper_log AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.oper_name = b.user_name;

-- 登录日志添加用户id字段
ALTER TABLE "plss-system".sys_logininfor
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_logininfor.user_id IS '用户ID';
--填充登录日志user_id的数据
UPDATE "plss-system".sys_logininfor AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.user_name = b.user_name;

--新增读缓存的磁盘信息发送告警消息的定时任务
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status,
                                        trigger_last_time, trigger_next_time)
VALUES (263608483480197, 3, '磁盘告警信息定时任务', '2024-08-07 10:32:49.953',
        '2024-08-07 10:33:06.145', 'wmd', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST',
        'diskAlarmMessage', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-08-07 10:32:49.953', NULL, 1, 0, 1723050000000);

--新增磁盘告警信息阈值的参数设置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name)
VALUES (263038234037893, '磁盘告警阈值', 'system.disk.threshold', '1048576', 'Y', 1,
        '2024-08-06 15:12:39.428', 'admin', '2024-08-07 11:24:52.653', 'admin',
        '单位kb，1G = 1024*1024kb = 1048576kb ', NULL, NULL);

--新增监管分析菜单
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (258049632232069, 0, '监管分析', 15, 'supervise', 'supervise/index', '', '1', '1', 'C', '1',
        '1', '', 'icon-supervise', '001', '2024-07-30 14:03:19.252', '001',
        '2024-08-05 16:56:30.815', '', 2);
--监管分析菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 258049632232069
FROM "plss-system".sys_tenant st;

--运营管理员和入库管理员绑定监管分析菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 258049632232069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 258049632232069);

--更新账号密码强制修改周期为30天
UPDATE "plss-system".sys_config SET config_name='密码强制修改周期', config_key='config_force_update_password_cycle', config_value='30', config_type='Y', status=1, create_time='2024-01-05 13:48:11.185', create_by='001', update_time='2024-08-15 15:55:41.225', update_by='admin', remark='0-99的数字。单位为天。输入0代表不要求修改。默认为0.注意只能输入规定数字，否则登陆报错。', element_type=NULL, element_name=NULL WHERE config_id=3485309743109;

-- 操作日志和登录日志归档定时任务执行间隔调整为1天
DELETE FROM "plss-system"."xxl_job_info" WHERE executor_handler IN ('archiveOperationLog','archiveLoginLog');
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (10, 3, '操作日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:52.404', 'fs', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveOperationLog', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (9, 3, '登录日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:43.315', 'fs', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveLoginLog', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);


--新增调用ai中台安全文件搜索地址
delete from "plss-system".sys_config where config_key = 'dp.feature.impl.buttJointOtherKey';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (3864463331077, '文档处理服务-功能对接其他配置', 'dp.feature.impl.buttJointOtherKey', '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":1000,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply","aiMiddleAggSearchUrl":"/openApi/aggregations/aggrCountByField","aiMiddleSearchSecretUrl":"/openApi/searchSecretData"}', 'Y', 1, '2024-01-22 17:12:39.892249', '001', '2024-08-12 16:15:50.951182', 'admin', 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭
aiMiddleSearchSecretUrl=ai中台安全文件搜索地址', NULL, NULL);

-- 新增字典
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272952112885509, 6, '安全入库任务', '6', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:22:29.717', '', '2024-08-20 15:22:29.717', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272951864602373, 5, '安全文档入库', '5', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:21:59.409', '', '2024-08-20 15:21:59.409', '');
       
-- 新增初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954309488389, 5, '安全文档入库-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:26:57.843', '2024-08-20 15:26:57.843', 1612752027141, 1);
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954614124293, 6, '安全入库任务-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:27:35.042', '2024-08-20 15:27:35.042', 1612752027141, 1);

-- 新增入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954309488389);
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954614124293);

delete from "plss-system".sys_config where config_key = 'sys.default.metadta';
INSERT INTO "plss-system".sys_config (CONFIG_ID, CONFIG_NAME, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE, STATUS, CREATE_TIME, CREATE_BY, UPDATE_TIME, UPDATE_BY, REMARK, ELEMENT_TYPE, ELEMENT_NAME) VALUES (4659653467397, '系统默认元数据', 'sys.default.metadta', '[{"categoryId":1160484769029,"id":1121765356037,"label":"实体日期","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121757193221,"label":"实体地点","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121745177861,"label":"实体人物","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121736301573,"label":"实体机构","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121727166725,"label":"关键词","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121716339461,"label":"系统分类","value":{"required":2},"disabled":["required"]},{"categoryId":1160482904581,"id":2716890001157,"label":"入库位置"}]', 'Y', 1, '2024-01-29 12:37:30.823000', '001', '2024-08-26 11:37:51.837616', 'admin', '控制文档类型中元数据默认状态（选中、禁用）。
required：必填
searchFlag：可被搜索
searchResultView：检索结果展示
detailsView：详情信息展示
optEdit：可编辑
optView：编辑页可见', null, null);

-- 增加索引
CREATE INDEX rc_task_batch_create_time_idx ON "plss-record".rc_task_batch (create_time DESC);

-- 开放平台 - 应用表新增字段 机构名称，过期时间
ALTER TABLE "plss-open".op_app_info ADD org_name varchar(64) NULL;
COMMENT ON COLUMN "plss-open".op_app_info.org_name IS '机构名称';
ALTER TABLE "plss-open".op_app_info ADD expiry_time varchar(64) NULL;
COMMENT ON COLUMN "plss-open".op_app_info.expiry_time IS '过期时间';
update "plss-open".op_app_info set expiry_time = '-1' where expiry_time is null;

-- AI实体关键词字数限制
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (279425554973509, 'AI实体关键词字数限制', 'record.ai.entity.keyword.limit', '15', 'Y', 1, '2024-08-29 18:52:44.817', 'admin', '2024-08-29 18:52:44.817', 'admin', 'AI实体关键词字数限制', 'text', NULL);


```

### 1. dm sql脚本

```sql
-- 新增菜单及绑定对应租户角色
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263610569966213,
        1097331411461,
        '安全文档入库',
        1,
        'document-safety',
        'doc-manage/docToLib',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:37:04.656544',
        '001',
        '2024-08-14 17:26:35.768065',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263610569966213
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263610569966213);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263610569966213);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263611112309381,
        263610569966213,
        '上传文件',
        0,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:upload',
        '#',
        'admin',
        '2024-08-07 10:38:10.859939',
        'chenhong',
        '2024-08-09 16:46:17.410897',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263611112309381
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263611112309381);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263611112309381);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207756710405,
        263610569966213,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:view',
        '#',
        'chenhong',
        '2024-08-09 16:46:33.742199',
        '',
        '2024-08-09 16:46:33.742199',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207756710405
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207756710405);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207756710405);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207916249605,
        263610569966213,
        '编辑',
        2,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:edit',
        '#',
        'chenhong',
        '2024-08-09 16:46:53.215193',
        '',
        '2024-08-09 16:46:53.215193',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207916249605
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207916249605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207916249605);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208086520325,
        263610569966213,
        '重试',
        3,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:retry',
        '#',
        'chenhong',
        '2024-08-09 16:47:13.999868',
        '',
        '2024-08-09 16:47:13.999868',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208086520325
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208086520325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208086520325);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208287838725,
        263610569966213,
        '人工处理',
        4,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:handle',
        '#',
        'chenhong',
        '2024-08-09 16:47:38.575372',
        '',
        '2024-08-09 16:47:38.575372',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208287838725
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208287838725);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208287838725);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208551768581,
        263610569966213,
        '添加位置',
        5,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:addAttr',
        '#',
        'chenhong',
        '2024-08-09 16:48:10.792742',
        'chenhong',
        '2024-08-09 16:48:34.569421',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208551768581
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208551768581);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208551768581);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208839586309,
        263610569966213,
        '审核',
        6,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:examin',
        '#',
        'chenhong',
        '2024-08-09 16:48:45.926667',
        '',
        '2024-08-09 16:48:45.926667',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208839586309
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208839586309);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208839586309);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209027699205,
        263610569966213,
        '删除',
        7,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:delete',
        '#',
        'chenhong',
        '2024-08-09 16:49:08.890142',
        '',
        '2024-08-09 16:49:08.890142',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209027699205
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209027699205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209027699205);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209239986693,
        263610569966213,
        '全库移除',
        8,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:globaldelete',
        '#',
        'chenhong',
        '2024-08-09 16:49:34.804707',
        '',
        '2024-08-09 16:49:34.804707',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209239986693
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209239986693);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209239986693);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612063670917,
        1097331411461,
        '安全入库任务',
        2,
        'document-tasksafety',
        'task',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:40:06.99221',
        '001',
        '2024-08-07 13:40:09.418628',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612063670917
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612063670917);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612063670917);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612286747269,
        263612063670917,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'warehousing:task:view',
        '#',
        'admin',
        '2024-08-07 10:40:34.222801',
        '',
        '2024-08-07 10:40:34.222801',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612286747269
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612286747269);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612286747269);


-- 新增库类型字典
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (264308295205829, 6, '安全文库', '6', 'rc_repo_type', '', 'default', 'N', '1', '001',
        '2024-08-08 10:16:36.192', '001', '2024-08-08 10:22:36.443479', '存放安全文档');


-- 配置更新
delete
from "plss-system"."sys_config"
where config_key LIKE '%sys.front.config%';

INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (192183311754501, '前端参数配置', 'sys.front.config', '[
  {
    "projectId": 1,
    "config": {
      "platform": "ndrc",
      "showNavTop": false
    }
  },
  {
    "projectId": 5,
    "config": {
      "platform": "goxinbu",
      "showFeekBack": {
        "img": "data:image/png;base64,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",
        "feekbackUrl": "https://f.kdocs.cn/g/A2W3tH5r/"
      }
    }
  },
  {
    "projectId": 4,
    "config": {
      "platform": "pudong",
      "jumpUrl": "https://ythbgptuat.ywxt.sh.cegn.cn/"
    }
  },
  {
    "projectId": 3,
    "config": {
      "platform": "neimeng",
      "hideSelectPermissionRole": true,
      "statisticsTimer": 3000,
      "orgId": 1610564159749,
      "tenantId": 1612752027141,
      "statisticsPath": "statistics_nmg",
      "statisticslabelAlias": {
        "year": [
          "民生服务",
          "生产监管",
          "经济建设",
          "社会保障",
          "数字政府",
          "城乡建设"
        ],
        "month": [
          "党的建设",
          "安全法治",
          "农村建设",
          "经济发展",
          "民生服务",
          "社会治理"
        ],
        "week": [
          "城市管理",
          "行政执法",
          "安全监管",
          "综合经济",
          "社会保障",
          "经济建设"
        ]
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        }
      ],
      "ctypePermission": [
        {
          "ctype": 1,
          "hideSetWaterMark": true,
          "hideAddPermission": true,
          "hidePermissionMemo": [
            "can_download",
            "print",
            "download_save"
          ]
        },
        {
          "ctype": 5,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        },
        {
          "ctype": 6,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        }
      ],
      "condition": {
        "hideDownloadButton": "origin!=999",
        "hidePrintButton": "origin!=999"
      }
    }
  },
  {
    "projectId": 0,
    "config": {
      "platform": "global",
      "pasteCapture": false,
      "showArticle": true,
      "showHistory": true,
      "showNavTop": true,
      "loginConfig": {
        "backend": [
          "default",
        ],
        "front": [
          "default",
          "ukey"
        ],
        "type": "hainan",
        "value": ""
      },
      "showKnowledge": true,
      "showHotwords": false,
      "orcMetadataDefaultValue": {
        "密级": "非密"
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "fileCount": 50,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 3,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.pdf,.ofd,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 4,
          "multiple": false,
          "tip": "提示：总大小不超过{0}M",
          "maxSize": 100,
          "maxSizeError": "总大小不能超过{0}M",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 5,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 6,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        }
      ],
      "hotWords": [],
      "metadataPlaceholder": {
        "发文字号": "请输入完整发文字号，示例：“国函〔2024〕1号",
        "发文机关|发文机关标志": "请输入发文机关名称，示例：“国务院办公厅”",
        "发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间": "请输入日期，示例：“2023年01月01日”"
      },
      "isClassified":true
    }
  }
]', 'Y', 1, '2024-04-28 12:37:56', '001', '2024-08-14 17:44:09.54061', 'admin', '此配置需要全量更新。
platform：平台名称
pasteCapture： 是否拦截粘贴
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式 
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符', NULL, NULL);

delete
from "plss-system"."sys_config"
where config_key LIKE '%system.repository.config%';


INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (205798046516677, '系统文库页签配置', 'system.repository.config',
        '{"tabs":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"参考库","ctype":[2]},{"label":"模板&范文样例库","ctype":[4,5]},{"label":"安全文库","ctype":[6]}],"query":[]}',
        'Y', 1, '2024-05-17 18:17:11.85', 'admin', '2024-08-14 17:08:01.770487', 'admin', '备份: 内蒙不需要显示该查询条件
"query": [
    {
      "id": "publishTime",
      "component": "date",
      "type": "metadata",
      "label": "发布日期",
      "data": {
        "metadataId": "1121649841157",
        "metadataValue": "{publishTime}"
      }
    }
  ]', NULL, NULL);

-- 用户表添加人员密级字段
ALTER TABLE "plss-system".sys_user
    ADD classified int;

COMMENT
ON COLUMN "plss-system"."sys_user"."classified" IS '人员密级';

-- 密级字典
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447217556037, '用户密级', 'sys_user_classified', '1', '2024-07-29 17:37:42.302288',
        '001', '2024-07-29 17:38:11.508552', '001', '用户密级');
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447641131589, '文件密级', 'rc_file_classified', '1', '2024-07-29 17:38:34.006048', '001',
        '2024-07-29 17:39:18.464625', '001', '文件密级');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257448772848197, 2, '一般涉密人员', '100', 'sys_user_classified', '', 'default', 'N', '1',
        '001', '2024-07-29 17:40:52.154', '001', '2024-07-30 09:58:10.926146', '');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257926153521733, 3, '重要涉密人员', '200', 'sys_user_classified', '', 'default', 'N', '1',
        '001', '2024-07-30 09:52:06.162', '001', '2024-07-30 09:58:16.65784', '');

-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (262298011395653, 1, '非密', '10', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-08-05 14:06:40.219', '001', '2024-08-05 14:38:42.039029', '非密');

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257929911552581, 3, '机密', '200', 'rc_file_classified', '', 'default', 'N', '1', '001',
        '2024-07-30 09:59:44.906', '001', '2024-07-30 10:02:18.182983', '机密');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
                                           "dict_type", "css_class", "list_class", "is_default",
                                           "status", "create_by", "create_time", "update_by",
                                           "update_time", "remark")
VALUES (257929828657733, 2, '秘密', '100', 'rc_file_classified', '', 'default', 'N', '1', '001',
        '2024-07-30 09:59:34.787', '001', '2024-07-30 10:02:12.881834', '秘密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257930165135941, 4, '绝密', '300', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:00:15.861', '001', '2024-07-30 10:02:25.030443', '绝密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257931004627525, 5, '核心', '400', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:01:58.338', '', '2024-07-30 10:01:58.338', '机密-核心');


-- rc_doc_process新增”文件密级”字段
ALTER TABLE "plss-record".rc_doc_process
    ADD classified int NULL;
COMMENT
ON COLUMN "plss-record".rc_doc_process.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- rc_document新增”文件密级”字段
ALTER TABLE "plss-record".rc_document
    ADD classified int NULL;
COMMENT
ON COLUMN "plss-record".rc_document.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_plan
    ADD store_type int DEFAULT 1 NOT NULL;
COMMENT
ON COLUMN "plss-record".rc_plan.store_type IS '入库类型：1-标准入库  2-安全入库';

-- rc_record新增”文件密级”字段
ALTER TABLE "plss-record".rc_record
    ADD classified int NULL;
COMMENT
ON COLUMN "plss-record".rc_record.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_repository
    ADD classified int;
COMMENT
ON COLUMN "plss-record".rc_repository.classified IS '密级';
ALTER TABLE "plss-record".rc_folder
    ADD classified int;
COMMENT
ON COLUMN "plss-record".rc_folder.classified IS '密级';
ALTER TABLE "plss-record".rc_resource_permission
    ADD classified int;
COMMENT
ON COLUMN "plss-record".rc_resource_permission.classified IS '密级';

-- 监管分析
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (267352032095813, '监管分析统计数据分布内置数据更新的日期范围',
        'record.supervise.analysis.weekMonthYear', '1', 'Y', 1, '2024-08-12 17:29:06.105', 'admin',
        '2024-08-12 17:29:06.105', 'admin', '1-本周更新的数据
2-本月更新的数据', 'text', NULL);

-- ai中台需要加密的字段配置
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (263599265514117, 'ES-es索引库加密字段', 'dp.es.encrypt.fields', '["title"]', 'Y', 1,
        '2024-08-07 10:14:04.716', 'admin', '2024-08-07 10:39:32.569453', 'admin',
        'ES-es索引库加密字段,没有加密字段则为空数组:[]', 'json', NULL);

-- 操作日志添加用户id字段
ALTER TABLE "plss-system".sys_oper_log
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_oper_log.user_id IS '用户ID';
--填充操作日志的user_id的数据
UPDATE "plss-system".sys_oper_log AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.oper_name = b.user_name;

-- 登录日志添加用户id字段
ALTER TABLE "plss-system".sys_logininfor
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_logininfor.user_id IS '用户ID';
--填充登录日志user_id的数据
UPDATE "plss-system".sys_logininfor AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.user_name = b.user_name;

--新增读缓存的磁盘信息发送告警消息的定时任务
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status,
                                        trigger_last_time, trigger_next_time)
VALUES (263608483480197, 3, '磁盘告警信息定时任务', '2024-08-07 10:32:49.953',
        '2024-08-07 10:33:06.145', 'wmd', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST',
        'diskAlarmMessage', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-08-07 10:32:49.953', NULL, 1, 0, 1723050000000);

--新增磁盘告警信息阈值的参数设置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name)
VALUES (263038234037893, '磁盘告警阈值', 'system.disk.threshold', '1048576', 'Y', 1,
        '2024-08-06 15:12:39.428', 'admin', '2024-08-07 11:24:52.653', 'admin',
        '单位kb，1G = 1024*1024kb = 1048576kb ', NULL, NULL);

--新增监管分析菜单
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (258049632232069, 0, '监管分析', 15, 'supervise', 'supervise/index', '', '1', '1', 'C', '1',
        '1', '', 'icon-supervise', '001', '2024-07-30 14:03:19.252', '001',
        '2024-08-05 16:56:30.815', '', 2);
--监管分析菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 258049632232069
FROM "plss-system".sys_tenant st;

--运营管理员和入库管理员绑定监管分析菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 258049632232069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 258049632232069);

--更新账号密码强制修改周期为30天
UPDATE "plss-system".sys_config SET config_name='密码强制修改周期', config_key='config_force_update_password_cycle', config_value='30', config_type='Y', status=1, create_time='2024-01-05 13:48:11.185', create_by='001', update_time='2024-08-15 15:55:41.225', update_by='admin', remark='0-99的数字。单位为天。输入0代表不要求修改。默认为0.注意只能输入规定数字，否则登陆报错。', element_type=NULL, element_name=NULL WHERE config_id=3485309743109;

-- 操作日志和登录日志归档定时任务执行间隔调整为1天
DELETE FROM "plss-system"."xxl_job_info" WHERE executor_handler IN ('archiveOperationLog','archiveLoginLog');
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (10, 3, '操作日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:52.404', 'fs', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveOperationLog', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (9, 3, '登录日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:43.315', 'fs', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveLoginLog', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);


--新增调用ai中台安全文件搜索地址
delete from "plss-system".sys_config where config_key = 'dp.feature.impl.buttJointOtherKey';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (3864463331077, '文档处理服务-功能对接其他配置', 'dp.feature.impl.buttJointOtherKey', '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":1000,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply","aiMiddleAggSearchUrl":"/openApi/aggregations/aggrCountByField","aiMiddleSearchSecretUrl":"/openApi/searchSecretData"}', 'Y', 1, '2024-01-22 17:12:39.892249', '001', '2024-08-12 16:15:50.951182', 'admin', 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭
aiMiddleSearchSecretUrl=ai中台安全文件搜索地址', NULL, NULL);

-- 新增字典
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272952112885509, 6, '安全入库任务', '6', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:22:29.717', '', '2024-08-20 15:22:29.717', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(272951864602373, 5, '安全文档入库', '5', 'sys_biz_module_type', '', 'default', 'N', '1', '001', '2024-08-20 15:21:59.409', '', '2024-08-20 15:21:59.409', '');
       
-- 新增初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954309488389, 5, '安全文档入库-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:26:57.843', '2024-08-20 15:26:57.843', 1612752027141, 1);
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype)
VALUES(272954614124293, 6, '安全入库任务-查看-本人创建数据权限', 1, 1, '1', '2024-08-20 15:27:35.042', '2024-08-20 15:27:35.042', 1612752027141, 1);

-- 新增入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954309488389);
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES(7, 272954614124293);

delete from "plss-system".sys_config where config_key = 'sys.default.metadta';
INSERT INTO "plss-system".sys_config (CONFIG_ID, CONFIG_NAME, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE, STATUS, CREATE_TIME, CREATE_BY, UPDATE_TIME, UPDATE_BY, REMARK, ELEMENT_TYPE, ELEMENT_NAME) VALUES (4659653467397, '系统默认元数据', 'sys.default.metadta', '[{"categoryId":1160484769029,"id":1121765356037,"label":"实体日期","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121757193221,"label":"实体地点","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121745177861,"label":"实体人物","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121736301573,"label":"实体机构","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121727166725,"label":"关键词","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121716339461,"label":"系统分类","value":{"required":2},"disabled":["required"]},{"categoryId":1160482904581,"id":2716890001157,"label":"入库位置"}]', 'Y', 1, '2024-01-29 12:37:30.823000', '001', '2024-08-26 11:37:51.837616', 'admin', '控制文档类型中元数据默认状态（选中、禁用）。
required：必填
searchFlag：可被搜索
searchResultView：检索结果展示
detailsView：详情信息展示
optEdit：可编辑
optView：编辑页可见', null, null);

-- 增加索引
CREATE INDEX rc_task_batch_create_time_idx ON "plss-record".rc_task_batch (create_time DESC);

-- AI实体关键词字数限制
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (279425554973509, 'AI实体关键词字数限制', 'record.ai.entity.keyword.limit', '15', 'Y', 1, '2024-08-29 18:52:44.817', 'admin', '2024-08-29 18:52:44.817', 'admin', 'AI实体关键词字数限制', 'text', NULL);


-- 开放平台 - 应用表新增字段 机构名称，过期时间
ALTER TABLE "plss-open".op_app_info ADD org_name varchar(64) NULL;
COMMENT ON COLUMN "plss-open".op_app_info.org_name IS '机构名称';
ALTER TABLE "plss-open".op_app_info ADD expiry_time varchar(64) NULL;
COMMENT ON COLUMN "plss-open".op_app_info.expiry_time IS '过期时间';
update "plss-open".op_app_info set expiry_time = '-1' where expiry_time is null;

```


# nacos 新增配置
# plss-document-process.yml
# PS: 注意 docker 环境变量一定要配置，否则会报错
# 默认值
# connectTimeoutInMissecond = 5000
# readTimeoutInMissecond = 300000
# maxThreadCount = 10
```yaml
suwell:
  convert:
	maxThreadCount: ${convert-max-thread-count}
	connectTimeoutInMissecond: ${convert-connect-timeout-inmissecond}
	readTimeoutInMissecond: ${convert-readtimeout-inmissecond}
```

 

