package com.suwell.plss.search.standard.dto.response;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */

@Data
public class RelRecordItemResp {

    /**
     * 关联的文档id
     */
    private Long recordId;

    /**
     * 关联的文档标题
     */
    private String title;

    public RelRecordItemResp(){

    }

    public RelRecordItemResp(Long recordId, String title) {
        this.recordId = recordId;
        this.title = title;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RelRecordItemResp that = (RelRecordItemResp) o;
        return Objects.equals(title, that.title);
    }

    @Override
    public int hashCode() {
        return Objects.hash(title);
    }
}
