package com.suwell.plss.search.standard.dto.request;

import com.suwell.plss.search.standard.enums.ContentVectorType;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * ==========================
 * 开发：wei.wang
 * 创建时间：2023-11-09 14:37
 * 版本：1.0
 * 描述：内容进行向量处理请求体
 * ==========================
 */
@Data
public class ContentVectorReq implements Serializable {
    /**
     * 数据id集合
     */
    private List<Long> dataIds;

    /**
     * 起点id
     */
    private Long startId = 0L;

    /**
     * 每批次处理数量
     */
    private Integer size;

    /**
     * 内容类型
     *
     * @see ContentVectorType
     */
    @NotNull(message = "contentType is null")
    private Integer contentType;

    /**
     * 同时多少个线程数处理
     */
    private Integer threads = 20;
}
