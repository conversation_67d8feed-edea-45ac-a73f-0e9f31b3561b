package com.suwell.plss.search.api.http;

import com.suwell.plss.framework.common.constant.ServiceNameConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.search.api.fallback.SearchRpcServiceFallbackFactory;
import com.suwell.plss.search.standard.dto.SubjectLibraryDTO;
import com.suwell.plss.search.standard.dto.request.ConditionItemQuery;
import com.suwell.plss.search.standard.dto.request.NavigationItemBarQuery;
import com.suwell.plss.search.standard.dto.response.NavigationItemBarDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/17
 */

@FeignClient(contextId = "subjectLibraryRpcEntrance", value = ServiceNameConstants.SEARCH_SERVICE
        , fallbackFactory = SearchRpcServiceFallbackFactory.class)
public interface SubjectLibraryRpcEntrance {

    String INNER_PREFIX = "/v1/inner/subjectLibrary";

    @PostMapping(INNER_PREFIX + "/list")
    R<List<SubjectLibraryDTO>> subjectLibrary(@RequestBody String moduleName);

    @PostMapping(INNER_PREFIX + "/queryCondition")
    R<List<NavigationItemBarDTO>> queryCondition(@RequestBody ConditionItemQuery query);

    @PostMapping(INNER_PREFIX + "/navigation")
    R<List<NavigationItemBarDTO>> navigation(@RequestBody NavigationItemBarQuery query);
}
