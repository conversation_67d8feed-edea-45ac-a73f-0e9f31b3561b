package com.suwell.plss.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.RequestBase;
import co.elastic.clients.elasticsearch._types.ScriptSortType;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.mapping.RuntimeFieldType;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.JsonpMapper;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.search.service.RecordInfoService;
import com.suwell.plss.search.standard.dto.request.newsSearch.MetadataListQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchRecordV2QueryReq;
import com.suwell.plss.search.standard.dto.response.RelatedRecordResp;
import com.suwell.plss.search.standard.dto.response.SearchRecordResp;
import jakarta.annotation.Resource;
import jakarta.json.stream.JsonGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.suwell.plss.framework.es.enums.EsIndexEnum.INDEX_NLP_RECORD;

/***
 * 文档详细页面接口信息
 *<AUTHOR>
 *@date 2023/11/30 22:09
 *@version 1.0.0
 */
@Slf4j
@Service
public class RecordInfoSearchImpl implements RecordInfoService {

    @Resource
    private ElasticsearchClient elasticsearchClient;

    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    private SearchRecordServiceImpl searchRecordServicel;


    @Override
    public RelatedRecordResp searchRelatedRecord(List<String> kws, Long recordId, Long repoId, List<String> specKws,Integer from,Integer size)
            throws ExecutionException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        RelatedRecordResp resp = new RelatedRecordResp();
        Query.Builder queryBuild = getPermissionBuilder(kws, recordId, specKws);
        TermsAggregation.Builder builder = new TermsAggregation.Builder().field("virtualYear").size(10);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();

        //fix 46291 按照文档关键词命中的个数排序
        List<SortOptions> sortList = buildSortOptions(kws);
        SearchRequest request = searchBuilder
                .query(queryBuild.build())
                .index(INDEX_NLP_RECORD.getIndexName())
                .aggregations("virtualYear", agg-> agg.terms(builder.build()))
                .from(from)
                .sort(sortList)
                .fields(f -> f.field("virtualYear"))
                .runtimeMappings("virtualYear", script -> {
                    script.type(RuntimeFieldType.Long);
                    script.script(s -> s.inline(i -> i.source(virtualYearScript()).params("_source",
                            JsonData.fromJson("{\"includes\": [\"metadataIdValue\"]}"))));
                    return script;
                })
                .source(s -> s.filter(f -> f.includes(Arrays.asList("recordId","title","metadataIdValue"))))
                .size(size)
                .build();
        printEsBySearchRequest(request);
        SearchResponse<NlpRecord> search = null;
        try {
            search = elasticsearchClient.search(request, NlpRecord.class);
        } catch (IOException e) {
            log.error("ES查询相关文档失败", e);
            return resp;
        }
        HitsMetadata<NlpRecord> hits = search.hits();

        //取结果和聚合结果
        List<NlpRecord> nlpRecordList = new ArrayList<>();
        for (Hit<NlpRecord> hit : hits.hits()) {
            nlpRecordList.add(hit.source());
        }
        List<SearchRecordResp> searchRecordRespList = Lists.newArrayList();
        List<RelatedRecordResp.YearNumVO> yearNums = new ArrayList<>();
        if (CollUtil.isNotEmpty(nlpRecordList)) {
            for (NlpRecord nlpRecord : nlpRecordList) {
                SearchRecordResp searchRecordResp = new SearchRecordResp();
                searchRecordResp.setRecordId(nlpRecord.getRecordId());
                searchRecordResp.setRecordStatus(nlpRecord.getRecordStatus());
                searchRecordResp.setDigest(nlpRecord.getDigest());
                searchRecordResp.setTitle(nlpRecord.getTitle());
                searchRecordResp.setReleaseTime(nlpRecord.getReleaseTime());
                searchRecordResp.setVisitNum(nlpRecord.getVisitNum());
                searchRecordResp.setCreateTime(nlpRecord.getCreateTime());
                List<SearchRecordResp.MetadataIdValue> metadataIdValueList = DozerUtils.convertListToNew(
                        nlpRecord.getMetadataIdValue(),
                        SearchRecordResp.MetadataIdValue.class);

                searchRecordResp.setCategoryIds(nlpRecord.getCategoryIds());
                if (CollUtil.isNotEmpty(nlpRecord.getRepoFolders())) {
                    List<SearchRecordResp.RepoFolder> collect = nlpRecord.getRepoFolders().stream().map(x -> {
                        SearchRecordResp.RepoFolder repoFolder = new SearchRecordResp.RepoFolder();
                        repoFolder.setRepoId(x.getRepoId());
                        repoFolder.setRepoStatus(x.getRepoStatus());
                        repoFolder.setFolderIds(x.getFolderIds());
                        return repoFolder;
                    }).collect(Collectors.toList());
                    searchRecordResp.setRepoFolders(collect);
                }
                searchRecordResp.setTenantId(nlpRecord.getTenantId());

                // TODO: 2023/11/15 过滤需要显示的元数据
                searchRecordResp.setMetadataIdValue(metadataIdValueList);
                searchRecordRespList.add(searchRecordResp);
            }
        }
        Map<String, Aggregate> aggregations = search.aggregations();
        Aggregate virtualYear = aggregations.get("virtualYear");
        List<LongTermsBucket> array = virtualYear.lterms().buckets().array();
        for (LongTermsBucket longTermsBucket : array) {
            if (0L != longTermsBucket.key()) {
                RelatedRecordResp.YearNumVO vo = new RelatedRecordResp.YearNumVO();
                vo.setYear(String.valueOf(longTermsBucket.key()));
                vo.setNum(Integer.parseInt(String.valueOf(longTermsBucket.docCount())));
                yearNums.add(vo);
            }
        }
        yearNums.sort(Comparator.comparing(RelatedRecordResp.YearNumVO::getYear));
        resp.setPages(new PageUtils<>(searchRecordRespList, hits.total().value(),10, 1));
        resp.setYearNumList(yearNums);
        stopWatch.stop();
        log.info("查询相关文档耗时:{}", stopWatch.getTotalTimeMillis());
        return resp;
    }


    @Override
    public Map<String, Integer> keywordDocHits(Long recordId, List<String> kws){

        Map<String, Integer> keywordDocHits = new HashMap<>(kws.size());
        //关键字聚合
        TermsAggregation.Builder nameAggrBuilder = new TermsAggregation.Builder();
        nameAggrBuilder.field("metadataIdValue.metadataValueList.keyword").size(10)
                .include(in -> in.terms(kws));
        TermsAggregation.Builder idAggregation = new TermsAggregation.Builder();
        idAggregation.field("metadataIdValue.metadataName").size(10)
                .include(in -> in.terms(
                        Lists.newArrayList(plssFixedMetadataNameConfig.getFixedKeywordName())));
        Aggregation.Builder.ContainerBuilder builder1 = new Aggregation.Builder().terms(idAggregation.build());
        builder1.aggregations("valueAggr", nameAggrBuilder.build()._toAggregation());

        Query.Builder queryBuild = getPermissionBuilder(kws, recordId, Collections.emptyList());
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        SearchRequest request = searchBuilder
                .query(queryBuild.build())
                .index(INDEX_NLP_RECORD.getIndexName())
                .aggregations("keyword", f -> f.nested(f1 ->
                        f1.path("metadataIdValue")).aggregations("nameAggr", builder1.build()))
                .from(0)
                .sort(s -> s.score(ss -> ss.order(SortOrder.Desc)))
                .size(0)
                .build();
        //Map<String,>
        SearchResponse<NlpRecord> search = null;
        try {
            search = elasticsearchClient.search(request, NlpRecord.class);
        } catch (IOException e) {
            log.error("ES查询相关文档失败", e);
            return keywordDocHits;
        }
        Map<String, Aggregate> aggregations = search.aggregations();
        Aggregate keywordAggr = aggregations.get("keyword");
        Aggregate nameAggr = keywordAggr.nested().aggregations().get("nameAggr");
        List<StringTermsBucket> nameAggrArray = nameAggr.sterms().buckets().array();
        for (StringTermsBucket longTermsBucket : nameAggrArray) {
            if(plssFixedMetadataNameConfig.getFixedKeywordName().equals(longTermsBucket.key().stringValue())){
                List<StringTermsBucket> caName = longTermsBucket.aggregations().get("valueAggr").sterms().buckets().array();
                for (StringTermsBucket termsBucket : caName) {
                    keywordDocHits.put(termsBucket.key().stringValue(), Convert.toLong(termsBucket.docCount()).intValue());
                }
            }
        }
        return keywordDocHits;
    }




    private static void printEsBySearchRequest(RequestBase request) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        JsonpMapper mapper = new JacksonJsonpMapper();
        JsonGenerator generator = mapper.jsonProvider().createGenerator(byteArrayOutputStream);
        mapper.serialize(request, generator);
        generator.close();
        log.info("打印DSL语句:{}", byteArrayOutputStream);
    }

    private Query.Builder getPermissionBuilder(List<String> kws, Long recordId, List<String> specKws) {

        List<MetadataListQueryItem> metaQueryList = new ArrayList<>();
        MetadataListQueryItem metadataListQueryItem = new MetadataListQueryItem();
        metadataListQueryItem.setMetadataName(plssFixedMetadataNameConfig.getFixedKeywordName());
        metadataListQueryItem.setSearchType(2);
        metadataListQueryItem.setMetadataValue(kws);
        metaQueryList.add(metadataListQueryItem);
        if (CollUtil.isNotEmpty(specKws)) {
            MetadataListQueryItem metadataListQueryItem2 = new MetadataListQueryItem();
            metadataListQueryItem2.setMetadataName(plssFixedMetadataNameConfig.getFixedKeywordName());
            metadataListQueryItem2.setSearchType(2);
            metadataListQueryItem2.setMetadataValue(specKws);
            metaQueryList.add(metadataListQueryItem2);
        }
        SearchRecordV2QueryReq req = new SearchRecordV2QueryReq();
        req.setFilterRecordIds(Arrays.asList(recordId));
        req.setMetadataList(metaQueryList);
        req.setCurrentUserId(SecurityUtils.getUserId());
        return searchRecordServicel.buildSearchEsQueryBuilder(req);
    }


    private static List<SortOptions> buildSortOptions(List<String> kws){
        List<SortOptions> sortList = new ArrayList<>();
        if(CollUtil.isNotEmpty(kws)){
            SortOptions kwSort = new SortOptions.Builder().script(s-> s.script(sc -> sc.inline(i -> i.source(orderByHitKwsScript(kws)))).type(ScriptSortType.Number).order(SortOrder.Desc)).build();
            sortList.add(kwSort);
        }
        SortOptions scoreSort = SortOptions.of(s -> s.score(ss -> ss.order(SortOrder.Desc)));
        SortOptions releaseTimeSort = SortOptions.of(s -> s.field(f -> f.field("releaseTime").order(SortOrder.Desc)));
        sortList.add(scoreSort);
        sortList.add(releaseTimeSort);
        return sortList;
    }

    /**
     * metadataValueType 2 3 4 代表元数据为日期格式类型
     */
    private static String virtualYearScript() {
        String script = "def minDate = null;" +
                "        for(item in params._source.metadataIdValue ){" +
                "          if((item.metadataValueType == 2 || item.metadataValueType == 3 || item.metadataValueType == 4) && item.metadataValue != null){" +
                "            def date = LocalDate.parse(item.metadataValue);" +
                "            if (minDate == null || date.isBefore(minDate)){" +
                "              minDate=date" +
                "            }" +
                "          }" +
                "        }" +
                "        if(minDate == null){" +
                "          emit(0);" +
                "        }else{" +
                "          emit(minDate.getYear());" +
                "        }";
        return script;
    }


    private static String orderByHitKwsScript(List<String> kws) {
        StringBuilder kwStr = new StringBuilder();
        for (String kw : kws) {
            kwStr.append("            targetKeywords.add('").append(kw).append("');");
        }
        String script =     "int count = 0;" +
                             // 显式创建目标关键词集合
                "            def targetKeywords = new ArrayList();" +
                             kwStr +
                //"            targetKeywords.add('妇幼保健院');" +
                //"            targetKeywords.add('部门决算');" +
                //"            targetKeywords.add('单位决算');" +
                             // 遍历 metadataIdValue 嵌套文档
                "            if (params._source.metadataIdValue != null) {" +
                "              for (def item : params._source.metadataIdValue) {" +
                                 // 仅处理 metadataName 为关键词的条目
                "                if (item.metadataName == '关键词') {" +
                "                  def values = item.metadataValues;" +
                                   // 处理数组类型的 metadataValues
                "                  if (values instanceof List) {" +
                "                    for (def valueEntry : values) {" +
                                       // 直接获取字符串值（假设 metadataValues 存储的是字符串）
                "                      if (valueEntry != null && targetKeywords.contains(valueEntry)) {" +
                "                        count++;" +
                "                      }" +
                "                    }" +
                "                  }" +
                                   // 处理单个值的情况
                "                  else if (values != null && targetKeywords.contains(values)) {" +
                "                    count++" +
                "                  }" +
                "                }" +
                "              }" +
                "            }" +
                "            return count;";
        return script;
    }


}
