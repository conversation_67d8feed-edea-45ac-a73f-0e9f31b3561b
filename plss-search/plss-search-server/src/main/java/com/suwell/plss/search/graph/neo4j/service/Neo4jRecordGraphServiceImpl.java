
package com.suwell.plss.search.graph.neo4j.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.record.model.KnowledgeMergeReq;
import com.suwell.plss.record.service.RecordKnowledgeRpcService;
import com.suwell.plss.search.graph.service.RecordGraphService;
import com.suwell.plss.search.standard.dto.graph.*;
import com.suwell.plss.search.standard.dto.graph.request.*;
import com.suwell.plss.search.standard.dto.graph.response.LabelValueResp;
import com.suwell.plss.search.standard.dto.response.*;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.internal.InternalNode;
import org.neo4j.driver.internal.InternalRelationship;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jClient.RunnableSpec;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * neo4j文档知识图谱服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class Neo4jRecordGraphServiceImpl implements RecordGraphService {

    @Resource
    private Neo4jClient neo4jClient;

    @Resource
    private RecordKnowledgeRpcService recordKnowledgeRpcService;

    @Value("${knowledge.merge.compare:true}")
    private Boolean mergeCompare;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSingleRecordGraph(RecordKnowledgeGraph recordKnowledgeGraph) {
        Long recordId = recordKnowledgeGraph.getRecordId();
        String title = recordKnowledgeGraph.getTitle();
        log.info("准备保存文档知识知识图谱数据到neo4j,recordId:{}", recordId);
        try {
            List<RecordKnowledgeGraphSubject> subjectList = recordKnowledgeGraph.getSubjectList();
            //删除原图谱数据
            deleteRecordGraph(recordId);
            saveRecord(recordId, title);
            //保存三元组节点
            saveSubjectNode(recordId, subjectList, recordKnowledgeGraph.getConceptAutoMegerAttrMap(),
                    recordKnowledgeGraph.isAutoMerge());
        } catch (Exception e) {
            log.error("保存文档知识知识图谱数据到neo4j异常,recordId:{}", recordId, e);
        }
    }

    @Override
    public List<RecordKnowledgeGraphPredicate> queryRelationsByRecordId(Long recordId) {
        String cypher = "MATCH (m:RecordKnowledgeNode)-[r:REL{recordId:$recordId}]->(n:RecordKnowledgeNode) RETURN m,r,n";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher).bind(recordId).to("recordId").fetch().all();
        log.info("recordId:{},查询文档知识知识图谱数据cypher:{},结果:{}", recordId, cypher, recordGraphList);
        if (CollectionUtils.isEmpty(recordGraphList)) {
            return null;
        }
        List<RecordKnowledgeGraphPredicate> predicateList = new ArrayList<>();
        for (Map<String, Object> recordSubject : recordGraphList) {
            InternalNode subjectNode = (InternalNode) recordSubject.get("m");
            InternalRelationship relationNode = (InternalRelationship) recordSubject.get("r");
            InternalNode objectNode = (InternalNode) recordSubject.get("n");
            if (subjectNode != null && relationNode != null && objectNode != null) {
                RecordKnowledgeGraphPredicate predicate = new RecordKnowledgeGraphPredicate();
                Map<String, Object> predicateAttributeMap = relationNode.asMap();
                predicate.setUniqueId(Long.parseLong(predicateAttributeMap.get("uniqueId").toString()));
                predicate.setRelName(predicateAttributeMap.get("relName").toString());
                Map<String, Object> objectNodeMap = objectNode.asMap();
                RecordKnowledgeGraphObject object = new RecordKnowledgeGraphObject();
                object.setUniqueId(Long.parseLong(objectNodeMap.get("uniqueId").toString()));
                object.setLabel(objectNodeMap.get("label").toString());
                object.setValue(objectNodeMap.get("value").toString());
                predicate.setGraphObject(object);
                predicateList.add(predicate);
            }
        }
        return predicateList;
    }

    @Override
    public List<KnowledgeInfoResp> searchKnowledge(String subjectName, String subName, int skip, int limit, int sourceSkip, int sourceLimit) {
        //转义特殊字符
        String subjectEscapeName = StringUtils.escapeSpecialChar(subjectName);
        Collection<Map<String, Object>> recordGraphList;
        if (!StringUtils.isEmpty(subName)) {
            String cypher = "call db.index.fulltext.queryNodes(\"RecordKnowledgeNode\",$subjectName) yield node AS rkn where rkn.label=$subName return distinct rkn skip $skip limit $limit ";
            recordGraphList = neo4jClient.query(cypher)
                    .bind(subjectEscapeName).to("subjectName")
                    .bind(subName).to("subName")
                    .bind(skip).to("skip")
                    .bind(limit).to("limit")
                    .fetch().all();
        } else {
            String cypher = "call db.index.fulltext.queryNodes(\"RecordKnowledgeNode\",$subjectName) yield node AS rkn return distinct rkn skip $skip limit $limit ";
            recordGraphList = neo4jClient.query(cypher)
                    .bind(subjectEscapeName).to("subjectName")
                    .bind(skip).to("skip")
                    .bind(limit).to("limit")
                    .fetch().all();
        }
        List<KnowledgeInfoResp> knowledgeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            recordGraphList.forEach(map -> {
                KnowledgeInfoResp knowledge = new KnowledgeInfoResp();
                List<KnowledgeInfoAttrResp> attrs = new ArrayList<>();
                InternalNode node = (InternalNode) map.get("rkn");
                List<Long> ids = new ArrayList<>();
                Map<String, Object> rkn = node.asMap();
                rkn.forEach((k, v) -> {
                    if ("label".equals(k)) {
                        knowledge.setSubName(v.toString());
                    } else if ("value".equals(k)) {
                        knowledge.setSubValue(v.toString());
                    } else if ("uniqueId".equals(k)) {
                        knowledge.setId(Long.valueOf(v.toString()));
                        ids.add(knowledge.getId());
                    } else {
                        KnowledgeInfoAttrResp attr = new KnowledgeInfoAttrResp();
                        attr.setName(k);
                        attr.setValue(v.toString());
                        attrs.add(attr);
                    }
                });
                knowledge.setAttrs(attrs);
                List<KnowledgeInfoPredicate> predicates = searchKnowledgePredicate(ids);
                List<KnowledgeInfoPredicate> subjects = searchKnowledgeSubject(ids);
                knowledge.setPredicates(predicates);
                knowledge.setSubjects(subjects);
                PageUtils<KnowledgeInfoSourceResp> knowledgeInfoSourceResps = searchKnowledgeRecord(ids, sourceSkip, sourceLimit,null);
                knowledge.setSources(knowledgeInfoSourceResps);
                knowledgeList.add(knowledge);
                if (limit == 1) {
                    knowledge.setHitCount(countKnowledge(subjectEscapeName, subName));
                    knowledge.setPredicatesCount(countPredicate(ids.get(0)));
                }
            });
        }
        log.info("recordId:{},查询文档知识图谱数据 结果:{}",subjectEscapeName,  knowledgeList);
        orderConceptAttr(knowledgeList);
        return knowledgeList;
    }

    @Override
    public int countKnowledge(String subjectName, String subName) {
        String cypher = null;
        String subjectEscapeName = StringUtils.escapeSpecialChar(subjectName);
        if (StringUtils.isNotEmpty(subName)) {
            cypher = "call db.index.fulltext.queryNodes(\"RecordKnowledgeNode\",$subjectName) yield node AS rkn where rkn.label=$subName  return count(distinct rkn) as count ";
        } else {
            cypher = "call db.index.fulltext.queryNodes(\"RecordKnowledgeNode\",$subjectName) yield node AS rkn return count(distinct rkn) as count ";
        }

        RunnableSpec query = neo4jClient.query(cypher).bind(subjectEscapeName).to("subjectName");
        if (StringUtils.isNotEmpty(subName)) {
            query.bind(subName).to("subName");
        }

        Collection<Map<String, Object>> recordGraphList = query.fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            Map<String, Object> map = recordGraphList.iterator().next();
            return Integer.valueOf(map.get("count").toString());
        }
        return 0;
    }


    @Override
    public KnowledgeInfoResp searchKnowledgeById(Long uniqueId, int sourceSkip, int sourceLimit) {
        String cypher = "match (rkn:RecordKnowledgeNode{uniqueId:$uniqueId}) return  rkn ";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(uniqueId).to("uniqueId")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            Map<String, Object> map = recordGraphList.iterator().next();
            InternalNode node = (InternalNode) map.get("rkn");
            List<Long> ids = new ArrayList<>();
            KnowledgeInfoResp knowledge = new KnowledgeInfoResp();
            knowledge.setId(uniqueId);
            List<KnowledgeInfoAttrResp> attrs = new ArrayList<>();
            Map<String, Object> rkn = node.asMap();
            rkn.forEach((k, v) -> {
                if ("label".equals(k)) {
                    knowledge.setSubName(v.toString());
                } else if ("value".equals(k)) {
                    knowledge.setSubValue(v.toString());
                } else if ("uniqueId".equals(k)) {
                    knowledge.setId(Long.valueOf(v.toString()));
                    ids.add(knowledge.getId());
                } else {
                    KnowledgeInfoAttrResp attr = new KnowledgeInfoAttrResp();
                    attr.setName(k);
                    attr.setValue(v.toString());
                    attrs.add(attr);
                }
            });
            knowledge.setAttrs(attrs);
            List<KnowledgeInfoPredicate> predicates = searchKnowledgePredicate(ids);
            List<KnowledgeInfoPredicate> subjects = searchKnowledgeSubject(ids);
            knowledge.setPredicates(predicates);
            knowledge.setSubjects(subjects);
            knowledge.setPredicatesCount(countPredicate(ids.get(0)));
            orderConceptAttr(Lists.newArrayList(knowledge));
            return knowledge;
        }
        return null;
    }

    @Override
    public List<KnowledgeInfoPredicateSub> getKnowledgeByRecordId(Long recordId) {
        String cypher = "match (n:RecordKnowledgeNode) -[r:REL{recordId:$recordId}]->(m:RecordKnowledgeNode) return distinct m as result " +
                "union" +
                " match (n1:RecordGraph{uniqueId:$recordId}) -[r:`包含`]->(m1:RecordKnowledgeNode) return distinct m1 as result ";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(recordId).to("recordId")
                .fetch().all();
        List<KnowledgeInfoPredicateSub> knowledgeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            for (Map<String, Object> map : recordGraphList) {
                InternalNode node = (InternalNode) map.get("result");
                Map<String, Object> rkn = node.asMap();
                KnowledgeInfoPredicateSub knowledge = new KnowledgeInfoPredicateSub();
                knowledge.setType(rkn.get("label").toString());
                knowledge.setValue(rkn.get("value").toString());
                knowledge.setId(Long.valueOf(rkn.get("uniqueId").toString()));
                knowledgeList.add(knowledge);
            }
        }
        return knowledgeList;
    }


    @Override
    public List<KnowledgeInfoPredicate> searchKnowledgePredicate(List<Long> ids) {
        String cypher = "match (n:RecordKnowledgeNode) -[r:REL]->(m:RecordKnowledgeNode) where n.uniqueId in $ids  " +
                "with r.relName as relName,m as mm return relName,collect(mm) as js";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(ids).to("ids")
                .fetch().all();
        List<KnowledgeInfoPredicate> predicates = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            for (Map<String, Object> map : recordGraphList) {
                KnowledgeInfoPredicate predicate = new KnowledgeInfoPredicate();
                predicate.setRelName(map.get("relName").toString());
                List<KnowledgeInfoPredicateSub> objects = new ArrayList<>();
                List<InternalNode> mm = (List<InternalNode>) map.get("js");
                for (InternalNode internalNode : mm) {
                    KnowledgeInfoPredicateSub knowledge = new KnowledgeInfoPredicateSub();
                    Map<String, Object> rkn = internalNode.asMap();
                    rkn.forEach((k, v) -> {
                        if ("value".equals(k)) {
                            knowledge.setValue(v.toString());
                        } else if ("uniqueId".equals(k)) {
                            if (v != null) {
                                knowledge.setId(Long.parseLong(v.toString()));
                            }
                        } else if ("label".equals(k)) {
                            knowledge.setType(v.toString());
                        }
                    });
                    objects.add(knowledge);
                }
                predicate.setSubs(objects);
                List<String> types = objects.stream().map(KnowledgeInfoPredicateSub::getType)
                        .distinct().collect(Collectors.toList());
                predicate.setTypes(types);
                predicates.add(predicate);
            }
        }
        //#21208 按照关系排序。
        predicates.sort((o1, o2) -> o1.getRelName().length() - o2.getRelName().length());
        log.info("recordId:{},查询文档知识图谱数据cypher:{},结果:{}", cypher, recordGraphList, recordGraphList);
        return predicates;
    }

    public List<KnowledgeInfoPredicate> searchKnowledgeSubject(List<Long> ids) {
        String cypher = "match (n:RecordKnowledgeNode) -[r:REL]->(m:RecordKnowledgeNode) where m.uniqueId in $ids  " +
                "with r.relName as relName,n as mm return relName,collect(mm) as js";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(ids).to("ids")
                .fetch().all();
        List<KnowledgeInfoPredicate> predicates = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            for (Map<String, Object> map : recordGraphList) {
                KnowledgeInfoPredicate predicate = new KnowledgeInfoPredicate();
                predicate.setRelName(map.get("relName").toString());
                List<KnowledgeInfoPredicateSub> objects = new ArrayList<>();
                List<InternalNode> mm = (List<InternalNode>) map.get("js");
                for (InternalNode internalNode : mm) {
                    KnowledgeInfoPredicateSub knowledge = new KnowledgeInfoPredicateSub();
                    Map<String, Object> rkn = internalNode.asMap();
                    rkn.forEach((k, v) -> {
                        if ("value".equals(k)) {
                            knowledge.setValue(v.toString());
                        } else if ("uniqueId".equals(k)) {
                            if (v != null) {
                                knowledge.setId(Long.parseLong(v.toString()));
                            }
                        } else if ("label".equals(k)) {
                            knowledge.setType(v.toString());
                        }
                    });
                    objects.add(knowledge);
                }
                predicate.setSubs(objects);
                List<String> types = objects.stream().map(KnowledgeInfoPredicateSub::getType)
                        .distinct().collect(Collectors.toList());
                predicate.setTypes(types);
                predicates.add(predicate);
            }
        }
        //#21208 按照关系排序。
        predicates.sort((o1, o2) -> o1.getRelName().length() - o2.getRelName().length());
        log.info("recordId:{},查询文档知识图谱数据cypher:{},结果:{}", cypher, recordGraphList, recordGraphList);
        return predicates;
    }


    private int countPredicate(Long uniqueId) {
        String cypher = "match(rkn:RecordKnowledgeNode)--(rkn1:RecordKnowledgeNode) " +
                "where rkn.uniqueId in $ids " +
                "return count(distinct rkn1) as count ";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(Arrays.asList(uniqueId)).to("ids")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            Map<String, Object> map = recordGraphList.iterator().next();
            return Integer.valueOf(map.get("count").toString());
        }
        return 0;
    }

    @Override
    public PageUtils<KnowledgeInfoSourceResp> searchKnowledgeRecord(List<Long> ids, int skip, int limit, String recordName) {

        String cypher = null;
        Collection<Map<String, Object>> recordGraphList = null;
        if(StringUtils.isNotBlank(recordName)){
            cypher = "match(a:RecordGraph)-[*1..2]->(b:RecordKnowledgeNode) where b.uniqueId in $ids AND a.title LIKE $recordName return distinct a.uniqueId as "
                            + "id,a.title as title order by id skip $skip limit $limit";
            recordGraphList = neo4jClient.query(cypher)
                    .bind(ids).to("ids")
                    .bind(skip).to("skip")
                    .bind(limit).to("limit")
                    .bind(recordName).to("recordName")
                    .fetch().all();
        }else{
            cypher = "match(a:RecordGraph)-[*1..2]->(b:RecordKnowledgeNode) where b.uniqueId in $ids return distinct a.uniqueId as "
                            + "id,a.title as title order by id skip $skip limit $limit";
            recordGraphList = neo4jClient.query(cypher)
                    .bind(ids).to("ids")
                    .bind(skip).to("skip")
                    .bind(limit).to("limit")
                    .fetch().all();
        }

        List<KnowledgeInfoSourceResp> sourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            //构建 文档id -> 文档标题 map
            Map<Long, String> recordId2TitleMap = recordGraphList.stream().collect(
                    Collectors.toMap(map -> (Long) map.get("id"), map -> (String) map.get("title"), (v1, v2) -> v1));

            //RPC调用文档服务查询文档信息
            R<List<KnowledgeInfoSourceResp>> respR = recordKnowledgeRpcService.queryKnowledgeSourceRecord(
                    new ArrayList<>(recordId2TitleMap.keySet()));
            if (respR.isSuccess()) {
                sourceList = respR.getData();
                sourceList.forEach(source -> {
                    //文档不存在时取图谱中的标题
                    if (StringUtils.isBlank(source.getTitle())) {
                        source.setTitle(recordId2TitleMap.get(source.getRecordId()));
                    }
                });
            }
        }

        String cypherCount = "match(a:RecordGraph)-[*1..2]->(b:RecordKnowledgeNode) where b.uniqueId in $ids return count(distinct a.uniqueId) as total";
        Collection<Map<String, Object>> countResult = neo4jClient.query(cypherCount)
                .bind(ids).to("ids")
                .fetch().all();

        PageUtils<KnowledgeInfoSourceResp> pageUtils = new PageUtils<>();
        if (!CollectionUtils.isEmpty(countResult)) {
            Map<String, Object> map = countResult.iterator().next();
            pageUtils.setTotalCount((Long) map.get("total"));
        }
        pageUtils.setList(sourceList);
        pageUtils.setPage(skip / limit + 1);
        pageUtils.setPageSize(limit);
        return pageUtils;
    }

    @Override
    public List<KnowledgeSubCountResp> listSubCount(String subjectName) {

        String cypher = "call db.index.fulltext.queryNodes(\"RecordKnowledgeNode\",$subjectName) " +
                "yield node AS rkn " +
                "with distinct rkn as rck " +
                "return rck.label as label, count(distinct rck) as count ";
        Collection<Map<String, Object>> countResult = neo4jClient.query(cypher)
                .bind(subjectName).to("subjectName")
                .fetch().all();
        List<KnowledgeSubCountResp> subCountResps = new ArrayList<>();
        KnowledgeSubCountResp total = new KnowledgeSubCountResp();
        total.setSubName("全部");
        if (!CollectionUtils.isEmpty(countResult)) {
            countResult.forEach(map -> {
                KnowledgeSubCountResp subCountResp = new KnowledgeSubCountResp();
                subCountResp.setSubName(map.get("label").toString());
                subCountResp.setSubCount(Long.parseLong(map.get("count").toString()));
                subCountResps.add(subCountResp);
                total.setSubCount(total.getSubCount() + subCountResp.getSubCount());
            });
        }
        subCountResps.add(0, total);
        return subCountResps;
    }

    @Override
    public void addLabelValue(LabelValueAddReq req) {
        //增加知识节点
        String cypher = "match (m:RecordGraph{uniqueId:$recordId})" +
                " create (m)-[r:`包含`]->(n:RecordKnowledgeNode{label:$label,value:$value,uniqueId:$uniqueId})";
        neo4jClient.query(cypher).bind(req.getRecordId()).to("recordId")
                .bind(req.getLabel()).to("label")
                .bind(req.getValue()).to("value")
                .bind(req.getNewNodeId()).to("uniqueId")
                .fetch().all();
    }

    @Override
    public void deleteLabelValue(LabelValueAddReq req) {
        String relCypher = "match (r:RecordGraph{uniqueId:$recordId})-[rel:`包含`]->(n:RecordKnowledgeNode{uniqueId:$uniqueId}) delete rel";
        neo4jClient.query(relCypher).bind(req.getRecordId()).to("recordId")
                .bind(req.getNodeId()).to("uniqueId")
                .fetch().all();
        String deleteCypher = "match (n:RecordKnowledgeNode{uniqueId:$uniqueId})-[rel:`REL`{recordId:$recordId}]-> (:RecordKnowledgeNode) delete rel";
        neo4jClient.query(deleteCypher).bind(req.getNodeId()).to("uniqueId")
                .bind(req.getRecordId()).to("recordId")
                .fetch().all();
    }

    @Override
    public void editLabelValue(LabelValueAddReq req) {
        addLabelValue(req);
        nodeReplace(req.getNodeId(), req.getRecordId(), req.getNewNodeId());
    }

    /**
     * 主体节点切换   此方法不创建新节点与文档节点的关系
     * @param oldId 旧节点ID
     * @param recordId 文档节点ID
     * @param newId 新节点ID
     */
    private void nodeReplace(Long oldId, Long recordId, Long newId) {
        String cypher = "MATCH (:RecordKnowledgeNode{uniqueId:$oldId}) -[r:REL{recordId:$recordId}]-> (n:RecordKnowledgeNode) return n.uniqueId AS uid, r";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(oldId).to("oldId")
                .bind(recordId).to("recordId")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            //创建受体节点与新节点的关系
            List<Long> predicateIds = new ArrayList<>();
            for (Map<String, Object> map : recordGraphList) {
                Long uid = Long.parseLong(map.get("uid").toString());
                InternalRelationship r = (InternalRelationship) map.get("r");
                Map<String, Object> relProperties = r.asMap();
                String cypher1 = "match (m:RecordKnowledgeNode{uniqueId:$uniqueId}), (n:RecordKnowledgeNode{uniqueId:$uid}) " +
                        " create (m)-[r:REL $relProperties ]->(n) return r";
                neo4jClient.query(cypher1).bind(newId).to("uniqueId")
                        .bind(uid).to("uid")
                        .bind(relProperties).to("relProperties")
                        .fetch().all();
                predicateIds.add(uid);
            }

            //创建新节点与文档节点的关系
//            String cypher2 = "match (m:RecordGraph{uniqueId:$recordId}), (n:RecordKnowledgeNode{uniqueId:$newId}) " +
//                    " create (m)-[r:`包含`]->(n) return r";
//            neo4jClient.query(cypher2).bind(newId).to("newId")
//                    .bind(recordId).to("recordId")
//                    .fetch().all();
            //删除旧节点的关系
            String delPredicateCypher = "MATCH (:RecordKnowledgeNode{uniqueId:$oldId}) -[r:REL{recordId:$recordId}]-> (n:RecordKnowledgeNode) delete r";
            neo4jClient.query(delPredicateCypher)
                    .bind(oldId).to("oldId")
                    .bind(recordId).to("recordId")
                    .fetch().all();
        }
        //删除就节点与文档节点的关系
        String delCypher = "MATCH (:RecordGraph{uniqueId:$recordId}) -[r:`包含`]-> (n:RecordKnowledgeNode{uniqueId:$oldId}) delete r";
        neo4jClient.query(delCypher)
                .bind(recordId).to("recordId")
                .bind(oldId).to("oldId")
                .fetch().all();
    }


    @Override
    public void addLabelProperty(LabelPropertyAddReq req) {
        String cypher = "match (r:RecordKnowledgeNode{uniqueId:$oldObjectId}) return r";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(req.getNodeId()).to("oldObjectId")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            for (Map<String, Object> map : recordGraphList) {
                InternalNode r = (InternalNode) map.get("r");
                Map<String, Object> relProperties = r.asMap();
                Map<String, Object> newProperties = new HashMap<>();
                newProperties.putAll(relProperties);
                newProperties.put(req.getKey(), req.getValue());
                newProperties.put("uniqueId", req.getNewNodeId());
                String cypher1 = "match (n:RecordGraph{uniqueId:$recordId}) create (n)-[r:`包含`]-> (m:RecordKnowledgeNode $newProperties) ";
                neo4jClient.query(cypher1)
                        .bind(req.getRecordId()).to("recordId")
                        .bind(newProperties).to("newProperties")
                        .fetch().all();
                nodeReplace(req.getNodeId(), req.getRecordId(), req.getNewNodeId());
            }
        }
    }

    @Override
    public void deleteLabelProperty(LabelPropertyAddReq req) {
        String cypher = "match (r:RecordKnowledgeNode{uniqueId:$oldObjectId}) return r";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(req.getNodeId()).to("oldObjectId")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            for (Map<String, Object> map : recordGraphList) {
                InternalNode r = (InternalNode) map.get("r");
                Map<String, Object> relProperties = r.asMap();
                Map<String, Object> newProperties = new HashMap<>();
                newProperties.putAll(relProperties);
                newProperties.put("uniqueId", req.getNewNodeId());
                newProperties.remove(req.getKey());
                String cypher1 = "match (n:RecordGraph{uniqueId:$recordId}))  create (n)-[r:`包含`]-> (m:RecordKnowledgeNode $newProperties) ";
                neo4jClient.query(cypher1)
                        .bind(newProperties).to("newProperties")
                        .bind(req.getRecordId()).to("recordId")
                        .fetch().all();
                nodeReplace(req.getNodeId(), req.getRecordId(), req.getNewNodeId());
            }
        }
    }

    @Override
    public void mergeNode(Long sourceNodeId, Long targetNodeId, Map<String, Object> nodeProperties) {
        //更新target节点的属性
        String updateCypher = "match (n:RecordKnowledgeNode{uniqueId:$targetNodeId}) set n += $nodeProperties return n";
        neo4jClient.query(updateCypher)
                .bind(targetNodeId).to("targetNodeId")
                .bind(nodeProperties).to("nodeProperties")
                .fetch().all();

        //遍历与原节点相关的文档节点关系以及受体节点
        String cypher = "match (n:RecordKnowledgeNode{uniqueId:$sourceNodeId}) " +
                "-[rel:`REL`]-> (m:RecordKnowledgeNode) return rel , m.uniqueId as objectId";
        Collection<Map<String, Object>> recordGraphList = neo4jClient.query(cypher)
                .bind(sourceNodeId).to("sourceNodeId")
                .fetch().all();
        if (!CollectionUtils.isEmpty(recordGraphList)) {
            //修改原节点与受体节点的关系
            for (Map<String, Object> map : recordGraphList) {
                InternalRelationship rel = (InternalRelationship) map.get("rel");
                Map<String, Object> relPropertie = rel.asMap();
                Long recordId = (Long) relPropertie.get("recordId");
                Long objectId = (Long) map.get("objectId");
                String newRelCypher = "match (m:RecordKnowledgeNode{uniqueId:$targetNodeId}),(n:RecordKnowledgeNode{uniqueId:$objectId})" +
                        " create (m)-[r:REL $relProperties ]->(n) ";
                neo4jClient.query(newRelCypher)
                        .bind(targetNodeId).to("targetNodeId")
                        .bind(relPropertie).to("relProperties")
                        .bind(objectId).to("objectId")
                        .fetch().all();
                //删除原节点与受体节点的关系
                String delOldRelCypher = "MATCH (:RecordKnowledgeNode{uniqueId:$sourceNodeId})" +
                        " -[r:REL]-> (n:RecordKnowledgeNode{uniqueId:$objectId}) delete r";
                neo4jClient.query(delOldRelCypher)
                        .bind(sourceNodeId).to("sourceNodeId")
                        .bind(objectId).to("objectId")
                        .fetch().all();
                //建立文档节点与新节点的关系
                String newRelCypher1 = "match (n:RecordGraph{uniqueId:$recordId}),(m:RecordKnowledgeNode{uniqueId:$targetNodeId}) " +
                        "create (n)-[r:`包含`]-> (m)";
                neo4jClient.query(newRelCypher1)
                        .bind(recordId).to("recordId")
                        .bind(targetNodeId).to("targetNodeId")
                        .fetch().all();
                //删除文档节点与原节点的关系
                String delOldRelCypher1 = "MATCH (:RecordGraph{uniqueId:$recordId})" +
                        " -[r:`包含`]-> (:RecordKnowledgeNode{uniqueId:$sourceNodeId}) delete r";
                neo4jClient.query(delOldRelCypher1)
                        .bind(recordId).to("recordId")
                        .bind(sourceNodeId).to("sourceNodeId")
                        .fetch().all();
            }
        }
    }

    @Override
    public void addLabelRelation(LabelRelationAddReq req) {
        String cypher = "match (m:RecordKnowledgeNode{uniqueId:$nodeId})" +
                " create (m)-[r:REL{uniqueId:$relId,recordId:$recordId,relName:$relName}]->" +
                "(n:RecordKnowledgeNode{uniqueId:$objectId,label:$label,value:$objectName}) return r";
        neo4jClient.query(cypher).bind(req.getNodeId()).to("nodeId")
                .bind(req.getRelId()).to("relId")
                .bind(req.getRecordId()).to("recordId")
                .bind(req.getRelName()).to("relName")
                .bind(req.getNewObjectId()).to("objectId")
                .bind(req.getObjectLabelName()).to("label")
                .bind(req.getObjectValue()).to("objectName")
                .fetch().all();
    }

    @Override
    public void editLabelRelation(LabelRelationAddReq req) {
        /*String cypher = "match (m:RecordKnowledgeNode{uniqueId:$nodeId})-[r:`REL`]->(n:RecordKnowledgeNode{uniqueId:$objectId}) return r";
        Optional<Map<String, Object>> first = neo4jClient.query(cypher)
                .bind(req.getNodeId()).to("nodeId")
                .bind(req.getObjectId()).to("objectId")
                .fetch().first();
        first.ifPresent(map ->{
            InternalRelationship r = (InternalRelationship)map.get("r");
            Map<String, Object> relProperties= r.asMap();
            Map<String, Object> newProperties = new HashMap<>();
            newProperties.putAll(relProperties);
            //如果受体名称没有更改，删除原关系，重新建立关系,不需要新增节点
            if(req.getNewObjectId() == null){
                //删除主体与受体之间的关系
                String delCypher = "match (m:RecordKnowledgeNode{uniqueId:$nodeId})-[r:`REL`{recordId:$recordId}]->(n:RecordKnowledgeNode{uniqueId:$objectId}) delete r";
                neo4jClient.query(delCypher)
                        .bind(req.getNodeId()).to("nodeId")
                        .bind(req.getObjectId()).to("objectId")
                        .bind(req.getRecordId()).to("recordId")
                        .fetch().all();
                newProperties.put("relName",req.getRelName());
                //重新建立主体与受体之间的关系
                String cypher1 = "match (m:RecordKnowledgeNode{uniqueId:$nodeId}), (n:RecordKnowledgeNode{uniqueId:$objectId})" +
                        " create (m)-[r:REL $relProperties]->(n) return r";
                neo4jClient.query(cypher1)
                        .bind(req.getNodeId()).to("nodeId")
                        .bind(req.getObjectId()).to("objectId")
                        .bind(newProperties).to("relProperties")
                        .fetch().all();
            }else{
                //如果受体名称更改，删除原关系，创建新节点，建立新关系
                //删除主体与受体之间的关系
                String delCypher = "match (m:RecordKnowledgeNode{uniqueId:$nodeId})-[r:`REL`{recordId:$recordId}]->(n:RecordKnowledgeNode{uniqueId:$objectId}) delete r";
                neo4jClient.query(delCypher)
                        .bind(req.getNodeId()).to("nodeId")
                        .bind(req.getObjectId()).to("objectId")
                        .bind(req.getRecordId()).to("recordId")
                        .fetch().all();
                newProperties.put("relName",req.getRelName());
                //创建新节点
                String cypher1 = "match (m:RecordKnowledgeNode{uniqueId:$nodeId})" +
                        " create (m) -[r:REL $relProperties] -> (n:RecordKnowledgeNode{uniqueId:$newObjectId,label:$label,value:$name})" +
                        " return n";
                neo4jClient.query(cypher1)
                        .bind(req.getNodeId()).to("nodeId")
                        .bind(req.getNewObjectId()).to("newObjectId")
                        .bind(req.getObjectLabelName()).to("label")
                        .bind(newProperties).to("relProperties")
                        .bind(req.getObjectValue()).to("name")
                        .fetch().all();
            }
        });*/
        //先删除原关系
        LabelRelationAddReq editReq = new LabelRelationAddReq();
        editReq.setRelId(req.getRelId());
        deleteLabelRelation(editReq);
        //再新增关系
        editReq.setRecordId(req.getRecordId());
        editReq.setNodeId(req.getNodeId());
        editReq.setNewObjectId(req.getNewObjectId());
        editReq.setObjectLabelName(req.getObjectLabelName());
        editReq.setObjectValue(req.getObjectValue());
        editReq.setRelName(req.getRelName());
        addLabelRelation(req);
    }

    @Override
    public void deleteLabelRelation(LabelRelationAddReq req) {
        String cypher = "match ()-[r:REL{uniqueId:$relationNodeId}]->() " +
                "delete r";
        neo4jClient.query(cypher).bind(req.getRelId()).to("relationNodeId").fetch().all();
    }

    @Override
    public void addLabelRelationProperty(LabelRelationPropertyReq req) {
        String cypher = "match ()-[r:REL{uniqueId:$relationNodeId}]->() " +
                "set r." + req.getKey() + "=$value return r";
        neo4jClient.query(cypher).bind(req.getRelationNodeId()).to("relationNodeId")
                .bind(req.getValue()).to("value")
                .fetch().all();
    }

    @Override
    public void deleteLabelRelationProperty(LabelRelationPropertyReq req) {
        String cypher = "match ()-[r:REL{uniqueId:$relationNodeId}]->() " +
                "remove r." + req.getKey() + " return r";
        neo4jClient.query(cypher).bind(req.getRelationNodeId()).to("relationNodeId").fetch().all();
    }

    /**
     * 查询标签实例
     */
    @Override
    public List<LabelValueResp> queryConflictByRecordId(ConflictQueryReq conflictQueryReq){
        Long recordId = conflictQueryReq.getRecordId();
        List<LabelValueReq> labelValueReqList = conflictQueryReq.getLabelValueReqList();
        List<LabelValueResp> labelValueRespList = new ArrayList<>();
        for (LabelValueReq labelValueReq : labelValueReqList) {
            String label = labelValueReq.getLabel();
            Set<String> values = labelValueReq.getValues();
            if (CollectionUtils.isEmpty(values)) {
                continue;
            }
            //根据标签和值查询实例
            String cypher = "MATCH (n:RecordGraph)-[:包含]->(m:RecordKnowledgeNode) where n.uniqueId <> $recordId and m.label=$label and m.value in $values RETURN distinct m";
            Map<String, Object> params = new HashMap<>();
            params.put("recordId", recordId);
            params.put("label", label);
            params.put("values", values);
            Collection<Map<String, Object>> valueList = neo4jClient.query(cypher).bindAll(params).fetch().all();
            log.info("查询标签和实例列表查询实例参数:{},结果:{}", JSON.toJSONString(params), valueList);
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }
            for (Map<String, Object> valueMap : valueList) {
                Object o = valueMap.get("m");
                if (o instanceof InternalNode internalNode) {
                    Map<String, Object> attributeMap = internalNode.asValue().asMap();
                    if (attributeMap != null) {
                        LabelValueResp labelValueResp = new LabelValueResp();
                        labelValueRespList.add(labelValueResp);
                        for (Map.Entry<String, Object> entry : attributeMap.entrySet()) {
                            String key = entry.getKey();
                            Object value = entry.getValue();
                            if (Objects.equals(key, "label")) {
                                labelValueResp.setLabel(value.toString());
                            } else if (Objects.equals(key, "value")) {
                                labelValueResp.setValue(value.toString());
                            } else if (Objects.equals(key, "uniqueId")) {
                                labelValueResp.setUniqueId(Long.parseLong(value.toString()));
                            } else {
                                LabelValueAttribute labelValueAttribute = new LabelValueAttribute();
                                labelValueAttribute.setName(key);
                                labelValueAttribute.setValue(value);
                                labelValueResp.getAttributes().add(labelValueAttribute);
                            }
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(labelValueRespList)) {
            for (LabelValueResp labelValueResp : labelValueRespList) {
                Long uniqueId = labelValueResp.getUniqueId();
                List<KnowledgeInfoPredicate> knowledgeInfoPredicates = searchKnowledgePredicate(Collections.singletonList(uniqueId));
                if (CollectionUtils.isEmpty(knowledgeInfoPredicates)) {
                    continue;
                }
                for (KnowledgeInfoPredicate knowledgeInfoPredicate : knowledgeInfoPredicates) {
                    String relName = knowledgeInfoPredicate.getRelName();
                    List<KnowledgeInfoPredicateSub> subs = knowledgeInfoPredicate.getSubs();
                    labelValueResp.getRelations().addAll(subs.stream().map(predicateSub -> {
                        LabelValueRelation labelValueRelation = new LabelValueRelation();
                        labelValueRelation.setName(relName);
                        labelValueRelation.setValue(predicateSub.getValue());
                        return labelValueRelation;
                    }).collect(Collectors.toList()));
                }
            }
        }
        return labelValueRespList;
    }


    private void deleteRecordGraph(Long recordId) {
        //todo 删除没有关联的节点
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("recordId", recordId);
        neo4jClient.query("MATCH (n:RecordGraph {uniqueId: $recordId}) DETACH DELETE n")
                .bindAll(paramMap)
                .run();
    }

    private void deleteRecordGraph(Long recordId,boolean deleteRelation) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("recordId", recordId);
        String cypher = "MATCH (n:RecordGraph {uniqueId: $recordId}) DETACH DELETE n";
        if(deleteRelation){
            cypher = "MATCH (n:RecordKnowledgeNode)-[rel:`REL`{recordId: $recordId}]-> (m:RecordKnowledgeNode) DETACH DELETE rel";
            //todo 删除没有关联的节点

        }
        neo4jClient.query(cypher)
                .bindAll(paramMap)
                .run();
    }


    private void saveRecord(Long recordId, String title) {
        if (recordId == null) {
            return;
        }
        //保存文档节点
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("recordId", recordId);
        paramMap.put("title", title);
        String cypher = "CREATE (n:RecordGraph {uniqueId: $recordId,title: $title})";
        neo4jClient.query(cypher).bindAll(paramMap).run();
        log.info("recordId:{},保存文档节点cypher:{}", recordId, cypher);
    }

    private void saveSubjectNode(Long recordId, List<RecordKnowledgeGraphSubject> subjectList,
                                 Map<String, List<String>> conceptAutoMegerAttrMap,
                                 boolean autoMerge) {
        if (CollectionUtils.isEmpty(subjectList)) {
            return;
        }
        for (RecordKnowledgeGraphSubject recordKnowledgeGraphSubject : subjectList) {
            String subjectLabel = recordKnowledgeGraphSubject.getLabel();
            String subjectValue = recordKnowledgeGraphSubject.getValue();
            if (StringUtils.isEmpty(subjectValue) || StringUtils.isEmpty(subjectLabel)) {
                continue;
            }
            //查询主体节点是否存在 存在则创建
            Long subjectId = saveSubject2Graph(recordId, recordKnowledgeGraphSubject.getUniqueId(),
                    subjectLabel, subjectValue,
                    recordKnowledgeGraphSubject.getAttributeMap(),
                    autoMerge,conceptAutoMegerAttrMap);
            //保存谓语节点
            List<RecordKnowledgeGraphPredicate> predicateList = recordKnowledgeGraphSubject.getPredicateList();
            if (CollectionUtils.isEmpty(predicateList)) {
                continue;
            }
            for (RecordKnowledgeGraphPredicate recordKnowledgeGraphPredicate : predicateList) {
                savePredicateNode(recordId, subjectId, subjectLabel, subjectValue,
                        recordKnowledgeGraphPredicate,conceptAutoMegerAttrMap,autoMerge);
            }
        }
    }

    private void savePredicateNode(Long recordId, Long subjectUniqueId,
                                   String subjectLabel, String subjectValue,
                                   RecordKnowledgeGraphPredicate recordKnowledgeGraphPredicate,
                                   Map<String, List<String>> conceptAutoMegerAttrMap,
                                   boolean autoMerge) {
        String relName = recordKnowledgeGraphPredicate.getRelName();
        RecordKnowledgeGraphObject graphObject = recordKnowledgeGraphPredicate.getGraphObject();
        if (StringUtils.isEmpty(relName) || graphObject == null) {
            return;
        }
        String objectLabel = graphObject.getLabel();
        String objectValue = graphObject.getValue();
        if (StringUtils.isEmpty(objectLabel) || StringUtils.isEmpty(objectValue)) {
            return;
        }
        Map<String, Object> predicateAttributeMap = recordKnowledgeGraphPredicate.getAttributeMap();
        Map<String, Object> objectAttributeMap = graphObject.getAttributeMap();
        Long objectRecordId = graphObject.getRecordId();
        if (objectAttributeMap == null) {
            objectAttributeMap = new HashMap<>();
        }
        if (objectRecordId != null) {
            objectAttributeMap.put("objectRecordId", objectRecordId);
        }
        objectAttributeMap.put("value", objectValue);
        objectAttributeMap.put("label", objectLabel);
        objectAttributeMap.put("uniqueId", graphObject.getUniqueId());
        if (predicateAttributeMap == null) {
            predicateAttributeMap = new HashMap<>();
        }
        predicateAttributeMap.put("relName", relName);
        predicateAttributeMap.put("uniqueId", recordKnowledgeGraphPredicate.getUniqueId());
        //关系添加recordId属性
        predicateAttributeMap.put("recordId", recordId);

        savePredicate2Graph(recordId, subjectUniqueId,
                graphObject.getUniqueId(), objectLabel, objectValue,
                autoMerge, objectAttributeMap, conceptAutoMegerAttrMap,
                predicateAttributeMap);
    }

    @Override
    public List<KnowledgeNodeResp> queryNodeByLabelAndValue(String label,String value){
        if(StringUtils.isEmpty(label) || StringUtils.isEmpty(value)){
            log.error("queryNodeByLabelAndValue label或value为空");
            return Collections.EMPTY_LIST;
        }
        String cypher = "MATCH (n:RecordKnowledgeNode {label: $label,value: $value}) RETURN n";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("label", label);
        paramMap.put("value", value);
        Collection<Map<String, Object>> valueList = neo4jClient.query(cypher).bindAll(paramMap).fetch().all();
        log.info("MATCH (n:RecordKnowledgeNode [label: {},value: {}]) RETURN n 查询结果:{}", label, value, valueList);
        if (CollectionUtils.isEmpty(valueList)) {
            return Collections.EMPTY_LIST;
        }
        List<KnowledgeNodeResp> KnowledgeNodeResp = Lists.newArrayList();
        for (Map<String, Object> map : valueList) {
            InternalNode node = (InternalNode) map.get("n");
            if(node != null){
                KnowledgeNodeResp nodeResp = new KnowledgeNodeResp();
                Map<String,Object> attributeMap = new HashMap<>();
                nodeResp.setAttributeMap(attributeMap);
                Map<String, Object> data = node.asMap();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    String k = entry.getKey();
                    Object v = entry.getValue();
                    if ("value".equals(k)) {
                        nodeResp.setValue(v.toString());
                    } else if ("uniqueId".equals(k)) {
                        if (v != null) {
                            nodeResp.setUniqueId(Long.parseLong(v.toString()));
                        }
                    } else if ("label".equals(k)) {
                        nodeResp.setLabel(v.toString());
                    }else{
                        attributeMap.put(k,v.toString());
                    }
                }
                KnowledgeNodeResp.add(nodeResp);
            }
        }
        return KnowledgeNodeResp;
    }

    /**
     *
     * @param recordId 文档ID
     * @param nodeId 节点ID
     * @param label 节点类型（概念名称）
     * @param value 节点值 （概念实例名称）
     * @param currentAttr 节点属性
     * @param autoMerge 是否自动合并
     * @param conceptAutoMergeAttrMap 配置自动合并的属性
     */
    private Long saveSubject2Graph(Long recordId, Long nodeId, String label, String value,
                                Map<String,Object> currentAttr,
                                boolean autoMerge,Map<String, List<String>> conceptAutoMergeAttrMap){
        if(autoMerge){
            return mergeSubject2Graph(recordId, nodeId,label, value, currentAttr, conceptAutoMergeAttrMap);
        } else {
            if (currentAttr == null) {
                currentAttr = new HashMap<>();
            }
            currentAttr.put("value", value);
            currentAttr.put("label", label);
            currentAttr.put("uniqueId", nodeId);
            String attributeStr = getAttributeStr(currentAttr);
            String cypher = "MATCH (n:RecordGraph {uniqueId: #recordId}) CREATE (n)-[:包含]->(m:RecordKnowledgeNode #subjectAttributes)";
            cypher = cypher.replace("#recordId", String.valueOf(recordId))
                    .replace("#subjectAttributes", attributeStr);
            log.info("recordId:{}, 保存主体节点cypher:{}", recordId, cypher);
            //保存主语节点并关联文档节点
            neo4jClient.query(cypher).run();
            return nodeId;
        }
    }

    /**
     *  合并
     * @param recordId 文档ID
     * @param nodeId 当前节点ID
     * @param label 节点类型（概念名称）
     * @param value 节点实例（概念实例）
     * @param currentAttr 节点属性
     * @param conceptAutoMergeAttrMap 勾选了自动合并的属性
     */
    private Long mergeSubject2Graph(Long recordId, Long nodeId, String label, String value,
                                 Map<String,Object> currentAttr,
                                 Map<String, List<String>> conceptAutoMergeAttrMap){
        List<KnowledgeNodeResp> knowledgeNodeResp = queryNodeByLabelAndValue(label, value);
        if(!CollectionUtils.isEmpty(knowledgeNodeResp)){
            for (KnowledgeNodeResp knowledgeNode : knowledgeNodeResp) {
                Map<String, Object> nodeAttr = knowledgeNode.getAttributeMap();
                List<String> mergeAttr = conceptAutoMergeAttrMap.get(label);
                //判断匹配到的节点属性 配置的值是否相等
                boolean merge = true;
                if(CollUtil.isNotEmpty(mergeAttr)){
                    for (String attr : mergeAttr) {
                        Object existVal = nodeAttr.get(attr);
                        Object currentAttrVal = currentAttr.get(attr);
                        if(currentAttrVal == null || !currentAttrVal.equals(existVal)){
                            merge = false;
                        }
                    }
                } else if (mergeCompare) {
                    if(!CollectionUtils.isEmpty(currentAttr)){
                        for (Map.Entry<String, Object> entry : currentAttr.entrySet()) {
                            String k = entry.getKey();
                            Object v = entry.getValue();
                            if("uniqueId".equals(k) || "value".equals(k) || "label".equals(k)){
                                continue;
                            }
                            if(!v.equals(nodeAttr.get(k))){
                                merge = false;
                            }
                        }
                    }
                }else{
                    merge = false;
                }


                //合并并且更新
                if(merge){
                    nodeAttr.putAll(currentAttr);
                    nodeAttr.put("uniqueId", knowledgeNode.getUniqueId());
                    String cypher = "MATCH (record:RecordGraph {uniqueId: $recordId}), (n:RecordKnowledgeNode{uniqueId:$nodeId}) " +
                            "CREATE (record)-[:包含]->(n) set n += $nodeProperties";
                    neo4jClient.query(cypher)
                            .bind(recordId).to("recordId")
                            .bind(knowledgeNode.getUniqueId()).to("nodeId")
                            .bind(nodeAttr).to("nodeProperties")
                            .fetch().all();
                    //合并之后要删除属性表原来节点的数据，并且更新当前节点的数据，更行
                    KnowledgeMergeReq req = new KnowledgeMergeReq();
                    req.setRecordId(recordId);
                    req.setAttr( JSON.toJSONString(nodeAttr));
                    req.setNewNodeId(knowledgeNode.getUniqueId());
                    req.setOldNodeId(nodeId);
                    recordKnowledgeRpcService.mergeUpdate(req);
                    return knowledgeNode.getUniqueId();
                }
            }
            //没有匹配到  直接存储节点
        }
        return saveSubject2Graph(recordId, nodeId,label, value,currentAttr, false, conceptAutoMergeAttrMap);
    }

    /**
     *
     * @param recordId
     * @param nodeId
     * @param label
     * @param value
     * @param conceptAutoMergeAttrMap
     */
    private Long savePredicate2Graph(Long recordId, Long subjectNodeId,
                                     Long nodeId, String label,
                                     String value, boolean autoMerge,Map<String, Object> currentAttr,
                                     Map<String, List<String>> conceptAutoMergeAttrMap,
                                     Map<String,Object> relAttr){

        if(autoMerge){
            return mergePredicate2Graph(recordId, subjectNodeId, nodeId, label, value, currentAttr, conceptAutoMergeAttrMap, relAttr);
        }
        String cypher = "MATCH (m:RecordKnowledgeNode {uniqueId: $subjectUniqueId})" +
                " CREATE (m)-[r:REL $relAttr]->(o:RecordKnowledgeNode $nodeProperties)";
        neo4jClient.query(cypher)
                .bind(subjectNodeId).to("subjectUniqueId")
                .bind(nodeId).to("objectUniqueId")
                .bind(currentAttr).to("nodeProperties")
                .bind(relAttr).to("relAttr")
                .run();
        log.info("recordId:{},保存关系节点cypher:{}", recordId, cypher);
        return nodeId;
    }


    private Long mergePredicate2Graph(Long recordId, Long subjectNodeId,
                                       Long nodeId, String label,
                                       String value,Map<String, Object> currentAttr,
                                       Map<String, List<String>> conceptAutoMergeAttrMap,
                                      Map<String,Object> relAttr){
        List<String> autoMergeAttr = conceptAutoMergeAttrMap.get(label);
        List<KnowledgeNodeResp> nodes = queryNodeByLabelAndValue(label, value);
        for (KnowledgeNodeResp node : nodes) {
            boolean merge = true;
            Map<String, Object> nodeAttr = node.getAttributeMap();
            if(!CollectionUtils.isEmpty(autoMergeAttr)){
                for (String attr : autoMergeAttr) {
                    Object existVal = nodeAttr.get(attr);
                    Object currentAttrVal = currentAttr.get(attr);
                    if(currentAttrVal == null || !currentAttrVal.equals(existVal)){
                        merge = false;
                    }
                }
            }else if(mergeCompare){
                if(!CollectionUtils.isEmpty(currentAttr)){
                    for (Map.Entry<String, Object> entry : currentAttr.entrySet()) {
                        String k = entry.getKey();
                        Object v = entry.getValue();
                        //基本数据不比较
                        if("uniqueId".equals(k) || "value".equals(k) || "label".equals(k)){
                            continue;
                        }
                        if(!v.equals(nodeAttr.get(k))){
                            merge = false;
                        }
                    }
                }
            }else{
                merge =false;
            }
            if(merge){
                nodeAttr.putAll(currentAttr);
                nodeAttr.put("uniqueId", node.getUniqueId());
                String cypher = "MATCH (n:RecordKnowledgeNode{uniqueId:$subjectUniqueId}), (m:RecordKnowledgeNode{uniqueId:$objectUniqueId}) " +
                        "CREATE (n)-[r:REL $relAttr]->(m) set m += $nodeProperties";
                neo4jClient.query(cypher)
                        .bind(subjectNodeId).to("subjectUniqueId")
                        .bind(node.getUniqueId()).to("objectUniqueId")
                        .bind(nodeAttr).to("nodeProperties")
                        .bind(relAttr).to("relAttr")
                        .run();
                log.info("recordId:{},保存关系节点cypher:{} ,subjectUniqueId{} objectUniqueId{}", recordId, cypher,
                        subjectNodeId, node.getUniqueId());
                KnowledgeMergeReq req = new KnowledgeMergeReq();
                req.setRecordId(recordId);
                req.setAttr( JSON.toJSONString(nodeAttr));
                req.setNewNodeId(node.getUniqueId());
                req.setOldNodeId(nodeId);
                //合并之后要删除属性表原来节点的数据，并且更新当前节点的数据，更新数据库
                recordKnowledgeRpcService.mergeUpdate(req);
                return node.getUniqueId();
            }
        }
        return savePredicate2Graph(recordId, subjectNodeId, nodeId, label, value, false, currentAttr, conceptAutoMergeAttrMap, relAttr);
    }

    private String getAttributeStr(Map<String, Object> attributeMap) {
        if (attributeMap == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        String attributeStr = "";
        stringBuilder.append("{");
        for (Map.Entry<String, Object> entry : attributeMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (StringUtils.isEmpty(key) || value == null) {
                continue;
            }
            stringBuilder.append(entry.getKey()).append(": ");
            if (value instanceof String) {
                stringBuilder.append("\"").append(entry.getValue()).append("\"").append(",");
            } else {
                stringBuilder.append(entry.getValue()).append(",");
            }
        }
        attributeStr = stringBuilder.substring(0, stringBuilder.length() - 1);
        attributeStr += "}";
        return attributeStr;
    }



    private void orderConceptAttr(List<KnowledgeInfoResp> knowledgeList){
        List<String> conceptNames = knowledgeList.stream().map(KnowledgeInfoResp::getSubName).collect(Collectors.toList());
        R<Map<String, Integer>> tableR = recordKnowledgeRpcService.queryConceptAttrOrder(conceptNames);
        if(tableR.isSuccess()){
            for (KnowledgeInfoResp knowledgeInfoResp : knowledgeList) {
                List<KnowledgeInfoAttrResp> attrs = knowledgeInfoResp.getAttrs();
                Optional.ofNullable(attrs).ifPresent(attrList->{
                    attrs.sort(Comparator.comparingInt(attr->{
                        Integer order = tableR.getData().get(knowledgeInfoResp.getSubName() + "_" + ((KnowledgeInfoAttrResp)attr).getName());
                        return order == null ? 0 : order;
                    }).reversed());
                });
            }
        }
    }
}