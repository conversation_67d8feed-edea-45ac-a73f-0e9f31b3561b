package com.suwell.plss.search.service.seek;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataTree;
import com.suwell.plss.record.standard.dto.response.RepoMetadataResp;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.search.entity.RepoDataAgg;
import com.suwell.plss.search.standard.dto.RepoSearchDataResp;
import com.suwell.plss.search.standard.dto.RepositorySearchDataTree;
import com.suwell.plss.search.standard.dto.request.ConditionItemQuery;
import com.suwell.plss.search.standard.dto.request.NavigationItemBarQuery;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchRecordV2QueryReq;
import com.suwell.plss.search.standard.dto.response.NavigationItemBarDTO;
import com.suwell.plss.search.standard.enums.NavigationTypeEnum;
import com.suwell.plss.search.standard.enums.SubjectLibraryTypeEnum;
import com.suwell.plss.system.api.domain.SysCategoryDTO;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.service.CategoryRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * ==========================
 * 开发：wei.wang
 * 创建时间：2023-04-24 11:10
 * 版本：1.0
 * 描述：抽象文档搜索服务
 * ==========================
 */
@Slf4j
@SuppressWarnings({"rawtypes"})
public abstract class AbstractDocumentSeekService {

    @Resource
    private CategoryRpcService categoryRpcService;

    public abstract SubjectLibraryTypeEnum type();

    public abstract RepoSearchDataResp queryRepoMetadataResp(Long subjectLibraryId);

    public abstract List<NavigationItemBarDTO> queryCondition(ConditionItemQuery query);


    public RepoSearchDataResp convert2RepoSearchDataResp(RepoMetadataResp repoMetadataResp, RepoDataAgg repoDataAgg){
        if(repoMetadataResp == null){
            return null;
        }
        RepoSearchDataResp searchDataResp = new RepoSearchDataResp();
        Long repoId = repoMetadataResp.getRepoId();
        searchDataResp.setRepoId(repoId);
        List<RepositoryMetadataTree> docTypeList = repoMetadataResp.getDocTypeList();
        if(!CollectionUtils.isEmpty(docTypeList)){
            List<RepositorySearchDataTree> searchDataDocTypeList = new ArrayList<>();
            for (RepositoryMetadataTree repositoryMetadataTree : docTypeList) {
                searchDataDocTypeList.add(convert2RepositorySearchDataTree(repositoryMetadataTree,null));
            }
            searchDataResp.setDocTypeList(searchDataDocTypeList);
        }
        if(repoDataAgg == null){
            repoDataAgg = new RepoDataAgg();
        }
        List<RepositoryMetadataTree> metadataTreeList = repoMetadataResp.getMetadataList();
        if(!CollectionUtils.isEmpty(metadataTreeList)){
            List<RepositorySearchDataTree> searchDataDocTypeList = new ArrayList<>();
            for (RepositoryMetadataTree repositoryMetadataTree : metadataTreeList) {
                if(Objects.equals(repositoryMetadataTree.getRetrieveType(),1)){
                    NlpRecord.MetadataIdValue metadataIdValue = new NlpRecord.MetadataIdValue();
                    repoDataAgg.getMetadataValues().add(metadataIdValue);
                    metadataIdValue.setMetadataId(repositoryMetadataTree.getNodeId());
                    metadataIdValue.setMetadataName(repositoryMetadataTree.getNodeName());
                    metadataIdValue.setMetadataValues(new ArrayList<>());
                    List<RepositoryMetadataTree> children = repositoryMetadataTree.getChildren();
                    if(!CollectionUtils.isEmpty(children)){
                        for (RepositoryMetadataTree child : children) {
                            metadataIdValue.getMetadataValues().add(child.getNodeName());
                        }
                    }
                }else if(Objects.equals(repositoryMetadataTree.getRetrieveType(),2)){
                    repoDataAgg.getCategoryIds().add(repositoryMetadataTree.getNodeId());
                }else if(Objects.equals(repositoryMetadataTree.getRetrieveType(),4)){
                    repoDataAgg.getFolderIds().add(repositoryMetadataTree.getNodeId());
                }
                searchDataDocTypeList.add(convert2RepositorySearchDataTree(repositoryMetadataTree,repoDataAgg));
            }
            searchDataResp.setMetadataList(searchDataDocTypeList);
        }
        return searchDataResp;
    }

    private RepositorySearchDataTree convert2RepositorySearchDataTree(RepositoryMetadataTree repositoryMetadataTree,RepoDataAgg repoDataAgg){
        if(repositoryMetadataTree == null){
            return null;
        }
        RepositorySearchDataTree repositorySearchDataTree = new RepositorySearchDataTree();
        BeanUtils.copyProperties(repositoryMetadataTree,repositorySearchDataTree);
        List<RepositoryMetadataTree> children = repositoryMetadataTree.getChildren();
        if(!CollectionUtils.isEmpty(children)){
            List<RepositorySearchDataTree> searchDataChildren = new ArrayList<>();
            for (RepositoryMetadataTree child : children) {
                if(repoDataAgg != null){
                    if(Objects.equals(child.getRetrieveType(),2)){
                        repoDataAgg.getCategoryIds().add(child.getNodeId());
                    }else if(Objects.equals(child.getRetrieveType(),4)){
                        repoDataAgg.getFolderIds().add(child.getNodeId());
                    }
                }
                searchDataChildren.add(convert2RepositorySearchDataTree(child,repoDataAgg));
            }
            repositorySearchDataTree.setChildren(searchDataChildren);
        }
        return repositorySearchDataTree;
    }

    public List<NavigationItemBarDTO> navigation(NavigationItemBarQuery query) {
        NavigationTypeEnum navigationType = query.getNavigationType();
        Long entryId = query.getEntryId();
        String entryName = query.getEntryName();
        if(navigationType == NavigationTypeEnum.NT_CATEGORY){
            //分类
            if(entryId == null){
                if(StringUtils.isEmpty(entryName)){
                    return Collections.emptyList();
                }
                R<List<SysCategory>> categoryIdListR = categoryRpcService.getCategoryListByName(entryName);
                if(!categoryIdListR.isSuccess()){
                    return Collections.emptyList();
                }
                List<SysCategory> categoryList = categoryIdListR.getData();
                if(CollectionUtils.isEmpty(categoryList)){
                    return Collections.emptyList();
                }
                entryId = categoryList.get(0).getId();
            }
            SysCategoryDTO category = new SysCategoryDTO();
            category.setId(entryId);
            R<List<SysCategory>> categoryListR = categoryRpcService.queryChildrenById(category);
            if(categoryListR.isSuccess()){
                List<NavigationItemBarDTO> childrenList = new ArrayList<>();
                List<SysCategory> sysCategoryList = categoryListR.getData();
                if(!CollectionUtils.isEmpty(sysCategoryList)){
                    for (SysCategory sysCategory : sysCategoryList) {
                        NavigationItemBarDTO navigationItemBar = new NavigationItemBarDTO();
                        navigationItemBar.setType(2);
                        navigationItemBar.setId(sysCategory.getId());
                        navigationItemBar.setName(sysCategory.getName());
                        navigationItemBar.setHasChildren(sysCategory.getHasChildren());
                        childrenList.add(navigationItemBar);
                    }
                }
                return childrenList;
            }
        }
        return Collections.emptyList();
    }

    public abstract RepoSearchDataResp searchRepoDataWithAgg(SearchRecordV2QueryReq req);
}
