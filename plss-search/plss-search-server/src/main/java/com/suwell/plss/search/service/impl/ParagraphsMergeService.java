package com.suwell.plss.search.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.frameworkset.util.StringUtil;
import com.suwell.plss.search.api.dto.request.ParagraphDto;
import com.suwell.plss.search.entity.dto.ParagraphPo;
import com.suwell.plss.search.util.StringUtils;
import com.suwell.plss.search.vo.ParagraphVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ParagraphsMergeService {

    /**
     * 段落合并后的最大长度
     */
    private final int maxLength = 1000;

    /**
     * 大纲的长度
     */
    private final int outlineLength = 200;


    /**
     * 正文lvl编码：
     */
    private final String textLvl = "9999";


    /**
     * 合并段落
     *
     * @param paragraphDtoList 段落列表
     * @return 合并后的段落列表
     */
    public List<ParagraphDto> paragraphMerge(List<ParagraphPo.Paragraph> paragraphDtoList) {
        List<ParagraphPo.Paragraph> preprocess = preprocess(paragraphDtoList);
        return mergeParagraphs2(preprocess);
    }

    /**
     * 合并段落3
     *
     * @param paragraphDtoList 段落列表
     * @return 合并后的段落列表
     */
    public List<ParagraphPo.Paragraph> paragraphOutlineMerge(List<ParagraphPo.Paragraph> paragraphDtoList) {
        List<ParagraphPo.Paragraph> preprocess = preprocess(paragraphDtoList);
        return outLineExtraction(preprocess);
    }

    /**
     * 分段提取4
     *
     * @param paragraphDtoList
     * @return
     */
    public List<ParagraphPo.Paragraph> paragraphRuleMerge(List<ParagraphPo.Paragraph> paragraphDtoList, List<String> rules, String textLvl) {
        List<ParagraphPo.Paragraph> preprocess = preprocess(paragraphDtoList);
        return ruleExtraction(preprocess, rules, textLvl);
    }


    /**
     * 将文档按照段落进行分段
     * @param paragraphDtoList
     * @return
     */
    public List<Map<String,Object>> paragraphSplit(List<ParagraphPo.Paragraph> paragraphDtoList) {
        List<ParagraphPo.Paragraph> preprocess = preprocess(paragraphDtoList);
        return paragraphsTruncate(preprocess);
    }

    /**
     * 提取大纲加正文
     * @param preprocess
     * @return
     */
    private List<Map<String,Object>> paragraphsTruncate(List<ParagraphPo.Paragraph> preprocess) {
        // 获取文档最大的标题与提纲
        List<Integer> outLineLvlList = getOutLineLvl(preprocess);
        int firstLvl = getFirstLevel(outLineLvlList);
        int secondLvl = getSecondLevel(outLineLvlList, firstLvl);
        int thirdLvl = getthirdLvlLevel(outLineLvlList, firstLvl, secondLvl);

        // 存储章节内容的段落索引列表
        List<String> textSections = new ArrayList<>();

        // 临时存储当前章节的段落索引
        StringBuffer currentTextBuffer = new StringBuffer();

        // 跟踪标题出现的次数
        int titleCount = 0;
        int  paragraphs =0;
        for (int i = 0; i < preprocess.size(); i++) {
            ParagraphPo.Paragraph paragraph = preprocess.get(i);
            // 判断当前段落是否是大纲段落
            if (containsOutLinePattern(paragraph, secondLvl, 8888)) {
                paragraphs++;
                // 如果当前已有文本且遇到新标题，则保存文本并重置缓冲区
                if (titleCount == 0 && currentTextBuffer.length() > 0) {
                    textSections.add(currentTextBuffer.toString());
                    currentTextBuffer.setLength(0);
                    titleCount++;
                }
                // 但为了保持原逻辑不变，我们保留它，并简化其操作
                if (!currentTextBuffer.isEmpty()) {
                    textSections.add(currentTextBuffer.toString());
                    currentTextBuffer.setLength(0);
                }
                // 添加当前大纲段落的索引到缓冲区
                currentTextBuffer.append(",").append(i);
            } else {
                if(paragraphs>0){
                    currentTextBuffer.append(",").append(i);
                }else{
                    // 非大纲段落，直接添加到当前章节的文本索引中
                    currentTextBuffer.append(",").append(i);
                }

            }
        }
        // 处理最后一部分文本（如果有）
        if (!currentTextBuffer.isEmpty()) {
            textSections.add(currentTextBuffer.toString());
        }
        int paragraphIndex = 1;
        // 获取每一个的段落的内容
        List<List<ParagraphPo.Paragraph>> list = getTextContent(preprocess, textSections, paragraphIndex, 9999);
        System.out.println(list);

        //获取标题  过滤出 lvl 等于 firstLvl 的段落
        List<ParagraphPo.Paragraph> firstLvlParagraphs = preprocess.stream()
                .filter(paragraph -> Integer.parseInt(paragraph.getLvl()) == firstLvl)
                .collect(Collectors.toList());
        //获取标题
        String title = firstLvlParagraphs.get(0).getText();

        List<Map<String,Object>> mapList = new ArrayList<>();
        //获取每一段的大纲
        for (List<ParagraphPo.Paragraph> paragraphList: list) {
           // 获取每一段的标题+大纲+正文
           String titleParagraph = title + "\n" + paragraphList.get(0).getText();

           //获取每一段的标题加大纲
           String text = paragraphList.get(0).getText();
           int newlineIndex = text.indexOf("\n");
           String contentBeforeNewline = newlineIndex != -1 ? text.substring(0, newlineIndex) : text;
           String contentAfterNewline = newlineIndex != -1 ? text.substring(newlineIndex + 1) : "";

           //获取每一段的标题加大纲
           Map<String,Object> map =new HashMap<>();
           //正文
           map.put("bodyText",contentAfterNewline);
           contentBeforeNewline = title + "\n" +contentBeforeNewline;

           //获取每一段的正文
           map.put("fullText",titleParagraph);
           // 标题+大纲
           map.put("titleText",contentBeforeNewline);
           mapList.add(map);
        }
        return mapList;
    }

    public List<ParagraphPo.Paragraph> preprocess(List<ParagraphPo.Paragraph> paragraphDtoList) {
        // 使用 Map 存储相同 index 的段落
        Map<Integer, List<ParagraphPo.Paragraph>> map = new TreeMap<>();

        // 将段落按照 index 存入 Map
        for (ParagraphPo.Paragraph paragraph : paragraphDtoList) {
            int index = paragraph.getIndex();
            if (!map.containsKey(index)) {
                map.put(index, new ArrayList<>());
            }
            map.get(index).add(paragraph);
        }

        // 对 Map 中相同 index 的段落进行合并并放入新的 List 中
        List<ParagraphPo.Paragraph> mergedList = new ArrayList<>();
        for (List<ParagraphPo.Paragraph> paragraphs : map.values()) {
            // 如果只有一个段落，则直接添加到新的 List 中
            if (paragraphs.size() == 1) {
                ParagraphPo.Paragraph paragraph = paragraphs.get(0);
                ParagraphPo.Paragraph.Location location = new ParagraphPo.Paragraph.Location();
                location.setPageIndex(paragraph.getPageIndex());
                location.setRect(paragraph.getRect());
                paragraph.setLocation(location);
                mergedList.add(paragraph);
            } else {
                // 对相同 index 的段落进行合并
                ParagraphPo.Paragraph mergedParagraph = mergeParagraphs(paragraphs);
                mergedList.add(mergedParagraph);
            }
        }
        return mergedList;
    }

    private ParagraphPo.Paragraph mergeParagraphs(List<ParagraphPo.Paragraph> paragraphs) {
        // 对段落进行排序，按照 index 从小到大的顺序
        paragraphs.sort(Comparator.comparingInt(ParagraphPo.Paragraph::getIndex));

        // 合并 text 和 rects
        StringBuilder mergedText = new StringBuilder();
        List<ParagraphPo.Paragraph.Location> mergedRects = new ArrayList<>();
        for (ParagraphPo.Paragraph paragraph : paragraphs) {
            mergedText.append(paragraph.getText());
            ParagraphPo.Paragraph.Location location = new ParagraphPo.Paragraph.Location();
            location.setPageIndex(paragraph.getPageIndex());
            location.setRect(paragraph.getRect());
            mergedRects.add(location);
        }

        // 创建合并后的段落对象
        ParagraphPo.Paragraph mergedParagraph = new ParagraphPo.Paragraph();
        mergedParagraph.setIndex(paragraphs.get(0).getIndex()); // 使用第一个段落的 index
        mergedParagraph.setPageIndex(paragraphs.get(0).getPageIndex()); // 使用第一个段落的页数
        mergedParagraph.setText(mergedText.toString());
        mergedParagraph.setRects(mergedRects); // 使用合并后的 rects
        // 使用最后一个段落的 lvl 和 cmptitle
        // 比较 lvl 和 cmptitle，保留较小的 lvl 值和较大的 cmptitle 值
        int minLvl = Integer.MAX_VALUE;
        String maxCmptitle = null;
        for (ParagraphPo.Paragraph paragraph : paragraphs) {
            int lvl = Integer.parseInt(paragraph.getLvl());
            if (lvl < minLvl) {
                minLvl = lvl;
            }
            String cmptitle = paragraph.getCmptitle();
            if (maxCmptitle == null || cmptitle.compareTo(maxCmptitle) > 0) {
                maxCmptitle = cmptitle;
            }
        }
        mergedParagraph.setLvl(String.valueOf(minLvl));
        mergedParagraph.setCmptitle(maxCmptitle);
        String timeDate = paragraphs.get(0).getTimedate();
        if (StringUtils.isEmpty(timeDate)) {
            timeDate = paragraphs.get(1).getTimedate();
        }
        mergedParagraph.setTimedate(timeDate);
        return mergedParagraph;
    }


    private List<ParagraphDto> mergeParagraphs2(List<ParagraphPo.Paragraph> paragraphDtoList) {
        try {
            String textId = textLvl;

            Map<Integer, ParagraphPo.Paragraph> allTxtMap = new HashMap<>();

            Map<Integer, ParagraphPo.Paragraph> outLineMap = new HashMap<>();
            Map<Integer, ParagraphPo.Paragraph> txtMap = new HashMap<>();
            Map<Integer, ParagraphPo.Paragraph> suspectedOutLineMap = new HashMap<>();

            for (int i = 0; i < paragraphDtoList.size(); i++) {
                ParagraphPo.Paragraph paragraphDto = paragraphDtoList.get(i);
                allTxtMap.put(i, paragraphDto);
                String lvl = paragraphDto.getLvl();
                String fuzzy = paragraphDto.getCmptitle();
                String text = paragraphDto.getText();
                if (!lvl.equals(textId) && !fuzzy.equals("1")) {
                    // 如果不是正文，也不是疑似大纲   =  纯大纲
                    outLineMap.put(i, paragraphDto);
                } else {
                    // 正文 或者 疑似大纲的段落
                    if (textId.equals(lvl)) {
                        //正文
                        txtMap.put(i, paragraphDto);
                    } else {
                        //疑似大纲
                        suspectedOutLineMap.put(i, paragraphDto);
                    }
                }
            }
            Map<Integer, ParagraphPo.Paragraph> allTxtFlagMap = new HashMap<>(allTxtMap);

            //处理疑似大纲的段落
            suspectedOutLineMap.keySet().forEach(index -> {
                //将获取该段落的下级段落， 检查其是否是正文 或者是子集大纲
                ParagraphPo.Paragraph susParagraph = suspectedOutLineMap.get(index);
                String susParagraphLvl = susParagraph.getLvl();
                //下一条数据
                int nestIndex = index + 1;
                if (textIsEnd(nestIndex, allTxtMap)) {
                    //视为正文
                    txtMap.put(index, susParagraph);
                    return;
                }
                ParagraphPo.Paragraph paragraphDto = allTxtMap.get(nestIndex);
                String lvl = paragraphDto.getLvl();
                ;
                if (textId.equals(lvl) || Integer.parseInt(susParagraphLvl) < Integer.parseInt(lvl)) {
                    //若 下级段落是正文  或者  下级段落是子集大纲，那么当前段落是大纲
                    outLineMap.put(index, susParagraph);
                } else {
                    //视为正文
                    txtMap.put(index, susParagraph);
                }
            });
            List<String> lastTxt = new ArrayList<>();
            List<String> mergeIndexList = new ArrayList<>();
            Map<Integer, ParagraphPo.Paragraph> txtMapCopy = new HashMap<>(txtMap);
            //找到正文，以正文为边界，多个大纲之间的段落不合并

            txtMap.keySet().forEach(txtIndex -> {
                if (!txtMapCopy.containsKey(txtIndex)) {
                    return;
                }
                String mergeIndex = "";
                Map<String, String> mergeMap = new HashMap<>();
                ParagraphPo.Paragraph paragraphDto = txtMap.get(txtIndex);
                String text = paragraphDto.getText();
                //找到正文的上级大纲，一直往上找
                String upAllLine = findUpOutline(txtIndex, null, outLineMap, null, mergeIndex, allTxtFlagMap, mergeMap);

                System.out.println("--------截止 获取到文本的所有上级大纲 +\n" + upAllLine + "    和大纲的下标" + "---------" + mergeMap.get("mergeIndex"));

                //删除这个正文的标记
                allTxtFlagMap.remove(txtIndex);

                //拼接正文
                String lastText = StringUtils.isEmpty(upAllLine) ? text + "\n" : upAllLine + "\n" + text + "\n";
                //拼上当前正文的下标
                mergeIndex = StringUtils.isEmpty(mergeMap.get("mergeIndex")) ? String.valueOf(txtIndex) : mergeMap.get("mergeIndex") + "," + txtIndex;
                //判断拼接上正文，是否超过500字， 如果超过500字 则对正文按照句号进行拆分
                if (lastText.length() > maxLength) {
                    //对正文按照句号进行拆分
                    splitParagraphs(txtIndex, text, upAllLine, lastTxt, allTxtMap, mergeIndex, mergeIndexList);
                } else {
                    //找下一段正文 看看是否合适
                    String txt = findNextTxt(txtIndex, lastText, txtMap, allTxtMap, allTxtFlagMap, txtMapCopy, mergeIndex, mergeIndexList);
                    lastTxt.add(txt);
                }
            });
            //处理漏掉的大纲
            dealMissOutline(allTxtFlagMap, outLineMap, lastTxt, mergeIndexList, allTxtMap);

            System.out.println(lastTxt.size());

            //处理段落合并后 null的情况
            lastTxt.replaceAll(s -> s.replaceAll("null\n", ""));
            for (int i = 0; i < mergeIndexList.size(); i++) {
                String[] split = mergeIndexList.get(i).split(",");
                String txt = lastTxt.get(i);
                String[] split1 = txt.split("\n");
                if (split1.length != split.length) {
                    System.out.println("不一样的 是  " + i + "-----" + mergeIndexList.get(i));
                }
            }

            //根据所有合并的顺序， 组装dto对象
            List<ParagraphDto> lastParagraphDtoList = new ArrayList<>();
            for (int i = 0; i < mergeIndexList.size(); i++) {
                ParagraphDto paragraphDto = new ParagraphDto();
                //正文
                String txt = lastTxt.get(i);
                paragraphDto.setText(txt);

                List<Integer> passageIds = new ArrayList<>();
                List<Integer> txtIds = new ArrayList<>();
                List<List<Object>> rectList = new ArrayList<>();
                String[] indexSplit = mergeIndexList.get(i).split(",");


                for (String index : indexSplit) {
                    int indexInt = Integer.parseInt(index);
                    ParagraphPo.Paragraph paragraph = allTxtMap.get(indexInt);
                    passageIds.add(paragraph.getIndex());
                    List<ParagraphPo.Paragraph.Location> rects = paragraph.getRects();
                    if (CollectionUtils.isEmpty(rects)) {
                        ParagraphPo.Paragraph.Location location = paragraph.getLocation();
                        List<Object> rect = new ArrayList<>();
                        rect.add(location);
                        rectList.add(rect);
                    } else {
                        rectList.add(JSONArray.parseArray(JSON.toJSONString(rects), Object.class));
                    }
                    ParagraphPo.Paragraph txtPar = txtMap.get(indexInt);
                    if (!ObjectUtils.isEmpty(txtPar)) {
                        txtIds.add(txtPar.getIndex());
                    }
                }
                paragraphDto.setPassageIds(passageIds);
                paragraphDto.setRectList(rectList);
                paragraphDto.setSplitId(i);
                paragraphDto.setTextIds(txtIds);

                lastParagraphDtoList.add(paragraphDto);
            }
            return lastParagraphDtoList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("段落合并异常：{}", e.getMessage());
            return null;
        }
    }


    /**
     * 判断当前段落是否是整篇文档的最后一段
     */
    private boolean textIsEnd(int txtIndex, Map<Integer, ParagraphPo.Paragraph> allTxtMap) {
        int nextIndex = txtIndex + 1;
        ParagraphPo.Paragraph nextPar = allTxtMap.get(nextIndex);
        return ObjectUtils.isEmpty(nextPar);
    }


    /**
     * @param outlineIndex  当前正文索引
     * @param outlineLvl    上一级大纲的等级
     * @param outLineMap    大纲索引
     * @param lastOutline   上一级大纲的文本
     * @param allTxtFlagMap 所有文本
     * @return
     */
    private String findUpOutline(int outlineIndex,
                                 String outlineLvl,
                                 Map<Integer, ParagraphPo.Paragraph> outLineMap,
                                 String lastOutline,
                                 String mergeIndex,
                                 Map<Integer, ParagraphPo.Paragraph> allTxtFlagMap,
                                 Map<String, String> mergeMap) {
        if (outlineIndex <= 0) {
            return lastOutline;
        }
        int upOutlineIndex = outlineIndex - 1;
        ParagraphPo.Paragraph upOutlinePar = outLineMap.get(upOutlineIndex);
        if (!ObjectUtils.isEmpty(upOutlinePar) && StringUtils.isEmpty(outlineLvl) && StringUtils.isEmpty(lastOutline)) {
            outlineLvl = upOutlinePar.getLvl();
            if (StringUtils.isEmpty(outlineLvl)) {
                outlineLvl = textLvl;
            }
            lastOutline = upOutlinePar.getText();
            mergeIndex = String.valueOf(upOutlineIndex);
            mergeMap.put("mergeIndex", mergeIndex);
            //标记已经找过的大纲
            allTxtFlagMap.remove(upOutlineIndex);
        }
        //看看是不是outlineLvl的上级   >=
        if (ObjectUtils.isEmpty(upOutlinePar) || Integer.parseInt(outlineLvl) < (Integer.parseInt(upOutlinePar.getLvl()) + 1)) {
            //如果不是，继续往上找
            return findUpOutline(upOutlineIndex, outlineLvl, outLineMap, lastOutline, mergeIndex, allTxtFlagMap, mergeMap);
        } else {
            //如果是， 则拼接上级大纲， 然后判断是否超过200字，如果不超过 则继续往上找
            //判断加上之后 是否超过200字， 如果超过 就不加了
            String upOutlineText = upOutlinePar.getText();
            outlineLvl = upOutlinePar.getLvl();
            if (!StringUtils.isEmpty(lastOutline)) {
                String outline = upOutlineText + "\n" + lastOutline;
                if (outline.length() > outlineLength) {
                    return lastOutline;
                }
                lastOutline = outline;
                mergeIndex = upOutlineIndex + "," + mergeIndex;
                mergeMap.put("mergeIndex", mergeIndex);
            } else {
                lastOutline = upOutlineText;
                mergeIndex = String.valueOf(upOutlineIndex);
                mergeMap.put("mergeIndex", mergeIndex);
            }
            //标记已经找过的大纲
            allTxtFlagMap.remove(upOutlineIndex);
            //不超过200字，再往上找，并且删除map中的数据
            if (lastOutline.length() <= outlineLength) {
                return findUpOutline(upOutlineIndex, outlineLvl, outLineMap, lastOutline, mergeIndex, allTxtFlagMap, mergeMap);
            }
            return lastOutline;
        }
    }


    /**
     * 段落拆分
     */
    private void splitParagraphs(int txtIndex,
                                 String text,
                                 String upAllLine,
                                 List<String> lastTxt,
                                 Map<Integer, ParagraphPo.Paragraph> allTxtMap,
                                 String mergeIndex,
                                 List<String> mergeIndexList) {
        String[] split = text.split("(?<=。)");
        if (!text.contains("。")) {
            //不包括句号的话， 就不拆了
            //判断是否是最后一段
            if (textIsEnd(txtIndex, allTxtMap)) {
                lastTxt.add(StringUtils.isEmpty(upAllLine) ? text : upAllLine + "\n" + text);
            } else {
                lastTxt.add(StringUtils.isEmpty(upAllLine) ? text : upAllLine + "\n" + text + "\n");
            }
            mergeIndexList.add(mergeIndex);
            return;
        }
        StringBuilder last = new StringBuilder();
        StringBuilder next = new StringBuilder();
        int tag = 0;
        for (int i = 0; i < split.length; i++) {
            String sp = split[i];
            int upLineLength = 0;
            if (!StringUtils.isEmpty(upAllLine)) {
                upLineLength = upAllLine.length();
            }
            if (upLineLength + last.length() + sp.length() <= maxLength && tag == i) {
                last.append(sp);
                tag++;
            } else {
                //如果是split中的第一条，则强行拼在一起
                if (last.length() == 0 && sp.equals(split[0])) {
                    last.append(sp);
                    continue;
                }
                next.append(sp);
            }
        }

        String nextData = next.toString();
        if (StringUtils.isEmpty(upAllLine)) {
            lastTxt.add(!StringUtils.isEmpty(nextData) ? last.toString() : last + "\n");
        } else {
            lastTxt.add(!StringUtils.isEmpty(nextData) ? upAllLine + "\n" + last : upAllLine + "\n" + last + "\n");
        }
        mergeIndexList.add(mergeIndex);
        // 如果next为空，并且last没装满， 就找下一段正文
        if (!StringUtils.isEmpty(nextData)) {
            //对剩下的 next中的数据，看看是否超过500字， 超过的话 继续拆分
            splitParagraphs(txtIndex, nextData, upAllLine, lastTxt, allTxtMap, mergeIndex, mergeIndexList);
        }

    }


    /**
     * 递归找到下一段正文
     * 如果大纲+正文+下一段正文的长度不超过500字， 则继续 找下一段
     */
    private String findNextTxt(int txtIndex,
                               String text,
                               Map<Integer, ParagraphPo.Paragraph> txtMap,
                               Map<Integer, ParagraphPo.Paragraph> allTxtMap,
                               Map<Integer, ParagraphPo.Paragraph> allTxtFlagMap,
                               Map<Integer, ParagraphPo.Paragraph> txtMapCopy,
                               String mergeIndex,
                               List<String> mergeIndexList) {
        if (textIsEnd(txtIndex, allTxtMap)) {
            mergeIndexList.add(mergeIndex);
            return text.endsWith("\n") ? text.substring(0, text.lastIndexOf("\n")) : text;
        }
        //找到下一段正文
        int nextIndex = txtIndex + 1;
        ParagraphPo.Paragraph nextPar = txtMap.get(nextIndex);
        if (ObjectUtils.isEmpty(nextPar)) {
            mergeIndexList.add(mergeIndex);
            return text;
        }
        String nextText = nextPar.getText();
        String lvl = nextPar.getLvl();
        //判断是否是大纲，如果是大纲，判断长度是否超过限制， 不超过的情况下， 再找下一个正文，判断是否是大纲   是否超过限制范围，如果超过了， 则这个大纲也不要了


        int textLeg = text.length();
        int nowAndNextTextLeg = textLeg + nextText.length();
        if (nowAndNextTextLeg <= maxLength) {
            text = text + nextText;
            //判断下一段是否是最后一段
            if (!textIsEnd(nextIndex, allTxtMap)) {
                text = text + "\n";
            }
            mergeIndex = mergeIndex + "," + nextIndex;
            txtMapCopy.remove(nextIndex);
            allTxtFlagMap.remove(nextIndex);
            //再找下一段
            return findNextTxt(nextIndex, text, txtMap, allTxtMap, allTxtFlagMap, txtMapCopy, mergeIndex, mergeIndexList);
        }
        mergeIndexList.add(mergeIndex);
        return text;
    }


    /**
     * 处理漏掉的大纲
     */
    private void dealMissOutline(Map<Integer, ParagraphPo.Paragraph> allTxtFlagMap,
                                 Map<Integer, ParagraphPo.Paragraph> outLineMap,
                                 List<String> lastTxt,
                                 List<String> mergeIndexList,
                                 Map<Integer, ParagraphPo.Paragraph> allTxtMap) {
        Map<String, String> mergeMap = new HashMap<>();
        String maxLvl = null;
        int maxIndex = 0;
        if (!CollectionUtils.isEmpty(allTxtFlagMap)) {
            for (Integer flag : allTxtFlagMap.keySet()) {
                ParagraphPo.Paragraph paragraphDto = allTxtFlagMap.get(flag);
                String lvl = paragraphDto.getLvl();
                if (maxLvl == null || lvl.compareTo(maxLvl) > 0) {
                    maxLvl = lvl;
                    maxIndex = flag;
                }
            }
            //根据最大的lvl 寻找上级大纲
            String upAllLine = findUpOutline(maxIndex, maxLvl, outLineMap, null, null, allTxtFlagMap, mergeMap);
            ParagraphPo.Paragraph paragraph = allTxtFlagMap.get(maxIndex);
            String text = paragraph.getText();
            lastTxt.add(textIsEnd(maxIndex, allTxtMap) ? upAllLine + "\n" + text : upAllLine + "\n" + text + "\n");
            if (StringUtils.isEmpty(mergeMap.get("mergeIndex"))) {
                mergeMap.put("mergeIndex", String.valueOf(maxIndex));
            } else {
                mergeMap.put("mergeIndex", mergeMap.get("mergeIndex") + "," + maxIndex);
            }
            mergeIndexList.add(mergeMap.get("mergeIndex"));
            allTxtFlagMap.remove(maxIndex);
            dealMissOutline(allTxtFlagMap, outLineMap, lastTxt, mergeIndexList, allTxtMap);
        }
    }


    private List<ParagraphPo.Paragraph> outLineExtraction(List<ParagraphPo.Paragraph> paragraphDtoList) {
        // 获取文档最大的标题与提纲
        List<Integer> outLineLvlList = getOutLineLvl(paragraphDtoList);
        int firstLvl = getFirstLevel(outLineLvlList);
        int secondLvl = getSecondLevel(outLineLvlList, firstLvl);
        int thirdLvl = getthirdLvlLevel(outLineLvlList, firstLvl, secondLvl);

        // 存储章节内容的段落索引列表
        List<String> textSections = new ArrayList<>();

        // 临时存储当前章节的段落索引
        StringBuffer currentTextBuffer = new StringBuffer();

        // 跟踪标题出现的次数
        int titleCount = 0;

        for (int i = 0; i < paragraphDtoList.size(); i++) {
            ParagraphPo.Paragraph paragraph = paragraphDtoList.get(i);
            // 判断当前段落是否是大纲段落
            if (containsOutLinePattern(paragraph, firstLvl, secondLvl)) {
                // 如果当前已有文本且遇到新标题，则保存文本并重置缓冲区
                if (titleCount == 0 && currentTextBuffer.length() > 0) {
                    textSections.add(currentTextBuffer.toString());
                    currentTextBuffer.setLength(0);
                    titleCount++;
                }
                // 但为了保持原逻辑不变，我们保留它，并简化其操作
                if (!currentTextBuffer.isEmpty()) {
                    textSections.add(currentTextBuffer.toString());
                    currentTextBuffer.setLength(0);
                }
                // 添加当前大纲段落的索引到缓冲区
                currentTextBuffer.append(",").append(i);

            } else {
                // 非大纲段落，直接添加到当前章节的文本索引中
                currentTextBuffer.append(",").append(i);
            }
        }

        // 处理最后一部分文本（如果有）
        if (!currentTextBuffer.isEmpty()) {
            textSections.add(currentTextBuffer.toString());
        }

        int paragraphIndex = 1;
        // 按提纲获取内容
        List<List<ParagraphPo.Paragraph>> content =  getTextContent(paragraphDtoList, textSections, paragraphIndex, thirdLvl);
        return getLargeParagraph(content, 0);
    }

    /**
     * 获取文档中最大的两个提纲层级
     *
     * @param paragraphDtoList
     * @return
     */
    private List<Integer> getOutLineLvl(List<ParagraphPo.Paragraph> paragraphDtoList) {
        // 使用HashMap来记录每个lvl出现的第一个Paragraph
        Map<Integer, ParagraphPo.Paragraph> lvlToParagraph = new HashMap<>();

        // 遍历列表，将每个lvl的第一个Paragraph添加到Map中
        for (ParagraphPo.Paragraph p : paragraphDtoList) {
            if ("0".equals(p.getCmptitle())) {
                lvlToParagraph.putIfAbsent(Integer.parseInt(p.getLvl()), p);
            }
        }
        // 将Map的键（lvl）放入列表中，并排序
        List<Integer> lvls = new ArrayList<>(lvlToParagraph.keySet());
        Collections.sort(lvls);
        List<Integer> outLineLvlList = new ArrayList<>();
        if (lvls.size() >= 3) {
//            int minLvl1 = lvls.get(0);
//            int minLvl2 = lvls.get(1);
            outLineLvlList.add(lvls.get(0));
            outLineLvlList.add(lvls.get(1));
            outLineLvlList.add(lvls.get(2));
        } else {
            outLineLvlList.addAll(lvls);

        }

        return outLineLvlList;
    }


    private boolean containsOutLinePattern(ParagraphPo.Paragraph paragraphDto, Integer firstLvl, Integer secondLvl) {

        if (StringUtil.isEmpty(paragraphDto.getLvl())) {
            return false;
        }
        int lvl = Integer.parseInt(paragraphDto.getLvl());
        if (firstLvl == 0) {
            return lvl == firstLvl || lvl == secondLvl;
        } else {
            return lvl == firstLvl && firstLvl != 9999 && secondLvl != 9999;
        }
    }


    /**
     * 获取章节中的内容
     */
    public List<List<ParagraphPo.Paragraph>> getTextContent(List<ParagraphPo.Paragraph> paragraphDtoList, List<String> textList, int paragraphIndex, int thirdLvl) {
        List<List<ParagraphPo.Paragraph>> content = new ArrayList<>();
        for (String text : textList) {
            String[] chapters = text.split(",");
            StringBuffer indexBuffer = new StringBuffer();
            List<String> lowestList = new ArrayList<>();

            for (String chapterIndexStr : chapters) {
                int index;
                try {
                    index = Integer.parseInt(chapterIndexStr);
                } catch (NumberFormatException e) {
                    continue; // 或抛出异常，取决于具体需求
                }
                if (index >= paragraphDtoList.size()) {
                    continue; // 或抛出异常
                }
                ParagraphPo.Paragraph dto = paragraphDtoList.get(index);
                if (containsOutLinePattern(dto, thirdLvl, thirdLvl)) {
                    if (!indexBuffer.isEmpty()) {
                        lowestList.add(indexBuffer.toString());
                        indexBuffer.setLength(0);
                    }
                    indexBuffer.append(",").append(index);

                } else {
                    indexBuffer.append(",").append(index);
                }
            }
            if (!indexBuffer.isEmpty()) {
                lowestList.add(indexBuffer.toString());
            }
            content.add(mergeLargeParagraphs(lowestList, paragraphDtoList, paragraphIndex));
        }
        return content;


    }


    public List<ParagraphPo.Paragraph> getLargeParagraph(List<List<ParagraphPo.Paragraph>> paragraphDtoList, int index) {
        List<ParagraphPo.Paragraph> mergedTexts = new ArrayList<>();
        StringBuilder currentBuilder = new StringBuilder();
        for (int i = 0; i < paragraphDtoList.size(); i++) {
            List<ParagraphPo.Paragraph> paragraphDtoList1 = paragraphDtoList.get(i);
            StringBuilder eachParagraphLength = new StringBuilder();
            System.out.println(i + ":" + currentBuilder.toString().length());
            int paragraphIndex = 0;
            for (ParagraphPo.Paragraph paragraphDto1 : paragraphDtoList1) {
                if (!eachParagraphLength.toString().isEmpty() && eachParagraphLength.length() + paragraphDto1.getText().length() > 1000) {
                    if (!currentBuilder.toString().isEmpty()) {
                        index++;
                        ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();
                        paragraph.setText(currentBuilder.toString());
                        paragraph.setIndex(index);
                        mergedTexts.add(paragraph);
                        currentBuilder.setLength(0);
                    }
                    index++;
                    ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();
                    paragraph.setText(eachParagraphLength.toString());
                    paragraph.setIndex(index);
                    mergedTexts.add(paragraph);

                    paragraphIndex++;
                    eachParagraphLength.setLength(0);
                }
                eachParagraphLength.append(paragraphDto1.getText());
            }
            if (paragraphIndex > 0) {
                ParagraphPo.Paragraph para = new ParagraphPo.Paragraph();
                index++;
                para.setText(eachParagraphLength.toString());
                para.setIndex(index);
                mergedTexts.add(para);
                eachParagraphLength.setLength(0);
                continue;
            }

            if (!currentBuilder.toString().isEmpty() && currentBuilder.length() + eachParagraphLength.length() > 1000) {
                ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();
                //判断结尾大于1000，但是最后一段小于100字就添加段落中
                if (i == paragraphDtoList.size() - 1 && currentBuilder.toString().length() + eachParagraphLength.toString().length() < 1200) {
                    currentBuilder.append(eachParagraphLength);
                    paragraph.setText(currentBuilder.toString());
                    currentBuilder.setLength(0);
                } else {
                    paragraph.setText(currentBuilder.toString());
                    currentBuilder.setLength(0);
                    currentBuilder.append(eachParagraphLength);
                }
                index++;
                paragraph.setIndex(index);
                mergedTexts.add(paragraph);

            } else {
                currentBuilder.append(eachParagraphLength);
            }
        }
        if (!currentBuilder.toString().isEmpty()) {
            ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();
            index++;
            paragraph.setIndex(index);
            paragraph.setText(currentBuilder.toString());
            mergedTexts.add(paragraph);
            currentBuilder.setLength(0);

        }
        return mergedTexts;
    }


    /**
     * 判断大段内容是否合并
     *
     * @param lowestList
     * @return
     */
    private List<ParagraphPo.Paragraph> mergeLargeParagraphs(List<String> lowestList, List<ParagraphPo.Paragraph> paragraphDtoList, Integer paragraphIndex) {
        List<ParagraphPo.Paragraph> mergedTexts = new ArrayList<>();
        //获取标题
        String titleIndex = lowestList.get(0);
        String[] titleChapters = titleIndex.split(",");
        String title = paragraphDtoList.get(Integer.parseInt(titleChapters[1])).getText();
        StringBuilder textBuffer = new StringBuilder();
        for (String lowest : lowestList) {

            String[] lowestChapters = lowest.split(",");
            for (String chapterIndexStr : lowestChapters) {
                int index;
                try {
                    index = Integer.parseInt(chapterIndexStr);
                } catch (NumberFormatException e) {
                    continue; // 或抛出异常，取决于具体需求
                }
                if (index >= paragraphDtoList.size()) {
                    continue; // 或抛出异常
                }
                ParagraphPo.Paragraph dto = paragraphDtoList.get(index);
                ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();

                if (!textBuffer.isEmpty() && textBuffer.length() + dto.getText().length() > 1000) {
                    paragraphIndex++;
                    paragraph.setIndex(paragraphIndex);
                    //判断结尾大于1000，但是最后一段小于100字就添加段落中
                    if (index == Integer.parseInt(lowestChapters[lowestChapters.length - 1]) && dto.getText().length() <= 100) {
                        textBuffer.append(dto.getText());
                    }
                    paragraph.setText(textBuffer.toString());
                    mergedTexts.add(paragraph);
                    textBuffer.setLength(0);
                    if (!(index == Integer.parseInt(lowestChapters[lowestChapters.length - 1]) && dto.getText().length() <= 100)) {
                        textBuffer.append(title).append("\n").append(dto.getText());
                    }

                } else {
                    textBuffer.append(dto.getText()).append("\n");
                }
            }
            if (!textBuffer.isEmpty()) {
                ParagraphPo.Paragraph paragraph = new ParagraphPo.Paragraph();
                paragraphIndex++;
                paragraph.setIndex(paragraphIndex);
                paragraph.setText(textBuffer.toString());
                mergedTexts.add(paragraph);
                textBuffer.setLength(0);
            }

        }
//        if (!textBuffer.isEmpty()) {
//            ParagraphPo.Paragraph paragraph =  new ParagraphPo.Paragraph();
//            paragraphIndex++;
//            paragraph.setIndex(paragraphIndex);
//            paragraph.setText(textBuffer.toString());
//            mergedTexts.add(paragraph);
//            textBuffer.setLength(0);
//        }
        return mergedTexts;
    }


    private int getFirstLevel(List<Integer> levels) {
        return levels.isEmpty() ? 0 : levels.get(0);
    }

    private int getSecondLevel(List<Integer> levels, int firstLvl) {
        if (levels.size() > 1 && (firstLvl == 0 || levels.get(0) != firstLvl)) {
            return levels.get(firstLvl == 0 ? 1 : 0);
        }
        return firstLvl; // Fallback to first level if not available
    }

    private int getthirdLvlLevel(List<Integer> levels, int firstLvl, int secondLvl) {
        if (levels.size() > 2) {
            if (firstLvl == 0) {
                return levels.get(2);
            } else {
                return levels.get(1);
            }
        }
        return secondLvl;
    }


    private List<ParagraphPo.Paragraph> ruleExtraction(List<ParagraphPo.Paragraph> paragraphDtoList, List<String> rules, String textLvl) {
        List<ParagraphPo.Paragraph> contentList = new ArrayList<>();
        for (int i = 0; i < paragraphDtoList.size(); i++) {
            ParagraphPo.Paragraph paragraph = paragraphDtoList.get(i);
//            if(textLvl.equals(paragraph.getLvl())){
            if (isValidText(paragraph.getText(), rules)) {
                ParagraphPo.Paragraph paragraph1 = new ParagraphPo.Paragraph();
                paragraph1.setText(paragraph.getText());
                paragraph1.setIndex(paragraph.getIndex());
                contentList.add(paragraph1);
            }
//            }
        }
        String content = JSON.toJSONString(contentList);
        System.out.println(content);
        return contentList;
    }

    /**
     * 判断文本是否满足规则
     *
     * @param text 要判断的文本
     * @return 如果文本满足规则，则返回true；否则返回false
     */
    public static boolean isValidText(String text, List<String> rules) {
        boolean status = false;
        for (String rule : rules) {
            // 匹配含有括号的规则
            Pattern patternWithBrackets = Pattern.compile(rule);
            Matcher matcherWithBrackets = patternWithBrackets.matcher(text);
            status = matcherWithBrackets.find();
        }
        System.out.println(text + "\t" + status);
        return status;
    }


    /**
     * 处理段落信息， 若index 一致 pageIndex不一致， 则进行数据合并
     * 并确保 pageIndex 按顺序排列
     */
    public List<ParagraphDto> mergeAndSortParagraph(List<ParagraphPo.Paragraph> paragraphs) {
        Map<Integer, List<ParagraphPo.Paragraph>> resultMap = new HashMap<>(paragraphs.size());

        for (ParagraphPo.Paragraph paragraph : paragraphs) {
            int index = paragraph.getIndex();

            // 将相同 index 的数据放入 Map 中
            resultMap.computeIfAbsent(index, k -> new ArrayList<>()).add(paragraph);
        }

        // 将 Map 中的数据按 pageIndex 排序合并
        List<ParagraphDto> mergedParagraphs = new ArrayList<>();
        resultMap.values().forEach(list -> {
            // 按 pageIndex 排序
            list.sort(Comparator.comparingInt(ParagraphPo.Paragraph::getPageIndex));
            // 合并 text 数据
            String mergedText = list.stream().map(ParagraphPo.Paragraph::getText).reduce((s1, s2) -> s1 + s2).orElse("");

            // 创建新的 ParagraphDto 对象
            ParagraphDto paragraphDto = new ParagraphDto();
            paragraphDto.setText(mergedText);
            paragraphDto.setIndex(list.get(0).getIndex());
            List<ParagraphDto.Location> locationList = new ArrayList<>();
            List<List<Object>> objects = new ArrayList<>();

            // 创建 Paragraph 列表
            for (ParagraphPo.Paragraph p : list) {
                // 创建新的 Rect 对象
                ParagraphDto.Location.Rect rect = new ParagraphDto.Location.Rect();
                if (!ObjectUtils.isEmpty(rect)) {
                    rect.setLeft(p.getRect().getLeft());
                    rect.setRight(p.getRect().getRight());
                    rect.setTop(p.getRect().getTop());
                    rect.setBottom(p.getRect().getBottom());
                }
                ParagraphDto.Location location = new ParagraphDto.Location();
                if (!ObjectUtils.isEmpty(location)) {
                    location.setPageIndex(p.getPageIndex());
                    location.setRect(rect);
                    locationList.add(location);
                }

            }
            objects.add(Collections.singletonList(locationList));
            // 将 Paragraph 列表添加到 ParagraphDto 中
            paragraphDto.setRectList(objects);
            mergedParagraphs.add(paragraphDto);
        });

        // 按 index 排序
        mergedParagraphs.sort(Comparator.comparingInt(ParagraphDto::getIndex));

        return mergedParagraphs;
    }


    /**
     * 处理段落信息， 若index 一致 pageIndex不一致， 则进行数据合并
     * 并确保 pageIndex 按顺序排列
     */
    public List<ParagraphDto> mergeOutLineAndSortParagraph(List<ParagraphPo.Paragraph> paragraphs) {
        Map<Integer, List<ParagraphPo.Paragraph>> resultMap = new HashMap<>(paragraphs.size());

        for (ParagraphPo.Paragraph paragraph : paragraphs) {
            int index = paragraph.getIndex();

            // 将相同 index 的数据放入 Map 中
            resultMap.computeIfAbsent(index, k -> new ArrayList<>()).add(paragraph);
        }

        // 将 Map 中的数据按 pageIndex 排序合并
        List<ParagraphDto> mergedParagraphs = new ArrayList<>();
        resultMap.values().forEach(list -> {
            // 按 pageIndex 排序
            list.sort(Comparator.comparingInt(ParagraphPo.Paragraph::getPageIndex));
            // 合并 text 数据
            String mergedText = list.stream().map(ParagraphPo.Paragraph::getText).reduce((s1, s2) -> s1 + s2).orElse("");

            // 创建新的 ParagraphDto 对象
            ParagraphDto paragraphDto = new ParagraphDto();
            paragraphDto.setText(mergedText);
            paragraphDto.setIndex(list.get(0).getIndex());
            List<ParagraphDto.Location> locationList = new ArrayList<>();
            List<List<Object>> objects = new ArrayList<>();
            objects.add(Collections.singletonList(locationList));
            // 将 Paragraph 列表添加到 ParagraphDto 中
            paragraphDto.setRectList(objects);
            mergedParagraphs.add(paragraphDto);
        });

        // 按 index 排序
        mergedParagraphs.sort(Comparator.comparingInt(ParagraphDto::getIndex));

        return mergedParagraphs;
    }






    /**
     * 合并段落3
     *
     * @param paragraphDtoList 段落列表
     * @return 合并后的段落列表
     */
    public List<ParagraphVO> extractSummarize(List<ParagraphPo.Paragraph> paragraphDtoList) {
        List<ParagraphPo.Paragraph> preprocess = preprocess(paragraphDtoList);
        return exportExtractSummarize(preprocess);
    }

    public List<ParagraphVO> exportExtractSummarize(List<ParagraphPo.Paragraph> paragraphDtoList) {
        List<List<ParagraphPo.Paragraph>> preprocess = new ArrayList<>();
        List<ParagraphPo.Paragraph> paragraphList = new ArrayList<>();
        for (int i = 0; i < paragraphDtoList.size(); i++) {
            ParagraphPo.Paragraph paragraph = paragraphDtoList.get(i);
            if (paragraph.getText().startsWith("（") && paragraph.getText().endsWith("）")) {
                paragraphList.add(paragraph);
                preprocess.add(paragraphList);
                paragraphList = new ArrayList<>();
            } else {
                paragraphList.add(paragraph);
            }

        }
        return segmentParagraph(preprocess, 0);
    }

    public List<ParagraphVO> segmentParagraph(List<List<ParagraphPo.Paragraph>> paragraphDtoList, int index) {
        List<ParagraphVO> mergedTexts = new ArrayList<>();
        for (int i = 0; i < paragraphDtoList.size(); i++) {
            List<ParagraphPo.Paragraph> paragraphDtoList1 = paragraphDtoList.get(i);
            StringBuilder eachParagraphLength = new StringBuilder();
            StringBuilder indexList = new StringBuilder();
            List<ParagraphPo.Paragraph.Rect> rects = new ArrayList<>();
            boolean flag = false;
            for (ParagraphPo.Paragraph paragraphDto1 : paragraphDtoList1) {
                if(startsWithArrow(paragraphDto1.getText())|| flag){
                    String content =  removeCharacter(paragraphDto1.getText(), '▲');
                    eachParagraphLength.append(content);
                    indexList.append(paragraphDto1.getIndex()).append(",");
                    rects.add(paragraphDto1.getRect());
                    flag = true;
                }
            }
            if (!indexList.isEmpty() && indexList.charAt(indexList.length() - 1) == ',') {
                indexList.setLength(indexList.length() - 1);
            }
            if (!eachParagraphLength.toString().isEmpty()) {
                ParagraphVO paragraph = new ParagraphVO();
                paragraph.setIndexList(indexList.toString());
                paragraph.setParagraphContent(eachParagraphLength.toString());
                paragraph.setRect(rects);
                mergedTexts.add(paragraph);
                eachParagraphLength.setLength(0);
                indexList.setLength(0);
                rects.clear();
            }
        }
        return mergedTexts;
    }


    public static String removeCharacter(String str, char ch) {
        if (str == null) {
            return null;
        }
        return str.replace(String.valueOf(ch), "");
    }

    public static boolean startsWithArrow(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.startsWith("▲");
    }
}