package com.suwell.plss.search.job;

import com.suwell.plss.search.service.RepairDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 各项目通用的定时任务，
 * 写清楚任务注释，以及在什么情况需要开启定时任务和何时关闭定时任务
 */
@Slf4j
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class XxlJobExecute {

    @Resource
    private RepairDataService repairDataService;
    /**
     * 清理es中存在但是数据库中不存在的数据
     */
    @XxlJob("cleanRecordInEsNotInDb")
    public ReturnT<String> cleanRecordInEsNotInDb() {
        repairDataService.cleanRecordInEsNotInDb();
        return ReturnT.SUCCESS;
    }

    /**
     * 缓存聚合元数据值
     */
    @XxlJob("cacheMetadataValueAgg")
    public ReturnT<String> cacheMetadataValueAgg() {
        repairDataService.cacheMetadataValueAgg();
        return ReturnT.SUCCESS;
    }
}
