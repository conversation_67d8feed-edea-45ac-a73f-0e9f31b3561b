package com.suwell.plss.search.nlp.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.LogUtil;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.search.api.constant.RepeatConstant;
import com.suwell.plss.search.config.NLPConfig;
import com.suwell.plss.search.entity.dto.ParagraphPo;
import com.suwell.plss.search.nlp.req.Req;
import com.suwell.plss.search.nlp.req.Req.FilterItem;
import com.suwell.plss.search.standard.dto.response.RepeatResp;
import jakarta.annotation.PreDestroy;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import name.fraser.neil.plaintext.diff_match_patch;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.nd4j.linalg.api.buffer.DataType;
import org.nd4j.linalg.api.ndarray.INDArray;
import org.nd4j.linalg.factory.Nd4j;
import org.nd4j.linalg.ops.transforms.Transforms;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.suwell.plss.search.api.enums.SearchBizError.STOP_WORD_NOT_NULL;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/25 11:09
 */
@Service
@Log4j2
public class RepeatService {
    @jakarta.annotation.Resource
    private RedisService redisService;
    @jakarta.annotation.Resource
    private NLPConfig nlpConfig;
    @jakarta.annotation.Resource
    private ServerBasePathConfig serverBasePathConfig;

    private static final ExecutorService executor = Executors.newFixedThreadPool(15);


    private final String SPLIT_PAGE_FLAG = "<split_page>";
    private static final String[] chinese_number_DIGITS = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] chinese_number_TENS = {"", "十", "二十", "三十", "四十", "五十", "六十", "七十", "八十", "九十"};
    private static final boolean bypass_no_policy = false;
    private static final Integer embedding_batch_size = 64;
    private static final boolean semanticSentenceScoreFilter = false;
    //环境变量  sentence||""
    private static final String repeat_rate_mode = "sentence";
    private static final String DELETED = "2";
    private static final String NO_PERMISSION = "1";
    private static final String HAS_PERMISSION = "0";

    private static final JiebaSegmenter segmenter = new JiebaSegmenter();
    private Set<String> stopWords;

    interface EmbeddingConfig {
        String embedding_model = "/opt/paraphrase-multilingual-mpnet-base-v2";
        String embedding_url = "*************:8819";
        Boolean remote = true;
    }

    interface PolicyConfig {
        String policy_model = "/opt/trained_models_policy";
        String host_port = "*************:8819";
        boolean remote = true;
    }

    interface ESConfig {
        String host = "************";
        int port = 9200;
        String index = "nlp_document";
        String es_username = "elastic";
        String es_password = "qwert1234567";
    }

    public Flux<ServerSentEvent<RepeatResp>> repeatAll(Req req) {
        return Flux.create(sink ->
                executor.submit(() -> {
                    try {
                        repeatAll(req, sink);
                    } catch (Exception e) {
                        sink.error(e);
                    }
                })
        );
    }

    @PreDestroy
    public void cleanup() {
        executor.shutdown();
    }

    public long computeTime(List<ParagraphPo.Paragraph> doc){
        List<MergeBlock> contents = merge_passages(doc);
        int totalPassgage = CollectionUtils.isNotEmpty(contents) ? contents.get(contents.size() - 1).blocks.get(0).getPassageIndex() : 0;
        return totalPassgage * 5L;
    }

    public Map<String, Object> searchByDoc(Req req) {
        long startTime = System.currentTimeMillis();
        List<ParagraphPo.Paragraph> doc = req.getDoc() != null ? req.getDoc() : new ArrayList<>();
        int page = req.getPage() != null ? req.getPage() : 1;
        int pageSize = req.getPageSize() != null ? req.getPageSize() : 10000;
        List<String> repoIds = req.getRepoIds();
        List<String> deptIds = req.getDeptIds();
        String year = req.getYear();
        String sort = req.getSort() != null ? req.getSort() : "score";
        String keyword = req.getKeyword() != null ? req.getKeyword() : "";
        JSONObject permissionDsl = req.getPermissionDsl();
        boolean filtered = req.getFilter() != null ? req.getFilter() : false;
        Map<String, String> fieldMapping = req.getFieldMapping() != null ? req.getFieldMapping() : new HashMap<>();
        String filename = req.getFilename() != null ? req.getFilename() : "";
        String fileId = req.getFileId() != null ? req.getFileId() : "";
        String indexName = req.getIndexName();
        Map<String, Object> stringObjectMap = searchByDigest(doc, page, pageSize, fileId, sort, keyword, 0, repoIds, deptIds, year, filename, null, permissionDsl, filtered, fieldMapping, indexName);
        log.info("searchByDigest result: {}", stringObjectMap);
        log.info("searchByDigest time: {}", System.currentTimeMillis() - startTime);
        return stringObjectMap;
    }

    public List<JSONObject> getRepeatAllOne(String fileId, String docId, JSONArray repeatData,
                                            List<String> ignoreData, List<JSONObject> policyPassages,
                                            JSONObject fieldMapping, Float minRepeatRate) {
        if (StringUtils.isNotBlank(fileId)) {
            String data = redisService.get("repeat_all:" + fileId, String.class);
            if (data == null) {
                return Collections.singletonList(JSONUtil.createObj().putOpt("error", "No data found for fileId: " + fileId));
            }
            JSONObject jsonData = JSONUtil.parseObj(data);
            repeatData = JSONUtil.parseArray(jsonData.getStr("repeat_data"));
            fieldMapping = JSONUtil.parseObj(jsonData.getStr("fieldMapping"));
            redisService.expire("repeat_all:" + fileId, 3600);
        } else {
            if (repeatData != null) {
                List<JSONObject> parsedData = new ArrayList<>();
                for (Object item : repeatData) {
                    JSONObject jsonItem = JSONUtil.parseObj(item);
                    JSONObject delta = jsonItem.getJSONObject("delta");
                    if (delta != null) {
                        String content = delta.getStr("content");
                        if (content != null) {
                            parsedData.add(JSONUtil.parseObj(content));
                        }
                    }
                }
                repeatData = JSONUtil.parseArray(parsedData);
            }
            if (policyPassages == null || policyPassages.isEmpty()) {
                policyPassages = null;
            }
        }
        List<Sentence> repeatDataList = JSONUtil.toList(repeatData, Sentence.class);
        Map<String, String> fieldMappingMap = JSONUtil.toBean(fieldMapping, Map.class);
        return getRepeatDocsFromSentenceAndPassage(repeatDataList, fieldMappingMap, minRepeatRate, Long.parseLong(docId)
                , policyPassages, null, null);
    }


    private void repeatAll(Req req, FluxSink<ServerSentEvent<RepeatResp>> sink) {
        log.info("repeat_all request:{}", JSONUtil.toJsonStr(req));
        int top_k = 60;
        float threshold = 0.2F;
        float semantic_sentence_threshold = 0.85F;
        boolean use_f1 = true;
        boolean semanticSentence = req.getSemanticSentence() != null ? req.getSemanticSentence() : true;

        Map<String, String> fieldMapping = req.getFieldMapping();
        String content_field = fieldMapping.getOrDefault("content", "content");
        JSONObject permissionDsl = req.getPermissionDsl();
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        List<ParagraphPo.Paragraph> doc = req.getDoc();

        // 检查filterItems中的checkFileInfo
        boolean hasItems = req.getFilterItems() != null && req.getFilterItems().getCheckFileInfo() != null;
        if (!hasItems) log.info("filterItems中的checkFileInfo为空，跳过处理");
        if (req.getFilterItems() != null && req.getFilterItems().getCheckFileInfo() != null) {
            List<Req.CheckFileInfo> checkFileInfoList = req.getFilterItems().getCheckFileInfo();
            List<Req.CheckFileInfo> updatedCheckFileInfo = new ArrayList<>();

            for (Req.CheckFileInfo info : checkFileInfoList) {
                String recordId = info.getRecordId();
                String docStatus = info.getDocStatus();

                if (DELETED.equals(docStatus)) {
                    updatedCheckFileInfo.add(info);
                } else {
                    // 获取文档信息
                    Map<String, Object> docInfo = getDocInfo(recordId, fieldMapping, req.getIndexName());
                    if (docInfo == null || docInfo.isEmpty()) {
                        info.setDocStatus(DELETED);
                    } else {
                        // 将文档信息添加到checkFileInfo中
                        for (Map.Entry<String, Object> entry : docInfo.entrySet()) {
                            info.addProperty(entry.getKey(), entry.getValue());
                        }
                    }
                    updatedCheckFileInfo.add(info);
                }
            }

            req.getFilterItems().setCheckFileInfo(updatedCheckFileInfo);
        }

        List<MergeBlock> contents = merge_passages(doc);
        int totalPassgage = CollectionUtils.isNotEmpty(contents) ? contents.get(contents.size() - 1).blocks.get(0).getPassageIndex() : 0;

        int total_para = 0;
        int total_length = 0;
        int total_repeat_length = 0;
        int n_sentences = 0;
        int n_repeat_sentences = 0;
        List<Sentence> result = new ArrayList<>();

        long total_source_paragragh_embedding_time = 0L;
        long total_target_paragraph_embedding_time = 0;
        long total_sentence_bm25_search_time = 0;
        long total_paragragh_bm25_search_time = 0;
        long total_paragragh_embedding_search_time = 0;
        long total_sentence_diff_time = 0;
        long total_paragragh_diff_time = 0;
        long total_target_sentence_words_cut_time = 0;
        long total_target_paragraph_words_cut_time = 0;
        long redis_write_time = 0;

        int total_source_paragragh_embedding_count = 0;
        int total_target_paragraph_embedding_count = 0;
        int total_sentence_bm25_search_count = 0;
        int total_paragragh_bm25_search_count = 0;
        int total_paragragh_embedding_search_count = 0;
        int total_sentence_diff_count = 0;
        int total_paragragh_diff_count = 0;
        int total_target_sentence_words_cut_count = 0;
        int total_target_paragraph_words_cut_count = 0;

        long start_time = System.currentTimeMillis();
        int contens_filter_ind_1 = 0, contens_filter_ind_2 = 0;
        boolean is_filter = false;

        for (int i = 0; i < contents.size(); i++) {
            String text = contents.get(i).text;
            if (text.contains("指导思想")) is_filter = true;
            if (is_filter) {
                if (StringUtils.isNotEmpty(is_first(text.trim()))) {
                    contens_filter_ind_1 = i;
                    is_filter = false;
                }
            }
            if (text.contains("基本原则") && is_filter && StringUtils.isNotEmpty(is_first(text.trim()))) {
                contens_filter_ind_2 = i;
            }
        }
        int contens_filter_ind = Math.max(contens_filter_ind_1, contens_filter_ind_2);

        int predictedTotalTime = totalPassgage * 5;
        RepeatResp repeatResp = new RepeatResp(
                List.of(
                        new RepeatResp.Choice(
                                0, new RepeatResp.DeltaMessage("assistant"), null, predictedTotalTime
                        )
                )
        );
        sink.next(ServerSentEvent.<RepeatResp>builder()
                .data(repeatResp)
                .build());

        int sentence_id = 0;
        log.info("开始处理段落，段落数量: {}", contents.size());
        for (int source_passage_id = 0; source_passage_id < contents.size(); source_passage_id++) {
            log.info("开始处理段落，段落序号: {}", source_passage_id);
            String original_passage_text = contents.get(source_passage_id).text.replace(" ", "");
            String clear_text = clear_text(original_passage_text);
            boolean is_policy;
            if (source_passage_id < contens_filter_ind) {
                is_policy = false;
            } else if (filter_content(clear_text, 15)) {
                is_policy = false;
            } else {
                long policy_start_time = System.currentTimeMillis();
                is_policy = !filter_policy(clear_text);
                log.info("policy_time:{}", System.currentTimeMillis() - policy_start_time);
            }
            if (bypass_no_policy && !is_policy) {
                log.info("跳过非政策段落，段落序号: {}", source_passage_id);
                continue;
            }
            String _cont = filter_order(clear_text);
            total_para += 1;
            long embedding_start_time = System.currentTimeMillis();
            log.info("开始向量化查询：{}", source_passage_id);
            List<Double> qv = get_embedding(List.of(_cont), true).get(0);
            long embedding_time = System.currentTimeMillis() - embedding_start_time;
            log.info("段落序号: {}, 语义向量计算用时: {}ms", source_passage_id, embedding_time);
            total_source_paragragh_embedding_time += embedding_time;
            total_source_paragragh_embedding_count += 1;
            int passage_length = get_length(original_passage_text);
            List<Sentence> sentences;
            try {
                sentences = split_by_sentence(contents.get(source_passage_id), 0, false);
            } catch (Exception e) {
                log.error("分句处理段落失败，段落序号: {}, 错误信息: {}", source_passage_id, e.getMessage());
                throw new RuntimeException("分句处理段落失败，段落序号: " + source_passage_id + ", 错误信息: " + e.getMessage());
            }
            log.info("分句处理段落，段落序号: {}, 分句数量: {}", source_passage_id, sentences.size());
            for (Sentence s_info : sentences) {
                String s = s_info.sentence;
                List<String> query_words = words_cut(s);
                int query_length = get_length(s);
                total_length += query_length;
                if (is_policy) n_sentences += 1;
                Set<String> s_chars = new HashSet<>();
                for (char c : s.toCharArray()) {
                    s_chars.add(c + "");
                }
                long search_start = System.currentTimeMillis();
                List<String> _es_result = new ArrayList<>();
                List<String> bm25_result = mix_bm25(s, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), top_k, null);
                long search_time = System.currentTimeMillis() - search_start;
                total_sentence_bm25_search_time += search_time;
                total_sentence_bm25_search_count += 1;

                search_start = System.currentTimeMillis();
                List<String> phrase_result = mix_phrase_search(s, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), 10, null);
                search_time = System.currentTimeMillis() - search_start;
                total_sentence_bm25_search_time += search_time;
                total_sentence_bm25_search_count += 1;

                search_start = System.currentTimeMillis();
                List<String> total_match_result = mix_total_match(s, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), 10, null);
                search_time = System.currentTimeMillis() - search_start;
                total_sentence_bm25_search_time += search_time;
                total_sentence_bm25_search_count += 1;
                if (semanticSentence) {
                    List<Double> doubles = get_embedding(List.of(filter_order(s)), false).get(0);
                    List<String> semantic_results = mix_embedding_search(doubles, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), 20, 0.85);
                    _es_result.addAll(semantic_results);
                }
                _es_result.addAll(bm25_result);
                _es_result.addAll(phrase_result);
                _es_result.addAll(total_match_result);
                log.info("段落序号: {}, 分句序号: {}, 段落长度: {}, 分句长度: {}, 句子: {}, 检索结果数量: {}", source_passage_id, sentence_id, passage_length, query_length, s, _es_result.size());
                Set<String> es_result_set = new HashSet<>();
                List<Object> es_result = new ArrayList<>();
                _es_result = _es_result.stream().sorted((s1, s2) -> JSONUtil.parseObj(s2).getInt("recordId").compareTo(JSONUtil.parseObj(s1).getInt("recordId"))).collect(Collectors.toList());
                for (String string : _es_result) {
                    JSONObject es_result_obj = JSONUtil.parseObj(string);
                    String docId = es_result_obj.getStr(docId_field);
                    if (es_result_obj.containsKey(passageIds_field)) {
                        String[] content_fields = es_result_obj.getStr(content_field).strip().split("\n");
                        JSONArray passageIds_fields = es_result_obj.getJSONArray(passageIds_field);
                        JSONArray rectangularBlock_fields = es_result_obj.getJSONArray(rectangularBlock_field);
                        int length = Math.min(Math.min(content_fields.length, passageIds_fields.size()), rectangularBlock_fields.size());
                        for (int i = 0; i < length; i++) {
                            String pid = passageIds_fields.getStr(i);
                            String c = content_fields[i];
                            Object rect = rectangularBlock_fields.get(i);
                            String h = docId + c;
                            if (!es_result_set.contains(h)) {
                                JSONObject _r = BeanUtil.copyProperties(es_result_obj, JSONObject.class);
                                _r.remove(passageIds_field);
                                _r.putOpt(splitId_field, pid).putOpt(rectangularBlock_field, rect).putOpt(content_field, c.replaceAll(" ", ""));
                                es_result.add(_r);
                                es_result_set.add(h);
                            }
                        }
                    } else {
                        String pid = es_result_obj.getStr(splitId_field);
                        String c = es_result_obj.getStr(content_field).strip();
                        String h = docId + c;
                        if (!es_result_set.contains(h)) {
                            JSONObject _r = BeanUtil.copyProperties(es_result_obj, JSONObject.class);
                            _r.putOpt(splitId_field, pid);  // 新增：设置splitId
                            _r.putOpt(content_field, c.replaceAll(" ", ""));  // 新增：移除空格
                            es_result_set.add(h);
                            es_result.add(_r);
                        }
                    }
                }
                List<JSONObject> repeat_sentences = new ArrayList<>();
                List<JSONObject> candidates = new ArrayList<>();
                // 创建一个集合用于去重
                Set<String> s_set = new HashSet<>();
                // 用于记录当前句子的索引
                int i = 0;
                List<String> ss = new ArrayList<>();
                for (Object o : es_result) {
                    JSONObject rr = JSONUtil.parseObj(o);
                    String contentText = rr.getStr(content_field).strip();
                    ss.addAll(split_by_sentence(contentText, 0, false));
                }
                double[] sematic_score = new double[ss.size()];
                if (semanticSentence && !ss.isEmpty()) {
                    if (semanticSentenceScoreFilter) {
                        List<List<Double>> docs_embedding_double = get_embedding(ss, false);
                        List<Double> _sv = get_embedding(List.of(filter_order(s)), false).get(0);
                        Double[] qv_d_1 = Convert.toDoubleArray(_sv);
                        double[][] qv_d_2 = {Arrays.stream(qv_d_1).mapToDouble(Double::doubleValue).toArray()};
                        try (INDArray indArray = Nd4j.create(qv_d_2);
                             INDArray query_embedding = normalizeVector(indArray);
                             INDArray docs_embedding_nparray = Nd4j.create(docs_embedding_double.stream().map(d -> d.stream().mapToDouble(Double::doubleValue).toArray()).toArray(double[][]::new));
                             INDArray docs_embedding_nparray_normal = normalizeVector(docs_embedding_nparray);
                             INDArray docs_embedding_nparray_normal_transpose = docs_embedding_nparray_normal.transpose();
                             INDArray mmul = query_embedding.mmul(docs_embedding_nparray_normal_transpose);
                             INDArray mmul_0 = mmul.getRow(0)) {
                            sematic_score = mmul_0.toDoubleVector();
                        }
                    } else {
                        Arrays.fill(sematic_score, 1.0);
                    }
                }
                for (Object o : es_result) {
                    JSONObject rr = JSONUtil.parseObj(o);
                    String contentText = rr.getStr(content_field).strip();
                    List<String> content = split_by_sentence(contentText, 0, false);
                    for (String sentence : content) {
                        int start_idx = contentText.indexOf(sentence);
                        int end_idx = start_idx + sentence.length();

                        // 计算字符集合相似度
                        Set<String> target_chars = new HashSet<>();
                        for (char c : sentence.toCharArray()) {
                            target_chars.add(String.valueOf(c));
                        }
                        Set<String> temp_set = new HashSet<>(s_chars);
                        temp_set.retainAll(target_chars);
                        double char_set_repeat_rate = (double) temp_set.size() / s_chars.size();

                        // 检查是否为重复句子
                        if (s_set.contains(sentence)) {
                            i++;
                            continue;
                        }
                        s_set.add(sentence);

                        // 使用字符相似度作为条件，移除语义相似度判断(因为没有对应的sematic_score)
                        if (char_set_repeat_rate > 0.3 || (semanticSentence && sematic_score[i] > semantic_sentence_threshold)) {
                            JSONObject record = BeanUtil.copyProperties(rr, JSONObject.class);
                            record.remove(content_field);
                            record.putOpt("text", sentence);
                            if (semanticSentence) record.putOpt("semanticScore", sematic_score[i]);
                            // 处理时间戳
                            Integer timestamp = rr.getInt(printedTime_field, 0);
                            if (timestamp != 0) {
                                timestamp = timestamp / 1000;
                                ZoneId beijingTimezone = ZoneId.of("Asia/Shanghai");
                                ZonedDateTime beijingTime = Instant.ofEpochSecond(timestamp).atZone(beijingTimezone);
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                String formattedBeijingTime = beijingTime.format(formatter);
                                record.putOpt("日期", formattedBeijingTime);
                            }
                            // 处理矩形区域
                            JSONArray rects = rr.getJSONArray(rectangularBlock_field);
                            String passageIndex = rr.getStr(splitId_field);
                            JSONArray sentenceBlocks = new JSONArray();

                            if (rects != null) {
                                for (int j = 0; j < rects.size(); j++) {
                                    JSONObject rect = rects.getJSONObject(j);
                                    JSONObject _rect = rect.getJSONObject("rect");
                                    Double[] bound = new Double[]{
                                            _rect.getDouble("left"),
                                            _rect.getDouble("top"),
                                            _rect.getDouble("right") - _rect.getDouble("left"),
                                            _rect.getDouble("bottom") - _rect.getDouble("top")
                                    };
                                    Integer pageIndex = rect.getInt("pageIndex");

                                    JSONObject sentenceBlock = new JSONObject();
                                    sentenceBlock.putOpt("passageIndex", passageIndex);
                                    sentenceBlock.putOpt("index", pageIndex);
                                    sentenceBlock.putOpt("bound", bound);
                                    sentenceBlock.putOpt("rect", _rect);
                                    sentenceBlock.putOpt("text", sentence);

                                    sentenceBlocks.add(sentenceBlock);
                                }
                                // 添加高亮范围
                                if (!sentenceBlocks.isEmpty()) {
                                    JSONObject firstBlock = sentenceBlocks.getJSONObject(0);
                                    firstBlock.putOpt("highlightRange", new int[]{start_idx, end_idx});
                                }
                            }
                            record.putOpt("sentenceBlocks", sentenceBlocks);
                            candidates.add(record);
                        }
                        i++;
                    }
                }
                for (JSONObject candidate : candidates) {
                    long diff_start = System.currentTimeMillis();
                    Repeat repeat = get_diff_3(s, candidate.getStr("text"), query_words);
                    long diff_time = System.currentTimeMillis() - diff_start;
                    total_sentence_diff_time += diff_time;
                    total_sentence_diff_count += 1;
                    candidate.putOpt("targetHighlight", repeat.target_highlight);
                    candidate.putOpt("repeatLength", repeat.n_repeat);
                    candidate.putOpt("queryHighlight", repeat.query_highlight);
                    // 使用F1计算重复率
                    if (use_f1) {
                        int query_length_local = get_length(candidate.getStr("text"));
                        double recall_score = query_length_local > 0 ? (double) repeat.n_repeat / query_length_local : 0.0;
                        double precision_score = query_length > 0 ? (double) repeat.n_repeat / query_length : 0.0;
                        double repeat_rate = (recall_score + precision_score) > 0 ? (2.0 * recall_score * precision_score) / (recall_score + precision_score) : 0.0;
                        candidate.putOpt("repeatRate", repeat_rate);
                        candidate.putOpt("_repeatRate", repeat.repeat_rate);
                    } else {
                        candidate.putOpt("repeatRate", repeat.repeat_rate);
                    }
                }
                candidates.stream().sorted((s1, s2) -> s2.getDouble("repeatRate").compareTo(s1.getDouble("repeatRate"))).forEach(q -> {
                    if (q.getDouble("repeatRate") > 0.2 || (semanticSentenceScoreFilter && q.getDouble("semanticScore") > semantic_sentence_threshold)) {
                        q.putOpt("targetHtml", render(q.getStr("text"), q.get("targetHighlight", List.class)));
                        repeat_sentences.add(q);
                    }
                });
                if (CollectionUtils.isNotEmpty(repeat_sentences) && is_policy) {
                    total_repeat_length += repeat_sentences.stream().max(Comparator.comparingInt(re -> re.getInt("repeatLength"))).get().getInt("repeatLength");
                    n_repeat_sentences += 1;
                }
                s_info.setSentenceIndex(sentence_id);
                s_info.setTotalPassgage(totalPassgage);
                s_info.setSentenceLength(query_length);
                s_info.setRepeatSentences(repeat_sentences);
                s_info.setPassageIndex(source_passage_id);
                s_info.setRepeatType("sentence");
                s_info.setIsPolicy(is_policy);
                result.add(s_info);
                sentence_id += 1;
                RepeatResp repeatResp1 = new RepeatResp(
                        List.of(new RepeatResp.Choice(
                                        0, new RepeatResp.DeltaMessage(null, JSONUtil.toJsonStr(s_info)), null
                                )
                        )
                );
                sink.next(ServerSentEvent.<RepeatResp>builder()
                        .data(repeatResp1)
                        .build());
            }
            long search_start = System.currentTimeMillis();
            List<String> bm25_result = mix_bm25(_cont, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), 10, null);
            long search_time = System.currentTimeMillis() - search_start;
            total_paragragh_bm25_search_time += search_time;
            total_paragragh_bm25_search_count += 1;
            log.info("分词检索段落用时: {}ms", search_time);
            search_start = System.currentTimeMillis();
            List<String> semantic_result = mix_embedding_search(qv, fieldMapping, permissionDsl, req.getIndexName(), req.getFilterItems(), req.getFilter(), top_k, 0.85);
            search_time = System.currentTimeMillis() - search_start;
            total_paragragh_embedding_search_time += search_time;
            total_paragragh_embedding_search_count += 1;
            log.info("语义检索段落用时: {}ms", search_time);
            List<String> passage_es_result = new ArrayList<>(bm25_result);
            passage_es_result.addAll(semantic_result);
            long split_time = System.currentTimeMillis();
            List<Map.Entry<JSONObject, Float>> split_passages = get_split_passages(passage_es_result, qv, fieldMapping, req.getIndexName(), 0, null);
            log.info("split_passages: {}ms", System.currentTimeMillis() - split_time);
            Set<String> passage_chars = new HashSet<>();
            for (char c : original_passage_text.toCharArray()) {
                passage_chars.add(c + "");
            }
            List<String> passage_words = words_cut(original_passage_text);
            List<JSONObject> repeat_passages = new ArrayList<>();
            for (Map.Entry<JSONObject, Float> split_passage : split_passages) {
                JSONObject es_result = split_passage.getKey();
                String t = es_result.getStr(content_field);
                Set<String> t_chars = new HashSet<>();
                for (char c : t.toCharArray()) {
                    t_chars.add(c + "");
                }
                Set<String> temp_set = new HashSet<>(passage_chars);
                temp_set.retainAll(t_chars);
                float char_set_repeat_rate = (float) temp_set.size() / passage_chars.size();
                if (char_set_repeat_rate > 0.3) {
                    long cut_start = System.currentTimeMillis();
                    long cut_time = System.currentTimeMillis() - cut_start;
                    total_target_paragraph_words_cut_time += cut_time;
                    total_target_paragraph_words_cut_count += 1;
                    long diff_start = System.currentTimeMillis();
                    Repeat repeat = get_diff_3(original_passage_text, t, passage_words);
                    long diff_time = System.currentTimeMillis() - diff_start;
                    total_paragragh_diff_time += diff_time;
                    total_paragragh_diff_count += 1;
                    if (repeat.repeat_rate > 0.2) {
                        JSONObject record = BeanUtil.copyProperties(es_result, JSONObject.class);
                        record.remove(content_field);
                        record.putOpt("text", t);
                        int timestamp = es_result.getInt(printedTime_field, 0);
                        if (timestamp != 0) {
                            timestamp = timestamp / 1000;
                            ZoneId beijingTimezone = ZoneId.of("Asia/Shanghai");
                            ZonedDateTime beijingTime = Instant.ofEpochSecond(timestamp).atZone(beijingTimezone);
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            String formattedBeijingTime = beijingTime.format(formatter);
                            record.putOpt("日期", formattedBeijingTime);
                        }
                        record.putOpt("targetHighlight", repeat.target_highlight)
                                .putOpt("repeatLength", repeat.n_repeat)
                                .putOpt("repeatRate", repeat.repeat_rate)
                                .putOpt("queryHighlight", repeat.query_highlight).putOpt("targetHtml", render(record.getStr("text"), repeat.target_highlight));
                        repeat_passages.add(record);
                    }
                }
            }
            log.info("段落语义过滤前:{}", repeat_passages.size());
            List<JSONObject> repeat_passages_sorted = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(repeat_passages)) {
                Double[] qv_d_1 = Convert.toDoubleArray(qv);
                double[][] qv_d_2 = {Arrays.stream(qv_d_1).mapToDouble(Double::doubleValue).toArray()};
                try (INDArray queryEmbedding = Nd4j.create(qv_d_2);
                     INDArray queryEmbedding_norm = normalizeVector(queryEmbedding)) {
                    long embedding_start = System.currentTimeMillis();
                    List<List<Double>> docs_embedding_list = get_embedding(repeat_passages.stream().map(repeat -> repeat.getStr("text")).toList(), false);
                    embedding_time = System.currentTimeMillis() - embedding_start;
                    total_target_paragraph_embedding_time += embedding_time;
                    total_target_paragraph_embedding_count += 1;
                    double[][] docs_embedding_array = docs_embedding_list.stream()
                            .map(list -> list.stream()
                                    .mapToDouble(Double::doubleValue)
                                    .toArray())
                            .toArray(double[][]::new);
                    long compute_scores_time = System.currentTimeMillis();
                    try (INDArray docs_embedding_ = Nd4j.create(docs_embedding_array);
                         INDArray docs_embedding_norm = normalizeVector(docs_embedding_);
                         INDArray docs_embedding_norm_transpose = docs_embedding_norm.transpose();
                         INDArray scores = queryEmbedding_norm.mmul(docs_embedding_norm_transpose);
                         INDArray scores_0 = scores.getRow(0);
                    ) {

                        float[] floatVector = scores_0.toFloatVector();
                        for (int i = 0; i < repeat_passages.size(); i++) {
                            if (floatVector[i] <= threshold) continue;
                            repeat_passages_sorted.add(repeat_passages.get(i));
                        }
                    }
                    log.info("计算相似度用时: {} ms", System.currentTimeMillis() - compute_scores_time);
                }
            }
            int passage_repeat_length = 0;
            double passage_repeat_rate = 0.0;
            long micro_repeat_rate_time = System.currentTimeMillis();
            log.info("段落语义过滤后:{}", repeat_passages_sorted.size());
            if (CollectionUtils.isNotEmpty(repeat_passages_sorted)) {
                repeat_passages_sorted.sort((a, b) -> Float.compare(b.getFloat("repeatRate"), a.getFloat("repeatRate")));
                double[] microRepeatRate = get_micro_repeat_rate(repeat_passages_sorted, passage_length);
                passage_repeat_length = (int) microRepeatRate[0];
                passage_repeat_rate = microRepeatRate[1];
            }
            log.info("micro_repeat_rate_time:{}", System.currentTimeMillis() - micro_repeat_rate_time);
            Sentence passage_info = new Sentence();
            passage_info.setRepeatType("passage");
            passage_info.setTotalPassgage(totalPassgage);
            passage_info.setPassageIndex(source_passage_id);
            passage_info.setPassageLength(passage_length);
            passage_info.setPassage(original_passage_text);
            passage_info.setIsPolicy(is_policy);
            passage_info.setPassageRepeatLength(passage_repeat_length);
            passage_info.setPassageRepeatRate(passage_repeat_rate);
            passage_info.setPassageBlocks(contents.get(source_passage_id).blocks);
            passage_info.setRepeatPassages(repeat_passages);
            result.add(passage_info);
            log.info("passage_repeat_rate:{}", passage_repeat_rate);
            RepeatResp choice_data = new RepeatResp(
                    List.of(
                            new RepeatResp.Choice(
                                    0, new RepeatResp.DeltaMessage(null, JSONUtil.toJsonStr(passage_info)), null
                            )
                    )
            );
            sink.next(ServerSentEvent.<RepeatResp>builder()

                    .data(choice_data)
                    .build());
        }
        long calculate_repeat_docs_start = System.currentTimeMillis();
        List<JSONObject> repeat_docs = getRepeatDocsFromSentenceAndPassage(result, fieldMapping, 0.2f, null, null, null, req.getFilterItems().getCheckFileInfo());
        long calculate_repeat_docs_time = System.currentTimeMillis() - calculate_repeat_docs_start;
        double total_repeat_rate;
        if ("sentence".equals(repeat_rate_mode)) {
            total_repeat_rate = n_sentences > 0 ? ((double) n_repeat_sentences / n_sentences * 100) : 0;
        } else {
            total_repeat_rate = total_length > 0 ? ((double) total_repeat_length / total_length * 100) : 0;
        }
        log.info("total_repeat_rate: {}", total_repeat_rate);
        // 构建文档重复结果
        JSONObject doc_repeat_result = JSONUtil.createObj()
                .putOpt("repeatType", "document")
                .putOpt("totalPassage", totalPassgage)
                .putOpt("totalRepeatRate", total_repeat_rate)
                .putOpt("repeatDocs", repeat_docs);

        RepeatResp doc_choice_data = new RepeatResp(
                List.of(
                        new RepeatResp.Choice(
                                0, new RepeatResp.DeltaMessage(null, JSONUtil.toJsonStr(doc_repeat_result)), null
                        )
                )
        );

        sink.next(ServerSentEvent.<RepeatResp>builder()

                .data(doc_choice_data)
                .build());
//        if (StringUtils.isNotEmpty(req.getFileId())) {
//            JSONObject data_to_store = JSONUtil.createObj().putOpt("repeat_data", result).putOpt("fieldMapping", fieldMapping);
//            redisService.setEx("repeat_all:" + req.getFileId(), JSONUtil.toJsonStr(data_to_store), 3600, TimeUnit.SECONDS);
//        }
        long total_time = System.currentTimeMillis() - start_time;
        log.info("总源段落向量化用时：{}ms,处理个数:{}", total_source_paragragh_embedding_time, total_source_paragragh_embedding_count);
        log.info("总目标段落向量化用时：{}ms,处理个数:{}", total_target_paragraph_embedding_time, total_target_paragraph_embedding_count);
        log.info("总句子分词检索用时：{}ms,处理个数:{}", total_sentence_bm25_search_time, total_sentence_bm25_search_count);
        log.info("总段落分词检索用时：{}ms,处理个数:{}", total_paragragh_bm25_search_time, total_paragragh_bm25_search_count);
        log.info("总段落语义检索用时：{}ms,处理个数:{}", total_paragragh_embedding_search_time, total_paragragh_embedding_search_count);
        log.info("总句子diff计算用时：{}ms,处理个数:{}", total_sentence_diff_time, total_sentence_diff_count);
        log.info("总段落diff计算用时：{}ms,处理个数:{}", total_paragragh_diff_time, total_paragragh_diff_count);
        log.info("总目标句子分词用时：{}ms,处理个数:{}", total_target_sentence_words_cut_time, total_target_sentence_words_cut_count);
        log.info("总目标段落分词用时：{}ms,处理个数:{}", total_target_paragraph_words_cut_time, total_target_paragraph_words_cut_count);
        log.info("重复文档计算用时：{}ms", calculate_repeat_docs_time);
        log.info("总用时：{}ms", total_time);
        // 返回最终结果
        RepeatResp final_choice_data = new RepeatResp(
                List.of(
                        new RepeatResp.Choice(
                                0,
                                new RepeatResp.DeltaMessage(
                                        null,
                                        JSONUtil.toJsonStr(JSONUtil.createObj().putOpt("totalRepeatRate", total_repeat_rate))
                                ),
                                "stop"
                        )
                )
        );

        sink.next(ServerSentEvent.<RepeatResp>builder()
                .data(final_choice_data)
                .build());
        sink.complete();
    }

    private INDArray normalizeVector(INDArray vector) {
        try (
                INDArray squared = Transforms.pow(vector, 2);
                INDArray sum = squared.sum(true, -1);
                // 计算L2范数: sqrt(sum(x^2))
                INDArray norm = Transforms.sqrt(sum).reshape(-1, 1);
                // 避免除零
                INDArray _norm = Transforms.max(norm, 1e-8);
        ) {
            // 归一化
            return vector.div(_norm);
        }
    }

    private Map<String, Object> searchByDigest(List<ParagraphPo.Paragraph> doc, int page, int pageSize, String fileId,
                                               String sort, String keyword, int topK, List<String> repoIds, List<String> deptIds, String year,
                                               String filename, FilterItem filterItems, JSONObject permissionDsl, boolean filtered,
                                               Map<String, String> fieldMapping, String indexName) {
        topK = topK == 0 ? 1000 : topK;
        String contentField = fieldMapping.getOrDefault("content", "content");
        String nameField = fieldMapping.getOrDefault("name", "name");
        String titleField = fieldMapping.getOrDefault("title", "title");
        String splitIdField = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTimeField = fieldMapping.getOrDefault("printedTime", "printedTime");
        String digestField = fieldMapping.getOrDefault("digest", "digest");
        String issueOrgField = fieldMapping.getOrDefault("issueOrg", "issueOrg");
        String categoriesField = fieldMapping.getOrDefault("categories", "categories");
        String repoIdField = fieldMapping.getOrDefault("repoId", "repoId");
        String docIdField = fieldMapping.getOrDefault("primaryKeyId", "docId");
        String rectangularBlockField = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String embeddingField = fieldMapping.getOrDefault("embedding", "embedding");
        List<String> timeGroup = fieldMapping.containsKey("timeGroup") ? JSONUtil.parseArray(fieldMapping.get("timeGroup")).toList(String.class) : Collections.singletonList(printedTimeField);

        List<String> passages = merge_passages_1(doc);

        if (StringUtils.isNotEmpty(keyword) && "score".equals(sort)) {
            sort = "keyword_score";
        }

        if (StringUtils.isEmpty(fileId)) {
            fileId = DigestUtils.md5Hex(passages.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining("\n")));
        }

        JSONObject redisResult = redisService.get(fileId, JSONObject.class);
        redisService.expire(fileId, 3600);

        Map<String, Object> data;
        if (!redisResult.isEmpty()) {
            data = redisResult;
        } else {
            data = new HashMap<>();
            data.put("contents", passages);
        }

        data.getOrDefault("contents", new ArrayList<>());
        List<String> contents = (List<String>) data.getOrDefault("contents", new ArrayList<>());
        List<Integer> searchResult = (List<Integer>) data.getOrDefault("searchResult", new ArrayList<>());
        Map<Integer, Map<String, Object>> docDict = (Map<Integer, Map<String, Object>>) data.getOrDefault("docDict", new HashMap<>());

        String previousKeyword = (String) data.getOrDefault("keyword", "");
        String previousSort = (String) data.getOrDefault("sort", "score");
        filename = StringUtils.defaultIfEmpty((String) data.get("filename"), filename);
        String name = filename.replace(".ofd", "").replace(".pdf", "").replace(".doc", "").replace(".docx", "");

        data.put("filename", filename);
        contents.addAll(Arrays.asList(name, name));

        if (MapUtils.isEmpty(docDict)) {
            int passageTopK = 3;
            // Note: Implementation of getSearchResultEs and other helper methods would be needed
            List<Object> result = getSearchResultEs(contents, permissionDsl, filterItems, filtered, fieldMapping, indexName, passageTopK, "embedding");
            JSONArray es_result = JSONUtil.parseArray(result.get(0));
            Object query_index = result.get(1);
            List<String> query_contents = Convert.toList(String.class, result.get(2));
            List<List<Double>> embedding = (List<List<Double>>) result.get(3);

            Set<Integer> docSet = new HashSet<>();
            for (Object o : es_result) {
                JSONArray row = JSONUtil.parseArray(o);
                for (Object r : row) {
                    JSONObject docObj = JSONUtil.parseObj(r);
                    docSet.add(Convert.toInt(docObj.getByPath("_source." + docIdField)));
                }
            }

            // 将docSet转换为List<Integer>用于后续处理
            List<Integer> doc_list = new ArrayList<>(docSet);

            // This is a simplified version showing the structure
            Map<Integer, Double> docDictScores = getDocScores(query_contents, embedding, doc_list, fieldMapping, indexName);

            List<Integer> searchedDocids = docDictScores.entrySet().stream()
                    .sorted(Map.Entry.<Integer, Double>comparingByValue().reversed())
                    .limit(topK)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            Map<String, JSONObject> docs = getDocs(searchedDocids, fieldMapping, indexName);
            // 将docDict中添加分数信息
            for (Integer docid : searchedDocids) {
                JSONObject docObj = docs.get(docid.toString());
                if (docObj != null) {
                    Map<String, Object> docInfo = new HashMap<>();
                    docInfo.put("score", docDictScores.get(docid));
                    // docInfo.put("yearASC", docObj.containsKey(printedTimeField) ? -Integer.parseInt(docObj.getStr(printedTimeField).substring(0, 4)) : 2022);
                    // docInfo.put("yearDESC", docObj.containsKey(printedTimeField) ? Integer.parseInt(docObj.getStr(printedTimeField).substring(0, 4)) : 2022);
                    docDict.put(docid, docInfo);
                }
            }
            // 确保docDict中的键都在docDictScores中
            docDict.keySet().retainAll(docDictScores.keySet());
            searchResult = new ArrayList<>(docDict.keySet());
            searchResult.sort((a, b) -> Double.compare((Double) docDict.get(b).getOrDefault(previousSort, 0.0), (Double) docDict.get(a).getOrDefault(previousSort, 0.0)));
        }

        if (!previousKeyword.equals(keyword)) {
            Map<Integer, Integer> keywordResult = searchByKeyword(keyword, new ArrayList<>(docDict.keySet()), fieldMapping, indexName);
            for (Map.Entry<Integer, Integer> entry : keywordResult.entrySet()) {
                docDict.get(entry.getKey()).put("keyword_score", entry.getValue());
            }
            searchResult = new ArrayList<>(keywordResult.keySet());
        }

        if (!previousSort.equals(sort) || !previousKeyword.equals(keyword)) {
            String finalSort = sort;
            searchResult.sort((a, b) -> {
                Object valueA = docDict.get(a).getOrDefault(finalSort, 0);
                Object valueB = docDict.get(b).getOrDefault(finalSort, 0);
                if (valueA instanceof Double && valueB instanceof Double) {
                    return ((Double) valueB).compareTo((Double) valueA);
                } else if (valueA instanceof Integer && valueB instanceof Integer) {
                    return ((Integer) valueB).compareTo((Integer) valueA);
                }
                return 0;
            });
        }

        List<Integer> filteredResult = new ArrayList<>();
        Map<String, Integer> allRepoIds = new HashMap<>();

        int[] timeRangeInt = null;
        String[] timeRangeStr = null;

        if (filterItems != null && filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2) {
            timeRangeInt = new int[]{Convert.toInt(filterItems.getPrintYearRange().get(0)), Convert.toInt(filterItems.getPrintYearRange().get(1))};
            timeRangeStr = new String[2];
            for (int i = 0; i < 2; i++) {
                long timestamp = timeRangeInt[i] / 1000;
                ZonedDateTime beijingTime = Instant.ofEpochSecond(timestamp)
                        .atZone(ZoneId.of("Asia/Shanghai"));
                timeRangeStr[i] = beijingTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        }

        for (Integer docid : searchResult) {
            Map<String, Object> docMap = docDict.get(docid);

            // 时间范围过滤
            if (filterItems != null && filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2) {
                boolean inTimeRange = false;
                for (String timeField : timeGroup) {
                    if (timeInRange(docMap.get(timeField), timeRangeInt, timeRangeStr)) {
                        inTimeRange = true;
                        break;
                    }
                }
                if (!inTimeRange) continue;
            }

            // 统计仓库ID
            if (docMap.containsKey(repoIdField)) {
                List<String> repoIdList = (List<String>) docMap.get(repoIdField);
                for (String repoId : repoIdList) {
                    allRepoIds.put(repoId, allRepoIds.getOrDefault(repoId, 0) + 1);
                }
            }

            // 分类过滤
            if (filterItems != null && CollectionUtils.isNotEmpty(filterItems.getCategoryIds()) && docMap.containsKey(categoriesField)) {
                Set<String> targetCategories = filterItems.getCategoryIds().stream().map(String::valueOf).collect(Collectors.toSet());
                Set<String> sourceCategories = ((List<Object>) docMap.get(categoriesField)).stream().map(String::valueOf).collect(Collectors.toSet());
                if (Collections.disjoint(targetCategories, sourceCategories)) {
                    continue;
                }
            }

            // 仓库ID过滤
            if (filterItems != null && CollectionUtils.isNotEmpty(filterItems.getRepoIds()) && docMap.containsKey(repoIdField)) {
                Set<String> targetRepoIds = filterItems.getRepoIds().stream().map(String::valueOf).collect(Collectors.toSet());
                Set<String> sourceRepoIds = ((List<Object>) docMap.get(repoIdField)).stream().map(String::valueOf).collect(Collectors.toSet());
                if (Collections.disjoint(targetRepoIds, sourceRepoIds)) {
                    continue;
                }
            }

            // 年份过滤
            if (year != null && !year.equals(String.valueOf(docMap.get(printedTimeField)))) {
                continue;
            }

            filteredResult.add(docid);
        }

        List<JSONObject> allRepoIdsResult = allRepoIds.entrySet().stream()
                .map(entry -> JSONUtil.createObj().putOpt("repoId", entry.getKey()).putOpt("count", entry.getValue()))
                .collect(Collectors.toList());

        int totalCount = (int) Math.ceil((double) filteredResult.size() / pageSize);
        if (page > totalCount) {
            page = totalCount;
        }

        List<Integer> pagedResult = filteredResult.subList(
                Math.max(0, (page - 1) * pageSize),
                Math.min(filteredResult.size(), page * pageSize)
        );

        data.put("searchResult", searchResult);
        data.put("docDict", docDict);
        data.put("keyword", keyword);
        data.put("sort", sort);

        redisService.setEx("search", fileId, JSONUtil.toJsonStr(data), 3600, TimeUnit.SECONDS);

        JSONObject result = JSONUtil.createObj()
                .putOpt("searchResult", pagedResult)
                .putOpt("totalCount", totalCount)
                .putOpt("fileId", fileId)
                .putOpt("totalDocCount", filteredResult.size())
                .putOpt("repoIds", allRepoIdsResult);

        return result;

    }

    private Map<Integer, Integer> searchByKeyword(String keyword, List<Integer> docids, Map<String, String> fieldMapping, String indexName) {
        String content_field = fieldMapping.getOrDefault("content", "content");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "docId");

        if (StringUtils.isEmpty(keyword)) {
            Map<Integer, Integer> result = new HashMap<>();
            for (Integer docid : docids) {
                result.put(docid, 100);
            }
            return result;
        }

        JSONObject body = JSONUtil.createObj();
        body.putByPath("query.bool.must.multi_match.query", keyword);
        body.putByPath("query.bool.must.multi_match.fields", Arrays.asList(content_field, title_field));
        body.putByPath("query.bool.filter.terms." + docId_field, docids);
        body.putOpt("size", docids.size() * 3);

        String esUrl = String.format("http://%s/%s/_search", nlpConfig.getEsUrl().get(0), indexName);
        String response = HttpUtil.post(esUrl, body.toString());
        JSONObject esTasksResult = JSONUtil.parseObj(response);
        JSONArray hits = esTasksResult.getByPath("hits.hits", JSONArray.class);

        Map<Integer, Integer> count = new HashMap<>();
        for (int i = 0; i < hits.size(); i++) {
            JSONObject row = hits.getJSONObject(i);
            Integer docId = row.getByPath("_source." + docId_field, Integer.class);
            if (!count.containsKey(docId)) {
                count.put(docId, 0);
            }
            count.put(docId, count.get(docId) + 1);
        }

        return count;
    }


    public List<JSONObject> repeat_sentence_report(Req req) {
        String fileId = req.getFileId();
        JSONArray ignoreData = req.getIgnoreData();
        JSONArray policyParagraph = req.getPolicyParagraph();
        JSONArray repeatData = req.getCheckResult();
        Map<String, String> fieldMapping = req.getFieldMapping();
        if (StringUtils.isNotEmpty(fileId)) {
            String data = redisService.get("repeat_all:" + fileId, String.class);
            if (data == null) {
                return new ArrayList<>();
            }
            JSONObject jsonData = JSONUtil.parseObj(data);
            repeatData = jsonData.getJSONArray("repeat_data");
            fieldMapping = jsonData.get("fieldMapping", Map.class);
            redisService.expire("repeat_all:" + fileId, 3600);
        } else {
            repeatData = JSONUtil.parseArray(repeatData.stream().map(s -> JSONUtil.parseObj(s).getByPath("delta.content")).toList());
        }

        String issueNumber_field = fieldMapping.getOrDefault("issueNumber", "发文字号");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");

        Set<String> ignoreDataSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(ignoreData)) {
            for (int i = 0; i < ignoreData.size(); i++) {
                JSONObject item = ignoreData.getJSONObject(i);
                String key = item.getStr("sentenceIndex") + "," +
                        item.getStr("recordId") + "," +
                        item.getStr("targetText");
                ignoreDataSet.add(key);
            }
        }
        Map<Integer, Boolean> policyPassages = new HashMap<>();
        if (CollectionUtils.isNotEmpty(policyParagraph)) {
            for (int i = 0; i < policyParagraph.size(); i++) {
                JSONObject p = policyParagraph.getJSONObject(i);
                policyPassages.put(p.getInt("passageIndex"), p.getBool("isPolicy"));
            }
        }
        int total_source_length = 0;
        int total_repeat_length = 0;
        int n_sentences = 0;
        int n_repeat_sentences = 0;
        List<JSONObject> result = new ArrayList<>();
        List<Sentence> sentenceList = repeatData.stream().map(s -> JSONUtil.toBean(JSONUtil.parseObj(s), Sentence.class)).toList();
        for (Sentence sentenceInfo : sentenceList) {
            if (!"sentence".equals(sentenceInfo.getRepeatType())) {
                continue;
            }
            Integer passageId = sentenceInfo.getPassageIndex();
            if (!policyPassages.getOrDefault(passageId, sentenceInfo.getIsPolicy() != null ? sentenceInfo.getIsPolicy() : true)) {
                continue;
            }
            int sentenceLength = sentenceInfo.getSentenceLength();
            total_source_length += sentenceLength;
            n_sentences += 1;
            int sentenceIndex = sentenceInfo.getSentenceIndex();

            double max_repeat_rate = 0;
            int max_repeat_length = 0;
            JSONObject max_sentence_info = null;

            if (CollectionUtils.isNotEmpty(sentenceInfo.getRepeatSentences())) {
                for (JSONObject r : sentenceInfo.getRepeatSentences()) {
                    String recordId = r.getStr(docId_field);
                    String target_text = r.getStr("text");
                    double repeatRate = r.getDouble("repeatRate");
                    int repeatLength = r.getInt("repeatLength");
                    String ignoreKey = sentenceIndex + "," + recordId + "," + target_text;
                    if (!ignoreDataSet.contains(ignoreKey)) {
                        if (repeatRate > max_repeat_rate) {
                            max_repeat_rate = repeatRate;
                            max_repeat_length = repeatLength;
                            max_sentence_info = r;
                        }
                    }
                }
            }
            sentenceInfo.setMaxRepeatRate(max_repeat_rate);
            if (max_repeat_rate > 0) {
                n_repeat_sentences += 1;
            }

            if (max_sentence_info != null) {
                String issueNumber = max_sentence_info.getStr(issueNumber_field, "");
                String title = max_sentence_info.getStr(title_field, "");
                List<Rbound> sentenceBlocks = sentenceInfo.getSentenceBlocks();
                if (CollectionUtils.isNotEmpty(sentenceBlocks)) {
                    for (int j = 0; j < sentenceBlocks.size(); j++) {
                        Rbound block = sentenceBlocks.get(j);
                        int[] highlightRange = block.getHighlightRange();
                        int a = highlightRange[0];
                        int b = highlightRange[1];
                        JSONObject highlight_info = JSONUtil.createObj()
                                .putOpt("index", block.getPassageIndex())
                                .putOpt("pageIndex", block.getIndex())
                                .putOpt("rect", block.getRect())
                                .putOpt("text", block.getText())
                                .putOpt("title", title);

                        List<Integer> textIndexList = new ArrayList<>();
                        for (int idx = a; idx <= b; idx++) {
                            textIndexList.add(idx);
                        }
                        highlight_info.putOpt("textIndex", textIndexList);

                        if (j == 0) {
                            highlight_info.putOpt("maxRepeatRate", max_repeat_rate)
                                    .putOpt("issueNumber", issueNumber)
                                    .putOpt("title", title);
                        }

                        result.add(highlight_info);
                    }
                }
            }

            total_repeat_length += max_repeat_length;
        }

        double total_repeat_rate;
        if ("sentence".equals(repeat_rate_mode)) {
            total_repeat_rate = n_sentences > 0 ? (double) n_repeat_sentences / n_sentences : 0;
        } else {
            total_repeat_rate = total_source_length > 0 ? (double) total_repeat_length / total_source_length : 0;
        }

        log.info("total_repeat_rate: {}", total_repeat_rate);
        log.info("number of repeat sentence block: {}", result.size());
        return result;
    }

    /**
     * 判断文档时间是否在指定范围内
     *
     * @param docTime  文档时间
     * @param intRange 整数类型的时间范围
     * @param strRange 字符串类型的时间范围
     * @return 是否在范围内
     */
    private boolean timeInRange(Object docTime, int[] intRange, String[] strRange) {
        if (docTime == null) {
            return false;
        }

        if (docTime instanceof String) {
            String timeStr = (String) docTime;
            if (timeStr.length() >= 10) {
                timeStr = timeStr.substring(0, 10);
            }
            return timeStr.compareTo(strRange[0]) >= 0 && timeStr.compareTo(strRange[1]) <= 0;
        } else if (docTime instanceof Integer || docTime instanceof Long) {
            long timeValue = ((Number) docTime).longValue();
            return timeValue >= intRange[0] && timeValue <= intRange[1];
        } else {
            throw new IllegalArgumentException("错误的时间类型");
        }
    }

    private Map<String, JSONObject> getDocs(List<Integer> docids, Map<String, String> fieldMapping, String indexName) {
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId = fieldMapping.getOrDefault("repoId", "repoId");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "docId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        List<String> timeGroup = fieldMapping.containsKey("docId_field") ? Convert.toList(String.class, fieldMapping.get("docId_field")) : Arrays.asList(printedTime_field);
        String esUrl = String.format("http://%s/%s/_search", nlpConfig.getEsUrl().get(0), indexName);
        List<CompletableFuture<JSONObject>> esTasks = new ArrayList<>();

        for (Integer id : docids) {
            List<String> _source = Arrays.asList(docId_field, printedTime_field, repoId, categories_field);
            _source.addAll(timeGroup);
            JSONObject body = JSONUtil.createObj().putOpt("size", 1).putOpt("_source", _source);
            body.putByPath("query.term." + docId_field, id);
            esTasks.add(CompletableFuture.supplyAsync(() -> JSONUtil.parseObj(HttpUtil.post(esUrl, body))));
        }
        List<JSONObject> esTasksResult = esTasks.stream()
                .map(CompletableFuture::join)
                .toList();
        Map<String, JSONObject> doc_dict = new HashMap<>();
        for (JSONObject row : esTasksResult) {
            if (!doc_dict.containsKey(row.getStr(docId_field))) {
                doc_dict.put(row.getStr(docId_field), row);
            }
        }
        return doc_dict;
    }

    private Map<Integer, Double> getDocScores(List<String> queryContents, List<List<Double>> embedding, List<Integer> docids, Map<String, String> fieldMapping, String indexName) {
        try {
            String contentField = fieldMapping.getOrDefault("content", "content");
            String nameField = fieldMapping.getOrDefault("name", "name");
            String docIdField = fieldMapping.getOrDefault("primaryKeyId", "docId");
            String embeddingField = fieldMapping.getOrDefault("embedding", "embedding");
            String printedTimeField = fieldMapping.getOrDefault("printedTime", "printedTime");
            List<String> timeGroup = fieldMapping.containsKey("timeGroup") ? JSONUtil.parseArray(fieldMapping.get("timeGroup")).toList(String.class) : Collections.singletonList(printedTimeField);

            // 计算查询权重
            int[] queryLength = queryContents.stream().mapToInt(String::length).toArray();
            double sumLength = Arrays.stream(queryLength).sum();
            double[] queryWeight = Arrays.stream(queryLength).mapToDouble(l -> l / sumLength).toArray();

            int topK = docids.size() * 10;
            List<CompletableFuture<JSONObject>> esTasks = new ArrayList<>();
            String esUrl = String.format("http://%s/%s/_search", nlpConfig.getEsUrl().get(0), indexName);

            // 创建ES查询任务
            for (List<Double> doubles : embedding) {
                JSONObject body = new JSONObject();
                body.putOpt("size", topK);

                JSONObject knn = new JSONObject();
                knn.putOpt("field", embeddingField);
                knn.putOpt("query_vector", doubles);
                knn.putOpt("k", Math.min(topK * 2, 10000));
                knn.putOpt("num_candidates", Math.min(topK * 6, 10000));

                JSONObject filter = new JSONObject();
                JSONObject terms = new JSONObject();
                terms.putOpt(docIdField, docids);
                filter.putOpt("terms", terms);
                knn.putOpt("filter", filter);

                body.putOpt("knn", knn);

                JSONArray sort = new JSONArray();
                JSONObject scoreSort = new JSONObject();
                scoreSort.putOpt("_score", JSONUtil.createObj().set("order", "desc"));
                sort.add(scoreSort);
                body.putOpt("sort", sort);

                JSONArray source = new JSONArray();
                source.add(docIdField);
                source.add(contentField);
                source.add(nameField);
                body.putOpt("_source", source);

                esTasks.add(CompletableFuture.supplyAsync(() -> JSONUtil.parseObj(HttpUtil.post(esUrl, body))));
            }

            List<JSONObject> esTasksResult = esTasks.stream()
                    .map(CompletableFuture::join)
                    .toList();

            // 处理结果
            List<List<Double>> result = new ArrayList<>();
            for (int i = 0; i < queryContents.size(); i++) {
                if (esTasksResult.get(i).isEmpty() || !esTasksResult.get(i).containsKey("hits")) {
                    continue;
                }
                JSONArray hits = esTasksResult.get(i).getJSONObject("hits").getJSONArray("hits");
                if (hits.isEmpty()) {
                    continue;
                }

                Map<Integer, Double> docMaxDict = new HashMap<>();
                for (int j = 0; j < hits.size(); j++) {
                    JSONObject hit = hits.getJSONObject(j);
                    int docId = hit.getJSONObject("_source").getInt(docIdField);
                    double score = hit.getDouble("_score");
                    docMaxDict.merge(docId, score, Math::max);
                }

                double minDocScore = docMaxDict.values().stream().min(Double::compareTo).orElse(0.0);
                List<Double> scores = docids.stream()
                        .map(did -> docMaxDict.getOrDefault(did, minDocScore))
                        .collect(Collectors.toList());
                result.add(scores);
            }

            // 使用INDArray计算最终分数
            INDArray resultArray = Nd4j.create(result.stream()
                    .map(l -> l.stream().mapToDouble(d -> d).toArray())
                    .toArray(double[][]::new));
            INDArray weightArray = Nd4j.create(queryWeight).reshape(queryWeight.length, 1);
            INDArray scores = weightArray.mmul(resultArray).mean(0);
            Map<Integer, Double> collect = IntStream.range(0, docids.size())
                    .boxed()
                    .collect(Collectors.toMap(
                            docids::get,
                            scores::getDouble
                    ));
            weightArray.close();
            scores.close();
            return collect;

        } catch (Exception e) {
            log.error("计算文档得分失败", e);
            throw new RuntimeException(e);
        }
    }

    private List<JSONObject> get_repeat_docs_from_passage(List<Sentence> repeat_data, Map<String, String> fieldMapping, float min_passage_repeat_rate, Long required_target_docid, List<JSONObject> policy_passages, List<Req.CheckFileInfo> checkFileInfo) {
        String content_field = "text";
        String name_field = fieldMapping.getOrDefault("name", "name");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String digest_field = fieldMapping.getOrDefault("digest", "digest");
        String issueOrg_field = fieldMapping.getOrDefault("issueOrg", "issueOrg");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");

        if (required_target_docid != 0 && String.valueOf(required_target_docid).matches("\\d+")) {
            required_target_docid = Long.parseLong(String.valueOf(required_target_docid));
        }

        JSONObject source_doc = JSONUtil.createObj();
        Map<String, Map<String, JSONObject>> repeat_docs = new HashMap<>();
        Map<String, JSONObject> repeat_result = new HashMap<>();
        boolean rm_duplicate_paragraph = true;

        // 处理policy_passages
        Map<Integer, Boolean> policyPassagesMap = null;
        if (policy_passages != null) {
            policyPassagesMap = new HashMap<>();
            for (JSONObject p : policy_passages) {
                policyPassagesMap.put(p.getInt("passageIndex"), p.getBool("isPolicy"));
            }
        }

        // 构建source_doc
        for (Sentence sourcePassage : repeat_data) {
            if (!"passage".equals(sourcePassage.getRepeatType())) {
                continue;
            }
            if (policyPassagesMap != null) {
                int passageId = sourcePassage.getPassageIndex();
                if (!policyPassagesMap.getOrDefault(passageId, sourcePassage.getIsPolicy() != null ? sourcePassage.getIsPolicy() : true)) {
                    continue;
                }
            } else if (sourcePassage.getIsPolicy() != null && !sourcePassage.getIsPolicy()) {
                continue;
            }

            int sourcePassageId = sourcePassage.getPassageIndex();
            JSONObject passageInfo = JSONUtil.createObj()
                    .putOpt("passageIndex", sourcePassageId)
                    .putOpt("passageLength", sourcePassage.getPassageLength())
                    .putOpt("passage", sourcePassage.getPassage())
                    .putOpt("passageRepeatLength", 0)
                    .putOpt("passageRepeatRate", 0)
                    .putOpt("passageBlocks", sourcePassage.getPassageBlocks())
                    .putOpt("repeatPassages", new ArrayList<>());
            source_doc.putOpt(String.valueOf(sourcePassageId), passageInfo);
        }

        // 处理重复数据
        for (Sentence sourcePassage : repeat_data) {
            if (!"passage".equals(sourcePassage.getRepeatType())) {
                continue;
            }
            if (policyPassagesMap != null) {
                int passageId = sourcePassage.getPassageIndex();
                if (!policyPassagesMap.getOrDefault(passageId, sourcePassage.getIsPolicy() != null ? sourcePassage.getIsPolicy() : true)) {
                    continue;
                }
            } else if (sourcePassage.getIsPolicy() != null && !sourcePassage.getIsPolicy()) {
                continue;
            }

            int sourcePassageId = sourcePassage.getPassageIndex();
            for (JSONObject targetPassage : sourcePassage.getRepeatPassages()) {
                if (min_passage_repeat_rate != 0.0f && targetPassage.getFloat("repeatRate") < min_passage_repeat_rate) {
                    continue;
                }
                String targetDocid = targetPassage.getStr(docId_field);
                if (required_target_docid != 0 && !String.valueOf(required_target_docid).equals(targetDocid)) {
                    continue;
                }

                repeat_docs.putIfAbsent(targetDocid, new HashMap<>());
                if (!repeat_docs.get(targetDocid).containsKey(String.valueOf(sourcePassageId))) {
                    repeat_docs.get(targetDocid).put(String.valueOf(sourcePassageId),
                            JSONUtil.parseObj(source_doc.getStr(String.valueOf(sourcePassageId))));
                }
                repeat_docs.get(targetDocid).get(String.valueOf(sourcePassageId))
                        .getJSONArray("repeatPassages").add(targetPassage);
            }
        }

        // 计算源文档总长度
        int sourceDocLength = source_doc.values().stream()
                .mapToInt(info -> ((JSONObject) info).getInt("passageLength"))
                .sum();

        // 如果未指定目标文档ID
        if (required_target_docid == 0) {
            for (Map.Entry<String, Map<String, JSONObject>> entry : repeat_docs.entrySet()) {
                String targetDocid = entry.getKey();
                Map<String, JSONObject> repeatInfo = entry.getValue();

                JSONObject docResult = new JSONObject();
                int docRepeatLength = 0;
                int numOfRepeatPassage = 0;

                for (Map.Entry<String, JSONObject> sourceEntry : repeatInfo.entrySet()) {
                    JSONObject sourcePassageInfo = sourceEntry.getValue();
                    List<JSONObject> repeatPassages = sourcePassageInfo.getBeanList("repeatPassages", JSONObject.class);

                    if (!repeatPassages.isEmpty()) {
                        double[] microRepeatRate = get_micro_repeat_rate(repeatPassages, sourcePassageInfo.getInt("passageLength"));
                        docRepeatLength += (int) microRepeatRate[0];

                        for (JSONObject targetPassage : repeatPassages) {
                            numOfRepeatPassage++;
                            for (String key : targetPassage.keySet()) {
                                if (!docResult.containsKey(key) &&
                                        !Arrays.asList("repeatRate", "repeatLength", "numOfRepeatPassage",
                                                "targetHighlight", "queryHighlight", "targetHtml", "text",
                                                splitId_field, rectangularBlock_field, embedding_field,
                                                passageIds_field).contains(key)) {
                                    docResult.putOpt(key, targetPassage.get(key));
                                }
                            }
                        }
                    }
                }

                double docRepeatRate = (double) docRepeatLength / sourceDocLength;
                docResult.putOpt("repeatRate", docRepeatRate);
                docResult.putOpt("numOfRepeatPassage", numOfRepeatPassage);
                docResult.putOpt("repeatLength", docRepeatLength);
                repeat_result.put(targetDocid, docResult);
            }

            // 处理checkFileInfo
            if (checkFileInfo != null && !checkFileInfo.isEmpty()) {
                for (Req.CheckFileInfo doc : checkFileInfo) {
                    String recordId = doc.getRecordId();
                    if (!repeat_result.containsKey(recordId)) {
                        if (DELETED.equals(doc.getDocStatus()) || NO_PERMISSION.equals(doc.getDocStatus())) {
                            repeat_result.put(recordId, JSONUtil.parseObj(doc));
                        } else {
                            doc.setRepeatRate(0);
                            doc.setNumOfRepeatPassage(0);
                            doc.setRepeatLength(0);
                            repeat_result.put(recordId, JSONUtil.parseObj(doc));
                        }
                    } else {
                        repeat_result.get(recordId).putOpt("docStatus", doc.getDocStatus());
                    }
                }
            }

            List<JSONObject> result = repeat_result.values().stream()
                    .filter(v -> checkFileInfo != null && !checkFileInfo.isEmpty() || v.getDouble("repeatRate") > 0.2)
                    .sorted((a, b) -> {
                        int statusCompare = Double.compare(
                                b.getDouble("docStatus", 0.0),
                                a.getDouble("docStatus", 0.0)
                        );
                        if (statusCompare != 0) {
                            return statusCompare;
                        }
                        return Double.compare(
                                b.getDouble("repeatRate", 0.0),
                                a.getDouble("repeatRate", 0.0)
                        );
                    })
                    .collect(Collectors.toList());

            return result;
        }
        // 如果指定了目标文档ID且存在
        else if (repeat_docs.containsKey(String.valueOf(required_target_docid))) {
            Map<String, JSONObject> repeatInfo = repeat_docs.get(String.valueOf(required_target_docid));
            List<JSONObject> sourceParagraphs = new ArrayList<>();
            List<Integer> sourceParagraphIds = new ArrayList<>();
            List<JSONObject> targetParagraphs = new ArrayList<>();
            List<Integer> targetParagraphIds = new ArrayList<>();
            List<Double> passageRepeatRates = new ArrayList<>();
            List<List<JSONObject>> multiPagesParagraphs = new ArrayList<>();

            int docRepeatLength = 0;
            int numOfRepeatPassage = 0;
            Set<Integer> sourcePassageIdSet = new HashSet<>();
            Set<Integer> targetPassageIdSet = new HashSet<>();

            for (Map.Entry<String, JSONObject> entry : repeatInfo.entrySet()) {
                String sourcePassageId = entry.getKey();
                JSONObject sourcePassageInfo = entry.getValue();
                List<JSONObject> repeatPassages = sourcePassageInfo.getBeanList("repeatPassages", JSONObject.class);

                if (!repeatPassages.isEmpty()) {
                    double[] microRepeatRate = get_micro_repeat_rate(repeatPassages,
                            sourcePassageInfo.getInt("passageLength"));
                    docRepeatLength += (int) microRepeatRate[0];

                    for (JSONObject targetPassage : repeatPassages) {
                        numOfRepeatPassage++;

                        sourceParagraphIds.add(Integer.parseInt(sourcePassageId));
                        targetParagraphIds.add(targetPassage.getInt("passageIndex"));
                        passageRepeatRates.add(targetPassage.getDouble("repeatRate") * 100);

                        if (!targetPassageIdSet.contains(targetPassage.getInt("passageIndex")) || !rm_duplicate_paragraph) {
                            targetPassageIdSet.add(targetPassage.getInt("passageIndex"));
                            JSONArray rectList = targetPassage.getJSONArray(rectangularBlock_field);

                            if (rectList.size() > 1) {
                                List<JSONObject> pages = new ArrayList<>();
                                for (int j = 0; j < rectList.size(); j++) {
                                    JSONObject block = rectList.getJSONObject(j);
                                    JSONObject page = new JSONObject();
                                    page.putOpt("index", targetPassage.getInt("passageIndex"));
                                    page.putOpt("pageIndex", block.getInt("pageIndex"));
                                    pages.add(page);
                                }
                                multiPagesParagraphs.add(pages);
                            }

                            for (int j = 0; j < rectList.size(); j++) {
                                JSONObject block = rectList.getJSONObject(j);
                                JSONObject para = new JSONObject();
                                para.putOpt("index", targetPassage.getInt("passageIndex"));
                                para.putOpt("pageIndex", block.getInt("pageIndex"));
                                para.putOpt("text", targetPassage.getStr("text"));
                                para.putOpt("rect", block.get("rect"));
                                targetParagraphs.add(para);
                            }
                        }

                        if (!sourcePassageIdSet.contains(Integer.parseInt(sourcePassageId)) || !rm_duplicate_paragraph) {
                            sourcePassageIdSet.add(Integer.parseInt(sourcePassageId));
                            JSONArray blocks = source_doc.getJSONObject(sourcePassageId)
                                    .getJSONArray("passageBlocks");

                            for (int j = 0; j < blocks.size(); j++) {
                                JSONObject block = blocks.getJSONObject(j);
                                JSONObject para = new JSONObject();
                                para.putOpt("index", block.getInt("passageIndex"));
                                para.putOpt("pageIndex", block.getInt("index"));
                                para.putOpt("text", block.getStr("text"));
                                para.putOpt("rect", block.get("rect"));
                                sourceParagraphs.add(para);
                            }
                        }
                    }
                }
            }

            double docRepeatRate = (double) docRepeatLength / sourceDocLength;

            JSONObject result = new JSONObject();
            result.putOpt("source", JSONUtil.createObj()
                    .putOpt("paragraph", sourceParagraphs)
                    .putOpt("paragraphId", sourceParagraphIds));
            result.putOpt("target", JSONUtil.createObj()
                    .putOpt("paragraph", targetParagraphs)
                    .putOpt("paragraphId", targetParagraphIds)
                    .putOpt("scores", passageRepeatRates)
                    .putOpt("multi_pages_paragraphs", multiPagesParagraphs));
            result.putOpt("count", numOfRepeatPassage);
            result.putOpt("doc_repeat_rate", Math.round(docRepeatRate * 100 * 100.0) / 100.0);
            result.putOpt("n_repeat_char", docRepeatLength);

            return Collections.singletonList(result);
        }
        // 如果目标文档ID不存在
        else {
            JSONObject result = new JSONObject();
            result.putOpt("source", JSONUtil.createObj()
                    .putOpt("paragraph", new ArrayList<>())
                    .putOpt("paragraphId", new ArrayList<>()));
            result.putOpt("target", JSONUtil.createObj()
                    .putOpt("paragraph", new ArrayList<>())
                    .putOpt("paragraphId", new ArrayList<>())
                    .putOpt("scores", new ArrayList<>()));
            result.putOpt("count", 0);
            result.putOpt("doc_repeat_rate", 0);
            result.putOpt("n_repeat_char", 0);
            return Collections.singletonList(result);
        }
    }


    private List<Map.Entry<JSONObject, Float>> get_split_passages(List<String> passage_es_result, List<Double> qv, Map<String, String> fieldMapping, String indexName, int top_k, Boolean filtered) {
        filtered = filtered != null && filtered;
        String content_field = fieldMapping.getOrDefault("content", "content");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");
        List<Map.Entry<JSONObject, Float>> sortedResults = new ArrayList<>();

        List<List<String>> docId_passageIds = new ArrayList<>();
        for (String es_r : passage_es_result) {
            JSONObject entries = JSONUtil.parseObj(es_r);
            JSONArray pid = JSONUtil.parseArray(entries.getOrDefault(passageIds_field, Collections.singletonList(entries.get(splitId_field))));
            for (Object o : pid) {
                docId_passageIds.add(Arrays.asList(entries.getStr(docId_field), o.toString()));
            }
        }
        List<String> docIds = new ArrayList<>();
        List<String> passageIds = new ArrayList<>();
        for (List<String> docIdPassageId : docId_passageIds) {
            if (!docIds.contains(docIdPassageId.get(0))) docIds.add(docIdPassageId.get(0));
            if (!passageIds.contains(docIdPassageId.get(1))) passageIds.add(docIdPassageId.get(1));
        }
        HashSet<List<String>> docId_passageIds_sets = new HashSet<>(docId_passageIds);
        JSONObject body = JSONUtil.createObj();
        body.putByPath("query.bool.must[0].terms." + docId_field, docIds);
        body.putByPath("query.bool.must[1].bool.should[0].terms." + passageIds_field, passageIds);
        body.putByPath("query.bool.must[1].bool.should[1].terms." + splitId_field, passageIds);
        body.putByPath("_source.excludes[0]", embedding_field);
        body.putOpt("size", 10000);

        String url = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";
        String bm25_res;
        if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
            try (HttpResponse execute = HttpUtil.createPost(url).header(
                    "Authorization", " Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
            ).body(JSONUtil.toJsonStr(body)).execute()) {
                bm25_res = execute.body();
            }
        } else {
            bm25_res = HttpUtil.post(url, JSONUtil.toJsonStr(body));
        }
        JSONArray array = parseEsResult(bm25_res, JSONUtil.toJsonStr(body));
        if (array == null) return Collections.emptyList();
        JSONArray hits = JSONUtil.parseObj(bm25_res).getByPath("hits.hits", JSONArray.class);
        List<JSONObject> source = hits.stream().map(hits_ -> JSONUtil.parseObj(hits_).getJSONObject("_source")).toList();
        List<JSONObject> resp = source.stream().sorted(Comparator.comparingInt(o -> JSONUtil.parseObj(o).getInt(docId_field)).thenComparing(o -> JSONUtil.parseObj(o).getStr(splitId_field))).toList();
        List<JSONObject> result = new ArrayList<>();
        Set<List<String>> result_set = new HashSet<>();
        for (Object o : resp) {
            JSONObject r = JSONUtil.parseObj(o);
            String docid = r.getStr(docId_field);
            if (r.containsKey(passageIds_field)) {
                JSONArray jsonArray = r.getJSONArray(passageIds_field);
                String[] split = r.getStr(content_field).strip().split("\n");
                JSONArray jsonArray1 = r.getJSONArray(rectangularBlock_field);
                int size = Math.min(Math.min(jsonArray.size(), jsonArray1.size()), split.length);
                for (int i = 0; i < size; i++) {
                    String pid = jsonArray.getStr(i);
                    String c = split[i];
                    Object rect = jsonArray1.get(i);
                    if (!docId_passageIds_sets.contains(Arrays.asList(docid, pid))) continue;
                    if (result_set.contains(Arrays.asList(docid, pid, c))) {
                        continue;
                    } else if (CollectionUtils.isNotEmpty(result) && result.get(result.size() - 1).getStr(docId_field).equals(docid) && result.get(result.size() - 1).getStr(splitId_field).equals(pid)) {
                        String str = result.get(result.size() - 1).getStr(content_field);
                        JSONObject entries = result.get(result.size() - 1).putOpt(content_field, str + c);
                        result.set(result.size() - 1, entries);
                    } else {
                        result_set.add(Arrays.asList(docid, pid, c));
                        JSONObject _r = BeanUtil.copyProperties(r, JSONObject.class);
                        _r.remove(passageIds_field);
                        result.add(_r.putOpt("passageIndex", pid).putOpt(splitId_field, pid).putOpt(rectangularBlock_field, rect).putOpt(content_field, c));
                    }
                }
            } else {
                String pid = r.getStr(splitId_field);
                String c = r.getStr(content_field).strip();
                if (!docId_passageIds.contains(Arrays.asList(docid, pid))) {
                    continue;
                }
                if (result_set.contains(Arrays.asList(docid, pid, c))) {
                    continue;
                } else {
                    result_set.add(Arrays.asList(docid, pid, c));
                    JSONObject _r = BeanUtil.copyProperties(r, JSONObject.class);
                    _r.putOpt("passageIndex", pid);
                    result.add(_r);
                }
            }
        }
        result = result.stream().filter(r -> r.getStr(content_field).length() > 10).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        if (filtered && top_k > 0) {
            Double[] qv_d_1 = Convert.toDoubleArray(qv);
            double[][] qv_d_2 = {Arrays.stream(qv_d_1).mapToDouble(Double::doubleValue).toArray()};
            // (1,768)
            try (INDArray queryEmbedding = Nd4j.create(qv_d_2);
                 INDArray normalizedQueryEmbedding = normalizeVector(queryEmbedding)) {
                //(n,768)
                List<List<Double>> docs_embedding = get_embedding(result.stream().map(r -> r.getStr(content_field)).toList(), false);
                double[][] docs_embedding_array = docs_embedding.stream()
                        .map(list -> list.stream()
                                .mapToDouble(Double::doubleValue)
                                .toArray())
                        .toArray(double[][]::new);
                try (INDArray docs_embedding_nd = Nd4j.create(docs_embedding_array);
                     // (1,x)
                     INDArray docs_embedding_nd_nor = normalizeVector(docs_embedding_nd);
                     INDArray docs_embedding_nd_nor_transpose = docs_embedding_nd_nor.transpose();
                     //点积(1,n)
                     INDArray scores = normalizedQueryEmbedding.mmul(docs_embedding_nd_nor_transpose);
                     INDArray scores_0 = scores.getRow(0)) {
                    float[] scoresArray = scores_0.toFloatVector();
                    // 将result和scores组合并按分数排序
                    sortedResults = new ArrayList<>();
                    for (int i = 0; i < result.size(); i++) {
                        sortedResults.add(new AbstractMap.SimpleEntry<>(result.get(i), scoresArray[i]));
                    }

                    // 按分数降序排序并取前top_k个
                    sortedResults.sort((a, b) -> Float.compare(b.getValue(), a.getValue()));
                    sortedResults = sortedResults.subList(0, Math.min(top_k, sortedResults.size()));
                }
            }
        } else {
            for (JSONObject r : result) {
                sortedResults.add(new AbstractMap.SimpleEntry<>(r, 0.0f));
            }
        }
        return sortedResults;
    }

    private String render(String s, List<String> highlight) {
        List<int[]> highlightRange = getHighlightRange(highlight);
        int p = 0;
        StringBuilder result = new StringBuilder();
        for (int[] hi : highlightRange) {
            int start = hi[0];
            int end = hi[1];
            result.append(s, p, start);
            result.append("<font color=red>").append(s, start, end).append("</font>");
            p = end;
        }
        result.append(s, p, s.length());
        return result.toString();
    }

    private List<int[]> getHighlightRange(List<String> highlight) {
        Integer start = null;
        List<int[]> result = new ArrayList<>();
        for (int i = 0; i < highlight.size(); i++) {
            if (Convert.toInt(highlight.get(i)) != 0) { // Check for non-null and non-empty strings
                if (start == null) {
                    start = i;
                }
            } else {
                if (start != null) {
                    result.add(new int[]{start, i});
                    start = null;
                }
            }
        }
        if (start != null) {
            result.add(new int[]{start, highlight.size()});
        }
        return result;
    }

    private List<String> mix_embedding_search(List<Double> qv, Map<String, String> fieldMapping, JSONObject permissionDsl, String indexName, FilterItem filterItems, Boolean filtered, int top_k, double threshold) {
        top_k = top_k == 0 ? 10 : top_k;
        filtered = filtered != null && filtered;
        threshold = threshold == 0 ? 0.88 : threshold;
        String content_field = fieldMapping.getOrDefault("content", "content");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");

        long start_time = System.currentTimeMillis();
        String url = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";
        JSONObject body = JSONUtil.createObj().putOpt("size", top_k);
        body.putByPath("knn.field", embedding_field);
        body.putByPath("knn.query_vector", qv);
        body.putByPath("knn.k", top_k * 2);
        body.putByPath("knn.num_candidates", top_k * 6);
        body.putByPath("sort._score.order", "desc");
        body.putByPath("_source.excludes", Collections.singletonList(embedding_field));
        JSONArray musts = JSONUtil.createArray();
        JSONObject obj = JSONUtil.createObj();
        obj.putByPath("exists.field", content_field);
        musts.add(obj);
        if (permissionDsl != null) musts.add(permissionDsl);
        if (filtered && (CollectionUtils.isNotEmpty(filterItems.getRepoIds()) || CollectionUtils.isNotEmpty(filterItems.getCategoryIds()) || (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2))) {
            if (CollectionUtils.isNotEmpty(filterItems.getRepoIds())) {
                musts.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(repoId_field, filterItems.getRepoIds())));
                // 如果filterItems中有categoryIds，则添加到exists列表中
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCategoryIds())) {
                filterItems.getCategoryIds().stream().forEach(category -> musts.add(JSONUtil.createObj().putOpt("term", JSONUtil.createObj().putOpt(categories_field, category))));
                // 如果filterItems中有printYearRange，则添加到exists列表中
            }
            if (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2) {
                musts.add(JSONUtil.createObj().putOpt("range", JSONUtil.createObj().putOpt(printedTime_field, JSONUtil.createObj().putOpt("gte", filterItems.getPrintYearRange().get(0)).putOpt("lte", filterItems.getPrintYearRange().get(1)))));
            }
        }
        body.putByPath("knn.filter.bool.must", musts);
        String jsonStr = JSONUtil.toJsonStr(body);
//    log.info("es search body: {}", jsonStr);
        String es_res;
        if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
            try (HttpResponse execute = HttpUtil.createPost(url).header(
                    "Authorization", "Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
            ).body(jsonStr).execute()) {
                es_res = execute.body();
            }
            // 否则直接post请求
        } else {
            es_res = HttpUtil.post(url, jsonStr);
            // 解析返回结果
        }
        JSONArray array = parseEsResult(es_res, JSONUtil.toJsonStr(es_res));
        if (array == null) return Collections.emptyList();
        // 返回结果
        JSONArray hits = JSONUtil.parseObj(es_res).getByPath("hits.hits", JSONArray.class);
        long end_time = System.currentTimeMillis();
        log.info("es search time: {}", end_time - start_time);
        List<String> resp = new ArrayList<>();
        for (Object _hit : hits) {
            JSONObject hit = JSONUtil.parseObj(_hit);
            Double score = hit.getDouble("_score");
            hit.putByPath("_source.score", score);
            if (score > threshold) {
                resp.add(hit.getStr("_source"));
            }
        }
        return resp;
    }

    private List<String> mix_total_match(String query, Map<String, String> fieldMapping, JSONObject permissionDsl, String indexName, FilterItem filterItems, Boolean filtered, int top_k, String searchRangeDsl) {
        // 如果top_k为0，则默认为10
        top_k = top_k == 0 ? 10 : top_k;
        filtered = filtered != null && filtered;
        String content_field = fieldMapping.getOrDefault("content", "content");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");
        // 添加match_phrase查询
        JSONObject dsl = JSONUtil.createObj();
        // 设置返回结果数量
        dsl.putByPath("query.bool.must[0]", JSONUtil.createObj().putOpt("match_phrase", JSONUtil.createObj().putOpt(content_field, query)));
        // 设置排序方式
        dsl.putOpt("size", top_k);
        // 设置不返回的field
        dsl.putByPath("sort[0]._score.order", "desc");
        dsl.putByPath("_source.excludes", Collections.singletonList(embedding_field));
        // 创建exists列表

        List<JSONObject> exists = new ArrayList<>(Arrays.asList(
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", title_field)),
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", docId_field)),
                // 如果permissionDsl不为空，则添加到exists列表中
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", content_field))));
        if (permissionDsl != null) exists.add(JSONUtil.parseObj(permissionDsl));
        // 如果filtered为true，并且filterItems中有repoIds、categoryIds或printYearRange，则添加到exists列表中

        // 如果filterItems中有repoIds，则添加到exists列表中
        if (filtered && (CollectionUtils.isNotEmpty(filterItems.getRepoIds())
                || CollectionUtils.isNotEmpty(filterItems.getCategoryIds())
                || (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2))
                || CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
            if (CollectionUtils.isNotEmpty(filterItems.getRepoIds())) {
                exists.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(repoId_field, filterItems.getRepoIds())));
                // 如果filterItems中有categoryIds，则添加到exists列表中
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCategoryIds())) {
                filterItems.getCategoryIds().forEach(category -> exists.add(JSONUtil.createObj().putOpt("term", JSONUtil.createObj().putOpt(categories_field, category))));
                // 如果filterItems中有printYearRange，则添加到exists列表中
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
                List<String> list = filterItems.getCheckFileInfo().stream().map(Req.CheckFileInfo::getRecordId).toList();
                exists.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(docId_field, list)));

            }
            if (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2) {
                exists.add(JSONUtil.createObj().putOpt("range", JSONUtil.createObj().putOpt(printedTime_field, JSONUtil.createObj().putOpt("gte", filterItems.getPrintYearRange().get(0)).putOpt("lte", filterItems.getPrintYearRange().get(1)))));
            }
            // 将exists列表添加到dsl中
        }
        // 创建url
        dsl.putByPath("query.bool.filter.bool.must", exists);
        String url = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";
        // 如果ESConfig中有es_username和es_password，则使用Basic认证
        String es_res;
        if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
            try (HttpResponse execute = HttpUtil.createPost(url).header(
                    "Authorization", " Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
            ).body(JSONUtil.toJsonStr(dsl)).execute()) {
                es_res = execute.body();
            }
            // 否则直接post请求
        } else {
            es_res = HttpUtil.post(url, JSONUtil.toJsonStr(dsl));
            // 解析返回结果
        }
        JSONArray array = parseEsResult(es_res, JSONUtil.toJsonStr(dsl));
        if (array == null) return Collections.emptyList();
        // 返回结果
        JSONArray hits = JSONUtil.parseObj(es_res).getByPath("hits.hits", JSONArray.class);
        return hits.stream().map(o -> JSONUtil.parseObj(o).getStr("_source")).collect(Collectors.toList());
    }

    private List<String> mix_bm25(String query, Map<String, String> fieldMapping, JSONObject permissionDsl, String indexName, FilterItem filterItems, Boolean filtered, int top_k, String searchRangeDsl) {
        top_k = top_k == 0 ? 10 : top_k;
        filtered = filtered != null && filtered;
        String content_field = fieldMapping.getOrDefault("content", "content");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");

        List<SegToken> process = segmenter.process(query, JiebaSegmenter.SegMode.SEARCH);
        if (CollectionUtils.isEmpty(stopWords)) stopWords();
        String new_query = String.join("", process.stream().map(s -> stopWords.contains(s.word) ? " " : s.word).toList());
        JSONObject dsl = JSONUtil.createObj();
        dsl.putOpt("size", top_k);
        dsl.putByPath("query.bool.must.match." + content_field, new_query);
        dsl.putByPath("sort[0]._score.order", "desc");
        dsl.putByPath("_source.excludes", Arrays.asList(embedding_field, "LOCATION", "ORGANIZATION", "DATE", "PERSON", "keywords", "DURATION", "实体机构", "实体地点", "实体日期", "关键词", "实体人物", "summary", "ORDINAL"));
        JSONArray must = JSONUtil.createArray();
        must.add(JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", title_field)));
        must.add(JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", docId_field)));
        must.add(JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", content_field)));
        if (permissionDsl != null) {
            must.add(permissionDsl);
        }
        if (searchRangeDsl != null) {
            must.add(searchRangeDsl);
        }
        if (filtered && (CollectionUtils.isNotEmpty(filterItems.getCategoryIds())
                || CollectionUtils.isNotEmpty(filterItems.getRepoIds())
                || (CollectionUtils.isNotEmpty(filterItems.getPrintYearRange()) && filterItems.getPrintYearRange().size() == 2))
                || CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
            if (CollectionUtils.isNotEmpty(filterItems.getCategoryIds())) {
                filterItems.getCategoryIds().forEach(category -> must.add(JSONUtil.createObj().putOpt("term", JSONUtil.createObj().putOpt(categories_field, category))));
            }
            if (CollectionUtils.isNotEmpty(filterItems.getRepoIds())) {
                must.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(repoId_field, filterItems.getRepoIds())));
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
                List<String> list = filterItems.getCheckFileInfo().stream().map(Req.CheckFileInfo::getRecordId).toList();
                must.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(docId_field, list)));

            }
            if (CollectionUtils.isNotEmpty(filterItems.getPrintYearRange()) && filterItems.getPrintYearRange().size() == 2) {
                must.add(JSONUtil.createObj().putOpt("range", JSONUtil.createObj().putOpt(printedTime_field, JSONUtil.createObj().putOpt("gte", filterItems.getPrintYearRange().get(0)).putOpt("lte", filterItems.getPrintYearRange().get(1)))));
            }
        }
        dsl.putByPath("query.bool.filter.bool.must", must);
        String url = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";
        String bm25_res;
        String jsonStr1 = JSONUtil.toJsonStr(dsl);
        if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
            try (HttpResponse execute = HttpUtil.createPost(url).header(
                    "Authorization", "Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
            ).body(jsonStr1).execute()) {
                bm25_res = execute.body();
            }
        } else {
            bm25_res = HttpUtil.post(url, jsonStr1);
        }
        JSONArray array = parseEsResult(bm25_res, jsonStr1);
        if (array == null) return Collections.emptyList();
        JSONArray hits = JSONUtil.parseObj(bm25_res).getByPath("hits.hits", JSONArray.class);
        return hits.stream().map(o -> JSONUtil.parseObj(o).getStr("_source")).collect(Collectors.toList());
    }


    private List<String> mix_phrase_search(String query, Map<String, String> fieldMapping, JSONObject permissionDsl, String indexName, FilterItem filterItems, Boolean filtered, int top_k, String searchRangeDsl) {
        top_k = top_k == 0 ? 10 : top_k;
        filtered = filtered != null && filtered;
        String content_field = fieldMapping.getOrDefault("content", "content");
        String name_field = fieldMapping.getOrDefault("name", "name");
        String title_field = fieldMapping.getOrDefault("title", "title");
        String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
        String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
        String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
        String categories_field = fieldMapping.getOrDefault("categories", "categories");
        String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");
        List<String> all_phrase = new ArrayList<>(Arrays.stream(query.split("[，；。！？：]")).filter(s -> s.length() > 2).toList());
        all_phrase.add(query);
        JSONObject dsl = JSONUtil.createObj();
        dsl.putByPath("query.bool.should", all_phrase.stream().map(s -> {
            JSONObject obj = JSONUtil.createObj();
            obj.putByPath("match_phrase." + content_field, s);
            return obj;
        }).toList());
        dsl.putOpt("size", top_k).putByPath("sort[0]._score.order", "desc");
        dsl.putByPath("_source.excludes", Arrays.asList(embedding_field, "LOCATION", "ORGANIZATION", "DATE", "PERSON", "keywords", "DURATION", "实体机构", "实体地点", "实体日期", "关键词", "实体人物", "summary", "ORDINAL"));
        List<JSONObject> exists = new ArrayList<>(Arrays.asList(
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", title_field)),
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", docId_field)),
                JSONUtil.createObj().putOpt("exists", JSONUtil.createObj().putOpt("field", content_field))));
        if (permissionDsl != null) exists.add(JSONUtil.parseObj(permissionDsl));

        if (filtered && (CollectionUtils.isNotEmpty(filterItems.getRepoIds())
                || CollectionUtils.isNotEmpty(filterItems.getCategoryIds())
                || (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2))
                || CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
            if (CollectionUtils.isNotEmpty(filterItems.getRepoIds())) {
                exists.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(repoId_field, filterItems.getRepoIds())));
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCategoryIds())) {
                filterItems.getCategoryIds().forEach(category -> exists.add(JSONUtil.createObj().putOpt("term", JSONUtil.createObj().putOpt(categories_field, category))));
            }
            if (CollectionUtils.isNotEmpty(filterItems.getCheckFileInfo())) {
                List<String> list = filterItems.getCheckFileInfo().stream().map(Req.CheckFileInfo::getRecordId).toList();
                exists.add(JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(docId_field, list)));
            }
            if (filterItems.getPrintYearRange() != null && filterItems.getPrintYearRange().size() == 2) {
                exists.add(JSONUtil.createObj().putOpt("range", JSONUtil.createObj().putOpt(printedTime_field, JSONUtil.createObj().putOpt("gte", filterItems.getPrintYearRange().get(0)).putOpt("lte", filterItems.getPrintYearRange().get(1)))));
            }
        }
        dsl.putByPath("query.bool.filter.bool.must", exists);
        String url = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";
        String es_res;
        if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
            try (HttpResponse execute = HttpUtil.createPost(url).header(
                    "Authorization", " Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
            ).body(JSONUtil.toJsonStr(dsl)).execute()) {
                es_res = execute.body();
            }
        } else {
            es_res = HttpUtil.post(url, JSONUtil.toJsonStr(dsl));
        }
        JSONArray array = parseEsResult(es_res, JSONUtil.toJsonStr(dsl));
        if (array == null) return Collections.emptyList();
        JSONArray hits = JSONUtil.parseObj(es_res).getByPath("hits.hits", JSONArray.class);
        return hits.stream().map(o -> JSONUtil.parseObj(o).getStr("_source")).collect(Collectors.toList());
    }

    private double[] get_micro_repeat_rate(List<JSONObject> repeat_passages, int source_passage_length) {
        double repeat_rate;
        double repeat_length;
        List<List<Double>> queryHighlight = repeat_passages.stream().map(s -> s.getBeanList("queryHighlight", Double.class)).toList();

        double[][] repeat_matrix_list = queryHighlight.stream()
                .map(list -> list.stream()
                        .mapToDouble(Double::doubleValue)
                        .toArray())
                .toArray(double[][]::new);
        try (INDArray repeat_matrix = Nd4j.create(repeat_matrix_list);
             INDArray gt = repeat_matrix.sum(0).gt(0).castTo(DataType.INT)) {
            repeat_length = gt.sumNumber().intValue();
            repeat_rate = repeat_length / source_passage_length;
        }
        return new double[]{repeat_length, repeat_rate};
    }

    private JSONArray parseEsResult(String result,String query){
        if (StringUtils.isEmpty(result) || !JSONUtil.isTypeJSONObject(result) || JSONUtil.parseObj(result).getByPath("hits.hits", JSONArray.class) == null) {
            log.info("es search null, result: {}, dsl: {}", result, query);
            return null;
        }
        return JSONUtil.createArray();
    }

    private Repeat get_diff_3(String query, String target, List<String> query_words) {
        if (CollectionUtils.isEmpty(query_words)) {
            query_words = words_cut(query);
        }
        List<String> target_words = words_cut(target);
        Map<Character, String> char2word = new HashMap<>();
        Map<String, String> word2char = new HashMap<>();
        StringBuilder query_words_str = new StringBuilder();
        StringBuilder target_words_str = new StringBuilder();
        int p = 0;
        for (String w : query_words) {
            if (word2char.containsKey(w)) {
                query_words_str.append(word2char.get(w));
            } else {
                char2word.put(RepeatConstant.charset.charAt(p), w);
                word2char.put(w, RepeatConstant.charset.charAt(p) + "");
                query_words_str.append(word2char.get(w));
                p += 1;
            }
        }
        for (String w : target_words) {
            if (word2char.containsKey(w)) {
                target_words_str.append(word2char.get(w));
            } else {
                char2word.put(RepeatConstant.charset.charAt(p), w);
                word2char.put(w, RepeatConstant.charset.charAt(p) + "");
                target_words_str.append(word2char.get(w));
                p += 1;
            }
        }
        diff_match_patch dmp = new diff_match_patch();
        LinkedList<diff_match_patch.Diff> diffs = dmp.diff_main(query_words_str.toString(), target_words_str.toString());
        List<String> diffList = new ArrayList<>();
        for (diff_match_patch.Diff diff : diffs) {
            int sign = diff.operation == diff_match_patch.Operation.DELETE ? -1 :
                    diff.operation == diff_match_patch.Operation.INSERT ? 1 : 0;
            String seg = diff.text;
            for (char c : seg.toCharArray()) {
                String word = char2word.getOrDefault(c, String.valueOf(c));
                if (sign == -1) {
                    diffList.add("- " + word);
                } else if (sign == 1) {
                    diffList.add("+ " + word);
                } else {
                    diffList.add("  " + word);
                }
            }
        }
        int query_total = 0;
        int n_repeat = 0;
        List<String> query_highlight = new ArrayList<>();
        List<String> target_highlight = new ArrayList<>();
        int diff_len = diffList.size();
        for (int i = 0; i < diffList.size(); i++) {
            String c = diffList.get(i);
            String w = c.substring(2);
            int w_l = w.length();
            if (w_l == 1 && RepeatConstant.punctuation.contains(w) && c.startsWith("-")) {
                query_highlight.add("0");
            } else if (w_l == 1 && RepeatConstant.punctuation.contains(w) && c.startsWith("+")) {
                target_highlight.add("0");
            } else if (w_l == 1 && RepeatConstant.punctuation.contains(w) && c.startsWith(" ")) {
                query_highlight.add("0");
                target_highlight.add("0");
            } else if (c.startsWith(" ") && w_l == 1 &&
                    (i - 1 < 0 || RepeatConstant.punctuation.contains(diffList.get(i - 1).substring(2))) &&
                    (i + 1 >= diff_len || RepeatConstant.punctuation.contains(diffList.get(i + 1).substring(2)))) {
                query_highlight.add("1");
                target_highlight.add("1");
                query_total += w_l;
                n_repeat += w_l;
            } else if (c.startsWith(" ") && w_l == 1 &&
                    (i - 1 < 0 || RepeatConstant.punctuation.contains(diffList.get(i - 1).substring(2)) || !diffList.get(i - 1).startsWith(" ")) &&
                    (i + 1 >= diff_len || RepeatConstant.punctuation.contains(diffList.get(i + 1).substring(2)) || !diffList.get(i + 1).startsWith(" "))) {
                query_highlight.add("0");
                target_highlight.add("0");
                query_total += w_l;
            } else if (c.startsWith(" ")) {
                for (int j = 0; j < w_l; j++) {
                    query_highlight.add("1");
                    target_highlight.add("1");
                }
                query_total += w_l;
                n_repeat += w_l;
            } else if (c.startsWith("-")) {
                for (int j = 0; j < w_l; j++) {
                    query_highlight.add("0");
                }
                query_total += w_l;
            } else if (c.startsWith("+")) {
                for (int j = 0; j < w_l; j++) {
                    target_highlight.add("0");
                }
            }
        }
        double repeat_rate = query_total > 0 ? (double) n_repeat / query_total : 0;
        // return values as needed
//    System.out.println("n_repeat: " + n_repeat);
//    System.out.println("repeat_rate: " + repeat_rate);
//    System.out.println("query_highlight: " + query_highlight);
//    System.out.println("target_highlight: " + target_highlight);
        return new Repeat(n_repeat, repeat_rate, query_highlight, target_highlight);
    }

    static class Repeat {
        int n_repeat;
        double repeat_rate;
        List<String> query_highlight;
        List<String> target_highlight;

        public Repeat(int n_repeat, double repeat_rate, List<String> query_highlight, List<String> target_highlight) {
            this.n_repeat = n_repeat;
            this.repeat_rate = repeat_rate;
            this.query_highlight = query_highlight;
            this.target_highlight = target_highlight;
        }
    }

    private void stopWords() {
        Set<String> result = new HashSet<>();
        Resource[] resources = null;
        try {
            ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            resources = resourcePatternResolver.getResources("classpath*:nlp/" + "stop_word.txt");
        } catch (IOException e) {
            log.error("加载文件失败, fileName:{}", LogUtil.name("stop_word.txt"), e);
        }
        AssertUtils.isTrue(resources != null && resources.length != 0, STOP_WORD_NOT_NULL);
        try (InputStream in = resources[0].getInputStream()) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            String strLine;
            while (null != (strLine = reader.readLine())) {
                result.add(strLine);
            }
        } catch (Exception e) {
            log.error("读取文件失败, fileName:{}", LogUtil.name("stop_word.txt"), e);
        }
        stopWords = result;
    }

    private List<String> words_cut(String s) {
        //JAVA 版本的jieba，search和index刚好是反的。所以精确分词用seach，细粒度分词用index
        List<SegToken> tokens = segmenter.process(s, JiebaSegmenter.SegMode.SEARCH);
        List<String> words = tokens.stream().map(q -> q.word).toList();
        List<String> newWords = new ArrayList<>();
        for (String word : words) {
            if (word.length() < 3) {
                newWords.add(word);
            } else {
                // 第二步：对长词再次使用搜索引擎模式切分
                List<SegToken> subTokens = segmenter.process(word, JiebaSegmenter.SegMode.INDEX);
                List<String> subWords = new ArrayList<>();
                for (SegToken token : subTokens) {
                    subWords.add(token.word);
                }
                subWords.removeIf(w -> w.equals(word) && subWords.size() != 1); // 排除原词

                if (subWords.isEmpty()) {
                    newWords.add(word); // 无法切分则保留原词
                } else {
                    // 查找所有能拼接回原词的子词组合
                    List<List<String>> candidates = new ArrayList<>();
                    findValidCombinations(subWords, word, new ArrayList<>(), 0, candidates);
                    if (candidates.isEmpty()) {
                        newWords.add(word);
                    } else {
                        // 选择子词最多的组合
                        List<String> bestCandidate = candidates.stream()
                                .max(Comparator.comparingInt(List::size))
                                .orElse(List.of(word));
                        newWords.addAll(bestCandidate);
                    }
                }
            }
        }
        return newWords;
    }

    private static void findValidCombinations(List<String> parts, String target,
                                              List<String> current, int start,
                                              List<List<String>> results) {
        String currentStr = String.join("", current);
        if (currentStr.length() > target.length()) return;
        if (currentStr.equals(target)) {
            results.add(new ArrayList<>(current));
            return;
        }
        for (int i = start; i < parts.size(); i++) {
            String part = parts.get(i);
            if (currentStr.length() + part.length() > target.length()) continue;
            current.add(part);
            findValidCombinations(parts, target, current, i + 1, results);
            current.remove(current.size() - 1);
        }
    }


    private List<Object> getSearchResultEs(List<String> query, JSONObject permissionDsl,
                                           FilterItem filterItems, boolean filter, Map<String, String> fieldMapping, String indexName, int topK, String mode) {
        String contentField = fieldMapping.getOrDefault("content", "content");
        String nameField = fieldMapping.getOrDefault("name", "name");
        String titleField = fieldMapping.getOrDefault("title", "title");
        String splitIdField = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTimeField = fieldMapping.getOrDefault("printedTime", "printedTime");
        String digestField = fieldMapping.getOrDefault("digest", "digest");
        String issueOrgField = fieldMapping.getOrDefault("issueOrg", "issueOrg");
        String categoriesField = fieldMapping.getOrDefault("categories", "categories");
        String repoIdField = fieldMapping.getOrDefault("repoId", "repoId");
        String docIdField = fieldMapping.getOrDefault("primaryKeyId", "docId");
        String rectangularBlockField = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String embeddingField = fieldMapping.getOrDefault("embedding", "embedding");
        List<String> timeGroup = fieldMapping.containsKey("timeGroup") ? JSONUtil.parseArray(fieldMapping.get("timeGroup")).toList(String.class) : Collections.singletonList(printedTimeField);

        int minLength = 10;
        List<String> queryContents = new ArrayList<>();
        List<Integer> queryIndex = new ArrayList<>();

        for (int i = 0; i < query.size(); i++) {
            String q = query.get(i).replace(" ", "");
            if (q.length() >= minLength) {
                queryContents.add(q);
                queryIndex.add(i);
            }
        }

        if (queryContents.isEmpty()) {
            return new ArrayList<>(4);
        }

        List<Object> esTasksResult = new ArrayList<>();
        try {
            long startTime = System.currentTimeMillis();
            List<List<Double>> embedding;
            if (!"bm25".equals(mode)) {
                embedding = get_embedding(queryContents, false);
            } else {
                embedding = queryContents.stream().map(q -> new ArrayList<Double>()).collect(Collectors.toList());
            }
            long endTime = System.currentTimeMillis();
            log.info("embedding time: {}", endTime - startTime);
            log.info("number of embedding: {}", embedding.size());

            startTime = System.currentTimeMillis();
            String esUrl = "http://" + nlpConfig.getEsUrl().get(0) + "/" + indexName + "/_search";

            for (int i = 0; i < embedding.size(); i++) {
                List<Double> v = embedding.get(i);
                String q = queryContents.get(i);
                JSONObject body = JSONUtil.createObj();

                if ("bm25".equals(mode)) {
                    JSONObject match = JSONUtil.createObj().putOpt(contentField, q);
                    JSONObject must = JSONUtil.createObj().putOpt("match", match);
                    JSONObject bool = JSONUtil.createObj().putOpt("must", must);
                    body.putOpt("query", JSONUtil.createObj().putOpt("bool", bool));
                    body.putOpt("size", topK * 2);
                    body.putOpt("sort", Collections.singletonList(JSONUtil.createObj().putOpt("_score", JSONUtil.createObj().putOpt("order", "desc"))));
                    List<String> source = new ArrayList<>(Arrays.asList(contentField, splitIdField, docIdField, repoIdField, nameField, titleField, printedTimeField));
                    source.addAll(timeGroup);
                    body.putOpt("_source", source);
                } else {
                    body.putOpt("size", topK);
                    JSONObject knn = JSONUtil.createObj()
                            .putOpt("field", "embedding")
                            .putOpt("query_vector", v)
                            .putOpt("k", topK * 2)
                            .putOpt("num_candidates", topK * 6);

                    if ("embedding".equals(mode)) {
                        if (permissionDsl != null) {
                            knn.putOpt("filter", JSONUtil.createObj()
                                    .putOpt("bool", JSONUtil.createObj()
                                            .putOpt("must", Collections.singletonList(permissionDsl))));
                        }

                        if (filterItems != null && (filterItems.getCategoryIds() != null || filterItems.getRepoIds() != null)) {
                            if (!knn.containsKey("filter")) {
                                knn.putOpt("filter", JSONUtil.createObj()
                                        .putOpt("bool", JSONUtil.createObj()
                                                .putOpt("must", new ArrayList<>())));
                            }

                            if (filterItems.getCategoryIds() != null) {
                                for (Object categ : Convert.toList(filterItems.getCategoryIds())) {
                                    ((JSONArray) knn.getByPath("filter.bool.must")).add(
                                            JSONUtil.createObj().putOpt("term", JSONUtil.createObj().putOpt(categoriesField, categ))
                                    );
                                }
                            }

                            if (filterItems.getRepoIds() != null) {
                                ((JSONArray) knn.getByPath("filter.bool.must")).add(
                                        JSONUtil.createObj().putOpt("terms", JSONUtil.createObj().putOpt(repoIdField, filterItems.getRepoIds()))
                                );
                            }
                        }
                    }
                    body.putOpt("knn", knn);
                    body.putOpt("sort", Collections.singletonList(JSONUtil.createObj().putOpt("_score", JSONUtil.createObj().putOpt("order", "desc"))));
                    List<String> source = new ArrayList<>(Arrays.asList(contentField, splitIdField, docIdField, repoIdField, nameField, titleField, printedTimeField));
                    source.addAll(timeGroup);
                    body.putOpt("_source", source);
                }
                //todo: 这里源代码是异步进行的，for外等待整个任务结束。后续也要改为异步
                String esRes;
                if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
                    try (HttpResponse execute = HttpUtil.createPost(esUrl).header(
                            "Authorization", " Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
                    ).body(JSONUtil.toJsonStr(body)).execute()) {
                        esRes = execute.body();
                    }
                } else {
                    esRes = HttpUtil.post(esUrl, JSONUtil.toJsonStr(body));
                }
                esTasksResult.add(JSONUtil.parseObj(esRes).getByPath("hits.hits", List.class));
            }

            endTime = System.currentTimeMillis();
            log.info("search time: {}", endTime - startTime);

            return Arrays.asList(esTasksResult, queryIndex, queryContents, embedding);
        } catch (Exception e) {
            log.error("Error in getSearchResultEs", e);
            return new ArrayList<>(4);
        }
    }

    private List<String> split_by_sentence(String text, int min_len, boolean rm_duplicate) {
        List<String> sentences = new ArrayList<>();

        List<String> final_sentences_list = new ArrayList<>();
        for (String passage : text.split("\n")) {
            // 使用正则表达式分割句子
            List<String> sentences_list = new ArrayList<>(Arrays.asList(passage.split("[。；！？]")));
            // 移除空字符串
            sentences_list.removeIf(String::isEmpty);

            // 恢复标点符号
            String cache = "";
            for (int j = 0; j < sentences_list.size(); j++) {
                String s = sentences_list.get(j);
                for (String p : new String[]{"。", "；", "！", "？"}) {
                    if (passage.startsWith(cache + s + p)) {
                        cache += s + p;
                        sentences_list.set(j, s + p);
                        break;
                    }
                }
            }

            // 合并短句
            while (sentences_list.stream().anyMatch(s -> get_length(s) < min_len) && sentences_list.size() > 1) {
                List<String> newSentencesList = new ArrayList<>();
                boolean concatNext = false;
                for (int j = 0; j < sentences_list.size(); j++) {
                    String s = sentences_list.get(j);
                    if (concatNext) {
                        String last = newSentencesList.get(newSentencesList.size() - 1);
                        newSentencesList.set(newSentencesList.size() - 1, last + s);
                        concatNext = false;
                    } else if (get_length(s) < min_len) {
                        if (j > 0 && j < sentences_list.size() - 1) {
                            String next = sentences_list.get(j + 1);
                            String prev = sentences_list.get(j - 1);
                            if (get_length(next) > get_length(prev)) {
                                concatNext = true;
                                newSentencesList.add(s);
                            } else {
                                String last = newSentencesList.get(newSentencesList.size() - 1);
                                newSentencesList.set(newSentencesList.size() - 1, last + s);
                            }
                        } else if (j > 0) {
                            String last = newSentencesList.get(newSentencesList.size() - 1);
                            newSentencesList.set(newSentencesList.size() - 1, last + s);
                        } else if (j < sentences_list.size() - 1) {
                            concatNext = true;
                            newSentencesList.add(s);
                        }
                    } else {
                        newSentencesList.add(s);
                    }
                }
                sentences_list = newSentencesList;
            }

            final_sentences_list.addAll(sentences_list);
        }

        // 处理重复句子
        for (String s : final_sentences_list) {
            if (rm_duplicate) {
                if (!sentences.contains(s)) {
                    sentences.add(s);
                }
            } else {
                sentences.add(s);
            }
        }

        return sentences;
    }

    private List<Sentence> split_by_sentence(MergeBlock content, int min_len, boolean rm_duplicate) {
        List<Sentence> sentences = new ArrayList<>();
        String text = content.text.replace(" ", "");
        List<Rbound> blocks = content.blocks;
        int page_index = blocks.get(0).getIndex();
        List<String> final_sentences_list = new ArrayList<>();
        for (String passage : text.split("\n")) {
            String[] split = {"。", "；", ";", "！", "!", "？", "?"};
            String regex = Arrays.stream(split).map(Pattern::quote).collect(Collectors.joining("|"));
            List<String> sentences_list = Arrays.stream(passage.split(regex)).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            StringBuilder cache = new StringBuilder();
            for (int i = 0; i < sentences_list.size(); i++) {
                for (String p : split) {
                    if (passage.startsWith(cache + sentences_list.get(i) + p)) {
                        cache.append(sentences_list.get(i)).append(p);
                        sentences_list.set(i, sentences_list.get(i) + p);
                        break;
                    }
                }
            }
            //合并短句
            boolean hasShortSentence = sentences_list.stream().anyMatch(s -> get_length(s) < min_len);
            while (hasShortSentence && sentences_list.size() > 1) {
                List<String> newSentencesList = new ArrayList<>();
                boolean concatNext = false;
                for (int j = 0; j < sentences_list.size(); j++) {
                    String s = sentences_list.get(j);
                    if (concatNext) {
                        String last = newSentencesList.get(newSentencesList.size() - 1);
                        newSentencesList.set(newSentencesList.size() - 1, last + s);
                        concatNext = false;
                    } else if (get_length(s) < min_len) {
                        if (j > 0 && j < sentences_list.size() - 1) {
                            String next = sentences_list.get(j + 1);
                            String prev = sentences_list.get(j - 1);
                            if (get_length(next) > get_length(prev)) {
                                concatNext = true;
                                newSentencesList.add(s);
                            } else {
                                String last = newSentencesList.get(newSentencesList.size() - 1);
                                newSentencesList.set(newSentencesList.size() - 1, last + s);
                            }
                        } else if (j > 0) {
                            String last = newSentencesList.get(newSentencesList.size() - 1);
                            newSentencesList.set(newSentencesList.size() - 1, last + s);
                        } else {
                            concatNext = true;
                            newSentencesList.add(s);
                        }
                    } else {
                        newSentencesList.add(s);
                    }
                }
                sentences_list = newSentencesList;
                hasShortSentence = sentences_list.stream().anyMatch(s -> s.length() < min_len);
            }
            final_sentences_list.addAll(sentences_list);
        }
        for (String s : final_sentences_list) {
            List<Rbound> sentence_blocks = new ArrayList<>();
            String[] split = s.split(SPLIT_PAGE_FLAG);
            for (int page_offset = 0; page_offset < split.length; page_offset++) {
                page_index += page_offset;
                int finalPage_index = page_index;
                boolean empty = blocks.stream().noneMatch(b -> b.getIndex() == finalPage_index);
                if (empty) log.error("page index {} not found,text is {}", finalPage_index, s);
                if (StringUtils.isEmpty(split[page_offset])) continue;
                Rbound cur_block = BeanUtil.copyProperties(blocks.stream().filter(b -> b.getIndex() == finalPage_index).findFirst().orElseThrow(), Rbound.class);
                int p = CollectionUtils.isNotEmpty(sentence_blocks) && page_offset == 0 ? sentence_blocks.get(sentence_blocks.size() - 1).highlightRange[1] + 1 : 0;
                int start = cur_block.getText().indexOf(split[page_offset], p);
                int end = start + split[page_offset].length() - 1;
                cur_block.highlightRange = new int[]{start, end};
                cur_block.setText(split[page_offset]);
                sentence_blocks.add(cur_block);
            }
            sentences.add(new Sentence(sentence_blocks, s.replace(SPLIT_PAGE_FLAG, "")));
        }
        return sentences;
    }

    private int get_length(String text) {
        String replace = text.replace(SPLIT_PAGE_FLAG, "");
        StringBuilder stringBuilder = new StringBuilder();
        IntStream.range(0, replace.length()).filter(i -> !RepeatConstant.punctuation.contains(String.valueOf(replace.charAt(i)))).forEach(i -> stringBuilder.append(replace.charAt(i)));
        return stringBuilder.toString().length();
    }

    private List<List<Double>> get_embedding(List<String> text, boolean is_search) {
        ArrayList<List<Double>> embedding_result = new ArrayList<>();
        try {
            List<List<String>> partition_text = Lists.partition(text, embedding_batch_size);
            String url = serverBasePathConfig.getAiMiddleHostPort() + "/openApi/algorithm/online/embedding";
//            String url = "http://" + EmbeddingConfig.embedding_url + "/algorithm";
            for (List<String> contents : partition_text) {
                String post = HttpUtil.post(url, JSONUtil.toJsonStr(JSONUtil.createObj().putOpt("contents", contents).putOpt("is_search", is_search)));
                // 解析返回的JSON数据
                if (!JSONUtil.isTypeJSON(post)) {
                    throw new RuntimeException("调用向量计算接口失败，请检查配置或网络连接，返回数据不是JSON格式");
                }
                JSONObject jsonObject = JSONUtil.parseObj(post);
                if (jsonObject.getInt("code") != 0) {
                    throw new RuntimeException("调用向量计算接口失败，错误信息：" + jsonObject.getStr("message") + "请求地址：" + url);
                }
                List<JSONArray> dataArrays = jsonObject.getBeanList("data", JSONArray.class);

                // 将每个向量添加到结果中
                for (JSONArray vector : dataArrays) {
                    List<Double> embedding = vector.toList(Double.class);
                    embedding_result.add(embedding);
                }
            }
        } catch (Exception e) {
            log.error("Error in get_embedding", e);
            throw new RuntimeException("调用向量计算接口失败，请检查配置或网络连接");
        }
        return embedding_result;
    }

    private String filter_order(String content) {
        String[] split_type = {"1", "11"};
        String[] split_str = {"、", "章"};
        IntStream.range(0, split_type.length).forEach(i -> {
            String type = split_type[i];
            String str = split_str[i];
            if (is_first(content.trim()).equals(type)) {

            }
        });
        return content;
    }

    private boolean filter_policy(String content) {
        Double rate;
        String url = serverBasePathConfig.getAiMiddleHostPort() + "/openApi/algorithm/online/policyScore";
//        String url = "http://" + PolicyConfig.host_port + "/query_policy";

        JSONObject entries = JSONUtil.createObj().putOpt("contents", List.of(content.replaceAll(SPLIT_PAGE_FLAG, "")));
        try {
            String response = HttpUtil.post(url, JSONUtil.toJsonStr(entries), 10000);
            if (!JSONUtil.isTypeJSON(response)) {
                throw new RuntimeException("调用政策过滤接口失败，返回数据不是JSON格式");
            }
            JSONObject jsonObject = JSONUtil.parseObj(response);
            if (jsonObject.getInt("code") != 0) {
                throw new RuntimeException("调用政策过滤接口失败，错误信息：" + jsonObject.getStr("message"));
            }
            rate = jsonObject.getJSONArray("data").getDouble(0);
        } catch (Exception e) {
            log.error("Error in filter_policy", e);
            throw new RuntimeException("调用政策过滤接口失败，请检查配置或网络连接");
        }

        return !(rate > 0.5);
    }

    private Boolean filter_content(String content, int filter_length) {
        filter_length = filter_length == 0 ? 10 : filter_length;
        String trim = content.trim();
        if (StringUtils.isNotEmpty(is_first(trim)) && !trim.contains("。")) return true;
        if (StringUtils.isNotEmpty(is_second(trim)) && !trim.contains("。")) return true;
        return trim.length() < filter_length;
    }

    private String clear_text(String text) {
        int idx = 0;
        List<String[]> replacements = new ArrayList<>();
        while (true) {
            try {
                int start = text.indexOf("（", idx);
                int end = text.indexOf("）", idx) + 1;
                if (start == -1 || end == 0) { // 未找到括号
                    break;
                }
                String s1 = text.substring(start, end);
                if (end != text.length()) {
                    idx = end;
                    continue;
                }
                if (s1.endsWith("负责）") ||
                        s1.endsWith("负" + SPLIT_PAGE_FLAG + "责）") ||
                        s1.endsWith("负责" + SPLIT_PAGE_FLAG + "）")) {
                    replacements.add(new String[]{s1, ""});
                }
                idx = end;
            } catch (Exception e) {
                break;
            }
        }
        for (String[] replacement : replacements) {
            text = text.replace(replacement[0], replacement[1]);
        }
        return text;

    }


    private String is_first(String text) {
        Set<String> first_level = generate_levels("{0}、");
        Optional<String> is_first = first_level.stream().filter(text::startsWith).findAny();
        if (is_first.isPresent()) return "1";
        Set<String> second_level = generate_levels("第{0}章");
        Optional<String> is_second = second_level.stream().filter(text::startsWith).findAny();
        if (is_second.isPresent()) return "11";
        return "";
    }

    private String is_second(String text) {
        Set<String> first_level = generate_levels("（{0}）");
        Optional<String> is_first = first_level.stream().filter(text::startsWith).findAny();
        if (is_first.isPresent()) return "2";
        Set<String> second_level = generate_levels("({0})");
        Optional<String> is_second = second_level.stream().filter(text::startsWith).findAny();
        if (is_second.isPresent()) return "22";
        return "";
    }

    private Set<String> generate_levels(String fmt) {
        if (StringUtils.isEmpty(fmt)) fmt = "{0}、";
        Set<String> res = new HashSet<>();
        for (int i = 1; i <= 99; i++) {
            res.add(MessageFormat.format(fmt, convertToChinese(i)));
        }
        return res;
    }


    private List<MergeBlock> merge_passages(List<ParagraphPo.Paragraph> doc) {
        List<MergeBlock> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(doc)) return result;
        String cur_con = null;
        int pageIndex = 0;
        int index = 0;
        ArrayList<Rbound> blocks = new ArrayList<>();
        for (ParagraphPo.Paragraph paragraph : doc) {
            Rbound r_bound = new Rbound();
            // 复制基本属性
            BeanUtil.copyProperties(paragraph, r_bound);

            // 设置特殊字段
            r_bound.setPassageIndex(paragraph.getIndex());
            r_bound.setIndex(paragraph.getPageIndex());

            // 处理rect和bound
            if (paragraph.getRect() != null) {
                r_bound.setRect(paragraph.getRect());
                Double[] bound = new Double[]{
                        paragraph.getRect().getLeft(),
                        paragraph.getRect().getTop(),
                        paragraph.getRect().getRight() - paragraph.getRect().getLeft(),
                        paragraph.getRect().getBottom() - paragraph.getRect().getTop()
                };
                r_bound.setBound(bound);
            }

            // 处理text，移除空格
            String text = paragraph.getText();
            if (text != null) {
                text = text.replace(" ", "");
                r_bound.setText(text);
            }

            if (StringUtils.isEmpty(cur_con)) {
                pageIndex = paragraph.getPageIndex();
                index = paragraph.getIndex();
                cur_con = text;
                blocks.add(r_bound);
            } else if (index == paragraph.getIndex() && pageIndex != paragraph.getPageIndex()) {
                cur_con += SPLIT_PAGE_FLAG + text;
                blocks.add(r_bound);
            } else {
                result.add(new MergeBlock(cur_con, blocks));
                pageIndex = paragraph.getPageIndex();
                index = paragraph.getIndex();
                cur_con = text;
                blocks = new ArrayList<>(Collections.singleton(r_bound));
            }
        }
        if (cur_con != null) {
            result.add(new MergeBlock(cur_con, blocks));
        }
        return result;
    }

    private List<String> merge_passages_1(List<ParagraphPo.Paragraph> doc) {
        List<String> contents = new ArrayList<>();
        if (CollectionUtils.isEmpty(doc)) {
            return contents;
        }

        int pageIndex = doc.get(0).getPageIndex();
        int index = doc.get(0).getIndex();
        String curCon = doc.get(0).getText();

        for (int i = 1; i < doc.size(); i++) {
            ParagraphPo.Paragraph d = doc.get(i);
            if (index == d.getIndex() && pageIndex != d.getPageIndex()) {
                curCon += d.getText();
            } else {
                contents.add(curCon);
                pageIndex = d.getPageIndex();
                index = d.getIndex();
                curCon = d.getText();
            }
        }
        contents.add(curCon);
        return contents;
    }

    private static String convertToChinese(int num) {
        if (num < 1 || num > 99) {
            return "";
        }
        if (num < 10) {
            return chinese_number_DIGITS[num];
        }
        if (num == 10) {
            return "十";
        }
        if (num < 20) {
            return "十" + chinese_number_DIGITS[num % 10];
        }
        if (num % 10 == 0) {
            return chinese_number_TENS[num / 10];
        }
        return chinese_number_TENS[num / 10] + chinese_number_DIGITS[num % 10];
    }

    @Data
    static class MergeBlock {
        String text;
        List<Rbound> blocks;

        public MergeBlock(String text, List<Rbound> blocks) {
            this.text = text;
            this.blocks = blocks;
        }
    }

    @Data
    static
    class Rbound extends ParagraphPo.Paragraph {
        //left top right bottom
        private Double[] bound = new Double[4];
        private int passageIndex;
        private int[] highlightRange = new int[2];
    }

    @Data
    static
    class Sentence {
        List<Rbound> sentenceBlocks;
        List<Rbound> passageBlocks;
        List<JSONObject> repeatPassages;
        String sentence;
        int sentenceIndex;
        int totalPassgage;
        int sentenceLength;
        List<JSONObject> repeatSentences;
        int passageIndex;
        int passageLength;
        String repeatType;
        String passage;
        int passageRepeatLength;
        double passageRepeatRate;
        Boolean isPolicy;
        double maxRepeatRate;

        public Sentence() {

        }

        public Sentence(List<Rbound> sentenceBlocks, String sentence) {
            this.sentenceBlocks = sentenceBlocks;
            this.sentence = sentence;
        }
    }

//    static class Block {
//        Double[] bound;
//        ParagraphPo.Paragraph.Rect rect;
//        int passageIndex;
//        int index;
//
//        public Block(Double[] bound, ParagraphPo.Paragraph.Rect rect, int passageIndex, int index) {
//            this.bound = bound;
//            this.rect = rect;
//            this.passageIndex = passageIndex;
//            this.index = index;
//        }
//
//        public Block() {
//        }
//    }

    /**
     * 获取文档信息
     *
     * @param docId        文档ID
     * @param fieldMapping 字段映射
     * @param indexName    索引名称
     * @return 文档信息
     */
    private Map<String, Object> getDocInfo(String docId, Map<String, String> fieldMapping, String indexName) {
        try {
            // 构建查询条件
            String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
            // 创建URL和请求体
            String esUrl = String.format("http://%s/%s/_search", nlpConfig.getEsUrl().get(0), indexName);
            JSONObject body = JSONUtil.createObj();
            body.putByPath("query.bool.must[0].term." + docId_field, docId);
            body.putOpt("size", 5);
            body.putByPath("_source.excludes", List.of(fieldMapping.getOrDefault("embedding", "embedding"), "LOCATION", "ORGANIZATION", "DATE", "PERSON", "keywords", "DURATION", "实体机构", "实体地点", "实体日期", "关键词", "实体人物", "summary", fieldMapping.getOrDefault("splitId", "splitId"), fieldMapping.getOrDefault("rectList", "rectangularBlock"), "ORDINAL"));

            // 执行HTTP请求
            String response;
            if (StringUtils.isNotEmpty(nlpConfig.getEsUserName()) && StringUtils.isNotEmpty(nlpConfig.getEsPassword())) {
                try (HttpResponse execute = HttpUtil.createPost(esUrl).header(
                        "Authorization", "Basic " + Base64.getEncoder().encodeToString((nlpConfig.getEsUserName() + ":" + nlpConfig.getEsPassword()).getBytes(StandardCharsets.UTF_8))
                ).body(JSONUtil.toJsonStr(body)).execute()) {
                    response = execute.body();
                }
            } else {
                response = HttpUtil.post(esUrl, JSONUtil.toJsonStr(body));
            }

            // 处理结果
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            JSONArray hits = jsonResponse.getByPath("hits.hits", JSONArray.class);

            if (hits != null && !hits.isEmpty()) {
                JSONObject hit = hits.getJSONObject(0);
                JSONObject source = hit.getJSONObject("_source");
                return source.toBean(Map.class);
            }

            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("获取文档信息失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 从句子和段落中获取重复文档
     *
     * @param repeatData          重复数据
     * @param fieldMapping        字段映射
     * @param minRepeatRate       最小重复率
     * @param requiredTargetDocid 目标文档ID
     * @param policyPassages      策略段落
     * @param ignoredSentences    忽略的句子
     * @param checkFileInfo       检查文件信息
     * @return 重复文档列表
     */
    private List<JSONObject> getRepeatDocsFromSentenceAndPassage(List<Sentence> repeatData,
                                                                 Map<String, String> fieldMapping,
                                                                 Float minRepeatRate,
                                                                 Long requiredTargetDocid,
                                                                 List<JSONObject> policyPassages,
                                                                 List<JSONObject> ignoredSentences,
                                                                 List<Req.CheckFileInfo> checkFileInfo) {
        String contentField = "text";
        String nameField = fieldMapping.getOrDefault("name", "name");
        String titleField = fieldMapping.getOrDefault("title", "title");
        String splitIdField = fieldMapping.getOrDefault("splitId", "splitId");
        String printedTimeField = fieldMapping.getOrDefault("printedTime", "printedTime");
        String digestField = fieldMapping.getOrDefault("digest", "digest");
        String issueOrgField = fieldMapping.getOrDefault("issueOrg", "issueOrg");
        String categoriesField = fieldMapping.getOrDefault("categories", "categories");
        String repoIdField = fieldMapping.getOrDefault("repoId", "repoId");
        String docIdField = fieldMapping.getOrDefault("primaryKeyId", "recordId");
        String rectangularBlockField = fieldMapping.getOrDefault("rectList", "rectangularBlock");
        String embeddingField = fieldMapping.getOrDefault("embedding", "embedding");
        String passageIdsField = fieldMapping.getOrDefault("passageIds", "passageIds");

        if (requiredTargetDocid != null && String.valueOf(requiredTargetDocid).matches("\\d+")) {
            requiredTargetDocid = Long.parseLong(String.valueOf(requiredTargetDocid));
        }

        Map<String, JSONObject> repeatResult = new HashMap<>();
        boolean rmDuplicateParagraph = true;
        int countSourceSentence = 0;

        // 处理policyPassages
        Map<Integer, Boolean> policyPassagesMap = null;
        if (policyPassages != null) {
            policyPassagesMap = new HashMap<>();
            for (JSONObject p : policyPassages) {
                policyPassagesMap.put(p.getInt("passageIndex"), p.getBool("isPolicy"));
            }
        }

        // 处理ignoredSentences
        Set<List<Object>> ignoreData = new HashSet<>();
        if (ignoredSentences != null) {
            for (JSONObject sentence : ignoredSentences) {
                ignoreData.add(Arrays.asList(
                        sentence.get("sentenceIndex"),
                        sentence.get(docIdField),
                        sentence.get(contentField)
                ));
            }
        }

        List<JSONObject> allRepeatSentences = new ArrayList<>();
        int totalSourceSentence = 0;

        for (Sentence r : repeatData) {
            if (r.getRepeatType() == null) {
                continue;
            }
            if (!"sentence".equals(r.getRepeatType()) && !"passages".equals(r.getRepeatType())) {
                continue;
            }

            if (policyPassagesMap != null) {
                int passageId = r.getPassageIndex();
                if (!policyPassagesMap.getOrDefault(passageId, r.getIsPolicy() != null ? r.getIsPolicy() : true)) {
                    continue;
                }
            } else if (r.getIsPolicy() != null && !r.getIsPolicy()) {
                continue;
            }

            if ("sentence".equals(r.getRepeatType())) {
                int sentenceIndex = r.getSentenceIndex();
                List<Rbound> sourceSentenceBlocks = r.getSentenceBlocks();
                String sourceSentence = r.getSentence();
                totalSourceSentence++;

                for (JSONObject targetSentence : r.getRepeatSentences()) {
                    String targetDocid = targetSentence.getStr(docIdField);
                    String targetText = targetSentence.getStr(contentField);

                    List<Object> ignoreKey = Arrays.asList(sentenceIndex, targetDocid, targetText);
                    if (ignoreData.contains(ignoreKey)) {
                        continue;
                    }

                    if (minRepeatRate != null && targetSentence.getDouble("repeatRate") < minRepeatRate) {
                        continue;
                    }

                    if (requiredTargetDocid == null || targetDocid.equals(String.valueOf(requiredTargetDocid))) {
                        if (!repeatResult.containsKey(targetDocid)) {
                            JSONObject docResult = new JSONObject();
                            for (String key : targetSentence.keySet()) {
                                if (!key.equals("repeatRate") && !key.equals("repeatLength") &&
                                        !key.equals("numOfRepeatPassage") && !key.equals("targetHighlight") &&
                                        !key.equals("queryHighlight") && !key.equals("targetHtml") &&
                                        !key.equals("text") && !key.equals(splitIdField) &&
                                        !key.equals(rectangularBlockField) && !key.equals(embeddingField) &&
                                        !key.equals(passageIdsField)) {
                                    docResult.putOpt(key, targetSentence.get(key));
                                }
                            }
                            docResult.putOpt("repeatSentencesIds", new ArrayList<>());
                            repeatResult.put(targetDocid, docResult);
                        }
                        repeatResult.get(targetDocid).getJSONArray("repeatSentencesIds").add(sentenceIndex);
                    }

                    if (targetDocid.equals(String.valueOf(requiredTargetDocid))) {
                        try {
                            if (!targetSentence.containsKey("sentenceBlocks")) {
                                log.warn("老版结果数据缺少targetSentenceBlocks");
                                continue;
                            }

                            List<JSONObject> targetSentenceBlocks = targetSentence.getJSONArray("sentenceBlocks").toList(JSONObject.class);

                            List<List<Integer>> queryHighlight = split_queryHighlight(sourceSentenceBlocks, targetSentence.getBeanList("queryHighlight", Integer.class));
                            List<Integer> targetHighlight = targetSentence.getBeanList("targetHighlight", Integer.class);

                            List<JSONObject> _sourceSentenceBlocks = new ArrayList<>();
                            int minSize = Math.min(sourceSentenceBlocks.size(), queryHighlight.size());
                            for (int i = 0; i < minSize; i++) {
                                Rbound block = sourceSentenceBlocks.get(i);
                                List<Integer> _queryHighlight = queryHighlight.get(i);
                                JSONObject newBlock = new JSONObject();
                                newBlock.putOpt("paragraphIndex", block.getPassageIndex());
                                newBlock.putOpt("pageIndex", block.getIndex());
                                newBlock.putOpt("bound", block.getBound());

                                JSONArray rects = new JSONArray();
                                JSONObject rect = new JSONObject();
                                rect.putOpt("fonts", IntStream.range(
                                                block.getHighlightRange()[0],
                                                block.getHighlightRange()[1])
                                        .boxed().collect(Collectors.toList()));
                                rects.add(rect);
                                newBlock.putOpt("rects", rects);

                                newBlock.putOpt("rectsHighlight", getRectsHighlight(_queryHighlight, block.getHighlightRange()[0]));
                                newBlock.putOpt("rect", block.getRect());
                                newBlock.putOpt("text", block.getText());
                                _sourceSentenceBlocks.add(newBlock);
                            }
                            List<JSONObject> _targetSentenceBlocks = new ArrayList<>();
                            for (JSONObject block : targetSentenceBlocks) {
                                JSONObject newBlock = new JSONObject();
                                newBlock.putOpt("paragraphIndex", block.getInt("passageIndex"));
                                newBlock.putOpt("pageIndex", block.getInt("index"));
                                newBlock.putOpt("bound", block.get("bound"));

                                if (block.containsKey("highlightRange")) {
                                    JSONArray jsonArray = block.getJSONArray("highlightRange");
                                    JSONArray rects = new JSONArray();
                                    JSONObject rect = new JSONObject();
                                    rect.putOpt("fonts", IntStream.range(
                                            jsonArray.get(0, Integer.class),
                                            jsonArray.get(1, Integer.class)
                                    ).boxed().collect(Collectors.toList()));
                                    rects.add(rect);
                                    newBlock.putOpt("rects", rects);
                                    newBlock.putOpt("rectsHighlight", getRectsHighlight(targetHighlight, jsonArray.get(0, Integer.class)));
                                } else {
                                    newBlock.putOpt("rects", new JSONArray());
                                    newBlock.putOpt("rectsHighlight", new JSONArray());
                                }

                                newBlock.putOpt("rect", block.get("rect"));
                                newBlock.putOpt("text", block.getStr("text"));
                                _targetSentenceBlocks.add(newBlock);
                            }

                            double repeatRate = targetSentence.getDouble("repeatRate");
                            int repeatLength = targetSentence.getInt("repeatLength");

                            JSONObject repeatSentence = new JSONObject();
                            repeatSentence.putOpt("targetSentenceBlocks", _targetSentenceBlocks);
                            repeatSentence.putOpt("sourceSentenceBlocks", _sourceSentenceBlocks);
                            repeatSentence.putOpt("repeatRate", Math.round(repeatRate * 10000) / 100.0);
                            repeatSentence.putOpt("repeatLength", repeatLength);
                            allRepeatSentences.add(repeatSentence);

                        } catch (Exception e) {
                            log.error("解析查重结果失败", e);
                        }
                    }
                }
            }
        }

        for (Map.Entry<String, JSONObject> entry : repeatResult.entrySet()) {
            String targetDocid = entry.getKey();
            JSONObject targetDoc = entry.getValue();
            int numOfRepeatSentence = new HashSet<>(targetDoc.getJSONArray("repeatSentencesIds").toList(Integer.class)).size();
            targetDoc.remove("repeatSentencesIds");
            double docRepeatRate = totalSourceSentence > 0 ? (double) numOfRepeatSentence / totalSourceSentence : 0;
            targetDoc.putOpt("repeatRate", docRepeatRate);
            List<JSONObject> allRepeatPassages = get_repeat_docs_from_passage(
                    repeatData,
                    fieldMapping,
                    minRepeatRate,
                    Convert.toLong(targetDocid),
                    policyPassages,
                    checkFileInfo
            );
            Integer integer = 0;
            if(CollectionUtils.isNotEmpty(allRepeatPassages)){
                JSONObject entries = allRepeatPassages.get(0);
                integer = entries.getInt("count", 0);
            }
            targetDoc.putOpt("numOfRepeatPassage", integer);
            targetDoc.putOpt("numOfRepeatSentence", integer);
        }

        if (requiredTargetDocid == null) {
            boolean hasCheck = checkFileInfo != null && !checkFileInfo.isEmpty();
            if (!hasCheck) log.info("checkFileInfo is empty, not checking file info");
            if (hasCheck) {
                for (Req.CheckFileInfo doc : checkFileInfo) {
                    String recordId = doc.getRecordId();
                    if (!repeatResult.containsKey(recordId)) {
                        JSONObject entries = JSONUtil.parseObj(doc);
                        entries.remove("properties");
                        doc.getProperties().forEach(entries::putOpt);
                        if (DELETED.equals(doc.getDocStatus()) || NO_PERMISSION.equals(doc.getDocStatus())) {
                            repeatResult.put(recordId, entries);
                        } else {
                            entries.putOpt("repeatRate", 0.0);
                            entries.putOpt("numOfRepeatPassage", 0);
                            entries.putOpt("repeatLength", 0);
                            repeatResult.put(recordId, entries);
                        }
                    } else {
                        repeatResult.get(recordId).putOpt("docStatus", doc.getDocStatus());
                    }
                }
            }

            List<JSONObject> result = repeatResult.values().stream()
                    .filter(v -> hasCheck || v.getDouble("repeatRate") > 0.2)
                    .sorted((a, b) -> {
                        int statusCompare = Double.compare(
                                b.getDouble("docStatus", 0.0),
                                a.getDouble("docStatus", 0.0)
                        );
                        if (statusCompare != 0) {
                            return statusCompare;
                        }
                        return Double.compare(
                                b.getDouble("repeatRate", 0.0),
                                a.getDouble("repeatRate", 0.0)
                        );
                    })
                    .collect(Collectors.toList());

            return result;
        } else
        {
            double docRepeatRate = repeatResult.getOrDefault(String.valueOf(requiredTargetDocid), new JSONObject())
                    .getDouble("repeatRate", 0.0);
            List<JSONObject> allRepeatPassages = get_repeat_docs_from_passage(
                    repeatData,
                    fieldMapping,
                    minRepeatRate,
                    requiredTargetDocid,
                    policyPassages,
                    checkFileInfo
            );

            JSONObject result = new JSONObject();
            result.putOpt("allRepeatSentences", allRepeatSentences);
            result.putOpt("allRepeatPassages", allRepeatPassages);
            result.putOpt("docRepeatRate", Math.round(docRepeatRate * 10000) / 100.0);
            return Collections.singletonList(result);
        }
    }

    private List<List<Integer>> split_queryHighlight(List<Rbound> sourceSentenceBlocks, List<Integer> queryHighlight) {
        List<Integer> list = new ArrayList<>(queryHighlight);
        List<List<Integer>> result = new ArrayList<>();
        for (Rbound sourceSentenceBlock : sourceSentenceBlocks) {
            int length = sourceSentenceBlock.getHighlightRange()[1] - sourceSentenceBlock.getHighlightRange()[0] + 1;
            List<Integer> list1 = new ArrayList<>(list.subList(0, length));
            result.add(list1);
            list = list.subList(length, list.size());
        }
        return result;
    }


    private List<Map<String, List<Integer>>> getRectsHighlight(List<Integer> highlight, int offset) {
        List<Map<String, List<Integer>>> result = new ArrayList<>();
        int previous = 0;

        for (int i = 0; i < highlight.size(); i++) {
            int h = highlight.get(i);
            if (h == 1 && previous == 0) {
                Map<String, List<Integer>> rect = new HashMap<>();
                rect.put("fonts", new ArrayList<>(Collections.singletonList(i + offset)));
                result.add(rect);
            } else if (h == 1 && previous == 1) {
                result.get(result.size() - 1).get("fonts").add(i + offset);
            }
            previous = h;
        }

        return result;
    }


//private List<JSONObject> get_repeat_docs_from_passage(List<Sentence> repeat_data, Map<String, String> fieldMapping, float min_passage_repeat_rate, int required_target_docid, List<JSONObject> policy_passages, List<JSONObject> checkFileInfo) {
//    String content_field = "text";
//    String name_field = fieldMapping.getOrDefault("name", "name");
//    String title_field = fieldMapping.getOrDefault("title", "title");
//    String splitId_field = fieldMapping.getOrDefault("splitId", "splitId");
//    String printedTime_field = fieldMapping.getOrDefault("printedTime", "printedTime");
//    String digest_field = fieldMapping.getOrDefault("digest", "digest");
//    String issueOrg_field = fieldMapping.getOrDefault("issueOrg", "issueOrg");
//    String categories_field = fieldMapping.getOrDefault("categories", "categories");
//    String repoId_field = fieldMapping.getOrDefault("repoId", "repoId");
//    String docId_field = fieldMapping.getOrDefault("primaryKeyId", "recordId");
//    String rectangularBlock_field = fieldMapping.getOrDefault("rectList", "rectangularBlock");
//    String embedding_field = fieldMapping.getOrDefault("embedding", "embedding");
//    String passageIds_field = fieldMapping.getOrDefault("passageIds", "passageIds");
//
//    if (required_target_docid != 0 && String.valueOf(required_target_docid).matches("\\d+")) {
//        required_target_docid = Integer.parseInt(String.valueOf(required_target_docid));
//    }
//    double docRepeatRate;
//
//    JSONObject source_doc = JSONUtil.createObj();
//    Map<String, Map<String, JSONObject>> repeat_docs = new HashMap<>();
//    Map<String, JSONObject> repeat_result = new HashMap<>();
//    boolean rm_duplicate_paragraph = true;
//
//    // 处理policy_passages
//    Map<Integer, Boolean> policyPassagesMap = null;
//    if (policy_passages != null) {
//        policyPassagesMap = new HashMap<>();
//        for (JSONObject p : policy_passages) {
//            policyPassagesMap.put(p.getInt("passageIndex"), p.getBool("isPolicy"));
//        }
//    }
//
//    // 构建source_doc
//    for (Sentence sourcePassage : repeat_data) {
//        if (!"passage".equals(sourcePassage.getRepeatType())) {
//            continue;
//        }
//        if (policyPassagesMap != null) {
//            int passageId = sourcePassage.getPassageIndex();
//            if (!policyPassagesMap.getOrDefault(passageId, sourcePassage.getIsPolicy() != null ? sourcePassage.getIsPolicy() : true)) {
//                continue;
//            }
//        } else if (sourcePassage.getIsPolicy() != null && !sourcePassage.getIsPolicy()) {
//            continue;
//        }
//
//        int sourcePassageId = sourcePassage.getPassageIndex();
//        JSONObject passageInfo = JSONUtil.createObj()
//                .putOpt("passageIndex", sourcePassageId)
//                .putOpt("passageLength", sourcePassage.getPassageLength())
//                .putOpt("passage", sourcePassage.getPassage())
//                .putOpt("passageRepeatLength", 0)
//                .putOpt("passageRepeatRate", 0)
//                .putOpt("passageBlocks", sourcePassage.getPassageBlocks())
//                .putOpt("repeatPassages", new ArrayList<>());
//        source_doc.putOpt(String.valueOf(sourcePassageId), passageInfo);
//    }
//
//    // 处理重复数据
//    for (Sentence sourcePassage : repeat_data) {
//        if (!"passage".equals(sourcePassage.getRepeatType())) {
//            continue;
//        }
//        if (policyPassagesMap != null) {
//            int passageId = sourcePassage.getPassageIndex();
//            if (!policyPassagesMap.getOrDefault(passageId, sourcePassage.getIsPolicy() != null ? sourcePassage.getIsPolicy() : true)) {
//                continue;
//            }
//        } else if (sourcePassage.getIsPolicy() != null && !sourcePassage.getIsPolicy()) {
//            continue;
//        }
//
//        int sourcePassageId = sourcePassage.getPassageIndex();
//        for (JSONObject targetPassage : sourcePassage.getRepeatPassages()) {
//            if (min_passage_repeat_rate > 0 && targetPassage.getDouble("repeatRate") < min_passage_repeat_rate) {
//                continue;
//            }
//
//            String targetDocid = targetPassage.getStr(docId_field);
//            if (required_target_docid != 0 && !String.valueOf(required_target_docid).equals(targetDocid)) {
//                continue;
//            }
//
//            repeat_docs.computeIfAbsent(targetDocid, k -> new HashMap<>());
//            if (!repeat_docs.get(targetDocid).containsKey(String.valueOf(sourcePassageId))) {
//                repeat_docs.get(targetDocid).put(String.valueOf(sourcePassageId), JSONUtil.parseObj(source_doc.getJSONObject(String.valueOf(sourcePassageId)).toString()));
//            }
//            repeat_docs.get(targetDocid).get(String.valueOf(sourcePassageId)).getJSONArray("repeatPassages").add(targetPassage);
//        }
//    }
//
//    // 计算源文档总长度
//    int sourceDocLength = 0;
//    for (String sourcePassageId : source_doc.keySet()) {
//        sourceDocLength += source_doc.getJSONObject(sourcePassageId).getInt("passageLength");
//    }
//
//    // 如果未指定目标文档ID
//    if (required_target_docid == 0) {
//        for (Map.Entry<String, Map<String, JSONObject>> entry : repeat_docs.entrySet()) {
//            String targetDocid = entry.getKey();
//            Map<String, JSONObject> repeatInfo = entry.getValue();
//            JSONObject docResult = new JSONObject();
//            int docRepeatLength = 0;
//            int numOfRepeatPassage = 0;
//
//            for (Map.Entry<String, JSONObject> infoEntry : repeatInfo.entrySet()) {
//                JSONObject sourcePassageInfo = infoEntry.getValue();
//                if (!sourcePassageInfo.getJSONArray("repeatPassages").isEmpty()) {
//                    double[] microRepeatRate = get_micro_repeat_rate(sourcePassageInfo.getJSONArray("repeatPassages").toList(JSONObject.class), sourcePassageInfo.getInt("passageLength"));
//                    docRepeatLength += (int) microRepeatRate[0];
//
//                    for (JSONObject targetPassage : sourcePassageInfo.getJSONArray("repeatPassages").toList(JSONObject.class)) {
//                        numOfRepeatPassage++;
//                        for (String key : targetPassage.keySet()) {
//                            if (!key.equals("repeatRate") && !key.equals("repeatLength") && !key.equals("numOfRepeatPassage")
//                                && !key.equals("targetHighlight") && !key.equals("queryHighlight") && !key.equals("targetHtml")
//                                && !key.equals("text") && !key.equals(splitId_field) && !key.equals(rectangularBlock_field)
//                                && !key.equals(embedding_field) && !key.equals(passageIds_field)) {
//                                docResult.putOpt(key, targetPassage.get(key));
//                            }
//                        }
//                    }
//                }
//            }
//
//            docRepeatRate = (double) docRepeatLength / sourceDocLength;
//            docResult.putOpt("repeatRate", docRepeatRate);
//            docResult.putOpt("numOfRepeatPassage", numOfRepeatPassage);
//            docResult.putOpt("repeatLength", docRepeatLength);
//            repeat_result.put(targetDocid, docResult);
//        }
//
//        // 处理checkFileInfo
//        if (checkFileInfo != null && !checkFileInfo.isEmpty()) {
//            for (JSONObject doc : checkFileInfo) {
//                String recordId = doc.getStr("recordId");
//                if (!repeat_result.containsKey(recordId)) {
//                    if (DELETED.equals(doc.getStr("docStatus")) || NO_PERMISSION.equals(doc.getStr("docStatus"))) {
//                        repeat_result.put(recordId, doc);
//                    } else {
//                        doc.putOpt("repeatRate", 0);
//                        doc.putOpt("numOfRepeatPassage", 0);
//                        doc.putOpt("repeatLength", 0);
//                        repeat_result.put(recordId, doc);
//                    }
//                } else {
//                    repeat_result.get(recordId).putOpt("docStatus", doc.getStr("docStatus"));
//                }
//            }
//        }
//
//        return repeat_result.values().stream()
//                .filter(v -> checkFileInfo == null || checkFileInfo.isEmpty() || v.getDouble("repeatRate") > 0.2)
//                .sorted((a, b) -> {
//                    int statusCompare = Double.compare(
//                            b.getDouble("docStatus", 0.0),
//                            a.getDouble("docStatus", 0.0)
//                    );
//                    if (statusCompare != 0) {
//                        return statusCompare;
//                    }
//                    return Double.compare(
//                            b.getDouble("repeatRate", 0.0),
//                            a.getDouble("repeatRate", 0.0)
//                    );
//                })
//                .collect(Collectors.toList());
//    }
//    // 如果指定了目标文档ID且存在
//    else if (repeat_docs.containsKey(String.valueOf(required_target_docid))) {
//        Map<String, JSONObject> repeatInfo = repeat_docs.get(String.valueOf(required_target_docid));
//        Set<String> source_passage_id_set = new HashSet<>(), target_passage_id_set = new HashSet<>();
//        List<String> source_paragraph_ids = new ArrayList<>(), target_paragraph_ids = new ArrayList<>()
//                , passage_repeat_rates = new ArrayList<>();
//        List<JSONObject> source_paragraphs = new ArrayList<>();
//        List<JSONArray> multi_pages_paragraphs = new ArrayList<>();
//        List<JSONObject> target_paragraphs = new ArrayList<>();
//        int doc_repeat_length = 0, num_of_repeat_passage = 0, repeat_passage_count =0;
//        List<JSONObject> sourceParagraphs = new ArrayList<>();
//        List<Integer> sourceParagraphIds = new ArrayList<>();
//        for (Map.Entry<String, JSONObject> entry : repeatInfo.entrySet()) {
//            String source_passage_id = entry.getKey();
//            JSONObject source_passage_info = entry.getValue();
//            if (source_passage_info.containsKey("repeatPassages")){
//                double[] microRepeatRate = get_micro_repeat_rate(source_passage_info.getJSONArray("repeatPassages").toList(JSONObject.class), source_passage_info.getInt("passageLength"));
//                doc_repeat_length+=Convert.toInt(microRepeatRate[0]);
//                for (JSONObject target_passage : source_passage_info.getBeanList("repeatPassages",JSONObject.class)) {
//                    num_of_repeat_passage += 1;
//                    source_paragraph_ids.add(source_passage_id);
//                    target_paragraph_ids.add(target_passage.getStr("'passageIndex'"));
//                    passage_repeat_rates.add((target_passage.getDouble("'repeatRate'")*100)+"");
//                    if (!target_passage_id_set.contains(target_passage.getStr("'passageIndex'")) || !rm_duplicate_paragraph) {
//                        target_passage_id_set.add(target_passage.getStr("'passageIndex'"));
//                        JSONArray array1 = target_passage.getJSONArray(rectangularBlock_field);
//                        if (array1.size() > 1) {
//                            JSONArray array = JSONUtil.createArray();
//                            target_passage.getBeanList(rectangularBlock_field, JSONObject.class).forEach(o -> {
//                                JSONObject jsonObject = JSONUtil.createObj();
//                                jsonObject.putOpt("index", target_passage.get("passageIndex"))
//                                        .putOpt("pageIndex", o.get("pageIndex"));
//                                array.add(jsonObject);
//                            });
//                            multi_pages_paragraphs.add(array);
//                        }
//                        for (Object o : array1) {
//                            JSONObject block = JSONUtil.parseObj(o);
//                            JSONObject entries = JSONUtil.createObj().putOpt("index", target_passage.get("passageIndex"))
//                                    .putOpt("pageIndex", block.get("pageIndex")).putOpt("text", target_passage.get("text")).putOpt("rect", block.get("rect"));
//                            target_paragraphs.add(entries);
//                        }
//                    }
//                    if (! source_passage_id_set.contains(source_passage_id) || !rm_duplicate_paragraph) {
//                        source_passage_id_set.add(source_passage_id);
//                        for (Object block : source_doc.getByPath(source_passage_id + ".passageBlocks", JSONArray.class)) {
//                            JSONObject entries = JSONUtil.parseObj(block);
//                            source_paragraphs.add(JSONUtil.createObj().putOpt("index",entries.get("passageIndex")).putOpt("pageIndex",entries.get("index")).putOpt("text", entries.get("text")).putOpt("rect", entries.get("rect")));
//                        }
//                    }
//
//                }
//
//            }
//        }
//        docRepeatRate = (double) doc_repeat_length / sourceDocLength;
//
//
//
//
//
//
//    }
//}
}
