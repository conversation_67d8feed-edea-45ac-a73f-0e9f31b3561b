package com.suwell.plss.search.service.impl;

import static com.suwell.plss.framework.es.enums.EsIndexEnum.INDEX_NLP_RECORD;

import cn.hutool.core.collection.CollUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.datapermission.enums.VisitUserType;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.framework.es.service.ElasticsearchService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.service.DocumentRpcService;
import com.suwell.plss.record.service.PermissionRpcService;
import com.suwell.plss.record.service.RecordRpcService;
import com.suwell.plss.record.service.RepositoryRpcService;
import com.suwell.plss.record.standard.dto.request.PackageSearchResultReq;
import com.suwell.plss.record.standard.dto.request.RepositoryQueryReq;
import com.suwell.plss.record.standard.dto.request.UserPermConditionReq;
import com.suwell.plss.record.standard.dto.response.DocumentFileResp;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.dto.response.PackageSearchResultResp;
import com.suwell.plss.record.standard.dto.response.PermissionOperateResp;
import com.suwell.plss.record.standard.dto.response.RecordInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.dto.response.UserPermissionResp;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.record.standard.enums.RepositoryEnum;
import com.suwell.plss.search.config.RecordOriginConfig;
import com.suwell.plss.search.config.RecordSearchConfig;
import com.suwell.plss.search.entity.DateMetadataAgg;
import com.suwell.plss.search.entity.DateRange;
import com.suwell.plss.search.entity.dto.NlpRecordDto;
import com.suwell.plss.search.entity.search.CategorySearchItem;
import com.suwell.plss.search.entity.search.EsSearchItem;
import com.suwell.plss.search.entity.search.ExistSearchItem;
import com.suwell.plss.search.entity.search.MustNotSearchItem;
import com.suwell.plss.search.entity.search.NotExistSearchItem;
import com.suwell.plss.search.entity.search.RangeSearchItem;
import com.suwell.plss.search.entity.search.RecordDataSourceSearchItem;
import com.suwell.plss.search.entity.search.RepoFolderSearchItem;
import com.suwell.plss.search.entity.search.SearchTreeNode;
import com.suwell.plss.search.entity.search.TermsSearchItem;
import com.suwell.plss.search.facade.TimeInfoHolder;
import com.suwell.plss.search.service.MetadataSearchService;
import com.suwell.plss.search.service.SearchRecordService;
import com.suwell.plss.search.standard.dto.request.newsSearch.CategoryListQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.KeywordQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.MetadataListQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.RecordDataSourceQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchRecordV2QueryReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchSortOption;
import com.suwell.plss.search.standard.dto.response.NlpRecordResp;
import com.suwell.plss.search.standard.dto.response.SearchAggCountResp;
import com.suwell.plss.search.standard.dto.response.SearchRecordResp;
import com.suwell.plss.search.standard.dto.response.SearchRepoRecordCountResp;
import com.suwell.plss.search.standard.enums.SearchEnum;
import com.suwell.plss.system.api.domain.response.CollectStatusResp;
import com.suwell.plss.system.api.service.ConfigRpcService;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.akali.annotation.AkaliHot;
import org.dromara.akali.enums.FlowGradeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractSearchRecordServiceImpl implements SearchRecordService {

    @Resource
    private PermissionRpcService permissionRpcService;
    @Resource
    protected RecordSearchConfig recordSearchConfig;
    @Resource
    private MetadataSearchService metadataSearchService;
    @Resource
    private RecordRpcService recordRpcService;
    @Resource
    private DocumentRpcService documentRpcService;
    @Resource
    private RecordOriginConfig recordOriginConfig;
    @Resource
    private RepositoryRpcService repositoryRpcService;
    @Resource
    private ConfigRpcService configRpcService;
    @Resource
    private ElasticsearchClient elasticsearchClient;
    @Resource
    private KsoAuthConfig ksoAuthConfig;
    /**
     * 段落分隔符
     */
    private static final String PARAGRAPH_SEPARATOR = "。";

    /**
     * 返回内容的长度
     */
    protected static final Integer CONTENT_LENGTH = 500;

    private static final Pattern PATTERN = Pattern.compile("<maple>(.*?)</maple>");

    /**
     * 高亮前缀
     */
    public static final String LIGHT_PRE_TAGS = "<maple>";
    /**
     * 高亮后缀
     */
    public static final String LIGHT_POST_TAGS = "</maple>";

    /**
     * 普通文件搜索
     * @param req 请求参数
     * @return 搜索结果
     */
    @Override
    public PageUtils<SearchRecordResp> searchPage(SearchRecordV2QueryReq req) {
        //根据要搜索的库优化是否查询文档权限
        List<Long> repoIds = req.getRepoIds();
        if (CollUtil.isNotEmpty(repoIds)) {
            RepositoryQueryReq repositoryQueryReq = new RepositoryQueryReq();
            //判断筛选库中是否包含私有库
            repositoryQueryReq.setRepoIds(repoIds);
            repositoryQueryReq.setNonShareType(RepositoryEnum.ShareType.ANY_SHARE.getCode());
            R<List<RepositoryResp>> listR = repositoryRpcService.listSimpleRepoInfo(repositoryQueryReq);
            if(listR.isSuccess()){
                List<RepositoryResp> repoList = listR.getData();
                if(CollUtil.isEmpty(repoList)){
                    //查询库中都是公开库 减少权限查询 优化性能
                    req.setViewTitleOnlyPerm(false);
                }
            }
        }
        return searchRecordWithWrapperData(req, false);
    }

    /**
     * 安全文件搜索
     *
     * @param req 请求参数
     * @return 搜索结果
     */
    @Override
    public PageUtils<SearchRecordResp> searchSecretPage(SearchRecordV2QueryReq req) {
        //限定库范围是安全文库
        List<Long> conditionRepoIds = getConditionRepoByCType(Collections.singletonList(RepositoryEnum.CType.SECURITY.getCode()));
        if (CollectionUtils.isEmpty(conditionRepoIds)) {
            return new PageUtils<>(Collections.emptyList(), 0, req.getPageSize(), req.getPage());
        }
        //限定搜索范围为安全文库
        req.setConditionRepoIds(conditionRepoIds);
        return searchRecordWithWrapperData(req, true);
    }


    /**
     * 文档搜索
     *
     * @param req 请求参数
     * @param secret 是否安全文件搜索
     * @return 搜索结果
     */
    @AkaliHot(grade = FlowGradeEnum.FLOW_GRADE_QPS, count = 5, duration = 1)
    private PageUtils<SearchRecordResp> searchRecordWithWrapperData(SearchRecordV2QueryReq req, boolean secret) {
        //限制分页
        limitPage(req);
        req.setSecret(secret);
        //搜索
        PageUtils<NlpRecordResp> page;
        if (secret) {
            page = this.searchSecret(req);
        } else {
            page = this.search(req);
        }
        List<NlpRecordResp> nlpRecordList = page.getList();
        //组装数据
        long start = System.currentTimeMillis();
        List<SearchRecordResp> searchRecordRespList = convert2SearchRecordResp(nlpRecordList, req);
        TimeInfoHolder.addTime("封装搜索结果", System.currentTimeMillis() - start);
        return new PageUtils<>(searchRecordRespList, page.getTotalCount(), req.getPageSize(), req.getPage());
    }

    private void limitPage(SearchRecordV2QueryReq req) {
        if (req == null) {
            return;
        }
        Integer pageNum = req.getPage();
        Integer pageSize = req.getPageSize();
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            //限制一次查询数量
            pageSize = 100;
        }
        if (pageNum * pageSize > 10000) {
            //限制查询页数
            pageNum = 10000 / pageSize;
        }
        req.setPage(pageNum);
        req.setPageSize(pageSize);
    }


    private List<SearchRecordResp> convert2SearchRecordResp(List<NlpRecordResp> nlpRecordList,
            SearchRecordV2QueryReq req) {
        String currentUserId = SecurityUtils.getUserId();
        if (CollUtil.isEmpty(nlpRecordList)) {
            return Collections.emptyList();
        }
        Set<Long> repoIds = new HashSet<>();
        List<SearchRecordResp> searchRecordRespList = new ArrayList<>();
        for (NlpRecordResp nlpRecord : nlpRecordList) {
            if (CollUtil.isNotEmpty(nlpRecord.getRepoFolders())) {
                repoIds.addAll(nlpRecord.getRepoFolders().stream().map(NlpRecordResp.RepoFolder::getRepoId)
                        .collect(Collectors.toSet()));
            }
        }
        PackageSearchResultReq packageSearchResultReq = new PackageSearchResultReq();
        List<Long> recordIds = nlpRecordList.stream().map(NlpRecordResp::getRecordId).toList();
        packageSearchResultReq.setCurrentUserId(currentUserId);
        packageSearchResultReq.setRecordIds(recordIds);
        packageSearchResultReq.setRepoIds(new ArrayList<>(repoIds));
        packageSearchResultReq.setViewTitleOnlyPerm(req.isViewTitleOnlyPerm());
        R<PackageSearchResultResp> packageSearchResultRespR = recordRpcService.packageSearchResult(packageSearchResultReq);
        if(packageSearchResultRespR.isError()){
            throw new BizException("封装搜索结果异常:" + packageSearchResultRespR.getMsg());
        }
        PackageSearchResultResp packageSearchResultResp = packageSearchResultRespR.getData();
        Map<Long, RepositoryResp> repositoryMap = packageSearchResultResp.getRepoMap();
        Map<Long, CollectStatusResp> collectStatusMap = packageSearchResultResp.getCollectStatusMap();
        Map<Long, RecordInfoResp> recordInfoMap = packageSearchResultResp.getRecordInfoMap();
        Map<Long, RecordTypeResp> recordTypeMap = packageSearchResultResp.getRecordTypeMap();
        Map<Long, List<MetadataResp>> searchResultMetadataMap = packageSearchResultResp.getSearchResultMetadataMap();
        Map<Long, List<PermissionOperateResp>> permissionOperateMap = packageSearchResultResp.getPermissionOperateMap();
        R<JSONObject> projectConfig = configRpcService.getProjectConfig();
        //是否隐藏摘要和元数据 默认展示
        boolean disableDigest = false;
        boolean disableMetadata = false;
        if(projectConfig.isSuccess()){
            JSONObject data = projectConfig.getData();
            JSONObject searchListSet = data.getJSONObject("searchListSet");
            if(searchListSet != null){
                disableDigest = searchListSet.getBooleanValue("disableDigest");
                disableMetadata = searchListSet.getBooleanValue("disableMetadata");
            }
        }
        log.info("封装搜索结果,禁用摘要:{},禁用元数据:{}",disableDigest,disableMetadata);
        for (NlpRecordResp nlpRecord : nlpRecordList) {
            SearchRecordResp searchRecordResp = new SearchRecordResp();
            BeanUtils.copyProperties(nlpRecord, searchRecordResp);
            if (CollUtil.isNotEmpty(nlpRecord.getRepoFolders())) {
                List<SearchRecordResp.RepoFolder> searchRepoFolderList = new ArrayList<>();
                for (NlpRecordResp.RepoFolder repoFolder : nlpRecord.getRepoFolders()) {
                    SearchRecordResp.RepoFolder searchRepoFolder = new SearchRecordResp.RepoFolder();
                    Long repoId = repoFolder.getRepoId();
                    searchRepoFolder.setRepoId(repoId);
                    RepositoryResp repository = repositoryMap.get(repoId);
                    if (repository != null) {
                        searchRepoFolder.setRepoName(repository.getName());
                        searchRepoFolder.setCtype(repository.getCtype());
                        searchRepoFolder.setShareType(repository.getShareType());
                    }
                    searchRepoFolder.setRepoStatus(repoFolder.getRepoStatus());
                    searchRepoFolder.setFolderIds(repoFolder.getFolderIds());
                    searchRepoFolderList.add(searchRepoFolder);
                }
                searchRecordResp.setRepoFolders(searchRepoFolderList);
            }
            List<SearchRecordResp.MetadataIdValue> metadataIdValueList = DozerUtils.convertListToNew(
                    nlpRecord.getMetadataIdValue(),
                    SearchRecordResp.MetadataIdValue.class);
            searchRecordResp.setMetadataIdValue(metadataIdValueList);
            List<NlpRecordResp> attachmentFileList = nlpRecord.getAttachmentFileList();
            if(CollUtil.isNotEmpty(attachmentFileList)){
                searchRecordResp.setAttachmentFileList(attachmentFileList.stream().map(attachmentFile -> {
                    SearchRecordResp.AttachmentFile attachmentFileResp = new SearchRecordResp.AttachmentFile();
                    BeanUtils.copyProperties(attachmentFile,attachmentFileResp);
                    return attachmentFileResp;
                }).collect(Collectors.toList()));
            }
            searchRecordRespList.add(searchRecordResp);
            wrapperData(req.getKw(), nlpRecord, searchRecordResp, req.getSearchType(),disableDigest,disableMetadata);
        }
        //设置收藏状态
        setCollectStatus(searchRecordRespList,collectStatusMap);
        //设置文档信息
        setRecordData(searchRecordRespList, recordInfoMap,recordTypeMap,searchResultMetadataMap);
        //设置操作权限
        setPermissionOperate(searchRecordRespList,currentUserId,permissionOperateMap);
        setDocumentInfo(searchRecordRespList);
        return searchRecordRespList;
    }

    private void setDocumentInfo(List<SearchRecordResp> searchRecordRespList) {
        if (CollUtil.isEmpty(searchRecordRespList)) {
            return;
        }
        //查询documentId
        List<Long> recordIdList = searchRecordRespList.stream().map(SearchRecordResp::getRecordId)
                .collect(Collectors.toList());
        R<List<DocumentFileResp>> documentListR = documentRpcService.getMasterDocumentListByRecordIds(recordIdList,true);
        if (documentListR.isSuccess()) {
            List<DocumentFileResp> documentListList = documentListR.getData();
            if (CollUtil.isNotEmpty(documentListList)) {
                Map<Long, DocumentFileResp> recordId2DocumentMap = documentListList.stream()
                        .collect(Collectors.toMap(DocumentFileResp::getRecordId, Function.identity(), (v1, v2) -> v2));
                for (SearchRecordResp searchRecordResp : searchRecordRespList) {
                    Long recordId = searchRecordResp.getRecordId();
                    DocumentFileResp documentFileResp = recordId2DocumentMap.get(recordId);
                    if (documentFileResp != null) {
                        searchRecordResp.setDocumentId(documentFileResp.getId());
                        searchRecordResp.setCoverUrl(documentFileResp.getCoverUrl());
                    }
                }
            }
        }
    }

    private void setPermissionOperate(List<SearchRecordResp> searchRecordRespList,String currentUserId,
            Map<Long, List<PermissionOperateResp>> permissionOperateMap) {
        if (CollUtil.isEmpty(searchRecordRespList) || CollUtil.isEmpty(permissionOperateMap)) {
            return;
        }
        if (!Objects.equals("0", currentUserId)) {
            //已登录用户
            for (SearchRecordResp searchRecordResp : searchRecordRespList) {
                Long recordId = searchRecordResp.getRecordId();
                List<PermissionOperateResp> permissionOperates = permissionOperateMap.getOrDefault(recordId, new ArrayList<>());
                boolean noneMatch = permissionOperates.stream().map(PermissionOperateResp::getCode)
                        .noneMatch(code -> Objects.equals(code, PermissionMask.VIEW.getMemo()));
                searchRecordResp.setBorrowstatus(0);
                if (noneMatch) {
                    searchRecordResp.setBorrowstatus(1);
                    searchRecordResp.setContent(null);
                    searchRecordResp.setCoverUrl(null);
                }
            }
        }

    }

    private void setRecordData(List<SearchRecordResp> searchRecordRespList,Map<Long,RecordInfoResp> recordInfoMap
            ,Map<Long, RecordTypeResp> recordTypeMap,Map<Long, List<MetadataResp>> searchResultMetadataMap) {
        if (CollUtil.isEmpty(searchRecordRespList) || CollUtil.isEmpty(recordInfoMap)) {
            return;
        }
        if(CollUtil.isEmpty(recordTypeMap)){
            recordTypeMap = new HashMap<>();
        }
        for (SearchRecordResp searchRecordResp : searchRecordRespList) {
            Long recordId = searchRecordResp.getRecordId();
            RecordInfoResp recordInfoResp = recordInfoMap.get(recordId);
            if (recordInfoResp != null) {
                searchRecordResp.setRecordName(recordInfoResp.getName());
                Long recordTypeId = recordInfoResp.getRecordtypeId();
                searchRecordResp.setRecordTypeId(recordTypeId);
                Integer origin = recordInfoResp.getOrigin();
                searchRecordResp.setSource(recordOriginConfig.getOriginName(origin));
                RecordTypeResp recordTypeResp = recordTypeMap.get(recordTypeId);
                if (recordTypeResp != null) {
                    searchRecordResp.setRecordTypeName(recordTypeResp.getName());
                }
                List<MetadataResp> searchMetadataIdList = searchResultMetadataMap.getOrDefault(recordTypeId,new ArrayList<>());
                Map<Long, MetadataResp> metadataRespMap = searchMetadataIdList.stream()
                        .collect(Collectors.toMap(MetadataResp::getId, Function.identity()));
                List<SearchRecordResp.MetadataIdValue> metadataIdValues = searchRecordResp.getMetadataIdValue();
                if(CollUtil.isNotEmpty(metadataIdValues)){
                    Iterator<SearchRecordResp.MetadataIdValue> iterator = metadataIdValues.iterator();
                    while (iterator.hasNext()){
                        SearchRecordResp.MetadataIdValue metadataIdValue = iterator.next();
                        MetadataResp metadataResp = metadataRespMap.get(metadataIdValue.getMetadataId());
                        if(metadataResp == null){
                            iterator.remove();
                            continue;
                        }
                        //替换名称
                        metadataIdValue.setMetadataName(metadataResp.getName());
                    }
                }
            }
        }
    }

    private void setCollectStatus(List<SearchRecordResp> searchRecordRespList,Map<Long,CollectStatusResp> collectStatusMap) {
        if(CollUtil.isEmpty(collectStatusMap)){
            return;
        }
        for (SearchRecordResp searchRecordResp : searchRecordRespList) {
            CollectStatusResp collectStatusResp = collectStatusMap.get(searchRecordResp.getRecordId());
            if (Objects.nonNull(collectStatusResp)) {
                searchRecordResp.setStatus(collectStatusResp.getStatus());
                searchRecordResp.setCollectId(collectStatusResp.getCollectId());
            }
        }
    }

    private List<Long> getConditionRepoByCType(List<Integer> cTypeList) {
        RepositoryQueryReq repositoryQueryReq = new RepositoryQueryReq();
        repositoryQueryReq.setCtypeList(cTypeList);
        R<List<RepositoryResp>> simpleRepoListR = repositoryRpcService.listSimpleRepoInfo(repositoryQueryReq);
        if (simpleRepoListR.isError()) {
            log.error("查询库列表失败:{}", simpleRepoListR.getMsg());
            throw new RuntimeException("查询库列表失败:" + simpleRepoListR.getMsg());
        }
        List<RepositoryResp> repoList = simpleRepoListR.getData();
        if (CollectionUtils.isEmpty(repoList)) {
            return Collections.emptyList();
        }
        return repoList.stream().map(RepositoryResp::getId).collect(Collectors.toList());
    }

    public Query.Builder buildSearchEsQueryBuilder(SearchRecordV2QueryReq req){
        //构建过滤条件（不包含搜索词）
        SearchTreeNode searchTreeNode = this.buildSearchTreeNode(req);
        Query.Builder filterBuilder = SearchTreeManager.searchTree2EsBuilder(searchTreeNode);
        Query keywordQuery = null;
        if (req.hasKeyword()) {
            keywordQuery = buildKeywordSearchEsQuery(req.getKw());
        }
        return buildSearchEsQueryBuilder(filterBuilder.build(),keywordQuery);
    }

    public Query.Builder buildSearchEsQueryBuilder(Query filterQuery,Query keywordQuery){
        //创建 BoolQuery.Builder 以合并两个查询条件
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        if(filterQuery != null){
            boolQueryBuilder.filter(filterQuery);
        }
        if(keywordQuery != null){
            boolQueryBuilder.must(keywordQuery);
        }
        Query.Builder lastBuilder = new Query.Builder();
        lastBuilder.bool(b -> boolQueryBuilder);
        return lastBuilder;
    }

    public abstract Query buildKeywordSearchEsQuery(KeywordQueryItem kw);

    @Override
    public List<SearchRepoRecordCountResp> aggByRepo(SearchRecordV2QueryReq req) {
        boolean attachmentSearch = checkAttachmentSearch(req);
        if(attachmentSearch){
            //带附件搜索
            req.setOnlyQueryMasterRecord(false);
        }
        Query.Builder queryBuilder = this.buildSearchEsQueryBuilder(req);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                .size(0)
                .query(q -> queryBuilder)
                .aggregations("repoFolder", agg -> agg
                        .nested(aggNested -> aggNested.path("repoFolders"))
                        .aggregations("repoIds", repoIdsAgg -> repoIdsAgg
                                .terms(rt -> rt.field("repoFolders.repoId").size(10000))
                                .aggregations("back_to_root",toRootAgg -> toRootAgg.reverseNested(r -> r)
                                        .aggregations("total_count",totalCount -> totalCount.cardinality(c -> c.field("recordId"))))));
        SearchRequest searchRequest = searchBuilder.build();
        try {
            log.info("aggByRepo es request:{}",searchRequest);
            SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate repoFolderAggregate = aggregations.get("repoFolder");
            NestedAggregate nestedAggregate = repoFolderAggregate.nested();
            Map<String, Aggregate> filterAggregationMap = nestedAggregate.aggregations();
            Aggregate repoIdsAggregate = filterAggregationMap.get("repoIds");
            Buckets<LongTermsBucket> buckets = repoIdsAggregate.lterms().buckets();
            List<LongTermsBucket> bucketList = buckets.array();
            List<SearchRepoRecordCountResp> result = Lists.newArrayList();
            bucketList.forEach(b -> {
                SearchRepoRecordCountResp searchRepoRecordCountResp = new SearchRepoRecordCountResp();
                searchRepoRecordCountResp.setRepoId(b.key());
                Map<String, Aggregate> backToRootAggMap = b.aggregations();
                Aggregate backToRootAgg = backToRootAggMap.get("back_to_root");
                ReverseNestedAggregate reverseNestedAggregate = backToRootAgg.reverseNested();
                Map<String, Aggregate> reverseAggregationsMap = reverseNestedAggregate.aggregations();
                Aggregate aggregate = reverseAggregationsMap.get("total_count");
                searchRepoRecordCountResp.setDocCount(aggregate.cardinality().value());
                result.add(searchRepoRecordCountResp);
            });
            log.info("searchRepoIds rsp----values:{}", JSON.toJSONString(result));
            return result;
        } catch (IOException e) {
            log.error("根据库id聚合查询异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<SearchAggCountResp> aggRecordCount(SearchRecordV2QueryReq req, String field) {
        Query.Builder queryBuilder = this.buildSearchEsQueryBuilder(req);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        String[] split = field.split("\\.");
        if (split.length == 1) {
            searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                    .size(0)
                    .query(q -> queryBuilder)
                    .aggregations("aggField", agg -> agg
                            .terms(rt -> {
                                if (CollectionUtils.isEmpty(req.getAggDataList())) {
                                    rt.field(field).size(10000);
                                } else {
                                    rt.field(field).include(t -> t.terms(req.getAggDataList().stream()
                                            .map(String::valueOf).collect(Collectors.toList()))).size(10000);
                                }
                                return rt;
                            }));
            SearchRequest searchRequest = searchBuilder.build();
            try {
                log.info("aggRecordCount field:{}, es request:{}",field,searchRequest);
                SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get("aggField");
                Buckets<LongTermsBucket> buckets = aggregate.lterms().buckets();
                List<LongTermsBucket> bucketList = buckets.array();
                List<SearchAggCountResp> result = Lists.newArrayList();
                bucketList.forEach(b -> {
                    SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                    searchAggCountResp.setId(b.key());
                    searchAggCountResp.setCount(b.docCount());
                    result.add(searchAggCountResp);
                });
                log.info("aggRecordCount field:{},rsp----values:{}", field,JSON.toJSONString(result));
                return result;
            } catch (IOException e) {
                log.error("聚合查询异常", e);
                return Collections.emptyList();
            }
        } else {
            String path = split[0];
            searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                    .size(0)
                    .query(q -> queryBuilder)
                    .aggregations("agg1", agg -> agg
                            .nested(aggNested -> aggNested.path(path))
                            .aggregations("agg2", repoIdsAgg -> repoIdsAgg
                                    .terms(rt -> rt.field(field).size(10000))
                                    .aggregations("back_to_root",toRootAgg -> toRootAgg.reverseNested(r -> r))));
            SearchRequest searchRequest = searchBuilder.build();
            try {
                log.info("aggRecordCount nested field:{}, es request:{}",field,searchRequest);
                SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate repoFolderAggregate = aggregations.get("agg1");
                NestedAggregate nestedAggregate = repoFolderAggregate.nested();
                Map<String, Aggregate> filterAggregationMap = nestedAggregate.aggregations();
                Aggregate repoIdsAggregate = filterAggregationMap.get("agg2");
                Buckets<LongTermsBucket> buckets = repoIdsAggregate.lterms().buckets();
                List<LongTermsBucket> bucketList = buckets.array();
                List<SearchAggCountResp> result = Lists.newArrayList();
                bucketList.forEach(b -> {
                    SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                    searchAggCountResp.setId(b.key());
                    Map<String, Aggregate> backToRootAggMap = b.aggregations();
                    Aggregate backToRootAgg = backToRootAggMap.get("back_to_root");
                    searchAggCountResp.setCount(backToRootAgg.reverseNested().docCount());
                    result.add(searchAggCountResp);
                });
                log.info("aggRecordCount nested field:{},rsp----values:{}", field,JSON.toJSONString(result));
                return result;
            } catch (IOException e) {
                log.error("聚合查询异常", e);
                return Collections.emptyList();
            }
        }
    }

    @Override
    public List<SearchAggCountResp> aggByMetadata(SearchRecordV2QueryReq req, NlpRecord.MetadataIdValue metadataValue) {
        Query.Builder queryBuilder = this.buildSearchEsQueryBuilder(req);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        Long metadataId = metadataValue.getMetadataId();
        String metadataName = metadataValue.getMetadataName();
        searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                .size(0)
                .query(q -> queryBuilder)
                .aggregations("metadataIdFilter", metadataIdFilter -> metadataIdFilter
                        .filter(f -> f.nested(n -> n.path("metadataIdValue").query(q -> q.bool(qb -> {
                            if (metadataId != null) {
                                qb.should(s -> s.term(
                                        t -> t.field("metadataIdValue.metadataId").value(metadataId)));
                            }
                            if (com.suwell.plss.framework.common.utils.StringUtils.isNotEmpty(metadataName)) {
                                qb.should(s -> s.term(t -> t.field("metadataIdValue.metadataName")
                                        .value(metadataName)));
                            }
                            return qb;
                        }))))
                        .aggregations("distinctMetadataValues", distinctMetadataValues -> distinctMetadataValues
                                .nested(n -> n.path("metadataIdValue")).
                                aggregations("values", values -> values.terms(
                                        t -> t.field("metadataIdValue.metadataValue.keyword").size(10000).include(
                                                in -> in.terms(metadataValue.getMetadataValues())
                                        )))));
        SearchRequest searchRequest = searchBuilder.build();
        try {
            SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate metadataIdFilterAggregate = aggregations.get("metadataIdFilter");
            FilterAggregate filterAggregate = metadataIdFilterAggregate.filter();
            Map<String, Aggregate> aggregations1 = filterAggregate.aggregations();
            Aggregate distinctMetadataValuesAggregate = aggregations1.get("distinctMetadataValues");
            NestedAggregate distinctNestedAggregate = distinctMetadataValuesAggregate.nested();
            Map<String, Aggregate> distinctAggregations = distinctNestedAggregate.aggregations();
            Buckets<StringTermsBucket> buckets = distinctAggregations.get("values").sterms().buckets();
            List<StringTermsBucket> bucketList = buckets.array();
            List<SearchAggCountResp> result = Lists.newArrayList();
            bucketList.forEach(b -> {
                SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                searchAggCountResp.setId(metadataId);
                searchAggCountResp.setName(b.key().stringValue());
                searchAggCountResp.setCount(b.docCount());
                result.add(searchAggCountResp);
            });
            log.info("searchRepoIds rsp----values:{}", JSON.toJSONString(result));
            return result;
        } catch (IOException e) {
            log.error("聚合查询异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<SearchAggCountResp> aggByDateMetadataRange(SearchRecordV2QueryReq req,
            DateMetadataAgg dateMetadataAgg) {
        Long metadataId = dateMetadataAgg.getMetadataId();
        List<DateRange> dateRangeList = dateMetadataAgg.getDateRangeList();
        if (metadataId == null || CollectionUtils.isEmpty(dateRangeList)) {
            return Collections.emptyList();
        }
        Query.Builder queryBuilder = this.buildSearchEsQueryBuilder(req);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                .size(0)
                .query(q -> queryBuilder);
        for (DateRange dateRange : dateRangeList) {
            searchBuilder.aggregations(dateRange.getName(), agg -> agg
                    .filter(f -> f.nested(n -> n.path("metadataIdValue").query(q -> q.bool(b -> {
                        b.must(m -> m.term(t -> t.field("metadataIdValue.metadataId").value(metadataId)));
                        b.must(m -> m.range(r -> {
                            RangeQuery.Builder field = r.field("metadataIdValue.metadataValue.keyword");
                            if (com.suwell.plss.framework.common.utils.StringUtils.isNotEmpty(dateRange.getStartTime())) {
                                field.gte(JsonData.of(dateRange.getStartTime()));
                            }
                            if (com.suwell.plss.framework.common.utils.StringUtils.isNotEmpty(dateRange.getEndTime())) {
                                field.lte(JsonData.of(dateRange.getEndTime()));
                            }
                            return r;
                        }));
                        return b;
                    }))))
                    .aggregations("count", count -> count
                            .valueCount(v -> v.field("recordId"))));
        }
        SearchRequest searchRequest = searchBuilder.build();
        try {
            SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
            Map<String, Aggregate> aggregations = response.aggregations();
            List<SearchAggCountResp> result = new ArrayList<>();
            for (DateRange dateRange : dateRangeList) {
                Aggregate aggregate = aggregations.get(dateRange.getName());
                long value = aggregate.filter().docCount();
                SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                searchAggCountResp.setName(dateRange.getName());
                searchAggCountResp.setCount(value);
                result.add(searchAggCountResp);
            }
            log.info("按日期范围聚合结果:{}", JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("聚合日期元数据查询异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据元数据id聚合元数据值
     * @param req 查询条件
     * @param metadataIds 元数据id
     * @return 聚合结果
     */
    public List<SearchAggCountResp> aggMetadataValueByMetadataIds(SearchRecordV2QueryReq req, List<Long> metadataIds) {
        Query.Builder queryBuilder = this.buildSearchEsQueryBuilder(req);
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        List<FieldValue> metadataIdFieldValues = metadataIds.stream().map(FieldValue::of).toList();
        searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                .size(0)
                .query(q -> queryBuilder)
                .aggregations("metadata_values", metadataValues ->
                        metadataValues.nested(n -> n.path("metadataIdValue"))
                                .aggregations("by_metadata_id_and_value", byMetadataIdAndName -> byMetadataIdAndName
                                        .filter(f -> f.terms(t -> t.field("metadataIdValue.metadataId")
                                                .terms(terms -> terms.value(metadataIdFieldValues))))
                                        .aggregations("by_metadata_id", byMetadataId ->
                                                byMetadataId.terms(t -> t.field("metadataIdValue.metadataId").size(10))
                                                        .aggregations("by_metadata_value", byMetadataValue ->
                                                                byMetadataValue.terms(t -> t.field(
                                                                                "metadataIdValue.metadataValue.keyword")
                                                                        .size(50000)))
                                        )
                                ));
        SearchRequest searchRequest = searchBuilder.build();
        try {
            SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate metadataValueAgg = aggregations.get("metadata_values");
            NestedAggregate nestedAggregate = metadataValueAgg.nested();
            Map<String, Aggregate> nestedAggregations = nestedAggregate.aggregations();
            Aggregate aggregate = nestedAggregations.get("by_metadata_id_and_value");
            FilterAggregate filterAggregate = aggregate.filter();
            Map<String, Aggregate> filterAggregations = filterAggregate.aggregations();
            Aggregate metadataIdAggregate = filterAggregations.get("by_metadata_id");
            Buckets<LongTermsBucket> buckets = metadataIdAggregate.lterms().buckets();
            List<SearchAggCountResp> searchAggCountRespList = new ArrayList<>();
            if (CollUtil.isNotEmpty(buckets.array())) {
                for (LongTermsBucket longTermsBucket : buckets.array()) {
                    Long metadataId = longTermsBucket.key();
                    Map<String, Aggregate> metadataValueAggregations = longTermsBucket.aggregations();
                    Aggregate metadataValueAggregate = metadataValueAggregations.get("by_metadata_value");
                    Buckets<StringTermsBucket> metadataValueBuckets = metadataValueAggregate.sterms().buckets();
                    List<StringTermsBucket> metadataValueBucketList = metadataValueBuckets.array();
                    if (CollUtil.isNotEmpty(metadataValueBucketList)) {
                        for (StringTermsBucket stringTermsBucket : metadataValueBucketList) {
                            SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                            searchAggCountResp.setId(metadataId);
                            searchAggCountResp.setName(stringTermsBucket.key().stringValue());
                            searchAggCountResp.setCount(stringTermsBucket.docCount());
                            searchAggCountRespList.add(searchAggCountResp);
                        }
                    }
                }
            }
            log.info("agg by metadata rsp----values:{}", JSON.toJSONString(searchAggCountRespList));
            return searchAggCountRespList;
        } catch (IOException e) {
            log.error("聚合元数据查询异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据元数据id聚合元数据值
     * @param metadataId 元数据id
     * @param keyword 搜索词
     * @param size 查询数量
     * @return 聚合结果
     */
    public List<SearchAggCountResp> aggMetadataValueByMetadataId(Long metadataId,String keyword,int size) {
        if(metadataId == null){
            return Collections.emptyList();
        }
        if(StringUtils.isEmpty(keyword)){
            return Collections.emptyList();
        }
        if(size <= 0){
            size = 10;
        }
        if(size > 100){
            size = 100;
        }
        final int finalSize = size;
        SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        List<Long> metadataIds = Collections.singletonList(metadataId);
        List<FieldValue> metadataIdFieldValues = metadataIds.stream().map(FieldValue::of).toList();
        searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                .size(0)
                .query(q -> {
                    q.nested(n -> n.path("metadataIdValue").query(nq -> nq.bool(b -> {
                        b.must(m -> m.term(t -> t.field("metadataIdValue.metadataId").value(metadataId)));
                        b.must(m -> m.wildcard(w -> w.field("metadataIdValue.metadataValue.keyword").value(keyword + "*")));
                        return b;
                    })));
                    return q;
                })
                .aggregations("metadata_values", metadataValues ->
                        metadataValues.nested(n -> n.path("metadataIdValue"))
                                .aggregations("by_metadata_id_and_value", byMetadataIdAndName -> byMetadataIdAndName
                                        .filter(f -> f.terms(t -> t.field("metadataIdValue.metadataId")
                                                .terms(terms -> terms.value(metadataIdFieldValues))))
                                        .aggregations("by_metadata_id", byMetadataId ->
                                                byMetadataId.terms(t -> t.field("metadataIdValue.metadataId").size(10))
                                                        .aggregations("by_metadata_value", byMetadataValue ->
                                                                byMetadataValue.terms(t -> t.field(
                                                                                "metadataIdValue.metadataValue.keyword")
                                                                        .size(finalSize)))
                                        )
                                ));
        SearchRequest searchRequest = searchBuilder.build();
        try {
            SearchResponse<String> response = elasticsearchClient.search(searchRequest, String.class);
            Map<String, Aggregate> aggregations = response.aggregations();
            Aggregate metadataValueAgg = aggregations.get("metadata_values");
            NestedAggregate nestedAggregate = metadataValueAgg.nested();
            Map<String, Aggregate> nestedAggregations = nestedAggregate.aggregations();
            Aggregate aggregate = nestedAggregations.get("by_metadata_id_and_value");
            FilterAggregate filterAggregate = aggregate.filter();
            Map<String, Aggregate> filterAggregations = filterAggregate.aggregations();
            Aggregate metadataIdAggregate = filterAggregations.get("by_metadata_id");
            Buckets<LongTermsBucket> buckets = metadataIdAggregate.lterms().buckets();
            List<SearchAggCountResp> searchAggCountRespList = new ArrayList<>();
            if (CollUtil.isNotEmpty(buckets.array())) {
                for (LongTermsBucket longTermsBucket : buckets.array()) {
                    Map<String, Aggregate> metadataValueAggregations = longTermsBucket.aggregations();
                    Aggregate metadataValueAggregate = metadataValueAggregations.get("by_metadata_value");
                    Buckets<StringTermsBucket> metadataValueBuckets = metadataValueAggregate.sterms().buckets();
                    List<StringTermsBucket> metadataValueBucketList = metadataValueBuckets.array();
                    if (CollUtil.isNotEmpty(metadataValueBucketList)) {
                        for (StringTermsBucket stringTermsBucket : metadataValueBucketList) {
                            SearchAggCountResp searchAggCountResp = new SearchAggCountResp();
                            searchAggCountResp.setId(longTermsBucket.key());
                            searchAggCountResp.setName(stringTermsBucket.key().stringValue());
                            searchAggCountResp.setCount(stringTermsBucket.docCount());
                            searchAggCountRespList.add(searchAggCountResp);
                        }
                    }
                }
            }
            log.info("agg by metadata rsp----values:{}", JSON.toJSONString(searchAggCountRespList));
            return searchAggCountRespList;
        } catch (IOException e) {
            log.error("聚合元数据查询异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<SearchAggCountResp> aggSecretRecordCount(SearchRecordV2QueryReq req, List<String> fields) {
        //目前安全文库相关的搜索都走AI中台
        return Collections.emptyList();
    }

    @Override
    public SearchTreeNode buildSearchTreeNode(SearchRecordV2QueryReq req) {
        long start = System.currentTimeMillis();
        String currentUserId = req.getCurrentUserId();
        if (StringUtils.isEmpty(currentUserId)) {
            //防止外层漏设置用户
            currentUserId = SecurityUtils.getUserId();
        }
        SearchTreeNode searchTreeNode = ExistSearchItem.fieldName("title").build2SearchTreeNode()
                .addAndSearchItem(ExistSearchItem.fieldName("tenantId"));
        Integer recordRepoStatus = req.getRecordRepoStatus();
        if(recordRepoStatus == null || Objects.equals(recordRepoStatus,1)){
            //默认查询有位置的文档 兼顾之前的业务
            searchTreeNode.addAndSearchItem(ExistSearchItem.fieldName("repoFolders/repoId"));
        } else if(Objects.equals(recordRepoStatus,2)){
            //查询暂无位置的文档
            searchTreeNode.addAndSearchItem(NotExistSearchItem.fieldName("repoFolders/repoId"));
        }
        if(req.isOnlyQueryMasterRecord()){
            //查询主件 兼容刷数据之前的情况
            Query.Builder masterBuilder = new Query.Builder();
            masterBuilder.bool(b1 -> {
                b1.should(s -> s.term(t -> t.field("fileType").value(1)))
                        .should(s -> s.bool(
                                b2 -> b2.mustNot(mn -> mn.exists(e -> e.field("fileType")))));
                return b1;
            });
            searchTreeNode.addAndSearchItem(EsSearchItem.builder(masterBuilder));
        }
        long buildRepoFolderStart = System.currentTimeMillis();
        RepoFolderSearchItem repoFolderSearchItem = buildRepoFolderSearchItem(req, currentUserId);
        TimeInfoHolder.addTime("构建权限类搜索条件", System.currentTimeMillis() - buildRepoFolderStart);
        searchTreeNode.addAndSearchItem(repoFolderSearchItem);
        //元数据
        List<MetadataResp> metadataRespList = metadataSearchService.getMetadataList();
        //解析元数据
        SearchTreeNode metadataTreeNode = SearchTreeManager.resolveMetadata2TreeNode(req, metadataRespList);
        searchTreeNode.addTreeNode(metadataTreeNode);
        //租户
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("tenantId").valueListStr(req.getTenantIds()));
        //组织架构
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("orgIds").valueListStr(req.getOrgIds()));
        //分类
        List<Long> categoryIds = req.getCategoryIds();
        List<CategoryListQueryItem> categoryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getCategoryList())) {
            categoryList = req.getCategoryList();
        } else if (CollUtil.isNotEmpty(categoryIds)) {
            for (Long categoryId : categoryIds) {
                CategoryListQueryItem categoryListQueryItem = new CategoryListQueryItem();
                categoryListQueryItem.setCategoryIds(Collections.singletonList(categoryId));
                categoryList.add(categoryListQueryItem);
            }
        }
        searchTreeNode.addAndSearchItem(CategorySearchItem.categoryList(categoryList));
        //指定文档
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("recordId").valueList(req.getRecordIds()));
        //指定用户
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("userIds").valueListStr(req.getUserIds()));
        //要过滤的文档
        searchTreeNode.addAndSearchItem(MustNotSearchItem.fieldName("recordId").valueList(req.getFilterRecordIds()));
        //要过滤的分类
        searchTreeNode.addAndSearchItem(
                MustNotSearchItem.fieldName("categoryIds").valueList(req.getFilterCategoryIds()));
        //入库流程类型
        Long storeProcess = req.getStoreProcess();
        if(storeProcess != null){
            searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("storeProcess").
                    valueList(Collections.singletonList(storeProcess)));
        }
        //要过滤的文档类型
        searchTreeNode.addAndSearchItem(
                MustNotSearchItem.fieldName("recordTypeId").valueList(req.getFilterRecordTypeIds()));
        //印发时间范围
        searchTreeNode.addAndSearchItem(
                RangeSearchItem.fieldName("releaseTime").start(req.getReleaseTimeStart()).end(req.getReleaseTimeEnd()));
        //record创建时间
        searchTreeNode.addAndSearchItem(
                RangeSearchItem.fieldName("recordCreateTime").start(req.getRecordCreateTimeStart()).end(req.getRecordCreateTimeEnd()));
        //文档id范围
        searchTreeNode.addAndSearchItem(
                RangeSearchItem.fieldName("recordId").start(req.getRecordIdStart()).end(req.getRecordIdEnd()));
        //文档类型
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("recordTypeId").valueList(req.getRecordTypeIds()));
        // 文档来源
        searchTreeNode.addAndSearchItem(TermsSearchItem.fieldName("origin").valueList(req.getOriginList()));
        //文档搜索来源 库/文档类型/目录
        RecordDataSourceSearchItem dataSourceSearchItem = new RecordDataSourceSearchItem();
        RecordDataSourceQueryItem recordDataSource = req.getRecordDataSource();
        if (recordDataSource != null) {
            searchTreeNode.addAndSearchItem(dataSourceSearchItem.repoIds(recordDataSource.getRepoIds())
                    .recordTypeIds(recordDataSource.getRecordTypeIds()).folderIds(recordDataSource.getFolderIds()));
        }
        //用户选中的数据来源 库/文档类型
        RecordDataSourceSearchItem selectedDataSourceSearchItem = new RecordDataSourceSearchItem();
        RecordDataSourceQueryItem selectedDataSource = req.getSelectedDataSource();
        if (selectedDataSource != null) {
            searchTreeNode.addAndSearchItem(selectedDataSourceSearchItem.repoIds(selectedDataSource.getRepoIds())
                    .recordTypeIds(selectedDataSource.getRecordTypeIds()));
        }
        TimeInfoHolder.addTime("构建搜索条件", System.currentTimeMillis() - start);
        return searchTreeNode;
    }


    private RepoFolderSearchItem buildRepoFolderSearchItem(SearchRecordV2QueryReq req, String currentUserId) {
        //目录嵌套
        RepoFolderSearchItem repoFolderSearchItem = new RepoFolderSearchItem();
        List<String> visitors = new ArrayList<>();
        if (!Objects.equals("0", currentUserId)) {
            //登录用户
            if(req.isQueryAll()){
                //跳过库目录权限拦截
                repoFolderSearchItem.setSkipPermissionFilter(true);
                repoFolderSearchItem.setConditionRepoIds(req.getConditionRepoIds());
            }else{
                UserPermConditionReq userPermConditionReq = new UserPermConditionReq();
                String userId = currentUserId;
                List<String> roleList = SecurityUtils.getRoleList();
                List<String> orgList = SecurityUtils.getOrgList();
                if (ksoAuthConfig.isEnable()) {
                    userId = VisitUserType.USER.wrapId(userId);
                    roleList = VisitUserType.USER_ROLE.wrapIdList(roleList);
                    orgList = VisitUserType.ORGANIZATION.wrapIdList(orgList);
                }
                visitors.add(userId);
                visitors.addAll(roleList);
                visitors.addAll(orgList);
                userPermConditionReq.setVisitorIds(visitors);
                userPermConditionReq.setExcludeTitlePerm(!req.isViewTitleOnlyPerm());
                userPermConditionReq.setCopyDownPerm(req.isCopyDownPerm());
                if (req.isSecret()) {
                    //安全文库搜索
                    userPermConditionReq.setClassified(SecurityUtils.getUserClassifiedCode());
                }
                log.info("rpc UserPermConditionReq:{}", JSON.toJSONString(userPermConditionReq));
                R<UserPermissionResp> userPermissionRespR = permissionRpcService.listUserResourcesWithCondition(
                        userPermConditionReq);
                log.info("rpc UserPermissionResp:{}", JSON.toJSONString(userPermissionRespR));
                if (userPermissionRespR.isError()) {
                    log.error("获取权限范围条件失败:{}", JSON.toJSONString(userPermissionRespR.getData()));
                    throw new RuntimeException("查询用户权限失败:" + userPermissionRespR.getMsg());
                }
                UserPermissionResp data = userPermissionRespR.getData();
                //不是查询所有 设置目录和库权限
                repoFolderSearchItem.setVisibleRepoIds(data.getVisibleRepoIds());
                repoFolderSearchItem.setSharedFolderIds(data.getSharedFolderIds());
                repoFolderSearchItem.setInvisibleFolderIds(data.getInvisibleFolderIds());
                //限定库范围为私有库和公开库
                List<Long> conditionRepoIds = req.getConditionRepoIds();
                if(CollUtil.isEmpty(conditionRepoIds)){
                    //未指定库的范围
                    conditionRepoIds = getConditionRepoByCType(Arrays.asList(RepositoryEnum.CType.PRIVATE.getCode()
                            , RepositoryEnum.CType.COMMON.getCode()));
                    if(CollUtil.isEmpty(conditionRepoIds)){
                        //没有私有库和公开库
                        conditionRepoIds = Collections.singletonList(-1L);
                    }
                    //缩小可访问库权限范围
                    Set<Long> visibleRepoIds = data.getVisibleRepoIds();
                    if(CollUtil.isNotEmpty(visibleRepoIds)){
                        visibleRepoIds.retainAll(conditionRepoIds);
                        repoFolderSearchItem.setVisibleRepoIds(visibleRepoIds);
                    }
                }
                repoFolderSearchItem.setConditionRepoIds(conditionRepoIds);
            }
        } else {
            //未登录用户
            //限定库范围为公开库
            List<Long> conditionRepoIds = req.getConditionRepoIds();
            if(CollUtil.isEmpty(conditionRepoIds)){
                conditionRepoIds = getConditionRepoByCType(Collections.singletonList(RepositoryEnum.CType.COMMON.getCode()));
            }
            if(CollUtil.isEmpty(conditionRepoIds)){
                //没有公开库
                conditionRepoIds = Collections.singletonList(-1L);
            }
            repoFolderSearchItem.setSkipPermissionFilter(true);
            repoFolderSearchItem.setConditionRepoIds(conditionRepoIds);
        }
        repoFolderSearchItem.setFolderIds(req.getFolderIds());
        //库查询 兼容单库和多库查询
        List<Long> repoIds = new ArrayList<>();
        Long repoId = req.getRepoId();
        if (repoId != null) {
            repoIds.add(repoId);
        }
        if (CollUtil.isNotEmpty(req.getRepoIds())) {
            repoIds.addAll(req.getRepoIds());
        }
        //指定库查询
        repoFolderSearchItem.setRepoIds(repoIds);
        //库过滤
        repoFolderSearchItem.setInvisibleRepoIds(req.getFilterRepoIds());
        //目录排除
        repoFolderSearchItem.setExcludeRepoFolderIds(req.getExcludeRepoFolderIds());
        //过滤仅查看标题权限文档
        repoFolderSearchItem.setViewTitleOnlyPerm(req.isViewTitleOnlyPerm());
        //用户关系id列表
        repoFolderSearchItem.setVisitorIds(visitors);
        return repoFolderSearchItem;
    }

    protected List<SortOptions> buildSortOptions(SearchRecordV2QueryReq req) {
        String sortVisit = req.getSortVisit();
        List<SearchSortOption> searchSortOptions = req.getSortOptions();
        if (StringUtils.isNotEmpty(sortVisit)) {
            //兼容sortVisit按浏览量排序字段
            SearchSortOption sortVisitOption = new SearchSortOption();
            sortVisitOption.setSortField("visitNum");
            sortVisitOption.setSortType(sortVisit);
            searchSortOptions.add(sortVisitOption);
        }
        //设置排序 注意先后顺序
        List<SortOptions> sorts = Lists.newArrayList();
        if (CollUtil.isNotEmpty(searchSortOptions)) {
            for (SearchSortOption searchSortOption : searchSortOptions) {
                String sortField = searchSortOption.getSortField();
                String sortType = searchSortOption.getSortType();
                boolean metadataSort = searchSortOption.isMetadataSort();
                //判断嵌套排序
                if(StringUtils.isNotEmpty(sortField) && Arrays.stream(SortOrder.values()).anyMatch(n -> n.jsonValue().equals(sortType))){
                    SortOptions sortOption;
                    if(metadataSort){
                        sortOption = SortOptions.of(s -> s.field(f -> f.field("metadataIdValue.metadataValue.keyword")
                                .nested(n -> n.path("metadataIdValue")
                                        .filter(f1 -> f1.term(f2 -> f2.field("metadataIdValue.metadataId").value(sortField))))
                                .order(SortOrder.Desc.jsonValue().equals(sortType) ? SortOrder.Desc : SortOrder.Asc)));
                    }else{
                        sortOption = SortOptions.of(s -> s.field(f -> f.field(sortField)
                                .order(SortOrder.Desc.jsonValue().equals(sortType) ? SortOrder.Desc : SortOrder.Asc)));
                    }
                    sorts.add(sortOption);
                }
            }
        }
        if (CollUtil.isEmpty(sorts)) {
            //没有任何排序条件
            if (req.hasKeyword()) {
                //输入了关键词 根据分值相关性排序
                SortOptions sortScore = SortOptions.of(s -> s.field(f -> f.field("_score").order(SortOrder.Desc)));
                sorts.add(sortScore);
            } else {
                SortOptions sortReleaseTime = SortOptions.of(s -> s.field(f ->
                        f.field("releaseTime").order(SortOrder.Desc)));
                sorts.add(sortReleaseTime);
            }
        }
        return sorts;
    }

    public Highlight.Builder buildHighlightBuilder(SearchRecordV2QueryReq req) {
        //高亮范围，关键词查询时高亮
        int fragmentSize = 220;
        Highlight.Builder highlightBuilder = new Highlight.Builder();
        if (req.hasKeyword()) {
            highlightBuilder.preTags(ElasticsearchService.LIGHT_PRE_TAGS)
                    .postTags(ElasticsearchService.LIGHT_POST_TAGS)
                    .requireFieldMatch(true)
                    //高亮关键字数量
                    .numberOfFragments(50)
                    //高亮字段内容长度
                    .fragmentSize(fragmentSize)
                    .fields("title", highlightFieldBuilder -> highlightFieldBuilder)
                    .fields("digest", highlightFieldBuilder -> highlightFieldBuilder)
                    .fields("content", highlightFieldBuilder -> highlightFieldBuilder);
        } else {
            highlightBuilder.fields(Collections.emptyMap());
        }
        return highlightBuilder;
    }

    /**
     * 文档检索
     *
     * @param req 请求参数
     * @return 搜索结果
     */
    public abstract PageUtils<NlpRecordResp> search(SearchRecordV2QueryReq req);

    /**
     * 安全文档搜索 默认空实现
     * @param req 请求参数
     * @return 搜索结果
     */
    public PageUtils<NlpRecordResp> searchSecret(SearchRecordV2QueryReq req){
        return new PageUtils<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPage());
    }

    /**
     * 数据包装
     *
     * @param nlpRecord 文档数据
     */
    private void wrapperData(KeywordQueryItem kw, NlpRecordResp nlpRecord, SearchRecordResp searchRecordResp,
            Integer searchType,boolean disableDigest,boolean disableMetadata) {
        //预处理正文 不带高亮
        String content = nlpRecord.getContent();
        if (StringUtils.isNotEmpty(content)) {
            if (content.length() > CONTENT_LENGTH) {
                content = content.substring(0, CONTENT_LENGTH);
            }
        }
        searchRecordResp.setContent(content);
        //是否隐藏元数据
        if(disableMetadata){
            searchRecordResp.setMetadataIdValue(null);
        }
        if (Objects.equals(searchType, SearchEnum.SearchType.ST_AI_SEARCH.getCode())
                || Objects.equals(searchType, SearchEnum.SearchType.ST_CUSTOMER_SEARCH.getCode())) {
            //Ai中台搜索 处理摘要
            searchRecordResp.setRealDigest(nlpRecord.getDigest());
            if (StringUtils.isNotEmpty(content) && content.contains("<maple>")) {
                //正文有命中
                searchRecordResp.setDigest(content);
            } else if (disableDigest) {
                //禁用摘要 则不展示任何内容
                searchRecordResp.setDigest(null);
            } else if (StringUtils.isEmpty(searchRecordResp.getDigest())) {
                String title = nlpRecord.getTitle();
                //处理正文以标题开头的情况
                if (StringUtils.isNotEmpty(title) && StringUtils.isNotEmpty(content) && content.startsWith(
                        title + "\r\n")) {
                    content = content.substring((title + "\r\n").length());
                }
                searchRecordResp.setDigest(content);
            }
            return;
        }
        String digest = nlpRecord.getDigest();
        //下面会对摘要做包装 这里先保存真实的摘要数据
        searchRecordResp.setRealDigest(digest);
        if (StringUtils.isEmpty(digest)) {
            //没有摘要 返回正文
            String title = nlpRecord.getTitle();
            //处理正文以标题开头的情况
            if (StringUtils.isNotEmpty(title) && StringUtils.isNotEmpty(content) && content.startsWith(
                    title + "\r\n")) {
                content = content.substring((title + "\r\n").length());
            }
            searchRecordResp.setDigest(content);
        }
        if (kw == null || StringUtils.isEmpty(kw.getKeyword())) {
            return;
        }
        String keyword = kw.getKeyword().trim();
        if (StringUtils.isEmpty(keyword)) {
            return;
        }
        Map<String, List<String>> highlightMap = nlpRecord.getHighlightMap();
        if (highlightMap == null || highlightMap.isEmpty()) {
            return;
        }
        List<String> titleHightLightList = highlightMap.get("title");
        if (!CollectionUtils.isEmpty(titleHightLightList)) {
            //返回高亮标题
            searchRecordResp.setTitle(titleHightLightList.get(0));
        }
        List<String> digestHightLightList = highlightMap.get("digest");
        if (!CollectionUtils.isEmpty(digestHightLightList)) {
            //返回高亮摘要
            searchRecordResp.setDigest(String.join("", digestHightLightList));
        }
        List<String> contentHightLightList = highlightMap.get("content");
        String specialSymbols = "^[，；。、》]+";
        if (CollectionUtils.isEmpty(contentHightLightList)) {
            return;
        }
        //返回高亮正文
        String highLightContentStr = String.join("", contentHightLightList);
        searchRecordResp.setContent(highLightContentStr.replaceFirst(specialSymbols, ""));
        //摘要和内容高亮的位置优先返回 高亮符号：<maple></maple>
        String highLightContent = String.join(PARAGRAPH_SEPARATOR, contentHightLightList);
        List<String> contentHighLightLines = getHighLightLines(keyword, highLightContent);
        String digestDisplay = null;
        if (!CollectionUtils.isEmpty(contentHighLightLines)) {
            //取命中的第一句话
            for (String line : contentHighLightLines) {
                String realLine = line.replace("<maple>", "").replace("</maple>", "");
                if (StringUtils.isNotEmpty(realLine) && StringUtils.isNotEmpty(realLine.trim())) {
                    digestDisplay = line + PARAGRAPH_SEPARATOR;
                    break;
                }
            }
        } else {
            digestDisplay = digest;
        }
        if (StringUtils.isNotEmpty(digestDisplay)) {
            //过滤最前面的特殊符号
            digestDisplay = digestDisplay.replaceFirst(specialSymbols, "");
            searchRecordResp.setDigest(digestDisplay);
        }
    }


    private List<String> getHighLightLines(String keyword, String body) {
        if (StringUtils.isEmpty(body)) {
            return Collections.emptyList();
        }
        //按句号分割 保留有高亮内容的内容
        List<String> matchLines = new ArrayList<>();
        //给每一个句子设置标识
        String[] lines = body.split(PARAGRAPH_SEPARATOR);
        TreeMap<Integer, List<String>> treeMap = new TreeMap<>((o1, o2) -> o2 - o1);
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            // 定义正则表达式模式
            Matcher matcher = PATTERN.matcher(line);
            List<String> matchWordList = new ArrayList<>();
            while (matcher.find()) {
                matchWordList.add(matcher.group(1));
            }
            int matchWordLength = 0;
            for (String word : matchWordList) {
                matchWordLength += word.length();
            }
            List<String> lineList = treeMap.get(matchWordLength);
            if (lineList == null) {
                lineList = new ArrayList<>();
            }
            lineList.add(line);
            treeMap.put(matchWordLength, lineList);
        }
        for (Map.Entry<Integer, List<String>> entry : treeMap.entrySet()) {
            matchLines.addAll(entry.getValue());
        }
        return matchLines;
    }


    protected List<NlpRecordResp> convertNlpRecordDto(List<NlpRecordDto> nlpRecordDtoList,SearchRecordV2QueryReq req) {
        if(CollUtil.isEmpty(nlpRecordDtoList)){
            return Collections.emptyList();
        }
        List<NlpRecord> nlpRecordList = convertDto2NlpRecord(nlpRecordDtoList);
        return convertNlpRecord(nlpRecordList,req);
    }

    private List<NlpRecord> convertDto2NlpRecord(List<NlpRecordDto> nlpRecordDtoList) {
        return nlpRecordDtoList.stream().map(nlpRecordDto -> {
            NlpRecord nlpRecord = new NlpRecord();
            BeanUtils.copyProperties(nlpRecordDto, nlpRecord);
            List<NlpRecordDto.RepoFolder> nlpRecordDtoRepoFolders = nlpRecordDto.getRepoFolders();
            List<NlpRecordDto.MetadataIdValue> nlpRecordDtoMetadataIdValueList = nlpRecordDto.getMetadataIdValue();
            if (nlpRecordDtoRepoFolders != null) {
                nlpRecord.setRepoFolders(nlpRecordDtoRepoFolders.stream().map(repoFolder -> {
                    NlpRecord.RepoFolder nlpRecordRepoFolder = new NlpRecord.RepoFolder();
                    BeanUtils.copyProperties(repoFolder, nlpRecordRepoFolder);
                    return nlpRecordRepoFolder;
                }).collect(Collectors.toList()));
            }
            if (nlpRecordDtoMetadataIdValueList != null) {
                nlpRecord.setMetadataIdValue(nlpRecordDtoMetadataIdValueList.stream().map(nlpRecordDtoMetadataIdValue -> {
                    NlpRecord.MetadataIdValue nlpRecordMetadataIdValue = new NlpRecord.MetadataIdValue();
                    BeanUtils.copyProperties(nlpRecordDtoMetadataIdValue, nlpRecordMetadataIdValue);
                    return nlpRecordMetadataIdValue;
                }).collect(Collectors.toList()));
            }
            List<NlpRecordDto> childNodes = nlpRecordDto.getChildNodes();
            if(CollUtil.isNotEmpty(childNodes)){
                nlpRecord.setChildNodes(convertDto2NlpRecord(childNodes));
            }
            return nlpRecord;
        }).collect(Collectors.toList());
    }

    protected List<NlpRecordResp> convertNlpRecord(List<NlpRecord> nlpRecordList,SearchRecordV2QueryReq req) {
        List<NlpRecordResp> nlpRecordDtoList = new ArrayList<>();
        List<MetadataListQueryItem> metadataList = req.getMetadataList();
        nlpRecordList.forEach(nlpRecord -> {
            NlpRecordResp nlpRecordResp = new NlpRecordResp();
            BeanUtils.copyProperties(nlpRecord, nlpRecordResp);
            List<NlpRecord.RepoFolder> repoFolders = nlpRecord.getRepoFolders();
            List<NlpRecordResp.RepoFolder> repoFolderDtoList = new ArrayList<>();
            nlpRecordResp.setRepoFolders(repoFolderDtoList);
            if (repoFolders != null) {
                repoFolders.forEach(repoFolder -> {
                    NlpRecordResp.RepoFolder repoFolderDto = new NlpRecordResp.RepoFolder();
                    BeanUtils.copyProperties(repoFolder, repoFolderDto);
                    repoFolderDtoList.add(repoFolderDto);
                });
            }
            List<NlpRecordResp.MetadataIdValue> metadataIdValueDtoList = new ArrayList<>();
            nlpRecordResp.setMetadataIdValue(metadataIdValueDtoList);
            List<NlpRecord.MetadataIdValue> metadataIdValueList = nlpRecord.getMetadataIdValue();
            if (metadataIdValueList != null) {
                metadataIdValueList.forEach(metadataIdValue -> {
                    NlpRecordResp.MetadataIdValue metadataIdValueDto = new NlpRecordResp.MetadataIdValue();
                    BeanUtils.copyProperties(metadataIdValue, metadataIdValueDto);
                    metadataIdValueDtoList.add(metadataIdValueDto);
                });
            }
            //元数据高亮
            highLightMetadata(nlpRecordResp,metadataList);
            //附件
            List<NlpRecord> childNodes = nlpRecord.getChildNodes();
            if(CollUtil.isNotEmpty(childNodes)){
                nlpRecordResp.setAttachmentFileList(childNodes.stream().map(childNode -> {
                    NlpRecordResp childNlpRecordResp = new NlpRecordResp();
                    BeanUtils.copyProperties(childNode,childNlpRecordResp);
                    //附件元数据高亮
                    highLightMetadata(childNlpRecordResp,metadataList);
                    return childNlpRecordResp;
                }).collect(Collectors.toList()));
            }
            nlpRecordDtoList.add(nlpRecordResp);
        });
        return nlpRecordDtoList;
    }


    /**
     * 元数据高亮
     * @param nlpRecordResp 响应结果
     * @param metadataListQueryItemList 元数据搜索条件
     */
    private void highLightMetadata(NlpRecordResp nlpRecordResp, List<MetadataListQueryItem> metadataListQueryItemList) {
        if(CollUtil.isEmpty(metadataListQueryItemList)){
            return;
        }
        List<NlpRecordResp.MetadataIdValue> metadataIdValue = nlpRecordResp.getMetadataIdValue();
        if(CollUtil.isEmpty(metadataIdValue)){
            return;
        }
        for (MetadataListQueryItem metadataListQueryItem : metadataListQueryItemList) {
            String metadataName = metadataListQueryItem.getMetadataName();
            List<String> metadataValueList = metadataListQueryItem.getMetadataValue();
            if(CollUtil.isEmpty(metadataValueList)){
                continue;
            }
            for (NlpRecordResp.MetadataIdValue metadataResp : metadataIdValue) {
                if(Objects.equals(metadataName,metadataResp.getMetadataName())){
                    String metadataValue = metadataResp.getMetadataValue();
                    if(StringUtils.isEmpty(metadataValue)){
                        continue;
                    }
                    for (String s : metadataValueList) {
                        if(metadataValue.contains(s)){
                            metadataResp.setMetadataValue(metadataValue.replace(s,LIGHT_PRE_TAGS + s + LIGHT_POST_TAGS));
                        }
                    }
                }
            }
        }
    }

    public PageUtils<NlpRecordResp> convertPlat2NlpRecordPage(Integer total, List<JSONObject> dataList,
            SearchRecordV2QueryReq req) {
        List<NlpRecordResp> nlpRecordList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<MetadataResp> metadataList = metadataSearchService.getMetadataList();
            for (JSONObject data : dataList) {
                NlpRecordResp nlpRecordResp = resolveNlpRecordResp(data, metadataList);
                if(nlpRecordResp != null){
                    nlpRecordList.add(nlpRecordResp);
                }
            }
        }
        return new PageUtils<>(nlpRecordList, total, req.getPage(), req.getPageSize());
    }

    private NlpRecordResp resolveNlpRecordResp(JSONObject data,List<MetadataResp> metadataList){
        try {
            if(data == null){
                return null;
            }
            NlpRecordResp nlpRecord = new NlpRecordResp();
            nlpRecord.setRecordId(data.getLong("recordId"));
            nlpRecord.setRecordName(data.getString("recordName"));
            nlpRecord.setTitle(data.getString("title"));
            String summary = data.getString("summary");
            String content = data.getString("content");
            nlpRecord.setDigest(summary);
            if (StringUtils.isNotEmpty(content) && content.length() > CONTENT_LENGTH) {
                nlpRecord.setContent(content.substring(0, CONTENT_LENGTH));
            } else {
                nlpRecord.setContent(content);
            }
            nlpRecord.setCategoryIds(data.getList("categoryIds", Long.class));
            nlpRecord.setTenantId(data.getString("tenantId"));
            nlpRecord.setReleaseTime(data.getLong("releaseTime"));
            nlpRecord.setVisitNum(data.getLong("visitNum"));
            nlpRecord.setClassified(data.getInteger("classified"));
            nlpRecord.setDocId(data.getLong("docId"));
            try {
                //附件
                JSONArray childNodes = data.getJSONArray("childNodes");
                if(childNodes != null){
                    List<NlpRecordResp> attachmentList = new ArrayList<>();
                    for (Object childNode : childNodes) {
                        JSONObject childNodeJson = JSON.parseObject(JSON.toJSONString(childNode));
                        NlpRecordResp childRecordResp = resolveNlpRecordResp(childNodeJson, metadataList);
                        if(childRecordResp != null){
                            attachmentList.add(childRecordResp);
                        }
                    }
                    nlpRecord.setAttachmentFileList(attachmentList);
                }
                List<NlpRecordResp.RepoFolder> repoFolders = data.getList("repoFolders",
                        NlpRecordResp.RepoFolder.class);
                if (repoFolders != null) {
                    nlpRecord.setRepoFolders(repoFolders);
                } else {
                    List<Long> repoIds = data.getList("repoIds", Long.class);
                    if (!CollectionUtils.isEmpty(repoIds)) {
                        nlpRecord.setRepoFolders(repoIds.stream().filter(Objects::nonNull).map(repoId -> {
                            NlpRecordResp.RepoFolder nlpRepoFolder = new NlpRecordResp.RepoFolder();
                            nlpRepoFolder.setRepoId(repoId);
                            return nlpRepoFolder;
                        }).collect(Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                log.error("调用AI中台搜索解析库数据异常,{}", JSON.toJSONString(data), e);
            }
            List<NlpRecordResp.MetadataIdValue> metadataIdValueList = new ArrayList<>();
            nlpRecord.setMetadataIdValue(metadataIdValueList);
            //解析元数据
            for (MetadataResp metadata : metadataList) {
                if (data.get(metadata.getName()) != null) {
                    Object o = data.get(metadata.getName());
                    if (o != null) {
                        NlpRecordResp.MetadataIdValue metadataIdValue = new NlpRecordResp.MetadataIdValue();
                        metadataIdValue.setMetadataId(metadata.getId());
                        metadataIdValue.setMetadataName(metadata.getName());
                        metadataIdValue.setMetadataValue(o.toString());
                        metadataIdValueList.add(metadataIdValue);
                    }
                }
            }
            return nlpRecord;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 判断是否附件搜索
     */
    public boolean checkAttachmentSearch(SearchRecordV2QueryReq req){
        //附件搜索开关
        boolean enableAttachmentSearch = recordSearchConfig.isEnableAttachmentSearch();
        //最终是否要执行附件搜索
        boolean attachmentSearchSwitch = false;
        if(enableAttachmentSearch){
            attachmentSearchSwitch = req.getAttachmentSearch() != null && req.getAttachmentSearch();
        }
        //有搜索词才走附件搜索
        return attachmentSearchSwitch && req.hasKeyword();
    }
}
