package com.suwell.plss.plugin.seeyon.abutment;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.multipart.PlssMultipartFile;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.plugin.eod.mapper.EdocSummaryMapper;
import com.suwell.plss.plugin.eod.mapper.OrgUnitMapper;
import com.suwell.plss.record.model.RpcFolderAddReq;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.service.FolderRpcService;
import com.suwell.plss.record.service.HSRecordRpcService;
import com.suwell.plss.record.service.RecordRpcService;
import com.suwell.plss.record.service.RepositoryRpcService;
import com.suwell.plss.record.standard.dto.request.FileInfoBaseV3Req;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineV3Req;
import com.suwell.plss.record.standard.dto.request.FolderMultiRecordReq;
import com.suwell.plss.record.standard.dto.request.RecordOfflineV3Req;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.FolderListResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.commons.CommonsMultipartFile;


/**
 * 致远公文同步服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/19 11:10
 */
@Slf4j
@Service
public class DocumentSycService {

    LoadingCache<String, OrgUnit> orgUnitCache = CacheBuilder.newBuilder()
            .concurrencyLevel(8)
            .maximumSize(1000)
            .expireAfterWrite(120, TimeUnit.SECONDS)
            .refreshAfterWrite(60, TimeUnit.SECONDS)
            .build(new CacheLoader<String, OrgUnit>() {
                @Override
                public OrgUnit load(String key) {
                    OrgUnit orgUnit = orgUnitMapper.selectOne(Wrappers.<OrgUnit>lambdaQuery()
                            .eq(OrgUnit::getPath, key)
                            .eq(OrgUnit::getIsEnable, 1)
                            .eq(OrgUnit::getIsDeleted, 0));
                    return orgUnit;
                }
            });


    @Value("${plugin.document.sync.config:''}")
    private String configStr;

    @Resource
    private RedisService redisService;

    @Resource
    private WFGIdGenerator wfgIdGenerator;

    @Resource
    private RecordRpcService recordRpcService;

    @Resource
    private FileManageRpcService fileManageRpcService;

    @Resource
    private EdocSummaryMapper edocSummaryMapper;

    @Resource
    private OrgUnitMapper orgUnitMapper;

    @Resource
    private RepositoryRpcService repositoryRpcService;

    @Resource
    private FolderRpcService folderRpcService;

    @Resource
    private HSRecordRpcService hsRecordRpcService;


    /**
     * 因致远对接方不愿采用开放平台对接方式，可能另算费用，故采用token方式对接
     *
     * @return
     */
    public String generateToken(String appId) {
        if (!getConfig().getInnerAppId().equals(appId)) {
            throw new RuntimeException("appId不正确");
        }
        long id = wfgIdGenerator.next();
        String token = Base62.encode(String.valueOf(id));
        redisService.setCacheObject(AbutmentConstants.REDIS_TOKEN_PREFIX + token, token,
                getConfig().getInnerTokenExpire() * 60, TimeUnit.SECONDS);
        return token;
    }

    public void addOnline(String token, JSONObject json) {
        String cacheToken = redisService.getCacheObject(AbutmentConstants.REDIS_TOKEN_PREFIX + token);
        if (StringUtils.isEmpty(cacheToken)) {
            throw new RuntimeException("token已过期");
        }
        Long recordId = addOnlineUploadV3(json);
        EdocSummary summary = JSON.parseObject(json.toJSONString(), EdocSummary.class);

        summary.setFileId(recordId);
        summary.setSyncStatus(AbutmentConstants.SummarySyncStatusEnum.SYNC_API.getCode());
        edocSummaryMapper.insert(summary);
    }


    /**
     * 处理映射
     *
     * @param json
     * @return
     */
    public String processMetadata(JSONObject json) {
        Map<String, String> map = new HashMap<>();
        for (AbutmentConstants.DocumentStandardMetadataEnum value : AbutmentConstants.DocumentStandardMetadataEnum.values()) {
            String metadataValue = value.convert(json);
            if (StringUtils.isNotBlank(metadataValue)) {
                map.put(value.getName(), metadataValue);
            }
        }
        return JSON.toJSONString(map);
    }

    @Cached(name = AbutmentConstants.REDIS_TOKEN_PREFIX + "huangshi",
            expire = 15, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    private String getAccessToken() {
        //判断token是否存在，是否过期
        OkHttpUtils okHttpUtils = OkHttpUtils.builder();
        Response res = okHttpUtils.url(getConfig().getTokenUrl() + getConfig().getAppId()).get().syncRes();
        if (HttpStatus.OK.value() == res.code()) {
            try {
                return res.body().string();
            } catch (IOException e) {
                log.error("获取token失败");
            }
        }
        log.info("获取token失败");
        throw new RuntimeException("获取token失败");
    }

    /**
     * 存量数据导入任务
     * 只能在指定时间段内执行 致远反馈只能晚上去调用他们，怕影响他们系统
     */
    @XxlJob("seeyonDocumentSyncTask")
    public void task() {
        int count = 0;
        int failCount = 0;
        LocalTime taskStartTime = LocalTime.parse(getConfig().getTaskStartTime(),
                DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime taskEndTime = LocalTime.parse(getConfig().getTaskEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));

        LambdaQueryWrapper<EdocSummary> queryWrapper = Wrappers.<EdocSummary>lambdaQuery()
                .eq(EdocSummary::getSyncStatus, AbutmentConstants.SummarySyncStatusEnum.SYNC_UNDO.getCode());
        //拉取指定时间段的文档
        if (StringUtils.isNotBlank(getConfig().getDocStartTime()) && StringUtils.isNotBlank(
                getConfig().getDocEndTime())) {
            queryWrapper.between(EdocSummary::getCreateTime,
                    LocalDateTime.parse(getConfig().getDocStartTime(), DatePattern.NORM_DATETIME_FORMATTER),
                    LocalDateTime.parse(getConfig().getDocEndTime(), DatePattern.NORM_DATETIME_FORMATTER));
        }
        queryWrapper.orderByDesc(EdocSummary::getCreateTime).last("limit 1");
        while (enableTimePeriod(taskStartTime, taskEndTime)) {
            EdocSummary edocSummary = edocSummaryMapper.selectOne(queryWrapper);
            if (edocSummary == null) {
                log.info("未查询到公文数据，无存量数据!!!");
                break;
            }
            try {
                JSONObject json = JSON.parseObject(JSON.toJSONString(edocSummary));
                Long recordId = addOnlineUploadV3(json);
                count++;
                log.info("导入存量公文数据 {}", json);
                edocSummary.setSyncStatus(AbutmentConstants.SummarySyncStatusEnum.SYNC_DONE.getCode());
                edocSummary.setFileId(recordId);
            } catch (Exception e) {
                edocSummary.setSyncStatus(AbutmentConstants.SummarySyncStatusEnum.SYNC_ERROR.getCode());
                failCount++;
                log.error("导入存量公文数据失败 {}", edocSummary.getId(), e);
            }
            edocSummaryMapper.updateById(edocSummary);
            Long pullPeriod = getConfig().getPullPeriod();
            if (pullPeriod != null && pullPeriod > 0) {
                try {
                    TimeUnit.SECONDS.sleep(pullPeriod);
                } catch (InterruptedException e) {
                    log.error("thread sleep error", e);
                }
            }
        }
        log.info("本轮导入存量公文数据完成，导入成功{}条 失败{}条", count, failCount);
    }

    /**
     * 判断是否在指定时间段内
     */
    public boolean enableTimePeriod(LocalTime start, LocalTime end) {
        LocalTime now = LocalTime.now();
        return now.isAfter(start) && now.isBefore(end);
    }


    private Long addOnlineUploadV3(JSONObject json) {
        Long id = json.getLong("id");
        if (id == null) {
            throw new RuntimeException("id不能为空");
        }
        long recordId = wfgIdGenerator.next();
        RecordOfflineV3Req recordOfflineV3Req = new RecordOfflineV3Req();
        recordOfflineV3Req.setOrigin(1);
        recordOfflineV3Req.setUserId(getConfig().getUserId());
        recordOfflineV3Req.setRealOrigin(1);
        recordOfflineV3Req.setPlanId(getConfig().getPlanId());
        recordOfflineV3Req.setTenantId(getConfig().getTenantId());
        FileInfoOfflineV3Req fileInfoOfflineV3Req = new FileInfoOfflineV3Req();
        fileInfoOfflineV3Req.setRecordId(recordId);
        fileInfoOfflineV3Req.setRepoIdList(getRepoId(json));
        String title = json.getString(AbutmentConstants.DocumentStandardMetadataEnum.BT.getFieldName());
        List<FileInfoBaseV3Req> fileInfoBaseV3Reqs = reqFileInfo(id, title);
        log.info("文件信息 {}", JSON.toJSONString(fileInfoBaseV3Reqs));
        FileInfoBaseV3Req masterFile = fileInfoBaseV3Reqs.stream().filter(fileInfo ->
                fileInfo.getCtype()
                        .equals(AbutmentConstants.DocumentFileTypeEnum.FILE_TYPE_MASTER.getRecordFileType().getCode())
        ).findFirst().get();
        String fileMd5 = masterFile.getFileMd5();
        R<Long> repoR = hsRecordRpcService.queryRecordByMd5(fileMd5);
        if (!repoR.isSuccess()) {
            throw new RuntimeException("查询文件md5失败");
        }
        Long masterRecordId = repoR.getData();
        if (masterRecordId != null) {
            FolderMultiRecordReq folderMultiRecordReq = new FolderMultiRecordReq();
            folderMultiRecordReq.setFolderOrRepoIdList(fileInfoOfflineV3Req.getRepoIdList());
            folderMultiRecordReq.setRecordIdList(Arrays.asList(masterRecordId));
            folderMultiRecordReq.setUserId(getConfig().getUserId());
            folderMultiRecordReq.setValidPass(true);
            R<Void> r = hsRecordRpcService.addMultiFolders(folderMultiRecordReq);
            if (!r.isSuccess()) {
                throw new RuntimeException("添加文件位置失败");
            }
            log.info("文件已存在，添加文件位置");
            return masterRecordId;
        }
        fileInfoOfflineV3Req.setMetadata(processMetadata(json));
        fileInfoOfflineV3Req.setFileInfoBaseReqList(fileInfoBaseV3Reqs);
        recordOfflineV3Req.setFileInfoReqList(Lists.newArrayList(fileInfoOfflineV3Req));
        R<Void> voidR = recordRpcService.addOnlineUploadV3(recordOfflineV3Req);
        if (!voidR.isSuccess()) {
            throw new RuntimeException("rpc调用入库失败");
        }
        log.info("rpc调用入库参数：{}", JSON.toJSONString(recordOfflineV3Req));
        return recordId;
    }

    /**
     * 入库位置映射
     */
    private List<Long> getRepoId(JSONObject json) {
        //发文单位
        String sendUnitId = json.getString("sendUnitId");
        //主送单位
        String sendToId = json.getString("receiveUnitId");
        //抄送单位
        String copyToId = json.getString("copyToId");
        List<Long> ids = Lists.newArrayList();
        log.info("发文单位：{} 主送单位：{} 抄送单位：{}", sendUnitId, sendToId, copyToId);
        ids.addAll(getDeptId(sendUnitId, AbutmentConstants.SendReceiveTypeEnum.SEND));
        log.info("发文单位id {}", ids);
        ids.addAll(getDeptId(sendToId, AbutmentConstants.SendReceiveTypeEnum.RECEIVE));
        log.info("主送单位id {}", ids);
        ids.addAll(getDeptId(copyToId, AbutmentConstants.SendReceiveTypeEnum.RECEIVE));
        log.info("抄送单位id {}", ids);
        if (CollUtil.isNotEmpty(ids)) {
            return ids;
        }
        Long orgDepartmentId = json.getLong("orgDepartmentId");
        log.info("取默认位置 orgDepartmentId {}", orgDepartmentId);
        if (orgDepartmentId != null) {
            Long gwkFolderId = getGWKFolderId(orgDepartmentId, null);
            if (gwkFolderId != null) {
                return Arrays.asList(gwkFolderId);
            }
        }
        log.info("参数配置默认位置 {}", getConfig().getDefaultRepoId());
        return Arrays.asList(getConfig().getDefaultRepoId());
    }

    /**
     * 示例数据
     * Department|1917594445944145480,Department|-6496162752065352890
     * Account|-5385948282049574866
     *
     * @param deptIds
     * @return
     */
    private List<Long> getDeptId(String deptIds, AbutmentConstants.SendReceiveTypeEnum type) {
        List<Long> ids = Lists.newArrayList();
        if (StringUtils.isNotBlank(deptIds)) {
            String[] deptStr = deptIds.split(",");
            if (deptStr != null) {
                for (String s : deptStr) {
                    String[] dept = s.split("\\|");
                    if (dept != null && dept.length == 2) {
                        String deptId = dept[1];
                        //根据部门名称查询部门id
                        Long gwkFolderId = getGWKFolderId(Long.parseLong(deptId), type);
                        if (gwkFolderId != null) {
                            ids.add(gwkFolderId);
                        }
                    }
                }
            }
        }
        return ids;
    }

    /**
     * 反查公文库收发文文件夹id，没有查到就创建
     *
     * @param deptId
     * @param type
     * @return
     */
    public Long getGWKFolderId(Long deptId, AbutmentConstants.SendReceiveTypeEnum type) {
        Long resultId = null;
        OrgUnit orgUnit = orgUnitMapper.selectById(deptId);
        if (orgUnit != null) {
            List<String> orgOrDeptNameList = Lists.newArrayList();
            orgOrDeptNameList.add(orgUnit.getName());
            if (!AbutmentConstants.DeptTypeEnum.ORG.getCode().equals(orgUnit.getType())) {
                getOrgNameByDeptPath(orgUnit.getPath(), orgOrDeptNameList);
            }
            Collections.reverse(orgOrDeptNameList);
            //根据名称找单位对应的库id
            R<Map<String, Long>> mapR = repositoryRpcService.queryRepoInfoMap(Arrays.asList(
                    orgOrDeptNameList.get(0)));
            Map<String, Long> data = mapR.getData();
            if (!mapR.isSuccess() || data == null || data.get(orgOrDeptNameList.get(0)) == null) {
                log.error("查询单位对应的库信息失败，deptId:{}", deptId);
                throw new RuntimeException("查询单位对应的库信息失败");
            }
            Long parentId = data.get(orgOrDeptNameList.get(0));
            Long repoId = data.get(orgOrDeptNameList.get(0));
            for (int i = 1; i < orgOrDeptNameList.size(); i++) {
                String orgOrDeptName = orgOrDeptNameList.get(i);
                R<List<FolderListResp>> r = folderRpcService.queryChildrenFolder(parentId);
                boolean findChild = false;
                if (r.isSuccess()) {
                    List<FolderListResp> childFolder = r.getData();
                    if (CollUtil.isNotEmpty(childFolder)) {
                        for (FolderListResp folderListResp : childFolder) {
                            if (folderListResp.getName().equals(orgOrDeptName)) {
                                parentId = folderListResp.getId();
                                findChild = true;
                                break;
                            }
                        }
                    }
                } else {
                    log.error("RPC查询文件夹失败，parentId:{}", parentId);
                }
                //如果任意一层没有找到则循环创建并且返回最后一层的文件夹ID
                if (!findChild) {
                    parentId = recursionCreateFolder(parentId, i, orgOrDeptNameList, repoId);
                    break;
                }
            }

            if (type == null) {
                return parentId;
            }

            //查询收发文的文件夹
            R<List<FolderListResp>> r = folderRpcService.queryChildrenFolder(parentId);
            if (r.isSuccess()) {
                boolean find = false;
                List<FolderListResp> folderList = r.getData();
                if (CollUtil.isNotEmpty(folderList)) {
                    for (FolderListResp folder : folderList) {
                        if (type.getDesc().equals(folder.getName())) {
                            resultId = folder.getId();
                            find = true;
                            break;
                        }
                    }
                }
                //没有找到收发文的文件夹就创建
                if (!find) {
                    RpcFolderAddReq rpcFolderAddReq = new RpcFolderAddReq();
                    rpcFolderAddReq.setFolderName(type.getDesc());
                    rpcFolderAddReq.setTenantId(getConfig().getTenantId());
                    rpcFolderAddReq.setRepoId(repoId);
                    rpcFolderAddReq.setParentId(parentId);
                    rpcFolderAddReq.setStatus(RecordEnum.EnableSwitchEnum.ENABLE.getCode());
                    rpcFolderAddReq.setOwnerId(getConfig().getUserId());
                    rpcFolderAddReq.setOrderby(1);
                    R<Long> addResult = folderRpcService.rpcAddFolder(rpcFolderAddReq);
                    resultId = addResult.getData();
                }
            }
        }
        return resultId;
    }

    /**
     * 循环创建部门文件夹
     *
     * @param parentId
     * @param index
     * @param orgOrDeptNameList
     * @return
     */
    private Long recursionCreateFolder(Long parentId, int index, List<String> orgOrDeptNameList, Long repoId) {
        for (int i = index; i < orgOrDeptNameList.size(); i++) {
            String orgOrDeptName = orgOrDeptNameList.get(i);
            RpcFolderAddReq rpcFolderAddReq = new RpcFolderAddReq();
            rpcFolderAddReq.setRepoId(repoId);
            rpcFolderAddReq.setFolderName(orgOrDeptName);
            rpcFolderAddReq.setTenantId(getConfig().getTenantId());
            rpcFolderAddReq.setParentId(parentId);
            rpcFolderAddReq.setOrderby(1);
            rpcFolderAddReq.setStatus(RecordEnum.EnableSwitchEnum.ENABLE.getCode());
            rpcFolderAddReq.setOwnerId(getConfig().getUserId());
            R<Long> addResult = folderRpcService.rpcAddFolder(rpcFolderAddReq);
            if (!addResult.isSuccess() && addResult.getData() == null) {
                throw new RuntimeException("创建文件夹失败");
            }
            parentId = addResult.getData();
        }
        return parentId;
    }

    /**
     * 获取所在单位名称
     *
     * @param deptPath
     * @return
     */
    private void getOrgNameByDeptPath(String deptPath, List<String> orgOrDeptNameList) {
        if (deptPath == null || deptPath.length() < 4) {
            return;
        }
        String parentPath = deptPath.substring(0, deptPath.length() - 4);
        try {
            OrgUnit orgUnit = orgUnitCache.get(parentPath);
            if (AbutmentConstants.DeptTypeEnum.ORG.getCode().equals(orgUnit.getType())) {
                orgOrDeptNameList.add(orgUnit.getName());
            } else if (AbutmentConstants.DeptTypeEnum.DEPT.getCode().equals(orgUnit.getType())) {
                orgOrDeptNameList.add(orgUnit.getName());
                getOrgNameByDeptPath(orgUnit.getPath(), orgOrDeptNameList);
            }
        } catch (ExecutionException e) {
            throw new RuntimeException("获取单位名称失败");
        }
    }


    /**
     * 拉取文件信息
     *
     * @param documentId
     * @return
     */
    private List<FileInfoBaseV3Req> reqFileInfo(Long documentId, String title) {
        String token = getAccessToken();
        String body = OkHttpUtils.builder().url(getConfig().getDocumentInfoUrl())
                .addParam(AbutmentConstants.REQ_PARAM_MODULE_ID, documentId)
                .addParam(AbutmentConstants.REQ_PARAM_TOKEN, token).get().sync();
        JSONObject res = JSON.parseObject(body);
        log.info("拉取文件信息返回结果：{}", body);
        List<FileInfoBaseV3Req> fileInfoBaseReqList = Lists.newArrayList();
        if (AbutmentConstants.RES_CODE_SUCCESS.equals(res.getInteger(AbutmentConstants.RES_CODE))) {
            JSONArray jsonArray = res.getJSONArray(AbutmentConstants.RES_DATA);
            boolean hasMasterFile = false;
            if (CollUtil.isNotEmpty(jsonArray)) {
                //判断是否有主件，没有主件直接报异常
                for (Object o : jsonArray) {
                    JSONObject json = (JSONObject) o;
                    String type = json.getString("type");
                    if (AbutmentConstants.DocumentFileTypeEnum.FILE_TYPE_MASTER.getSeeyonFileType().equals(type)) {
                        hasMasterFile = true;
                    }
                }
                if (hasMasterFile) {
                    for (Object o : jsonArray) {
                        JSONObject json = (JSONObject) o;
                        String type = json.getString("type");
                        Long fileId = json.getLong(AbutmentConstants.RES_FILE_ID);
                        FileInfoBaseV3Req req = getFileData(fileId, type, title);
                        fileInfoBaseReqList.add(req);
                    }
                }
                log.info("文件拉取处理完成：{}", JSON.toJSONString(fileInfoBaseReqList));
                return fileInfoBaseReqList;
            }
        }
        throw new RuntimeException("获取文件信息失败");
    }


    /**
     * 根据文件ID下载文件
     *
     * @param fileId
     * @param fileType
     * @return
     */
    private FileInfoBaseV3Req getFileData(Long fileId, String fileType, String title) {
        String token = getAccessToken();
        String body = OkHttpUtils.builder().url(getConfig().getDocumentFileUrl())
                .addParam(AbutmentConstants.REQ_PARAM_FILE_ID, fileId)
                .addParam(AbutmentConstants.REQ_PARAM_TOKEN, token).get().sync();
        JSONObject res = JSON.parseObject(body);
        if (AbutmentConstants.RES_CODE_SUCCESS.equals(res.getInteger(AbutmentConstants.RES_CODE))) {
            JSONArray jsonArray = res.getJSONArray(AbutmentConstants.RES_DATA);
            log.info("下载文件列表:{}", jsonArray.size());
            if (CollUtil.isNotEmpty(jsonArray)) {
                for (Object o : jsonArray) {
                    JSONObject json = (JSONObject) o;
                    String fileName = json.getString(AbutmentConstants.RES_FILENAME);
                    String fileData = json.getString(AbutmentConstants.RES_FILE_DATA);
                    //因为致远接口返回的文件名称全部为正文.xxx 所以需要替换为原文件名为 标题.xxx
                    if (AbutmentConstants.DocumentFileTypeEnum.FILE_TYPE_MASTER.getSeeyonFileType().equals(fileType)) {
                        fileName = cutTitleWithUtf8(title, 250) + fileName.substring(fileName.indexOf("."));
                    }

                    log.info("开始解码文件：{}", json.getString(AbutmentConstants.RES_FILENAME));
                    byte[] fileBytes = Base64.decode(fileData);
                    String parentPath = getConfig().getTempPath() + wfgIdGenerator.next();
                    log.info("开始写入文件：{}", parentPath + File.separator + fileName);
                    File file = FileUtil.writeBytes(fileBytes, parentPath + File.separator + fileName);
                    log.info("开始上传文件：{} 文件大小：{}", parentPath + File.separator + fileName, file.length());
                    MultipartFile multipartFile = buildMultipartFile(file, fileName);
                    FileInfoBaseV3Req fileInfoBaseV3Req = null;
                    try {
                        fileInfoBaseV3Req = buildFileInfoBaseV3Req(multipartFile,
                                AbutmentConstants.DocumentFileTypeEnum.getRecordFileTypeBySeeyonFileType(fileType),
                                fileName);
                    } catch (Exception e) {
                        throw e;
                    } finally {
                        FileUtil.del(file);
                        FileUtil.del(parentPath);
                    }
                    log.info("解码文件结束：{}", res.getString(AbutmentConstants.RES_FILENAME));
                    return fileInfoBaseV3Req;
                }
            }
        }
        log.info("调用致远下载文件失败：{}", res.toJSONString());
        throw new RuntimeException("调用致远下载文件失败");
    }

    /**
     * 构建RPC入库参数信息
     *
     * @param file
     * @param ctype
     * @return
     */
    private FileInfoBaseV3Req buildFileInfoBaseV3Req(MultipartFile file, Integer ctype, String fileName) {
        if (Objects.nonNull(file)) {
            FileInfoBaseV3Req fileInfoBaseV3Req = new FileInfoBaseV3Req();
            fileInfoBaseV3Req.setFileName(fileName);
            UploadReq uploadReqCnt = new UploadReq();
            uploadReqCnt.setFileData(file);
            uploadReqCnt.setOriginName(fileInfoBaseV3Req.getFileName());
            uploadReqCnt.setRequireEncrypt(true);
            R<UploadResp> uploadRespR = fileManageRpcService.generalUpload(uploadReqCnt);
            if (uploadRespR.isSuccess()) {
                fileInfoBaseV3Req.setFileId(uploadRespR.getData().getId());
                fileInfoBaseV3Req.setCtype(ctype);
                fileInfoBaseV3Req.setFileMd5(uploadRespR.getData().getMd5Hex());
                return fileInfoBaseV3Req;
            }
        }
        return null;
    }


    /**
     * 构建MultipartFile文件上传对象
     *
     * @param file
     * @param fileName
     * @return
     */
    private MultipartFile buildMultipartFile(File file, String fileName) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.APPLICATION_OCTET_STREAM_VALUE
                , true
                , fileName);
        try (InputStream input = new FileInputStream(file);
                OutputStream os = item.getOutputStream()) {
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }
        return new PlssMultipartFile(item);
    }

    private SeeyonDucumenSyncConfig getConfig() {
        return JSON.parseObject(configStr, SeeyonDucumenSyncConfig.class);
    }


    /**
     * 根据UTF-8编码截取字符串 保证截取后的字符串不乱码
     *
     * @param title
     * @param maxLength
     * @return
     */
    private String cutTitleWithUtf8(String title, int maxLength) {
        int position = 0;
        for (int i = 0; i < title.length(); i++) {
            char c = title.charAt(i);
            int length = String.valueOf(c).getBytes(StandardCharsets.UTF_8).length;
            if (position + length > maxLength) {
                return title.substring(0, i);
            }
            position += length;
        }
        return title;
    }


}
