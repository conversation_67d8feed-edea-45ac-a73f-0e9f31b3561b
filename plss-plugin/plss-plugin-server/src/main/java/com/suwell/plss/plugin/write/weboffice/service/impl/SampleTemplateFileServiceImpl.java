package com.suwell.plss.plugin.write.weboffice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.plugin.api.enums.PluginBizError;
import com.suwell.plss.plugin.write.weboffice.constant.WebOfficeRedisKeyConstants;
import com.suwell.plss.plugin.write.weboffice.domain.base.ModuleParam;
import com.suwell.plss.plugin.write.weboffice.domain.request.GetOnlineUrlReq;
import com.suwell.plss.plugin.write.weboffice.domain.request.WofGetFileVerReq;
import com.suwell.plss.plugin.write.weboffice.domain.request.WofHistoryReq;
import com.suwell.plss.plugin.write.weboffice.domain.request.WofOnnotifyReq;
import com.suwell.plss.plugin.write.weboffice.domain.resp.WofFileHistoryResp;
import com.suwell.plss.plugin.write.weboffice.domain.resp.WofVersionFileResp;
import com.suwell.plss.plugin.write.weboffice.dto.FileInfoDTO;
import com.suwell.plss.plugin.write.weboffice.dto.WofSaveFileDTO;
import com.suwell.plss.plugin.write.weboffice.enums.WebOfficeModule;
import com.suwell.plss.record.model.RpcRecordRenameReq;
import com.suwell.plss.record.service.DocumentPersonalRpcService;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.service.RecordRpcService;
import com.suwell.plss.record.service.SampleTemplateRpcService;
import com.suwell.plss.record.standard.dto.request.BatchGetFileInfoReq;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.request.UploadFileToPersonalReq;
import com.suwell.plss.record.standard.dto.response.DocumentFileResp;
import com.suwell.plss.record.standard.dto.response.SampleTemplateFileResp;
import com.suwell.plss.record.standard.dto.response.UploadFileToPersonalResp;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * ==========================
 * 开发：wei.wang
 * 创建时间：2023-03-02 17:10
 * 版本：1.0
 * 描述：标准文件服务
 * ==========================
 */
@Slf4j
@Component
@RefreshScope
public class SampleTemplateFileServiceImpl extends AbstractProcessor {

    @Resource
    private FileManageRpcService fileService;
    @Resource
    private RecordRpcService recordRpcService;
    @Resource
    private SampleTemplateRpcService sampleTemplateRpcService;
    @Resource
    private DocumentPersonalRpcService documentPersonalRpcService;

    @Override
    protected String getModuleTag() {
        return WebOfficeModule.SAMPLETE_TEMPLATE.getTag();
    }

    @Override
    protected ModuleParam loadModuleParams(GetOnlineUrlReq req) {
        Long sgFileId = req.getSgFileId();
        BatchGetFileInfoReq getFileInfoReq = new BatchGetFileInfoReq();
        getFileInfoReq.addId(sgFileId);
        R<SampleTemplateFileResp> fileRespR = sampleTemplateRpcService.getFileById(sgFileId);
        AssertUtils.isTrue(fileRespR.isSuccess(), PluginBizError.WOF_GET_FILE_RECORD_EMPTY);
        SampleTemplateFileResp fileResp = fileRespR.getData();
        AssertUtils.isTrue(fileResp != null, PluginBizError.WOF_GET_FILE_RECORD_EMPTY);
        // 每次产生新的id 避免形成多人编辑共享的场景
        long newDocId = idGenerator.next();
        // 每次产生新的id 避免形成多人编辑共享的场景
        redisService.setCacheObject(WebOfficeRedisKeyConstants.getVirtualRedisKey(newDocId), sgFileId, 6 * 60 * 60L, TimeUnit.SECONDS);
        ModuleParam param = new ModuleParam();
        param.setFileId(String.valueOf(newDocId));
        param.setSourceFileId(String.valueOf(sgFileId));
        param.setFileName(fileResp.getFileName());
        return param;

    }

    @Override
    public WofVersionFileResp getFileVersion(WofGetFileVerReq in) {
        Long docId = Long.valueOf(in.getFileId());
        R<DocumentFileResp> mainFileR = recordRpcService.getMainFile(docId);
        if (!mainFileR.isSuccess()) {
            throw new BizException(PluginBizError.WOF_GET_FILE_RECORD_EMPTY);
        }
        return new WofVersionFileResp();
    }

    @Override
    public void rename(String fileId, String newName, String moduleTag) {
        Long docId = Long.valueOf(fileId);
        R<DocumentFileResp> mainFileR = recordRpcService.getMainFile(docId);
        if (mainFileR.isError()) {
            throw new BizException(PluginBizError.WOF_GET_FILE_RECORD_EMPTY);
        }
        if (mainFileR.getData() == null) {
            return;
        }
        RpcRecordRenameReq renameReq = new RpcRecordRenameReq();
        renameReq.setId(docId);
        renameReq.setName(newName);
        recordRpcService.rename(renameReq);
    }

    @Override
    public WofFileHistoryResp findFileHistory(WofHistoryReq req) {
        return new WofFileHistoryResp();
    }

    @Override
    public void onNotify(WofOnnotifyReq in) {
        String cmd = in.getCmd();
        JSONObject body = in.getBody();
        log.info("cmd:{}, 回调通知:{}", cmd, body);
    }

    @Override
    protected FileInfoDTO internalGetFileInfo(String fileId, Integer version, Boolean insideTemp) {
        // 范文模板文件
        // 查询数据库是否包含文档/没有的话返回redis缓存中的虚拟文档
        Long sgFileId = redisService.get(WebOfficeRedisKeyConstants.getVirtualRedisKey(fileId), Long.class);
        R<SampleTemplateFileResp> fileRespR = sampleTemplateRpcService.getFileById(sgFileId);
        AssertUtils.isTrue(fileRespR.isSuccess() && fileRespR.getData() != null, PluginBizError.WOF_GET_FILE_RECORD_EMPTY);
        SampleTemplateFileResp data = fileRespR.getData();
        FileInfoDTO dto = new FileInfoDTO();
        dto.setVersion(1);
        dto.setFileId(fileId);
        dto.setFileName(data.getFileName());
        dto.setFileSize(data.getFileSize().intValue());
        FileTempUrlReq urlReq = new FileTempUrlReq();
        urlReq.setFileId(data.getOriginalFileId());
        urlReq.setDuration(5 * 60);
        dto.setDownloadUrl(fileService.tempUrl(urlReq).getData());
        dto.setCreateTime(data.getCreateTime());
        dto.setUpdateTime(data.getUpdateTime());
        return dto;
    }

    @Override
    protected FileInfoDTO internalSaveFile(WofSaveFileDTO in) {
        Long fileId = Long.valueOf(in.getFileId());
        UploadFileToPersonalReq req = new UploadFileToPersonalReq();
        req.setId(fileId);
        req.setUserId(in.getUid());
        req.setOrigin("weboffice");
        MultipartFile file = in.getFile();
        String fileName = file.getOriginalFilename();
        long size = file.getSize();
        req.setFileName(fileName);
        FileInfoDTO fileInfo = new FileInfoDTO();
        try {
            req.setBytes(file.getBytes());
            req.setPath("我的文档");
            R<UploadFileToPersonalResp> fileRespR = documentPersonalRpcService.uploadFileToPersonalRepo(req);
            if (fileRespR.isError()) {
                throw new BizException(PluginBizError.WOF_SAVE_FILE_ERROR);
            }
            UploadFileToPersonalResp resp = fileRespR.getData();
            fileInfo.setFileId(in.getFileId());
            fileInfo.setFileName(fileName);
            fileInfo.setFileSize(Math.toIntExact(size));
            fileInfo.setDownloadUrl(resp.getWebUrl());
            fileInfo.setVersion(1);
            LocalDateTime now = LocalDateTime.now();
            fileInfo.setCreateTime(now);
            fileInfo.setUpdateTime(now);
        } catch (IOException e) {
            log.error("上传文件异常, in:{}", in, e);
            throw new BizException(PluginBizError.WOF_SAVE_FILE_ERROR);
        }
        return fileInfo;
    }

}
