package com.suwell.plss.system.api.service;

import com.suwell.plss.framework.common.constant.ServiceNameConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.system.api.domain.request.UserBehaviorLogReq;
import com.suwell.plss.system.api.fallback.UserBehaviorLogRpcServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 组织机构服务
 *
 * <AUTHOR>
 * @date 202405-15
 */
@FeignClient(contextId = "userBehaviorLogRpcService", value = ServiceNameConstants.SYSTEM_SERVICE,
        path = UserBehaviorLogRpcService.INNER_PREFIX, fallbackFactory = UserBehaviorLogRpcServiceFallbackFactory.class)
public interface UserBehaviorLogRpcService {

    String INNER_PREFIX = "/inner/userBehaviorLogRpcService";

    @PostMapping("/save")
    R<Void> saveUserBehaviorLog(@RequestBody List<UserBehaviorLogReq> userBehaviorLogReqList);

}
