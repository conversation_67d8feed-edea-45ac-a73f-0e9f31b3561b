package com.suwell.plss.system.service;

import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.system.dto.request.CollectDataReq;
import com.suwell.plss.system.dto.request.DetailStatisticsReq;
import com.suwell.plss.system.dto.request.UserLivenessAppReq;
import com.suwell.plss.system.dto.request.UserLivenessStatisticsReq;
import com.suwell.plss.system.dto.response.*;

/**
 * <AUTHOR>
 * @create 2024/11/6
 * @content
 */
public interface UserLivenessService {

    void userStatisticsExport(UserLivenessStatisticsReq req);

    PageUtils<UserLivenessStatisticsResp> userStatistics(UserLivenessStatisticsReq req);

    void intelligentAppStatisticsExport(UserLivenessAppReq req);

    PageUtils<UserLivenessAppResp> intelligentAppStatistics(UserLivenessAppReq req);

    void dataDetailStatisticsExport(DetailStatisticsReq req);

    PageUtils<DetailStatisticsV2Resp> dataDetailStatistics(DetailStatisticsReq req);

    void collectDataDetailExport(CollectDataReq req);

    PageUtils<CollectDataResp> collectDataDetail(CollectDataReq req);
}
