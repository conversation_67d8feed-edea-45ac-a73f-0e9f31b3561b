package com.suwell.plss.system.remote;

import cn.hutool.core.collection.CollUtil;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.constant.SymbolPool;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.api.domain.SysRemainDTO;
import com.suwell.plss.system.api.entity.SysRemain;
import com.suwell.plss.system.api.enums.SysRemainEnum.CheckStatusEnum;
import com.suwell.plss.system.api.enums.SysRemainEnum.StatusEnum;
import com.suwell.plss.system.api.service.RemainRpcService;
import com.suwell.plss.system.service.SysRemainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(RemainRpcService.INNER_PATH)
public class RpcRemainServiceImpl implements RemainRpcService {
    @Resource
    private SysRemainService remainService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Override
    public R<Void> addRemain(@RequestBody SysRemainDTO sysRemainDTO){
        if(StringUtils.contains(sysRemainDTO.getAcceptPersonId(), SymbolPool.AT+SecurityUtils.getUserId()+SymbolPool.AT)){
            String[] accpetPersonIds = sysRemainDTO.getAcceptPersonId().split(SymbolPool.COMMA);
            List<String> result = Arrays.stream(accpetPersonIds).filter(o -> !(SymbolPool.AT + SecurityUtils.getUserId() + SymbolPool.AT).equals(o)).toList();
            if(CollUtil.isEmpty( result)){
                return R.ok();
            }
            sysRemainDTO.setAcceptPersonId(String.join(SymbolPool.COMMA, result));
        }
        SysRemain sysRemain = new SysRemain();
        BeanUtils.copyProperties(sysRemainDTO, sysRemain);
        remainService.save(sysRemain);
        return R.ok();
    }

    @Override
    public R<Void> batchSaveRemain(@RequestBody List<SysRemain> remains){
        remains = remains.stream().map(remain -> {
            if(StringUtils.contains(remain.getAcceptPersonId(), SymbolPool.AT+SecurityUtils.getUserId()+SymbolPool.AT)){
                String[] accpetPersonIds = remain.getAcceptPersonId().split(SymbolPool.COMMA);
                List<String> result = Arrays.stream(accpetPersonIds).filter(o -> !(SymbolPool.AT + SecurityUtils.getUserId() + SymbolPool.AT).equals(o)).toList();
                if(CollUtil.isEmpty( result)){
                    return null;
                }
                remain.setAcceptPersonId(String.join(SymbolPool.COMMA, result));
            }
            return remain;
        }).filter(Objects::nonNull).toList();
        remainService.saveBatch(remains);
        return R.ok();
    }

    @Override
    public R<Void> revoke(@RequestBody Long id){
        remainService.revoke(id);
        return R.ok();
    }

    @Override
    public R<Void> updateStatus(@RequestBody List<Long> recordBorrowIdList,String checkStatus,String rejectReason){
        remainService.lambdaUpdate().set(SysRemain::getHandlePersonId, SecurityUtils.getUserId())
                .set(SysRemain::getStatus,  StatusEnum.REMAIN_DONE.getCode())
                .set(SysRemain::getCheckStatus,checkStatus)
                .set(SysRemain::getUpdateTime, new Date())
                .set(CheckStatusEnum.REJECT.getCode().equals(checkStatus),SysRemain::getRejectReason,rejectReason)
                .in(SysRemain::getBusinessId,recordBorrowIdList)
                .update();
        return R.ok();
    }

    @Override
    public R updateInvalidStatus(List<Long> recordIds) {
        remainService.updateInvalidStatus(recordIds);
        return R.ok();
    }
}
