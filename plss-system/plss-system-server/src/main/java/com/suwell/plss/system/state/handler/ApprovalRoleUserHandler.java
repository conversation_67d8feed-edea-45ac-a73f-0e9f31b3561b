package com.suwell.plss.system.state.handler;

import com.alibaba.cola.statemachine.Action;
import com.alibaba.cola.statemachine.Condition;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.dto.request.SysOperateApproveReq;
import com.suwell.plss.system.entity.SysOperateApprove;
import com.suwell.plss.system.entity.SysUserRole;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.service.SysOperateApproveService;
import com.suwell.plss.system.service.SysUserRoleService;
import com.suwell.plss.system.state.StateMachineHandler;
import com.suwell.plss.system.state.context.SysOperateInfo;
import com.suwell.plss.system.state.context.UserContext;
import com.suwell.plss.system.state.enums.RoleUserEventEnum;
import com.suwell.plss.system.state.enums.RoleUserStatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;

@Component
public class ApprovalRoleUserHandler implements StateMachineHandler<RoleUserStatusEnum, RoleUserEventEnum, UserContext> {

    @Resource
    private SysOperateApproveService sysOperateApproveService;

    @Resource
    private SysUserRoleService userRoleService;

    @Resource
    private IUserService userService;

    @Override
    public Condition<UserContext> condition() {
        return (context ->true);
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public Action<RoleUserStatusEnum, RoleUserEventEnum, UserContext> action() {
        //执行逻辑操作
        return (from,to,event,context)->{
            List<SysOperateInfo> sysOperateInfos = new ArrayList<>(context.getSysOperateApproveReq().getSysOperateInfo().size());
            List<SysOperateApprove> updateList = new ArrayList<>(context.getSysOperateApproveReq().getSysOperateInfo().size());
            for (SysOperateApproveReq sar: context.getSysOperateApproveReq().getSysOperateInfo()) {
                SysOperateApprove operateApprove = sysOperateApproveService.getById(sar.getId());
                SysOperateInfo sysOperateInfo = JSON.parseObject(operateApprove.getOperateValue(), SysOperateInfo.class);
                operateApprove.setApproveStatus(context.getSysOperateApproveReq().getApproveStatus());
                operateApprove.setApproveUser(SecurityUtils.getUserId().toString());
                operateApprove.setApproveUserName(SecurityUtils.getUsername());
                operateApprove.setApproveTime(new Date());
                updateList.add(operateApprove);
                sysOperateInfos.add(sysOperateInfo);
            }
            // 更新审批记录
            sysOperateApproveService.updateBatchById(updateList);
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>();
            Set<String> userIdSet = new HashSet<>(sysOperateInfos.size());
            for (SysOperateInfo f : sysOperateInfos) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(f.getUserId());
                ur.setRoleId(f.getRoleId());
                ur.setTenantId(SecurityUtils.getTenantId());
                list.add(ur);
                userIdSet.add(f.getUserId());
            }
            userRoleService.saveBatch(list);
            // 更新用户状态
            userService.afterAuditModifyStatus(new ArrayList<>(userIdSet), null);
        };
    }
}
