package com.suwell.plss.system.service;

import com.suwell.plss.record.standard.dto.request.AccessTrendReq;
import com.suwell.plss.record.standard.dto.response.AccessTrendResp;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
public interface SuperviseUserAnalysisService {

    /**
     * 统计当前所有启用的用户数量
     *
     * @return
     */
    Long userCount();

    /**
     * 统计趋势
     *
     * @param req
     * @return
     */
    AccessTrendResp accessTrend(AccessTrendReq req);
}
