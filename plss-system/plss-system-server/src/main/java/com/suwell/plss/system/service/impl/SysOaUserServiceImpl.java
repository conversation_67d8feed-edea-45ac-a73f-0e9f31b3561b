package com.suwell.plss.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.system.api.domain.request.OaUserReq;
import com.suwell.plss.system.api.domain.request.SimpleOrgFindDto;
import com.suwell.plss.system.api.domain.request.SimpleUserFindDto;
import com.suwell.plss.system.api.domain.request.SrcOrgUserInfoReq;
import com.suwell.plss.system.api.domain.response.SrcOrgUserInfoResp;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.entity.SysOaUser;
import com.suwell.plss.system.manager.service.IOrgService;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.mapper.SysOaUserMapper;
import com.suwell.plss.system.service.SysOaUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SysOaUserServiceImpl extends ServiceImpl<SysOaUserMapper, SysOaUser> implements SysOaUserService {

    @Resource
    private WFGIdGenerator idGenerator;

    @Resource
    private IOrgService sysOrgService;

    @Resource
    private IUserService sysUserService;

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void saveList(List<SrcOrgUserInfoReq> infoList) {
        LocalDateTime now = LocalDateTime.now();
        List<SysOaUser> userList = new ArrayList<>(infoList.size());
        for (SrcOrgUserInfoReq i : infoList) {
            SysOaUser oaUser = new SysOaUser();
            oaUser.setId(idGenerator.next());
            oaUser.setUserUid(i.getUid());
            oaUser.setDetailsJson(i.getDetailsJson());
            oaUser.setOperationStatus(i.getOperationStatus());
            oaUser.setCreateTime(now);
            oaUser.setUpdateTime(now);
            userList.add(oaUser);
        }
        this.saveBatch(userList);
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void modifyList(List<SrcOrgUserInfoReq> infoList) {
        for (SrcOrgUserInfoReq f : infoList) {
            LambdaUpdateWrapper<SysOaUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(SysOaUser::getDetailsJson, f.getDetailsJson())
                    .set(SysOaUser::getUpdateTime, LocalDateTime.now()).set(SysOaUser::getOperationStatus, f.getOperationStatus())
                    .eq(SysOaUser::getUserUid, f.getUid());
            this.update(updateWrapper);
        }
    }

    @Override
    public List<SrcOrgUserInfoResp> queryCompositeUser(List<String> srcIdList, Integer operationStatus) {
        return this.baseMapper.selectCompositeUser(srcIdList, operationStatus);
    }

    @Override
    public List<String> queryAlreadyDeletedUser(LocalDateTime startTime) {
        return this.baseMapper.selectAlreadyDeletedUser(startTime);
    }

    @Override
    public void deleteList(List<String> srcIdList) {
        this.remove(new LambdaQueryWrapper<SysOaUser>().in(SysOaUser::getUserUid, srcIdList));
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public R<Void> addOaUser(OaUserReq oaOrgReq) {
        SimpleUserFindDto dto = new SimpleUserFindDto();
        dto.setSrcIds(CollUtil.newArrayList(oaOrgReq.getId()));
        SysUser existSysUser = sysUserService.getSimpleUserByDto(dto);
        if(existSysUser != null){
            return updateOaUser(oaOrgReq);
        }
        SysUser sysUser = convertToSysUser(oaOrgReq);
        sysUserService.insertUser(sysUser);
        //保存三方用户信息
        SysOaUser sysOaUser = new SysOaUser();
        sysOaUser.setId(idGenerator.next());
        sysOaUser.setUserUid(sysUser.getSrcId());
        sysOaUser.setOperationStatus(1);
        sysOaUser.setDetailsJson(oaOrgReq.getDetailJson());
        sysOaUser.setDeleted(Objects.equals(1, oaOrgReq.getDelFlag()) ? 0 : 1);
        LocalDateTime now = LocalDateTime.now();
        sysOaUser.setCreateTime(now);
        sysOaUser.setUpdateTime(now);
        this.save(sysOaUser);
        return R.ok();
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public R<Void> updateOaUser(OaUserReq oaOrgReq) {
        SimpleUserFindDto dto = new SimpleUserFindDto();
        dto.setSrcIds(CollUtil.newArrayList(oaOrgReq.getId()));
        SysUser existSysUser = sysUserService.getSimpleUserByDto(dto);
        if(existSysUser == null){
            return addOaUser(oaOrgReq);
        }
        SysUser sysUser = convertToSysUser(oaOrgReq);
        sysUser.setUserId(existSysUser.getUserId());
        sysUserService.updateUser(sysUser);
        //更新三方用户信息
        SysOaUser existSysOaUser = this.getOne(new LambdaQueryWrapper<SysOaUser>().eq(SysOaUser::getUserUid, oaOrgReq.getId()));
        LocalDateTime now = LocalDateTime.now();
        if(existSysOaUser == null){
            existSysOaUser = new SysOaUser();
            existSysOaUser.setId(idGenerator.next());
            existSysOaUser.setCreateTime(now);
            existSysOaUser.setOperationStatus(1);
        }
        existSysOaUser.setDeleted(Objects.equals(sysUser.getDelFlag(),"1") ? 0 : 1);
        existSysOaUser.setDetailsJson(oaOrgReq.getDetailJson());
        existSysOaUser.setUpdateTime(now);
        this.saveOrUpdate(existSysOaUser);
        return R.ok();
    }

    @Override
    public R<Void> deleteOaUser(String userId) {
        if(StringUtils.isEmpty(userId)){
            return R.error("用户id不能为空");
        }
        SimpleUserFindDto dto = new SimpleUserFindDto();
        dto.setSrcIds(CollUtil.newArrayList(userId));
        SysUser existSysUser = sysUserService.getSimpleUserByDto(dto);
        if(existSysUser != null){
            existSysUser.setDelFlag("2");
            sysUserService.updateById(existSysUser);
        }
        this.update(new LambdaUpdateWrapper<SysOaUser>().set(SysOaUser::getDeleted,1).eq(SysOaUser::getUserUid,userId));
        return R.ok();
    }

    private SysUser convertToSysUser(OaUserReq oaOrgReq) {
        SysUser sysUser = new SysUser();
        sysUser.setUserName(oaOrgReq.getUserName());
        sysUser.setNickName(oaOrgReq.getNickName());
        sysUser.setSrcId(oaOrgReq.getId());
        sysUser.setEmail(oaOrgReq.getEmail());
        sysUser.setPhonenumber(oaOrgReq.getPhone());
        sysUser.setPassword(oaOrgReq.getPassword());
        sysUser.setSex(oaOrgReq.getSex() == null ? "3" : oaOrgReq.getSex().toString());
        sysUser.setAvatar(oaOrgReq.getAvatar());
        sysUser.setStatus(Objects.equals(1, oaOrgReq.getStatus()) ? "1" : "2");
        sysUser.setDelFlag(Objects.equals(1, oaOrgReq.getDelFlag()) ? "1" : "2");
        sysUser.setRemark(oaOrgReq.getRemark());
        if(oaOrgReq.getOrderNum() != null){
            sysUser.setOrderNum(oaOrgReq.getOrderNum().toString());
        }
        //关联组织机构
        List<String> oaOrgUidList = oaOrgReq.getOaOrgUidList();
        if(!CollectionUtils.isEmpty(oaOrgUidList)){
            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setSrcIds(oaOrgUidList);
            List<SysOrg> sysOrgList = sysOrgService.listSimpleOrgByDto(dto);
            sysUser.setOrgIds(sysOrgList.stream().map(SysOrg::getOrgId).collect(Collectors.toList()));
        }
        return sysUser;
    }

}
