package com.suwell.plss.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.suwell.plss.system.entity.SysCategoryRelation;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-02 13:46
 **/
public interface SysCategoryRelationService extends IService<SysCategoryRelation> {

    /**
     * 根据父id获取所有子类id
     */
    public List<Long> getSonIdsByPid(Long ancestorId);


    /**
     * 根据父id获取所有子类id
     */
    public List<Long> getAncestorIdById(Long descendantId);

    /**
     * 根据父id集合获取所有子类
     */
    public List<SysCategoryRelation> getSonIdsByPids(List<Long> ancestorIdList);

    void saveCategoryRelationListBatchSize(List<SysCategoryRelation> batch);

    boolean saveOrUpdateBatch(List<SysCategoryRelation> entityList, boolean isUpdate);
}

