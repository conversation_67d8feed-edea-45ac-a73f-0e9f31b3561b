package com.suwell.plss.system.controller;

import cn.hutool.core.collection.CollUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.system.api.domain.OrgImportDTO;
import com.suwell.plss.system.api.domain.SysCategoryDTO;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.enums.SystemBizError;
import com.suwell.plss.system.domain.vo.out.CategoryExcelResultDTO;
import com.suwell.plss.system.dto.request.*;
import com.suwell.plss.system.dto.response.CategoryImportResp;
import com.suwell.plss.system.dto.response.CategoryResp;
import com.suwell.plss.system.dto.response.CategoryTreeResp;
import com.suwell.plss.system.dto.response.CategorysAddResp;
import com.suwell.plss.system.entity.ImportExcel;
import com.suwell.plss.system.service.SysCategoryService;
import com.suwell.plss.system.service.impl.ImportExcelServiceImpl;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * sys-category-controller
 *
 * <AUTHOR>
 * @date 2023-08-02 13:46
 **/
@RequestMapping("/v1/category")
@RestController
@Slf4j
public class SysCategoryController {

    @Autowired
    private SysCategoryService categoryService;

    @Autowired
    private ImportExcelServiceImpl importExcelService;

    public static final String MODE_NAME = "'后台-分类标签管理'";

    /**
     * 根据id查询分类信息
     *
     * @param categoryId 分类的主键id
     * @return 分类信息
     */
    @PostMapping("/info/{categoryId}")
    public R queryById(@PathVariable("categoryId") Long categoryId) {
        return R.ok(categoryService.queryById(categoryId));
    }

    /**
     * 根据id查询分类的子类
     *
     * @param categoryDTO
     * @return 子分类信息
     */
    @PostMapping("/queryChildrenById")
    public R<List<CategoryResp>> queryChildrenById(@RequestBody SysCategoryDTO categoryDTO) {
        SysCategory category = new SysCategory();
        BeanUtils.copyProperties(categoryDTO, category);
        Boolean isPersonal = Boolean.FALSE;
        if(ObjectUtils.isNotEmpty(category.getIsPersonal())){
            isPersonal = category.getIsPersonal();
        }
        return R.ok(categoryService.queryChildrenById(category,isPersonal));
    }

    /**
     * 根据id查询分类的子类
     *
     * @param categoryDTO
     * @return 子分类信息
     */
    @PostMapping("/personal/queryChildrenById")
    public R<List<CategoryResp>> queryPersonalChildrenById(@RequestBody SysCategoryDTO categoryDTO) {
        SysCategory category = new SysCategory();
        BeanUtils.copyProperties(categoryDTO, category);
        return R.ok(categoryService.queryChildrenById(category,Boolean.TRUE));
    }

    /**
     * 依据ID查询分类的子类   可能与上述接口冲突，但考虑到兼容自定义菜单的配置，新增此项
     * @param catId 分类ID
     * @return
     */
    @GetMapping("/queryChildrenById")
    public R<List<CategoryResp>> queryChildrenById(@RequestParam(name = "id",required = true) Long catId) {
        SysCategory sysCategory = new SysCategory();
        sysCategory.setId(catId);
        return R.ok(categoryService.queryChildrenById(sysCategory,Boolean.FALSE));
    }

    /**
     * 根据id查询分类的子类 提供文件类型分组
     *
     * @return 子分类信息
     */
    @PostMapping("/queryGroupTree")
    public R<List<SysCategory>> queryGroupTree() {
        return R.ok(categoryService.queryGroupTree());
    }

    /**
     * 分类分页查询
     *
     * @param categoryDTO 参数，name：分类名称，pid：上级分类id，page：页数，pageSize：每页条数
     * @return
     */
    @PostMapping("/list")
    public R<PageUtils<SysCategory>> pageList(@RequestBody SysCategoryDTO categoryDTO) {
        SysCategory category = new SysCategory();
        BeanUtils.copyProperties(categoryDTO, category);
        return R.ok(categoryService.pageList(category));
    }

    /**
     * 新增分类标签
     *
     * @param saveDto 需要新增的分类信息
     * @return 新增的分类信息
     */
    @Log(title = "'后台-文档类型管理'", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<Long> save(@Valid @RequestBody CategorySaveReq saveDto) {
        AssertUtils.isNotEmpty(saveDto.getName(),SystemBizError.CATEGORY_NAME_EMPTY);
        return R.ok(categoryService.save(saveDto));
    }


    /**
     * 批量新增分类标签(弃用)
     *
     * @param saveDto 需要新增的分类信息
     * @return 新增的分类信息
     */
    @Deprecated
    @PostMapping("/batchSave")
    public R<List<Long>> batchSave(@Valid @RequestBody CategorySaveReq saveDto) {
        return R.ok(categoryService.batchSave(saveDto));
    }


    /**
     * 批量新增分类标签
     * @param categorysAddReq
     * @return
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public R<CategorysAddResp> batchAdd(@Valid @RequestBody CategorysAddReq categorysAddReq){
        return R.ok(categoryService.batchAdd(categorysAddReq));
    }

    /**
     * 个人库全量保存
     *
     * @param saveDto 需要保存的分类信息
     * @return 新增的分类信息
     */
    @PostMapping("/personal/batchSave")
    public R<List<Long>> savePersonal(@Valid @RequestBody CategorySaveReq saveDto) {
        return R.ok(categoryService.savePersonal(saveDto));
    }

    /**
     * 修改分类
     *
     * @param dto 需要更新的分类信息
     * @return 更新后的分类信息
     */
    @Log(title = MODE_NAME,businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R<CategoryResp> update(@RequestBody CategoryUpdateReq dto) {
        return R.ok(categoryService.update(dto));
    }

    /**
     * 删除分类
     *
     * @param categoryId 需要删除的分类id
     * @return 删除的条数
     */
    @Log(title = MODE_NAME,businessType = BusinessType.DELETE)
    @PostMapping("/del/{categoryId}")
    public R<Integer> delete(@PathVariable("categoryId") Long categoryId) {
        categoryService.delete(categoryId);
        return R.ok();
    }

    /**
     * 查询某个分类下的树形结构
     *
     * @param dto 查询条件
     * @return 查询出来的树状结构
     */
    @PostMapping("/tree")
    public R<CategoryTreeResp> tree(@Valid @RequestBody CategoryTreeReq dto) {
        return R.ok(categoryService.tree(dto));
    }

    /**
     * 导入excel
     *
     * @param file 导入的表格文件
     * @return
     */
    @Log(title = MODE_NAME, businessType = BusinessType.IMPORT, isSaveResponseData = false)
    @PostMapping("/importExcel")
    public R<Map<String, Set>> importExcel(@RequestPart("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null) {
            String regex = ".*\\.(xlsx|xls|et)";
            AssertUtils.isTrue(fileName.matches(regex), SystemBizError.CATEGORY_TEMPLATE_ERROR);
        }
        ExcelUtil<CategoryImportReq> util = new ExcelUtil<>(CategoryImportReq.class);
        InputStream temp1Is = null;
        InputStream temp2Is = null;
        try {
            FileItem fileItem = FileUtils.getFileItem(file.getInputStream(), fileName);
            List<CategoryImportReq> categoryImportDTOList = util.importExcel(temp1Is = fileItem.getInputStream(), 1)
                    .stream().filter(Objects::nonNull).toList();
            if (CollUtil.isEmpty(categoryImportDTOList)) {
                categoryImportDTOList = util.importExcel(temp2Is = fileItem.getInputStream(), 0)
                        .stream().filter(Objects::nonNull).toList();
            }
            if (CollUtil.isEmpty(categoryImportDTOList)) {
                return R.error("导入数据为空");
            }
            return categoryService.importExcel(categoryImportDTOList);
        } catch (Exception e) {
            return R.error("异常文件");
        } finally {
            IOUtils.closeQuietly(temp1Is);
            IOUtils.closeQuietly(temp2Is);
        }
    }

    /**
     * 导出分类
     *
     * @param categoryExportReq 导出的查询条件
     * @param response
     */
    @Log(title = MODE_NAME, businessType = BusinessType.EXPORT,isSaveResponseData = false)
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody CategoryExportReq categoryExportReq, HttpServletResponse response) {
        List<SysCategory> excels = categoryService.queryForExcel(categoryExportReq);
        AssertUtils.notEmptyCollection(excels, SystemBizError.CATEGORY_QUERY_DATA_EMPTY);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        String dateStr = LocalDateTime.now().format(dtf);
        String fileName = "导出分类_" + dateStr;
        // 这里注意 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            String resFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + resFileName + ".xlsx");

            ExcelUtil<SysCategory> excelUtil = new ExcelUtil<>(SysCategory.class);
            excelUtil.exportExcel(response, excels, "模板");
        } catch (Exception e) {
            log.error("导出文件失败");
        }
    }

    /**
     * 下载失败清单
     *
     * @param sessionId 会话id
     */
    @PostMapping("/download/failed")
    public void downloadFailed(@RequestParam("sessionId") String sessionId, HttpServletResponse response) {
        List<ImportExcel> excels = importExcelService.findBySessionId(sessionId, false);
        AssertUtils.notEmptyCollection(excels, SystemBizError.CATEGORY_IMPORT_FAIL_DATA_EMPTY);
        String fileName = "失败清单_" + excels.get(0).getFileName();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        try {
            List<CategoryExcelResultDTO> list = excels.stream()
                    .map(o -> {
                        CategoryExcelResultDTO dto = new CategoryExcelResultDTO();
                        BeanUtils.copyProperties(o, dto);
                        dto.setReason(o.getReason());
                        return dto;
                    }).collect(Collectors.toList());
            String resFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + resFileName + ".xlsx");

            ExcelUtil<CategoryExcelResultDTO> util = new ExcelUtil<>(CategoryExcelResultDTO.class);
            util.exportExcel(response, list, "模板");
        } catch (Exception e) {
            log.error("导出文件失败");
        }
    }

    /**
     * 下载分类导入模板
     */
    @PostMapping("/download/template")
    public void downloadTemplate(HttpServletResponse response) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        String dateStr = LocalDateTime.now().format(dtf);
        String fileName = "分类导入模板_" + dateStr;
        // 这里注意 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            String resFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + resFileName + ".xlsx");

            ExcelUtil<SysCategory> excelUtil = new ExcelUtil<>(SysCategory.class);
            excelUtil.exportExcel(response, null, "模板");
        } catch (Exception e) {
            log.error("导出模板失败");
        }
    }

    /**
     * 查询某个分类下是否有同名称分类
     * @param dto
     * @return
     */
    @PostMapping("/queryExist")
    public R<Boolean> queryExist(@RequestBody CategorySaveReq dto){
        return R.ok(categoryService.queryExist(dto));
    }


    /**
     * 分页查询所有分类，并带全路径,
     * @return
     */
    @PostMapping("/queryAll")
    public R<PageUtils<SysCategory>> queryAll(@RequestBody SysCategoryDTO categoryDTO){
        SysCategory category = new SysCategory();
        BeanUtils.copyProperties(categoryDTO,category);
        return R.ok(categoryService.queryAll(category));
    }


    /**
     * 查询当前登录人创建的所有分类
     * @return
     */
    @PostMapping("/queryAllByUser")
    public R<List<SysCategory>> queryAllByUser(){
        return R.ok(categoryService.queryAllByUser());
    }


    /**
     * 根据根节点和名称查询所有分类
     * @return
     */
    @PostMapping("/queryByName")
    public R<List<SysCategory>> queryByName(@RequestBody SysCategoryDTO categoryDTO){
        SysCategory category = new SysCategory();
        BeanUtils.copyProperties(categoryDTO,category);
        return R.ok(categoryService.queryByName(category));
    }
}
