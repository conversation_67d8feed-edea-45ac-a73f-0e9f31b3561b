package com.suwell.plss.system.service.impl;

import static com.suwell.plss.framework.common.constant.CacheConstants.DOWNLOAD_ATT_FILE_LIMIT;
import static com.suwell.plss.framework.common.constant.CacheConstants.DOWNLOAD_MASTER_FILE_LIMIT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.annotation.Excel.Type;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.constant.CacheConstants;
import com.suwell.plss.framework.common.constant.Constants;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.entity.slc.SLCEntity;
import com.suwell.plss.framework.common.enums.FigureEnum;
import com.suwell.plss.framework.common.enums.ResouceClassifiedEnum;
import com.suwell.plss.framework.common.enums.UserClassifiedEnum;
import com.suwell.plss.framework.common.enums.UserStatus;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.common.web.page.Query;
import com.suwell.plss.framework.datapermission.annotation.DataScope;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.mq.enums.EventType;
import com.suwell.plss.framework.mq.events.RecordFixRefreshEvent;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.model.SendDocUserIdReq;
import com.suwell.plss.system.api.domain.request.RoleFindDto;
import com.suwell.plss.system.api.domain.request.RoleImportDto;
import com.suwell.plss.system.api.domain.request.*;
import com.suwell.plss.record.service.RepositoryRpcService;
import com.suwell.plss.system.api.domain.request.OrgStatusFindDto;
import com.suwell.plss.system.api.domain.request.RoleFindDto;
import com.suwell.plss.system.api.domain.request.RoleImportDto;
import com.suwell.plss.system.api.domain.request.SimpleOrgFindDto;
import com.suwell.plss.system.api.domain.request.UserKeyInfoReq;
import com.suwell.plss.system.api.domain.request.*;
import com.suwell.plss.system.api.entity.SysConfig;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysRole;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.SysMessageEnum;
import com.suwell.plss.system.api.enums.SystemBizError;
import com.suwell.plss.system.config.ApproveTurnConfig;
import com.suwell.plss.system.config.CaptchaTurnConfig;
import com.suwell.plss.system.config.SysClassifiedConfig;
import com.suwell.plss.system.constant.ConfigConstant;
import com.suwell.plss.system.dto.SysUserExportDTO;
import com.suwell.plss.system.dto.request.OrgReq;
import com.suwell.plss.system.dto.request.ReviseUserPasswordReq;
import com.suwell.plss.system.dto.request.UserReq;
import com.suwell.plss.system.dto.response.CheckPasswordUpdateResp;
import com.suwell.plss.system.dto.response.SysOrgResp;
import com.suwell.plss.system.dto.response.SysUserResp;
import com.suwell.plss.system.encrypt.service.EncryptService;
import com.suwell.plss.system.entity.SysOperateApprove;
import com.suwell.plss.system.entity.SysPost;
import com.suwell.plss.system.entity.SysRoleMenu;
import com.suwell.plss.system.entity.SysTenantMenu;
import com.suwell.plss.system.entity.SysUserOrg;
import com.suwell.plss.system.entity.SysUserPost;
import com.suwell.plss.system.entity.SysUserRole;
import com.suwell.plss.system.enums.PmsTypeEnum;
import com.suwell.plss.system.enums.SysOrgEnum.OrgTypeEnum;
import com.suwell.plss.system.enums.SysRoleEnum.RoleTypeEnum;
import com.suwell.plss.system.enums.SysTenantEnum.DeleteEnum;
import com.suwell.plss.system.enums.SysTenantEnum.StatusEnum;
import com.suwell.plss.system.enums.SystemDataTypeEnum;
import com.suwell.plss.system.manager.service.IOrgService;
import com.suwell.plss.system.manager.service.IRoleService;
import com.suwell.plss.system.mapper.SysOrgMapper;
import com.suwell.plss.system.mapper.SysPostMapper;
import com.suwell.plss.system.mapper.SysRoleMenuMapper;
import com.suwell.plss.system.mapper.SysUserMapper;
import com.suwell.plss.system.mapper.SysUserOrgMapper;
import com.suwell.plss.system.mapper.SysUserPostMapper;
import com.suwell.plss.system.mapper.SysUserRoleMapper;
import com.suwell.plss.system.service.SLCService;
import com.suwell.plss.system.service.SysConfigService;
import com.suwell.plss.system.service.SysOperateApproveService;
import com.suwell.plss.system.service.SysOrgService;
import com.suwell.plss.system.service.SysPermissionService;
import com.suwell.plss.system.service.SysPostService;
import com.suwell.plss.system.service.SysTenantMenuService;
import com.suwell.plss.system.service.SysUserOrgService;
import com.suwell.plss.system.service.SysUserRoleService;
import com.suwell.plss.system.service.SysUserService;
import com.suwell.plss.system.state.StateMachineFlowHandler;
import com.suwell.plss.system.state.context.SysOperateInfo;
import com.suwell.plss.system.state.context.SysOperateInfoUserUpdate;
import com.suwell.plss.system.state.context.UserContext;
import com.suwell.plss.system.util.EasyExcelExport;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 * @date 2023年4月20日
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    public static final String REPO_MAX_COUNT_KEY = "sys.repo.admin.maxCount";
    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;
    private final static String SLC_VERSION = "V1";
    private final static String USER_ONLINE_PLUS_KEY = "sys.user.online.plus";
    private static final String ROLE_INIT_TYPE = "sys.init.roleType";

    private static final String IMPORT_USER_ERROR_ORG_MSG = "导入失败，出现系统中不存在的组织架构，请认真核对！";
    @Resource
    protected ApplicationContext context;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private SysPostMapper postMapper;
    @Resource
    private SysUserRoleMapper userRoleMapper;
    @Resource
    private SysUserPostMapper userPostMapper;
    @Resource
    private SysUserOrgMapper userOrgMapper;
    @Resource
    private SysOrgMapper orgMapper;
    @Resource
    private SysConfigService configService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private RedisService redisService;
    @Resource
    private IOrgService orgService;
    @Resource
    private IRoleService roleService;
    @Resource
    private SysUserOrgService userOrgService;
    @Resource
    private SysPostService postService;
    @Resource
    private SysUserRoleService userRoleService;
    @Resource
    private EncryptService encryptService;
    @Resource
    private SLCService slcService;
    @Resource
    private SysRoleMenuMapper roleMenuMapper;
    @Resource
    private SysTenantMenuService tenantMenuService;
    @Resource
    private ApproveTurnConfig approveTurnConfig;
    @Resource
    private StateMachineFlowHandler stateMachineFlowHandler;
    @Resource
    private SysOperateApproveService operateApproveService;
    @Resource
    private SysClassifiedConfig classifiedConfig;
    @Resource
    private CaptchaTurnConfig captchaTurnConfig;
    @Resource
    private RepositoryRpcService repositoryRpcService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Resource
    private SysPermissionService sysPermissionService;

    @Value("${sys.init.roleType:}")
    private String roleType;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(orgAlias = "d", userAlias = "u")
    public PageUtils<SysUser> selectUserList(SysUser user) {
        buildUserCondition(user);
        int queryType = 0;
        if (ObjectUtils.isNotEmpty(user.getParams()) && user.getParams().containsKey("queryType")) {
            queryType = Integer.parseInt(user.getParams().get("queryType").toString());
        }
        IPage<SysUser> page = userMapper.selectUserPage(new Query<SysUser>().getPage(user.getParams()), user,
                queryType);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<SysUser> userList = page.getRecords();
            buildUserInfo(userList);
            page.setRecords(userList);
        }
        return new PageUtils<>(page);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param role 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(orgAlias = "d", userAlias = "u")
    public PageUtils<SysUser> selectAllocatedList(SysRole role) {
        IPage<SysUser> page = new Query<SysUser>().getPage(role.getParams());
        LambdaQueryWrapper<SysUserRole> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(SysUserRole::getRoleId, role.getRoleId());
        if (!SecurityUtils.isAdmin()) {
            sysUserRoleLambdaQueryWrapper.eq(SysUserRole::getTenantId, SecurityUtils.getTenantId());
        }
        List<SysUserRole> userRoleList = userRoleMapper.selectList(sysUserRoleLambdaQueryWrapper);
        if (userRoleList.isEmpty()) {
            return new PageUtils<>(page);
        }
        List<String> userIdList = userRoleList.stream().map(SysUserRole::getUserId).collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.like(StringUtils.isNotEmpty(role.getUserName()), SysUser::getUserName,
                role.getUserName());
        userLambdaQueryWrapper.like(StringUtils.isNotEmpty(role.getNickName()), SysUser::getNickName,
                role.getNickName());
        userLambdaQueryWrapper.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS).in(SysUser::getUserId, userIdList);
        IPage<SysUser> userPage = this.page(page, userLambdaQueryWrapper);
        // 如果开启审批流程，则重新设置用户状态，便于前端展示处理
        if (!CollectionUtils.isEmpty(userPage.getRecords()) && approveTurnConfig.getSafeGab()) {
            for (SysUser u : userPage.getRecords()) {
                if (FigureEnum.ONE.getNumChar().equals(u.getStatus()) || FigureEnum.TWO.getNumChar()
                        .equals(u.getStatus())) {
                    u.setStatus(FigureEnum.ONE.getNumChar());
                } else {
                    u.setStatus(FigureEnum.THREE.getNumChar());
                }
            }
        }
        return new PageUtils<>(userPage);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public PageUtils<SysUser> selectUnallocatedList(SysUser user) {
        String tenantId = SecurityUtils.getTenantId();
        LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
        //先查询该租户下的用户
        List<SysUserOrg> userOrgList = userOrgService.lambdaQuery().eq(SysUserOrg::getTenantId, tenantId).list();
        List<String> orgUserIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
        //如果用户为空
        if (CollUtil.isEmpty(orgUserIdList)) {
            return new PageUtils<>(new Query<SysUser>().getPage(user.getParams()));
        }
//        userLqw.in(SysUser::getUserId, orgUserIdList);
        //查询已经绑定该角色的用户
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        List<String> roleIdList = CollUtil.newArrayList(user.getRoleId());
        //如果当前角色为初始化角色，需要排除其他已选中初始化角色的用户
        SysRole role = roleService.selectRoleById(user.getRoleId());
        if (CollUtil.contains(RoleTypeEnum.getDefaultRoleType(), role.getRoleType())) {
            SysConfig config = configService.getByConfigKey(ROLE_INIT_TYPE);
            List<Integer> defaultRoleTypeList = new ArrayList<>();
            defaultRoleTypeList.add(RoleTypeEnum.SUPER_ADMIN.getCode());
            if (ObjectUtils.isNotEmpty(config) && StringUtils.isNotEmpty(config.getConfigValue())) {
                defaultRoleTypeList = JSON.parseArray(config.getConfigValue(), Integer.class);
            }
            List<SysRole> defaultRoleList = roleService.listByRoleType(defaultRoleTypeList);
            roleIdList.addAll(defaultRoleList.stream().map(SysRole::getRoleId).toList());
        }
//        String tenantId = SecurityUtils.getTenantId();
        user.setTenantId(tenantId);
        user.setRoleIdList(roleIdList);
        int queryType = 0;
        if (ObjectUtils.isNotEmpty(user.getParams()) && user.getParams().containsKey("queryType")) {
            queryType = Integer.parseInt(user.getParams().get("queryType").toString());
        }
        IPage<SysUser> page = userMapper.selectUserPage(new Query<SysUser>().getPage(user.getParams()), user,queryType);



//        LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
//        //先查询该租户下的用户
//        List<SysUserOrg> userOrgList = userOrgService.lambdaQuery().eq(SysUserOrg::getTenantId, tenantId).list();
//        List<String> orgUserIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
//        //如果用户为空
//        if (CollUtil.isEmpty(orgUserIdList)) {
//            return new PageUtils<>(new Query<SysUser>().getPage(user.getParams()));
//        }
////        userLqw.in(SysUser::getUserId, orgUserIdList);
//        //查询已经绑定该角色的用户
//        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
//        userRoleLqw.in(SysUserRole::getRoleId, roleIdList).eq(SysUserRole::getTenantId, tenantId);
//        List<SysUserRole> userRoleList = userRoleMapper.selectList(userRoleLqw);
//        if (CollUtil.isNotEmpty(userRoleList)) {
//            List<String> roleUserIdList = userRoleList.stream().map(SysUserRole::getUserId).collect(Collectors.toList());
//            userLqw.notIn(SysUser::getUserId, roleUserIdList);
//        }
//
////        userLqw.like(StringUtils.isNotEmpty(user.getUserName()), SysUser::getUserName, user.getUserName());
////        userLqw.like(StringUtils.isNotEmpty(user.getNickName()), SysUser::getNickName, user.getNickName());
////        userLqw.like(StringUtils.isNotEmpty(user.getPhonenumber()), SysUser::getPhonenumber, user.getPhonenumber());
//        // 如果开启审批流程，只查询启用状态的用户
//        userLqw.eq(approveTurnConfig.getSafeGab(), SysUser::getStatus, FigureEnum.ONE.getNumChar());
        return new PageUtils<>(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(String userId) {
        SysUser sysUser = userMapper.selectById(userId);
        log.info("从数据库查询到userId:{}的数据:{}", userId, JSON.toJSONString(sysUser));
        if (ObjectUtils.isEmpty(sysUser)) {
            return new SysUser();
        }
        List<SysUserRole> userRoleList = new ArrayList<>();
        if (!ksoAuthConfig.isEnable()) {
            userRoleList = userRoleService.lambdaQuery().eq(SysUserRole::getUserId, sysUser.getUserId())
                    .eq(SysUserRole::getStatus, StatusEnum.ENABLE.getCode()).list();
            if (CollUtil.isNotEmpty(userRoleList)) {
                List<String> roleIdList = userRoleList.stream().map(SysUserRole::getRoleId).distinct()
                        .collect(Collectors.toList());
                SysConfig config = configService.getByConfigKey(ROLE_INIT_TYPE);
                List<Integer> defaultRoleTypeList = new ArrayList<>();
                defaultRoleTypeList.add(RoleTypeEnum.SUPER_ADMIN.getCode());
                if (ObjectUtils.isNotEmpty(config) && StringUtils.isNotEmpty(config.getConfigValue())) {
                    defaultRoleTypeList = JSON.parseArray(config.getConfigValue(), Integer.class);
                }
                RoleFindDto dto = new RoleFindDto();
                dto.setRoleIdList(roleIdList);
                dto.setInitRoleTypeList(RoleTypeEnum.getDefaultRoleType());
                dto.setConfigRoleTypeList(defaultRoleTypeList);
                // TODO listByRoleDto 角色已改造-首页用户详情
                // TODO 用户组放开 已回退  kso环境不会走到这里，所以先不动
                List<SysRole> roleList = roleService.listByRoleDto(dto);
                if (roleList != null) {
                    sysUser.setRoles(roleList);
                }
            }
        }
        LambdaQueryWrapper<SysUserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userOrgLambdaQueryWrapper.eq(SysUserOrg::getUserId, userId);
        userOrgLambdaQueryWrapper.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(userOrgList)) {
            orderTenant(sysUser, userOrgList, userRoleList);
        }
        LambdaQueryWrapper<SysUserPost> userPostLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userPostLambdaQueryWrapper.eq(SysUserPost::getUserId, userId);
        List<SysUserPost> userPostList = userPostMapper.selectList(userPostLambdaQueryWrapper);
        if (!userPostList.isEmpty()) {
            List<Long> postIdList = userPostList.stream().map(SysUserPost::getPostId).collect(Collectors.toList());
            sysUser.setPostIds(ArrayUtil.toArray(postIdList, Long.class));
        }
        List<String> userIdList = new ArrayList<>();
        userIdList.add(sysUser.getUserId());
        Map<String, String> idOrgPathMap = orgService.getOrgPathByUserId(userIdList);
        sysUser.setOrgPaths(idOrgPathMap.get(sysUser.getUserId()));
        sysUser.setPassword(null);
        sysUser.setIsAdmin(sysUser.isAdmin());
        if (Objects.isNull(sysUser.getClassified())) {
            sysUser.setClassified(UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL.getCode());
        }
        return sysUser;
    }

    private void orderTenant(SysUser sysUser, List<SysUserOrg> userOrgList, List<SysUserRole> userRoleList) {
        // 确保userOrgList不为空且至少有一个元素
        if (userOrgList != null && !userOrgList.isEmpty()) {
            sysUser.setOrgId(userOrgList.get(0).getOrgId());

            // 获取非零租户ID列表
            List<String> userTenantIdList = userOrgList.stream()
                    .filter(uo -> uo != null && !uo.getTenantId().equals("0"))
                    .map(SysUserOrg::getTenantId)
                    .distinct()
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userTenantIdList)) {
                List<SysRole> roles = sysUser.getRoles();

                if (roles != null && !roles.isEmpty()) {
                    Set<String> roleIdSet = roles.stream().map(SysRole::getRoleId).collect(Collectors.toSet());
                    LambdaQueryWrapper<SysRoleMenu> roleMenuLqw = new LambdaQueryWrapper<>();
                    roleMenuLqw.in(SysRoleMenu::getRoleId, roleIdSet);
                    List<SysRoleMenu> roleMenuList = roleMenuMapper.selectList(roleMenuLqw);

                    Map<String, List<SysUserRole>> tenantRoleMap = userRoleList.stream()
                            .collect(Collectors.groupingBy(SysUserRole::getTenantId));

                    List<SysTenantMenu> tenantMenuList = tenantMenuService.lambdaQuery()
                            .in(SysTenantMenu::getTenantId, userTenantIdList)
                            .list();

                    // 排序租户ID列表
                    CollUtil.sort(userTenantIdList, Comparator.comparing(id -> {
                        if (tenantRoleMap != null && tenantRoleMap.containsKey(id)) {
                            List<String> tenantRoleIdList = tenantRoleMap.get(id).stream()
                                    .map(SysUserRole::getRoleId)
                                    .toList();

                            Set<Long> roleMenuIdSet = roleMenuList.stream()
                                    .filter(rm -> tenantRoleIdList != null && tenantRoleIdList.contains(rm.getRoleId()))
                                    .map(SysRoleMenu::getMenuId)
                                    .collect(Collectors.toSet());

                            Set<Long> tenantMenuIdSet = tenantMenuList.stream()
                                    .filter(t -> t != null && t.getTenantId().equals(id))
                                    .map(SysTenantMenu::getMenuId)
                                    .collect(Collectors.toSet());

                            Collection<Long> menuIdList = CollUtil.intersection(roleMenuIdSet, tenantMenuIdSet);
                            return !menuIdList.isEmpty();
                        }
                        return false;
                    }).reversed());

                    boolean defaultMenuNotEmpty = false;
                    if (tenantRoleMap != null && tenantRoleMap.containsKey(sysUser.getDefaultTenantId())) {
                        List<String> defaultTenantRoleIdList = tenantRoleMap.get(sysUser.getDefaultTenantId()).stream()
                                .map(SysUserRole::getRoleId)
                                .toList();

                        if (!CollectionUtils.isEmpty(defaultTenantRoleIdList)) {
                            List<Long> defaultTenantRoleMenuIdList = roleMenuList.stream()
                                    .filter(r -> defaultTenantRoleIdList != null && defaultTenantRoleIdList.contains(
                                            r.getRoleId()))
                                    .map(SysRoleMenu::getMenuId)
                                    .collect(Collectors.toList());

                            List<Long> defaultTenantMenuIdList = tenantMenuList.stream()
                                    .filter(t -> t != null && t.getTenantId().equals(sysUser.getDefaultTenantId()))
                                    .map(SysTenantMenu::getMenuId)
                                    .collect(Collectors.toList());

                            Collection<Long> unionIdList = CollUtil.intersection(defaultTenantRoleMenuIdList,
                                    defaultTenantMenuIdList);
                            defaultMenuNotEmpty = !unionIdList.isEmpty();
                        }
                    }

                    if (defaultMenuNotEmpty) {
                        CollUtil.sort(userTenantIdList,
                                Comparator.comparing(id -> id.equals(sysUser.getDefaultTenantId())).reversed());
                    }
                }
            }

            sysUser.setTenantIdList(userTenantIdList);

        }
    }

    @Override
    public SysUser selectUserBySrcUserId(String srcUserId) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getSrcId, srcUserId).eq(SysUser::getDelFlag, UserStatus.OK.getCode());
        List<SysUser> sysUserList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(sysUserList)) {
            log.error("根据三方用户id:{}无法获取到对应的用户信息", srcUserId);
            throw new ServiceException("来源id错误，无法获取到对应用户信息");
        }
        if (sysUserList.size() > FigureEnum.ONE.getNumVal()) {
            throw new ServiceException("来源id获取到多个用户信息");
        }
        return this.selectUserById(sysUserList.get(0).getUserId());
    }

    @Override
    public List<SysUser> listUsersBySrcIds(Collection<String> srcIds) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getSrcId, srcIds).eq(SysUser::getDelFlag, UserStatus.OK.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public SysUser selectUserByKeyInfo(UserKeyInfoReq req) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        String encryptPhone = req.getKeyInfo();
        try {
            encryptPhone = encryptService.encrypt(null, req.getKeyInfo());
        } catch (Exception e) {
            log.error("关键信息作为手机号时，需要加密处理，处理结果异常", e);
        }
        String finalEncryptPhone = encryptPhone;
        queryWrapper.eq(SysUser::getDelFlag, UserStatus.OK.getCode()).and(c ->
                c.eq(SysUser::getUserName, req.getKeyInfo()).or().eq(SysUser::getPhonenumber, finalEncryptPhone));
        return this.getOne(queryWrapper);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<String> list = roleService.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return String.join(",", list);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        String userId = Objects.isNull(user.getUserId()) ? "-1" : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        String userId = Objects.isNull(user.getUserId()) ? "-1" : user.getUserId();
        if (!user.getPhonenumber().contains("*")) {
            try {
                LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
                userLqw.eq(SysUser::getPhonenumber, encryptService.encrypt(null, user.getPhonenumber()));
                userLqw.ne(SysUser::getUserId, userId);
                userLqw.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
                long count = this.count(userLqw);
                if (count > 0L) {
                    return UserConstants.NOT_UNIQUE;
                }
            } catch (Exception e) {
                log.error("手机号加密失败");
                throw new BizException(SystemBizError.USER_PHONE_ENTRY_ERROR);
            }
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        String userId = Objects.isNull(user.getUserId()) ? "-1" : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验多个用户是否允许操作
     *
     * @param userIdList 用户信息
     */
    @Override
    public void checkUserAllowed(List<String> userIdList) {
        if (CollUtil.isNotEmpty(userIdList) && containConfigAdmin(userIdList)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    @Override
    public List<SysUser> getUserListByOrgIds(List<String> orgIds, Integer classified, boolean recursive) {
        List<String> allOrgIds;
        if (recursive) {
            allOrgIds = addChildrenDeptId(new ArrayList<>(), orgIds);
        } else {
            allOrgIds = orgIds;
        }
        List<SysUserOrg> userOrgs = userOrgService.lambdaQuery()
                .eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode())
                .in(SysUserOrg::getOrgId, allOrgIds).list();
        if (CollUtil.isEmpty(userOrgs)) {
            return new ArrayList<>();
        }
        List<Integer> classifieds = new ArrayList<>();
        if (Objects.nonNull(classified)) {
            classifieds = UserClassifiedEnum.getCodes(
                    Objects.requireNonNull(ResouceClassifiedEnum.getEnum(classified)).getUserPerms());
        }
        return this.lambdaQuery()
                .in(SysUser::getUserId, userOrgs.stream().map(SysUserOrg::getUserId).toList())
                .in(CollectionUtil.isNotEmpty(classifieds), SysUser::getClassified, classifieds)
                .list();
    }

    private List<String> addChildrenDeptId(List<String> allOrgIds, List<String> orgIds) {
        allOrgIds.addAll(orgIds);
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setDelFlag(UserConstants.YSE_STATUS);
        dto.setOrgType(OrgTypeEnum.DEPARTMENT.getCode());
        dto.setParentIds(orgIds);
        List<SysOrg> children = orgService.listSimpleOrgByDto(dto);
        if (CollUtil.isEmpty(children)) {
            return orgIds;
        }
        List<String> childrenId = children.stream().map(SysOrg::getOrgId).toList();
        addChildrenDeptId(allOrgIds, childrenId);
        return allOrgIds;
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(String userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
//            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
//            if (StringUtils.isEmpty(users)) {
//                throw new ServiceException("没有权限访问用户数据！");
//            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        checkUserWps(1);
        // 新增用户信息
        String userId = wfgIdGenerator.next() + "";
        user.setUserId(userId);
        user.setLoginDate(LocalDateTime.now());
        user.setPwdUpdateTime(LocalDateTime.now());
        user.setStatus(StatusEnum.ENABLE.getCode() + "");
        user.setDelFlag(UserConstants.YSE_STATUS);
        user.setCreateBy(SecurityUtils.getUsername());
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        Date now = new Date();
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setClassified(
                Objects.isNull(user.getClassified()) ? UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL.getCode()
                        : user.getClassified());
//        user.setTenantId(SecurityUtils.getTenantId());
        //加密手机号
        if (StringUtils.isNotEmpty(user.getPhonenumber())) {
            try {
                String encrypt = encryptService.encrypt(null, user.getPhonenumber());
                user.setPhonenumber(encrypt);
            } catch (Exception e) {
                log.error("手机号加密失败", e);
            }
        }
        user.setWpsAuth(UserConstants.WPS_NOT_AUTH);
        if (ObjectUtils.isEmpty(user.getOrderNum())) {
            user.setOrderNum("1");
        }

        // 国安部安全管理审批开启时，新增用户默认禁用状态
        if (approveTurnConfig.getSafeGab()) {
            user.setStatus(StatusEnum.DISABLE.getCode() + "");
        }
        user.setDataActStatus(user.getStatus());
        int rows = this.saveBatch(CollectionUtil.newArrayList(user)) ? 1 : 0;
        user.setUserId(userId);
        //新增用户部门关联
        insertUserOrg(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        SysRole normalRole = roleService.listByRoleType(CollUtil.newArrayList(RoleTypeEnum.ORDINARY_USER.getCode()))
                .get(0);
        if (!ArrayUtil.contains(user.getRoleIds(), normalRole.getRoleId())) {
            user.setRoleIds(ArrayUtil.append(user.getRoleIds(), normalRole.getRoleId()));
        }
        insertUserRole(user);
        return rows;
    }

    @Override
    public int fireUserStateMachine(SysUser user) {
        UserReq userReq = new UserReq();
        userReq.setUserIdList(Arrays.asList(user.getUserId()));
        userReq.setStatus(user.getStatus());
        fireUserStateMachineBatch(userReq);
        return 1;
    }

    @Override
    public boolean fireUserStateMachineBatch(UserReq userReq) {
        //构造上下文对象
        UserContext userContext = new UserContext();
        List<SysUser> sysUsers = userMapper.selectList(
                Wrappers.<SysUser>lambdaQuery().in(SysUser::getUserId, userReq.getUserIdList()));
        Map<String, String> idOrgPathMap = orgService.getOrgPathByUserId(userReq.getUserIdList());
        List<SysOperateInfo> sysOperateInfoList = new ArrayList<>();
        sysUsers.forEach(sysUser -> {
            SysOperateInfo sysOperateInfo = new SysOperateInfo();
            BeanUtils.copyProperties(sysUser, sysOperateInfo);
            sysOperateInfo.setOrgName(idOrgPathMap.get(sysUser.getUserId()));
            sysOperateInfoList.add(sysOperateInfo);
        });
        userContext.setSysOperateInfo(sysOperateInfoList);
        switch (userReq.getStatus()) {
            case "1":
                stateMachineFlowHandler.fireEnableUserStateMachine(userContext);
                break;
            case "2":
                stateMachineFlowHandler.fireDisableUserStateMachine(userContext);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public List<SysUser> listUserOrgForRepo(Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return userMapper.listUserOrgForRepo(userIds);
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        if(StringUtils.isNotEmpty(user.getUserId())){
            user.setUserId(wfgIdGenerator.next()+"");
        }
        return userMapper.insertUser(user) > 0;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void afterAuditModifyStatus(List<String> userIds, String status) {
        Map<String, List<SysOperateApprove>> hashed = operateApproveService.hashInReviewByUserIds(userIds);
        List<SysUser> userList = this.listByIds(userIds);
        Map<String, String> actStatusMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getUserId, SysUser::getDataActStatus));
        List<SysUser> updateUsers = new ArrayList<>(userIds.size());
        for (String i : userIds) {
            SysUser userUpdate = new SysUser();
            userUpdate.setUserId(i);
            userUpdate.setUpdateTime(new Date());
            if (!CollectionUtils.isEmpty(hashed.get(i))) {
                userUpdate.setStatus(FigureEnum.THREE.getNumChar());
            } else if (StringUtils.isNotEmpty(status)) {
                userUpdate.setStatus(status);
                userUpdate.setDataActStatus(status);
            } else {
                userUpdate.setStatus(actStatusMap.get(i));
            }
            updateUsers.add(userUpdate);
        }
        this.updateBatchById(updateUsers);
    }


    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        String userId = user.getUserId();
        int updateNum = 0;
        if (approveTurnConfig.getSafeGab() && !SecurityUtils.isAdmin()) {
            SysUser oldUser = this.selectUserById(userId);
            // 手机号解密
            if (StringUtils.isNotEmpty(oldUser.getPhonenumber())) {
                try {
                    String decrypt = encryptService.decrypt(null, oldUser.getPhonenumber());
                    oldUser.setPhonenumber(decrypt);
                } catch (Exception e) {
                    log.error("手机号解密失败", e);
                }
            }
            List<SysOrg> oldOrgList = orgService.getOrgByUserId(userId, SecurityUtils.getTenantId());
            // 处理角色安全审批
            this.dealWithUserRoleStateMachine(oldUser, SecurityUtils.getTenantId(), user.getRoleIds());
            // 处理用户安全审批
            this.dealWithUserUpdateStateMachine(oldUser, oldOrgList, user);
            updateNum = 1;
        } else {
            this.insertUserAuth(user.getUserId(), user.getRoleIds());
            updateNum = this.modifyUserMainInfo(user);
        }
        TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            // 刷新用户所属单位
            SendDocUserIdReq req = new SendDocUserIdReq();
            req.setUserId(user.getUserId());
            context.publishEvent(new RecordFixRefreshEvent<>(req, null, EventType.DOC_REFRESH_ORG));
        });
        return updateNum;
    }

    private void dealWithUserUpdateStateMachine(SysUser oldUser, List<SysOrg> oldOrgList, SysUser user) {
        List<String> oldOrgIds = oldOrgList.stream().map(SysOrg::getOrgId).toList();
        List<String> newOrgIds = user.getOrgIds();

        // 对比新旧组织机构id，获取新增和删除的组织机构id
        List<String> addOrgIds = newOrgIds.stream().filter(orgId -> !oldOrgIds.contains(orgId))
                .collect(Collectors.toList());
        List<String> delOrgIds = oldOrgIds.stream().filter(orgId -> !newOrgIds.contains(orgId))
                .collect(Collectors.toList());
        SysOperateInfoUserUpdate operateInfoUserBeforeUpdate = new SysOperateInfoUserUpdate();
        BeanUtils.copyProperties(oldUser, operateInfoUserBeforeUpdate);
        SysOperateInfoUserUpdate operateInfoUserAfterUpdate = new SysOperateInfoUserUpdate();
        BeanUtils.copyProperties(user, operateInfoUserAfterUpdate);
        UserContext userContext = new UserContext();
        userContext.setOldUser(operateInfoUserBeforeUpdate);
        userContext.setNewUser(operateInfoUserAfterUpdate);
        userContext.setAddOrgIds(addOrgIds);
        userContext.setDelOrgIds(delOrgIds);
        stateMachineFlowHandler.fireUpdateUserStateMachine(userContext);
    }

    @Override
    public int modifyUserMainInfo(SysUser user) {
        String userId = user.getUserId();
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        //删除用户与组织机构关联
        if (CollUtil.isNotEmpty(user.getOrgIds())) {
            LambdaQueryWrapper<SysUserOrg> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.eq(SysUserOrg::getUserId, user.getUserId());
            String tenantId = SecurityUtils.getTenantId();
            userLambdaQueryWrapper.eq(!org.springframework.util.ObjectUtils.isEmpty(tenantId) && !tenantId.equals(0L),
                    SysUserOrg::getTenantId, tenantId);
            userOrgService.remove(userLambdaQueryWrapper);

            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setOrgIds(user.getOrgIds());
            List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
            orgList = orgList.stream().filter(o -> ObjectUtils.isNotEmpty(o.getTenantId()))
                    .toList();
            //如果为空或存在非当前租户的org抛异常
            AssertUtils.isTrue(
                    orgList.stream().allMatch(org -> org.getTenantId().equals(tenantId) || tenantId.equals("0")),
                    SystemBizError.ORG_NOT_EXIST_OR_NOT_TENANT);

            List<SysUserOrg> userOrgList = orgList.stream().map(org -> {
                SysUserOrg userOrg = new SysUserOrg();
                userOrg.setUserId(userId);
                userOrg.setOrgId(org.getOrgId());
                userOrg.setTenantId(org.getTenantId());
                return userOrg;
            }).collect(Collectors.toList());
            userOrgService.saveBatch(userOrgList);
        }
        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUser::getUserId, userId);
        updateWrapper.set(StringUtils.isNotEmpty(user.getNickName()), SysUser::getNickName, user.getNickName());
        updateWrapper.set(SysUser::getEmail, user.getEmail());
        updateWrapper.set(SysUser::getOrderNum, user.getOrderNum());
        updateWrapper.set(SysUser::getSex, user.getSex());
        updateWrapper.set(SysUser::getValidityPeriod, user.getValidityPeriod());
        updateWrapper.set(SysUser::getRemark, user.getRemark());
        updateWrapper.set(SysUser::getClassified, user.getClassified());
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && !user.getPhonenumber().contains("*")) {//与前端约定好，只有手机号发生变更了才传这个参数
            try {
                String encrypt = encryptService.encrypt(null, user.getPhonenumber());
                updateWrapper.set(SysUser::getPhonenumber, encrypt);
            } catch (Exception e) {
                log.error("手机号加密失败", e);
            }
        }
        updateWrapper.set(SysUser::getUpdateBy, SecurityUtils.getUsername());
        updateWrapper.set(SysUser::getUpdateTime, new Date());
        return this.update(updateWrapper) ? 1 : 0;
    }


    private void dealWithUserRoleStateMachine(SysUser oldUser, String tenantId, String[] roleIds) {
        List<String> roleIdList = Arrays.asList(roleIds);
        LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
        userRoleQuery.eq(SysUserRole::getUserId, oldUser.getUserId()).eq(SysUserRole::getTenantId, tenantId);
        // 原有角色集合
        List<SysUserRole> oldUserRoleList = userRoleService.list(userRoleQuery);
        Set<String> oldRoleIds = oldUserRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toSet());
        // 原有和现有一起的角色集合查询
        Set<String> allRoleIds = new HashSet<>(oldRoleIds.size() + roleIdList.size());
        allRoleIds.addAll(oldRoleIds);
        allRoleIds.addAll(roleIdList);
        List<SysRole> sysRoles = roleService.listByIds(allRoleIds);
        // 原有和现有一起的角色集合转map
        Map<String, String> roleMap = sysRoles.stream()
                .collect(Collectors.toMap(SysRole::getRoleId, SysRole::getRoleName));
        if (CollectionUtils.isEmpty(roleMap)) {
            throw new BizException(SystemBizError.ROLE_NOT_EXISTS);
        }
        Map<String, String> userOrg = orgService.getOrgPathByUserId(Collections.singletonList(oldUser.getUserId()));
        String orgStr = userOrg.get(oldUser.getUserId());
        List<SysOperateInfo> deleteAuthRoleList = new ArrayList<>(roleIds.length);
        List<SysOperateInfo> createAuthRoleList = new ArrayList<>(roleIds.length);
        for (SysUserRole sr : oldUserRoleList) {
            // 修改后的角色id集合如果不包含原有的角色id，则删除原有的角色
            if (!roleIdList.contains(sr.getRoleId())) {
                SysOperateInfo operateInfo = new SysOperateInfo();
                operateInfo.setUserId(oldUser.getUserId());
                operateInfo.setUserName(oldUser.getUserName());
                operateInfo.setNickName(oldUser.getNickName());
                operateInfo.setRoleName(roleMap.get(sr.getRoleId()));
                operateInfo.setRoleId(sr.getRoleId());
                operateInfo.setOrgName(orgStr);
                deleteAuthRoleList.add(operateInfo);
            }
        }
        for (String rid : roleIdList) {
            if (roleMap.get(rid) == null) {
                throw new BizException(SystemBizError.ROLE_NOT_EXISTS);
            }
            // 原有的角色id集合如果不包含修改后的id，则新增角色
            if (!oldRoleIds.contains(rid)) {
                SysOperateInfo operateInfo = new SysOperateInfo();
                operateInfo.setUserId(oldUser.getUserId());
                operateInfo.setUserName(oldUser.getUserName());
                operateInfo.setNickName(oldUser.getNickName());
                operateInfo.setRoleName(roleMap.get(rid));
                operateInfo.setRoleId(rid);
                operateInfo.setOrgName(orgStr);
                createAuthRoleList.add(operateInfo);
            }
        }
        if (!CollectionUtils.isEmpty(createAuthRoleList)) {
            UserContext userRoleCreate = new UserContext();
            userRoleCreate.setSysOperateInfo(createAuthRoleList);
            stateMachineFlowHandler.authorizeRoleUserStateMachine(userRoleCreate);
        }
        if (!CollectionUtils.isEmpty(deleteAuthRoleList)) {
            UserContext userRoleDelete = new UserContext();
            userRoleDelete.setSysOperateInfo(deleteAuthRoleList);
            stateMachineFlowHandler.deleteRoleUserStateMachine(userRoleDelete);
        }
    }


    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void insertUserAuth(String userId, String[] roleIds) {
        if (!SecurityUtils.isAdmin()) {
            LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
            userRoleLqw.eq(SysUserRole::getUserId, userId);
            userRoleLqw.eq(SysUserRole::getTenantId, SecurityUtils.getTenantId());
            userRoleService.remove(userRoleLqw);
            insertUserRole(userId, roleIds);
        }
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        //删除当前在线用户
        for (int i = 1; i <= 999; i++) {
            String userKey = ACCESS_TOKEN + user.getUserId() + i;
            // 删除用户缓存记录
            redisService.deleteObject(userKey);
        }
        if (UserConstants.YSE_STATUS.equals(user.getStatus())) {
            redisService.delete(DOWNLOAD_MASTER_FILE_LIMIT + user.getUserId());
            redisService.delete(DOWNLOAD_ATT_FILE_LIMIT + user.getUserId());
        }
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户状态
     *
     * @param userReq 用户信息
     * @return 结果
     */
    @Override
    public Boolean batchUpdateUserStatus(UserReq userReq) {
        //删除当前在线用户
        for (String userId : userReq.getUserIdList()) {
            for (int i = 1; i <= 999; i++) {
                String userKey = ACCESS_TOKEN + userId + i;
                // 删除用户缓存记录
                redisService.deleteObject(userKey);
            }
            if (UserConstants.YSE_STATUS.equals(userReq.getStatus())) {
                redisService.delete(DOWNLOAD_MASTER_FILE_LIMIT + userId);
                redisService.delete(DOWNLOAD_ATT_FILE_LIMIT + userId);
            }
        }
        return this.lambdaUpdate().set(SysUser::getStatus, userReq.getStatus())
                .in(SysUser::getUserId, userReq.getUserIdList()).update();
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        //删除当前在线用户
        for (int i = 1; i <= 999; i++) {
            String userKey = ACCESS_TOKEN + user.getUserId() + i;
            // 删除用户缓存记录
            redisService.deleteObject(userKey);
        }

        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    public void insertUserOrg(SysUser user) {
        AssertUtils.notEmptyCollection(user.getOrgIds(), SystemBizError.USER_ORG_EMPTY);
        List<String> orgIds = user.getOrgIds();
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgIds(orgIds);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        List<SysUserOrg> sysUserOrgList = new ArrayList<>(orgIds.size());
        for (SysOrg org : orgList) {
            SysUserOrg userOrg = new SysUserOrg();
            userOrg.setUserId(user.getUserId());
            userOrg.setOrgId(org.getOrgId());
            userOrg.setTenantId(org.getTenantId());
            userOrg.setStatus(StatusEnum.ENABLE.getCode());
            userOrg.setOrderBy(1);
            sysUserOrgList.add(userOrg);
        }
        userOrgService.saveBatch(sysUserOrgList);
    }


    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(String userId, String[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>();
            List<String> tenantIdList = new ArrayList<>();
            if (SecurityUtils.isAdmin()) {
                List<SysUserOrg> userOrgList = userOrgService.lambdaQuery().eq(SysUserOrg::getUserId, userId)
                        .eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode()).select(SysUserOrg::getTenantId).list();
                tenantIdList = userOrgList.stream().map(SysUserOrg::getTenantId).distinct()
                        .collect(Collectors.toList());
            } else {
                tenantIdList.add(SecurityUtils.getTenantId());
            }
            for (String roleId : roleIds) {

                for (String tenantId : tenantIdList) {
                    SysUserRole ur = new SysUserRole();
                    ur.setUserId(userId);
                    ur.setRoleId(roleId);
                    ur.setTenantId(tenantId);
                    ur.setStatus(StatusEnum.ENABLE.getCode());
                    list.add(ur);
                }
            }
            userRoleService.saveBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public int deleteUserById(String userId) {
        // 删除用户与角色关联
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        userRoleLqw.eq(SysUserRole::getUserId, userId);
        userRoleLqw.eq(SysUserRole::getTenantId, SecurityUtils.getTenantId());
        userRoleService.remove(userRoleLqw);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public int deleteUserByIds(String[] userIds) {
        for (String userId : userIds) {
            R<Long> repoCountR = repositoryRpcService.countRepoByOwnerId(userId);
            if (repoCountR.isError()) {
                throw new RuntimeException("查询当前用户拥有的库异常");
            }
            if (repoCountR.getData() > 0) {
                throw new BizException(SystemBizError.USER_REPO_EXIST);
            }
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        LambdaUpdateWrapper<SysUserOrg> userOrgLuw = new LambdaUpdateWrapper<>();
        userOrgLuw.in(SysUserOrg::getUserId, userIds);
        userOrgService.remove(userOrgLuw);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public R importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {

        ArrayList<String> errInfo = new ArrayList<>();
        //校验userName是否存在
        List<String> userNameList = userList.stream().map(SysUser::getUserName).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(SysUser::getUserName, userNameList);
        userLqw.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
        List<SysUser> userQueryList = this.list(userLqw);
        List<String> oldUserNameList = new ArrayList<>();
        if (CollUtil.isNotEmpty(userQueryList)) {
            oldUserNameList = userQueryList.stream().map(SysUser::getUserName).distinct().toList();
        }
        //找出系统中租户下角色列表
        Set<String> roleNameSet = userList.stream().map(SysUser::getRoleNameList).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Map<String, SysRole> roleMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(roleNameSet)) {
            Set<String> set = roleNameSet.stream().map(s -> s.split(",")).flatMap(Arrays::stream)
                    .collect(Collectors.toSet());

            List<Integer> roleTypeList = CollUtil.newArrayList(RoleTypeEnum.getDefaultRoleType());
            roleTypeList.add(RoleTypeEnum.CONFIG_ADMIN.getCode());
            RoleImportDto dto = new RoleImportDto();
            dto.setRoleTypeList(roleTypeList);
            dto.setTenantIdList(CollUtil.newArrayList(SecurityUtils.getTenantId(), "0"));
            dto.setRoleNames(set);
            dto.setRoleDelFlag(UserConstants.YSE_STATUS);
            List<SysRole> roleList = roleService.listByRoleImportDto(dto);
            if (CollUtil.isNotEmpty(roleList)) {
                roleMap = roleList.stream().collect(Collectors.toMap(SysRole::getRoleName,
                        Function.identity()));
            }
        }

        String password = configService.selectConfigByKey("sys.user.initPassword");

        Map<String, Set> result = new HashMap<>(2);
        List<Integer> errorDataList = new ArrayList<>();
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        //记录循环后的剩余异常数据
        Map<Integer, SysUser> rightDataMap = new HashMap<>();
        for (int i = 0; i < userList.size(); i++) {
            Boolean checkResult = importUserCheck(userList, i, errorDataList, rightDataMap, queryWrapper, roleMap);
            if (!checkResult) {
                continue;
            }
            SysUser user = rightDataMap.get(i);
            //用户策略校验
            if (!checkSLC()) {
                user.setStatus(StatusEnum.DISABLE.getCode() + "");
            }
            user.setPassword(
                    DigestUtil.md5Hex(("suwell:" + password).getBytes(StandardCharsets.UTF_8)));
            user.setCreateBy(SecurityUtils.getUsername());
            user.setUserId(wfgIdGenerator.next() + "");
            user.setWpsAuth(UserConstants.WPS_AUTH);
            rightDataMap.put(i, user);
        }

        result.put("errorData", errorDataList.stream().map(o -> o + 3).collect(Collectors.toSet()));
        result.put("rightData", rightDataMap.keySet().stream().map(o -> o + 3).collect(Collectors.toSet()));
        if (CollUtil.isNotEmpty(rightDataMap)) {
            rightDataMap.values().forEach(this::insertUser);
        }
        return R.ok(result);
    }

    /**
     * 导入用户的数据校验
     *
     * @param userList
     * @param i
     * @param errInfoList
     * @param rightDataMap
     */
    public Boolean importUserCheck(List<SysUser> userList, int i, List<Integer> errInfoList,
            Map<Integer, SysUser> rightDataMap, LambdaQueryWrapper<SysUser> queryWrapper,
            Map<String, SysRole> roleMap) {
        SysUser user = userList.get(i);
        try {
            BeanValidators.defaultValidate(user);
        } catch (Exception e) {
            errInfoList.add(i);
            return false;
        }
        if (StringUtils.isNotEmpty(user.getSrcId())) {
            if (userList.stream().filter(o -> o.getSrcId().equals(user.getSrcId().trim())).count() > 1) {
                errInfoList.add(i);
                return false;
            }
            //校验数据库中来源id是否存在重复数据
            queryWrapper.clear();
            queryWrapper.eq(StringUtils.isNotEmpty(user.getSrcId().trim()), SysUser::getSrcId, user.getSrcId().trim());
            queryWrapper.eq(SysUser::getDelFlag,UserStatus.OK.getCode());
            long coountBySrcId = this.count(queryWrapper);
            if (coountBySrcId > 0) {
                errInfoList.add(i);
                return false;
            }
        }
        //校验是否存在同名数据
        queryWrapper.clear();
        queryWrapper.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS)
                .eq(SysUser::getUserName, user.getUserName().trim());
        long countByUserName = this.count(queryWrapper);
        if (countByUserName > 0) {
            //存在同名数据
            errInfoList.add(i);
            return false;
        }

        //校验用户组织信息
        List<String> orgIdList = getOrgIdsByPath(user.getOrgPaths());
        if (CollUtil.isEmpty(orgIdList)) {
            errInfoList.add(i);
            return false;
        }
        user.setOrgIds(orgIdList);
        // 导入用户角色，若系统中不存在该角色则自动忽略；
        if (StringUtils.isNotEmpty(user.getRoleName())) {
            String[] roleNameArray = user.getRoleName().split(",");
            user.setRoleIds(Arrays.stream(roleNameArray).filter(roleMap::containsKey)
                    .map(roleName -> roleMap.get(roleName).getRoleId()).toArray(String[]::new));
        } else {
            log.warn("若系统中不存在该角色则自动忽略；角色{}不存在", user.getRoleName());
        }
        rightDataMap.put(i, user);
        return true;
    }

    public List<String> getOrgIdsByPath(String orgPath) {
        List<String> orgIdList = new ArrayList<>();
        String[] orgNamePathList = orgPath.split(",");
        List<SysOrg> rootOrgList = orgService.getRootOrgList();
        for (String orgNamePath : orgNamePathList) {
            String[] orgNameList = orgNamePath.split("/");
            long count = rootOrgList.stream().filter(o -> o.getOrgName().equals(orgNameList[0])).count();
            if (count != 1) {
                return null;
            }
            String parentId = rootOrgList.stream().filter(o -> o.getOrgName().equals(orgNameList[0])).findAny().get()
                    .getOrgId();
            OrgStatusFindDto dto = new OrgStatusFindDto();
            SimpleOrgFindDto simpleDto = new SimpleOrgFindDto();
            for (int j = 1; j < orgNameList.length; j++) {
                String orgName = orgNameList[j];
                dto.setDelFlag(UserConstants.YSE_STATUS);
                dto.setStatus(UserConstants.YSE_STATUS);
                dto.setParentId(parentId);
                dto.setOrgName(orgName);
                long orgCount = orgService.countByStatusDto(dto);
                if (orgCount != 1) {
                    return null;
                }
                simpleDto.setDelFlag(dto.getDelFlag());
                simpleDto.setStatus(dto.getStatus());
                simpleDto.setParentIds(CollUtil.newArrayList(dto.getParentId()));
                simpleDto.setOrgName(dto.getOrgName());
                parentId = orgService.getSimpleOrgByDto(simpleDto).getOrgId();
            }
            orgIdList.add(parentId);
        }

        return orgIdList;
    }

    @Override
    public List<String> selectAllOrgChildrenByUserId(String userId) {
//        SysUser user = this.getById(userId);
        LambdaQueryWrapper<SysUserOrg> userOrgWrapper = new LambdaQueryWrapper<>();
        userOrgWrapper.eq(SysUserOrg::getUserId, userId);
        List<SysUserOrg> userOrgs = userOrgMapper.selectList(userOrgWrapper);
        if (CollectionUtils.isEmpty(userOrgs)) {
            return new ArrayList<>();
        }
        List<String> orgIds = userOrgs.stream().map(SysUserOrg::getOrgId).toList();
        LambdaQueryWrapper<SysOrg> orgLqw = new LambdaQueryWrapper<>();
        orgLqw.eq(SysOrg::getDelFlag, UserConstants.YSE_STATUS);
        orgLqw.eq(SysOrg::getStatus, UserConstants.YSE_STATUS);
        Set<String> tenantIds = userOrgs.stream().map(SysUserOrg::getTenantId).collect(Collectors.toSet());
        orgLqw.in(SysOrg::getTenantId, new ArrayList<>(tenantIds));
        orgLqw.and(lqw -> {
            for (String orgId : orgIds) {
                lqw.or().like(SysOrg::getAncestors, orgId);
            }
        });
        List<SysOrg> orgList = orgMapper.selectList(orgLqw);
        List<String> childrenOrgIdList = orgList.stream().map(SysOrg::getOrgId).collect(Collectors.toList());
        childrenOrgIdList.addAll(orgIds);
        return childrenOrgIdList;
    }

    /**
     * 校验用户是否需要修改密码
     */
    @Override
    public CheckPasswordUpdateResp checkUpdatePassword() {
        CheckPasswordUpdateResp checkPasswordUpdateResp = new CheckPasswordUpdateResp();
        checkPasswordUpdateResp.setStatus(1);
        //运营管理员跳过强制修改密码
        if (SecurityUtils.isAdmin()) {
            return checkPasswordUpdateResp;
        }
        SysConfig config = configService.getByConfigKey(ConfigConstant.CONFIG_FORCE_UPDATE_PASSWORD_CYCLE);
        if (ObjectUtils.isEmpty(config) || ObjectUtils.isEmpty(config.getConfigValue())) {
            return checkPasswordUpdateResp;
        }
        int days = Integer.parseInt(config.getConfigValue());
        if (days > 0) {
            SysUser user = this.getById(SecurityUtils.getUserId());
            LocalDateTime pwdUpdateTime = user.getPwdUpdateTime();
            if (pwdUpdateTime.plusDays(days).isBefore(LocalDateTime.now())) {
                checkPasswordUpdateResp.setStatus(2);
            }
        }
        return checkPasswordUpdateResp;
    }

    @Override
    public Map<String, Object> getDetailByUserId(String userId) {
        Map<String, Object> result = new HashMap<>();
        SysUser sysUser = this.getById(userId);
        sysUser.setPassword(null);

        //手机号解密并脱敏
        if (StringUtils.isNotEmpty(sysUser.getPhonenumber())) {
            try {
                String decrypt = encryptService.decrypt(null, sysUser.getPhonenumber());
                if (decrypt.length() == 11) {
                    String s = decrypt.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                    sysUser.setPhonenumber(s);
                } else {//兼容非手机号，比如座机号
                    String s = decrypt.replaceAll("(\\d{2})\\d{1,9}(\\d{2})", "$1****$2");
                    sysUser.setPhonenumber(s);
                }
            } catch (Exception e) {
                log.error("手机号解密失败", e);
            }
        }
        if (Objects.isNull(sysUser.getClassified())) {
            sysUser.setClassified(UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL.getCode());
        }

        result.put("sysUser", sysUser);
        List<SysOrg> orgList = orgService.getOrgByUserId(userId, SecurityUtils.getTenantId());
        result.put("orgList", orgList);
        List<SysRole> roleList = roleService.selectRolesByUserId(userId);
        result.put("roleList", roleList);
        List<SysPost> postList = postService.getByUserId(userId);
        result.put("postList", postList);
        return result;
    }

    /**
     * 用户对接，添加用户
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public List<SysUser> syncAdd(List<SysUser> userList) {
        List<SysUser> failUserList = new ArrayList<>();
        List<SysUser> successUserList = new ArrayList<>();
        List<SysUserOrg> sysUserOrgList = new ArrayList<>();
        int errorIndex = 0;
        for (int i = 0; i < userList.size(); i++) {
            SysUser sysUser = userList.get(i);
            String userId = wfgIdGenerator.next() + "";
            sysUser.setUserId(userId);
            //筛选掉用户名称或组织机构id为空的数据，
            String userName = sysUser.getUserName();
            List<String> akList = sysUser.getOrgAccessKeys();
            if (StringUtils.isBlank(userName) || CollectionUtil.isEmpty(akList)) {
                failUserList.add(sysUser);
                continue;
            }
            //查询组织机构id,判断组织机构id是否存在
            LambdaQueryWrapper<SysOrg> sysOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysOrgLambdaQueryWrapper.in(SysOrg::getAccessKey, akList)
                    .eq(SysOrg::getStatus, UserConstants.YSE_STATUS)
                    .eq(SysOrg::getDelFlag, UserConstants.YSE_STATUS);
            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setAccessKeys(akList);
            dto.setStatus(UserConstants.YSE_STATUS);
            dto.setDelFlag(UserConstants.YSE_STATUS);
            List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
            //当前用户绑定了某个不存在的组织机构id
            if (orgList == null || orgList.size() != akList.size()) {
                failUserList.add(sysUser);
                errorIndex = i + 1;
                break;
            }
            successUserList.add(sysUser);
            Map<String, String> akOrgIdMap = orgList.stream()
                    .collect(Collectors.toMap(SysOrg::getAccessKey, SysOrg::getOrgId));
            for (String ak : akList) {
                SysUserOrg userOrg = new SysUserOrg();
                userOrg.setUserId(userId);
                userOrg.setOrgId(akOrgIdMap.get(ak));
                sysUserOrgList.add(userOrg);
            }
        }
        //如果errorIndex不等于0，则表示中途有用户绑定不存在组织机构而跳出了循环
        failUserList.addAll(userList.subList(errorIndex, userList.size()));
        userOrgService.saveBatch(sysUserOrgList);
        checkUserWps(successUserList.size());
        this.saveBatch(successUserList);
        return failUserList;
    }

    @Override
    public List<SysUser> syncDelete(List<SysUser> userList) {
        List<SysUser> errorUserList = new ArrayList<>();
        List<SysUser> successUserList = new ArrayList<>();
        checkUserList(userList, successUserList, errorUserList);
        if (successUserList.isEmpty()) {
            return errorUserList;
        }
        List<String> userIdList = successUserList.stream().map(SysUser::getUserId).collect(Collectors.toList());
        //删除用户
        LambdaUpdateWrapper<SysUser> sysUserLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserLambdaUpdateWrapper.in(SysUser::getUserId, userIdList);
        sysUserLambdaUpdateWrapper.set(SysUser::getDelFlag, UserConstants.NO_STATUS);
        this.update(sysUserLambdaUpdateWrapper);
        //删除用户组织机构信息
        LambdaUpdateWrapper<SysUserOrg> sysUserOrgLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserOrgLambdaUpdateWrapper.in(SysUserOrg::getUserId, userIdList);
        userOrgService.remove(sysUserOrgLambdaUpdateWrapper);
        return errorUserList;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public List<SysUser> syncUpdate(List<SysUser> userList) {
        List<SysUser> errorUserList = new ArrayList<>();
        List<SysUser> successUserList = new ArrayList<>();
        checkUserList(userList, successUserList, errorUserList);
        userList = successUserList;
        successUserList.clear();
        checkUserOrg(userList, successUserList, errorUserList);
        if (successUserList.isEmpty()) {
            return errorUserList;
        }
        this.updateBatchById(successUserList);
        Map<String, List<String>> userOrgMap = successUserList.stream()
                .filter(sysUser -> CollectionUtil.isNotEmpty(sysUser.getOrgIds()))
                .collect(Collectors.toMap(SysUser::getUserId, SysUser::getOrgIds));
        Set<String> userIds = userOrgMap.keySet();
        List<SysUserOrg> sysUserOrgs = new ArrayList<>();
        for (String userId : userIds) {
            for (String orgId : userOrgMap.get(userId)) {
                SysUserOrg userOrg = new SysUserOrg();
                userOrg.setUserId(userId);
                userOrg.setOrgId(orgId);
                sysUserOrgs.add(userOrg);
            }
        }
        //删除用户组织机构信息
        LambdaUpdateWrapper<SysUserOrg> sysUserOrgLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserOrgLambdaUpdateWrapper.in(SysUserOrg::getUserId, userIds);
        userOrgService.remove(sysUserOrgLambdaUpdateWrapper);
        userOrgService.saveBatch(sysUserOrgs);
        return errorUserList;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public List<SysUser> syncAddOrUpdate(List<SysUser> userList) {
        List<SysUser> errorUserList = new ArrayList<>();
        List<SysUser> successUserList = new ArrayList<>();
        checkUserOrg(userList, successUserList, errorUserList);
        if (successUserList.isEmpty()) {
            return errorUserList;
        }
        List<String> userAkList = successUserList.stream().map(SysUser::getAccessKey).collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(SysUser::getAccessKey, userAkList);
        List<SysUser> existUserList = this.list(userLqw);
        Map<String, SysUser> userAkMap = existUserList.stream()
                .collect(Collectors.toMap(SysUser::getAccessKey, o -> o));
        successUserList.forEach(user -> {
            user.setUpdateTime(new Date());
            if (userAkMap.containsKey(user.getAccessKey())) {
                user.setUserId(userAkMap.get(user.getAccessKey()).getUserId());
            }
        });
        checkUserWps(successUserList.size());
        this.saveOrUpdateBatch(successUserList);
        List<SysUserOrg> userOrgList = new ArrayList<>();
        successUserList.forEach(u -> {
            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setOrgCodes(u.getOrgAccessKeys());
            List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
            for (SysOrg org : orgList) {
                SysUserOrg userOrg = new SysUserOrg();
                userOrg.setUserId(u.getUserId());
                userOrg.setOrgId(org.getOrgId());
                userOrgList.add(userOrg);
            }
        });
        List<String> userIdList = successUserList.stream().map(SysUser::getUserId).collect(Collectors.toList());
        LambdaUpdateWrapper<SysUserOrg> sysUserOrgLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserOrgLambdaUpdateWrapper.in(SysUserOrg::getUserId, userIdList);
        userOrgService.remove(sysUserOrgLambdaUpdateWrapper);
        userOrgService.saveBatch(userOrgList);
        return errorUserList;
    }

    /**
     * 查询该部门下除去某个用户外其余的人
     */
    @Override
    public List<SysUser> getByOrg(String orgId, String userId) {
        LambdaQueryWrapper<SysUserOrg> userOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userOrgLambdaQueryWrapper.eq(SysUserOrg::getOrgId, orgId).ne(SysUserOrg::getUserId, userId);
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLambdaQueryWrapper);
        if (userOrgList.isEmpty()) {
            return null;
        }
        List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
        return this.listByIds(userIdList);
    }

    private void checkUserList(List<SysUser> userList, List<SysUser> successUserList, List<SysUser> errorUserList) {
        List<String> userAkList = userList.stream().map(SysUser::getAccessKey).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserLambdaQueryWrapper.in(SysUser::getAccessKey, userAkList)
                .eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
        List<SysUser> userQueryList = this.list(sysUserLambdaQueryWrapper);
        if (userQueryList.isEmpty()) {
            errorUserList.addAll(userList);
            return;
        }
        List<String> queryUserIdList = userQueryList.stream().map(SysUser::getAccessKey).toList();
        //参数中包含不存在系统中的用户id
        errorUserList.addAll(userList.stream().filter(sysUser -> !queryUserIdList.contains(sysUser.getAccessKey()))
                .toList());
        successUserList.addAll(userQueryList);
    }

    public void checkUserOrg(List<SysUser> userList, List<SysUser> successUserList, List<SysUser> errorUserList) {
        List<String> accessKsyList = userList.stream().map(SysUser::getOrgAccessKeys)
                .collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);

        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgCodes(accessKsyList);
        dto.setDelFlag(UserConstants.YSE_STATUS);
        dto.setStatus(UserConstants.YSE_STATUS);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        if (orgList.isEmpty()) {
            errorUserList.addAll(userList);
            return;
        }
        if (accessKsyList.size() == orgList.size()) {
            successUserList.addAll(userList);
            return;
        }
        Set<String> resultOrgCodeList = orgList.stream().map(SysOrg::getOrgCode).collect(Collectors.toSet());
        errorUserList.addAll(userList.stream().filter(
                sysUser -> !resultOrgCodeList.containsAll(sysUser.getOrgAccessKeys())).collect(Collectors.toList()));
        successUserList.addAll(userList.stream().filter(
                sysUser -> resultOrgCodeList.containsAll(sysUser.getOrgAccessKeys())).collect(Collectors.toList()));
    }

    @Override
    public void reviseUserPassword(ReviseUserPasswordReq reviseUserPasswordReq) {
        String userId = SecurityUtils.getUserId();
        SysUser sysUser = this.getById(userId);
        AssertUtils.isTrue(SecurityUtils.matchesPassword(reviseUserPasswordReq.getOldPassword(), sysUser.getPassword()),
                SystemBizError.USER_PASSWORD_ERROR);
        AssertUtils.isFalse(
                StringUtils.equals(reviseUserPasswordReq.getNewPassword(), reviseUserPasswordReq.getOldPassword()),
                SystemBizError.USER_PASSWORD_CHANGE_ERROR);
        LambdaUpdateWrapper<SysUser> userLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        userLambdaUpdateWrapper.eq(SysUser::getUserId, userId);
        userLambdaUpdateWrapper.set(SysUser::getPassword,
                SecurityUtils.encryptPassword(reviseUserPasswordReq.getNewPassword()));
        userLambdaUpdateWrapper.set(SysUser::getUpdateBy, sysUser.getUserName());
        userLambdaUpdateWrapper.set(SysUser::getUpdateTime, new Date());
        userLambdaUpdateWrapper.set(SysUser::getPwdUpdateTime, LocalDateTime.now());
        this.update(userLambdaUpdateWrapper);
    }

    @Override
    public void export(HttpServletResponse response, SysUser user) {
        String fileName = "导出用户" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        try {
            fileName = URLEncoder.encode(fileName, "UTF8");
        } catch (UnsupportedEncodingException e) {
            throw new BizException(SystemBizError.USER_IMPORT_TEMPLATE_DOWNLOAD_ERROR);
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.init(null, "后台管理用户数据", null, Type.ALL);
        SysOrgService.queryCondition.set(user);
        buildUserCondition(user);
        int queryType = 0;
        if (ObjectUtils.isNotEmpty(user.getParams()) && user.getParams().containsKey("queryType")) {
            queryType = Integer.parseInt(user.getParams().get("queryType").toString());
        }
        long current = 1;
        long size = 10000;
        IPage<SysUser> userPage = userMapper.selectUserPage(new Page<>(current, size), user, queryType);

        if (CollUtil.isEmpty(userPage.getRecords())) {
            util.exportExcel(response);
            return;
        }

        long totalPages = userPage.getPages();
        List<SysUser> records = userPage.getRecords();
        try (OutputStream outputStream = response.getOutputStream()) {
            while (current < totalPages) {
                current++;
                userPage = userMapper.selectUserPage(new Page<>(current, size), user, queryType);
                records = userPage.getRecords();
                if (!CollectionUtils.isEmpty(records)) {
                    buildUserInfo(records);
                    util.exportExcel(outputStream, records, "后台管理用户数据");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {

        }

    }

    @Override
    public void exportV2(HttpServletResponse response, SysUser user) {
        String fileName = "导出用户" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String sheetName = "后台管理用户数据";
        SysOrgService.queryCondition.set(user);
        buildUserCondition(user);
        int queryType = 0;
        if (ObjectUtils.isNotEmpty(user.getParams()) && user.getParams().containsKey("queryType")) {
            queryType = Integer.parseInt(user.getParams().get("queryType").toString());
        }
        final int queryTypeFinal = queryType;
        int pageSize = 10000;
        new EasyExcelExport<SysUserExportDTO, SysUser>() {
            @Override
            public SysUserExportDTO convertSourceData2ExportEntity(SysUser sourceData, Integer lineNum) {
                SysUserExportDTO dto = new SysUserExportDTO();
                BeanUtils.copyProperties(sourceData, dto);
                return dto;
            }

            @Override
            public List<SysUser> getData(int currentBatch) {
                IPage<SysUser> userPage = userMapper.selectUserPage(new Page<>(currentBatch, pageSize), user,
                        queryTypeFinal);
                List<SysUser> userList = userPage.getRecords();
                if (!CollectionUtils.isEmpty(userList)) {
                    buildUserInfo(userList);
                }
                return userList;
            }
        }.easyExcelBatchExport(fileName, sheetName, response);
    }

    /**
     * 通过上级组织机构id查询下级组织机构，返回数据中带组织机构下的用户
     *
     * @param orgReq 组织机构id
     */
    @Override
    public SysOrgResp getLazyListWithUser(OrgReq orgReq) {
        SysOrgResp orgResp = new SysOrgResp();
        orgResp.setOrgId(orgReq.getOrgId());
        List<Object> children = new ArrayList<>();
        SysOrg org = new SysOrg();
        if (orgReq.getIsTenant()) {
            org = orgService.getLazyList(null, orgReq.getOrgId(), true);
        } else {
            if (!"0".equals(orgReq.getOrgId())) {
                org = orgService.simpleGetById(orgReq.getOrgId());
            }
            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setParentIds(CollUtil.newArrayList(orgResp.getOrgId()));
            dto.setDelFlag(UserConstants.YSE_STATUS);
            dto.setStatus(UserConstants.YSE_STATUS);
            org.setChildren(orgService.listSimpleOrgByDto(dto));
        }

        BeanUtils.copyProperties(org, orgResp);
        List<SysOrg> orgChildren = org.getChildren();
        for (SysOrg o : orgChildren) {
            SysOrgResp sonOrgResp = new SysOrgResp();
            BeanUtils.copyProperties(o, sonOrgResp);
            sonOrgResp.setDataType("1");
            children.add(sonOrgResp);
        }
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getOrgId, orgReq.getOrgId());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (!userOrgList.isEmpty()) {
            LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
            userLqw.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
            userLqw.eq(SysUser::getStatus, UserConstants.YSE_STATUS);
            List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
            userLqw.in(SysUser::getUserId, userIdList);
            List<SysUser> userList = this.list(userLqw);
            if (!userList.isEmpty()) {
                List<String> userResultIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
                Map<String, String> orgPathByUserId = orgService.getOrgPathByUserId(userResultIdList);
                userList.forEach(u -> {
                    SysUserResp sysUserResp = new SysUserResp();
                    BeanUtils.copyProperties(u, sysUserResp);
                    sysUserResp.setDataType("2");
                    if (orgPathByUserId.containsKey(u.getUserId())) {
                        sysUserResp.setOrgPaths(orgPathByUserId.get(u.getUserId()));
                    }
                    children.add(sysUserResp);
                });
            }

        }
        if (!children.isEmpty()) {
            orgResp.setLeaf("1");
        }
        orgResp.setChildren(children);
        return orgResp;
    }

    @Override
    public List<SysUser> getAll(String userName, String isTenant) {
        List<SysUser> userList = new ArrayList<>();
        LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
        userLambdaQueryWrapper.eq(SysUser::getStatus, UserConstants.YSE_STATUS);
        userLambdaQueryWrapper.like(StringUtils.isNotEmpty(userName), SysUser::getNickName, userName);
        if (Boolean.parseBoolean(isTenant)) {
            LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
            userOrgLqw.eq(SysUserOrg::getTenantId, SecurityUtils.getTenantId());
            userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
            List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
            if (CollUtil.isEmpty(userOrgList)) {
                return userList;
            }
            List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
            userLambdaQueryWrapper.in(SysUser::getUserId, userIdList);
        }
        userList = this.list(userLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(userList)) {
            return userList;
        }
        List<String> userIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
        Map<String, String> orgPathByUserId = orgService.getOrgPathByUserId(userIdList);
        userList.forEach(user -> {
            user.setDataType("2");
            user.setOrgPaths(orgPathByUserId.get(user.getUserId()));
        });
        return userList;
    }

    @Override
    public boolean checkUserId(String userId, String tenantId) {
        SysUser sysUser = this.lambdaQuery().eq(SysUser::getDelFlag, UserConstants.YSE_STATUS)
                .eq(SysUser::getUserId, userId)
                .eq(SysUser::getStatus, UserConstants.YSE_STATUS)
                .one();
        return Objects.isNull(sysUser);
    }

    @Override
    public List<SysUser> getUserListByOrgList(List<String> orgIdList) {
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgIds(orgIdList);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        AssertUtils.notEmptyCollection(orgList, SystemBizError.ORG_ID_NOT_NULL_ERROR);

        dto.setOrgIds(null);
        dto.setDelFlag(UserConstants.YSE_STATUS);
        dto.setStatus(UserConstants.YSE_STATUS);
        dto.setLikeAncestorsList(orgList.stream().map(o -> {
            return o.getAncestors() + "," + o.getOrgId();
        }).toList());
        List<SysOrg> allOrgList = orgService.listSimpleOrgByDto(dto);
        List<String> allOrgIdList = allOrgList.stream().map(SysOrg::getOrgId).collect(Collectors.toList());
        for (SysOrg sysOrg : orgList) {
            if (!allOrgIdList.contains(sysOrg.getOrgId())) {
                allOrgIdList.add(sysOrg.getOrgId());
                allOrgList.add(sysOrg);
            }
        }

        Map<String, SysOrg> orgMap = allOrgList.stream().collect(Collectors.toMap(SysOrg::getOrgId, org -> org));
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.in(SysUserOrg::getOrgId, allOrgIdList);
        long count = userOrgService.count(userOrgLqw);
        String maxCountStr = configService.selectConfigByKey(REPO_MAX_COUNT_KEY);
        if (count > Integer.parseInt(maxCountStr)) {
            throw new BizException(SystemBizError.USER_MAX_COUNTL_ERROR);
        }
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return new ArrayList<>();
        }
        List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).distinct()
                .collect(Collectors.toList());
        List<SysUser> userList = this.listByIds(userIdList);
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }
        Map<String, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, user -> user));
        ArrayList<SysUser> resultList = new ArrayList<>();
        for (SysUserOrg userOrg : userOrgList) {
            SysUser sysUser = new SysUser();
            if (userMap.containsKey(userOrg.getUserId())) {
                SysUser user = userMap.get(userOrg.getUserId());
                BeanUtils.copyProperties(user, sysUser);
                if (orgMap.containsKey(userOrg.getOrgId())) {
                    SysOrg org = orgMap.get(userOrg.getOrgId());
                    sysUser.setOrgName(org.getOrgName());
                    resultList.add(sysUser);
                }
            }
        }
        return resultList;
    }

    @Override
    public List<SysUser> getUserListByRoleList(List<String> roleIdList) {
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        userRoleLqw.in(SysUserRole::getRoleId, roleIdList);
        List<SysUserRole> userRoleList = userRoleMapper.selectList(userRoleLqw);
        long count = userRoleMapper.selectCount(userRoleLqw);
        String maxCountStr = configService.selectConfigByKey(REPO_MAX_COUNT_KEY);
        if (count > Integer.parseInt(maxCountStr)) {
            throw new BizException(SystemBizError.USER_MAX_COUNTL_ERROR);
        }
        if (CollUtil.isEmpty(userRoleList)) {
            return new ArrayList<>();
        }
        List<String> userIdList = userRoleList.stream().map(SysUserRole::getUserId).distinct()
                .collect(Collectors.toList());
        return this.listByIds(userIdList);
    }

    @Override
    public List<SysUser> getByDeptId(List<String> deptIdList) {
        List<SysUser> userList = new ArrayList<>();
        Map<String, String> orgMap = new HashMap<>();
        for (String deptId : deptIdList) {
            orgMap.put(deptId, deptId);
        }
        orgMap = this.getByOrgId(deptIdList, orgMap);
        //获取该单位下的所有部门及本身
        Set<String> orgIdList = orgMap.keySet();
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.in(SysUserOrg::getOrgId, orgIdList);
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return userList;
        }
        HashMap<String, List<String>> userOrgMap = new HashMap<>();
        for (SysUserOrg userOrg : userOrgList) {
            List<String> userOrgIdList = new ArrayList<>();
            if (userOrgMap.containsKey(userOrg.getUserId())) {
                userOrgIdList = userOrgMap.get(userOrg.getUserId());
            }
            userOrgIdList.add(userOrg.getOrgId());
            userOrgMap.put(userOrg.getUserId(), userOrgIdList);
        }

        List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(SysUser::getUserId, userIdList);
        userLqw.eq(SysUser::getDelFlag, UserConstants.YSE_STATUS);
        userLqw.eq(SysUser::getStatus, UserConstants.YSE_STATUS);
        userList = this.list(userLqw);
        for (SysUser sysUser : userList) {
            List<String> userOrgIdList = userOrgMap.getOrDefault(sysUser.getUserId(), new ArrayList<>());
            if (CollUtil.isNotEmpty(userOrgIdList)) {
                List<String> userDeptIdList = new ArrayList<>();
                for (String userOrgId : userOrgIdList) {
                    if (orgMap.containsKey(userOrgId)) {
                        String deptId = orgMap.get(userOrgId);
                        if (!CollUtil.contains(userDeptIdList, deptId)) {
                            userDeptIdList.add(deptId);
                        }
                    }
                }
                sysUser.setDeptIds(userDeptIdList);
            }
        }
        return userList;
    }

    /**
     * 根据单位id，获取所有部门和单位对应map
     */
    public Map<String, String> getByOrgId(List<String> orgIdList, Map<String, String> orgMap) {
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgIds(orgIdList);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        List<String> orgParentStr = orgList.stream().map(org -> org.getAncestors() + "," + org.getOrgId())
                .collect(Collectors.toList());
        dto.setOrgIds(null);
        dto.setOrgType(OrgTypeEnum.DEPARTMENT.getCode());
        dto.setAncestorsList(orgParentStr);
        List<SysOrg> sonOrgList = orgService.listSimpleOrgByDto(dto);
        if (CollUtil.isEmpty(sonOrgList)) {
            return orgMap;
        }
        for (SysOrg sonOrg : sonOrgList) {
            orgMap.put(sonOrg.getOrgId(), orgMap.getOrDefault(sonOrg.getOrgId(), sonOrg.getOrgId()));
        }
        List<String> sonOrgIdList = sonOrgList.stream().map(SysOrg::getOrgId).collect(Collectors.toList());
        this.getByOrgId(sonOrgIdList, orgMap);
        return orgMap;
    }

    @Override
    public List<String> getUserRoleIdList(String userId) {
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        userRoleLqw.eq(SysUserRole::getUserId, userId);
        userRoleLqw.eq(SysUserRole::getTenantId, SecurityUtils.getTenantId());
        userRoleLqw.eq(SysUserRole::getStatus, 1);
        List<SysUserRole> userRoleList = userRoleMapper.selectList(userRoleLqw);
        return userRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
    }

    @Override
    public List<SysUser> getUserList(List<String> userIdList) {
        List<SysUser> userList = this.listByIds(userIdList);
        if (CollUtil.isNotEmpty(userList)) {
            List<String> resultUserIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
            LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
            userOrgLqw.in(SysUserOrg::getUserId, resultUserIdList);
            List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
            List<String> orgIdList = userOrgList.stream().map(SysUserOrg::getOrgId).collect(Collectors.toList());
            List<SysOrg> orgList = new ArrayList<>();
            if (CollUtil.isNotEmpty(orgIdList)) {
                SimpleOrgFindDto dto = new SimpleOrgFindDto();
                dto.setOrgIds(orgIdList);
                orgList = orgService.listSimpleOrgByDto(dto);
            }
            for (SysUser user : userList) {
                if (CollUtil.isNotEmpty(userOrgList)) {
                    List<String> userOrgIdList = userOrgList.stream()
                            .filter(item -> item.getUserId().equals(user.getUserId()))
                            .map(SysUserOrg::getOrgId).collect(
                                    Collectors.toList());
                    if (CollUtil.isNotEmpty(userOrgIdList)) {
                        List<SysOrg> tempOrgList = orgList.stream()
                                .filter(item -> userOrgIdList.contains(item.getOrgId()))
                                .collect(Collectors.toList());
                        user.setOrgList(tempOrgList);
                    }
                }
            }
        }
        return userList;
    }

    /**
     * 获取在线用户数
     * 利用有效的token去获取用户信息，然后将用户id去重，获取一个大概的数值（有些情况下用户不是点击退出登录退的，所以会存在一定的误差）
     *
     * @return java.lang.Integer
     * @date 2024/3/4 18:48
     */
    @Override
    public Integer getOnlineUsers() {
        String tokenKeyPrefix = CacheConstants.LOGIN_TOKEN_KEY + "*";
        Collection<String> keySet = redisService.keys(tokenKeyPrefix);
        if (CollectionUtils.isEmpty(keySet)) {
            return 0;
        }
        Set<String> hashSet = new HashSet<>(keySet.size());
        for (String k : keySet) {
            String value = redisService.getStr(k);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(value)) {
                hashSet.add(value);
            }
        }
        SysConfig config = configService.getByConfigKey(USER_ONLINE_PLUS_KEY);
        int size = hashSet.size();
        if (ObjectUtils.isNotEmpty(config)) {
            size = size + Integer.parseInt(config.getConfigValue());
        }
        return size;
    }

    @Override
    public List<Object> getListWithUser(String name) {
        ArrayList<Object> result = new ArrayList<>();
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getTenantId, SecurityUtils.getTenantId());
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        List<String> userIdList = userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userIdList)) {
            LambdaQueryWrapper<SysUser> userLqw = new LambdaQueryWrapper<>();
            userLqw.like(StringUtils.isNotEmpty(name), SysUser::getNickName, name);
            userLqw.eq(SysUser::getStatus, UserConstants.YSE_STATUS);
            userLqw.in(SysUser::getUserId, userIdList);
            List<SysUser> userList = this.list(userLqw);
            if (CollUtil.isNotEmpty(userList)) {
                //List<Long> userResultIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
                //Map<Long, String> orgPathByUserId = orgService.getOrgPathByUserId(userResultIdList);
                for (SysUser user : userList) {
                    SysUserResp userResp = new SysUserResp();
                    BeanUtils.copyProperties(user, userResp);
                    userResp.setDataType(SystemDataTypeEnum.USER.getCode());
                    result.add(userResp);
                }
            }
        }
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setTenantIds(CollUtil.newArrayList(SecurityUtils.getTenantId()));
        dto.setLikeOrgName(name);
        dto.setDelFlag(UserConstants.YSE_STATUS);
        dto.setStatus(UserConstants.YSE_STATUS);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        for (SysOrg org : orgList) {
            SysOrgResp orgResp = new SysOrgResp();
            BeanUtils.copyProperties(org, orgResp);
            orgResp.setDataType(SystemDataTypeEnum.ORG.getCode());
            result.add(orgResp);
        }
        return result;
    }

    @Override
    public List<String> getByPmsType(Integer code, String userId) {
        log.info("userService开始执行getByPmsType方法，req:code:{},userId:{}", code, userId);
        List<String> userIdList = new ArrayList<>();
        List<String> orgIdList = new ArrayList<>();
        PmsTypeEnum pmsTypeEnum = PmsTypeEnum.getEnum(code);
        switch (Objects.requireNonNull(pmsTypeEnum)) {
            //本人创建数据权限
            case PMS_TYPE_1:
                userIdList.add(userId);
                return userIdList;
            //本部门数据权限
            case PMS_TYPE_2:
                orgIdList = getDepartmentId(userId);
                break;
            //本部门及下级部门数据权限
            case PMS_TYPE_3:
                orgIdList = getSonDepartmentId(userId);
                break;
            //本单位数据权限
            case PMS_TYPE_4:
                orgIdList = getDeptId(userId);
                break;
            //本单位及下级单位数据权限（租户内）
            case PMS_TYPE_5:
                orgIdList = getSonDeptId(userId);
                break;
            //本租户数据权限
            case PMS_TYPE_6:
                userIdList.addAll(getTenantOrgId(userId));
                return userIdList;
            default:
                break;
        }
        if (CollUtil.isNotEmpty(orgIdList)) {
            LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
            userOrgLqw.in(SysUserOrg::getOrgId, orgIdList);
            userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
            List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
            userIdList = userOrgList.stream().map(SysUserOrg::getUserId).distinct().collect(Collectors.toList());
        }
        return userIdList;
    }

    @Override
    public Boolean checkSLC() {
        //查询用户授权策略
        SLCEntity slcEntity = slcService.querySLCEntity();
        if (Objects.isNull(slcEntity)) {
            log.warn("授权信息不存在");
            return true;
        }
        Long count = this.lambdaQuery().eq(SysUser::getStatus, StatusEnum.ENABLE.getCode() + "")
                .eq(SysUser::getDelFlag, DeleteEnum.NO_DELETE.getCode() + "").count();

        return !slcEntity.isAccountFlag() || slcEntity.getAccountNum() > count;
    }

    @Override
    public void checkSLCV2() {
        //查询用户授权策略
        SLCEntity slcEntity = slcService.querySLCEntity();
        if (Objects.isNull(slcEntity)) {
            return;
        }
        Long count = this.lambdaQuery().eq(SysUser::getStatus, StatusEnum.ENABLE.getCode() + "")
                .eq(SysUser::getDelFlag, DeleteEnum.NO_DELETE.getCode() + "").count();

        if (slcEntity.isAccountFlag()) {
            if (slcEntity.getAccountNum() <= count) {
                throw new ServiceException("当前可启用" + slcEntity.getAccountNum() + "个账号,您的选择已超出上限！");
            }
        }
        //todo V2暂不实现
//        if (slcEntity.getVersion().equals("v1")) {
//            SLCEntityV2 v2 = (SLCEntityV2) slcEntity;
//        }
    }

    /**
     * type2:本部门数据权限:获取所属部门id
     */
    private List<String> getDepartmentId(String userId) {
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getUserId, userId);
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return new ArrayList<>();
        }
        return userOrgList.stream().map(SysUserOrg::getOrgId).collect(Collectors.toList());
    }

    /**
     * type3:本部门及下级部门数据权限、:获取所属部门及下级部门id
     */
    private List<String> getSonDepartmentId(String userId) {
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getUserId, userId);
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return new ArrayList<>();
        }
        List<String> orgIdList = userOrgList.stream().map(SysUserOrg::getOrgId).collect(Collectors.toList());
        orgIdList.addAll(getSonOrgIdList(orgIdList, Boolean.TRUE, null));
        return orgIdList;
    }

    /**
     * type4:本单位数据权限、:获取所属单位及该单位下所有部门id
     */
    private List<String> getDeptId(String userId) {
        List<String> deptOrgIdList = getUserDeptIdList(userId);
        if (CollUtil.isEmpty(deptOrgIdList)) {
            return new ArrayList<>();
        }
        deptOrgIdList.addAll(getSonOrgIdList(deptOrgIdList, Boolean.TRUE, null));
        return deptOrgIdList;
    }

    /**
     * type5:本单位及下级单位数据权限（租户内）、:获取同租户下所属单位下所有单位及部门
     */
    private List<String> getSonDeptId(String userId) {
        List<String> deptOrgIdList = getUserDeptIdList(userId);
        if (CollUtil.isEmpty(deptOrgIdList)) {
            return new ArrayList<>();
        }
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgIds(deptOrgIdList);
        List<SysOrg> deptList = orgService.listSimpleOrgByDto(dto);
        for (SysOrg sysOrg : deptList) {
            List<String> deptIdList = new ArrayList<>();
            deptIdList.add(sysOrg.getOrgId());
            List<String> tenantIdList = new ArrayList<>();
            tenantIdList.add(sysOrg.getTenantId());
            deptOrgIdList.addAll(getSonOrgIdList(deptIdList, Boolean.FALSE, tenantIdList));
        }
        return deptOrgIdList;
    }

    /**
     * type6:本租户数据权限、:获取同租户下所属单位下所有单位及部门
     */
    private List<String> getTenantOrgId(String userId) {
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getUserId, userId);
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return new ArrayList<>();
        }
        List<String> tenantIdList = userOrgList.stream().map(SysUserOrg::getTenantId).distinct()
                .collect(Collectors.toList());
//        userOrgLqw.clear();
//        userOrgLqw.in(SysUserOrg::getTenantId, tenantIdList);
//        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
//        userOrgList = userOrgService.list(userOrgLqw);
//        return userOrgList.stream().map(SysUserOrg::getUserId).collect(Collectors.toList());

//        userOrgLqw.eq(SysUserOrg::getUserId, userId);
//        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
//        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        List<String> userIdList = userOrgMapper.selectUserIdByTenant(tenantIdList);
        return userIdList;
    }

    /**
     * 查询部门下的所有部门id
     */
    private List<String> getSonOrgIdList(List<String> orgIdList, Boolean isDepartment, List<String> tenantIdList) {
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setStatus(UserConstants.YSE_STATUS);
        if (isDepartment) {
            dto.setOrgType(OrgTypeEnum.DEPARTMENT.getCode());
        }
        dto.setParentIds(orgIdList);
        if (CollUtil.isNotEmpty(tenantIdList)) {
            dto.setTenantIds(tenantIdList);
        }
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        if (CollUtil.isEmpty(orgList)) {
            return new ArrayList<>();
        }
        List<String> departmentOrgIdList = orgList.stream().map(SysOrg::getOrgId).collect(Collectors.toList());
        tenantIdList = orgList.stream().map(SysOrg::getTenantId).collect(Collectors.toList());
        List<String> sonOrgIdList = getSonOrgIdList(departmentOrgIdList, isDepartment, tenantIdList);
        for (String orgId : sonOrgIdList) {
            if (!CollUtil.contains(departmentOrgIdList, orgId)) {
                departmentOrgIdList.add(orgId);
            }
        }
        return departmentOrgIdList;
    }


    /**
     * 根据用户id查询用户所属单位id集合
     *
     * @param userId
     * @return
     */
    private List<String> getUserDeptIdList(String userId) {
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(SysUserOrg::getUserId, userId);
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        userOrgLqw.ne(SysUserOrg::getTenantId, "0");
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return new ArrayList<>();
        }
        List<String> orgIdList = userOrgList.stream().map(SysUserOrg::getOrgId).collect(Collectors.toList());
        SimpleOrgFindDto dto = new SimpleOrgFindDto();
        dto.setOrgIds(orgIdList);
        List<SysOrg> orgList = orgService.listSimpleOrgByDto(dto);
        List<String> allParentOrgIdList = new ArrayList<>();

        //获取所有上级机构的id
        orgList.forEach(o -> {
            for (String parentOrgId : o.getAncestors().split(",")) {
                if (!CollUtil.contains(allParentOrgIdList, Long.parseLong(parentOrgId))) {
                    allParentOrgIdList.add(parentOrgId);
                }
            }
        });
        SimpleOrgFindDto findDto = new SimpleOrgFindDto();
        findDto.setOrgIds(allParentOrgIdList);
        List<SysOrg> allParentOrgList = orgService.listSimpleOrgByDto(findDto);
        Map<String, SysOrg> orgMap = allParentOrgList.stream().collect(Collectors.toMap(SysOrg::getOrgId, o -> o));
        List<String> deptOrgIdList = new ArrayList<>();
        for (SysOrg sysOrg : orgList) {
            if (sysOrg.getOrgType().equals(OrgTypeEnum.DEPT.getCode())) {
                deptOrgIdList.add(sysOrg.getOrgId());
                continue;
            }

            String[] ancestors = sysOrg.getAncestors().split(",");
            for (int i = ancestors.length - 1; i >= 0; i--) {
                if (orgMap.containsKey(ancestors[i])) {
                    SysOrg parentOrg = orgMap.get(ancestors[i]);
                    if (parentOrg.getOrgType().equals(OrgTypeEnum.DEPT.getCode()) && !deptOrgIdList.contains(
                            parentOrg.getOrgId())) {
                        deptOrgIdList.add(parentOrg.getOrgId());
                    }
                }
            }
        }
        return deptOrgIdList;
    }

    @Override
    public Boolean isConfigAdmin(String userId) {
        if (ksoAuthConfig.isEnable()) {
            return false;
        }
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        userRoleLqw.eq(SysUserRole::getUserId, userId);
        userRoleLqw.eq(SysUserRole::getStatus, DeleteEnum.NO_DELETE.getCode());
        List<SysUserRole> userRoleList = userRoleMapper.selectList(userRoleLqw);
        List<String> roleIdList = userRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        if (CollUtil.isEmpty(roleIdList)) {
            return Boolean.FALSE;
        }
        // TODO listByIds 角色已改造-配置管理员
        List<SysRole> roleList = roleService.listByIds(roleIdList);
        List<Integer> roleTypeList = roleList.stream().map(SysRole::getRoleType).collect(Collectors.toList());
        return CollUtil.contains(roleTypeList, RoleTypeEnum.CONFIG_ADMIN.getCode());
    }

    @Override
    public Boolean isSysAdmin() {
        SysConfig config = configService.getByConfigKey(ROLE_INIT_TYPE);
        final List<Integer> defaultRoleTypeList = JSON.parseArray(config.getConfigValue(), Integer.class);
        List<SysUserRole> userRoleList = userRoleService.lambdaQuery()
                .eq(SysUserRole::getUserId, SecurityUtils.getUserId())
                .eq(SysUserRole::getStatus, DeleteEnum.NO_DELETE.getCode())
                .eq(SysUserRole::getTenantId, SecurityUtils.getTenantId())
                .list();
        if (CollUtil.isEmpty(userRoleList)) {
            return Boolean.FALSE;
        }
        List<String> roleIdList = userRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        RoleFindDto dto = new RoleFindDto();
        dto.setRoleIdList(roleIdList);
        dto.setRoleDelFlag(UserConstants.YSE_STATUS);
        dto.setRoleStatus(UserConstants.YSE_STATUS);
        dto.setInitRoleTypeList(RoleTypeEnum.getDefaultRoleType());
        dto.setConfigRoleTypeList(defaultRoleTypeList);
        // TODO listByRoleDto 角色改造（前端屏蔽接口调用 /system/v1/user/isSysAdmin）
        List<SysRole> roleList = roleService.listByRoleDto(dto);
        List<Integer> roleTypeList = roleList.stream().map(SysRole::getRoleType).distinct()
                .collect(Collectors.toList());
        return CollUtil.contains(roleTypeList, RoleTypeEnum.SYSTEM_ADMIN.getCode()) || CollUtil.contains(roleTypeList,
                RoleTypeEnum.SUPER_ADMIN.getCode());
    }

    private void checkUserWps(int expectCount) {
        SLCEntity slcEntity = slcService.querySLCEntity();
        if (Objects.isNull(slcEntity)) {
            log.warn("未配置用户授权策略");
            return;
        }
        Long count = this.lambdaQuery().eq(SysUser::getStatus, StatusEnum.ENABLE.getCode() + "")
                .eq(SysUser::getDelFlag, DeleteEnum.NO_DELETE.getCode() + "")
                .eq(SysUser::getWpsAuth, UserConstants.WPS_AUTH)
                .count();
        if (slcEntity.isWpsAccountFlag() && slcEntity.getWpsAccountNum() < count + expectCount) {
            throw new ServiceException("wps端用户数量已达上限");
        }
    }

    @Override
    public Boolean containConfigAdmin(List<String> userIdList) {
        LambdaQueryWrapper<SysUserRole> userRoleLqw = new LambdaQueryWrapper<>();
        userRoleLqw.in(SysUserRole::getUserId, userIdList);
        userRoleLqw.eq(SysUserRole::getStatus, SysMessageEnum.DeleteEnum.NO_DELETE.getCode());
        List<SysRole> configRoleList = roleService.listByRoleType(
                CollUtil.newArrayList(RoleTypeEnum.CONFIG_ADMIN.getCode()));
        if (CollUtil.isEmpty(configRoleList)) {
            return Boolean.FALSE;
        }
        List<String> configRoleIdList = configRoleList.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        userRoleLqw.in(SysUserRole::getRoleId, configRoleIdList);
        List<SysUserRole> userRoleList = userRoleMapper.selectList(userRoleLqw);
        return CollUtil.isNotEmpty(userRoleList);
    }

    @Override
    public boolean checkUserOrg(List<String> orgIdList) {
        OrgStatusFindDto dto = new OrgStatusFindDto();
        dto.setDelFlag(UserConstants.YSE_STATUS);
        dto.setOrgIds(orgIdList);
        long orgCount = orgService.countByStatusDto(dto);
        return orgIdList.size() == orgCount;
    }

    @Override
    public List<SysUser> listUserByOrgIds(List<String> orgIds, List<Integer> classifieds) {
        return this.getBaseMapper().listUserByOrgIds(orgIds, classifieds);
    }

    private void buildUserCondition(SysUser user) {
        if (!SecurityUtils.isAdmin()) {
            String tenantId = SecurityUtils.getTenantId();
            user.setTenantId(tenantId);
        }
        if (ObjectUtils.isNotEmpty(user.getParams()) && user.getParams().containsKey("beginTime") && user.getParams()
                .containsKey("endTime")) {
            LocalDateTime beginTime = LocalDateTimeUtil.of(DateUtils.parseDate(user.getParams().get("beginTime")));
            LocalDateTime endTime = LocalDateTimeUtil.of(DateUtils.parseDate(user.getParams().get("endTime")));

            user.setBeginTime(LocalDateTimeUtil.beginOfDay(beginTime));
            user.setEndTime(LocalDateTimeUtil.endOfDay(endTime));
        }
    }

    private void buildUserInfo(List<SysUser> userList) {
        List<String> userIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
        Map<String, String> idOrgPathMap = orgService.getOrgPathByUserId(userIdList);
        Map<String, List<SysRole>> userRoleMap = roleService.selectRolesByUserIdList(userIdList);
        userList.forEach(u -> {
            if (CollUtil.isNotEmpty(userRoleMap) && userRoleMap.containsKey(u.getUserId())) {
                String roleNameList = userRoleMap.get(u.getUserId()).stream().map(SysRole::getRoleName)
                        .collect(Collectors.joining(StringPool.COMMA));
                u.setRoleNameList(roleNameList);
            }
            if (CollUtil.isNotEmpty(idOrgPathMap) && idOrgPathMap.containsKey(u.getUserId())) {
                u.setOrgPaths(idOrgPathMap.get(u.getUserId()));
            }

            // 国安部安全管理审批开启时，新增用户默认禁用状态
            if (approveTurnConfig.getSafeGab()) {
                u.setApproveStatus(operateApproveService.getStatus(u.getUserId()));
            }
        });
    }


    @Override
    public String selectUserInfoOrg(String userId) {
        SysUser sysUser = userMapper.selectById(userId);
        log.info("从数据库查询到userId:{}的数据:{}", userId, JSON.toJSONString(sysUser));
        if (ObjectUtils.isEmpty(sysUser)) {
            throw new ServiceException("未查询到用户");
        }
        List<String> userIdList = new ArrayList<>();
        userIdList.add(sysUser.getUserId());
        Map<String, String> idOrgPathMap = orgService.getOrgPathByUserId(userIdList);
        return idOrgPathMap.get(sysUser.getUserId());
    }

    @Override
    public Map<String, Object> getUserInfo() {
        SysUser user = selectUserById(SecurityUtils.getUserId());
        // 角色集合
        Set<String> roles = sysPermissionService.getRolePermission(user);
        log.info("roles {}", roles);
        //是否三员
        if (CollUtil.isNotEmpty(user.getRoles())) {
            user.setRoles(filterRole(user.getRoles()));
        }        // 权限集合
        Set<String> permissions = sysPermissionService.getMenuPermission(user);
        HashMap<String, Object> ajax = new HashMap<>(Constants.INITIAL_CAPACITY);
        List<SysPost> postList = new ArrayList<>();
        if (user.getPostIds() != null && user.getPostIds().length != 0) {
            postList = postService.listByIds(Arrays.stream(user.getPostIds()).collect(Collectors.toList()));
        }
        String orgRootName = orgService.getRootName();
        ajax.put("posts", postList);
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        ajax.put("orgRootName", orgRootName);
        ajax.put("checkPasswordUpdateResp", checkUpdatePassword());
        return ajax;
    }

    private List<SysRole> filterRole(List<SysRole> roles) {
        List<Integer> defaultRoleType = RoleTypeEnum.getDefaultRoleType();
        List<Integer> roleTypes = JSON.parseArray(roleType, Integer.class);
        return roles.stream()
                .filter(role -> !CollUtil.contains(defaultRoleType, role.getRoleType()) || CollUtil.contains(roleTypes,
                        role.getRoleType())).collect(Collectors.toList());
    }


    @Override
    public Map<String, List<String>> getUserDeptIdV2(List<String> userIdList) {
        Map<String, List<String>> result = new HashMap<>();
        LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.in(SysUserOrg::getUserId, userIdList);
        userOrgLqw.eq(SysUserOrg::getStatus, StatusEnum.ENABLE.getCode());
        List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
        if (CollUtil.isEmpty(userOrgList)) {
            return result;
        }
        for (SysUserOrg userOrg : userOrgList) {
            List<String> userDeptIdList = new ArrayList<>();
            if (result.containsKey(userOrg.getUserId())) {
                userDeptIdList = result.get(userOrg.getUserId());
            }
            SysOrg org = orgService.simpleGetById(userOrg.getOrgId());
            if (ObjectUtils.isEmpty(org)) {
                continue;
            }
            //如果当前单位正好是单位，直接返回
            if (org.getOrgType().equals(OrgTypeEnum.DEPT.getCode())) {
                userDeptIdList.add(userOrg.getOrgId());
                result.put(userOrg.getUserId(), userDeptIdList);
                continue;
            }
            String[] parentIdStrList = org.getAncestors().split(",");
            List<String> parentIdList = Arrays.stream(parentIdStrList).collect(
                    Collectors.toList());
            SimpleOrgFindDto dto = new SimpleOrgFindDto();
            dto.setOrgIds(parentIdList);
            List<SysOrg> parentOrgList = orgService.listSimpleOrgByDto(dto);
            Map<String, SysOrg> parentOrgMap = parentOrgList.stream()
                    .collect(Collectors.toMap(SysOrg::getOrgId, o -> o));
            for (int i = parentIdStrList.length - 1; i >= 0; i--) {
                String parentId = parentIdStrList[i];
                if (parentOrgMap.containsKey(parentId)) {
                    SysOrg parentOrg = parentOrgMap.get(parentId);
                    if (parentOrg.getOrgType().equals(OrgTypeEnum.DEPT.getCode())) {
                        userDeptIdList.add(userOrg.getOrgId());
                        result.put(userOrg.getUserId(), userDeptIdList);
                        break;
                    }
                }
            }
        }
        return result;
    }
}
