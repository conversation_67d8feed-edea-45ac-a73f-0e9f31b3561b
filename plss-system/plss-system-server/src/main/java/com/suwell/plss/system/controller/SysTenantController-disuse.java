//package com.suwell.plss.system.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.suwell.plss.framework.common.constant.UserConstants;
//import com.suwell.plss.framework.common.domain.R;
//import com.suwell.plss.framework.common.utils.PageUtils;
//import com.suwell.plss.framework.log.annotation.Log;
//import com.suwell.plss.framework.log.enums.BusinessType;
//import com.suwell.plss.framework.security.utils.SecurityUtils;
//import com.suwell.plss.system.api.entity.SysOrg;
//import com.suwell.plss.system.dto.request.SysTenantListReq;
//import com.suwell.plss.system.entity.SysTenant;
//import com.suwell.plss.system.entity.SysTenantManager;
//import com.suwell.plss.system.enums.SysOrgEnum.OrgTypeEnum;
//import com.suwell.plss.system.enums.SysTenantEnum.DeleteEnum;
//import com.suwell.plss.system.service.SysOrgService;
//import com.suwell.plss.system.service.SysTenantManagerService;
//import com.suwell.plss.system.service.SysTenantService;
//import java.util.List;
//import java.util.function.Predicate;
//import java.util.stream.Collectors;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * @Author:yuehai。gao
// * @Date: 2023/9/16
// */
//@RestController
//@RequestMapping("/v1/sysTenant")
//public class SysTenantController {
//
//    @Autowired
//    private SysTenantService sysTenantService;
//
//    @Autowired
//    private SysTenantManagerService sysTenantManagerService;
//
//    @Autowired
//    private SysOrgService sysOrgService;
//
//    /***
//     * 获取当前租户下的组织单位树；排除掉已关联租户的单位
//     */
//    @PostMapping("/getOrgListByLazy/{orgId}")
//    public R<SysOrg> getOrgListByLazy(@PathVariable(value = "orgId", required = false) String orgId) {
//        SysOrg sysOrg = sysOrgService.getLazyListNoTeanant(orgId);
//
//
//        List<SysOrg> children = sysOrg.getChildren();
//        //只保留单位类型的机构。且根据组织查询租户，如果存在关联关系，排除掉
//        List<SysOrg> collect = children.stream().filter(o -> OrgTypeEnum.DEPT.getCode().equals(o.getOrgType()))
//                .filter(new Predicate<SysOrg>() {
//                    @Override
//                    public boolean test(SysOrg sysOrg) {
//                        if (orgId != 0) {
//                            LambdaQueryWrapper<SysTenant> sysTenantLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                            sysTenantLambdaQueryWrapper.eq(SysTenant::getLinkId, sysOrg.getOrgId());
//                            sysTenantLambdaQueryWrapper.eq(SysTenant::getDelFlag, DeleteEnum.NO_DELETE.getCode());
//                            long count = sysTenantService.count(sysTenantLambdaQueryWrapper);
//                            return !(count > 0);
//                        } else {
//                            return true;
//                        }
//
//                    }
//                })
//                .collect(Collectors.toList());
//
//         //由于排除了部门，所以leaf字段要重新设置
//        LambdaQueryWrapper<SysOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        List<Long> orgIdList = collect.stream().map(SysOrg::getOrgId).collect(Collectors.toList());
//        if(orgIdList != null && orgIdList.size() > 0){
//            orgLambdaQueryWrapper.in(SysOrg::getParentId, orgIdList);
//            if(!SecurityUtils.isAdmin()){
//                String tenantId = SecurityUtils.getTenantId();
//                orgLambdaQueryWrapper.eq(SysOrg::getTenantId, tenantId);
//            }
//            orgLambdaQueryWrapper.eq(SysOrg::getStatus, UserConstants.YSE_STATUS);
//            orgLambdaQueryWrapper.eq(SysOrg::getOrgType,OrgTypeEnum.DEPT.getCode());
//            List<SysOrg> orgList = sysOrgService.list(orgLambdaQueryWrapper);
//            List<SysOrg> coll = null;
//            if(!SecurityUtils.isAdmin()){
//                coll = orgList.stream().filter(new Predicate<SysOrg>() {
//                    @Override
//                    public boolean test(SysOrg sysOrg) {
//
//                        LambdaQueryWrapper<SysTenant> sysTenantLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                        sysTenantLambdaQueryWrapper.eq(SysTenant::getLinkId, sysOrg.getOrgId());
//                        sysTenantLambdaQueryWrapper.eq(SysTenant::getDelFlag, DeleteEnum.NO_DELETE.getCode());
//                        long count = sysTenantService.count(sysTenantLambdaQueryWrapper);
//                        return !(count > 0);
//
//
//                    }
//                }).collect(Collectors.toList());
//            }else{
//                coll = orgList;
//            }
//
//
//            List<Long> parentIdList = coll.stream().map(SysOrg::getParentId).collect(Collectors.toList());
//            collect.stream().forEach(o -> {
//                o.setLeaf(parentIdList.contains(o.getOrgId()) ? "1" : "2");
//            });
//        }
//
//        sysOrg.setChildren(collect);
//
//        return R.ok(sysOrg);
//    }
//
//
//    /**
//     * 获取租户详情
//     */
//    @PostMapping("/info")
//    public R<SysTenant> getTenantInfo(@RequestParam String tenantId) {
//        SysTenant info = sysTenantService.getInfo(tenantId);
//        return R.ok(info);
//    }
//
//
//    /**
//     * 获取某管理员所能管得租户列表
//     */
//    @PostMapping("/getListByUserId")
//    public R<List<SysTenant>> getListByUserId(@RequestParam String userId) {
//        List<SysTenant> sysTenantByUserId = sysTenantService.getSysTenantByUserId(userId);
//        return R.ok(sysTenantByUserId);
//    }
//
//
//    /**
//     * 租户列表
//     */
//    @PostMapping("/list")
//    public R<PageUtils<SysTenant>> getList(@RequestBody SysTenantListReq sysTenantListReq) {
//        PageUtils<SysTenant> page = sysTenantService.sysTenantList(sysTenantListReq);
//        return R.ok(page);
//    }
//
//
//    /**
//     * 新增租户
//     */
//    @Log(title = "单位管理",businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    public R addSystenant(@RequestBody SysTenant sysTenant) {
//
//        sysTenantService.insertSysTenant(sysTenant);
//        return R.ok();
//    }
//
//    /**
//     * 更新租户
//     */
//    @Log(title = "单位管理",businessType = BusinessType.UPDATE)
//    @PostMapping("/updateInfo")
//    public R updateSystenant(@RequestBody SysTenant sysTenant) {
//        sysTenantService.updateSysTenant(sysTenant);
//        return R.ok();
//    }
//
//
//    /**
//     * 禁用/启用租户
//     */
//    @PostMapping("/updateStatus")
//    public R updateStatus(@RequestParam Long id, @RequestParam Integer status) {
//        sysTenantService.updateStatus(id, status);
//        return R.ok();
//    }
//
//    /**
//     * 删除租户：逻辑删除
//     */
//    @Log(title = "单位管理",businessType = BusinessType.DELETE)
//    @PostMapping("/delete")
//    public R delete(@RequestParam Long id) {
//        //todo 待租户功能设计完善后放开
////        sysTenantService.deleteById(id);
////        return R.ok();
//        return R.error("租户不允许删除");
//    }
//
//    /**
//     * 新增租户-管理者关系信息
//     */
//    @PostMapping("/addSysTenantManager")
//    public R addSysTenantManager(@RequestBody SysTenantManager sysTenantManager) {
//        sysTenantManagerService.save(sysTenantManager);
//        return R.ok();
//    }
//
//}
