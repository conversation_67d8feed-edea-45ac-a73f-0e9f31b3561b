package com.suwell.plss.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Maps;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.domain.AccessTrendDTO;
import com.suwell.plss.framework.common.domain.AccessTrendDateDTO;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.common.utils.NumberUtils;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.standard.dto.request.AccessTrendReq;
import com.suwell.plss.record.standard.dto.response.AccessTrendResp;
import com.suwell.plss.system.api.domain.request.UserStatusFindDto;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.dto.OptTrendDTO;
import com.suwell.plss.system.entity.SysUserOrg;
import com.suwell.plss.system.enums.AccessTrendEnum;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.enums.SysTenantEnum;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.service.SearchHistoryService;
import com.suwell.plss.system.service.SuperviseUserAnalysisService;
import com.suwell.plss.system.service.SysLogininforService;
import com.suwell.plss.system.service.SysOperLogService;
import com.suwell.plss.system.enums.SysTenantEnum;
import com.suwell.plss.system.mapper.SysUserOrgMapper;
import com.suwell.plss.system.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.suwell.plss.system.mapper.SysUserOrgMapper;
import com.suwell.plss.system.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.suwell.plss.framework.log.enums.BusinessType.*;
import static com.suwell.plss.system.api.enums.SystemBizError.ACCESS_TREND_TYPE;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
@Slf4j
@Service
public class SuperviseUserAnalysisServiceImpl implements SuperviseUserAnalysisService {

    // 用户访问量缓存key
    public static final String SYSTEM_SUPERVISE_ANALYSIS_USER = "SYSTEM_SUPERVISE_ANALYSIS:ACCESS_TREND_USER:";
    //阅读量  下载量 分享量 缓存key
    public static final String SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_OPT = "SYSTEM_SUPERVISE_ANALYSIS:ACCESS_TREND_OPT:";
    // 检索量 缓存key
    public static final String SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_SEARCH = "SYSTEM_SUPERVISE_ANALYSIS:ACCESS_TREND_SEARCH:";
    public static final int TIME = 7;
    public static final TimeUnit TIMEUNIT = TimeUnit.HOURS;
    @Resource
    private IUserService sysUserService;
    @Resource
    private SearchHistoryService searchHistoryService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private SysLogininforService sysLogininforService;
    @Resource
    private RedisService redisService;
    @Resource
    private SysUserOrgService userOrgService;

    @Resource
    private SysUserOrgMapper userOrgMapper;
    @Resource
    private KsoAuthConfig ksoAuthConfig;
//    @Value("${supervise.user.analysis.limit:false}")
//    private Boolean toDay;
//      配合测试写的测试代码
//    /**
//     * 获取访问趋势时间
//     *
//     * @return
//     */
//    public AccessTrendDateDTO getAccessTrendDateDTOOld() {
//        // 获取当前日期
//        Calendar today = Calendar.getInstance();
//        Date todayTime = today.getTime();
//
//        AccessTrendDateDTO accessTrendDateDTO = new AccessTrendDateDTO();
//        DateTime todayBegin = getTodayBegin();
//        accessTrendDateDTO.setTodayBegin(todayBegin.getTime());
//        accessTrendDateDTO.setThirtyDaysAgo(DateUtil.offsetDay(todayBegin, -30).getTime());
//        accessTrendDateDTO.setSixtyDaysAgo(DateUtil.offsetDay(todayBegin, -60).getTime());
//
//        // 获取去年的今天
//        today.add(Calendar.YEAR, -1);
//        DateTime lastYearToday = DateUtil.beginOfDay(today.getTime());
//        accessTrendDateDTO.setLastYearToday(lastYearToday.getTime());
//        accessTrendDateDTO.setLastYearTodayThirtyDaysAgo(DateUtil.offsetDay(lastYearToday, -30).getTime());
//        return accessTrendDateDTO;
//    }
//
//    private DateTime getTodayBegin() {
//        log.info("是否查询当天数据 true当天凌晨，false明天凌晨:{}", toDay);
//        if (!toDay) {
//            return DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1));
//        }
//        return DateUtil.beginOfDay(new Date());
//    }

    @Override
    public Long userCount() {
        // TODO 角色改造
        String userId = SecurityUtils.getUserId();
        if(SecurityUtils.isAdmin() || ksoAuthConfig.isEnable()){
            UserStatusFindDto dto = new UserStatusFindDto();
            dto.setDelFlag(UserConstants.YSE_STATUS);
            return sysUserService.countByStatusDto(dto);
        }
        log.info("当前人用户id:{}", userId);
//        List<Long> userIds = sysUserService.getByPmsType(PMS_TYPE_6.getCode(), userId);
        Long count = userOrgMapper.selectTenantUserIdCount(userId);
        log.info("当前人所在租户下的人列表:{}", count);
        return count;
    }

    @Override
    public AccessTrendResp accessTrend(AccessTrendReq req) {
        log.info("统计趋势req:{}", JSON.toJSONString(req));
        AccessTrendEnum accessType = AccessTrendEnum.getEnum(req.getAccessType());
        AssertUtils.isFalse(Objects.isNull(accessType), ACCESS_TREND_TYPE);

//        AccessTrendDateDTO accessTrendDateDTO = !toDay ? getAccessTrendDateDTOOld() : DateUtils.getAccessTrendDateDTO();
        AccessTrendDateDTO accessTrendDateDTO = DateUtils.getAccessTrendDateDTO();
        log.info("统计趋势时间条件:{}", JSON.toJSONString(accessTrendDateDTO));
        AccessTrendResp accessTrendResp = null;
        switch (accessType) {
            case ACCESS_TREND_USER:
                log.info("ACCESS_TREND_USER accessType:{}", accessType);
                accessTrendResp = getUserVisitStatisticCache(accessTrendDateDTO, accessType);
                break;
            case ACCESS_TREND_READ:
                log.info("ACCESS_TREND_READ accessType:{}", accessType);
                accessTrendResp = getAccessTrendCache(accessTrendDateDTO, VIEW_DOC.getCode(), accessType);
                break;
            case ACCESS_TREND_SEARCH:
                log.info("ACCESS_TREND_SEARCH accessType:{}", accessType);
                accessTrendResp = getAccessTrendSearchCache(accessTrendDateDTO, accessType);
                break;
            case ACCESS_TREND_DOWNLOAD:
                log.info("ACCESS_TREND_DOWNLOAD accessType:{}", accessType);
                accessTrendResp = getAccessTrendCache(accessTrendDateDTO, DOCUMENT_DOWNLOAD.getCode(), accessType);
                break;
            case ACCESS_TREND_SHARE:
                log.info("ACCESS_TREND_SHARE accessType:{}", accessType);
                accessTrendResp = getAccessTrendCache(accessTrendDateDTO, DOCUMENT_SHARE.getCode(), accessType);
                break;
            default:
                break;
        }
        return accessTrendResp;
    }

    private AccessTrendResp getUserVisitStatisticCache(AccessTrendDateDTO req, AccessTrendEnum accessType) {
        String userId = SecurityUtils.getUserId();
        log.info("用户:{},走缓存的查询方式:{},趋势类型:{}", userId, JSON.toJSONString(req), accessType);
        AccessTrendResp accessTrendResp = redisService.get(SYSTEM_SUPERVISE_ANALYSIS_USER + userId
                + StringPool.UNDERSCORE + req.getTodayBegin()
                + StringPool.UNDERSCORE + accessType.getCode(), AccessTrendResp.class);
        log.info("监管分析用户访问量redis查询:{}", JSON.toJSONString(accessTrendResp));
        if (Objects.isNull(accessTrendResp)) {
            accessTrendResp = getUserVisitStatistic(req, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析用户访问量db查询:{}", JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_USER + userId
                        + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        } else {
//            log.info("数据权限命中缓存,续期2小时:{}", JSON.toJSONString(optDataUserPmsResp));
//            redisService.setExpire(USER_OPT_DATA_PMS_SCOPE + req.getUserId() + StringPool.UNDERSCORE
//                            + req.getModuleType() + StringPool.UNDERSCORE + req.getOperateType(),
//                    2, TimeUnit.HOURS);
        }

        // 校验缓存时间错误情况,redis挂断场景
        if (!accessTrendResp.getTodayBegin().equals(req.getTodayBegin())) {
            log.info("兜底策略,监管分析用户访问量redis查询时间不一致,重新查询:{}", JSON.toJSONString(accessTrendResp));
            accessTrendResp = getUserVisitStatistic(req, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析用户访问量db查询:{}", JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_USER + userId
                        + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        }
        return accessTrendResp;
    }

    private AccessTrendResp getUserVisitStatistic(AccessTrendDateDTO req, String userId) {
        // TODO id类型修改
        AccessTrendResp accessTrendResp = new AccessTrendResp();
        log.info("当前人用户id:{}", userId);
//        List<Long> userIds = sysUserService.getByPmsType(PMS_TYPE_6.getCode(), userId);
        List<String> tenantIdList = Optional.of(ksoAuthConfig.isEnable()).map(flag->{
            if(flag){
                return null;
            }else{
                LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
                userOrgLqw.eq(SysUserOrg::getUserId, userId);
                userOrgLqw.eq(SysUserOrg::getStatus, SysTenantEnum.StatusEnum.ENABLE.getCode());
                List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
                return userOrgList.stream().map(SysUserOrg::getTenantId).distinct().collect(Collectors.toList());
            }
        }).orElseGet(ArrayList::new);

        if(!ksoAuthConfig.isEnable() && CollUtil.isEmpty(tenantIdList)){
            accessTrendResp.setTodayBegin(0L);
            accessTrendResp.setSixTotalCount(0L);
            accessTrendResp.setChainRate(0D);
            accessTrendResp.setTotalCount(0L);
            accessTrendResp.setAlikeRate(0D);
            accessTrendResp.setYearTotalCount(0L);
            return accessTrendResp;
        }

        String tenantId = SecurityUtils.getTenantId();
        log.info("当前登录的用户id：{},统计时间范围条件：{}", userId, JSON.toJSONString(req));
        List<OptTrendDTO> statisticDataList = ksoAuthConfig.isEnable() ? sysLogininforService.queryUserVisitStatisticByTenantId(
                req.getThirtyDaysAgo(), req.getTodayBegin(), tenantId) : sysLogininforService.queryUserVisitStatisticByTenantIds(
                req.getThirtyDaysAgo(), req.getTodayBegin(), tenantIdList);

        log.info("当前登录的用户id：{}，近三十天统计数据：{}", userId, JSON.toJSONString(statisticDataList));
        List<OptTrendDTO> sixStatisticDataList = ksoAuthConfig.isEnable() ? sysLogininforService.queryUserVisitStatisticByTenantId(
                req.getSixtyDaysAgo(), req.getThirtyDaysAgo(), tenantId) : sysLogininforService.queryUserVisitStatisticByTenantIds(
                req.getSixtyDaysAgo(), req.getThirtyDaysAgo(), tenantIdList) ;
        log.info("当前登录的用户id：{}，上一个三十天的统计数据：{}", userId, JSON.toJSONString(sixStatisticDataList));
//        // 同比总数 产品沟通 暂不做
//        List<LogininforTrendDTO> yearStatisticDataList = sysLogininforService.queryUserVisitStatistic(
//                req.getLastYearTodayThirtyDaysAgo(), req.getLastYearToday(), Lists.newArrayList(userId));

        Long totalCount = 0L;
        if (CollUtil.isNotEmpty(statisticDataList)) {
            totalCount = statisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
        Long sixTotalCount = 0L;
        if (CollUtil.isNotEmpty(sixStatisticDataList)) {
            sixTotalCount = sixStatisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
//        Long yearTotalCount = 0L;
//        if (CollUtil.isNotEmpty(yearStatisticDataList)) {
//            yearTotalCount = yearStatisticDataList.stream().mapToLong(LogininforTrendDTO::getDayCount).sum();
//        }

        accessTrendResp.setTotalCount(totalCount);
        if (sixTotalCount != 0) {
            Long c = totalCount - sixTotalCount;
            accessTrendResp.setChainRate(NumberUtils.analyzePercent(new BigDecimal(c),
                    new BigDecimal(sixTotalCount)).doubleValue());
        } else {
            accessTrendResp.setChainRate(0.0D);
        }
//        if (yearTotalCount != 0) {
//            Long c = totalCount - yearTotalCount;
//            accessTrendResp.setAlikeRate(NumberUtil.div(c.doubleValue(), yearTotalCount.doubleValue()) * 100);
//        } else {
//            accessTrendResp.setAlikeRate(0.0D);
//        }

        // 处理趋势列表
        if (CollUtil.isNotEmpty(statisticDataList)) {
            // 转换并分组统计   获取每一天的开始时间 分组统计
            Map<Long, Long> groupedByDay = statisticDataList.stream()
                    .collect(Collectors.groupingBy(log -> DateUtil.beginOfDay(log.getDayDate()).getTime(),
                            Collectors.summingLong(OptTrendDTO::getDayCount)));
            accessTrendResp.setAccessTrendList(getAccessTrendList(req, groupedByDay));
        } else {
            accessTrendResp.setAccessTrendList(DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30));
        }
        List<AccessTrendDTO> accessTrendList = accessTrendResp.getAccessTrendList();
        accessTrendResp.setTodayBegin(accessTrendList.get(accessTrendList.size() - 1).getDay());
        accessTrendResp.setSixTotalCount(sixTotalCount);
//        accessTrendResp.setYearTotalCount();
        return accessTrendResp;
    }

    /**
     * 搜索量统计
     *
     * @param req
     * @return
     */
    private AccessTrendResp getAccessTrendSearchCache(AccessTrendDateDTO req, AccessTrendEnum accessType) {
        String userId = SecurityUtils.getUserId();
        log.info("用户{},走缓存的查询方式:{},趋势类型:{}", userId, JSON.toJSONString(req), accessType);
        AccessTrendResp accessTrendResp = redisService.get(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_SEARCH
                + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                + StringPool.UNDERSCORE + accessType.getCode(), AccessTrendResp.class);
        log.info("监管分析{},redis查询:{}", accessType.getDesc(), JSON.toJSONString(accessTrendResp));
        if (Objects.isNull(accessTrendResp)) {
            accessTrendResp = getAccessTrendSearch(req, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析{},db查询:{}", accessType.getDesc(), JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_SEARCH
                        + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        } else {
//            log.info("数据权限命中缓存,续期2小时:{}", JSON.toJSONString(optDataUserPmsResp));
//            redisService.setExpire(USER_OPT_DATA_PMS_SCOPE + req.getUserId() + StringPool.UNDERSCORE
//                            + req.getModuleType() + StringPool.UNDERSCORE + req.getOperateType(),
//                    2, TimeUnit.HOURS);
        }

        // 校验缓存时间错误情况,redis挂断场景
        if (!accessTrendResp.getTodayBegin().equals(req.getTodayBegin())) {
            log.info("兜底策略,监管分析{},redis查询时间不一致,重新查询:{}", accessType.getDesc(),
                    JSON.toJSONString(accessTrendResp));
            accessTrendResp = getAccessTrendSearch(req, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析{},db查询:{}", accessType.getDesc(), JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_SEARCH
                        + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        }
        return accessTrendResp;
    }

    /**
     * 搜索量统计
     *
     * @param req
     * @return
     */
    private AccessTrendResp getAccessTrendSearch(AccessTrendDateDTO req, String userId) {
        // TODO id类型修改
        log.info("当前人用户id:{}", userId);
//        List<Long> userIds = sysUserService.getByPmsType(PMS_TYPE_6.getCode(), userId);
//        log.info("当前人所在租户下的人列表:{}", userIds);
        List<String> tenantIdList = Optional.of(ksoAuthConfig.isEnable()).map(flag->{
            if(flag){
                return null;
            }else{
                LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
                userOrgLqw.eq(SysUserOrg::getUserId, userId);
                userOrgLqw.eq(SysUserOrg::getStatus, SysTenantEnum.StatusEnum.ENABLE.getCode());
                List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
                return userOrgList.stream().map(SysUserOrg::getTenantId).distinct().collect(Collectors.toList());
            }
        }).orElseGet(ArrayList::new);

        String tenantId = SecurityUtils.getTenantId();
        List<OptTrendDTO> statisticDataList = ksoAuthConfig.isEnable() ? searchHistoryService.querySearchHistoryAccessTrendByTenantId(
                new Date(req.getThirtyDaysAgo()), new Date(req.getTodayBegin()), tenantId) :searchHistoryService.querySearchHistoryAccessTrendByTenantId(
                new Date(req.getThirtyDaysAgo()), new Date(req.getTodayBegin()), tenantIdList);
        log.info("当前登录的用户id：{}，近三十天统计数据：{}", userId, JSON.toJSONString(statisticDataList));

        List<OptTrendDTO> sixStatisticDataList = ksoAuthConfig.isEnable() ? searchHistoryService.querySearchHistoryAccessTrendByTenantId(
                new Date(req.getSixtyDaysAgo()), new Date(req.getThirtyDaysAgo()), tenantId):searchHistoryService.querySearchHistoryAccessTrend(
                new Date(req.getSixtyDaysAgo()), new Date(req.getThirtyDaysAgo()), tenantIdList);
        log.info("当前登录的用户id：{}，上一个三十天的统计数据：{}", userId, JSON.toJSONString(sixStatisticDataList));
//        // 同比总数 产品沟通 暂不做
//        List<OptTrendDTO> yearStatisticDataList = searchHistoryService.querySearchHistoryAccessTrend(
//                new Date(req.getLastYearTodayThirtyDaysAgo()), new Date(req.getLastYearToday()), userIds);

        Long totalCount = 0L;
        if (CollUtil.isNotEmpty(statisticDataList)) {
//            totalCount = (long) statisticDataList.size();
            totalCount = statisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
        Long sixTotalCount = 0L;
        if (CollUtil.isNotEmpty(sixStatisticDataList)) {
//            sixTotalCount = (long) sixStatisticDataList.size();
            sixTotalCount = sixStatisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
//        Long yearTotalCount = 0L;
//        if (CollUtil.isNotEmpty(yearStatisticDataList)) {
//            yearTotalCount = (long) yearStatisticDataList.size();
//        }

        AccessTrendResp accessTrendResp = new AccessTrendResp();
        accessTrendResp.setTotalCount(totalCount);
        if (sixTotalCount != 0) {
            Long c = totalCount - sixTotalCount;
            accessTrendResp.setChainRate(NumberUtils.analyzePercent(new BigDecimal(c),
                    new BigDecimal(sixTotalCount)).doubleValue());
        } else {
            accessTrendResp.setChainRate(0.0D);
        }
//        if (yearTotalCount != 0) {
//            Long c = totalCount - yearTotalCount;
//            accessTrendResp.setAlikeRate(NumberUtil.div(c.doubleValue(), yearTotalCount.doubleValue()) * 100);
//        } else {
//            accessTrendResp.setAlikeRate(0.0D);
//        }

        // 处理趋势列表
        if (CollUtil.isNotEmpty(statisticDataList)) {
            // 转换并分组统计  获取每一天的开始时间 分组统计
            Map<Long, Long> groupedByDay = statisticDataList.stream()
                    .collect(Collectors.groupingBy(log -> DateUtil.beginOfDay(log.getDayDate()).getTime(),
                            Collectors.summingLong(OptTrendDTO::getDayCount)));
            accessTrendResp.setAccessTrendList(getAccessTrendList(req, groupedByDay));
        } else {
            accessTrendResp.setAccessTrendList(DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30));
        }
        List<AccessTrendDTO> accessTrendList = accessTrendResp.getAccessTrendList();
        accessTrendResp.setTodayBegin(accessTrendList.get(accessTrendList.size() - 1).getDay());
        accessTrendResp.setSixTotalCount(sixTotalCount);
//        accessTrendResp.setYearTotalCount();
        return accessTrendResp;
    }

    /**
     * 统计 分享，下载 阅读量
     *
     * @param req
     * @param businessType
     * @return
     */
    private AccessTrendResp getAccessTrendCache(AccessTrendDateDTO req, Integer businessType,
            AccessTrendEnum accessType) {
        String userId = SecurityUtils.getUserId();
        log.info("用户:{},走缓存的查询方式:{},businessType:{},趋势类型:{}", userId,
                JSON.toJSONString(req), businessType, accessType);
        AccessTrendResp accessTrendResp = redisService.get(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_OPT
                + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                + StringPool.UNDERSCORE + accessType.getCode(), AccessTrendResp.class);
        log.info("监管分析{}访问量redis查询:{}", accessType.getDesc(), JSON.toJSONString(accessTrendResp));
        if (Objects.isNull(accessTrendResp)) {
            accessTrendResp = getAccessTrend(req, businessType, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析{}访问量db查询:{}", accessType.getDesc(), JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_OPT
                        + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        } else {
//            log.info("数据权限命中缓存,续期2小时:{}", JSON.toJSONString(optDataUserPmsResp));
//            redisService.setExpire(USER_OPT_DATA_PMS_SCOPE + req.getUserId() + StringPool.UNDERSCORE
//                            + req.getModuleType() + StringPool.UNDERSCORE + req.getOperateType(),
//                    2, TimeUnit.HOURS);
        }

        // 校验缓存时间错误情况,redis挂断场景
        if (!accessTrendResp.getTodayBegin().equals(req.getTodayBegin())) {
            log.info("兜底策略,监管分析{}访问量redis查询时间不一致,重新查询:{}", accessType.getDesc(),
                    JSON.toJSONString(accessTrendResp));
            accessTrendResp = getAccessTrend(req, businessType, userId);
            if (Objects.nonNull(accessTrendResp)) {
                log.info("监管分析用户访问量db查询:{}", JSON.toJSONString(accessTrendResp));
                redisService.setEx(SYSTEM_SUPERVISE_ANALYSIS_ACCESS_TREND_OPT
                        + userId + StringPool.UNDERSCORE + req.getTodayBegin()
                        + StringPool.UNDERSCORE + accessType.getCode(), accessTrendResp, TIME, TIMEUNIT);
            }
        }
        return accessTrendResp;
    }

    /**
     * 统计 分享，下载 阅读量
     *
     * @param req
     * @param businessType
     * @return
     */
    private AccessTrendResp getAccessTrend(AccessTrendDateDTO req, Integer businessType, String userId) {
        // TODO id类型修改
        log.info("当前人用户id:{}", userId);
        List<String> tenantIdList = Optional.of(ksoAuthConfig.isEnable()).map(flag->{
            if(flag){
                return null;
            }else{
                LambdaQueryWrapper<SysUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
                userOrgLqw.eq(SysUserOrg::getUserId, userId);
                userOrgLqw.eq(SysUserOrg::getStatus, SysTenantEnum.StatusEnum.ENABLE.getCode());
                List<SysUserOrg> userOrgList = userOrgService.list(userOrgLqw);
                return userOrgList.stream().map(SysUserOrg::getTenantId).distinct().collect(Collectors.toList());
            }
        }).orElseGet(ArrayList::new);

        String tenantId = SecurityUtils.getTenantId();
        List<OptTrendDTO> statisticDataList = ksoAuthConfig.isEnable() ? sysOperLogService.queryOperLogAccessTrendByTeanantdId(
                new Date(req.getThirtyDaysAgo()), new Date(req.getTodayBegin()), businessType, tenantId) :sysOperLogService.queryOperLogAccessTrendByTeanantdId(
                new Date(req.getThirtyDaysAgo()), new Date(req.getTodayBegin()), businessType, tenantIdList);
        log.info("当前登录的用户id：{}，近三十天统计数据：{}", userId, JSON.toJSONString(statisticDataList));

        List<OptTrendDTO> sixStatisticDataList = ksoAuthConfig.isEnable() ? sysOperLogService.queryOperLogAccessTrendByTeanantdId(
                new Date(req.getSixtyDaysAgo()), new Date(req.getThirtyDaysAgo()), businessType, tenantId) : sysOperLogService.queryOperLogAccessTrendByTeanantdId(
                new Date(req.getSixtyDaysAgo()), new Date(req.getThirtyDaysAgo()), businessType, tenantIdList);
        log.info("当前登录的用户id：{}，上一个三十天的统计数据：{}", userId, JSON.toJSONString(sixStatisticDataList));

        // 同比总数 产品沟通 暂不做
//        List<OptTrendDTO> yearStatisticDataList = sysOperLogService.queryOperLogAccessTrend(
//                new Date(req.getLastYearTodayThirtyDaysAgo()), new Date(req.getLastYearToday()), businessType, userIds);

        Long totalCount = 0L;
        if (CollUtil.isNotEmpty(statisticDataList)) {
//            totalCount = (long) statisticDataList.size();
            totalCount = statisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
        Long sixTotalCount = 0L;
        if (CollUtil.isNotEmpty(sixStatisticDataList)) {
//            sixTotalCount = (long) sixStatisticDataList.size();
            sixTotalCount = sixStatisticDataList.stream().mapToLong(OptTrendDTO::getDayCount).sum();
        }
//        Long yearTotalCount = 0L;
//        if (CollUtil.isNotEmpty(yearStatisticDataList)) {
//            yearTotalCount = (long) yearStatisticDataList.size();
//        }

        AccessTrendResp accessTrendResp = new AccessTrendResp();
        accessTrendResp.setTotalCount(totalCount);
        if (sixTotalCount != 0) {
            Long c = totalCount - sixTotalCount;
            accessTrendResp.setChainRate(NumberUtils.analyzePercent(new BigDecimal(c),
                    new BigDecimal(sixTotalCount)).doubleValue());
        } else {
            accessTrendResp.setChainRate(0.0D);
        }
//        if (yearTotalCount != 0) {
//            Long c = totalCount - yearTotalCount;
//            accessTrendResp.setAlikeRate(NumberUtil.div(c.doubleValue(), yearTotalCount.doubleValue()) * 100);
//        } else {
//            accessTrendResp.setAlikeRate(0.0D);
//        }

        // 处理趋势列表
        if (CollUtil.isNotEmpty(statisticDataList)) {
            // 转换并分组统计   获取每一天的开始时间 分组统计
            Map<Long, Long> groupedByDay = statisticDataList.stream()
                    .collect(Collectors.groupingBy(log -> DateUtil.beginOfDay(log.getDayDate()).getTime(),
                            Collectors.summingLong(OptTrendDTO::getDayCount)));
            accessTrendResp.setAccessTrendList(getAccessTrendList(req, groupedByDay));
        } else {
            accessTrendResp.setAccessTrendList(DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30));
        }
        List<AccessTrendDTO> accessTrendList = accessTrendResp.getAccessTrendList();
        accessTrendResp.setTodayBegin(accessTrendList.get(accessTrendList.size() - 1).getDay());
        accessTrendResp.setSixTotalCount(sixTotalCount);
//        accessTrendResp.setYearTotalCount();
        return accessTrendResp;
    }

    private List<AccessTrendDTO> getAccessTrendList(AccessTrendDateDTO req, Map<Long, Long> groupedByDay) {
        if (CollUtil.isNotEmpty(groupedByDay)) {
            // 创建 AccessTrendDTO 列表
            List<AccessTrendDTO> accessTrendDTODbList = groupedByDay.entrySet().stream()
                    .map(entry -> {
                        AccessTrendDTO dto = new AccessTrendDTO();
                        dto.setDay(entry.getKey());
                        dto.setDayCount(entry.getValue());
                        return dto;
                    })
                    .collect(Collectors.toList());

            Map<Long, Long> accessTrendDTODbMap = Maps.newHashMap();
            for (AccessTrendDTO accessTrendDTO : accessTrendDTODbList) {
                accessTrendDTODbMap.put(accessTrendDTO.getDay(), accessTrendDTO.getDayCount());
            }

            return DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30)
                    .stream().map(x -> {
                        Long dayCount = accessTrendDTODbMap.get(x.getDay());
                        x.setDayCount(Objects.nonNull(dayCount) ? dayCount : 0L);
                        return x;
                    }).collect(Collectors.toList());
        } else {
            return DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30);
        }

    }
}
