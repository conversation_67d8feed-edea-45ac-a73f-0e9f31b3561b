package com.suwell.plss.system.state.handler;

import com.alibaba.cola.statemachine.Action;
import com.alibaba.cola.statemachine.Condition;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.dto.request.SysOperateApproveReq;
import com.suwell.plss.system.entity.SysOperateApprove;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.service.SysOperateApproveService;
import com.suwell.plss.system.state.StateMachineHandler;
import com.suwell.plss.system.state.context.SysOperateInfoUserUpdate;
import com.suwell.plss.system.state.context.UserContext;
import com.suwell.plss.system.state.enums.UserEventEnum;
import com.suwell.plss.system.state.enums.UserStatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class UpdateApprovalUserHandler implements StateMachineHandler<UserStatusEnum, UserEventEnum, UserContext> {

    @Resource
    private SysOperateApproveService sysOperateApproveService;

    @Resource
    private IUserService sysUserService;

    @Override
    public Condition<UserContext> condition() {
        return (context -> true);
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public Action<UserStatusEnum, UserEventEnum, UserContext> action() {
        //执行逻辑操作
        return (from,to,event,context)->{
            List<SysOperateApprove> updateList = new ArrayList<>(context.getSysOperateApproveReq().getSysOperateInfo().size());
            List<String> userIds = new ArrayList<>(context.getSysOperateApproveReq().getSysOperateInfo().size());
            for (SysOperateApproveReq sar: context.getSysOperateApproveReq().getSysOperateInfo()) {
                SysOperateApprove operateApprove = sysOperateApproveService.getById(sar.getId());
                SysOperateInfoUserUpdate sysOperateInfo = JSON.parseObject(operateApprove.getOperateValue(), SysOperateInfoUserUpdate.class);
                SysUser sysUser = new SysUser();
                BeanUtils.copyProperties(sysOperateInfo, sysUser);
                sysUserService.modifyUserMainInfo(sysUser);
                userIds.add(sysOperateInfo.getUserId());
                operateApprove.setApproveStatus(context.getSysOperateApproveReq().getApproveStatus());
                operateApprove.setApproveUser(SecurityUtils.getUserId().toString());
                operateApprove.setApproveUserName(SecurityUtils.getUsername());
                operateApprove.setApproveTime(new Date());
                updateList.add(operateApprove);
            }
            // 更新审批记录
            sysOperateApproveService.updateBatchById(updateList);

            // 更新用户状态
            sysUserService.afterAuditModifyStatus(userIds, null);
        };
    }
}
