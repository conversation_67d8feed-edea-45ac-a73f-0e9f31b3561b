package com.suwell.plss.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.constant.Constants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.common.web.controller.BaseController;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.security.annotation.RequiresPermissions;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.api.domain.SysRoleDTO;
import com.suwell.plss.system.api.domain.SysUserDTO;
import com.suwell.plss.system.api.domain.request.RoleUserReq;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysRole;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.SystemBizError;
import com.suwell.plss.system.dto.SysUserRoleDTO;
import com.suwell.plss.system.dto.request.OptDataPmsRoleBindReq;
import com.suwell.plss.system.manager.service.IOrgService;
import com.suwell.plss.system.manager.service.IRoleService;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.sync.AESUtil;

import java.util.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @date 2023年4月20日
 */
@Slf4j
@RestController
@RequestMapping("/v1/role")
public class SysRoleController extends BaseController {

    @Resource
    private IRoleService roleService;
    @Resource
    private IUserService userService;
    @Resource
    private IOrgService orgService;

    @Resource
    private KsoAuthConfig ksoAuthConfig;

    public static final String MODE_NAME = "'后台-角色管理'";


    @RequiresPermissions("system:role:list")
    @PostMapping("/list")
    public R list(@RequestBody SysRoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO,role);
        return R.ok(roleService.selectRoleList(role));
    }

    @Log(title = MODE_NAME, businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:role:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO,role);
        List<SysRole> list = roleService.selectRoleList(role).getList();
        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @RequiresPermissions("system:role:query")
    @PostMapping(value = "/{roleId}")
    public R getInfo(@PathVariable String roleId) {
        roleService.checkRoleDataScope(roleId);
        return R.ok(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @RequiresPermissions("system:role:add")
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody OptDataPmsRoleBindReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        SysRole r = req.getSysRole();
        int code = Optional.of(req).map(o->{
            SysRole role = o.getSysRole();
            if(!roleService.checkRoleNameUnique(role)){
                return -1;
            }else if(!roleService.checkRoleKeyUnique(role)){
                return -2;
            }
            role.setCreateBy(SecurityUtils.getUsername());
            req.setSysRole(role);
            return roleService.insertRole(req);
        }).orElse(-1);
        if (code == -1) {
            return R.error("新增角色'" + r.getRoleName() + "'失败，角色名称已存在");
        } else if (code == -2) {
            return R.error("新增角色'" + r.getRoleName() + "'失败，角色权限已存在");
        }
        return code > 0 ? R.ok() : R.error();
    }

    /**
     * 修改保存角色
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R edit(@Validated @RequestBody OptDataPmsRoleBindReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        SysRole r = req.getSysRole();
        int code = Optional.of(req).map(o->{
            SysRole role = req.getSysRole();
            roleService.checkRoleDataScope(role.getRoleId());
            if(!ksoAuthConfig.isEnable() && !roleService.checkRoleNameUnique(role)){
                return -1;
            }
            role.setUpdateBy(SecurityUtils.getUsername());
            req.setSysRole(role);
            return roleService.updateRole(req);
        }).orElse(-1);
        if (code == -1) {
            return R.error("修改角色'" + r.getRoleName() + "'失败，角色名称已存在");
        }
        return code > 0 ? R.ok() : R.error();
    }

    /**
     * 修改保存数据权限
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/dataScope")
    public R dataScope(@RequestBody SysRoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO,role);
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return roleService.authDataScope(role) > 0 ? R.ok() : R.error();
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus")
    public R changeStatus(@RequestBody SysRoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO,role);
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        role.setUpdateBy(SecurityUtils.getUsername());
        return roleService.updateRoleStatus(role) > 0 ? R.ok() : R.error();
    }

    /**
     * 删除角色
     */
    @RequiresPermissions("system:role:remove")
    @Log(title = MODE_NAME, businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R remove(@RequestBody String[] roleIds) {
        return roleService.deleteRoleByIds(roleIds) > 0 ? R.ok() : R.error();
    }

    /**
     * 获取角色选择框列表
     */
    @RequiresPermissions("system:role:query")
    @PostMapping("/optionselect")
    public R optionselect() {
        return R.ok(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    @RequiresPermissions("system:role:list")
    @PostMapping("/authUser/allocatedList")
    public R<String> allocatedList(@RequestBody SysRoleDTO roleDTO) {
        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO,role);
        //用户信息加密处理
        try {
            String secretKey = SecurityUtils.getSecretKey();
            PageUtils<SysUser> sysUserPageUtils = userService.selectAllocatedList(role);
            log.info("secretKey:{},str:{}",secretKey,JSONUtil.toJsonStr(sysUserPageUtils));
            return R.ok(AESUtil.encrypte(secretKey, JSONUtil.toJsonStr(sysUserPageUtils)));
        } catch (Exception e) {
            log.error("用户信息加密异常",e);
            return R.error(SystemBizError.USER_INFO_ENCRY_ERROR.getCode(),SystemBizError.USER_INFO_ENCRY_ERROR.getMessage());
        }
    }

    /**
     * 查询未分配角色的用户列表
     */
    @RequiresPermissions("system:role:list")
    @PostMapping("/authUser/unallocatedList")
    public R unallocatedList(@RequestBody SysUserDTO userDTO) {
        //用户信息加密处理
        try {
            SysUser user = new SysUser();
            BeanUtils.copyProperties(userDTO,user);

            String secretKey = SecurityUtils.getSecretKey();
            PageUtils<SysUser> sysUserPageUtils = userService.selectUnallocatedList(user);
            log.info("secretKey:{},str:{}",secretKey,JSONUtil.toJsonStr(sysUserPageUtils));
            return R.ok(AESUtil.encrypte(secretKey, JSONUtil.toJsonStr(sysUserPageUtils)));
        } catch (Exception e) {
            log.error("用户信息加密异常",e);
            return R.error(SystemBizError.USER_INFO_ENCRY_ERROR.getCode(),SystemBizError.USER_INFO_ENCRY_ERROR.getMessage());
        }
    }

    /**
     * 取消授权用户
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.GRANT)
    @PostMapping("/authUser/cancel")
    public R cancelAuthUser(@RequestBody SysUserRoleDTO userRoleDTO) {
        return roleService.deleteAuthUser(userRoleDTO) > 0 ? R.ok() : R.error();
    }

    /**
     * 批量取消授权用户
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.REVOKE,remark = "#roleUserReq.getRoleName() + '角色取消授权用户' + #roleUserReq.getUserIds().toString().replaceAll(',','、').replaceAll('\\[|\\]','')")
    @PostMapping("/revoke/batch")
    public R cancelAuthUserAll(@RequestBody RoleUserReq roleUserReq) {
        roleService.deleteAuthUsers(roleUserReq.getRoleId(), roleUserReq.getUserIds());
        return  R.ok();
    }

    /**
     * 批量选择用户授权
     */
    @RequiresPermissions("system:role:edit")
    @Log(title = MODE_NAME, businessType = BusinessType.GRANT,remark = "#roleUserReq.getRoleName() + '角色添加授权用户' + #roleUserReq.getUserIds().toString().replaceAll(',','、').replaceAll('\\[|\\]','')")
    @PostMapping("/authUser/batch")
    public R selectAuthUserAll(@RequestBody RoleUserReq roleUserReq) {
        roleService.insertAuthUsers(roleUserReq.getRoleId(), roleUserReq.getUserIds());
        return R.ok() ;
    }

    /**
     * 获取对应角色机构树列表
     */
    @RequiresPermissions("system:role:query")
    @PostMapping(value = "/orgTree/{roleId}")
    public R orgTree(@PathVariable("roleId") String roleId) {
        Map<String, Object> ajax = new HashMap<>(Constants.INITIAL_CAPACITY);
        ajax.put("checkedKeys", orgService.selectOrgListByRoleId(roleId));
        ajax.put("orgs", orgService.selectOrgTreeList(new SysOrg()));
        return R.ok(ajax);
    }

    /**
     * 查询所有角色信息
     * @return
     */
    @RequiresPermissions("system:role:query")
    @PostMapping("/all")
    public R getAll(@RequestBody(required = false) Map<String,String> param){
        String roleName = null;
        if(!CollectionUtils.isEmpty(param) && param.containsKey("roleName")){
            roleName = param.get("roleName");
        }
        return R.ok(roleService.getAll(roleName));
    }

    /**
     * 根据角色名称模糊查询
     */
    @PostMapping("/listByName")
    public R listByName(@RequestParam("roleName") String roleName){
        return R.ok(roleService.listByName(roleName));
    }

    @PostMapping("/getRoleList")
    public R<List<SysRole>> getRoleList(@RequestBody List<String> roleIdList){
        return R.ok(roleService.listByIds(roleIdList));
    }

}
