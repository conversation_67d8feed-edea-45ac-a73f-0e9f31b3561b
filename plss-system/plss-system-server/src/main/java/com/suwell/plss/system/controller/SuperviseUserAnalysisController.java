package com.suwell.plss.system.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.AccessTrendReq;
import com.suwell.plss.record.standard.dto.response.AccessTrendResp;
import com.suwell.plss.system.service.SuperviseUserAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监管用户分析
 *
 * <AUTHOR>
 * @date 2024/7/29
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/supervise/analysis")
public class SuperviseUserAnalysisController {

    private final SuperviseUserAnalysisService superviseUserAnalysisService;

    /**
     * 统计当前所有启用的用户数量
     */
    @PostMapping("/userCount")
    public R<Long> userCount() {
        return R.ok(superviseUserAnalysisService.userCount());
    }

    /**
     * 统计访问趋势
     */
    @PostMapping("/accessTrend")
    public R<AccessTrendResp> accessTrend(@Validated @RequestBody AccessTrendReq req) {
        return R.ok(superviseUserAnalysisService.accessTrend(req));
    }

}
