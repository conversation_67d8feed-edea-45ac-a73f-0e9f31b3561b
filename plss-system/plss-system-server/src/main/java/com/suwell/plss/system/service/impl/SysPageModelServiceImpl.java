package com.suwell.plss.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.service.FolderRpcService;
import com.suwell.plss.record.service.MetadataRpcService;
import com.suwell.plss.record.service.RecordTypeRpcService;
import com.suwell.plss.record.service.RepositoryRpcService;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.response.FolderResp;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.dto.response.TempUrlResp;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordMetadataValueTypeEnum;
import com.suwell.plss.search.api.http.SearchRpcEntrance;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.enums.SystemBizError;
import com.suwell.plss.system.dto.ConfigDetailEntityDTO;
import com.suwell.plss.system.dto.ConfigDetailIdDTO;
import com.suwell.plss.system.dto.request.PageModelConfigDetailReq;
import com.suwell.plss.system.dto.request.PageModelConfigSaveReq;
import com.suwell.plss.system.dto.request.PageModelListReq;
import com.suwell.plss.system.dto.request.PageModelPageReq;
import com.suwell.plss.system.dto.request.PageModelSaveReq;
import com.suwell.plss.system.dto.response.CategoryResp;
import com.suwell.plss.system.dto.response.PageModelConfigDetailResp;
import com.suwell.plss.system.dto.response.PageModelDetailResp;
import com.suwell.plss.system.dto.response.PageModelListResp;
import com.suwell.plss.system.dto.response.PageModelPageResp;
import com.suwell.plss.system.entity.SysPageModel;
import com.suwell.plss.system.entity.SysPageModuleData;
import com.suwell.plss.system.enums.PageModelEnum.PageModelScene;
import com.suwell.plss.system.enums.PageModelEnum.PageModelIsEnable;
import com.suwell.plss.system.enums.PageModelEnum.PageModelType;
import com.suwell.plss.system.enums.PageModelEnum.PageModuleDataType;
import com.suwell.plss.system.manager.service.IOrgService;
import com.suwell.plss.system.mapper.SysPageModelMapper;
import com.suwell.plss.system.mapper.SysPageModuleDataMapper;
import com.suwell.plss.system.service.SysCategoryService;
import com.suwell.plss.system.service.SysOrgService;
import com.suwell.plss.system.service.SysPageModelService;
import com.suwell.plss.system.service.SysUserOrgService;
import com.suwell.plss.system.util.PageModelUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 页面模型 服务层实现
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@Slf4j
public class SysPageModelServiceImpl extends ServiceImpl<SysPageModelMapper, SysPageModel> implements
        SysPageModelService {

    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    private SysPageModelMapper pageModelMapper;

    @Resource
    private SysPageModuleDataMapper pageModuleDataMapper;

    @Resource
    private FileManageRpcService fileManageRpcService;

    @Resource
    private SysUserOrgService sysUserOrgService;

    @Resource
    private IOrgService sysOrgService;

    @Value("${page.model.tenant.isolation.switch:false}")
    private Boolean tenantIsolationSwitch;

    @Resource
    private WFGIdGenerator wfgIdGenerator;

    @Resource
    private MetadataRpcService metadataRpcService;

    @Resource
    private SearchRpcEntrance searchRpcEntrance;

    @Resource
    private RecordTypeRpcService recordTypeRpcService;

    @Resource
    private RepositoryRpcService repositoryRpcService;

    @Resource
    private FolderRpcService folderRpcService;

    @Resource
    private SysCategoryService sysCategoryService;

    /**
     * 处理查询条件中租户条件
     *
     * @param queryWrapper
     */
    private void handleTenantCondition(LambdaQueryWrapper<SysPageModel> queryWrapper) {
        if (tenantIsolationSwitch) {
            queryWrapper.eq(SysPageModel::getTenantId, SecurityUtils.getTenantId());
        }
    }

    /**
     * 查询租户下的页面模型
     *
     * @param id
     * @return
     */
    private SysPageModel getByTenantAndId(Long id) {
        //查询条件
        LambdaQueryWrapper<SysPageModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysPageModel::getId, id);
        handleTenantCondition(queryWrapper);
        return pageModelMapper.selectOne(queryWrapper, false);
    }

    @Override
    public PageUtils<PageModelPageResp> getPage(PageModelPageReq req) {
        //分页条件
        IPage<SysPageModel> page = new Page<>(req.getPage(), req.getPageSize());

        //查询条件
        LambdaQueryWrapper<SysPageModel> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(req.getModelName())) {
            queryWrapper.like(SysPageModel::getModelName, req.getModelName());
        }
        handleTenantCondition(queryWrapper);
        queryWrapper.orderByDesc(SysPageModel::getIsEnable).orderByDesc(SysPageModel::getCreateTime)
                .orderByAsc(SysPageModel::getId);

        IPage<SysPageModel> result = pageModelMapper.selectPage(page, queryWrapper);

        List<PageModelPageResp> list = DozerUtils.convertListToNew(result.getRecords(), PageModelPageResp.class);

        return new PageUtils<>(list, result.getTotal(), result.getSize(), result.getCurrent());
    }

    @Override
    public List<PageModelListResp> getList(PageModelListReq req) {
        //查询条件
        LambdaQueryWrapper<SysPageModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysPageModel::getModelType, req.getModelType());

        handleTenantCondition(queryWrapper);
        List<SysPageModel> modelList = pageModelMapper.selectList(queryWrapper);

        return DozerUtils.convertListToNew(modelList, PageModelListResp.class);
    }

    @Override
    public PageModelDetailResp detail(Long modelId) {
        PageModelDetailResp resp = new PageModelDetailResp();

        SysPageModel pageModel = getByTenantAndId(modelId);

        if (pageModel != null) {
            BeanUtils.copyBeanProp(pageModel, resp);
        }
        return resp;
    }

    @Override
    public void save(PageModelSaveReq req) {
        Long modelId = req.getModelId();
        Date now = new Date();
        String userId = SecurityUtils.getUserId();

        int n = 0;
        //新增
        if (modelId == null) {
            SysPageModel pageModel = new SysPageModel();
            BeanUtils.copyBeanProp(req, pageModel);
            pageModel.setIsEnable(PageModelIsEnable.NO.getCode());//默认未启用
            pageModel.setTenantId(SecurityUtils.getTenantId());
            pageModel.setCreateTime(now);
            pageModel.setCreateBy(userId);
            pageModel.setUpdateTime(now);
            pageModel.setUpdateBy(userId);
            n = pageModelMapper.insert(pageModel);
        } else {
            SysPageModel pageModel = getByTenantAndId(modelId);
            AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);

            BeanUtils.copyBeanProp(req, pageModel);
            pageModel.setUpdateTime(now);
            pageModel.setUpdateBy(userId);
            n = pageModelMapper.updateById(pageModel);
        }
        AssertUtils.isFalse(n == 0, SystemBizError.PAGE_MODEL_ERROR);
    }

    @Override
    public void delete(Long modelId) {
        SysPageModel pageModel = getByTenantAndId(modelId);
        AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);

        AssertUtils.isFalse(pageModel.getModelType() == PageModelType.INDEX.getCode()
                && pageModel.getIsEnable() == PageModelIsEnable.YES.getCode(), SystemBizError.PAGE_MODEL_INDEX_ENABLE);

        int n = pageModelMapper.deleteById(modelId);
        AssertUtils.isFalse(n == 0, SystemBizError.PAGE_MODEL_ERROR);
    }

    @Override
    public PageModelListResp getEnabledIndex() {
        PageModelListResp resp = new PageModelListResp();

        //查询条件
        LambdaQueryWrapper<SysPageModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysPageModel::getModelType, PageModelType.INDEX.getCode())
                .eq(SysPageModel::getIsEnable, PageModelIsEnable.YES.getCode());
        handleTenantCondition(queryWrapper);
        SysPageModel pageModel = pageModelMapper.selectOne(queryWrapper, false);

        if (pageModel != null) {
            BeanUtils.copyBeanProp(pageModel, resp);
        }
        return resp;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void enableIndex(Long modelId) {
        LambdaQueryWrapper<SysPageModel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysPageModel::getModelType, PageModelType.INDEX.getCode())
                .eq(SysPageModel::getIsEnable, PageModelIsEnable.YES.getCode());
        handleTenantCondition(queryWrapper);
        SysPageModel enablePageModel = pageModelMapper.selectOne(queryWrapper, false);

        Date now = new Date();
        String userId = SecurityUtils.getUserId();

        //禁用原首页
        if (enablePageModel != null) {
            enablePageModel.setIsEnable(PageModelIsEnable.NO.getCode());
            enablePageModel.setUpdateTime(now);
            enablePageModel.setUpdateBy(userId);
            pageModelMapper.updateById(enablePageModel);
        }

        //启用指定首页
        SysPageModel pageModel = getByTenantAndId(modelId);
        AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);
        AssertUtils.isTrue(pageModel.getModelType() == PageModelType.INDEX.getCode(),
                SystemBizError.PAGE_MODEL_NOT_INDEX);
        pageModel.setIsEnable(PageModelIsEnable.YES.getCode());
        pageModel.setUpdateTime(now);
        pageModel.setUpdateBy(userId);
        int n = pageModelMapper.updateById(pageModel);
        AssertUtils.isFalse(n == 0, SystemBizError.PAGE_MODEL_ERROR);
    }

    @Override
    public void disableIndex(Long modelId) {
        SysPageModel pageModel = getByTenantAndId(modelId);
        AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);
        AssertUtils.isTrue(pageModel.getModelType() == PageModelType.INDEX.getCode(),
                SystemBizError.PAGE_MODEL_NOT_INDEX);

        pageModel.setIsEnable(PageModelIsEnable.NO.getCode());
        pageModel.setUpdateTime(new Date());
        pageModel.setUpdateBy(SecurityUtils.getUserId());
        int n = pageModelMapper.updateById(pageModel);
        AssertUtils.isFalse(n == 0, SystemBizError.PAGE_MODEL_ERROR);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void configSave(PageModelConfigSaveReq req) {
        Date now = new Date();
        String userId = SecurityUtils.getUserId();

        SysPageModel pageModel = getByTenantAndId(req.getModelId());
        AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);

        pageModel.setModelConfig(req.getModelConfig());
        pageModel.setUpdateTime(now);
        pageModel.setUpdateBy(userId);
        int n = pageModelMapper.updateById(pageModel);
        AssertUtils.isFalse(n == 0, SystemBizError.PAGE_MODEL_ERROR);

        pageModuleDataMapper.delete(
                Wrappers.<SysPageModuleData>lambdaQuery().eq(SysPageModuleData::getModelId, req.getModelId()));

        if (CollectionUtils.isEmpty(req.getModuleDataList())) {
            return;
        }
        List<SysPageModuleData> pmdList = new ArrayList<>();
        req.getModuleDataList().forEach(moduleData -> {
            SysPageModuleData pmd = new SysPageModuleData();
            pmd.setId(wfgIdGenerator.next());
            pmdList.add(pmd);
            pmd.setModelId(req.getModelId());
            BeanUtils.copyProperties(moduleData, pmd);
            pmd.setCreateTime(now);
            pmd.setCreateBy(userId);
            pmd.setUpdateTime(now);
            pmd.setUpdateBy(userId);
        });
        pageModuleDataMapper.insertList(pmdList);
    }

    @Override
    public PageModelConfigDetailResp configDetail(PageModelConfigDetailReq req) {
        Long modelId = req.getModelId();
        String entryModuleCode = req.getEntryModuleCode();
        PageModelScene scene = req.getScene();

        PageModelConfigDetailResp resp = new PageModelConfigDetailResp();
        SysPageModel pageModel = getByTenantAndId(modelId);
        AssertUtils.notNull(pageModel, SystemBizError.PAGE_MODEL_NOT_EXIST);
        resp.setModelConfig(pageModel.getModelConfig());

        List<SysPageModuleData> moduleDataList = pageModuleDataMapper.selectList(
                Wrappers.<SysPageModuleData>lambdaQuery().eq(SysPageModuleData::getModelId, modelId));

        if (StringUtils.isNotBlank(entryModuleCode) && scene.getCode() == PageModelScene.FRONT.getCode()) {
            moduleDataList = moduleDataList.stream()
                    .filter(moduleData -> StringUtils.isBlank(moduleData.getEntryModuleCode())
                            || moduleData.getEntryModuleCode().equals(entryModuleCode)).toList();
        }

        ConfigDetailIdDTO idDTO = readData(scene, moduleDataList);
        ConfigDetailEntityDTO entityDTO = queryData(scene, idDTO);
        writeData(scene, moduleDataList, entityDTO);

        resp.setModuleDataList(DozerUtils.convertListToNew(moduleDataList, PageModelConfigDetailResp.ModuleData.class));
        return resp;
    }

    /**
     * 读数据
     *
     * @param scene 场景
     * @param moduleDataList 组件数据集合
     * @return 存储id
     */
    private ConfigDetailIdDTO readData(PageModelScene scene, List<SysPageModuleData> moduleDataList) {
        ConfigDetailIdDTO idDTO = new ConfigDetailIdDTO();

        for (SysPageModuleData moduleData : moduleDataList) {
            String dataConfig = moduleData.getDataConfig();
            if (StringUtils.isBlank(dataConfig)) {
                continue;
            }
            JSONObject jsonObj = JSON.parseObject(dataConfig);

            if (moduleData.getDataType() == PageModuleDataType.TITLE.getCode()) {

            } else if (moduleData.getDataType() == PageModuleDataType.FILE.getCode()) {
                readData4Id(idDTO.getFileIdList(), PageModelUtil.getJSONArray(jsonObj, "fileList"));
            } else if (moduleData.getDataType() == PageModuleDataType.ROUTE_PAGE.getCode()) {

            } else if (moduleData.getDataType() == PageModuleDataType.DATA_SOURCE_FROM.getCode()) {
                readData4Id(idDTO.getRecordTypeIdList(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"));
                readData4Id(idDTO.getRepoIdList(), PageModelUtil.getJSONArray(jsonObj, "repoList"));
            } else if (moduleData.getDataType() == PageModuleDataType.EFFECTIVE_RANGE.getCode()) {
                if (scene.getCode() == PageModelScene.FRONT.getCode()) {
                    readData4Id(idDTO.getEffectiveRangeOrgIdList().stream().map(Long::parseLong).toList(), PageModelUtil.getJSONArray(jsonObj, "orgList"));
                }
                readData4Id(idDTO.getOrgIdList().stream().map(Long::parseLong).toList(), PageModelUtil.getJSONArray(jsonObj, "orgList"));
            } else if (moduleData.getDataType() == PageModuleDataType.FILTER_SEARCH_RANGE.getCode()) {
                readData4Id(idDTO.getMdIdList(), PageModelUtil.getJSONArray(jsonObj, "mdList"));
            } else if (moduleData.getDataType() == PageModuleDataType.FILTER_SEARCH_QUICK_ITEM.getCode()) {
                readData4Id(idDTO.getMdIdList(), PageModelUtil.getJSONArray(jsonObj, "mdList"));
                readData4Id(idDTO.getRecordTypeIdList(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"));
                readData4Id(idDTO.getRepoIdList(), PageModelUtil.getJSONArray(jsonObj, "repoList"));
                readData4Id(idDTO.getCategoryIdList(), PageModelUtil.getJSONArray(jsonObj, "categoryList"));
            } else if (moduleData.getDataType() == PageModuleDataType.CLASSIFY_SEARCH_DATA.getCode()) {
                readData4Id(idDTO.getMdIdList(), PageModelUtil.getJSONArray(jsonObj, "mdList"));
                readData4Id(idDTO.getRecordTypeIdList(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"));
                readData4Id(idDTO.getRepoIdList(), PageModelUtil.getJSONArray(jsonObj, "repoList"), 1);
                readData4Id(idDTO.getFolderIdList(), PageModelUtil.getJSONArray(jsonObj, "repoList"), 2);
                readData4Id(idDTO.getCategoryIdList(), PageModelUtil.getJSONArray(jsonObj, "categoryList"));
                readData4Id(idDTO.getOrgIdList().stream().map(Long::parseLong).toList(), PageModelUtil.getJSONArray(jsonObj, "orgList"));
            }
        }

        return idDTO;
    }

    private void readData4Id(List<Long> idList, JSONArray dataArr) {
        readData4Id(idList, dataArr, 0);
    }

    private void readData4Id(List<Long> idList, JSONArray dataArr, int filterType) {
        dataArr.forEach(oneObj -> {
            JSONObject one = (JSONObject) oneObj;
            Optional.ofNullable(PageModelUtil.getLong(one, "id")).ifPresent(id -> {
                Integer type = PageModelUtil.getInteger(one, "type");
                if (filterType <= 0 || (type != null && type == filterType)) {
                    idList.add(id);
                }
            });

            JSONArray children = PageModelUtil.getJSONArray(one, "children");
            if (CollectionUtils.isNotEmpty(children)) {
                readData4Id(idList, children, filterType);
            }
        });
    }

    /**
     * 查询数据
     *
     * @param scene 场景
     * @param idDTO 存储id
     * @return 存储实体
     */
    private ConfigDetailEntityDTO queryData(PageModelScene scene, ConfigDetailIdDTO idDTO) {
        ConfigDetailEntityDTO entityDTO = new ConfigDetailEntityDTO();

        //文件Id集合
        if (CollectionUtils.isNotEmpty(idDTO.getFileIdList())) {
            List<FileTempUrlReq> urlReqList = idDTO.getFileIdList().stream().map(fileId -> {
                FileTempUrlReq urlReq = new FileTempUrlReq();
                urlReq.setFileId(fileId);
                urlReq.setDuration(0);
                urlReq.setForFront(true);
                return urlReq;
            }).collect(Collectors.toList());
            R<List<TempUrlResp>> tempUrlListR = fileManageRpcService.batchTempUrl(urlReqList);
            if (tempUrlListR.isSuccess()) {
                tempUrlListR.getData()
                        .forEach(tempUrl -> entityDTO.getFileIdMap().put(tempUrl.getFileId(), tempUrl.getTempUrl()));
            }
        }

        //生效范围
        if (CollectionUtils.isNotEmpty(idDTO.getEffectiveRangeOrgIdList())
                && PageModelScene.FRONT.getCode() == scene.getCode()) {
            entityDTO.setEffectiveRangeDescendantOrgList(
                    sysOrgService.listDescendant(idDTO.getEffectiveRangeOrgIdList()));
            entityDTO.setEffectiveRangeUserOrgList(sysUserOrgService.listByUserId(SecurityUtils.getUserId()));
        }

        //机构id集合
        if (CollectionUtils.isNotEmpty(idDTO.getOrgIdList())) {
            List<SysOrg> orgList = sysOrgService.findByIds(idDTO.getOrgIdList());
            orgList.forEach(org -> {
                entityDTO.getOrgIdMap().put(Long.parseLong(org.getOrgId()), org.getOrgName());
            });
        }

        //元数据Id集合
        if (CollectionUtils.isNotEmpty(idDTO.getMdIdList())) {
            R<List<MetadataResp>> mdListR = metadataRpcService.queryByIds(idDTO.getMdIdList());
            if (mdListR.isSuccess()) {
                mdListR.getData().forEach(md -> entityDTO.getMdIdMap().put(md.getId(), md));
            }
        }

        //文档类型id集合
        if (CollectionUtils.isNotEmpty(idDTO.getRecordTypeIdList())) {
            R<List<RecordTypeResp>> recordTypeListR = recordTypeRpcService.queryByIds(idDTO.getRecordTypeIdList());
            if (recordTypeListR.isSuccess()) {
                recordTypeListR.getData()
                        .forEach(recordType -> entityDTO.getRecordTypeIdMap()
                                .put(recordType.getId(), recordType.getName()));
            }
        }

        //文库id集合
        if (CollectionUtils.isNotEmpty(idDTO.getRepoIdList())) {
            R<List<RepositoryResp>> repoListR = repositoryRpcService.queryByIds(idDTO.getRepoIdList());
            if (repoListR.isSuccess()) {
                repoListR.getData().forEach(repo -> entityDTO.getRepoIdMap().put(repo.getId(), repo.getName()));
            }
        }

        //目录id集合
        if (CollectionUtils.isNotEmpty(idDTO.getFolderIdList())) {
            R<List<FolderResp>> folderListR = folderRpcService.batchQueryByIds(idDTO.getFolderIdList());
            if (folderListR.isSuccess()) {
                folderListR.getData()
                        .forEach(folder -> entityDTO.getFolderIdMap().put(folder.getId(), folder.getName()));
            }
        }

        //分类标签id集合
        if (CollectionUtils.isNotEmpty(idDTO.getCategoryIdList())) {
            List<CategoryResp> categoryList = sysCategoryService.queryByIds(idDTO.getCategoryIdList());
            categoryList.forEach(category -> {
                entityDTO.getCategoryIdMap().put(category.getId(), category.getName());
            });
        }
        return entityDTO;
    }


    /**
     * 写数据
     *
     * @param scene 场景
     * @param moduleDataList 组件数据集合
     * @param entityDTO 存储实体
     */
    private void writeData(PageModelScene scene, List<SysPageModuleData> moduleDataList,
            ConfigDetailEntityDTO entityDTO) {
        //回写数据
        for (SysPageModuleData moduleData : moduleDataList) {
            String dataConfig = moduleData.getDataConfig();
            if (StringUtils.isBlank(dataConfig)) {
                continue;
            }
            JSONObject jsonObj = JSON.parseObject(dataConfig);

            if (moduleData.getDataType() == PageModuleDataType.TITLE.getCode()) {

            } else if (moduleData.getDataType() == PageModuleDataType.FILE.getCode()) {
                writeData4Id(entityDTO.getFileIdMap(), PageModelUtil.getJSONArray(jsonObj, "fileList"), "url");
            } else if (moduleData.getDataType() == PageModuleDataType.ROUTE_PAGE.getCode()) {

            } else if (moduleData.getDataType() == PageModuleDataType.DATA_SOURCE_FROM.getCode()) {
                writeData4Id(entityDTO.getRecordTypeIdMap(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"),
                        "name");
                writeData4Id(entityDTO.getRepoIdMap(), PageModelUtil.getJSONArray(jsonObj, "repoList"), "name");
            } else if (moduleData.getDataType() == PageModuleDataType.EFFECTIVE_RANGE.getCode()) {
                if (PageModelScene.FRONT.getCode() == scene.getCode()) {
                    writeEffectiveRange(entityDTO, jsonObj);
                }
                writeData4Id(entityDTO.getOrgIdMap(), PageModelUtil.getJSONArray(jsonObj, "orgList"), "name");
            } else if (moduleData.getDataType() == PageModuleDataType.FILTER_SEARCH_RANGE.getCode()) {
                writeMd(entityDTO.getMdIdMap(), PageModelUtil.getJSONArray(jsonObj, "mdList"), "name");
            } else if (moduleData.getDataType() == PageModuleDataType.FILTER_SEARCH_QUICK_ITEM.getCode()) {
                writeMd(entityDTO.getMdIdMap(), PageModelUtil.getJSONArray(jsonObj, "mdList"), "name");
                writeData4Id(entityDTO.getRecordTypeIdMap(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"),
                        "name");
                writeData4Id(entityDTO.getRepoIdMap(), PageModelUtil.getJSONArray(jsonObj, "repoList"), "name");
                writeData4Id(entityDTO.getCategoryIdMap(), PageModelUtil.getJSONArray(jsonObj, "categoryList"), "name");
            } else if (moduleData.getDataType() == PageModuleDataType.CLASSIFY_SEARCH_DATA.getCode()) {
                JSONArray mdTypeList = PageModelUtil.getJSONArray(jsonObj, "mdList");
                mdTypeList.forEach(mdTypeObj -> {
                    JSONObject mdType = (JSONObject) mdTypeObj;
                    writeMd(entityDTO.getMdIdMap(), PageModelUtil.getJSONArray(mdType, "children"), "name");
                });
                writeData4Id(entityDTO.getRecordTypeIdMap(), PageModelUtil.getJSONArray(jsonObj, "recordTypeList"),
                        "name");
                writeData4Id(entityDTO.getRepoIdMap(), PageModelUtil.getJSONArray(jsonObj, "repoList"), "name", 1);
                writeData4Id(entityDTO.getFolderIdMap(), PageModelUtil.getJSONArray(jsonObj, "repoList"), "name", 2);
                writeData4Id(entityDTO.getCategoryIdMap(), PageModelUtil.getJSONArray(jsonObj, "categoryList"), "name");
                writeData4Id(entityDTO.getOrgIdMap(), PageModelUtil.getJSONArray(jsonObj, "orgList"), "name");
            }

            moduleData.setDataConfig(JSON.toJSONString(jsonObj));
        }
    }

    private void writeData4Id(Map<Long, String> idMap, JSONArray dataArr, String nameKey) {
        writeData4Id(idMap, dataArr, nameKey, 0);
    }

    private void writeData4Id(Map<Long, String> idMap, JSONArray dataArr, String nameKey, int filterType) {
        ListIterator<Object> iterator = dataArr.listIterator();
        while (iterator.hasNext()) {
            Object oneObj = iterator.next();
            JSONObject one = (JSONObject) oneObj;
            Optional.ofNullable(PageModelUtil.getLong(one, "id")).ifPresent(id -> {
                Integer type = PageModelUtil.getInteger(one, "type");
                if (filterType <= 0 || (type != null && type == filterType)) {
                    Optional.ofNullable(idMap.get(id)).ifPresentOrElse(name -> {
                        one.put(nameKey, name);
                    }, iterator::remove);
                }
            });
            JSONArray children = PageModelUtil.getJSONArray(one, "children");
            if (CollectionUtils.isNotEmpty(children)) {
                writeData4Id(idMap, children, nameKey, filterType);
            }
        }
    }

    private void writeEffectiveRange(ConfigDetailEntityDTO entityDTO, JSONObject jsonObj) {
        JSONArray orgList = PageModelUtil.getJSONArray(jsonObj, "orgList");
        if (CollectionUtils.isEmpty(orgList)) {
            jsonObj.put("isPermit", "false");
        } else {
            //当前机构id列表
            List<String> currentOrgIdList = orgList.stream().map(orgObj -> {
                JSONObject org = (JSONObject) orgObj;
                return PageModelUtil.getLong(org, "id")+"";
            }).toList();
            //当前有权限的机构列表
            List<String> permitOrgIdList = entityDTO.getEffectiveRangeDescendantOrgList().stream().
                    filter(sysOrg -> currentOrgIdList.stream()
                            .anyMatch(orgId -> sysOrg.getAncestors().contains(String.valueOf(orgId))))
                    .map(SysOrg::getOrgId).collect(Collectors.toList());
            permitOrgIdList.addAll(currentOrgIdList);

            //有权限
            if (entityDTO.getEffectiveRangeUserOrgList().stream()
                    .anyMatch(userOrg -> permitOrgIdList.contains(userOrg.getOrgId()))) {
                jsonObj.put("isPermit", "true");
            } else {
                jsonObj.put("isPermit", "false");
            }
        }
    }

    private void writeMd(Map<Long, MetadataResp> idMap, JSONArray dataArr, String nameKey) {
        ListIterator<Object> iterator = dataArr.listIterator();
        while (iterator.hasNext()) {
            Object oneObj = iterator.next();
            JSONObject one = (JSONObject) oneObj;
            Optional.ofNullable(PageModelUtil.getLong(one, "id")).ifPresent(id -> {
                Optional.ofNullable(idMap.get(id)).ifPresentOrElse(md -> {
                    one.put(nameKey, md.getName());
                    if (RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_LIST.getCode()
                            .equals(md.getValueType())) {
                        String[] rangeArr = md.getValueRange()
                                .substring(2, md.getValueRange().length() - 2).split("\\|");
                        List<JSONObject> children = Stream.of(rangeArr).map(v -> {
                            JSONObject child = new JSONObject();
                            child.put(nameKey, v);
                            return child;
                        }).toList();
                        one.put("children", children);
                    } else if (plssFixedMetadataNameConfig.getFixedYearName()
                            .equals(md.getName())) {
                        //年份元数据
                        R<List<String>> yearListR = searchRpcEntrance.getYearList();
                        if (yearListR.isSuccess()) {
                            List<JSONObject> children = yearListR.getData().stream().map(v -> {
                                JSONObject child = new JSONObject();
                                child.put(nameKey, v);
                                return child;
                            }).toList();
                            one.put("children", children);
                        }
                    }
                }, iterator::remove);
            });
        }
    }
}
