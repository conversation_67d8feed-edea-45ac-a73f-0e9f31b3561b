package com.suwell.plss.system.controller.sync;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.suwell.plss.domain.BaseResult;
import com.suwell.plss.domain.DataOrg;
import com.suwell.plss.domain.DataUser;
import com.suwell.plss.domain.ReqData;
import com.suwell.plss.domain.ReqData.Org;
import com.suwell.plss.domain.ReqData.User;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.entity.SysSource;
import com.suwell.plss.system.service.SysSourceService;
import com.suwell.plss.system.sync.AESUtil;
import com.suwell.plss.system.sync.Synchronizer;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:yuehai。gao
 * @Date: 2023/9/1
 */
@RestController
@RequestMapping("/v1/oau/sync")
public class SyncController {

    @Autowired
    private Synchronizer synchronizer;

    @Autowired
    private SysSourceService sysSourceService;

    @PostMapping("/org")
    public BaseResult<List<DataOrg>> syncOrg(@RequestBody String json,
            @RequestHeader("sourceUnicode") String token) throws Exception{

        try {
            long count = sysSourceService.count(new LambdaQueryWrapper<SysSource>().eq(SysSource::getToken, token));
            if (count == 0) {
                return BaseResult.error("非法的客户来源系统");
            }

            //查询来源系统密钥
            SysSource one = sysSourceService.getOne(new LambdaQueryWrapper<SysSource>().eq(SysSource::getToken, token));

            String decrypte = AESUtil.decrypte(one.getSecretKey(), json);
            Org orgReq = JSONObject.parseObject(decrypte, Org.class);

            List<DataOrg> syncOrgs = orgReq.getData();
            List<SysOrg> orgs = new ArrayList<>();
            for (DataOrg org : syncOrgs) {
                orgs.add(SyncOrgToSysOrg(org));
            }
            //返回同步失败的
            List<DataOrg> dataOrgs = synchronizer.syncOrg(orgs, orgReq.getOperate());
            if(dataOrgs != null && dataOrgs.size() > 0){
                return BaseResult.error(dataOrgs);
            }else {
                return BaseResult.ok();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.error("服务端未知异常,同步失败");
        }
    }

    private SysOrg SyncOrgToSysOrg(DataOrg dataOrg) {

        SysOrg sysOrg = new SysOrg();
        sysOrg.setAccessKey(dataOrg.getAccessKey());
        sysOrg.setParentAccessKey(dataOrg.getParentAccessKey());
        sysOrg.setOrgName(dataOrg.getName());
        sysOrg.setOrgCode(dataOrg.getCode());
        sysOrg.setOrderNum(dataOrg.getOrderNum());
        sysOrg.setLeader(dataOrg.getLeader());
        sysOrg.setPhone(dataOrg.getPhone());
        sysOrg.setEmail(dataOrg.getEmail());
        sysOrg.setStatus(dataOrg.getStatus());
        sysOrg.setRemark(dataOrg.getRemark());
        sysOrg.setOrgType(dataOrg.getOrgType());
        sysOrg.setExistType(dataOrg.getExistType());
        sysOrg.setOrgLevel(dataOrg.getOrgLevel());

        return sysOrg;

    }

    @PostMapping("/user")
    public BaseResult<List<DataUser>> syncUser(@RequestBody String json,
            @RequestHeader("sourceUnicode") String token) throws Exception{
        try {
            long count = sysSourceService.count(new LambdaQueryWrapper<SysSource>().eq(SysSource::getToken, token));
            if (count == 0) {
                return BaseResult.error("非法的客户来源系统");
            }

            //查询来源系统密钥
            SysSource one = sysSourceService.getOne(new LambdaQueryWrapper<SysSource>().eq(SysSource::getToken, token));

            String decrypte = AESUtil.decrypte(one.getSecretKey(), json);
            User req = JSONObject.parseObject(decrypte, User.class);

            List<DataUser> sysUsers = req.getData();
            List<SysUser> eus = new ArrayList<>();
            for (DataUser user : sysUsers) {
                eus.add(SyncUserToSysUser(user));
            }
            //返回同步失败的
            List<DataUser> dataUsers = synchronizer.syncUser(eus, req.getOperate());
            if(dataUsers != null && dataUsers.size() > 0){
                return BaseResult.error(dataUsers);
            }else {
                return BaseResult.ok();
            }

        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.error("服务端未知异常,同步失败");
        }
    }

    private SysUser SyncUserToSysUser(DataUser dataUser) {
        SysUser sysUser = new SysUser();
        sysUser.setAccessKey(dataUser.getAccessKey());
        sysUser.setUserName(dataUser.getName());
        sysUser.setNickName(dataUser.getNickName());
        sysUser.setEmail(dataUser.getEmail());
        sysUser.setPhonenumber(dataUser.getPhone());
        sysUser.setSex(dataUser.getSex());
        sysUser.setAvatar(dataUser.getAvatar());
        sysUser.setPassword(dataUser.getPassword());//一般不会同步
        sysUser.setStatus(dataUser.getStatus());
        sysUser.setRemark(dataUser.getRemark());
        sysUser.setOrgAccessKeys(dataUser.getOrgAccessKey());
        return sysUser;

    }


}
