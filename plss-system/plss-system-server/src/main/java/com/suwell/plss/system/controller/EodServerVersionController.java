package com.suwell.plss.system.controller;

import com.suwell.plss.framework.common.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
@RestController
@RequestMapping("/system")
public class EodServerVersionController {

    @GetMapping("/eod-server-version")
    public R<Map<String, Object>> serverVersion() {
        Map<String, Object> map = new HashMap<>(2);
        map.put("version", "2.6.0-1130");
        map.put("desc", "公文产品");
        return R.ok(map);
    }

}
