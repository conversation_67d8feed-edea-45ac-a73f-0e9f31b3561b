package com.suwell.plss.system.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.system.api.entity.SysLogininfor;
import com.suwell.plss.system.api.entity.SysOperLog;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.encrypt.service.EncryptService;
import com.suwell.plss.system.manager.service.IOrgService;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.service.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.util.List;

@Slf4j
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class XxlJobExecute {

    @Value("${app.tmp.folderpath}")
    private String folderPath;

    @Value("${app.tmp.reserve:24}")
    private Long reserveTime;

    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private SysLogininforService sysLogininforService;

    @Autowired
    private IUserService userService;
    @Resource
    private EncryptService encryptService;
    @Resource
    private SLCService slcService;
    @Resource
    private SysMessageService messageService;
    @Resource
    private IOrgService orgService;

    @Resource
    private SysRemainService sysRemainService;


    @XxlJob("unZipDir")
    public ReturnT<String> unZipDir() {
        System.out.println("11111111111111111111111");
        return ReturnT.SUCCESS;
    }

    @XxlJob("uploadRecord")
    public ReturnT<String> uploadRecord() {
        System.out.println("22222222222222222222222");
        return ReturnT.SUCCESS;
    }

    @XxlJob("updateLogHistoryFromSign")
    public ReturnT<String> updateLogHistoryFromSign() {

        try {
            log.info("定时任务处理操作日志-数据签名");
            List<SysOperLog> sysOperLogs = sysOperLogService.selectNoSignature();
            if (CollectionUtil.isNotEmpty(sysOperLogs)) {
                for (SysOperLog operLog : sysOperLogs) {
                    sysOperLogService.updateSignature(operLog.getOperId());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            log.info("定时任务处理登录日志-数据签名");
            List<SysLogininfor> sysLogininfors = sysLogininforService.selectNoSignature();
            if (CollectionUtil.isNotEmpty(sysLogininfors)) {
                for (SysLogininfor sysLogininfor : sysLogininfors) {
                    sysLogininforService.updateSignature(sysLogininfor.getInfoId());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("updateUserPhone")
    public ReturnT<String> updateUserPhone() {
        //查询出所有手机号不为空的数据
        List<SysUser> list = userService.listNoMatchPhoneNumber("");
        if (CollectionUtil.isNotEmpty(list)) {
            for (SysUser user : list) {
                try {
                    if (user.getPhonenumber().length() <= 13) {//防止多次执行导致多层加密：手机号11位，座机0371-12345678
                        String encrypt = encryptService.encrypt(null, user.getPhonenumber());
                        userService.updatePhoneNumberByUserId(user.getUserId(),encrypt);
                    }


                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }

        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("archiveLoginLog")
    public ReturnT<String> archiveLoginLog() {
        sysLogininforService.archiveLog();
        return ReturnT.SUCCESS;
    }

    @XxlJob("archiveOperationLog")
    public ReturnT<String> archiveOperationLog() {
        sysOperLogService.archiveLog();
        return ReturnT.SUCCESS;
    }

    @XxlJob("disableSLC")
    public ReturnT<String> disableSLC() {
        slcService.disableSLC();
        return ReturnT.SUCCESS;
    }

    /**
     * 磁盘告警
     *
     * @return
     */
    @XxlJob("diskAlarmMessage")
    public ReturnT<String> diskAlarmMessage() {
        messageService.scanDiskAlarmInfo();
        return ReturnT.SUCCESS;
    }

    @XxlJob("clearApplicationTemp")
    public void clearApplicationTemp() {
        File dir = FileUtils.getFile(folderPath);
        clearFile(dir);
    }

    @XxlJob("clearOverOneMonthSearchHistory")
    public ReturnT<String> clearOverOneMonthSearchHistory() {
        return ReturnT.SUCCESS;
    }

    @XxlJob("fillOrgOwnerId")
    public ReturnT<String> fillOrgOwnerId() {
        orgService.fillOrgOwnerId();
        return ReturnT.SUCCESS;
    }

    /**
     * 清理文件 递归清理文件夹 默认保留24小时
     *
     * @param dir
     */
    private void clearFile(File dir) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                //文件
                if (file.isFile()) {
                    if (checkFileDelete(file)) {
                        FileUtil.del(file);
                    }
                } else {
                    //文件夹递归处理
                    clearFile(file);
                    File[] child = file.listFiles();
                    if (child == null || child.length == 0) {
                        if (checkFileDelete(file)) {
                            FileUtil.del(file);
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查文件是否删除 true 删除 false
     *
     * @param file
     * @return
     */
    private boolean checkFileDelete(File file) {
        try {
            Path path = Paths.get(file.getAbsolutePath());
            Instant creationTime = Files.readAttributes(path, BasicFileAttributes.class)
                    .creationTime().toInstant();
            return Instant.now().toEpochMilli() - creationTime.toEpochMilli() > reserveTime * 3600 * 1000;
        } catch (IOException e) {
            log.error("删除temp文件失败 {}", file.getAbsolutePath());
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 修复代办的接收人字段
     */
    @XxlJob("repairRemainAcceptPersonId")
    public ReturnT<String> repairRemainAcceptPersonId() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("修复代办的接收人字段-开始。jobParam={}", jobParam);

        try {
            sysRemainService.repairRemainAcceptPersonId();
            log.info("修复代办的接收人字段-成功。jobParam={}", jobParam);
        } catch (Exception e) {
            log.error("修复代办的接收人字段-失败。jobParam={},em={}", jobParam, e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }



    /**
     * 发送账号即将到期的消息
     */
    @XxlJob("send")
    public ReturnT<String> sendValidityPeriodMsg() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("发送账号到期消息-开始。jobParam={}", jobParam);

        try {
            messageService.sendValidityPeriodMsg();
            log.info("发送账号到期消息-成功。jobParam={}", jobParam);
        } catch (Exception e) {
            log.error("发送账号到期消息-失败。jobParam={},em={}", jobParam, e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
