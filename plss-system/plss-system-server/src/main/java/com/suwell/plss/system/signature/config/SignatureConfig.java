package com.suwell.plss.system.signature.config;

import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:yuehai。gao
 * @Date: 2024/1/23
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties("encrypt.signature")
public class SignatureConfig implements InitializingBean {
    /**
     * 是否使用第三方签名
     */
    private boolean useThirdSign = false;
    /**
     * jar绝对包路径
     */
    private String jarAbsolutePath;

    /**
     * 签名三方实现类
     */
    private String thirdSignImpl;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (useThirdSign) {
            if (StringUtils.isBlank(jarAbsolutePath)) {
                throw new ServiceException("三方签名验签jar包的路径不能为空");
            }
            if (StringUtils.isBlank(thirdSignImpl)) {
                throw new ServiceException("三方签名验签实现类全路径不能为空");
            }
        }
    }
}
