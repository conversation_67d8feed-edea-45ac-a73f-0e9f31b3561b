package com.suwell.plss.system.controller;

import com.suwell.plss.framework.common.constant.CacheConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.web.controller.BaseController;
import com.suwell.plss.framework.common.web.page.PageDomain;
import com.suwell.plss.framework.common.web.page.TableDataInfo;
import com.suwell.plss.framework.common.web.page.TableSupport;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.security.annotation.RequiresPermissions;
import com.suwell.plss.system.api.domain.LoginUser;
import com.suwell.plss.system.entity.SysUserOnline;
import com.suwell.plss.system.service.SysUserOnlineService;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 * @date 2023年4月20日
 */
@RestController
@RequestMapping("/v1/online")
public class SysUserOnlineController extends BaseController {

    @Autowired
    private SysUserOnlineService userOnlineService;

    @Autowired
    private RedisService redisService;

    @RequiresPermissions("monitor:online:list")
    @GetMapping("/list")
    public R list(String ipaddr, String userName) {
        Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
        for (String key : keys) {
            LoginUser user = redisService.getCacheObject(key);
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
                userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
            } else if (StringUtils.isNotEmpty(ipaddr)) {
                userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
            } else if (StringUtils.isNotEmpty(userName)) {
                userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
            } else {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));

        TableDataInfo dataTable = getDataTable(userOnlineList);
        PageDomain pageDomain = TableSupport.buildPageRequest();
        PageUtils pageUtils = new PageUtils(dataTable.getRows(), dataTable.getTotal(), pageDomain.getPageSize(),
                pageDomain.getPageNum());
        return R.ok(pageUtils);
    }

    /**
     * 强退用户
     */
    @RequiresPermissions("monitor:online:forceLogout")
    @Log(title = "'在线用户'", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    public R forceLogout(@PathVariable String tokenId) {
        redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
        return R.ok();
    }
}
