package com.suwell.plss.system.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 机构用户树
 */
@Data
public class OrgUserTreeResp implements Serializable, Comparator<OrgUserTreeResp> {

    private static final long serialVersionUID = 1L;

    /**
     * 叶子节点id，可能为组织机构id或用户id
     */
    private String leafId;

    /**
     * 组织机构名称或用户名
     */
    private String leafName;

    private String extraName;

    /**
     * 节点类型，1-机构，2-用户
     */
    private Integer leafType;

    /**
     * 子节点
     */
    private List<OrgUserTreeResp> children = new ArrayList<>();

    /**
     * 子节点数量
     */
    private Integer childrenCount;


    @Override
    public int compare(OrgUserTreeResp o1, OrgUserTreeResp o2) {
        return o1.getLeafId().compareTo(o2.getLeafId());
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        OrgUserTreeResp that = (OrgUserTreeResp) o;
        return Objects.equals(leafId, that.leafId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(leafId);
    }
}
