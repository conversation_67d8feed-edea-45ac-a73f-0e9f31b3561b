package com.suwell.plss.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.entity.SysUserOrg;
import com.suwell.plss.system.enums.SysTenantEnum.StatusEnum;
import com.suwell.plss.system.mapper.SysUserOrgMapper;
import com.suwell.plss.system.service.SysUserOrgService;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-08-31 16:05
 **/
@Service
public class SysUserOrgServiceImpl extends ServiceImpl<SysUserOrgMapper, SysUserOrg> implements SysUserOrgService {

    @Resource
    private SysUserOrgMapper sysUserOrgMapper;

    @Override
    public List<SysUserOrg> listByUserId(String userId) {
        LambdaQueryWrapper<SysUserOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysUserOrg::getUserId,userId).eq(SysUserOrg::getStatus,
                StatusEnum.ENABLE.getCode());

        return sysUserOrgMapper.selectList(queryWrapper);
    }
}
