<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <logger name="org.redisson.connection" level="WARN"/>
    <logger name="org.redisson.cluster" level="WARN"/>
    <!-- 日志存放路径 -->
    <property name="log_home" value="/Users/<USER>/Documents/IDEA/logs"/>
    <!-- 日志服务工程名称 -->
    <property name="project_name" value="plss-system"/>
    <!-- 日志输出格式 -->
    <property name="pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %boldYellow([%thread]) %highlight(%-5level) %green(%logger) %blue(method[%M]) %red(line[%L]) seqId[%X{seqId}] reqId[%X{reqId}] traceId[%X{traceId}] - %msg%n"/>
    <property name="rollLogPattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} method[%M] line[%L] seqId[%X{seqId}] reqId[%X{reqId}] traceId[%X{traceId}] - %msg%n"/>

    <!-- 系统日志输出 -->
    <appender name="rollLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 路径也要按照规范修改 -->
        <file>${log_home}/${project_name}/${project_name}.log</file>
        <!--日志消息格式配置-->
        <encoder>
            <pattern>${rollLogPattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>
                ${log_home}/${project_name}/%d{yyyy-MM-dd}/${project_name}.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <!-- 文件大小拆分-->
            <maxFileSize>512MB</maxFileSize>
            <!--保存7天的日志-->
            <maxHistory>7</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <!--启动时候清理日志-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="asyncLog" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <appender-ref ref="rollLog"/>
    </appender>

    <!-- 控制台输出 -->
<!--    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        <target>System.out</target>-->
<!--        <encoder>-->
<!--            <pattern>${pattern}</pattern>-->
<!--            <charset>UTF-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>{"level":"%-5level","time":"%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}","msg":"%msg","request_id":"%X{xreqId}","status_code":"%X{status_code}","upstream":"%X{upstream}","kind":"%X{kind:-local}","cost":"%X{cost:-0}ms","server_url":"%X{server_url}","server_method":"%X{server_method}","args":"%X{args}","remote_addr":"","remote_server":"","host":"","trace_id":"%X{traceId}","process_name":"${project_name}","pos":"[%thread] %logger#%M:%L","extra":"%exception"}</pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!--系统操作日志-->
    <root level="INFO">
        <appender-ref ref="console"/>
        <!--        <appender-ref ref="asyncLog"/>-->
        <appender-ref ref="rollLog"/>
    </root>

    <!--过滤elasticsearch的请求日志-->
    <!--    <logger name="org.elasticsearch.client.RestClient" level="off" additivity="false"/>-->
    <!--    <logger name="org.frameworkset.elasticsearch.client" level="OFF"/>-->
    <logger name="com.alicp.jetcache.support.StatInfoLogger" level="OFF"/>
    <!-- 系统模块日志级别控制  -->
    <logger name="com.suwell.plss" level="info"/>
    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn"/>
    <!-- mybatis日志级别控制 -->
    <logger name="org.apache.ibatis" level="WARN"/>
    <logger name="org.mybatis.spring" level="WARN"/>
    <logger name="org.mybatis.example" level="WARN"/>
    <logger name="com.baomidou" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="WARN"/>

</configuration>