server:
  port: 8061

spring:
  application:
    name: plss-system
  ## 基本配置
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  ## redis config 注意: redis的连接地址，集群和单机方式二选一进行配置；
  ## 1.【集群】配置集群连接地址，则注释掉单机连接配置
  ## 2.【单机】配置单机连接地址，则注释掉“cluster”集群连接配置
  redis:
    # cluster:
    # nodes: *************:6381,*************:6382,*************:6383,*************:6384,*************:6385,*************:6386
    host: *************
    port: 6481
    password: 'RCB+v3qG1o8REDIS'
    lettuce:
      pool:
        max-active: 8 #连接池最大连接数（使用负数表示没有限制）默认8
        max-wait: -1  #连接池最大阻塞等待时间（使用负数表示没有限制）默认-1
        max-idle: 8 #连接池中的最大空闲连接 默认8
        min-idle: 0 #连接池中的最小空闲连接 默认0
  cache:
    type: redis #SpringBoot集成缓存替换默认使用Redis缓存
    redis:
      time-to-live: 3600000  # 毫秒为单位，设置1个小时
      #key-prefix: SUWELL_CACHE_ #如果指定了前缀就用我们指定的前缀，如果没有就默认使用缓存的分区名字作为前缀【推荐没设置缓存前缀时则默认就是缓存分区名作为前缀】
      use-key-prefix: true
      #是否缓存空值，防止缓存穿透
      cache-null-values: true
  session: ##spring session共享存储redis
    store-type: redis  #spring session共享存储redis

  ## db config
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        login-username: admin
        login-password: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,slf4j
        connection-properties:
          druid:
            stat:
              mergeSql: true
              slowSqlMillis: 5000
      datasource:
        master: # 主库数据源
          driver-class-name: org.postgresql.Driver
          url: *******************************************************************************************************************************************************************************************************
          username: suwell
          password: 'ptset0$581E@lWVZ'
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   url: ****************************************************************************************************************************************************
        #   username: root
        #   password: root
        # slave: # 从库数据源
        #   driver-class-name:
        #   url:
        #   username:
        #   password:

## mybatis-plus config
mybatis-plus:
  type-aliases-package: com.suwell.plss.**.entity
  mapper-locations: classpath*:mapper/postgresql/**/*Mapper.xml

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true

# 暴露监控端点
management:
  endpoints:
    web:
      exposure:
        include: '*'