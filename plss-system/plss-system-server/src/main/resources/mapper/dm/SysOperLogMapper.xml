<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysOperLogMapper">

    <resultMap type="com.suwell.plss.system.api.entity.SysOperLog" id="SysOperLogResult">
        <id property="operId" column="oper_id"/>
        <result property="title" column="title"/>
        <result property="businessType" column="business_type"/>
        <result property="method" column="method"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operName" column="oper_name"/>
        <result property="orgName" column="org_name"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
        <result property="costTime" column="cost_time"/>
        <result property="signature" column="signature"/>
        <result property="checkSign" column="check_sign"/>
        <result property="userId" column="user_id"/>
        <result property="remark" column="remark"/>
        <result property="os" column="os"/>
    </resultMap>

    <sql id="selectOperLogVo">
        select oper_id,
               title,
               business_type,
               method,
               request_method,
               operator_type,
               oper_name,
               org_name,
               oper_url,
               oper_ip,
               oper_param,
               json_result,
               status,
               error_msg,
               oper_time,
               cost_time,
               signature,
               check_sign
        from sys_oper_log
    </sql>

    <select id="selectOperLogPage" parameterType="com.suwell.plss.system.api.entity.SysOperLog"
            resultMap="SysOperLogResult">
        select oper_id,title,business_type,method,request_method,operator_type,oper_name,org_name,oper_url,oper_ip,oper_param,json_result,status,
        error_msg,oper_time,cost_time,signature,check_sign,user_id,remark,os
        from sys_oper_log sol
        <where>
            <if test="operLog.title != null and operLog.title != ''">
                AND sol.title like concat('%', #{operLog.title}, '%')
            </if>
            <if test="operLog.status != null and operLog.status != ''">
                AND sol.status = #{operLog.status}
            </if>
            <if test="operLog.businessType != null">
                AND sol.business_type = #{operLog.businessType}
            </if>
            <if test="operLog.operName != null and operLog.operName != ''">
                AND sol.oper_name like concat('%', #{operLog.operName}, '%')
            </if>
            <!-- 开始时间检索 -->
            <if test="operLog.params.beginTime != null and operLog.params.beginTime != ''">
                AND sol.oper_time &gt;= #{operLog.beginTime}
            </if>
            <!-- 结束时间检索 -->
            <if test="operLog.params.endTime != null and operLog.params.endTime != ''">
                AND sol.oper_time &lt;= #{operLog.endTime}
            </if>
            <if test="operLog.roleTypeList != null and operLog.roleTypeList.size() > 0">
                AND
                <foreach collection="operLog.roleTypeList" item="roleType" open="(" separator="or"
                         close=")">
                    sol.role_type like concat('%', #{roleType}, '%')
                </foreach>
                and (
                sol.tenant_id = #{operLog.tenantId} or
                sol.user_id in(
                select uo1.user_id from sys_user_org uo1 where uo1.tenant_id in (
                select uo2.tenant_id from sys_user_org uo2 where uo2.user_id = #{operLog.userId} and uo2.tenant_id != 0
                )
                )
                )

            </if>
        </where>
        order by sol.oper_time
        <if test='operLog.order != null and operLog.order == "1"'>ASC</if>
        <if test='operLog.order !="1" '>DESC</if>
    </select>

    <insert id="insertOperlog" parameterType="com.suwell.plss.system.api.entity.SysOperLog">
        insert into sys_oper_log(oper_id, title, business_type, method, request_method,
                                 operator_type,
                                 oper_name, org_name, oper_url, oper_ip, oper_param, json_result,
                                 status, error_msg, cost_time, oper_time, signature, check_sign)
        values (#{operId}, #{title}, #{businessType}, #{method}, #{requestMethod}, #{operatorType},
                #{operName}, #{orgName}, #{operUrl}, #{operIp}, #{operParam}, #{jsonResult},
                #{status}, #{errorMsg}, #{costTime}, now(), #{signature}, #{checkSign})
    </insert>

    <select id="selectOperLogList" parameterType="com.suwell.plss.system.api.entity.SysOperLog"
            resultMap="SysOperLogResult">
        select oper_id,title,business_type,method,request_method,operator_type,oper_name,org_name,oper_url,oper_ip,oper_param,json_result,status,
        error_msg,oper_time,cost_time,signature,check_sign,user_id,remark,os
        from sys_oper_log sol
        <where>
            <if test="operLog.title != null and operLog.title != ''">
                AND sol.title like concat('%', #{operLog.title}, '%')
            </if>
            <if test="operLog.businessType != null">
                AND sol.business_type = #{operLog.businessType}
            </if>
            <if test="operLog.status != null and operLog.status != ''">
                AND sol.status = #{operLog.status}
            </if>
            <if test="operLog.operName != null and operLog.operName != ''">
                AND sol.oper_name like concat('%', #{operLog.operName}, '%')
            </if>
            <!-- 开始时间检索 -->
            <if test="operLog.params.beginTime != null and operLog.params.beginTime != ''">
                AND sol.oper_time &gt;= #{operLog.beginTime}
            </if>
            <!-- 结束时间检索 -->
            <if test="operLog.params.endTime != null and operLog.params.endTime != ''">
                AND sol.oper_time &lt;= #{operLog.endTime}
            </if>
            <if test="operLog.roleTypeList != null and operLog.roleTypeList.size() > 0">
                AND
                <foreach collection="operLog.roleTypeList" item="roleType" open="(" separator="or"
                         close=")">
                    sol.role_type like concat('%', #{roleType}, '%')
                </foreach>
                and (
                sol.tenant_id = #{operLog.tenantId} or
                sol.user_id in(
                select uo1.user_id from sys_user_org uo1 where uo1.tenant_id in (
                select uo2.tenant_id from sys_user_org uo2 where uo2.user_id = #{operLog.userId} and uo2.tenant_id != 0
                )
                )
                )

            </if>
        </where>
        order by sol.oper_time
        <if test='operLog.order != null and operLog.order == "1"'>ASC</if>
        <if test='operLog.order !="1" '>DESC</if>
    </select>

    <delete id="deleteOperLogByIds" >
        delete from sys_oper_log where oper_id in
        <foreach collection="array" item="operId" open="(" separator="," close=")">
            #{operId}
        </foreach>
    </delete>

    <select id="selectOperLogById"  resultMap="SysOperLogResult">
        <include refid="selectOperLogVo"/>
        where oper_id = #{operId}
    </select>

    <update id="cleanOperLog">
        truncate table sys_oper_log
    </update>

    <update id="updateSignStatus">
        update sys_oper_log
        set check_sign = #{checkSign}
        where oper_id = #{id}
    </update>

    <select id="queryOperLogAccessTrend"
            resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(oper_time, 'YYYY-MM-DD') AS "dayDate",
        COUNT(*) AS "dayCount"
        FROM "sys_oper_log"
        WHERE 1 = 1
        <if test="businessType != null">
            AND business_type = #{businessType}
        </if>
        <if test="stm != null">
            <![CDATA[ AND oper_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND oper_time < #{etm} ]]>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach collection="userIds" item="item" open="(" close=")" separator=",">#{item}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(oper_time, 'YYYY-MM-DD')
        ORDER BY "dayDate";
    </select>

    <select id="queryOperLogAccessTrendByTenantId"
            resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(oper_time, 'YYYY-MM-DD') AS dayDate,
        COUNT(*) AS dayCount
        FROM sys_oper_log
        WHERE 1 = 1
        <if test="businessType != null">
            AND business_type = #{businessType}
        </if>
        <if test="stm != null">
            <![CDATA[ AND oper_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND oper_time < #{etm} ]]>
        </if>
        AND user_id in (
            select suo.user_id
            from sys_user_org suo
            left join sys_user su on suo.user_id = su.user_id
            where suo.tenant_id in
            <foreach collection="list" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            and su.del_flag = '1' and suo.status = 1
        )
        GROUP BY
        TO_CHAR(oper_time, 'YYYY-MM-DD')
        ORDER BY dayDate;
    </select>
    <select id="queryOperLogAccessTrendById" resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(oper_time, 'YYYY-MM-DD') AS dayDate,
        COUNT(*) AS dayCount
        FROM sys_oper_log
        WHERE 1 = 1
        <if test="businessType != null">
            AND business_type = #{businessType}
        </if>
        <if test="stm != null">
            <![CDATA[ AND oper_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND oper_time < #{etm} ]]>
        </if>
        AND tenant_id = #{tenantId}
        GROUP BY
        TO_CHAR(oper_time, 'YYYY-MM-DD')
        ORDER BY dayDate;
    </select>
</mapper>