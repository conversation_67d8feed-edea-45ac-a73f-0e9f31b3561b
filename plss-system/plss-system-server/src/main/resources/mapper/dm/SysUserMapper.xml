<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysUserMapper">

    <resultMap type="com.suwell.plss.system.api.entity.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="orgId" column="org_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="orderNum" column="order_num"/>
        <result property="remark" column="remark"/>
        <result property="validityPeriod" column="validity_period"/>
        <result property="wpsAuth" column="wps_auth"/>
        <result property="defaultTenantId" column="default_tenant_id"/>
        <result property="classified" column="classified"/>
        <result property="approveStatus" column="approve_status"/>
        <result property="dataActStatus" column="data_act_status"/>
        <!--        <result property="tenantId" column="tenant_id"/>-->
        <result property="certificateNo" column="certificate_no"/>
        <association property="org" column="org_id"
                javaType="com.suwell.plss.system.api.entity.SysOrg" resultMap="orgResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="orgResult" type="com.suwell.plss.system.api.entity.SysOrg">
        <id property="orgId" column="org_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="orgName" column="org_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="org_status"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.suwell.plss.system.api.entity.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
        <result property="roleType" column="role_type"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id,
               u.org_id,
               u.user_name,
               u.nick_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.validity_period,
               u.wps_auth,
               u.default_tenant_id,
               u.classified,
               u.data_act_status,
               d.org_id,
               d.parent_id,
               d.ancestors,
               d.org_name,
               d.order_num,
               d.leader,
               d.status as org_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.role_type,
               r.status as role_status
        from sys_user u
                 left join sys_org d on u.org_id = d.org_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserPage" parameterType="com.suwell.plss.system.api.entity.SysUser"
            resultMap="SysUserResult">
        select u.user_id, u.org_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber,
        u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        u.order_num,u.update_time,u.src_id
        <!--        d.org_name, d.leader -->
        from sys_user u
        <!--        left join sys_org d on u.org_id = d.org_id-->
        where u.del_flag = '1'
        <if test="sysUser.userId != null and sysUser.userId != 0">
            AND u.user_id = #{sysUser.userId}
        </if>
        <if test="sysUser.userName != null and sysUser.userName != ''">
            AND u.user_name like concat('%', #{sysUser.userName}, '%')
        </if>
        <if test="sysUser.nickName != null and sysUser.nickName != ''">
            AND u.nick_name like concat('%', #{sysUser.nickName}, '%')
        </if>
        <if test="sysUser.status != null and sysUser.status != ''">
            AND u.status = #{sysUser.status}
        </if>
        <if test="sysUser.phonenumber != null and sysUser.phonenumber != ''">
            AND u.phonenumber like concat('%', #{sysUser.phonenumber}, '%')
        </if>
        <!-- 开始时间检索 -->
        <if test="sysUser.params.beginTime != null and sysUser.params.beginTime != ''">
            AND u.create_time &gt;= #{sysUser.beginTime}
        </if>
        <!-- 结束时间检索 -->
        <if test="sysUser.params.endTime != null and sysUser.params.endTime != ''">
            AND u.create_time &lt;= #{sysUser.endTime}
        </if>
        <if test="sysUser.orgIds != null and sysUser.orgIds.size() > 0">
            AND u.user_id in (select a.user_id from sys_user_org a where a.status = 1 and a.org_id
            in
            <foreach collection="sysUser.orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
            )
        </if>
        <if test="sysUser.orgId != null and sysUser.orgId != 0">
            AND (u.user_id IN (select suo.user_id from sys_user_org suo where suo.org_id in
            (
            SELECT t.org_id FROM sys_org t WHERE (
            ancestors like concat('%', #{sysUser.orgId}, '%')
            OR t.org_id = #{sysUser.orgId}
            )
            AND t.del_flag = '1'
            <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
                and t.tenant_id = #{sysUser.tenantId}
            </if>
            AND t.status = '1'
            )
            ))
        </if>
        <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
            AND (u.user_id IN (select suo.user_id from sys_user_org suo where
            suo.tenant_id = #{sysUser.tenantId}
            ))
        </if>
        <if test="queryType == 1">
            AND u.user_id > 10
        </if>
        <if test="queryType == 2">
            AND u.user_id > 10 AND u.classified  in (100,200)
        </if>
        <if test="queryType == 3">
            AND u.user_id > 10 AND u.classified not in (100,200)
        </if>
        <if test="sysUser.roleIdList != null and sysUser.roleIdList.size() > 0">
            AND u.user_id not in (select sur.user_id from sys_user_role sur where sur.status = 1 and sur.role_id
            in
            <foreach collection="sysUser.roleIdList" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
            <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
                AND sur.tenant_id = #{sysUser.tenantId}
            </if>
            )
        </if>
        order by u.order_num,u.update_time desc
    </select>

    <select id="selectUserList" parameterType="com.suwell.plss.system.api.entity.SysUser"
            resultMap="SysUserResult">
        select u.user_id, u.org_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber,
        u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        u.order_num,u.update_time
        <!--        d.org_name, d.leader -->
        from sys_user u
        <!--        left join sys_org d on u.org_id = d.org_id-->
        where u.del_flag = '1'
        <if test="sysUser.userId != null and sysUser.userId != 0">
            AND u.user_id = #{sysUser.userId}
        </if>
        <if test="sysUser.userName != null and sysUser.userName != ''">
            AND u.user_name like concat('%', #{sysUser.userName}, '%')
        </if>
        <if test="sysUser.nickName != null and sysUser.nickName != ''">
            AND u.nick_name like concat('%', #{sysUser.nickName}, '%')
        </if>
        <if test="sysUser.status != null and sysUser.status != ''">
            AND u.status = #{sysUser.status}
        </if>
        <if test="sysUser.phonenumber != null and sysUser.phonenumber != ''">
            AND u.phonenumber like concat('%', #{sysUser.phonenumber}, '%')
        </if>
        <!-- 开始时间检索 -->
        <if test="sysUser.params.beginTime != null and sysUser.params.beginTime != ''">
            AND u.create_time &gt;= #{sysUser.beginTime}
        </if>
        <!-- 结束时间检索 -->
        <if test="sysUser.params.endTime != null and sysUser.params.endTime != ''">
            AND u.create_time &lt;= #{sysUser.endTime}
        </if>
        <if test="sysUser.orgIds != null and sysUser.orgIds.size() > 0">
            AND u.user_id in (select a.user_id from sys_user_org a where a.status = 1 and a.org_id
            in
            <foreach collection="sysUser.orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
            )
        </if>
        <if test="sysUser.orgId != null and sysUser.orgId != 0">
            AND (u.user_id IN (select suo.user_id from sys_user_org suo where suo.org_id in
            (
            SELECT t.org_id FROM sys_org t WHERE (
            ancestors like concat('%', #{sysUser.orgId}, '%')
            OR t.org_id = #{sysUser.orgId}
            )
            AND t.del_flag = '1'
            <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
                and t.tenant_id = #{sysUser.tenantId}
            </if>
            AND t.status = '1'
            )
            ))
        </if>
        <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
            AND (u.user_id IN (select suo.user_id from sys_user_org suo where
            suo.tenant_id = #{sysUser.tenantId}
            ))
        </if>
        order by u.order_num,u.update_time desc
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '1'
    </select>

    <select id="selectUserById" parameterType="java.lang.String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, user_name
        from sys_user
        where user_name = #{userName}
          and del_flag = '1' limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, phonenumber
        from sys_user
        where phonenumber = #{phonenumber}
          and del_flag = '1' limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, email
        from sys_user
        where email = #{email}
          and del_flag = '1' limit 1
    </select>

    <select id="listUserByOrgIds" resultType="com.suwell.plss.system.api.entity.SysUser">
        SELECT
        a.user_id, a.user_name,
        a.nick_name, b.org_id
        FROM
        sys_user a
        JOIN sys_user_org b ON a.user_id = b.user_id
        <where>
            AND a.del_flag = '1' AND b.status = 1 AND a.status = '1'
            <if test="classifieds != null and classifieds.size() > 0">
                AND a.classified IN
                <foreach collection="classifieds" item="classified" open="(" separator=","
                        close=")">
                    #{classified}
                </foreach>
            </if>
            <if test="orgIds != null and orgIds.size() > 0">AND b.org_id IN
                <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listUserOrgForRepo" resultType="com.suwell.plss.system.api.entity.SysUser">
        SELECT
        a.user_id, a.nick_name,
        MAX(b.org_id) orgId, MAX(b.org_name) orgName
        FROM
        sys_user a
        JOIN sys_user_org c ON a.user_id = c.user_id
        JOIN sys_org b ON c.org_id = b.org_id
        <where>
            AND a.del_flag = '1' AND c.status = 1 AND a.status = '1'
            <if test="userIds != null and userIds.size() > 0">AND c.user_id IN
                <foreach collection="userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY a.user_id, a.nick_name
    </select>

    <insert id="insertUser" parameterType="com.suwell.plss.system.api.entity.SysUser"
            useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="orgId != null and orgId != 0">org_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="validityPeriod != null and validityPeriod != ''">validity_period,</if>
        <!--        <if test="tenantId != null and tenantId != 0">tenant_id,</if>-->
        <if test="orderNum != null and orderNum != 0">order_num,</if>
        <if test="wpsAuth != null and wpsAuth != 0">wps_auth,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="orgId != null and orgId != ''">#{orgId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="validityPeriod != null and validityPeriod != ''">#{validityPeriod},</if>
        <!--        <if test="tenantId != null and tenantId != ''">#{tenantId},</if>-->
        <if test="orderNum != null and orderNum != 0">#{orderNum},</if>
        <if test="wpsAuth != null and wpsAuth != 0">#{wpsAuth},</if>
        now()
        )
    </insert>

    <update id="updateUser" parameterType="com.suwell.plss.system.api.entity.SysUser">
        update sys_user
        <set>
            <if test="orgId != null and orgId != 0">org_id = #{orgId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="validityPeriod != null">validity_period=#{validityPeriod},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            update_time = now()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="com.suwell.plss.system.api.entity.SysUser">
        update sys_user
        set status = #{status}
        where user_id = #{userId}
    </update>

    <update id="updateUserAvatar" parameterType="com.suwell.plss.system.api.entity.SysUser">
        update sys_user
        set avatar = #{avatar}
        where user_name = #{userName}
    </update>

    <update id="resetUserPwd" parameterType="com.suwell.plss.system.api.entity.SysUser">
        update sys_user
        set password = #{password}
        where user_name = #{userName}
    </update>

    <delete id="deleteUserById" parameterType="java.lang.String">
        update sys_user
        set del_flag = '2'
        where user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="java.lang.String">
        update sys_user set del_flag = '2',update_time = now() where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper> 