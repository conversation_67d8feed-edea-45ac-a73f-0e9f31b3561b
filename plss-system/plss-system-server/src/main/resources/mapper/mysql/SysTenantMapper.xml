<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysTenantMapper">

    <resultMap type="com.suwell.plss.system.api.entity.SysTenant" id="sysTenantResultMap">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <!--        <result property="linkId" column="link_id"/>-->
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getSysTenantByUserId" resultMap="sysTenantResultMap">
        select id, name, link_id
        from sys_tenant st
                 right join sys_tenant_manager sm
                            on st.id = sm.tenant_id
        where st.del_flag = 1
          and st.status = 1
          and sm.user_id = #{userId}
        order by st.create_time desc
    </select>

    <select id="selectListByPage" resultType="com.suwell.plss.system.api.entity.SysTenant">
        select st.id,st.name,st.status,st.create_time as createTime,
        -- select st.id,st.name,st.status,st.link_id as linkId,st.create_time as createTime,
        array_to_string(array_agg(su.nick_name),',') as manangerNames
        from sys_tenant st
        left join sys_tenant_manager stm on st.id = stm.tenant_id
        left join sys_user su on stm.user_id = su.user_id
        <where>
            st.del_flag = 1
            <if test="sysTenantListReq.name != null and !''.equals(sysTenantListReq.name)">
                and st.name like concat('%', #{sysTenantListReq.name}, '%')
            </if>
            <if test="sysTenantListReq.status != null">
                and st.status = #{sysTenantListReq.status}
            </if>
            <if test="sysTenantListReq.managerName != null and !''.equals(sysTenantListReq.managerName)">
                and su.nick_name like concat('%', #{sysTenantListReq.managerName}, '%')
            </if>
            <if test="sysTenantListReq.beginTime != null and sysTenantListReq.endTime != null">
                and st.create_time between #{sysTenantListReq.beginTime} and
                #{sysTenantListReq.endTime}
            </if>
        </where>
        group by st.id
        order by st.create_time desc
    </select>

    <select id="listSysTenant" resultType="com.suwell.plss.system.api.entity.SysTenant">
        SELECT
        st.id,st.name,st.status,
        -- st.link_id as linkId,st.create_time as createTime
        st.create_time as createTime
        FROM sys_tenant st
        <where>
            st.del_flag = 1
            <if test="name != null and name != ''">
                and st.name like concat('%', #{name}, '%')
            </if>
            <if test="status != null">
                and st.status = #{status}
            </if>
            <if test="beginTime != null and endTime != null">
                and st.create_time between #{beginTime} and #{endTime}
            </if>
            <if test="managerName != null and managerName != ''">
                AND st.id IN (SELECT a.tenant_id
                FROM sys_tenant_manager a
                JOIN sys_user b on a.user_id = b.user_id
                WHERE b.nick_name LIKE CONCAT('%', #{managerName}, '%'))
            </if>
        </where>
    </select>

    <select id="listTenantAdmin" resultType="com.suwell.plss.system.api.entity.SysUser">
        SELECT
        a.user_id, a.user_name,
        a.nick_name, b.tenant_id
        FROM sys_user a
        JOIN sys_tenant_manager b ON a.user_id = b.user_id
        <where>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND b.tenant_id IN
                <foreach collection="tenantIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="tenantDetail" resultType="com.suwell.plss.system.dto.response.TenantDetailResp">
        SELECT
        a.id tenantId, MAX(a.name) tenantName,
        COUNT ( c.user_id ) accountNum
        FROM sys_tenant a
        LEFT JOIN sys_user_org b ON a.id = b.tenant_id
        JOIN sys_user c ON b.user_id = c.user_id
        WHERE
        a.del_flag = '1' AND b.status = 1
        AND c.status = '1' AND c.del_flag = '1'
        <if test="tenantId != null">  AND a.id = #{tenantId} </if>
        GROUP BY a.id
        ORDER BY a.id DESC
    </select>
</mapper>