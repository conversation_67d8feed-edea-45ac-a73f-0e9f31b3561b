<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysRemainMapper">

    <resultMap type="com.suwell.plss.system.api.entity.SysRemain" id="SysRemainResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="title" column="title"/>
        <result property="sendTime" column="send_time"/>
        <result property="sendPersonId" column="send_person_id"/>
        <result property="handlePersonId" column="handle_person_id"/>
        <result property="acceptPersonId" column="accept_person_id"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="businessId" column="business_id"/>
        <result property="noticeType" column="notice_type"/>
        <result property="repoName" column="repo_name"/>
        <result property="invalidStatus" column="invalid_status"/>
    </resultMap>

    <select id="getCenterList" resultMap="SysRemainResult">
        <if test='!req.noticeType.equals("1") and !req.noticeType.equals("2")'>
            (
        </if>
        <if test='!req.noticeType.equals("1")'>
            select id, send_person_id, title, type, content, record_id as business_id, '2' as notice_type, send_time,repo_name
            from sys_message sm
            where accept_person_id = #{req.userId}
            and del_flag = 1
        </if>
        <if test='!req.noticeType.equals("1") and !req.noticeType.equals("2")'>
            )union all(
        </if>
        <if test='!req.noticeType.equals("2")'>
            select id,send_person_id,title,type ,content,business_id,'1' as notice_type,send_time,'' as repo_name
            from sys_remain sr
            where accept_person_id like concat('%@',#{req.userId},'@%')
            AND status = '2'
            and del_flag = '1'
        </if>
        <if test='!req.noticeType.equals("1") and !req.noticeType.equals("2")'>
            )
        </if>
        order by send_time desc
    </select>
</mapper>