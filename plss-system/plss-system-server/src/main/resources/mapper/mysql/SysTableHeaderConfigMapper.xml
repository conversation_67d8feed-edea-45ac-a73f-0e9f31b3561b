<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.system.mapper.SysTableHeaderConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.system.entity.SysTableHeaderConfig" id="sysTableHeaderConfigMap">
        <result property="id" column="id"/>
        <result property="functionModule" column="function_module"/>
        <result property="tableType" column="table_type"/>
        <result property="tableNames" column="table_names"/>
        <result property="fieldCount" column="field_count"/>
        <result property="configType" column="config_type"/>
        <result property="repoId" column="repo_id"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="getFieldByTableName" resultType="com.suwell.plss.system.dto.response.TableTieldDto">
        select
            c.relname tableName, a.attname fieldName, d.description fieldDescription
        from pg_class c, pg_attribute a, pg_description d
        where a.attnum > 0 and a.attrelid = c.oid and d.objoid=a.attrelid and d.objsubid=a.attnum
          and c.relname=#{tableName} order by c.relname, a.attnum
    </select>

</mapper>