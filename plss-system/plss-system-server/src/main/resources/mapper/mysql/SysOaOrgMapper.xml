<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysOaOrgMapper">

    <select id="selectCompositeOrg" resultType="com.suwell.plss.system.api.domain.response.SrcTarOrgInfoResp">
        select so1.org_uid, so1.org_parent_uid, so1.details_json, so2.org_id as lid, so2.ancestors
        from sys_oa_org so1
        left join sys_org so2 on so1.org_uid = so2.src_id and so2.del_flag = '1'
        <where>
            and so1.deleted = 0
            <if test="operationStatus != null">
                and so1.operation_status = #{operationStatus}
            </if>
            <if test="uidList != null and uidList.size() > 0">
                and so1.org_uid in
                <foreach collection="uidList" item="uid" open="(" separator=","
                         close=")">
                    #{uid}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectCompositeOrgByOperation" resultType="com.suwell.plss.system.api.domain.response.SrcOrgUserInfoResp">
        select ou.user_uid, ou.details_json, u.user_id as lid
        from sys_oa_user ou
        left join sys_user u on ou.user_uid = u.src_id and u.del_flag = '1'
        <where>
            and ou.deleted = 0
            <if test="operationStatus != null">
                and ou.operation_status = #{operationStatus}
            </if>
        </where>
    </select>

    <select id="selectAlreadyDeletedOrg" resultType="java.lang.String">
        select a.org_uid
        from sys_oa_org a
        inner join (select org_uid, max(create_time) as max_create_time from sys_oa_org
        <if test="startTime != null">
            where create_time &gt;= #{startTime}
        </if>
        group by org_uid) b on a.org_uid = b.org_uid and a.create_time = b.max_create_time
        where a.operation_status = 1 and a.deleted = 1
    </select>
</mapper>