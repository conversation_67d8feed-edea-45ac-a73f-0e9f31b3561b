<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.XxlJobRegistryMapper">
	
	<resultMap id="XxlJobRegistry" type="com.suwell.plss.system.xxljob.model.XxlJobRegistry" >
		<result column="id" property="id" />
	    <result column="registry_group" property="registryGroup" />
	    <result column="registry_key" property="registryKey" />
	    <result column="registry_value" property="registryValue" />
		<result column="update_time" property="updateTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.registry_group,
		t.registry_key,
		t.registry_value,
		t.update_time
	</sql>

	<select id="findDead" resultType="java.lang.Long">
		SELECT t.id
		FROM xxl_job_registry AS t
		WHERE t.update_time <![CDATA[ < ]]> #{outTime}
	</select>

	<update id="registryUpdate" >
		UPDATE xxl_job_registry
		SET update_time = #{updateTime}
		WHERE registry_group = #{registryGroup}
		  AND registry_key = #{registryKey}
		  AND registry_value = #{registryValue}
	</update>

	<delete id="registryDelete" >
		DELETE FROM xxl_job_registry
		WHERE registry_group = #{registryGroup}
		  AND registry_key = #{registryKey}
		  AND registry_value = #{registryValue}
	</delete>

</mapper>