<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysPageModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.system.entity.SysPageModel">
        <id column="id" property="id" />
        <result column="model_name" property="modelName" />
        <result column="model_url" property="modelUrl" />
        <result column="model_type" property="modelType" />
        <result column="model_description" property="modelDescription" />
        <result column="model_config" property="modelConfig" />
        <result column="is_enable" property="isEnable" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_name, model_url, model_type, model_description, model_config, is_enable,tenant_id, create_time, create_by, update_time, update_by
    </sql>

</mapper>
