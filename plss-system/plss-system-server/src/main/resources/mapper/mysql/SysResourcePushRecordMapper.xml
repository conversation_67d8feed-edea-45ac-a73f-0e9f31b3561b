<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysResourcePushRecordMapper">

    <select id="selectUserSyncSend" resultType="com.suwell.plss.system.api.domain.response.UserInfoPushResp">
        select su.user_id,
        su.user_name,
        su.nick_name,
        su.email,
        su.phonenumber,
        su.sex,
        su.status,
        su.del_flag,
        su.update_time,
        pr.business_data_id,
        pr.business_data_update_time
        from sys_user su
        inner join (select user_id
        from sys_user_org
        where tenant_id = #{tenantId}
        <if test="limitOrgIds != null and limitOrgIds.size() > 0">
            and org_id in
            <foreach collection="limitOrgIds" item="orgId" open="(" separator=","
                     close=")">
                #{orgId}
            </foreach>
        </if>
        group by user_id) as suo on su.user_id = suo.user_id
        left join sys_resource_push_record pr
        on su.user_id = pr.business_data_id and pr.business_type = 'user'
        <if test="manufacturer != null and manufacturer != ''">
            and pr.manufacturer_code = #{manufacturer}
        </if>
    </select>

    <select id="selectOrgSyncSend" resultType="com.suwell.plss.system.api.domain.response.OrgInfoPushResp">
        select so.*, pr.business_data_id, pr.business_data_update_time
        from (select org_id,
        parent_id,
        org_name,
        org_code,
        order_num,
        phone,
        email,
        status,
        del_flag,
        update_time
        from sys_org
        where tenant_id = #{tenantId}
        <if test="ancestors != null and ancestors != ''">
            and ancestors like CONCAT(#{ancestors}, '%')
        </if>
        ) so
        left join sys_resource_push_record pr
        on so.org_id = pr.business_data_id and pr.business_type = 'org'
        <if test="manufacturer != null and manufacturer != ''">
            and pr."manufacturer_code" = #{manufacturer}
        </if>
    </select>
</mapper>