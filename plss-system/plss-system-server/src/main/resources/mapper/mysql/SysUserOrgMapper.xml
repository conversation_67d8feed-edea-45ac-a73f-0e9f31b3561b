<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysUserOrgMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.system.entity.SysUserOrg">
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_Id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="orderBy" column="order_by" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectUserOrgList" parameterType="com.suwell.plss.system.api.entity.SysUser">
        SELECT * FROM sys_user_org where user_id  in (
            SELECT u.user_id FROM  sys_user u
            <!--        left join sys_org d on u.org_id = d.org_id-->
            where u.del_flag = '1'
            <if test="sysUser.userId != null and sysUser.userId != ''">
                AND u.user_id = #{sysUser.userId}
            </if>
            <if test="sysUser.userName != null and sysUser.userName != ''">
                AND u.user_name like concat('%', #{sysUser.userName}, '%')
            </if>
            <if test="sysUser.nickName != null and sysUser.nickName != ''">
                AND u.nick_name like concat('%', #{sysUser.nickName}, '%')
            </if>
            <if test="sysUser.status != null and sysUser.status != ''">
                AND u.status = #{sysUser.status}
            </if>
            <if test="sysUser.phonenumber != null and sysUser.phonenumber != ''">
                AND u.phonenumber like concat('%', #{sysUser.phonenumber}, '%')
            </if>
            <!-- 开始时间检索 -->
            <if test="sysUser.params.beginTime != null and sysUser.params.beginTime != ''">
                AND u.create_time &gt;= #{sysUser.beginTime}
            </if>
            <!-- 结束时间检索 -->
            <if test="sysUser.params.endTime != null and sysUser.params.endTime != ''">
                AND u.create_time &lt;= #{sysUser.endTime}
            </if>
            <if test="sysUser.orgIds != null and sysUser.orgIds.size() > 0">
                AND u.user_id in (select a.user_id from sys_user_org a where a.status = 1 and a.org_id
                in
                <foreach collection="sysUser.orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
                )
            </if>
            <if test="sysUser.orgId != null and sysUser.orgId != ''">
                AND (u.user_id IN (
                select suo.user_id from sys_user_org suo where suo.org_id in(
                SELECT t.org_id FROM sys_org t WHERE (ancestors like concat('%', #{sysUser.orgId}, '%')
                OR t.org_id = #{sysUser.orgId})
                <if test="sysUser.tenantId != null and sysUser.tenantId != ''">
                    and t.tenant_id = #{sysUser.tenantId}
                </if>
                AND t.status = '1'
                AND t.del_flag = '1')
                ))
            </if>
            <if test="sysUser.tenantId != null and sysUser.tenantId != 0">
                AND (u.user_id IN (select suo.user_id from sys_user_org suo where
                suo.tenant_id = #{sysUser.tenantId}
                ))
            </if>
       )
    </select>

    <select id="selectTenantUserId">
        select su.user_id from sys_user  su, sys_user_org suo,sys_user_org suo1
        where su.user_id = suo.user_id and suo.tenant_id = suo1.tenant_id
        <if test="userId != null and userId != 1">
            and suo1.user_id = #{userId}
        </if>
    </select>

    <select id="selectTenantUserIdCount">
        select count(su.user_id) from sys_user  su, sys_user_org suo,sys_user_org suo1
        where su.user_id = suo.user_id and suo.tenant_id = suo1.tenant_id
        <if test="userId != null and userId != 1">
            and suo1.user_id = #{userId}
        </if>
    </select>

    <select id="selectUserIdByTenant">
        select suo.user_id
        from sys_user_org suo
        left join sys_user su on suo.user_id = su.user_id
        where suo.tenant_id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        and su.del_flag = '1' and suo.status = 1
    </select>
</mapper> 