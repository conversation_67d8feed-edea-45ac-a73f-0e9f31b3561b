<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysCategoryMapper">

    <select id="findOneLevelByName" resultType="java.lang.Long">
        select count(1) as num
        from sys_category where status = '1' and name = #{name}
                            and id in (
                select descendant_id from sys_category_relation where ancestor_id = 0 and distance = 1
            )
    </select>
</mapper>
