<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysCollectMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.system.entity.SysCollect">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="recordId" column="record_id" jdbcType="BIGINT"/>
            <result property="recordName" column="record_name" jdbcType="VARCHAR"/>
            <result property="collectTime" column="collect_time" jdbcType="TIMESTAMP"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="userid" column="userId" jdbcType="BIGINT"/>
            <result property="folder" column="folder" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_id,record_name,
        collect_time,url,userId,
        folder
    </sql>
</mapper>
