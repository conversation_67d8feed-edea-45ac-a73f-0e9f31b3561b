<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysRoleOrgMapper">

	<resultMap type="com.suwell.plss.system.entity.SysRoleOrg" id="SysRoleOrgResult">
		<result property="roleId"     column="role_id"      />
		<result property="orgId"     column="org_id"      />
	</resultMap>

	<delete id="deleteRoleOrgByRoleId" parameterType="java.lang.String">
		delete from sys_role_org where role_id=#{roleId}
	</delete>
	
	<select id="selectCountRoleOrgByOrgId" resultType="Integer">
	    select count(1) from sys_role_org where org_id=#{orgId}
	</select>
	
	<delete id="deleteRoleOrg" parameterType="java.lang.String">
 		delete from sys_role_org where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach> 
 	</delete>
	
	<insert id="batchRoleOrg">
		insert into sys_role_org(role_id, org_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.orgId})
		</foreach>
	</insert>
	
</mapper> 