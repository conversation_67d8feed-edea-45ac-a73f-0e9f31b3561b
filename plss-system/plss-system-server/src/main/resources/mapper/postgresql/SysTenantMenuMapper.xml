<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysTenantMenuMapper">

	<resultMap type="com.suwell.plss.system.entity.SysTenantMenu" id="tenantMenuResultMap">
		<result property="tenantId"     column="tenant_id"      />
		<result property="menuId"     column="menu_id"      />
	</resultMap>

	
	<insert id="batchInsertTenantMenu">
		insert into sys_tenant_menu(tenant_id, menu_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.tenantId},#{item.menuId})
		</foreach>
	</insert>

</mapper> 