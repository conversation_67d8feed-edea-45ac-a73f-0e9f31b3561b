<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysPageModuleDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.system.entity.SysPageModuleData">
        <id column="id" property="id" />
        <result column="model_id" property="modelId" />
        <result column="module_code" property="moduleCode" />
        <result column="entry_module_code" property="entryModuleCode" />
        <result column="module_type" property="moduleType" />
        <result column="data_type" property="dataType" />
        <result column="data_name" property="dataName" />
        <result column="data_config" property="dataConfig" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_id, module_code,entry_module_code, module_type,data_type,data_name,data_config, create_time, create_by, update_time, update_by
    </sql>

    <insert id="insertList">
        <if test="list != null and list.size() &gt; 0">
            INSERT INTO sys_page_module_data(id,model_id, module_code,entry_module_code, module_type, data_type,data_name,data_config, create_time, create_by, update_time, update_by)
            <foreach collection="list" index="index" item="item" open="VALUES" close="" separator=",">
                (#{item.id},#{item.modelId},#{item.moduleCode},#{item.entryModuleCode},#{item.moduleType},#{item.dataType},#{item.dataName},
                #{item.dataConfig},#{item.createTime},#{item.createBy},#{item.updateTime},#{item.updateBy})
            </foreach>
        </if>
    </insert>
</mapper>
