<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysOrgMapper">

    <resultMap type="com.suwell.plss.system.api.entity.SysOrg" id="SysOrgResult">
        <id property="orgId" column="org_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="orgName" column="org_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectOrgVo">
        select d.org_id,
               d.parent_id,
               d.ancestors,
               d.org_name,
               d.order_num,
               d.leader,
               d.phone,
               d.email,
               d.status,
               d.del_flag,
               d.create_by,
               d.create_time
        from sys_org d
    </sql>

    <select id="selectOrgList" parameterType="com.suwell.plss.system.api.entity.SysOrg" resultMap="SysOrgResult">
        <include refid="selectOrgVo"/>
        where d.del_flag = '1'
        <if test="orgId != null and orgId != 0">
            AND org_id = #{orgId}
        </if>
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="orgName != null and orgName != ''">
            AND org_name like concat('%', #{orgName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectOrgListByRoleId" resultType="java.lang.String">
        select d.org_id
        from sys_org d
        left join sys_role_org rd on d.org_id = rd.org_id
        where rd.role_id = #{roleId}
        and rd.tenant_id = #{tenantId}
        <if test="orgCheckStrictly">
            and d.org_id not in (select d.parent_id from sys_org d inner join sys_role_org rd on
            d.org_id = rd.org_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectOrgById" parameterType="java.lang.String" resultMap="SysOrgResult">
        <include refid="selectOrgVo"/>
        where org_id = #{orgId}
    </select>

    <select id="checkOrgExistUser" parameterType="java.lang.String" resultType="int">
        select count(1)
        from sys_user
        where org_id = #{orgId}
          and del_flag = '1'
    </select>

    <select id="hasChildByOrgId" parameterType="java.lang.String" resultType="int">
        select count(1)
        from sys_org
        where del_flag = '1'
            and tenant_id = #{tenantId}
          and parent_id = #{orgId} limit 1
    </select>

    <select id="selectChildrenOrgById" parameterType="java.lang.String" resultMap="SysOrgResult">
        select *
        from sys_org
        where ancestors like concat('%', #{orgId}, '%')
    </select>

    <select id="selectNormalChildrenOrgById" parameterType="java.lang.String" resultType="int">
        select count(*)
        from sys_org
        where status = '1'
          and del_flag = '1'
          and tenant_id = #{tenantId}
          and ancestors like concat('%', #{orgId}, '%')
    </select>

    <select id="checkOrgNameUnique" resultMap="SysOrgResult">
        <include refid="selectOrgVo"/>
        where org_name=#{orgName} and parent_id = #{parentId} and del_flag = '1' limit 1
    </select>

    <select id="checkBatchOrgNameUnique" resultMap="SysOrgResult">
        <include refid="selectOrgVo"/>
        where del_flag = '1'
        <if test="orgCheckList != null and orgCheckList.size() > 0">
            and
            <foreach collection="orgCheckList" item="orgCheck" open="(" separator=" or"
                     close=")">
                (parent_id = #{orgCheck.parentId} and org_name in
                <foreach collection="orgCheck.orgNames" item="name" open="(" separator=","
                         close=")">
                    #{name}
                </foreach>
                )
            </foreach>
        </if>
        <if test="exclusionSrcIds != null and exclusionSrcIds.size() > 0">
            and d.src_id not in
            <foreach collection="exclusionSrcIds" item="srcId" open="(" separator="," close=")">
                #{srcId}
            </foreach>
        </if>
    </select>

    <insert id="insertOrg" parameterType="com.suwell.plss.system.api.entity.SysOrg">
        insert into sys_org(
        <if test="orgId != null and orgId != 0">org_id,</if>
        <if test="parentId != null and parentId != 0">parent_id,</if>
        <if test="orgName != null and orgName != ''">org_name,</if>
        <if test="ancestors != null and ancestors != ''">ancestors,</if>
        <if test="orderNum != null">order_num,</if>
        <if test="leader != null and leader != ''">leader,</if>
        <if test="phone != null and phone != ''">phone,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="status != null">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="orgId != null and orgId != 0">#{orgId},</if>
        <if test="parentId != null and parentId != 0">#{parentId},</if>
        <if test="orgName != null and orgName != ''">#{orgName},</if>
        <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        <if test="orderNum != null">#{orderNum},</if>
        <if test="leader != null and leader != ''">#{leader},</if>
        <if test="phone != null and phone != ''">#{phone},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="status != null">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        now()
        )
    </insert>

    <update id="updateOrg" parameterType="com.suwell.plss.system.api.entity.SysOrg">
        update sys_org
        <set>
            <if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
            <if test="orgName != null and orgName != ''">org_name = #{orgName},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where org_id = #{orgId}
    </update>

    <update id="updateOrgChildren" parameterType="java.util.List">
        update sys_org set ancestors =
        <foreach collection="orgs" item="item" index="index"
                separator=" " open="case org_id" close="end">
            when #{item.orgId} then #{item.ancestors}
        </foreach>
        where org_id in
        <foreach collection="orgs" item="item" index="index"
                separator="," open="(" close=")">
            #{item.orgId}
        </foreach>
    </update>

    <update id="updateOrgStatusNormal" parameterType="java.lang.String">
        update sys_org set status = '1' where org_id in
        <foreach collection="array" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </update>

    <delete id="deleteOrgById" parameterType="java.lang.String">
        update sys_org
        set del_flag = '2'
        where org_id = #{orgId}
    </delete>

    <select id="selectChildrenById" parameterType="java.lang.String" resultType="java.lang.String">
        select org_id
        from sys_org
        where status = '1'
          and del_flag = '1'
          and ancestors like concat('%', #{orgId}, '%')
    </select>

    <select id="getAllLevel" resultType="java.lang.String">
        select org_level from sys_org where where status = '1' and del_flag = '1' distinct
    </select>

    <select id="queryListByLevels" resultType="com.suwell.plss.system.api.entity.SysOrg">
        SELECT
            *
        FROM sys_org
        <where>
            status = '1' AND del_flag = '1'
            <if test="levels != null and levels.size() > 0">AND org_level IN
                <foreach collection="levels" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
        </where>
    </select>

    <select id="queryTopLevelOrgList" resultType="com.suwell.plss.system.api.entity.SysOrg">
        SELECT
            *
        FROM sys_org
        <where>
            status = '1' AND del_flag = '1'
            <if test="tenantIds != null and tenantIds.size() > 0">AND tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">AND owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            AND org_level IN (
            SELECT MIN(org_level) FROM sys_org WHERE status = '1' AND del_flag = '1'
            <if test="tenantIds != null and tenantIds.size() > 0">AND tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">AND owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            )
        </where>
    </select>

    <select id="findChildrenOrgCount" resultType="com.suwell.plss.system.dto.response.OrgUserTreeResp">
        SELECT
            a.parent_id AS leafId,COUNT(*) AS childrenCount
        FROM sys_org a
        <where>
            a.status = '1' AND a.del_flag = '1'
            <if test="orgIds != null and orgIds.size() > 0">
                AND a.parent_id IN
                <foreach collection="orgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">
                AND a.owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND a.tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
        GROUP BY a.parent_id
    </select>

    <select id="findChildrenUserCount" resultType="com.suwell.plss.system.dto.response.OrgUserTreeResp">
        SELECT
            a.org_id AS leafId,COUNT(*) AS childrenCount
        FROM sys_org a
        LEFT JOIN sys_user_org b ON a.org_id = b.org_id
        <where>
            a.status = '1' AND a.del_flag = '1' AND b.status = '1'
            <if test="orgIds != null and orgIds.size() > 0">
                AND a.org_id IN
                <foreach collection="orgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">
                AND a.owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND a.tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
        GROUP BY a.org_id
    </select>

    <select id="searchOrgUserList" resultType="com.suwell.plss.system.dto.response.OrgUserTreePathResp">
        SELECT
        a.org_id leafId,a.org_name leafName,
        a.org_id, a.ancestors AS orgPath, 1 AS leafType
        FROM sys_org a
        <where>
            a.status = '1' AND a.del_flag = '1'
            <if test="name != null and name != ''">
                AND a.org_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">
                AND a.owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND a.tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
        ORDER BY a.org_id LIMIT 500
    </select>

    <select id="searchUserList" resultType="com.suwell.plss.system.dto.response.OrgUserTreePathResp">
        SELECT
            c.user_id leafId,c.nick_name leafName,
            a.org_id, a.ancestors AS orgPath, 2 AS leafType
        FROM sys_org a
        LEFT JOIN sys_user_org b ON a.org_id = b.org_id
        LEFT JOIN sys_user c ON b.user_id = c.user_id
        <where>
            a.status = '1' AND a.del_flag = '1' AND b.status = '1' AND c.status = '1' AND c.del_flag = '1'
            <if test="name != null and name != ''">
                AND c.nick_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="ownerOrgIds != null and ownerOrgIds.size() > 0">
                AND a.owner_org_id IN
                <foreach collection="ownerOrgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            <if test="tenantIds != null and tenantIds.size() > 0">
                AND a.tenant_id IN
                <foreach collection="tenantIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
        ORDER BY c.user_id LIMIT 500
    </select>

    <select id="listUserOrg"
            resultType="com.suwell.plss.system.api.domain.response.UserOrgResp">
        SELECT
        b.user_id, a.org_id, a.org_name
        FROM sys_org a
        JOIN sys_user_org b ON a.org_id = b.org_id
        <where>
            a.status = '1' AND a.del_flag = '1' AND b.status = 1
            <if test="userIds != null and userIds.size() > 0">
                AND b.user_id IN
                <foreach collection="userIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
    </select>

</mapper>