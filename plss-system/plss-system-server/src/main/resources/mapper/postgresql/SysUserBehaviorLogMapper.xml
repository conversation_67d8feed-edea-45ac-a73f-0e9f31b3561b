<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.system.mapper.SysUserBehaviorLogMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.system.entity.SysUserBehaviorLog" id="sysUserBehaviorLogMap">
        <result property="id" column="id"/>
        <result property="projectName" column="project_name"/>
        <result property="gwkVersion" column="gwk_version"/>
        <result property="eventName" column="event_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="model" column="model"/>
        <result property="os" column="os"/>
        <result property="osVersion" column="os_version"/>
        <result property="browser" column="browser"/>
        <result property="browserVersion" column="browser_version"/>
        <result property="screenHeight" column="screen_height"/>
        <result property="screenWidth" column="screen_width"/>
        <result property="ip" column="ip"/>
        <result property="data" column="data"/>
        <result property="createYear" column="create_year"/>
        <result property="createMonth" column="create_month"/>
        <result property="createDay" column="create_day"/>
        <result property="createHour" column="create_hour"/>
        <result property="createMinute" column="create_minute"/>
        <result property="createSecond" column="create_second"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <!-- 注意：此操作是DDL，应谨慎使用 -->
    <update id="createTableIfNotExists">
        CREATE TABLE IF NOT EXISTS ${tableName}
        (
            "id"
            INT8
            NOT
            NULL,
            "project_name"
            VARCHAR
        (
            64
        ) NOT NULL,
            "gwk_version" VARCHAR
        (
            64
        ) NOT NULL,
            "event_name" VARCHAR
        (
            256
        ) NOT NULL,
            "user_id" INT8 NOT NULL,
            "user_name" VARCHAR
        (
            64
        ) NOT NULL,
            "model" VARCHAR
        (
            64
        ) ,
            "os" VARCHAR
        (
            64
        ) ,
            "os_version" VARCHAR
        (
            64
        ) ,
            "browser" VARCHAR
        (
            64
        ) ,
            "browser_version" VARCHAR
        (
            64
        ) ,
            "screen_height" INT4 NOT NULL,
            "screen_width" INT4 NOT NULL,
            "ip" VARCHAR
        (
            64
        ) NOT NULL,
            "data" TEXT NOT NULL,
            "create_year" INT4 NOT NULL,
            "create_month" INT4 NOT NULL,
            "create_day" INT4 NOT NULL,
            "create_hour" INT4 NOT NULL,
            "create_minute" INT4 NOT NULL,
            "create_second" INT4 NOT NULL,
            "create_time" TIMESTAMP
        (
            6
        ) NOT NULL,
            "status" INT4 NOT NULL DEFAULT 1,
            "del_flag" INT4 NOT NULL DEFAULT 1,
            "remark" VARCHAR
        (
            512
        ),
            CONSTRAINT "${tableName}_pkey" PRIMARY KEY
        (
            "id"
        )
            )
    </update>
    <select id="findUserIdList" resultType="java.lang.String">
<!--        select distinct user_id from (-->
        select distinct user_id from sys_user_behavior_log
        <where>
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.userIdList)">
                and user_id in <foreach collection="dto.userIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
            </if>
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.eventNameList)">
                and event_name in <foreach collection="dto.eventNameList" item="event_name" open="(" separator="," close=")">#{event_name}</foreach>
            </if>
            <if test="dto.beginTime != null">
                and create_time &gt;= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                and create_time &lt; #{dto.endTime}
            </if>
        </where>
<!--        ) as t where user_id > 0-->
    </select>
    <select id="findUserIdPage" resultType="java.lang.String">
        select distinct user_id from (
        select distinct user_id from sys_user_behavior_log
        <where>
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.userIdList)">
                and user_id in <foreach collection="dto.userIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
            </if>
            <if test="dto.beginTime != null">
                and create_time &gt;= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                and create_time &lt; #{dto.endTime}
            </if>
        </where>
        ) as t where user_id > 0
    </select>

</mapper>