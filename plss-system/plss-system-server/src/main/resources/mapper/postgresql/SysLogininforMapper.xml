<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysLogininforMapper">
    <resultMap type="com.suwell.plss.system.api.entity.SysLogininfor" id="SysLogininforResult">
        <id property="infoId" column="info_id"/>
        <result property="userName" column="user_name"/>
        <result property="status" column="status"/>
        <result property="ipaddr" column="ipaddr"/>
        <result property="msg" column="msg"/>
        <result property="os" column="os"/>
        <result property="accessTime" column="access_time"/>
        <result property="signature" column="signature"/>
        <result property="checkSign" column="check_sign"/>
    </resultMap>

    <insert id="insertLogininfor" parameterType="com.suwell.plss.system.api.entity.SysLogininfor">
        insert into sys_logininfor (info_id, user_name, status, ipaddr, msg,os,signature)
        values (#{infoId}, #{userName}, #{status}, #{ipaddr}, #{msg},#{os},#{signature})
    </insert>

    <select id="selectLogininforPage"
            parameterType="com.suwell.plss.system.api.entity.SysLogininfor"
            resultMap="SysLogininforResult">
        select info_id, user_name, ipaddr, status, msg, access_time,user_id,os,check_sign from sys_logininfor
        <where>
            <if test="logininfor.ipaddr != null and logininfor.ipaddr != ''">
                AND ipaddr like concat('%', #{logininfor.ipaddr}, '%')
            </if>
            <if test="logininfor.status != null and logininfor.status != ''">
                AND status = #{logininfor.status}
            </if>
            <if test="logininfor.userName != null and logininfor.userName != ''">
                AND user_name like concat('%', #{logininfor.userName}, '%')
            </if>
            <if test="logininfor.beginTime != null ">
                <!-- 开始时间检索 -->
                AND access_time &gt;= #{logininfor.beginTime}
            </if>
            <if test="logininfor.endTime != null ">
                <!-- 结束时间检索 -->
                AND access_time &lt;= #{logininfor.endTime}
            </if>
            <if test="logininfor.roleTypeList != null and logininfor.roleTypeList.size() > 0">
                and
                <foreach collection="logininfor.roleTypeList" item="item" open="(" close=")" separator="or">
                    role_type like concat('%', #{item}, '%')
                </foreach>
            </if>
            <if test='!"1".equals(logininfor.loginUserId)'>
                AND user_name in (
                select user_name from sys_user where user_id in (
                select user_id from sys_user_org where tenant_id in (
                select tenant_id from sys_user_org where user_id = #{logininfor.loginUserId} and tenant_id !='0'
                )
                )
                )
            </if>
        </where>
        <if test='"1".equals(logininfor.order)'>
            order by access_time asc
        </if>
        <if test='!"1".equals(logininfor.order)'>
            order by access_time desc
        </if>
    </select>

    <select id="selectLogininforList"
            parameterType="com.suwell.plss.system.api.entity.SysLogininfor"
            resultMap="SysLogininforResult">
        select info_id, user_name, ipaddr, status, msg, access_time,user_id,os,check_sign from sys_logininfor
        <where>
            <if test="logininfor.ipaddr != null and logininfor.ipaddr != ''">
                AND ipaddr like concat('%', #{logininfor.ipaddr}, '%')
            </if>
            <if test="logininfor.status != null and logininfor.status != ''">
                AND status = #{logininfor.status}
            </if>
            <if test="logininfor.userName != null and logininfor.userName != ''">
                AND user_name like concat('%', #{logininfor.userName}, '%')
            </if>
            <if test="logininfor.beginTime != null">
                <!-- 开始时间检索 -->
                AND access_time &gt;= #{logininfor.beginTime}
            </if>
            <if test="logininfor.endTime != null ">
                <!-- 结束时间检索 -->
                AND access_time &lt;= #{logininfor.endTime}
            </if>
            <if test="logininfor.roleTypeList != null and logininfor.roleTypeList.size() > 0">
                and
                <foreach collection="logininfor.roleTypeList" item="item" open="(" close=")" separator="or">
                    role_type like concat('%', #{item}, '%')
                </foreach>
            </if>
            <if test='!"1".equals(logininfor.loginUserId)'>
                AND user_name in (
                select user_name from sys_user where user_id in (
                    select user_id from sys_user_org where tenant_id in (
                        select tenant_id from sys_user_org where user_id = #{logininfor.loginUserId} and tenant_id != '0'
                )
                )
                )
            </if>
        </where>
        order by info_id desc
    </select>

    <delete id="deleteLogininforByIds" >
        delete from sys_logininfor where info_id in
        <foreach collection="array" item="infoId" open="(" separator="," close=")">
            #{infoId}
        </foreach>
    </delete>

    <update id="cleanLogininfor">
        truncate table sys_logininfor
    </update>

    <select id="countByDate"  resultType="com.suwell.plss.system.dto.OptTrendDTO">
        select to_char(access_time,'YYYY-MM-DD') as day_date,count(1) as day_count from sys_logininfor
        where
        access_time &gt;= #{stm}
        AND access_time &lt;= #{etm}
        AND status = '1'
        <if test="userIdList != null and userIdList.size() > 0">
            and user_id in
            <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        group by to_char(access_time,'YYYY-MM-DD')
    </select>

    <select id="countByDateAndTenant" resultType="com.suwell.plss.system.dto.OptTrendDTO">
        select to_char(access_time,'YYYY-MM-DD') as day_date,count(1) as day_count from sys_logininfor
        where
        access_time &gt;= #{stm}
        AND access_time &lt;= #{etm}
        AND status = '1'
        and user_id in (
        select suo.user_id
        from sys_user_org suo
        left join sys_user su on suo.user_id = su.user_id
        where suo.tenant_id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        and su.del_flag = '1' and suo.status = 1
        )
        group by to_char(access_time,'YYYY-MM-DD')
    </select>
    <select id="countByDateAndTenantId" resultType="com.suwell.plss.system.dto.OptTrendDTO">
        select to_char(access_time,'YYYY-MM-DD') as day_date,count(1) as day_count from sys_logininfor
        where
        access_time &gt;= #{stm}
        AND access_time &lt;= #{etm}
        AND status = '1'
        and tenant_id = #{tenantId}
        group by to_char(access_time,'YYYY-MM-DD')
    </select>

    <select id="userLivenessStatistics" resultType="com.suwell.plss.system.dto.UserLivenessStatisticsOutDto">
        <foreach collection="dto" item="d" separator=" union all ">
            select #{d.monthStr} as monthStr,count(info_id) as num
            from sys_logininfor
            where access_time &gt;= #{d.beginTime} and access_time &lt; #{d.endTime}
            and msg != '退出成功'
        </foreach>
    </select>
</mapper>