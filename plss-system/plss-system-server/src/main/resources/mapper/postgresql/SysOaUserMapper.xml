<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysOaUserMapper">

    <select id="selectCompositeUser" resultType="com.suwell.plss.system.api.domain.response.SrcOrgUserInfoResp">
        select u1.user_uid, u1.details_json, u2.user_id as lid
        from sys_oa_user u1
        left join sys_user u2 on u1.user_uid = u2.src_id and u2.del_flag = '1'
        <where>
            and u1.deleted = 0
            <if test="operationStatus != null">
                and u1.operation_status = #{operationStatus}
            </if>
            <if test="uidList != null and uidList.size() > 0">
                and u1.user_uid in
                <foreach collection="uidList" item="uid" open="(" separator=","
                         close=")">
                    #{uid}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAlreadyDeletedUser" resultType="java.lang.String">
        select a.user_uid
        from sys_oa_user a
        inner join (select user_uid, max(create_time) as max_create_time from sys_oa_user
        <if test="startTime != null">
            where create_time &gt;= #{startTime}
        </if>
        group by user_uid) b on a.user_uid = b.user_uid and a.create_time = b.max_create_time
        where a.operation_status = 1 and a.deleted = 1
    </select>
</mapper>