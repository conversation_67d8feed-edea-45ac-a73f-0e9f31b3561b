<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SysCategoryRelationMapper">

    <insert id="insertCategoryRelation">
        INSERT INTO sys_category_relation (ancestor_id, descendant_id, distance)
        SELECT t.ancestor_id, #{descendantId}, t.distance + 1
        FROM sys_category_relation AS t
        WHERE t.descendant_id = #{ancestorId}
        UNION ALL
        SELECT #{ancestorId}, #{descendantId}, 1
    </insert>

    <select id="getBrotherId" resultType="long">
        select p.descendant_id
        from sys_category_relation AS p
                 inner join sys_category_relation AS s on p.ancestor_id = s.ancestor_id
        where p.distance = 1
          and s.descendant_id = #{descendantId}
          and s.distance = 1
          and p.descendant_id != #{descendantId}
    </select>
</mapper>
