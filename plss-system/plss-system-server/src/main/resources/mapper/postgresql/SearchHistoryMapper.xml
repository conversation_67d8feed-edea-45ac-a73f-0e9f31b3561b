<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.system.mapper.SearchHistoryMapper">

    <select id="querySearchHistoryAccessTrend"
            resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(create_time, 'YYYY-MM-DD') AS dayDate,
        COUNT(*) AS dayCount
        FROM sys_search_history
        WHERE 1 = 1
        <if test="stm != null">
            <![CDATA[ AND create_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND create_time < #{etm} ]]>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND create_by IN
            <foreach collection="userIds" item="item" open="(" close=")" separator=",">#{item}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(create_time, 'YYYY-MM-DD')
        ORDER BY dayDate;
    </select>

    <select id="querySearchHistoryAccessTrendByTenantId"
            resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(create_time, 'YYYY-MM-DD') AS dayDate,
        COUNT(*) AS dayCount
        FROM sys_search_history
        WHERE 1 = 1
        <if test="stm != null">
            <![CDATA[ AND create_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND create_time < #{etm} ]]>
        </if>
        AND create_by IN
        (
        select suo.user_id
        from sys_user_org suo
        left join sys_user su on suo.user_id = su.user_id
        where suo.tenant_id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        and su.del_flag = '1' and suo.status = 1
        )
        GROUP BY
        TO_CHAR(create_time, 'YYYY-MM-DD')
        ORDER BY dayDate;
    </select>
    <select id="querySearchHistoryAccessTrendById"
            resultType="com.suwell.plss.system.dto.OptTrendDTO">
        SELECT
        TO_CHAR(create_time, 'YYYY-MM-DD') AS dayDate,
        COUNT(*) AS dayCount
        FROM sys_search_history
        WHERE 1 = 1
        <if test="stm != null">
            <![CDATA[ AND create_time >= #{stm} ]]>
        </if>
        <if test="etm != null">
            <![CDATA[ AND create_time < #{etm} ]]>
        </if>
        AND tenant_id = #{tenantId}
        GROUP BY
        TO_CHAR(create_time, 'YYYY-MM-DD')
        ORDER BY dayDate;
    </select>

</mapper>