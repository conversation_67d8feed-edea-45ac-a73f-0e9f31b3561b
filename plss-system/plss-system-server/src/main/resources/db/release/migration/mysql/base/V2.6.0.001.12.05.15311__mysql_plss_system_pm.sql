-- 添加全局水印菜单
INSERT INTO `plss_system`.`sys_menu` (`menu_id`, `parent_id`, `menu_name`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `system_menu_flag`) VALUES (344471057539845, 1, '全局水印', 7, 'watermark', 'system/watermark/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2024-11-29 16:28:09.645006', '', '2024-11-29 16:28:09.645006', '', 2);

-- 菜单添加给所有租户
INSERT INTO `plss_system`.`sys_tenant_menu` (`tenant_id`, `menu_id`)
SELECT `st`.`ID`,
       344471057539845
FROM `plss_system`.`sys_tenant` `st`;

-- 菜单角色绑定关系
INSERT INTO `plss_system`.`sys_role_menu` (`role_id`, `menu_id`) VALUES (10007, 344471057539845);