insert into "plss-system"."sys_menu" ("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") values (291513372109253, 1185128382981, '禁用', 2, '', '', '', '2', '1', 'F', '2', '1', 'tenant:manage:disable', '#', '001', '2024-09-15 20:45:28.429540', '', '2024-09-15 20:45:28.429542', '', 2);

ALTER TABLE "plss-system".sys_oper_log ADD tenant_id int8 NOT NULL DEFAULT 0;
COMMENT ON COLUMN "plss-system".sys_oper_log.tenant_id IS '租户id';

-- 数据大屏标签定时任务自启动
UPDATE "plss-system"."xxl_job_info" SET "trigger_status" = 1 WHERE "id" = 25;
