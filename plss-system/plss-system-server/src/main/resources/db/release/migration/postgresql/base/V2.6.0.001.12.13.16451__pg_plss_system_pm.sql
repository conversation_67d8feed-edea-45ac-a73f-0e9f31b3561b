
--全量更新项目配置
delete from "plss-system".sys_config where config_id in(
                                                        314729326846021,
                                                        351386334536389,
                                                        310321439892933,
                                                        310320830186949,
                                                        310319480989125,
                                                        310318678967749,
                                                        310319002109381,
                                                        354354976499717,
                                                        354357409933317,
                                                        310316438037957,
                                                        323255684280197,
                                                        310318678967750,
                                                        310319940036037,
                                                        310320319219141
    );

INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310321439892933, '项目参数配置-pm', 'sys.project.config_0', '{"platform":"global","whetherToCollect":true,"personRole":true,"personPost":false,"showWpsPlugin":true,"wpsPluginUrl":"/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false},{"id":"10","lable":"外观设置","isShow":true}]}', 'Y', 1, '2024-10-12 10:30:35.144', 'admin', '2024-12-13 16:39:22.993343', 'admin', '标品参数配置，此配置需要全量更新。
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
searchListSet搜索列表设置
-disabledDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
disabledNavBar是否隐藏导航栏
userDropDisable导航栏下拉隐藏菜单', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310316438037957, '项目参数配置-gjfgw', 'sys.project.config_1', '{"platform":"ndrc","showNavTop":false,"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"gjfgw","value":""}}', 'Y', 1, '2024-10-12 10:20:24.565', 'admin', '2024-12-13 16:20:36.971359', 'admin', '发改委参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
showNavTop:  是否显示nav模块', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (354354976499717, '项目参数配置-zb', 'sys.project.config_10', '{"platform":"zhongban","disableHomeSection":true,"fullBackground":true,"showLogo":true,"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请按规定格式输入日期，示例“2023-01-01”"}}', 'Y', 1, '2024-12-13 15:37:02.717', 'admin', '2024-12-13 16:22:06.520803', 'admin', '', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (323255684280197, '项目参数配置-jxxs', 'sys.project.config_11', '{
  "platform": "xiushui",
  "ssoLoginAuthCode": true,
}', 'Y', 1, '2024-10-30 17:05:22.397', 'admin', '2024-12-13 16:21:54.089274', 'admin', '江西修水配置，此配置需要全量更新。
ssoLoginAuthCode：单点登录配置', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (351386334536389, '项目参数配置-shbb', 'sys.project.config_12', '{"platform":"shbb","recordNoName":"文件编号","graphNoName":"图号","dpRepoId":327602071242373,"ccRepoId":327601942554245,"showKnowledge":true,"showHotwords":true,"disabledFrontDoc":true,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"searchDateFilter":"编制日期","homeRepo":{"repoName":"配套表","repoId":"327599503402629","metadataList":[],"kw":{},"categoryIds":[],"page":1,"pageSize":10,"sortOptions":[{"sortField":"releaseTime","sortType":"desc"}]},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":false},{"id":"todo","lable":"我的待办","isShow":false},{"id":"borrow","lable":"我的借阅","isShow":false},{"id":"collect","lable":"我的收藏","isShow":false},{"id":"read","lable":"我的阅读","isShow":false},{"id":"share","lable":"我的分享","isShow":false},{"id":"documentWarehousing","lable":"文档入库","isShow":false}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":false},{"id":"4","lable":"我的待办","isShow":false},{"id":"3","lable":"我的借阅","isShow":false},{"id":"2","lable":"我的收藏","isShow":false},{"id":"1","lable":"我的阅读","isShow":false},{"id":"5","lable":"我的分享","isShow":false},{"id":"7","lable":"文档入库","isShow":false}],"userDropDisable":["数据大屏","版本号","系统后台"],"documentType":1,"customizedSecurityDoc":true,"customizedSearchResult":true}', 'Y', 1, '2024-12-09 10:57:19.665', 'admin', '2024-12-13 16:24:46.753373', 'admin', 'disableHomeSection :禁用首页文档列表
fullBackground:全屏背景图
showLogo:是否显示logo', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (354357409933317, '项目参数配置-xjklmy', 'sys.project.config_13', '{"platform":"klmy","isLoginUrl":"/v1/sinopec/login/redirect_operation"}', 'Y', 1, '2024-12-13 15:41:59.767', 'admin', '2024-12-13 16:22:16.265098', 'admin', '', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310318678967750, '项目参数配置-zbkt', 'sys.project.config_2', '{
  "platform": "zbkt",
  "disableHomeSection": true,
  "fullBackground": true,
  "showLogo": false,
}', 'Y', 1, '2024-10-25 10:24:58', 'admin', '2024-12-13 16:24:28.395654', 'admin', 'recordNoMid： 文档编号元数据ID
graphNoMid： 更改偏离单图号元数据ID
dpRepoId: 偏离单库ID
ccRepoId：更改单库ID
showKnowledge：开启知识工程', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310320830186949, '项目参数配置-nm', 'sys.project.config_3', '{"platform":"neimeng","hideSelectPermissionRole":false,"hideSelectAdminRole":true,"disabledBrrow":true,"disabledFrontDoc":true,"disabledSignVerification":true,"statisticsTimer":3000,"orgId":1610564159749,"tenantId":1612752027141,"statisticsPath":"statistics_nmg","orcMetadataDefaultValue":{"密级":"非密"},"statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""}],"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}],"condition":{"showDownloadButton":"origin==999","showPrintButton":"origin==999"}}', 'Y', 1, '2024-10-12 10:29:20.716', 'admin', '2024-12-13 16:21:27.482245', 'admin', '内蒙参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310320319219141, '项目参数配置-pd', 'sys.project.config_4', '{"platform":"pudong","jumpUrl":"https://ythbgptuat.ywxt.sh.cegn.cn/"}', 'Y', 1, '2024-10-12 10:28:18.342', 'admin', '2024-12-13 16:21:21.674729', 'admin', '浦东参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310319940036037, '项目参数配置-gxb', 'sys.project.config_5', '{"platform":"goxinbu","showFeekBack":{"img":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAIAAAAP3aGbAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAItUlEQVR4nO3dW1LrSBBAwcsE+98yswPLEUU/jsn8x24JcaI/VPTXz8/PP4CC/04vAOBdggVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZAgWkCFYQMb38Oe/vr5+ZR03ezyn4/VN2HDMx/C3cMNBJI+X8HqRwx9/5xOGji/gBvMnzQ4LyBAsIEOwgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgYzqa8+iGsY/X5iMRq6/x+Ao3DL4cn3/acAlDf+FP6ZEdFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZAgWkCFYQMby0ZxHx08rWW0+0jG/hOFcyw0nvhz/PT6qH5706IZfgR0WkCFYQIZgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAxvnRnA8wHIm4f67l+LE98zXccKwOc3ZYQIZgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhjfdf8Hrd6Dnh1DMrT4fYcOL7Kvv0vEF8A47LCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIEOwgIzzozkmHm444mHo+PkLGwZrjl/jo+OPwQZ2WECGYAEZggVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARnLR3PuH2iYG55JMx8rGX7C8QW88wnHHb+E+2/RBnZYQIZgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGV9/4aSNutWDLxuGThxaw6+wwwIyBAvIECwgQ7CADMECMgQLyBAsIEOwgAzBAjIEC8g4P5qz+sSXudWTK8fHVo4/Axscny6az1fNDSe0hp//K19hhwVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWScf9P90erXc4cL2LCG1Qu4YVrguNU3YT4vcfwxm/OmO/CHCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpDxPfz5Da/zX76AhNWnVDx+/nzuZDihtWHw5fVXbHhQh9d4/DCUd9hhARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZCw/Nef4TMbcx0//3D/X8itfcdYNF7h6vOmRU3OAP0SwgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgQ7CAjM8/NWfD3MnxM2nuX+H8Kz7ehtmdD7jJdlhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpAhWECGYAEZgVNz6gt4dMN00ZAFvLOG1TZMUA05NQf4QwQLyBAsIEOwgAzBAjIEC8gQLCBDsIAMwQIyzr/p/uj1Cje8vDt8gfj+99Qf3bDC+k0+voBHN/wpPbLDAjIEC8gQLCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyPg+vYCp4wMNNzh+uMCj1Sucf/7q0ZkNJ0QcnyHbwA4LyBAsIEOwgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgYzqaMx9oOP6+//wShuf63H+L5lZf4/33cMPkzXEbVmiHBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZAgWkLH81JzjIxGP7p8amU88HD8uZfV80oZ7uNrx5+T+P9V/dlhAiGABGYIFZAgWkCFYQIZgARmCBWQIFpAhWECGYAEZX8dHFoYDBzfME9x/as7xe7j6Mbv/TJoNj8H9oznzm2yHBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZAgWkDEdzTl+4svc/SMR9fGpX/mEoeMLeGSF77DDAjIEC8gQLCBDsIAMwQIyBAvIECwgQ7CADMECMr6HP7/hFefVByisfo17eIGPn/+O44dQPLrhLeqh4WOwweoVbvgl2mEBGYIFZAgWkCFYQIZgARmCBWQIFpAhWECGYAEZggVkTA+heP6C68dKPuCYjEf1qZENNgxIDRew2oZfgdEc4A8RLCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIGM6mrP6yJl3PmH4+XP3Dw+9dvwWvbOG+x+D146fjTR3wwrtsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIEOwgAzBAjK+hz9//KyRufklDM+kcYvm7p8u2jDEtnq86YYn2Q4LyBAsIEOwgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgYzqac8PAwdDqs0Dmn796hTcc6HJ8Pun4Co/fgQQ7LCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIGP5IRQbPuG444dQrP6K42+Bb/iK1TMbG57z4S1KvGpvhwVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpCx/BCKD/A4VDE8hOL44MvxBczNB1/uPwxl9TUmHgM7LCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIEOwgIzpaM6j+w/FmQ8crD4uZfU9vOHYntVzITeMlby24S9l+BU3zO7YYQEZggVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWQsH815dPxImNWOn4byaD5ysXq8ae748ND9s0GPNjzJj+ywgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgQ7CADMECMs6P5nyA1xMJ87mWx4mH4UjEDSMXwzXccAn3D4ENHb/Af3ZYQIhgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhjfdf8HqMyDmXq/w+AENv/IJxx0/CuTR8RXOH3U7LCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIEOwgIzzozk3/Gf7oeNnQBwfWzl+CffPBm34Ja7+U7rhpA87LCBDsIAMwQIyBAvIECwgQ7CADMECMgQLyBAsIEOwgIyv1WMlH8Atmo9crJ7qOD55M3d8hYnhITssIEOwgAzBAjIEC8gQLCBDsIAMwQIyBAvIECwgQ7CAjOloDsA2dlhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpAhWECGYAEZggVkCBaQIVhAhmABGYIFZAgWkCFYQIZgARmCBWQIFpAhWEDG/33L/XMQyNG1AAAAAElFTkSuQmCC","feekbackUrl":"https://f.kdocs.cn/g/A2W3tH5r/"},"recommend":{"rootCategoryId":2804027708677,"defaultSelectId":2804040429573},"guideVideo":{"disabled":false,"title":"操作视频","lists":[{"title":"公文库操作视频","lists":[{"title":"男声版","path":"static/video/test2.mp4"},{"title":"女声版","path":"static/video/test1.mp4"}]},{"title":"AI模型能力展示","lists":[{"title":"男声版","path":"static/video/test4.mp4"},{"title":"女声版","path":"static/video/test3.mp4"}]}]}}', 'Y', 1, '2024-10-12 10:27:32.055', 'admin', '2024-12-13 16:21:09.353704', 'admin', '工信部参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310319480989125, '项目参数配置-gzw', 'sys.project.config_6', '{"platform":"gzw","isLoginUrl":"/open/v1/bcr/login/getAuthorizeUrl"}', 'Y', 1, '2024-10-12 10:26:36.019', 'admin', '2024-12-13 16:21:02.625955', 'admin', '国资委参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310319002109381, '项目参数配置-gjaqb', 'sys.project.config_7', '{"platform":"guoanbu","loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"disabledFrontDoc":true,"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":false}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false}]}', 'Y', 1, '2024-10-12 10:25:37.562', 'admin', '2024-12-13 16:20:55.401823', 'admin', '国安部参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (314729326846021, '项目参数配置-hn', 'sys.project.config_8', '{"platform":"hainan","isClassified":true}', 'Y', 1, '2024-10-18 15:58:27.281', 'admin', '2024-12-13 16:21:38.982537', 'admin', '海南参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL, 1);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (310318678967749, '项目参数配置-xzgf', 'sys.project.config_9', '{"platform":"xzgf","showLogo":false,"showScreen":true}', 'Y', 1, '2024-10-12 10:24:58.116', 'admin', '2024-12-13 16:20:44.121361', 'admin', '西藏高法参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
showScreen: 是否显示我的筛查', NULL, NULL, 1);

--更新备注
update "plss-system".sys_config set remark = '' where config_id = 4812811220741;