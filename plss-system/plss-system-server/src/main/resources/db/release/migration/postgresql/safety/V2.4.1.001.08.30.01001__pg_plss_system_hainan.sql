-- 标品海南需求

-- 新增字典
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status,
 create_by, create_time, update_by, update_time, remark)
VALUES (272952112885509, 6, '安全入库任务', '6', 'sys_biz_module_type', '', 'default', 'N', '1',
        '001', '2024-08-20 15:22:29.717', '', '2024-08-20 15:22:29.717', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status,
 create_by, create_time, update_by, update_time, remark)
VALUES (272951864602373, 5, '安全文档入库', '5', 'sys_biz_module_type', '', 'default', 'N', '1',
        '001', '2024-08-20 15:21:59.409', '', '2024-08-20 15:21:59.409', '');

-- 新增初始化数据权限项
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id,
 rtype)
VALUES (272954309488389, 5, '安全文档入库-查看-本人创建数据权限', 1, 1, '1',
        '2024-08-20 15:26:57.843', '2024-08-20 15:26:57.843', 1612752027141, 1);
INSERT INTO "plss-system".sys_opt_data_pms
(id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id,
 rtype)
VALUES (272954614124293, 6, '安全入库任务-查看-本人创建数据权限', 1, 1, '1',
        '2024-08-20 15:27:35.042', '2024-08-20 15:27:35.042', 1612752027141, 1);

-- 新增入库管理员初始化数据权限
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES (7, 272954309488389);
INSERT INTO "plss-system".sys_role_data_pms
    (role_id, data_pms_id)
VALUES (7, 272954614124293);

-- 新增菜单及绑定对应租户角色
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263610569966213,
        1097331411461,
        '安全文档入库',
        1,
        'document-safety',
        'doc-manage/docToLib',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:37:04.656544',
        '001',
        '2024-08-14 17:26:35.768065',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263610569966213
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263610569966213);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263610569966213);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263611112309381,
        263610569966213,
        '上传文件',
        0,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:upload',
        '#',
        'admin',
        '2024-08-07 10:38:10.859939',
        'chenhong',
        '2024-08-09 16:46:17.410897',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263611112309381
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263611112309381);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263611112309381);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207756710405,
        263610569966213,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:view',
        '#',
        'chenhong',
        '2024-08-09 16:46:33.742199',
        '',
        '2024-08-09 16:46:33.742199',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207756710405
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207756710405);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207756710405);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265207916249605,
        263610569966213,
        '编辑',
        2,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:edit',
        '#',
        'chenhong',
        '2024-08-09 16:46:53.215193',
        '',
        '2024-08-09 16:46:53.215193',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265207916249605
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265207916249605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265207916249605);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208086520325,
        263610569966213,
        '重试',
        3,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:retry',
        '#',
        'chenhong',
        '2024-08-09 16:47:13.999868',
        '',
        '2024-08-09 16:47:13.999868',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208086520325
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208086520325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208086520325);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208287838725,
        263610569966213,
        '人工处理',
        4,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:handle',
        '#',
        'chenhong',
        '2024-08-09 16:47:38.575372',
        '',
        '2024-08-09 16:47:38.575372',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208287838725
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208287838725);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208287838725);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208551768581,
        263610569966213,
        '添加位置',
        5,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:addAttr',
        '#',
        'chenhong',
        '2024-08-09 16:48:10.792742',
        'chenhong',
        '2024-08-09 16:48:34.569421',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208551768581
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208551768581);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208551768581);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265208839586309,
        263610569966213,
        '审核',
        6,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:examin',
        '#',
        'chenhong',
        '2024-08-09 16:48:45.926667',
        '',
        '2024-08-09 16:48:45.926667',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265208839586309
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265208839586309);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265208839586309);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209027699205,
        263610569966213,
        '删除',
        7,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:delete',
        '#',
        'chenhong',
        '2024-08-09 16:49:08.890142',
        '',
        '2024-08-09 16:49:08.890142',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209027699205
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209027699205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209027699205);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (265209239986693,
        263610569966213,
        '全库移除',
        8,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'document:warehousing:globaldelete',
        '#',
        'chenhong',
        '2024-08-09 16:49:34.804707',
        '',
        '2024-08-09 16:49:34.804707',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       265209239986693
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 265209239986693);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 265209239986693);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612063670917,
        1097331411461,
        '安全入库任务',
        2,
        'document-tasksafety',
        'task',
        '',
        '2',
        '1',
        'C',
        '1',
        '1',
        '',
        '#',
        'admin',
        '2024-08-07 10:40:06.99221',
        '001',
        '2024-08-07 13:40:09.418628',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612063670917
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612063670917);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612063670917);
INSERT INTO "plss-system"."sys_menu" ("menu_id",
                                      "parent_id",
                                      "menu_name",
                                      "order_num",
                                      "path",
                                      "component",
                                      "query",
                                      "is_frame",
                                      "is_cache",
                                      "menu_type",
                                      "visible",
                                      "status",
                                      "perms",
                                      "icon",
                                      "create_by",
                                      "create_time",
                                      "update_by",
                                      "update_time",
                                      "remark",
                                      "system_menu_flag")
VALUES (263612286747269,
        263612063670917,
        '查看',
        1,
        '',
        '',
        '',
        '2',
        '1',
        'F',
        '1',
        '1',
        'warehousing:task:view',
        '#',
        'admin',
        '2024-08-07 10:40:34.222801',
        '',
        '2024-08-07 10:40:34.222801',
        '',
        2);
--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       263612286747269
FROM "plss-system".sys_tenant st;
--运营管理员和入库管理员绑定菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 263612286747269);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 263612286747269);
-- 新增库类型字典
INSERT INTO "plss-system"."sys_dict_data" ("dict_code",
                                           "dict_sort",
                                           "dict_label",
                                           "dict_value",
                                           "dict_type",
                                           "css_class",
                                           "list_class",
                                           "is_default",
                                           "status",
                                           "create_by",
                                           "create_time",
                                           "update_by",
                                           "update_time",
                                           "remark")
VALUES (264308295205829,
        6,
        '安全文库',
        '6',
        'rc_repo_type',
        '',
        'default',
        'N',
        '1',
        '001',
        '2024-08-08 10:16:36.192',
        '001',
        '2024-08-08 10:22:36.443479',
        '存放安全文档');

DELETE
FROM "plss-system"."sys_config"
WHERE config_key = 'sys.classified.config';
INSERT INTO "plss-system".sys_config (CONFIG_ID, CONFIG_NAME, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE,
                                      STATUS, CREATE_TIME, CREATE_BY, UPDATE_TIME, UPDATE_BY,
                                      REMARK, ELEMENT_TYPE, ELEMENT_NAME)
VALUES (282005897103109, '安全设置', 'sys.classified.config', 'true', 'Y', 1,
        '2024-09-02 10:22:27.986000', 'admin', '2024-09-02 10:22:27.986000', 'admin',
        '开启/隐藏安全相关', 'text', null);


-- 密级字典
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447217556037, '用户密级', 'sys_user_classified', '1', '2024-07-29 17:37:42.302288',
        '001', '2024-07-29 17:38:11.508552', '001', '用户密级');
INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status",
                                           "create_time", "create_by", "update_time", "update_by",
                                           "remark")
VALUES (257447641131589, '文件密级', 'rc_file_classified', '1', '2024-07-29 17:38:34.006048', '001',
        '2024-07-29 17:39:18.464625', '001', '文件密级');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code",
                                           "dict_sort",
                                           "dict_label",
                                           "dict_value",
                                           "dict_type",
                                           "css_class",
                                           "list_class",
                                           "is_default",
                                           "status",
                                           "create_by",
                                           "create_time",
                                           "update_by",
                                           "update_time",
                                           "remark")
VALUES (257448772848197,
        2,
        '一般涉密人员',
        '100',
        'sys_user_classified',
        '',
        'default',
        'N',
        '1',
        '001',
        '2024-07-29 17:40:52.154',
        '001',
        '2024-07-30 09:58:10.926146',
        '');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code",
                                           "dict_sort",
                                           "dict_label",
                                           "dict_value",
                                           "dict_type",
                                           "css_class",
                                           "list_class",
                                           "is_default",
                                           "status",
                                           "create_by",
                                           "create_time",
                                           "update_by",
                                           "update_time",
                                           "remark")
VALUES (257926153521733,
        3,
        '重要涉密人员',
        '200',
        'sys_user_classified',
        '',
        'default',
        'N',
        '1',
        '001',
        '2024-07-30 09:52:06.162',
        '001',
        '2024-07-30 09:58:16.65784',
        '');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (262298011395653, 1, '非密', '10', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-08-05 14:06:40.219', '001', '2024-08-05 14:38:42.039029', '非密');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code",
                                           "dict_sort",
                                           "dict_label",
                                           "dict_value",
                                           "dict_type",
                                           "css_class",
                                           "list_class",
                                           "is_default",
                                           "status",
                                           "create_by",
                                           "create_time",
                                           "update_by",
                                           "update_time",
                                           "remark")
VALUES (257929911552581,
        3,
        '机密',
        '200',
        'rc_file_classified',
        '',
        'default',
        'N',
        '1',
        '001',
        '2024-07-30 09:59:44.906',
        '001',
        '2024-07-30 10:02:18.182983',
        '机密');
INSERT INTO "plss-system"."sys_dict_data" ("dict_code",
                                           "dict_sort",
                                           "dict_label",
                                           "dict_value",
                                           "dict_type",
                                           "css_class",
                                           "list_class",
                                           "is_default",
                                           "status",
                                           "create_by",
                                           "create_time",
                                           "update_by",
                                           "update_time",
                                           "remark")
VALUES (257929828657733,
        2,
        '秘密',
        '100',
        'rc_file_classified',
        '',
        'default',
        'N',
        '1',
        '001',
        '2024-07-30 09:59:34.787',
        '001',
        '2024-07-30 10:02:12.881834',
        '秘密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257930165135941, 4, '绝密', '300', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:00:15.861', '001', '2024-07-30 10:02:25.030443', '绝密');
-- INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value",
--                                            "dict_type", "css_class", "list_class", "is_default",
--                                            "status", "create_by", "create_time", "update_by",
--                                            "update_time", "remark")
-- VALUES (257931004627525, 5, '核心', '400', 'rc_file_classified', '', 'default', 'N', '1', '001',
--         '2024-07-30 10:01:58.338', '', '2024-07-30 10:01:58.338', '机密-核心');
-- rc_doc_process新增”文件密级”字段

DELETE
FROM "plss-system"."sys_config"
WHERE config_key = 'sys.classified.config';
INSERT INTO "plss-system"."sys_config" (config_id, config_name, config_key, config_value,
                                        config_type, status, create_time, create_by, update_time,
                                        update_by, remark, element_type, element_name)
VALUES (282016809774213, '安全设置', 'sys.classified.config',
        '{"classified":true,"sections":{"name":"安全文库","show":true,"nodes":[{"name":"安全文库","show":true,"icon":"report","type":1,"repoType":3,"subRepos":[]}]}}',
        'Y', 1, '2024-09-02 10:44:40.099', 'admin', '2024-09-04 10:55:27.127554', 'admin',
        '开启/隐藏安全相关', NULL, NULL);

DELETE FROM "plss-system".sys_config WHERE config_key = 'plss.temporary-file.encrypt';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value,
                                      config_type, status, create_time, create_by, update_time,
                                      update_by, remark, element_type, element_name)
VALUES (294938077602821, '临时文件落盘加密', 'plss.temporary-file.encrypt', 'true',
        'Y', 1, '2024-09-20 16:53:03.301', 'admin', '2024-09-20 16:53:03.301', 'admin', '', NULL, NULL);