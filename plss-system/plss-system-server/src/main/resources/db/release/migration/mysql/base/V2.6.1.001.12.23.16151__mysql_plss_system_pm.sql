delete from `plss_system`.sys_page_module;

INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276655948180677, '{
  "label": "智能应用",
  "icon": "application",
  "layout": {
    "w": 2,
    "h": 2
  },
  "node": {
    "type": "l-button",
    "moduleType": "application",
    "config": {
      "label": "智能应用",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 32,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-12-03 15:22:56.448', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964214222021, '{
  "label": "文库入口",
  "icon": "enter",
  "layout": {
    "w": 2,
    "h": 2
  },
  "node": {
    "type": "l-button",
    "moduleType": "library",
    "config": {
      "label": "我的文库",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 32,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-12-03 15:23:06.919', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275044225289413, '{
  "label": "列表组件",
  "icon": "lists",
  "layout": {
    "w": 6,
    "h": 6,
    "minH": 3,
    "minW": 4
  },
  "node": {
    "type": "l-lists",
    "moduleType": "lists",
    "config": {
      "label": "列表标题",
      "showDigest": false,
      "showTime": false,
      "showMetadata": false,
      "pageSize": 10,
      "requestPath": "queryLatestRecordList",
      "icon": "",
      "pageType": 0,
      "orderType": 0
    }
  }
}', '2024-08-22 17:18:54.532', 1612860642053, '2024-12-03 15:23:19.431', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276468841450693, '{
  "label": "容器组件",
  "icon": "container",
  "layout": {
    "w": 6,
    "h": 2,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-container",
    "children": [],
    "config": {
      "label": "",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-25 14:37:17.871', 1612860642053, '2024-12-03 15:23:33.124', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337476010691205, '{
  "label": "图片组件",
  "icon": "image",
  "layout": {
    "w": 12,
    "h": 2
  },
  "node": {
    "type": "l-image",
    "config": {
      "icon": "/plss/front/static/themes/red/index_bg.jpg",
      "isBottom": false,
      "fullWidth": false,
      "horizontalSpacing": 0,
      "borderRadius": 0
    }
  }
}', '2024-11-19 19:16:42.086', 1, '2024-12-03 15:23:42.613', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(338118581332613, '{
  "label": "分类组件",
  "icon": "classify",
  "layout": {
    "w": 4,
    "h": 4,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-search-classify",
    "moduleType": "l-search-classify",
    "config": {
      "isCollapseOpen": "openFirst"
    }
  }
}', '2024-11-20 17:04:00.885', 1, '2024-12-03 15:23:51.508', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337475096447621, '{
  "label": "筛选组件",
  "icon": "filter",
  "layout": {
    "w": 10,
    "h": 2,
    "minH": 2,
    "minW": 8
  },
  "node": {
    "type": "c-detail-search",
    "moduleType": "c-detail-search",
    "config": {
      "disabledAllOption": false,
      "disabledSearchArticle": false,
      "disabledLibrary": true,
      "disabledAdvancedSearch": true,
      "disabledMultiple": false
    }
  }
}', '2024-11-20 19:14:50.482', 1, '2024-12-03 15:24:03.300', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276471564602565, '{
  "label": "LOGO",
  "icon": "logo",
  "layout": {
    "w": 4,
    "h": 1
  },
  "node": {
    "type": "l-button",
    "moduleType": "logo",
    "config": {
      "label": "电子公文库",
      "icon": "/plss/front/static/themes/default/index-second-logo.svg",
      "iconSize": 28,
      "fontWeight": 700,
      "fontSize": 20,
      "color": "rgba(255,255,255,1)",
      "horizontal": true,
      "href": ""
    }
  }
}', '2024-08-22 14:42:50.288', 1612860642053, '2024-12-03 15:05:22.842', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964391824581, '{
  "label": "文库列表",
  "icon": "library",
  "layout": {
    "w": 6,
    "h": 4,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-container",
    "moduleType": "library-container",
    "children": [],
    "config": {
      "label": "文库列表",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-22 15:36:29.236', 1612860642053, '2024-12-03 15:22:43.624', 299806062537861, 0);
INSERT INTO `plss_system`.sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275043279088837, '{
  "label": "首页搜索",
  "icon": "home-search",
  "layout": {
    "w": 12,
    "h": 2,
    "minW": 12,
    "minH": 2
  },
  "node": {
    "type": "c-home-search",
    "moduleType": "c-home-search",
    "config": {
      "disabledTabs": false,
      "disabledHotWord": false,
      "disabledSearchArticle": false,
      "disabledLibrary": true,
      "disabledAdvancedSearch": true,
      "hotWordLabel": "热搜词",
      "tabs": [
        {
          "label": "智能搜索",
          "id": 0,
          "name": "intelligent-search",
          "show": true
        },
        {
          "label": "政务问答",
          "id": 1,
          "name": "chat",
          "show": true
        },
        {
          "label": "公文写作",
          "id": 2,
          "name": "write",
          "show": true
        }
      ],
      "hotWordData": [
        "静态搜索词"
      ],
      "hotWordType": 1
    }
  }
}', '2024-08-22 14:42:59.029', 1612860642053, '2024-12-04 22:31:51.851', 299806062537861, 0);