--应急添加素材库字典
INSERT INTO `plss_system`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES(331750094318213, 0, '素材库', '7', 'rc_repo_type', '', 'default', 'N', '1', 'admin', '2024-11-11 17:07:17.685', '', '2024-11-11 17:07:17.685', '存放文本素材');

--添加应急素材来源内置库或自定义库的配置
INSERT INTO `plss_system`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `status`, `create_time`, `create_by`, `update_time`, `update_by`, `remark`, `element_type`, `element_name`) VALUES(332292685908613, 'ai素材内置库或自定义库的配置', 'ai.material.type', '1', 'Y', 1, '2024-11-12 11:31:12.010', 'admin', '2024-11-12 11:31:12.010', 'admin', '1：内置库，2：自定义库', NULL, NULL);

--应急升级material表name字段长度
ALTER TABLE `plss_record`.`rc_material` MODIFY COLUMN `name` TEXT(16383);

--应急添加分类标签根节点配置
INSERT INTO `plss_system`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `status`, `create_time`, `create_by`, `update_time`, `update_by`, `remark`, `element_type`, `element_name`) VALUES(343723123564997, '应急部素材标签根节点', 'yj.material.tag.categoryId', '343692471336965', 'Y', 1, '2024-11-28 15:06:29.107', 'admin', '2024-11-28 15:16:55.462', 'admin', '应急部内置的素材标签根节点', NULL, NULL);

--工信部添加非密系统提示语
delete from `plss_system`.`sys_config` where `config_id` = 341476730601925;
INSERT INTO `plss_system`.`sys_config`
(`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `status`, `create_time`, `create_by`, `update_time`, `update_by`, `remark`, `element_type`, `element_name`)
VALUES(341476730601925, '非密系统提示语', 'sys.noConfidential.prompt', '{"show":false,"tips":"非密系统，请勿上传涉密文件"}', 'Y', 1, '2024-11-25 10:56:11.216', 'admin', '2024-11-27 16:40:49.857', 'admin', '非密系统提示语', 'json', NULL);

--更新开放平台白名单配置
update `plss_system`.`sys_config`
set `config_value` = '{"verifyAk":true,"verifyWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/write/assist/search/**","/v1/auth/login","/v1/user/getInfo","/v1/white/**","/v1/abutment/config/manufacturer_reload","/v1/am/login/ack","/v1/app/**","/v1/redHeadTemplate/**","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo","/v1/material/**","/v1/category/tree"],"appWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/auth/login","/v1/user/getInfo","/v1/auth/refreshToken","/v1/app/**","/v1/white/**","/v1/write/assist/search/**","/v1/redHeadTemplate/**","/v1/am/login/ack","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo","/v1/material/**","/v1/category/tree"]}'
where `config_key` = 'open.base.config';

--显示模板管理菜单和排版管理菜单
UPDATE `plss_system`.`sys_menu` SET `visible`='1' WHERE `menu_id`=297764407879173 OR `menu_id`=301826980374341;

--更新操作日志相关配置
UPDATE `plss_system`.`sys_config` SET  `config_value` = '[{"code":1,"modeName":"前台-文档操作","bussineType":6003,"remark":"打印了文档#recordName"},{"code":2,"modeName":"前台-文档操作","bussineType":6007,"remark":"分享了文档#recordName"},{"code":3,"modeName":"前台-文档操作","bussineType":6002,"remark":"复制了文档#recordName的内容:#remark"},{"code":4,"modeName":"前台-文档操作","bussineType":3015,"remark":"查看了文档#recordName"},{"code":11,"modeName":"后台-文档操作","bussineType":6003,"remark":"打印了文档#recordName"},{"code":14,"modeName":"后台-文档操作","bussineType":3015,"remark":"查看了文档#recordName"}]' WHERE `config_key` = 'sys:operLog:type';






















