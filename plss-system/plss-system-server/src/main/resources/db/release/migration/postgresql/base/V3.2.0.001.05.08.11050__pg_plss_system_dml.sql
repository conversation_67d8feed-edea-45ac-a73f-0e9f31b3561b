DELETE
FROM "plss_system".sys_config
WHERE config_id = 310321439892933;
INSERT INTO "plss_system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name,
                                      deliver_required_item_flag)
VALUES (310321439892933, '项目参数配置-pm', 'sys.project.config_0',
        '{"platform":"global","whetherToCollect":true,"personRole":true,"personPost":false,"showWpsPlugin":false,"wpsPluginUrl":"/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":true,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"showFrontThemeChange":true,"font":[{"name":"标准","value":"1","bacgroundcolor":"#efefef","fontSize":"10","show":true},{"name":"大字","value":"2","bacgroundcolor":"#efefef","fontSize":"11","show":true},{"name":"超大字","value":"3","bacgroundcolor":"#efefef","fontSize":"12","show":true}],"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx,.wps,.wps,.dot,dotx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":100,"maxSizeError":"文件限制{0}M以内","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":false},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":false},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":false},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":false},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":true},{"id":"10","lable":"外观设置","isShow":true}],"showScreen":false,"searchListSet":{"disableDigest":false,"disableMetadata":false},"userDropDisable":["退出登录"],"showOrgInteraction":true}',
        'Y', 1, '2024-10-12 10:30:35.144', 'admin', '2025-04-24 14:42:22.223', 'admin', '标品参数配置，此配置需要全量更新。
showThemeChange:前台个人中心主题变更是否展示
font:个人中心的字号
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
searchListSet搜索列表设置
-disabledDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
disabledNavBar是否隐藏导航栏
userDropDisable导航栏下拉隐藏菜单
showScreen: 显示我的筛查', NULL, NULL, 1);

DELETE FROM "plss-system".sys_config WHERE config_key = 'record.pubLib.verify.md5';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time,
                                      create_by, update_time, update_by,
                                      remark, element_type, element_name,
                                      deliver_required_item_flag)
VALUES (462661487600197, 'md5文件校验', 'record.pubLib.verify.md5', 'false', 'Y', 1,
        '2025-05-15 16:07:12', 'admin', '2025-05-15 16:07:12', 'admin', '开启校验: true
关闭校验: false', 'text', NULL, 1);

DELETE FROM "plss-system".sys_config
WHERE config_key = 'sys.appearance.theme';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES (849473740037, '主题设置', 'sys.appearance.theme', '[{"name":"素雅白","flodername":"default","value":"1","ischeckd":false,"bacgroundcolor":"#efefef"},{"name":"星耀红","flodername":"red","value":"2","ischeckd":false,"bacgroundcolor":"#C54334"},{"name":"智慧蓝","flodername":"blue","value":"3","ischeckd":false,"bacgroundcolor":"#1F50DB"},{"name":"军工绿","flodername":"armyGreen","value":"4","ischeckd":false,"bacgroundcolor":"#1E8C69"},{"name":"科技蓝","flodername":"techBlue","value":"5","ischeckd":true,"bacgroundcolor":"#4D7AD8"},{"name":"金山蓝","flodername":"ksBlue","value":"6","ischeckd":false,"bacgroundcolor":"#1890FF"}]', 'Y', 1, '2023-09-08 00:00:00', 'admin', '2025-05-19 11:30:11.863591', 'admin', '请选择颜色', 'color', '主题颜色', 1);


UPDATE "plss-system".sys_dict_data SET dict_label = '主题库' WHERE dict_type = 'rc_repo_type' AND dict_value = '2';


DELETE FROM "plss-system".sys_config WHERE config_id = 462520365212549;
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(462520365212549, '资源库-标题设置', 'sys.appearance.resource.title', 'WPS政务资源库', 'Y', 1, '2025-05-15 11:20:05.519', 'admin', '2025-05-15 11:20:05.519', 'admin', '', NULL, NULL, 1);


DELETE FROM "plss-system".sys_config WHERE config_id=205798046516677;
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(205798046516677, '系统文库页签配置', 'system.repository.config', '{"tabs":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"主题库","ctype":[2]},{"label":"模板&范文样例库","ctype":[4,5]},{"label":"安全文库","ctype":[6]}],"query":[],"front":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"安全文库","ctype":[6]},{"label":"主题库","ctype":[2]}]}', 'Y', 1, '2024-05-17 18:17:11.850', 'admin', '2025-05-16 17:21:59.781', 'admin', '备份: 内蒙不需要显示该查询条件
		"query": [
    {
		"id": "publishTime",
		"component": "date",
		"type": "metadata",
		"label": "发布日期",
		"data": {
		"metadataId": "1121649841157",
		"metadataValue": "{publishTime}"
		}
    }
  ]', NULL, NULL, 1);

DELETE FROM "plss-system".sys_config WHERE config_key = 'ai.home.section';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES(413122742585221, '首页版本配置-AIChat版', 'ai.home.section', '{"content":"金山政务大模型，专注政务领域，一站式解决复杂问题","sideBarItems":{"show":true,"lists":[[{"icon":"MainPage","title":"首页","id":"11","path":"/","show":true}],[{"icon":"NewDialog","title":"新对话","id":"12","path":"/aichathome","show":true},{"icon":"DialogHistory","title":"历史对话","id":"13","path":"/historySpeak","show":true}],[{"icon":"ResourceBase","title":"资源库","id":"14","path":"/airesource","show":true},{"icon":"AiApplication","title":"智能应用","id":"15","path":"/aiIntelligence","show":false}],[{"icon":"Avatar","id":"16","show":true,"component":"UserCenter"}]]},"aiIntelligence":{"content":"智能应用，让高效与智慧触手可及！","navList":[{"icon":"weboffice","title":"公文写作","id":"10","path":"/weboffice","dec":"专业的公文写作助手","show":false},{"icon":"contrast","title":"智能校对","id":"12","path":"/proofread","dec":"检测文字错误和内容表述问题","show":false},{"icon":"comparison","title":"智能对比","id":"11","path":"/duplicateCheck","dec":"精准对比文档增删改差异点","show":false},{"icon":"check","title":"智能查重","id":"13","path":"/checkIndex","dec":"快速对文档内容进行查重并标记 ","show":false}]},"aiModel":[{"name":"金山政务大模型","dec":"采用金山政务大模型回答","value":"qingqiu","id":1,"icon":"jinshandamoxing"},{"name":"DeepSeek-R1","dec":"采用DeepSeek-R1推理模型回答","value":"deepSeekR1","icon":"a-DeepSeek_logo1","id":2,"isactive":true}],"userDropDisable":[],"uploadFileLimit":{"multiple":true,"maxSize":50,"limit":10,"maxSizeError":"最多支持{4}个文件，总大小不超过{0}MB","accept":".doc,.docx,.ofd,.pdf,.xls,.xlsx","acceptTip":"Word,Excel,OFD,PDF","tip":"最多支持{4}个文件，总大小不超过{0}MB"},"aiResourceVersion":"2","hideAdvanced":true}', 'Y', 1, '2025-03-06 16:20:22', 'admin', '2025-05-21 10:41:57', 'admin', 'sideBarItems：侧边栏项目，每一项id不可重复', NULL, NULL, 1);



INSERT INTO "plss-system".sys_version
(id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by)
VALUES(7, 'v3.2.0', 'WPS政务资源库UI升级；优化“AI问答”；优化“高级搜索”；修复已知问题。', '1', 'v3.2.0_front.md', '2025-05-22 00:00:00.000', '1', '1', '2025-05-22 00:00:00.000', 1);

INSERT INTO "plss-system".sys_version
(id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by)
VALUES(8, 'v3.2.0', '文库内新增“上传文档”功能；新增“金山蓝”主题；其他优化；修复已知问题。', '2', 'v3.2.0_backend.md', '2025-05-22 00:00:00.000', '1', '1', '2025-05-22 00:00:00.000', 1);

UPDATE "plss-system".sys_config
SET  config_value='{"front":"v3.2.0","backend":"v3.2.0"}'
WHERE config_id=203685690483910;