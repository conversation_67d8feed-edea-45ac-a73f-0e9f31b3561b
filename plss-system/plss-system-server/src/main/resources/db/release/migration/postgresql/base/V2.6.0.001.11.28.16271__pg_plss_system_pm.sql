--搜索历史添加搜索类型
ALTER TABLE "plss-system"."sys_search_history"
    ADD COLUMN "type" int2;

COMMENT ON COLUMN "plss-system"."sys_search_history"."type" IS '搜索类型 1普通搜索 2高级搜索';

--高级搜索聚合元数据值定时任务
delete from "plss-system".xxl_job_info where id = 343765357682949;
insert into "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") values (343765357682949, 191033526781957, '高级搜索缓存聚合元数据值', '2024-11-28 16:32:24.636000', '2024-11-28 16:32:45.501000', '杨俊', null, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'cacheMetadataValueAgg', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-11-28 16:32:24.636000', null, 1, 0, 1732809600000);

--高级搜索配置
delete from "plss-system".sys_config where config_key = 'search.record.search.config';
insert into "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") values (2669511064581, '搜索文档的配置', 'search.record.search.config', '{"enableVector":true,"enableAi":true,"advanceSearchConfig":{"excludeMetadataNameList":["文件层级"],"aggValueMetadataList":[{"id":1121551467781,"name":"发文机关"},{"id":1121443148293,"name":"发文字号"}]}}', 'Y', 1, '2023-11-29 16:36:17.598975', '001', '2024-11-28 16:38:38.338490', 'admin', 'enableAi:是否开启AI搜索
enableVector:是否开启向量搜索
symbolsFilter:搜索时要过滤的符号
advanceSearchConfig高级搜索配置
excludeMetadataNameList不需要展示的元数据名称
aggValueMetadataList需要聚合的元数据列表', null, null);
