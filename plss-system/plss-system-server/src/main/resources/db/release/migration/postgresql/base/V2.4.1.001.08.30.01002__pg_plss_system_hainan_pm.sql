update "plss-system".xxl_job_info set job_desc='触发元数据修复任务',schedule_conf='0 0/1 * * * ?',executor_handler='triggerMetadataRepairTask' where id = 279173196621509;
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) VALUES(288565733327877, 2, '触发元数据修复消息', '2024-09-11 16:48:29.238', '2024-09-11 16:50:19.811', '陈忠鑫', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'ROUND', 'triggerMetadataRepairMsg', '{"threadNum":2,"maxFailTimes":3,"pageSize":100}', 'DISCARD_LATER', 0, 0, 'BEAN', NULL, NULL, '2024-09-11 16:48:29.238', NULL, 1, 0, 1726074000000);

update "plss-system".sys_config set config_value = 2 where config_id = 274937511995589;
update "plss-system".sys_config set config_value = 500 where config_id = 274936123369669;