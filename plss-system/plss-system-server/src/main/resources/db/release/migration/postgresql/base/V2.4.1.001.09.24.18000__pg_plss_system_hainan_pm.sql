-- 开放平台页面 字典sql

INSERT INTO "plss-system".sys_dict_type
(dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark)
VALUES(287155650415749, '权益类型', 'ope_equity_type', '1', '2024-09-09 16:59:39.993', 'admin', '2024-09-10 11:08:09.321', 'admin', '应用管理权益类型');
INSERT INTO "plss-system".sys_dict_type
(dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark)
VALUES(287690338263429, '签名类型', 'ope_signature_type', '1', '2024-09-10 11:07:29.507', 'admin', '2024-09-10 11:07:29.507', '', '应用管理签名类型');
INSERT INTO "plss-system".sys_dict_type
(dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark)
VALUES(287690565288325, '密码类型', 'ops_password_type', '1', '2024-09-10 11:07:57.219', 'admin', '2024-09-10 11:07:57.219', '', '应用管理密码类型类型');


INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287691923997061, 3, 'AES对称加密', 'AES', 'ops_password_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:10:43.066', '', '2024-09-10 11:10:43.066', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287156730506373, 0, '默认', '1', 'ope_equity_type', '', 'default', 'N', '1', 'admin', '2024-09-09 17:01:51.829', '', '2024-09-09 17:01:51.829', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287156821511301, 1, '内网', '2', 'ope_equity_type', '', 'default', 'N', '1', 'admin', '2024-09-09 17:02:02.938', 'admin', '2024-09-11 10:28:37.069', '海南内网');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287690864083333, 0, 'SM3', 'SM3', 'ope_signature_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:08:33.682', '', '2024-09-10 11:08:33.682', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287690932609413, 1, 'MD5', 'MD5', 'ope_signature_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:08:42.047', '', '2024-09-10 11:08:42.047', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287691137581445, 0, '明文', 'plaintext', 'ops_password_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:09:07.068', '', '2024-09-10 11:09:07.068', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287691285930373, 1, 'sm4对称加密', 'SM4', 'ops_password_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:09:25.177', 'admin', '2024-09-10 11:10:23.651', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287691737055621, 2, 'MD5', 'MD5', 'ops_password_type', '', 'default', 'N', '1', 'admin', '2024-09-10 11:10:20.246', '', '2024-09-10 11:10:20.246', '');


update "plss-system".sys_config
set config_value = '{"verifyAk":true,"verifyWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/write/assist/search/**","/v1/auth/login","/v1/user/getInfo","/v1/white/**","/v1/abutment/config/manufacturer_reload","/v1/am/login/ack","/v1/app/**","/v1/redHeadTemplate/**","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**"],"appWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/auth/login","/v1/user/getInfo","/v1/auth/refreshToken","/v1/app/**","/v1/white/**","/v1/write/assist/search/**","/v1/redHeadTemplate/**","/v1/am/login/ack","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**"]}'
where config_key = 'open.base.config'