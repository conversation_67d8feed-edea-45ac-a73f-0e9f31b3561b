-- 文件下载限制配置
delete from `plss_system`.`sys_config` where config_key = 'record.downloads.files.limits.configKey';
INSERT INTO `plss_system`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`,
                                        `config_type`, `status`, `create_time`, `create_by`,
                                        `update_time`, `update_by`, `remark`, `element_type`,
                                        `element_name`)
VALUES (348711930726917, '反爬虫文件时间内下载个数限制配置', 'record.downloads.files.limits.configKey', '[{"fileType":1,"timeLimit":60,"downCountLimit":100,"switchStatus":2}]', 'Y', 1, '2024-12-05 16:16:14.356', 'admin', '2024-12-05 16:18:01.141443', 'admin', 'fileType=1-主件，2附件
timeLimit=时间(单位分钟)
downloadCountLimit=下载文件个数
', 'json', NULL);


DELETE FROM `plss_system`.xxl_job_info WHERE executor_handler = 'syncDocProcessAllDbToEs';
INSERT INTO `plss_system`.xxl_job_info
(id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type,
 schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param,
 executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (436518615444357, 2, '文档处理表全量同步es中【每月1号凌晨1点执行】', '2025-04-08 17:39:23.795', '2025-04-08 17:39:23.795', 'zdh', NULL, 'CRON', '0 1 0 1 * ?', 'DO_NOTHING', 'FIRST', 'syncDocProcessAllDbToEs', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-04-08 17:39:23.795', NULL, 0, 0, 0);

UPDATE `plss_system`.sys_user SET password='$2a$10$JoYgOauZYoGRV0LUjsFQOOcJ1aznpziyI2vSo6T4.E.gJ814AP71e' WHERE user_id=1;
UPDATE `plss_system`.sys_user SET password='$2a$10$0yR5tEKucE9zPbwrD8b9s.nd3D.JUTLBPxv06gs1XvBA0UxCYAPYC' WHERE user_id=2;
UPDATE `plss_system`.sys_user SET password='$2a$10$FOwP8r.HQzON.zqCn9l9AOpJTdPUdvEkGGuplIizeUogD.NwsJ88C' WHERE user_id=3;
UPDATE `plss_system`.sys_user SET password='$2a$10$DdE30qmb2gZsDSOdzA6/rOUSUlM0BlPAsyqjWTx2K/Y5XblmxIqUK' WHERE user_id=4;
UPDATE `plss_system`.sys_user SET password='$2a$10$fGAiVFvuZ0pYQEE0Y/2TSekM7j3Ej2nCm.rBWA1EO8p5gCbnotTXi' WHERE user_id=5;
UPDATE `plss_system`.sys_user SET password='$2a$10$Whk0SrYzTBeHmgJIWLO7C.tt8etDoOfjligd7Pk.9n3DrPTLkDgd6' WHERE user_id=6;
UPDATE `plss_system`.sys_user SET password='$2a$10$pg8gajubuSfV9pPsbR1lTOAudPBBEk1mSogHN3DRiaxSrnTGY4OS.' WHERE user_id=7;
UPDATE `plss_system`.sys_user SET password='$2a$10$AKieTXzTm7hfPKHp/JoQIerIWSI8ITi6v8KPs675aZ0/kVxQnRmLy' WHERE user_id=8;
UPDATE `plss_system`.sys_user SET password='$2a$10$Vrov6w4NNMqZBrhsTBcjYO6PavUa.YBPiTiUN/NTyw8tNO.CzzH46' WHERE user_id=9;
UPDATE `plss_system`.sys_user SET password='$2a$10$VRx78Q31oshWxs0EeWyE.uTw8KL.v3ELLRzgqAzN3GGSb3Y2TB/0y' WHERE user_id=1612860642053;

update `plss_system`.sys_version set version_desc = 'UI界面全新改版；AI问答接入“DeepSeek”深度推理模型；新增问答“联网搜索、上传文件”功能；新增“搜索附件”功能；新增“个性化设置-主题&字号”功能；新增“AI仿写”功能；新增“内容排版为公文格式”功能。；新增“自动使用素材”全局RAG设置；新增“公文套红”功能；新增AI问答“历史记录”；新增“AI写作引导页”功能；写作接入“DeepSeek”深度推理模型；优化“文件阅读”界面；优化“AI写作”“添加灵感”交互；优化写作指令激活；优化写作界面显示大小；优化提示词输入效果；优化“一键排版”界面参数；优化法律文件“时效性”更新机制；新增“AI写作引导页”功能；新增“公文套红”功能；新增“自动使用素材”全局RAG设置；新增“AI仿写”功能；新增“生成结语”功能；新增“内容排版为公文格式”功能；新增“我的模板”功能；新增AI问答“历史记录”；优化“AI写作”“添加灵感”交互；优化“润色全文/大纲/内容”功能；优化“一键排版”界面参数；优化“智能查重”功能；修复已知问题。' where id = 5;

-- 版本日志 20250422更新  from 王帅鹏
update `plss_system`.sys_version set version_desc = 'UI界面全新改版；AI问答接入“DeepSeek”深度推理模型；新增问答“联网搜索、上传文件”功能；新增“搜索附件”功能；新增“个性化设置-主题&字号”功能；新增“AI仿写”功能；新增“写公文”功能；新增“自动使用素材”全局RAG设置与按句溯源；新增“公文套红”功能；新增AI问答“历史记录”；新增“AI写作引导页和欢迎页”；写作接入“DeepSeek”深度推理模型；优化“文件阅读”界面；优化“大纲成文”功能；优化“AI帮我写”交互；优化写作指令激活；优化写作界面显示大小；优化“模板库”；优化“一键排版”界面参数；优化法律文件“时效性”更新机制；新增“AI写作引导页”功能；新增“公文套红”功能；新增“自动使用素材”全局RAG设置；新增“AI仿写”功能；新增“生成结语”功能；新增“内容排版为公文格式”功能；新增“我的模板”功能；新增AI问答“历史记录”；优化“AI写作”“添加灵感”交互；优化“润色全文/大纲/内容”功能；优化“一键排版”界面参数；优化“智能查重”功能；修复已知问题。' where id = 5;

DELETE FROM `plss_system`.sys_logininfor WHERE msg='退出成功';