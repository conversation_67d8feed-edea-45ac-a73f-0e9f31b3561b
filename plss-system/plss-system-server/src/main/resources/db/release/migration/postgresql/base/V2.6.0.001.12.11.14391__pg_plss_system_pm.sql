delete from "plss-system".sys_menu sm where sm.menu_id in(347943710608581,347944609639621,347944827808965,347945175944389,347945320074437,348560440069381,258049632232069);
delete from "plss-system".sys_tenant_menu sm where sm.menu_id in(347943710608581,347944609639621,347944827808965,347945175944389,347945320074437,348560440069381,258049632232069);
delete from "plss-system".sys_role_menu sm where sm.menu_id in(347943710608581,347944609639621,347944827808965,347945175944389,347945320074437,348560440069381,258049632232069);

-- 新增菜单	统计分析
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347943710608581, 0, '统计分析', 17, 'statistics-data', '', '', '1', '1', 'M', '1', '1', '', '#', '001', '2024-12-04 14:13:17.487', '001', '2024-12-10 15:59:20.670', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 347943710608581 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 347943710608581);

-- 新增菜单	用户统计概览
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347944609639621, 347943710608581, '用户统计概览', 1, 'userView', 'statistics-data/userView/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-12-04 14:15:07.231', '001', '2024-12-04 14:59:16.013', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 347944609639621 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 347944609639621);

-- 新增菜单	智能应用概览
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347944827808965, 347943710608581, '智能应用概览', 2, 'aiView', 'statistics-data/aiView/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-12-04 14:15:33.864', '001', '2024-12-04 14:50:48.100', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 347944827808965 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 347944827808965);

-- 新增菜单	数据统计明细
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347945175944389, 347943710608581, '数据统计明细', 3, 'dataView', 'statistics-data/dataView/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-12-04 14:16:16.361', '001', '2024-12-05 11:07:18.394', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 347945175944389 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 347945175944389);

-- 新增菜单	采集数据详情
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347945320074437, 347943710608581, '采集数据详情', 4, 'dataDetailView', 'statistics-data/dataDetailView/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-12-04 14:16:33.955', '001', '2024-12-04 14:50:58.776', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 347945320074437 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 347945320074437);

-- 新增菜单	监管分析
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(348560440069381, 347943710608581, '监管分析', 5, 'supervise', 'supervise/index', '', '1', '1', 'C', '1', '1', '', '#', '001', '2024-12-05 11:08:01.848', '001', '2024-12-05 11:21:14.340', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 348560440069381 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(1, 348560440069381);