
-- 文库填充组织机构id任务
DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'fillRepoOrgId';
INSERT INTO "plss-system".xxl_job_info
(id, job_group, job_desc, add_time, update_time,
 author, alarm_email, schedule_type, schedule_conf,
 misfire_strategy, executor_route_strategy, executor_handler,
 executor_param, executor_block_strategy, executor_timeout,
 executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status,
 trigger_last_time, trigger_next_time)
VALUES (309718159960389, 2, '文库填充组织机构id', '2024-10-11 14:03:12.57',
        '2024-10-11 14:03:12.57', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *',
        'DO_NOTHING', 'ROUND', 'fillRepoOrgId', NULL, 'SERIAL_EXECUTION',
        0, 0, 'BEAN', NULL, NULL, '2024-10-11 14:03:12.57', NULL, 0, 0, 0);

-- 分类标签《论“一带一路”》
DELETE FROM "plss-system".sys_category WHERE id = 229182064347529;
INSERT INTO "plss-system".sys_category
(id, name, status, order_by, create_time, create_by,
 update_by, update_time, ctype, remark, visit_type, org_id,
 image_url, fixed_data, prohibit_state)
VALUES (229182064347529, '论“一带一路”', '1', 3, '2024-06-19 00:00:00', '001',
        '001', '2024-06-19 00:00:00', 1, NULL, 1, 1610564159749, NULL, 2, 1);


-- 清理用户行为日志脏数据
DELETE
FROM
    "plss-system".sys_user_behavior_log_202409
WHERE
    create_time IN ( SELECT create_time FROM "plss-system".sys_user_behavior_log_202409 GROUP BY create_time HAVING COUNT ( * ) > 3 )