-- yangjun
--模板库范文样例库表头
delete from `plss_system`.`sys_table_header_config` where id in( 338076401322640,338076401322630);
delete from `plss_system`.`sys_table_header` where config_id in( 338076401322640,338076401322630);

INSERT INTO `plss_system`.`sys_table_header_config` (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (338076401322630, '文库管理', '库的目录', 'rc_record', 4, 1, 5435880395013, 1, '1', '1191407656453', '2024-12-09 11:40:56.383198', '1191407656453', '2024-12-09 11:40:56.383198', '', NULL);
INSERT INTO `plss_system`.`sys_table_header_config` (id, function_module, table_type, table_names, field_count, config_type, repo_id, status, del_flag, create_by, create_time, update_by, update_time, remark, code) VALUES (338076401322640, '文库管理', '库的目录', 'rc_record', 4, 1, 5435878501381, 1, '1', '1191407656453', '2024-12-10 02:01:53.89815', '1191407656453', '2024-12-10 02:01:53.89815', '', NULL);

INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322631, 338076401322630, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322632, 338076401322630, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 1, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322633, 338076401322630, 1, 'orderBy', '序号', '序号', 0, '', 2, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322634, 338076401322630, 1, 'modifyTime', '更新时间', '更新时间', 0, '', 3, 1, '1', '1612860642053', '2024-12-09 19:46:42', '', '2024-12-09 19:46:46', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322641, 338076401322640, 1, 'resourceName', '资源名称(可能为库名、目录名、文件名)', '名称', 0, '', 0, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322642, 338076401322640, 1, 'resourceCreator', '资源创建人姓名', '操作人', 0, '', 1, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322643, 338076401322640, 1, 'orderBy', '序号', '序号', 0, '', 2, 1, '1', '1612860642053', '2024-04-28 00:00:00', '', '2024-04-28 00:00:00', '');
INSERT INTO `plss_system`.`sys_table_header` (id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES (338076401322644, 338076401322640, 1, 'modifyTime', '更新时间', '更新时间', 0, '', 3, 1, '1', '1612860642053', '2024-12-09 19:46:42', '', '2024-12-09 19:46:46', '');

--删除原AI模板
delete from `plss_system`.`sys_category` where id = 230231454655749;
--新增仿写模板 id保持不变
INSERT INTO `plss_system`.`sys_category` (id, name, status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state) VALUES (230231454655749, '仿写模板', '1', 0, '2024-06-21 06:47:05.566202', '001', '001', '2024-06-21 06:47:05.566202', 1, NULL, 1, 1610564159749, NULL, 1, 1);
--新增AI模板
delete from `plss_system`.`sys_category` where id = 334529096862277;
delete from `plss_system`.`sys_category_relation` where descendant_id = 334529096862277;
INSERT INTO `plss_system`.`sys_category` (id, name, status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state) VALUES (334529096862277, 'AI模板', '1', -1, '2024-11-15 15:21:11.392433', '001', '001', '2024-11-15 15:21:11.392433', 1, NULL, 1, 1610564159749, NULL, 1, 1);
INSERT INTO `plss_system`.`sys_category_relation`  (descendant_id, ancestor_id, distance) VALUES (334529096862277, 0, 2);
INSERT INTO `plss_system`.`sys_category_relation`  (descendant_id, ancestor_id, distance) VALUES (334529096862277, 187865553923845, 1);
--修改法定公文为红头模板
update `plss_system`.`sys_category` set name = '红头模板' where id = 187865965465349;
--禁用事务公文
update `plss_system`.`sys_category` set status = 2 where id = 187865779097349;

-- zhoujiahao
UPDATE `plss_system`.sys_config SET config_name='项目参数配置-标品', config_key='sys.project.config_0', config_value='{"platform":"global","whetherToCollect":true,"personRole":true,"personPost":false,"showWpsPlugin":true,"wpsPluginUrl":"/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":true,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false}]}', config_type='Y', status=1, create_time='2024-10-12 10:30:35.144', create_by='admin', update_time='2024-12-06 16:10:57.403', update_by='admin', remark='标品参数配置，此配置需要全量更新。
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', element_type=NULL, element_name=NULL, deliver_required_item_flag=1 WHERE config_key='sys.project.config_0';

-- zhongxin.chen
ALTER TABLE plss_system.sys_user_behavior_log ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';

ALTER TABLE plss_system.sys_user_behavior_log_202407 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202407 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202407 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202407 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202408 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202408 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202408 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202408 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202409 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202409 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202409 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202409 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202410 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202410 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202410 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202410 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202411 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202411 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202411 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202411 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202412 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202412 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202412 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202412 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202501 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202501 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202501 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202501 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202502 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202502 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202502 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202502 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202503 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202503 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202503 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202503 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202504 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202504 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202504 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202504 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202505 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202505 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202505 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202505 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202506 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202506 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202506 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202506 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202507 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202507 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202507 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202507 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202508 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202508 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202508 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202508 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202509 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202509 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202509 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202509 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202510 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202510 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202510 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202510 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202511 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202511 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202511 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202511 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202512 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202512 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202512 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202512 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202601 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202601 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202601 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202601 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202602 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202602 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202602 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202602 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202603 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202603 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202603 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202603 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202604 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202604 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202604 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202604 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202605 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202605 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202605 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202605 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202606 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202606 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202606 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202606 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202607 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202607 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202607 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202607 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202608 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202608 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202608 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202608 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202609 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202609 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202609 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202609 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202610 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202610 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202610 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202610 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202611 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202611 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202611 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202611 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202612 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202612 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202612 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202612 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202701 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202701 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202701 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202701 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202702 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202702 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202702 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202702 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202703 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202703 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202703 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202703 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202704 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202704 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202704 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202704 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202705 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202705 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202705 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202705 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202706 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202706 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202706 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202706 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';
ALTER TABLE plss_system.sys_user_behavior_log_202707 ADD consume_token INT DEFAULT 0 NULL COMMENT '消耗token数量';
ALTER TABLE plss_system.sys_user_behavior_log_202707 ADD record_id BIGINT DEFAULT 0 NULL COMMENT '文档id';
ALTER TABLE plss_system.sys_user_behavior_log_202707 ADD model_name varchar(100) DEFAULT '' NULL COMMENT '模型名称';
ALTER TABLE plss_system.sys_user_behavior_log_202707 ADD source SMALLINT DEFAULT 0 NULL COMMENT '来源。1web，2客户端';


