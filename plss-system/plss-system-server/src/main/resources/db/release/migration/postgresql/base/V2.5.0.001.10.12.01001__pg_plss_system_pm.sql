
CREATE TABLE "plss-system".sys_version (
                                           id bigint NULL,
                                           version_no varchar(10) NULL,
                                           version_desc varchar NULL,
                                           version_type char(1) NULL,
                                           version_md varchar(20) NULL,
                                           publish_time timestamp NULL,
                                           menu_visible char(1) NULL DEFAULT 1,
                                           remind_visible char(1) NULL DEFAULT 1,
                                           create_time timestamp NULL,
                                           create_by bigint NULL,
                                           CONSTRAINT sys_version_pk PRIMARY KEY (id)
);
COMMENT ON TABLE "plss-system".sys_version IS '版本信息表';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN "plss-system".sys_version.id IS '主键id';
COMMENT ON COLUMN "plss-system".sys_version.version_no IS '版本号';
COMMENT ON COLUMN "plss-system".sys_version.version_desc IS '版本更新内容(弹框的描述)';
COMMENT ON COLUMN "plss-system".sys_version.version_type IS '1前台 、 2后台';
COMMENT ON COLUMN "plss-system".sys_version.publish_time IS '发布时间';
COMMENT ON COLUMN "plss-system".sys_version.menu_visible IS '是否在更新日志左侧菜单展示(1是，2否)';
COMMENT ON COLUMN "plss-system".sys_version.remind_visible IS '是否在登录弹框提示（防止有的版本不需要提示，如bugfix版本。1是，2否）';
COMMENT ON COLUMN "plss-system".sys_version.create_time IS '创建时间';
COMMENT ON COLUMN "plss-system".sys_version.create_by IS '创建人';
COMMENT ON COLUMN "plss-system".sys_version.version_md IS '版本更新内容md文件名称';



CREATE TABLE "plss-system".sys_version_remind (
                                                  id bigint NULL,
                                                  user_id bigint NULL,
                                                  version_no varchar(10) NULL,
                                                  version_type char(1) NULL,
                                                  create_time timestamp NULL,
                                                  create_by bigint NULL,
                                                  CONSTRAINT sys_version_remind_pk PRIMARY KEY (id)
);
CREATE INDEX sys_version_remind_user_id_idx ON "plss-system".sys_version_remind (user_id);

-- Column comments

COMMENT ON COLUMN "plss-system".sys_version_remind.id IS '主键id';
COMMENT ON COLUMN "plss-system".sys_version_remind.user_id IS '登录用户id';
COMMENT ON COLUMN "plss-system".sys_version_remind.version_no IS '版本号';
COMMENT ON COLUMN "plss-system".sys_version_remind.version_type IS '1前台登录 2后台登录';
COMMENT ON COLUMN "plss-system".sys_version_remind.create_time IS '创建时间';
COMMENT ON COLUMN "plss-system".sys_version_remind.create_by IS '创建人';

-- 定时任务
update "plss-system".xxl_job_info set job_desc = '入库异常自动重试【默认每天0点执行一次】' where executor_handler = 'repairDocProcessStatus';
update "plss-system".xxl_job_info set author = 'xks' where author = '开胜';

-- 新增配置1
INSERT INTO "plss-system"."sys_config"
("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name")
VALUES(298473520220613, '文档类型关联的文件类型', 'record.store.filetype', '{"list":[{"code":1,"name":"文本文件","typeList":[{"suffix":"doc"},{"suffix":"docx"},{"suffix":"wps"},{"suffix":"wpt"},{"suffix":"rtf"},{"suffix":"dot"},{"suffix":"dotx"},{"suffix":"docm"},{"suffix":"dotm"},{"suffix":"uot"},{"suffix":"txt"}]},{"code":2,"name":"演示文件","typeList":[{"suffix":"dps"},{"suffix":"dpt"},{"suffix":"ppt"},{"suffix":"pptx"},{"suffix":"pot"},{"suffix":"pps"},{"suffix":"pptm"},{"suffix":"potm"},{"suffix":"ppsx"},{"suffix":"ppsm"},{"suffix":"uop"}]},{"code":3,"name":"表格文件","typeList":[{"suffix":"et"},{"suffix":"eet"},{"suffix":"xls"},{"suffix":"xlsx"},{"suffix":"xlt"},{"suffix":"xlsm"},{"suffix":"csv"},{"suffix":"xltx"},{"suffix":"dif"},{"suffix":"uos"},{"suffix":"dbf"}]},{"code":4,"name":"版式文件","typeList":[{"suffix":"pdf"},{"suffix":"ofd"},{"suffix":"ceb"},{"suffix":"sep"},{"suffix":"gd"},{"suffix":"gw"},{"suffix":"ps"},{"suffix":"s72"},{"suffix":"s92"},{"suffix":"s10"},{"suffix":"caj"},{"suffix":"cebx"},{"suffix":"xps"}]},{"code":5,"name":"图像文件","typeList":[{"suffix":"bmp"},{"suffix":"jpg"},{"suffix":"jpeg"},{"suffix":"png"},{"suffix":"gif"},{"suffix":"tif"},{"suffix":"tiff"}]},{"code":6,"name":"其他文件","typeList":[{"suffix":"html"},{"suffix":"htm"},{"suffix":"mht"},{"suffix":"mhtml"},{"suffix":"vsd"},{"suffix":"vsdx"},{"suffix":"eml"},{"suffix":"dwg"},{"suffix":"dwt"},{"suffix":"dwf"},{"suffix":"dwxf"}]}]}', 'Y', 1, '2024-09-25 16:45:55.886', 'admin', '2024-09-25 16:45:55.886', 'admin', '', 'json', NULL);

-- 新增配置2
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(307514599507397, '文档入库异常自动重试配置', 'record.auto.retry', '{"commonErrorRetry":true,"commonStopRetry":true,"knowledgeErrorRetry":true,"knowledgeStopRetry":true,"limitMinutes":30,"maxNum":5000,"retryPerCentum":10}', 'Y', 1, '2024-10-08 11:20:03.260', 'admin', '2024-10-08 14:44:16.126', 'admin', 'knowledgeErrorRetry：知识提取异常重试开关 （默认为true，即扫描知识提取异常的文档重试）
knowledgeStopRetry：知识提取停滞重试开关 （默认为true，即扫描知识提取停滞的文档重试）
commonErrorRetry：其他异常重试开关（默认为true，即扫描异常的文档重试）
commonStopRetry：其他停滞重试开关（默认为true，即扫描停滞的文档重试）
limitMinutes：停滞判定时间（分钟），默认30分钟，配置范围不能低于30分钟
retryPerCentum：重试百分比（配置不带%），默认10%
maxNum：重试最大数量，默认5000
每次重试取retryPerCentum 和 maxNum 最大的数量为计划执行数量', NULL, NULL);


-- 版本更新
INSERT INTO "plss-system".sys_config (config_id,config_name,config_key,config_value,config_type,status,create_time,create_by,update_time,update_by,remark,element_type,element_name) VALUES
    (203685690483910,'版本信息','sys.version','{"front":"v2.5.0","backend":"v2.5.0"}','Y',1,'2024-05-14 00:00:00.000','admin','2024-05-14 00:00:00.000','admin','系统版本信息',NULL,NULL);

--版本信息文件
INSERT INTO "plss-system".sys_version
(id, version_no, version_desc, version_type, publish_time, menu_visible, remind_visible, create_time, create_by, version_md)
VALUES(1, 'v2.5.0', '总书记讲话图谱展示；文库管理优化；自定义排版配置；文档类型支持设置文件格式；前台搜索文件框选支持复制和添加素材；元素据项优化；其他ui细节上调整；', '1', '2024-10-08 00:00:00.000', '1', '1', '2024-10-08 00:00:00.000', 1, 'v2.4.0_front.md');
INSERT INTO "plss-system".sys_version
(id, version_no, version_desc, version_type, publish_time, menu_visible, remind_visible, create_time, create_by, version_md)
VALUES(2, 'v2.5.0', '总书记讲话图谱展示；文库管理优化；自定义排版配置；文档类型支持设置文件格式；前台搜索文件框选支持复制和添加素材；元素据项优化；知识模型优化；扫描漏洞项处理；', '2', '2024-10-08 00:00:00.000', '1', '1', '2024-10-08 00:00:00.000', 1, 'v2.4.0_backend.md');



-- 定时清理缓存目录下的文件
INSERT INTO "plss-system".xxl_job_info (id,job_group,job_desc,add_time,update_time,author,alarm_email,schedule_type,schedule_conf,misfire_strategy,executor_route_strategy,executor_handler,executor_param,executor_block_strategy,executor_timeout,executor_fail_retry_count,glue_type,glue_source,glue_remark,glue_updatetime,child_job_id,trigger_status,trigger_last_time,trigger_next_time) VALUES
    (309105719273989,3,'定时清理缓存文件夹下的文件','2024-10-10 17:17:11.743','2024-10-10 17:17:18.576','jianwannian',NULL,'CRON','0 0/5 * * * ?','DO_NOTHING','FIRST','clearApplicationTemp',NULL,'SERIAL_EXECUTION',0,0,'BEAN',NULL,NULL,'2024-10-10 17:17:11.743',NULL,1,0,1728552000000);



-- 新增排版模版菜单
INSERT INTO "plss-system".sys_menu (menu_id,parent_id,menu_name,order_num,"path",component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark,system_menu_flag) VALUES
                                                                                                                                                                                                                                (301826980374341,297764407879173,'排版管理',1,'composing','template-manage/composing','','2','1','C','1','1','','#','admin','2024-09-30 10:28:33.816','','2024-09-30 10:28:33.816','',2),
                                                                                                                                                                                                                                (301832247879493,301826980374341,'新增',1,'','','','2','1','F','1','1','template:composing:add','#','admin','2024-09-30 10:39:16.822','','2024-09-30 10:39:16.822','',2);
-- 新增排版模版租户菜单
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 301826980374341 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 301832247879493 FROM "plss-system".sys_tenant st;


-- 新增角色菜单中间表数据排版模版数据
insert into "plss-system"."sys_role_menu" ("role_id", "menu_id")
values (7, 301826980374341);
insert into "plss-system"."sys_role_menu" ("role_id", "menu_id")
values (7, 301832247879493);


--更新文档基础日期配置
delete from "plss-system".sys_config where config_key = 'record.metadata.base.date.config';
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(269386336201797, '文档基准日期元数据配置', 'record.metadata.base.date.config', '[{"recordTypeId":2221158194437,"recordTypeName":"法律法规","baseDateMetadataId":3574042605061},{"recordTypeId":2781453102085,"recordTypeName":"工作报告","baseDateMetadataId":198625168844293}]', 'Y', 1, '2024-08-15 14:27:54.243', 'admin', '2024-10-11 09:52:47.835', 'admin', '配置文档类型的基准时间
法律法规：公布日期
工作报告：报告日期
裁判文书：裁判日期
其他未配置的默认采用发布日期', NULL, NULL);

--项目配置拆分
delete from "plss-system"."sys_config" where config_id in(
                                                          310316438037957,
                                                          310318678967749,
                                                          310319002109381,
                                                          310319480989125,
                                                          310319940036037,
                                                          310320319219141,
                                                          310320830186949,
                                                          310321439892933);

INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310320830186949, '项目参数配置-内蒙', 'sys.project.config_3', '{"platform":"neimeng","hideSelectPermissionRole":true,"statisticsTimer":3000,"orgId":1610564159749,"tenantId":1612752027141,"statisticsPath":"statistics_nmg","statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""}],"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}],"condition":{"showDownloadButton":"origin==999","showPrintButton":"origin==999"}}', 'Y', 1, '2024-10-12 10:29:20.716', 'admin', '2024-10-12 11:08:02.587', 'admin', '内蒙参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310320319219141, '项目参数配置-浦东', 'sys.project.config_4', '{"platform":"pudong","jumpUrl":"https://ythbgptuat.ywxt.sh.cegn.cn/"}', 'Y', 1, '2024-10-12 10:28:18.342', 'admin', '2024-10-12 11:08:07.740', 'admin', '浦东参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310319480989125, '项目参数配置-国资委', 'sys.project.config_6', '{"platform":"gzw","isLoginUrl":true}', 'Y', 1, '2024-10-12 10:26:36.019', 'admin', '2024-10-12 11:08:16.329', 'admin', '国资委参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL);
insert into "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") values (310321439892933, '项目参数配置-标品', 'sys.project.config_0', '{"platform":"global","pasteCapture":false,"showArticle":true,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledSignVerification":false,"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true,"showChangePassword":true,"showMyInfo":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true,"showChangePassword":true,"showMyInfo":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":true}]}', 'Y', 1, '2024-10-12 10:30:35.144000', 'admin', '2024-10-16 10:00:59.848678', 'admin', '标品参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', null, null);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310319940036037, '项目参数配置-工信部', 'sys.project.config_5', '{"platform":"goxinbu","showFeekBack":{"img":"data:image/png;base64,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","feekbackUrl":"https://f.kdocs.cn/g/A2W3tH5r/"},"recommend":{"rootCategoryId":2804027708677,"defaultSelectId":2804040429573}}', 'Y', 1, '2024-10-12 10:27:32.055', 'admin', '2024-10-12 11:08:12.033', 'admin', '工信部参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310319002109381, '项目参数配置-海南', 'sys.project.config_7', '{"platform":"hainan","isClassified":true}', 'Y', 1, '2024-10-12 10:25:37.562', 'admin', '2024-10-12 11:08:22.174', 'admin', '海南参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', NULL, NULL);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310318678967749, '项目参数配置-西藏高法', 'sys.project.config_9', '{"platform":"xzgf","showLogo":false,"showScreen":true}', 'Y', 1, '2024-10-12 10:24:58.116', 'admin', '2024-10-12 11:08:31.729', 'admin', '西藏高法参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
showScreen: 是否显示我的筛查', NULL, NULL);
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name)
VALUES(310316438037957, '项目参数配置-发改委', 'sys.project.config_1', '{"platform":"ndrc","showNavTop":false}', 'Y', 1, '2024-10-12 10:20:24.565', 'admin', '2024-10-12 11:08:37.202', 'admin', '发改委参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
showNavTop:  是否显示nav模块', NULL, NULL);


-- 文库填充组织机构id任务
DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'fillRepoOrgId';
INSERT INTO "plss-system".xxl_job_info
(id, job_group, job_desc, add_time, update_time,
 author, alarm_email, schedule_type, schedule_conf,
 misfire_strategy, executor_route_strategy, executor_handler,
 executor_param, executor_block_strategy, executor_timeout,
 executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status,
 trigger_last_time, trigger_next_time)
VALUES (309718159960389, 2, '文库填充组织机构id', '2024-10-11 14:03:12.57',
        '2024-10-11 14:03:12.57', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *',
        'DO_NOTHING', 'ROUND', 'fillRepoOrgId', NULL, 'SERIAL_EXECUTION',
        0, 0, 'BEAN', NULL, NULL, '2024-10-11 14:03:12.57', NULL, 0, 0, 0);