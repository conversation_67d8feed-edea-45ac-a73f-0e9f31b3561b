
-- 清理用户行为日志脏数据   （9,10,11,12）四个月，避免830版本在9,10,11,12更新现场出现脏数据
DELETE
FROM
    "plss-system".sys_user_behavior_log_202409
WHERE
    create_time IN ( SELECT create_time FROM "plss-system".sys_user_behavior_log_202409 GROUP BY create_time HAVING COUNT ( * ) > 3 );

DELETE
FROM
    "plss-system".sys_user_behavior_log_202410
WHERE
    create_time IN ( SELECT create_time FROM "plss-system".sys_user_behavior_log_202410 GROUP BY create_time HAVING COUNT ( * ) > 3 );

DELETE
FROM
    "plss-system".sys_user_behavior_log_202411
WHERE
    create_time IN ( SELECT create_time FROM "plss-system".sys_user_behavior_log_202411 GROUP BY create_time HAVING COUNT ( * ) > 3 );

DELETE
FROM
    "plss-system".sys_user_behavior_log_202412
WHERE
    create_time IN ( SELECT create_time FROM "plss-system".sys_user_behavior_log_202412 GROUP BY create_time HAVING COUNT ( * ) > 3 );


-- 新增刷基准时间每秒速率配置
delete from "plss-system"."sys_config" where config_key = 'dp.feature.impl.buttJointOtherKey';
insert into "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") values (3864463331077, '文档处理服务-功能对接其他配置', 'dp.feature.impl.buttJointOtherKey', '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"flushReleaseTimeRate":5,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":1000,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply","aiMiddleAggSearchUrl":"/openApi/aggregations/aggrCountByField","aiMiddleSearchSecretUrl":"/openApi/searchSecretData","aiMiddleBindSegmentAddress":"/generalSecretarySpeech/bindId","aiMiddleUnBindSegmentAddress":"/generalSecretarySpeech/unbindId"}', 'Y', 1, '2024-01-22 17:12:39.892249', '001', '2024-10-16 16:09:56.757759', 'admin', 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭
aiMiddleSearchSecretUrl=ai中台安全文件搜索地址
aiMiddleBindSegmentAddress=ai中台绑定文档片段地址
aiMiddleUnBindSegmentAddress=ai中台解绑绑定文档片段地址
flushReleaseTimeRate=刷基准时间每秒速率', null, null);