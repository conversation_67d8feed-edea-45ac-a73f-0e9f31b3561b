-- 新增安全管理员审批表
CREATE TABLE "plss-system".sys_operate_approve
(
    id BIGINT NOT NULL,
    operate_type SMALLINT NOT NULL,
    approve_status SMALLINT,
    approve_user VARCHAR(50),
    approve_time TIMESTAMP(6),
    create_by VARCHAR(50),
    create_time TIMESTAMP(6),
    operate_info VARCHAR(256),
    operate_value VARCHAR(256),
    approve_user_name VARCHAR(50),
    create_by_name CHAR(10),
    user_id BIGINT,
    tenant_id BIGINT,
    CONSTRAINT sys_operate_approve_pk PRIMARY KEY (id));

COMMENT ON TABLE "plss-system".sys_operate_approve IS '安全管理员审批表';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_status IS '审批状态   1：待审批 2：通过 3：驳回';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_time IS '审批时间';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_user IS '审批人';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_user_name IS '审批人名称';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_by IS '操作人';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_by_name IS '操作人名称';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_time IS '操作时间';
COMMENT ON COLUMN "plss-system".sys_operate_approve.id IS '主键';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_info IS '操作详情';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_type IS '操作类型';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_value IS '操作详情JSON  启用/禁用账号，角色分配用户时 SysOperateInfo ，修改用户信息时 SysOperateInfoUserUpdate';
COMMENT ON COLUMN "plss-system".sys_operate_approve.user_id IS '操作用户id';
COMMENT ON COLUMN "plss-system".sys_operate_approve.tenant_id IS '租户id';


-- 用户表新增审核状态字段
ALTER TABLE "plss-system".sys_user ADD data_act_status char NULL;
COMMENT ON COLUMN "plss-system".sys_user.data_act_status IS '原数据实际状态（1=启用,2=停用）';