UPDATE "plss-system".sys_config SET config_value='{"tabs":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"参考库","ctype":[2]},{"label":"模板&范文样例库","ctype":[4,5]},{"label":"安全文库","ctype":[6]},{"label":"素材库","ctype":[7]}],"query":[]}' WHERE config_key='system.repository.config';

UPDATE "plss-system".sys_config SET config_value='{
	"verifyAk": true,
	"verifyWhiteList": ["/v1/permission/**", "/v1/aiassist/**", "/v1/open/**", "/v1/proofread/**", "/v1/write/assist/search/**", "/v1/auth/login", "/v1/user/getInfo", "/v1/white/**", "/v1/abutment/config/manufacturer_reload", "/v1/am/login/ack", "/v1/app/**", "/v1/redHeadTemplate/**", "/v1/bcr/login/**", "/v1/bitech/login/**", "/v1/clin_brain/login/**", "/v1/oa/auth/dreamIt", "/v2/dream_it/login/**", "/v3/dream_it/login/**", "/v1/hua_di/login/**", "/v2/si/dreamIt/resource/**", "/v2/oa/auth/nwxr/**", "/v1/rise_soft/login/**", "/v1/shenzhouas/login/**", "/v1/sinopec/login/**", "/v1/wof/getInfo", "/v1/gzt/resource/sync", "/v1/oa/auth/dingtalk/login", "/v1/open/u/key/koal/login","/v1/material/queryRepository","/v1/material/queryCategory","/v1/material/queryMaterialPage"],
	"appWhiteList": ["/v1/permission/**", "/v1/aiassist/**", "/v1/open/**", "/v1/proofread/**", "/v1/auth/login", "/v1/user/getInfo", "/v1/auth/refreshToken", "/v1/app/**", "/v1/white/**", "/v1/write/assist/search/**", "/v1/redHeadTemplate/**", "/v1/abutment/config/manufacturer_reload", "/v1/am/login/ack", "/v1/bcr/login/**", "/v1/bitech/login/**", "/v1/clin_brain/login/**", "/v1/oa/auth/dreamIt", "/v2/dream_it/login/**", "/v3/dream_it/login/**", "/v1/hua_di/login/**", "/v2/si/dreamIt/resource/**", "/v2/oa/auth/nwxr/**", "/v1/rise_soft/login/**", "/v1/shenzhouas/login/**", "/v1/sinopec/login/**", "/v1/wof/getInfo", "/v1/gzt/resource/sync", "/v1/oa/auth/dingtalk/login", "/v1/open/u/key/koal/login","/v1/material/queryRepository","/v1/material/queryCategory","/v1/material/queryMaterialPage"]
}' WHERE config_key='open.base.config';

INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(331750094318213, 0, '素材库', '7', 'rc_repo_type', '', 'default', 'N', '1', 'admin', '2024-11-11 17:07:17.685', '', '2024-11-11 17:07:17.685', '存放文本素材');

INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(332292685908613, 'ai素材内置库或自定义库的配置', 'ai.material.type', '2', 'Y', 1, '2024-11-12 11:31:12.010', 'admin', '2024-11-12 11:31:12.010', 'admin', '1：内置库，2：自定义库', NULL, NULL);

