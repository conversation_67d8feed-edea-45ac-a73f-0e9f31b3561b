ALTER TABLE `plss_system`.sys_user
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user
MODIFY COLUMN default_tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_role
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_role
MODIFY COLUMN role_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_role
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_org
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_org
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_org
MODIFY COLUMN tenant_id VARCHAR(256) ;
<PERSON>TER TABLE `plss_system`.sys_user_post
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_category
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_logininfor
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_message
MODIFY COLUMN send_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_message
MODIFY COLUMN accept_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_oper_log
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_oper_log
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_tenant
MODIFY COLUMN id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_search_history
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_message
MODIFY COLUMN send_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_message
MODIFY COLUMN accept_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_org
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_org
MODIFY COLUMN parent_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_org
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_org
MODIFY COLUMN owner_org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_module
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_module
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role_org
MODIFY COLUMN role_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role_org
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_search_collect
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_search_history
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_tenant_menu
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role_menu
MODIFY COLUMN role_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_post
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role_data_pms
MODIFY COLUMN role_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_model
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_model
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_model
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_module_data
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_page_module_data
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_layout
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_layout
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_appearance
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_appearance
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_appearance
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_appearance
MODIFY COLUMN id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_version_remind
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_version_remind
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role
MODIFY COLUMN role_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_role
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_collect
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_operate_approve
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_operate_approve
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_opt_data_pms
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_literary_collect
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_remain
MODIFY COLUMN send_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_remain
MODIFY COLUMN handle_person_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202407
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202408
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202409
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202410
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202411
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202412
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202501
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202502
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202503
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202504
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202505
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202506
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202507
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202508
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202509
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202510
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202511
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202512
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202601
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202602
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202603
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202604
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202605
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202606
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202607
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202608
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202609
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202610
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202611
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202612
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202701
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202702
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202703
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202704
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202705
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202706
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_system`.sys_user_behavior_log_202707
MODIFY COLUMN user_id VARCHAR(256) ;
