delete from plss_system.xxl_job_info where executor_handler in ('convertImportedDocAndUpload','prepareReadDocPackage','scanDocPackage','clearDocPackageFolder','retryUploadDoc');
insert into plss_system.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) values (327302740492421, 199430670180805, '导入至数据库的文档转换为对接结构化并入库', '2024-11-05 10:19:07.809000', '2024-11-05 10:19:07.809000', 'lzm', null, 'CRON', '44 0/3 * * * ?', 'DO_NOTHING', 'FIRST', 'convertImportedDocAndUpload', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-11-05 10:19:07.809000', null, 0, 0, 0);
insert into plss_system.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) values (199432501060037, 199430670180805, '扫描最近更新的文档包', '2024-05-08 18:26:27.721000', '2024-05-23 10:32:29.370000', 'lzm', null, 'CRON', '7 0/3 * * * ?', 'DO_NOTHING', 'FIRST', 'scanDocPackage', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-05-08 18:26:27.721000', null, 0, 0, 0);
insert into plss_system.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) values (199433761563077, 199430670180805, '每天执行一次，清理一周前解压后的文件包', '2024-05-08 18:29:01.596000', '2024-05-08 18:29:01.596000', 'lzm', null, 'CRON', '43 38 3 * * ?', 'DO_NOTHING', 'FIRST', 'clearDocPackageFolder', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-05-08 18:29:01.596000', null, 0, 0, 0);
insert into plss_system.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) values (214215933642693, 199430670180805, '重试上传文档', '2024-05-29 15:43:25.957000', '2024-05-29 15:43:25.957000', 'lzm', null, 'CRON', '0 0/7 * * * ?', 'DO_NOTHING', 'FIRST', 'retryUploadDoc', null, 'SERIAL_EXECUTION', 0, 0, 'BEAN', null, null, '2024-05-29 15:43:25.957000', null, 0, 0, 0);