--AI模板分类

delete from "plss-system".sys_category where id in(294231567683333,
                                                   294231657205509,
                                                   294231720750853,
                                                   230231454655749,
                                                   294231302344453);
delete from "plss-system".sys_category_relation where descendant_id in(
                                                                       294231567683333,
                                                                       294231657205509,
                                                                       294231720750853,
                                                                       230231454655749,
                                                                       294231302344453);

INSERT INTO "plss-system".sys_category
(id, "name", status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state)
VALUES(294231567683333, '征求意见函', '1', 1, '2024-09-19 16:55:39.421', '001', '001', '2024-09-19 16:55:39.421', 1, NULL, 1, 1610564159749, <PERSON>ULL, 2, 1);
INSERT INTO "plss-system".sys_category
(id, "name", status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state)
VALUES(294231657205509, '邀请函', '1', 2, '2024-09-19 16:55:50.349', '001', '001', '2024-09-19 16:55:50.349', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system".sys_category
(id, "name", status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state)
VALUES(294231720750853, '派员函', '1', 3, '2024-09-19 16:55:58.106', '001', '001', '2024-09-19 16:55:58.106', 1, NULL, 1, 1610564159749, NULL, 2, 1);
INSERT INTO "plss-system".sys_category
(id, "name", status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state)
VALUES(230231454655749, 'AI模板', '1', 0, '2024-06-21 06:47:05.566', '001', '001', '2024-06-21 06:47:05.566', 1, NULL, 1, 1610564159749, NULL, 1, 1);
INSERT INTO "plss-system".sys_category
(id, "name", status, order_by, create_time, create_by, update_by, update_time, ctype, remark, visit_type, org_id, image_url, fixed_data, prohibit_state)
VALUES(294231302344453, '简函', '1', 1, '2024-09-19 16:55:07.032', '001', '001', '2024-09-19 16:55:07.032', 1, NULL, 1, 1610564159749, NULL, 1, 1);

--AI模板分类关系
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (230231454655749, 0, 2);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (230231454655749, 187865553923845, 1);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231302344453, 0, 3);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231302344453, 187865553923845, 2);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231302344453, 230231454655749, 1);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231567683333, 0, 4);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231567683333, 187865553923845, 3);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231567683333, 230231454655749, 2);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231567683333, 294231302344453, 1);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231657205509, 0, 4);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231657205509, 187865553923845, 3);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231657205509, 230231454655749, 2);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231657205509, 294231302344453, 1);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231720750853, 0, 4);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231720750853, 187865553923845, 3);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231720750853, 230231454655749, 2);
INSERT INTO "plss-system".sys_category_relation ("descendant_id", "ancestor_id", "distance") VALUES (294231720750853, 294231302344453, 1);


--标品参数更新
delete from "plss-system"."sys_config" where config_key = 'sys.project.config_0';
insert into "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") values (310321439892933, '项目参数配置-标品', 'sys.project.config_0', '{"platform":"global","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledSignVerification":false,"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default","ukey"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":true,"showResultsAdvancedSearch":true,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":true}]}', 'Y', 1, '2024-10-12 10:30:35.144000', 'admin', '2024-10-29 11:16:09.415662', 'admin', '标品参数配置，此配置需要全量更新。
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表', null, null);
