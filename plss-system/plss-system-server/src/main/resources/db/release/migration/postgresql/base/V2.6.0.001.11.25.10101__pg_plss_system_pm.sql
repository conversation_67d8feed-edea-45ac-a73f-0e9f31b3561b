
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (336624241036357, '系统级内置固定元数据', 'fixed.metadata.names.configKey', '[{"fixedMdNameType":101,"fixedMdName":"入库位置"},{"fixedMdNameType":102,"fixedMdName":"系统分类"},{"fixedMdNameType":103,"fixedMdName":"关键词"},{"fixedMdNameType":104,"fixedMdName":"实体机构"},{"fixedMdNameType":104,"fixedMdName":"实体人物"},{"fixedMdNameType":106,"fixedMdName":"实体地点"},{"fixedMdNameType":107,"fixedMdName":"实体日期"},{"fixedMdNameType":108,"fixedMdName":"知识提取"},{"fixedMdNameType":201,"fixedMdName":"标题"},{"fixedMdNameType":202,"fixedMdName":"机构层级"},{"fixedMdNameType":203,"fixedMdName":"年份"},{"fixedMdNameType":204,"fixedMdName":"发布日期"},{"fixedMdNameType":205,"fixedMdName":"密级"}]', 'Y', 1, '2024-11-18 14:23:46.298', 'admin', '2024-11-19 10:25:34.433893', 'admin', '【一般不推荐改系统级内置元数据】
适用系统级固定元数据更改的场景.
fixedMdNameType:系统级固定元数据名称类型【类型编码不能更改固定】
fixedMdName:系统级固定元数据名称【只能修改映射名称】', 'json', NULL);

INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name") VALUES (337467535329925, '管理节点功能点细粒度控制', 'record.node.feature.configKey', '[{"nodeType":1,"nodeFeatures":[{"featId":10001,"featName":"文件上传","featView":1,"featEdit":2},{"featId":10002,"featName":"设置默认入库位置","featView":1,"featEdit":1},{"featId":10003,"featName":"允许入库人员修改入库位置","featView":1,"featEdit":2}]},{"nodeType":4,"nodeFeatures":[{"featId":40001,"featName":"文件转换","featView":1,"featEdit":2},{"featId":40002,"featName":"转换成OFD格式","featView":1,"featEdit":2},{"featId":40003,"featName":"扫描件双层OFD处理","featView":1,"featEdit":1},{"featId":40004,"featName":"提取正文","featView":2,"featEdit":1}]},{"nodeType":5,"nodeFeatures":[{"featId":50001,"featName":"文件拆分","featView":1,"featEdit":1},{"featId":50002,"featName":"系统自动拆分","featView":1,"featEdit":1},{"featId":50003,"featName":"人工拆分","featView":1,"featEdit":1}]},{"nodeType":6,"nodeFeatures":[{"featId":60001,"featName":"元数据自动提取","featView":1,"featEdit":1},{"featId":60002,"featName":"ocr提取元数据","featView":2,"featEdit":1},{"featId":60003,"featName":"模型提取元数据","featView":2,"featEdit":1}]},{"nodeType":7,"nodeFeatures":[{"featId":70001,"featName":"标引信息自动提取","featView":1,"featEdit":1},{"featId":70002,"featName":"摘要提取","featView":1,"featEdit":1},{"featId":70003,"featName":"主题分类","featView":1,"featEdit":1},{"featId":70004,"featName":"主题词提取","featView":1,"featEdit":1},{"featId":70005,"featName":"实体识别","featView":1,"featEdit":1}]},{"nodeType":11,"nodeFeatures":[{"featId":110001,"featName":"人工核对与填充","featView":1,"featEdit":1}]},{"nodeType":8,"nodeFeatures":[{"featId":80001,"featName":"人工审核","featView":1,"featEdit":1},{"featId":80002,"featName":"配置审核人","featView":1,"featEdit":1}]},{"nodeType":9,"nodeFeatures":[{"featId":90001,"featName":"入库","featView":1,"featEdit":2},{"featId":90002,"featName":"系统自动生成封面","featView":1,"featEdit":1}]},{"nodeType":13,"nodeFeatures":[{"featId":130001,"featName":"知识提取","featView":1,"featEdit":1},{"featId":130002,"featName":"知识模型","featView":1,"featEdit":1},{"featId":130003,"featName":"实体对齐","featView":1,"featEdit":1},{"featId":130004,"featName":"自动对齐","featView":1,"featEdit":1},{"featId":130005,"featName":"手动对齐","featView":1,"featEdit":1}]}]', 'Y', 1, '2024-11-19 18:59:27.496', 'admin', '2024-11-20 16:45:05.191309', 'admin', 'nodeType=节点类型
featId=功能编号ID可为雪花算法生成[唯一]
featName=功能名称
featView=1展示，2不展示
featEdit=1编辑,2不可编辑
', 'json', NULL);

-- 标品前端配置 ，个人库上传文件限制、AI中台上报数据内容开关（默认关闭）
update "plss-system".sys_config set config_value = '{"platform":"global","whetherToCollect":false,"personRole":true,"personPost":false,"showWpsPlugin":true,"wpsPluginUrl":"https://edms.suwell.com:30443/plss/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":true,"showResultsAdvancedSearch":true,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":true}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false}]}'
where config_key = 'sys.project.config_0';


-- 代办添加字段
alter table "plss-system"."sys_remain" ADD invalid_status int2 DEFAULT 1;

COMMENT
ON COLUMN "plss-system"."sys_remain".invalid_status IS '效用状态(1有效2无效)';


-- 配置中心添加是否交付必修改项字段
alter table "plss-system"."sys_config" ADD deliver_required_item_flag int2 DEFAULT 1;

COMMENT
ON COLUMN "plss-system"."sys_config".deliver_required_item_flag IS '是否交付必修改项（1:否 2:是）';

update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='sys.domain.url';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='sys.host.port';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='ai.middle.host.port';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='wo.host.port';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='sys.host.context.path';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='plugin.web-office.config';
update "plss-system"."sys_config" set deliver_required_item_flag = 2 where config_key ='ai.platform.config';

-- 动态表头字段长度修改
ALTER TABLE "plss-system".sys_table_header ALTER COLUMN field_key TYPE varchar(64) USING field_key::varchar;
ALTER TABLE "plss-system".sys_table_header ALTER COLUMN field_name TYPE varchar(256) USING field_name::varchar;
ALTER TABLE "plss-system".sys_table_header ALTER COLUMN field_show_name TYPE varchar(256) USING field_show_name::varchar;

-- 添加用户列表动态表头数据脚本
INSERT INTO "plss-system"."sys_table_header_config" ("id", "function_module", "table_type", "table_names", "field_count", "config_type", "repo_id", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark", "code") VALUES (338076401322629, '用户管理', '用户列表', 'sys_user', 0, 0, 0, 1, '1', '1', '2024-11-20 15:38:11.959264', '', '2024-11-20 15:38:11.960543', '', NULL);

INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223002245, 338076401322629, 1, 'userName', '账号', '账号', 0, '', 1, 1, '1', '1', '2024-11-20 16:14:27.456381', '', '2024-11-20 16:14:27.453122', '');
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223002246, 338076401322629, 1, 'nickName', '姓名', '姓名', 0, '', 2, 1, '1', '1', '2024-11-20 16:14:27.457098', '', '2024-11-20 16:14:27.453122', '');
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223010437, 338076401322629, 1, 'orgPaths', '归属部门', '部门名称', 0, '', 3, 1, '1', '1', '2024-11-20 16:14:27.457478', '', '2024-11-20 16:14:27.453122', '');
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223010438, 338076401322629, 1, 'createTime', '创建时间', '创建时间', 0, '', 4, 1, '1', '1', '2024-11-20 16:14:27.457829', '', '2024-11-20 16:14:27.453122', '');
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223010439, 338076401322629, 1, 'updateTime', '更新日期', '更新日期', 0, '', 5, 1, '1', '1', '2024-11-20 16:14:27.458168', '', '2024-11-20 16:14:27.453122', '');
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key", "field_name", "field_show_name", "metadata_category_id", "metadata_category_name", "order_by", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES (338094223026821, 338076401322629, 1, 'status', '帐号状态（1正常 2停用）', '是否启用账号', 0, '', 6, 1, '1', '1', '2024-11-20 16:14:27.460018', '', '2024-11-20 16:14:27.453122', '');

-- file和document分表数据迁移的xxl任务
DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'migrateDoc2Shard';
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) VALUES (331708200758981, 2, 'document分表数据迁移', '2024-11-11 15:42:03.724', '2024-11-11 15:42:03.724', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *', 'DO_NOTHING', 'ROUND', 'migrateDoc2Shard', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-11-11 15:42:03.724', NULL, 0, 0, 0);
DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'migrateFile';
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time) VALUES (331707516128965, 2, 'file分表数据迁移', '2024-11-11 15:40:40.152', '2024-11-11 15:40:40.152', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *', 'DO_NOTHING', 'ROUND', 'migrateFile', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-11-11 15:40:40.152', NULL, 0, 0, 0);

--weboffice相关接口添加到白名单
update "plss-system"."sys_config"
set config_value = '{"verifyAk":true,"verifyWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/write/assist/search/**","/v1/auth/login","/v1/user/getInfo","/v1/white/**","/v1/abutment/config/manufacturer_reload","/v1/am/login/ack","/v1/app/**","/v1/redHeadTemplate/**","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo"],"appWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/auth/login","/v1/user/getInfo","/v1/auth/refreshToken","/v1/app/**","/v1/white/**","/v1/write/assist/search/**","/v1/redHeadTemplate/**","/v1/am/login/ack","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo"]}'
where config_key = 'open.base.config';

--修改保存日志的参数配置
UPDATE "plss-system".sys_config SET config_value='[{"code":1,"modeName":"前台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":2,"modeName":"前台-文档操作","bussineType":6007,"remark":"分享了文档#recordId"},{"code":3,"modeName":"前台-文档操作","bussineType":6002,"remark":"复制了文档#recordId的内容"},{"code":4,"modeName":"前台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"},{"code":11,"modeName":"后台-文档操作","bussineType":6003,"remark":"打印了文档#recordId"},{"code":14,"modeName":"后台-文档操作","bussineType":3015,"remark":"查看了文档#recordId"}]'  WHERE config_key='sys:operLog:type';

--添加新增租户初始化角色配置的sql
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(326767287003525, '租户初始化角色id', 'sys.tenant.init.roleId', '[10002,10003,10004,10005]', 'Y', 1, '2024-11-04 16:09:44.839', 'admin', '2024-11-04 16:09:44.839', 'admin', '租户初始化角色id', 'json', NULL);

--将初始化角色【入库管理员】改为【文库管理员】：其职责是负责后台文库管理、入库管理。
update "plss-system".sys_role set role_name = '文库管理员' where role_id=10007;

--【基础数据管理-文档类型、元数据、分类标签】：非租户隔离，全局共享使用。由运营管理员、文库管理员负责维护。
DELETE FROM "plss-system".sys_role_menu WHERE role_id in (10001,10007) AND
                                              menu_id in (4035890139909,4035893468933,3868784745221,4016792224773,4016795066373,
                                                          680207111173,699369213445,2206751772677,2206732500229,3868085615877,
                                                          3868089044485,3868117296901,3868723818757,3868727505413,3868731898117,
                                                          3868765636613,3868770451461,3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 680207111173);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 699369213445);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 2206732500229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 2206751772677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868085615877);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868089044485);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868117296901);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868723818757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868727505413);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868731898117);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868765636613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868770451461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3868784745221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 4016792224773);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 4016795066373);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 4035890139909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 4035893468933);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 680207111173);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 699369213445);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 2206732500229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 2206751772677);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868085615877);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868089044485);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868117296901);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868723818757);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868727505413);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868731898117);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868765636613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868770451461);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868775889669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 3868784745221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 4016792224773);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 4016795066373);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 4035890139909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 4035893468933);

--1130追加：系统初始化数据权限如下，标品为该初始化设置。支持各项目按需调整。
--（1）文库管理员：本单位及下级单位数据权限（租户内）
--（2）借阅审批人：本单位及下级单位数据权限（租户内）
update "plss-system".sys_role_data_pms set data_pms_id = 337463577963141
where data_pms_id  = (select id from sys_opt_data_pms sodp where sodp.pms_type = 5 and sodp.module_type = 1);

update "plss-system".sys_role_data_pms set data_pms_id = 337464047233669
where data_pms_id  = (select id from sys_opt_data_pms sodp where sodp.pms_type = 5 and sodp.module_type = 2);

update "plss-system".sys_role_data_pms set data_pms_id = 337464117529221
where data_pms_id  = (select id from sys_opt_data_pms sodp where sodp.pms_type = 5 and sodp.module_type = 3);

update "plss-system".sys_role_data_pms set data_pms_id = 337464178870917
where data_pms_id  = (select id from sys_opt_data_pms sodp where sodp.pms_type = 5 and sodp.module_type = 5);

update "plss-system".sys_role_data_pms set data_pms_id = 337464272972421
where data_pms_id  = (select id from sys_opt_data_pms sodp where sodp.pms_type = 5 and sodp.module_type = 6);

DELETE FROM "plss-system".sys_opt_data_pms WHERE pms_type = 5 and module_type in (1,2,3,5,6);

INSERT INTO "plss-system".sys_opt_data_pms (id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(337464272972421, 6, '安全入库任务-查看-本单位及下级单位数据权限（租户内）', 5, 1, '1', '2024-11-19 18:52:49.257', '2024-11-19 18:52:49.257', 0, 1);
INSERT INTO "plss-system".sys_opt_data_pms (id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(337464178870917, 5, '安全文档入库-查看-本单位及下级单位数据权限（租户内）', 5, 1, '1', '2024-11-19 18:52:37.770', '2024-11-19 18:52:37.770', 0, 1);
INSERT INTO "plss-system".sys_opt_data_pms (id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(337464117529221, 3, '入库方案配置-查看-本单位及下级单位数据权限（租户内）', 5, 1, '1', '2024-11-19 18:52:30.282', '2024-11-19 18:52:30.282', 0, 1);
INSERT INTO "plss-system".sys_opt_data_pms (id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(337464047233669, 2, '入库任务-查看-本单位及下级单位数据权限（租户内）', 5, 1, '1', '2024-11-19 18:52:21.701', '2024-11-19 18:52:21.701', 0, 1);
INSERT INTO "plss-system".sys_opt_data_pms (id, module_type, pms_name, pms_type, status, operate_type, create_time, modified_time, tenant_id, rtype) VALUES(337463577963141, 1, '文档入库-查看-本单位及下级单位数据权限（租户内）', 5, 1, '1', '2024-11-19 18:51:24.414', '2024-11-19 18:51:24.414', 0, 1);

DELETE FROM "plss-system".sys_role_data_pms WHERE role_id in (10007,10008);

INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10007, 337463577963141);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10007, 337464047233669);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10007, 337464117529221);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10007, 337464178870917);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10007, 337464272972421);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10008, 337463577963141);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10008, 337464047233669);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10008, 337464117529221);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10008, 337464178870917);
INSERT INTO "plss-system".sys_role_data_pms (role_id, data_pms_id) VALUES(10008, 337464272972421);

--【系统管理-授权信息查看】：标品默认给【运营管理员】、【系统管理员】
--（1）运营管理员看所有
--（2）系统管理员看租户自己的
DELETE FROM "plss-system".sys_role_menu WHERE role_id in (10001,10002) AND menu_id=3331323127557;
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 3331323127557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10002, 3331323127557);

--增加非密系统提示语
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(341476730601925, '非密系统提示语', ' sys.noConfidential.prompt', '注意：非密系统，请勿上传涉密文件', 'Y', 1, '2024-11-25 10:56:11.216', 'admin', '2024-11-25 10:56:11.216', 'admin', '非密系统提示语', 'text', NULL, 1);




ALTER TABLE "plss-system".sys_page_module ADD module_type int2 DEFAULT 0 NOT NULL;
COMMENT ON COLUMN "plss-system".sys_page_module.module_type IS '组件类型';

CREATE TABLE "plss-system".sys_page_module_data (
                                                    id int8 NOT NULL, -- id
                                                    model_id int8 NOT NULL, -- 模型id
                                                    module_code varchar(50) NOT NULL, -- 组件code
                                                    entry_module_code varchar(50) NULL, -- 入口组件code
                                                    module_type int2 DEFAULT 0 NOT NULL, -- 组件类型
                                                    data_type int2 DEFAULT 0 NOT NULL, -- 数据类型
                                                    data_name varchar(50) NOT NULL, -- 数据名称
                                                    data_config text NULL, -- 数据配置
                                                    create_time timestamp NULL, -- 创建时间
                                                    update_time timestamp NULL, -- 更新时间
                                                    create_by int8 NULL, -- 创建人
                                                    update_by int8 NULL, -- 更新人
                                                    CONSTRAINT sys_page_module_data_pk PRIMARY KEY (id)
);
COMMENT ON TABLE "plss-system".sys_page_module_data IS '页面组件数据表';
COMMENT ON COLUMN "plss-system".sys_page_module_data.id IS 'id';
COMMENT ON COLUMN "plss-system".sys_page_module_data.model_id IS '模型id';
COMMENT ON COLUMN "plss-system".sys_page_module_data.module_code IS '组件code';
COMMENT ON COLUMN "plss-system".sys_page_module_data.entry_module_code IS '入口组件code';
COMMENT ON COLUMN "plss-system".sys_page_module_data.module_type IS '组件类型';
COMMENT ON COLUMN "plss-system".sys_page_module_data.data_type IS '数据类型';
COMMENT ON COLUMN "plss-system".sys_page_module_data.data_name IS '数据名称';
COMMENT ON COLUMN "plss-system".sys_page_module_data.data_config IS '数据配置';
COMMENT ON COLUMN "plss-system".sys_page_module_data.create_time IS '创建时间';
COMMENT ON COLUMN "plss-system".sys_page_module_data.update_time IS '更新时间';
COMMENT ON COLUMN "plss-system".sys_page_module_data.create_by IS '创建人';
COMMENT ON COLUMN "plss-system".sys_page_module_data.update_by IS '更新人';

delete from "plss-system".sys_page_module;
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964391824581, '{
  "label": "文库列表",
  "layout": {
    "w": 18,
    "h": 5,
    "minH": 3,
    "minW": 3
  },
  "node": {
    "type": "l-container",
    "moduleType": "library-container",
    "children": [],
    "config": {
      "label": "文库列表",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-22 15:36:29.236', 1612860642053, '2024-11-24 17:09:45.992', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275043279088837, '{
  "label": "首页搜索",
  "node": {
    "type": "c-home-search",
    "moduleType": "c-home-search",
    "config": {
      "disabledTabs": false,
      "disabledHotWord": false,
      "disabledSearchArticle": false,
      "disabledLibrary": false,
      "disabledAdvancedSearch": false,
      "href": "",
      "hotWordLabel": "热搜词",
      "tabs": [
        {
          "label": "智能搜索",
          "id": 0,
          "name": "intelligent-search",
          "show": true
        },
        {
          "label": "政务问答",
          "id": 1,
          "name": "chat",
          "show": true
        },
        {
          "label": "公文写作",
          "id": 2,
          "name": "write",
          "show": true
        }
      ],
      "hotWordData": [
        "静态搜索词"
      ],
      "hotWordType": 1
    }
  },
  "layout": {
    "w": 20,
    "h": 5,
    "minH": 3,
    "minW": 20
  }
}', '2024-08-22 14:42:59.029', 1612860642053, '2024-11-25 10:07:12.306', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276655948180677, '{
  "label": "智能应用",
  "layout": {
    "w": 3,
    "h": 3,
    "minW": 3,
    "minH": 3
  },
  "node": {
    "type": "l-button",
    "moduleType": "application",
    "config": {
      "label": "智能应用",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 28,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-11-25 10:33:32.217', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276468841450693, '{
  "label": "容器组件",
  "layout": {
    "w": 24,
    "h": 8,
    "minH": 8,
    "minW": 4
  },
  "node": {
    "type": "l-container",
    "children": [],
    "config": {
      "label": "",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-25 14:37:17.871', 1612860642053, '2024-11-23 14:02:39.380', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964214222021, '{
  "label": "文库入口",
  "layout": {
    "w": 3,
    "h": 3,
    "minW": 3,
    "minH": 3
  },
  "node": {
    "type": "l-button",
    "moduleType": "library",
    "config": {
      "label": "我的文库",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 28,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-11-25 10:33:27.365', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337476010691205, '{
  "label": "图片组件",
  "layout": {
    "w": 12,
    "h": 6
  },
  "node": {
    "type": "l-image",
    "config": {
      "icon": "/plss/front/static/themes/red/index_bg.jpg",
      "isBottom": false
    }
  }
}', '2024-11-19 19:16:42.086', 1, '2024-11-25 10:33:42.103', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(338118581332613, '{
  "label": "分类组件",
  "node": {
    "type": "l-search-classify",
    "moduleType": "l-search-classify",
    "config": {
      "isCollapseOpen": "openFirst"
    }
  },
  "layout": {
    "w": 10,
    "h": 15,
    "minH": 15,
    "minW": 10
  }
}', '2024-11-20 17:04:00.885', 1, '2024-11-25 10:08:28.998', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275044225289413, '{
  "label": "列表组件",
  "node": {
    "type": "l-lists",
    "moduleType": "lists",
    "config": {
      "label": "列表标题",
      "showDigest": false,
      "showTime": false,
      "showMetadata": false,
      "pageSize": 10,
      "requestPath": "queryLatestRecordList",
      "icon": "",
      "pageType": 0
    }
  },
  "layout": {
    "w": 18,
    "h": 6,
    "minH": 5,
    "minW": 14
  }
}', '2024-08-22 17:18:54.532', 1612860642053, '2024-11-25 10:08:51.846', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276471564602565, '{
  "label": "LOGO",
  "node": {
    "type": "l-button",
    "moduleType": "logo",
    "config": {
      "label": "电子公文库",
      "icon": "/plss/front/static/themes/default/index-second-logo.svg",
      "iconSize": 28,
      "fontWeight": 600,
      "fontSize": 18,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": true,
      "href": ""
    }
  },
  "layout": {
    "w": 10,
    "h": 3,
    "minW": 4,
    "minH": 2
  }
}', '2024-08-22 14:42:50.288', 1612860642053, '2024-11-25 10:33:13.285', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337475096447621, '{
  "label": "筛选组件",
  "layout": {
    "w": 26,
    "h": 4,
    "minH": 4,
    "minW": 26
  },
  "node": {
    "type": "c-detail-search",
    "moduleType": "c-detail-search",
    "config": {
      "disabledAllOption": false,
      "disabledSearchArticle": false,
      "disabledLibrary": true,
      "disabledAdvancedSearch": false,
      "disabledMultiple": false
    }
  }
}', '2024-11-20 19:14:50.482', 1, '2024-11-25 13:00:21.563', 299806062537861, 0);

UPDATE "plss-system"."sys_config" SET "config_name" = '主题设置', "config_key" = 'sys.appearance.theme', "config_value" = '[{"name":"素雅白","flodername":"default","value":"1","ischeckd":false,"bacgroundcolor":"#efefef"},{"name":"星耀红","flodername":"red","value":"2","ischeckd":true,"bacgroundcolor":"#C54334"},{"name":"智慧蓝","flodername":"blue","value":"3","ischeckd":false,"bacgroundcolor":"#1F50DB"},{"name":"军工绿","flodername":"armyGreen","value":"4","ischeckd":false,"bacgroundcolor":"#1E8C69"},{"name":"科技蓝","flodername":"techBlue","value":"5","ischeckd":false,"bacgroundcolor":"#4D7AD8"}]', "config_type" = 'Y', "status" = 1, "create_time" = '2023-09-08 00:00:00', "create_by" = 'admin', "update_time" = '2024-11-25 16:41:12.408117', "update_by" = 'admin', "remark" = '请选择颜色', "element_type" = 'color', "element_name" = '主题颜色', "deliver_required_item_flag" = 1 WHERE "config_id" = 849473740037;
