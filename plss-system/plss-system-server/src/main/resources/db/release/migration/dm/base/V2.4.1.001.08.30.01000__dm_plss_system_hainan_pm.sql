-- 操作日志添加用户id字段
ALTER TABLE "plss-system".sys_oper_log
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_oper_log.user_id IS '用户ID';

-- 登录日志添加用户id字段
ALTER TABLE "plss-system".sys_logininfor
    ADD user_id BIGINT;
COMMENT
ON COLUMN "plss-system".sys_logininfor.user_id IS '用户ID';

-- 创建待办表sys_remain
CREATE TABLE "plss-system".sys_remain (
  id BIGINT NOT NULL,
  "type" char(1) NOT NULL,
  status char(1) DEFAULT 2 NOT NULL,
  title varchar(256) NOT NULL,
  send_time timestamp NOT NULL,
  "content" text NOT NULL,
  create_time timestamp DEFAULT now() NOT NULL,
  update_time timestamp DEFAULT now() NOT NULL,
  del_flag char(1) DEFAULT 1 NOT NULL,
  accept_person_id text NOT NULL,
  send_person_id BIGINT NOT NULL,
  handle_person_id BIGINT ,
  business_id BIGINT NULL,
  check_status VARCHAR(1),
  reject_reason VARCHAR(256),
  CONSTRAINT sys_remain_pk PRIMARY KEY (id)
);
CREATE INDEX remain_business_id_idx ON "plss-system".sys_remain (business_id);
CREATE INDEX remain_type_idx ON "plss-system".sys_remain ("type");

COMMENT
ON COLUMN "plss-system".sys_remain.id IS '主键';
COMMENT
ON COLUMN "plss-system".sys_remain."type" IS '待办类型：1入库审核，2借阅审批';
COMMENT
ON COLUMN "plss-system".sys_remain.status IS '待办状态：1未处理，2已处理，3已撤回';
COMMENT
ON COLUMN "plss-system".sys_remain.title IS '待办标题';
COMMENT
ON COLUMN "plss-system".sys_remain.send_time IS '发送日期';
COMMENT
ON COLUMN "plss-system".sys_remain."content" IS '待办内容';
COMMENT
ON COLUMN "plss-system".sys_remain.del_flag IS '删除标记：1未删除，2已删除';
COMMENT
ON COLUMN "plss-system".sys_remain.accept_person_id IS '收件人id，多个收件人用逗号分隔';
COMMENT
ON COLUMN "plss-system".sys_remain.send_person_id IS '发送人id';
COMMENT
ON COLUMN "plss-system".sys_remain.handle_person_id IS '办理人id';
COMMENT
ON COLUMN "plss-system".sys_remain.business_id IS '业务id,入库审核的待办为recordId,借阅审批的待办为借阅id';

alter table "plss-system"."sys_message" add column("repo_name" VARCHAR(256));

comment on column "plss-system"."sys_message"."repo_name" is '文库名称';

-- 用户表添加人员密级字段
ALTER TABLE "plss-system".sys_user
    ADD classified int;
COMMENT
ON COLUMN "plss-system"."sys_user"."classified" IS '人员密级';
-- sys_user_behavior_log分表
CREATE TABLE "plss-system"."sys_user_behavior_log_202407"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202407_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202407_idx ON "plss-system".sys_user_behavior_log_202407 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202408"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202408_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202408_idx ON "plss-system".sys_user_behavior_log_202408 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202409"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202409_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202409_idx ON "plss-system".sys_user_behavior_log_202409 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202410"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202410_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202410_idx ON "plss-system".sys_user_behavior_log_202410 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202411"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202411_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202411_idx ON "plss-system".sys_user_behavior_log_202411 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202412"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202412_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202412_idx ON "plss-system".sys_user_behavior_log_202412 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202501"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202501_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202501_idx ON "plss-system".sys_user_behavior_log_202501 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202502"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202502_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202502_idx ON "plss-system".sys_user_behavior_log_202502 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202503"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202503_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202503_idx ON "plss-system".sys_user_behavior_log_202503 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202504"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202504_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202504_idx ON "plss-system".sys_user_behavior_log_202504 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202505"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202505_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202505_idx ON "plss-system".sys_user_behavior_log_202505 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202506"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202506_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202506_idx ON "plss-system".sys_user_behavior_log_202506 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202507"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202507_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202507_idx ON "plss-system".sys_user_behavior_log_202507 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202508"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202508_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202508_idx ON "plss-system".sys_user_behavior_log_202508 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202509"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202509_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202509_idx ON "plss-system".sys_user_behavior_log_202509 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202510"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202510_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202510_idx ON "plss-system".sys_user_behavior_log_202510 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202511"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202511_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202511_idx ON "plss-system".sys_user_behavior_log_202511 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202512"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202512_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202512_idx ON "plss-system".sys_user_behavior_log_202512 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202601"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202601_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202601_idx ON "plss-system".sys_user_behavior_log_202601 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202602"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202602_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202602_idx ON "plss-system".sys_user_behavior_log_202602 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202603"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202603_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202603_idx ON "plss-system".sys_user_behavior_log_202603 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202604"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202604_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202604_idx ON "plss-system".sys_user_behavior_log_202604 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202605"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202605_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202605_idx ON "plss-system".sys_user_behavior_log_202605 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202606"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202606_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202606_idx ON "plss-system".sys_user_behavior_log_202606 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202607"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202607_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202607_idx ON "plss-system".sys_user_behavior_log_202607 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202608"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202608_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202608_idx ON "plss-system".sys_user_behavior_log_202608 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202609"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202609_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202609_idx ON "plss-system".sys_user_behavior_log_202609 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202610"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202610_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202610_idx ON "plss-system".sys_user_behavior_log_202610 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202611"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202611_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202611_idx ON "plss-system".sys_user_behavior_log_202611 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202612"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202612_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202612_idx ON "plss-system".sys_user_behavior_log_202612 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202701"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202701_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202701_idx ON "plss-system".sys_user_behavior_log_202701 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202702"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202702_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202702_idx ON "plss-system".sys_user_behavior_log_202702 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202703"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202703_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202703_idx ON "plss-system".sys_user_behavior_log_202703 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202704"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202704_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202704_idx ON "plss-system".sys_user_behavior_log_202704 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202705"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202705_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202705_idx ON "plss-system".sys_user_behavior_log_202705 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202706"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202706_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202706_idx ON "plss-system".sys_user_behavior_log_202706 (user_id);
CREATE TABLE "plss-system"."sys_user_behavior_log_202707"
(
    "id"              BIGINT       NOT NULL,
    "project_name"    VARCHAR(64)  NOT NULL,
    "gwk_version"     VARCHAR(64)  NOT NULL,
    "event_name"      VARCHAR(256) NOT NULL,
    "user_id"         BIGINT       NOT NULL,
    "user_name"       VARCHAR(64)  NOT NULL,
    "model"           VARCHAR(64),
    "os"              VARCHAR(64),
    "os_version"      VARCHAR(64),
    "browser"         VARCHAR(64),
    "browser_version" VARCHAR(64),
    "screen_height"   INT          NOT NULL,
    "screen_width"    INT          NOT NULL,
    "ip"              VARCHAR(64)  NOT NULL,
    "data"            TEXT         NOT NULL,
    "create_year"     INT          NOT NULL,
    "create_month"    INT          NOT NULL,
    "create_day"      INT          NOT NULL,
    "create_hour"     INT          NOT NULL,
    "create_minute"   INT          NOT NULL,
    "create_second"   INT          NOT NULL,
    "create_time"     TIMESTAMP(6) NOT NULL,
    "status"          INT          NOT NULL DEFAULT 1,
    "del_flag"        INT          NOT NULL DEFAULT 1,
    "remark"          VARCHAR(512),
    CONSTRAINT "sys_user_behavior_log_202707_pkey" PRIMARY KEY ("id")
);
CREATE INDEX user_id_202707_idx ON "plss-system".sys_user_behavior_log_202707 (user_id);
CREATE TABLE "plss-system"."sys_page_model"
(
    "id"                BIGINT              NOT NULL,
    "model_name"        VARCHAR(100),
    "model_url"         VARCHAR(200),
    "model_type"        SMALLINT DEFAULT 0,
    "model_description" VARCHAR(500),
    "model_config"      TEXT,
    "is_enable"         SMALLINT DEFAULT 0,
    "tenant_id"         BIGINT,
    "create_time"       TIMESTAMP(6),
    "create_by"         BIGINT,
    "update_time"       TIMESTAMP(6),
    "update_by"         BIGINT,
    CONSTRAINT          "sys_page_model_pk" NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "plss-system"."sys_page_model" IS '页面模型表';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."create_by" IS '创建人';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."create_time" IS '创建时间';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."is_enable" IS '是否启用。0：否 1：是';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."model_config" IS '页面模型配置';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."model_description" IS '页面模型说明';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."model_name" IS '页面模型名称';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."model_type" IS '页面模型类型。0：普通 1：首页';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."model_url" IS '页面模型路由';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."tenant_id" IS '租户id';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."update_by" IS '更新人';
COMMENT
ON COLUMN "plss-system"."sys_page_model"."update_time" IS '更新时间';


CREATE TABLE "plss-system"."sys_page_module"
(
    "id"            BIGINT               NOT NULL,
    "module_config" TEXT,
    "create_time"   TIMESTAMP(6),
    "create_by"     BIGINT,
    "update_time"   TIMESTAMP(6),
    "update_by"     BIGINT,
    CONSTRAINT      "sys_page_module_pk" NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT
ON TABLE "plss-system"."sys_page_module" IS '页面组件表';
COMMENT
ON COLUMN "plss-system"."sys_page_module"."create_by" IS '创建人';
COMMENT
ON COLUMN "plss-system"."sys_page_module"."create_time" IS '创建时间';
COMMENT
ON COLUMN "plss-system"."sys_page_module"."module_config" IS '组件配置';
COMMENT
ON COLUMN "plss-system"."sys_page_module"."update_by" IS '更新人';
COMMENT
ON COLUMN "plss-system"."sys_page_module"."update_time" IS '更新时间';


ALTER TABLE "plss-system".sys_user
    ADD certificate_no varchar(100) NULL;
COMMENT
ON COLUMN "plss-system".sys_user.certificate_no IS '证书序列号';
-- DML

-- 标品需求

-- 新增”入库位置”默认非必填，并不可更改
UPDATE "plss-system".sys_config
SET "config_value" = '[{"categoryId":1160484769029,"id":1121765356037,"label":"实体日期","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121757193221,"label":"实体地点","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121745177861,"label":"实体人物","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121736301573,"label":"实体机构","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121727166725,"label":"关键词","value":{"required":2},"disabled":["required"]},{"categoryId":1160484769029,"id":1121716339461,"label":"系统分类","value":{"required":2},"disabled":["required"]},{"categoryId":1160482904581,"id":2716890001157,"label":"入库位置","value":{"required":2},"disabled":["required"]}]'
WHERE config_key = 'sys.default.metadta';

--填充操作日志的user_id的数据
UPDATE "plss-system".sys_oper_log AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.oper_name = b.user_name;


--填充登录日志user_id的数据
UPDATE "plss-system".sys_logininfor AS a
SET user_id = b.user_id FROM "plss-system".sys_user AS b
WHERE a.user_id is null and a.user_name = b.user_name;

-- 监管分析
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (267352032095813, '监管分析统计数据分布内置数据更新的日期范围',
        'record.supervise.analysis.weekMonthYear', '1', 'Y', 1, '2024-08-12 17:29:06.105', 'admin',
        '2024-08-12 17:29:06.105', 'admin', '1-本周更新的数据
2-本月更新的数据', 'text', NULL);

-- ai中台需要加密的字段配置
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (263599265514117, 'ES-es索引库加密字段', 'dp.es.encrypt.fields', '["title"]', 'Y', 1,
        '2024-08-07 10:14:04.716', 'admin', '2024-08-07 10:39:32.569453', 'admin',
        'ES-es索引库加密字段,没有加密字段则为空数组:[]', 'json', NULL);

-- AI实体关键词字数限制
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (279425554973509, 'AI实体关键词字数限制', 'record.ai.entity.keyword.limit', '15', 'Y', 1,
        '2024-08-29 18:52:44.817', 'admin', '2024-08-29 18:52:44.817', 'admin',
        'AI实体关键词字数限制', 'text', NULL);

-- 对接ai中台配置 新增ai中台绑定文档片段地址
UPDATE "plss-system"."sys_config"
SET "config_value" = '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":1000,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply","aiMiddleAggSearchUrl":"/openApi/aggregations/aggrCountByField","aiMiddleSearchSecretUrl":"/openApi/searchSecretData","aiMiddleBindSegmentAddress":"/generalSecretarySpeech/bindId","aiMiddleUnBindSegmentAddress":"/generalSecretarySpeech/unbindId"}',
    "remark"       = 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭
aiMiddleSearchSecretUrl=ai中台安全文件搜索地址
aiMiddleBindSegmentAddress=ai中台绑定文档片段地址
aiMiddleUnBindSegmentAddress=ai中台解绑绑定文档片段地址'
WHERE "config_key" = 'dp.feature.impl.buttJointOtherKey';


--新增监管分析菜单
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (258049632232069, 0, '监管分析', 15, 'supervise', 'supervise/index', '', '1', '1', 'C', '1',
        '1', '', 'icon-supervise', '001', '2024-07-30 14:03:19.252', '001',
        '2024-08-05 16:56:30.815', '', 2);
--监管分析菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 258049632232069
FROM "plss-system".sys_tenant st;

--运营管理员和入库管理员绑定监管分析菜单
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (1, 258049632232069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id)
VALUES (7, 258049632232069);

--消息类型字典配置
delete
from "plss-system".sys_dict_data
where dict_type = 'sys_message_type';

INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (4019390280965, 3, '文档移除', '4', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-01-29 17:19:03.286', '001', '2024-08-16 11:14:36.342', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (4019378222597, 1, '所有者变更', '1', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-01-29 17:18:16.184', '001', '2024-08-16 11:13:38.083', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (4019381607685, 2, '协同管理', '2,3', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-01-29 17:18:29.406', '001', '2024-08-16 11:13:58.838', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (4019387652613, 4, '入库驳回', '6', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-01-29 17:18:53.019', '001', '2024-08-16 11:15:35.179', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (192258864358341, 9, '授权策略变更', '11', 'sys_message_type', '', 'default', 'N', '1',
        '001', '2024-04-28 15:11:39.654', '001', '2024-08-16 11:20:11.815', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (270000615361925, 6, '借阅到期', '9', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-08-16 11:17:39.492', '', '2024-08-16 11:17:39.492', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (270000939240837, 7, '文档收回', '10', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-08-16 11:18:19.028', '', '2024-08-16 11:18:19.028', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (267869615467909, 8, '审批通过', '5,7', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-08-13 11:02:07.669', '001', '2024-08-16 11:19:17.316', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (264533649523397, 11, '文件分享', '13', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-08-08 17:55:05.264', '001', '2024-08-16 11:21:26.642', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (267869438324101, 5, '借阅驳回', '8', 'sys_message_type', '', 'default', 'N', '1', '001',
        '2024-08-13 11:01:46.045', '001', '2024-08-16 11:22:47.921', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (270002172513669, 10, '存储空间告警', '12', 'sys_message_type', '', 'default', 'N', '1',
        '001', '2024-08-16 11:20:49.574', '001', '2024-08-16 11:24:38.628', '');

--代办字典配置
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time,
                                         create_by, update_time, update_by, remark)
VALUES (263804346173509, '待办类型', 'sys_remain_type', '1', '2024-08-07 17:11:18.978', 'admin',
        '2024-08-07 17:11:18.978', '', '待办类型');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time,
                                         create_by, update_time, update_by, remark)
VALUES (269388841952517, '审批状态', 'sys_remain_check_status', '1', '2024-08-15 14:33:00.124',
        'admin', '2024-08-15 14:50:06.200', 'admin', '审批状态：1已审批、2待审批、3已驳回');
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time,
                                         create_by, update_time, update_by, remark)
VALUES (270194295702981, '待办审批结果', 'sys_remain_check_result', '1', '2024-08-16 17:51:42.116',
        '001', '2024-08-16 17:51:42.116', '', '');

INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (263804959074373, 1, '入库审批', '1', 'sys_remain_type', '', 'default', 'N', '1', 'admin',
        '2024-08-07 17:12:33.794', '001', '2024-08-08 10:12:11.531', '入库审批待办');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (263805087434821, 2, '借阅审批', '2', 'sys_remain_type', '', 'default', 'N', '1', 'admin',
        '2024-08-07 17:12:49.462', 'admin', '2024-08-07 17:13:08.999', '借阅审批待办');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269389662536965, 1, '已审批', '1', 'sys_remain_check_status', '', 'default', 'N', '1',
        'admin', '2024-08-15 14:34:40.291', 'admin', '2024-08-15 14:50:20.411', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269389760808197, 2, '待审批', '2', 'sys_remain_check_status', '', 'default', 'N', '1',
        'admin', '2024-08-15 14:34:52.286', 'admin', '2024-08-15 14:50:33.003', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269397543970053, 3, '已驳回', '3', 'sys_remain_check_status', '', 'default', 'N', '1',
        'admin', '2024-08-15 14:50:42.379', '', '2024-08-15 14:50:42.379', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (270194513774021, 1, '审批通过', '1', 'sys_remain_check_result', '', 'default', 'N', '1',
        '001', '2024-08-16 17:52:08.732', '001', '2024-08-18 08:38:05.839', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (270194831164869, 2, '审批驳回', '2', 'sys_remain_check_result', '', 'default', 'N', '1',
        '001', '2024-08-16 17:52:47.476', '001', '2024-08-18 08:38:15.226', '');


--新增读缓存的磁盘信息发送告警消息的定时任务
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status,
                                        trigger_last_time, trigger_next_time)
VALUES (263608483480197, 3, '磁盘告警信息定时任务', '2024-08-07 10:32:49.953',
        '2024-08-07 10:33:06.145', 'wmd', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST',
        'diskAlarmMessage', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-08-07 10:32:49.953', NULL, 1, 0, 1723050000000);

--新增磁盘告警信息阈值的参数设置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name)
VALUES (263038234037893, '磁盘告警阈值', 'system.disk.threshold', '1048576', 'Y', 1,
        '2024-08-06 15:12:39.428', 'admin', '2024-08-07 11:24:52.653', 'admin',
        '单位kb，1G = 1024*1024kb = 1048576kb ', NULL, NULL);


--修改我的文库表头配置
delete
from "plss-system"."sys_table_header"
where id = 192133214562309;
INSERT INTO "plss-system"."sys_table_header" ("id", "config_id", "field_type", "field_key",
                                              "field_name", "field_show_name",
                                              "metadata_category_id", "metadata_category_name",
                                              "order_by", "status", "del_flag", "create_by",
                                              "create_time", "update_by", "update_time", "remark")
VALUES (192133214562309, 2398227485702, 1, 'modifyTime', '修改时间', '最后修改时间', 0, '', 2, 1,
        '1', '1612860642053', '2024-04-28 10:56:01.543', '', '2024-04-28 10:56:01.550691', '');

--文档类型基准时间配置
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (269386336201797, '文档基准日期元数据配置', 'record.metadata.base.date.config',
        '[{"recordTypeId":2221158194437,"recordTypeName":"法律法规","baseDateMetadataId":3574045886213},{"recordTypeId":2781453102085,"recordTypeName":"工作报告","baseDateMetadataId":198625168844293},{"recordTypeId":227683451972741,"recordTypeName":"党纪法规","baseDateMetadataId":3574045886213}]',
        'Y', 1, '2024-08-15 14:27:54.243', 'admin', '2024-08-15 17:07:21.31645', 'admin', '配置文档类型的基准时间
法律法规：施行日期
工作报告：报告日期
党纪法规：施行日期
裁判文书：裁判日期
其他未配置的默认采用发布日期', NULL, NULL);

--更新文档基准时间xxljob
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (272919543908357, 2, '更新文档基准时间', '2024-08-20 14:16:14.008',
        '2024-08-20 14:16:14.008', 'yangjun', NULL, 'CRON', '0 0 1 * * ?', 'DO_NOTHING', 'FIRST',
        'repairRecordReleaseTimeToEs', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-08-20 14:16:14.008', NULL, 0, 0, 0);

--更新参数配置
delete
from "plss-system"."sys_config"
where config_key = 'sys.front.config';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (192183311754501, '前端参数配置', 'sys.front.config',
        '[{"projectId":1,"config":{"platform":"ndrc","showNavTop":false}},{"projectId":6,"config":{"platform":"gzw","isLoginUrl":true}},{"projectId":5,"config":{"platform":"goxinbu","showFeekBack":{"img":"data:image/png;base64,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","feekbackUrl":"https://f.kdocs.cn/g/A2W3tH5r/"},"recommend":{"rootCategoryId":2804027708677,"defaultSelectId":2804040429573},"isHighlightDigest":true}},{"projectId":4,"config":{"platform":"pudong","jumpUrl":"https://ythbgptuat.ywxt.sh.cegn.cn/"}},{"projectId":3,"config":{"platform":"neimeng","hideSelectPermissionRole":false,"hideSelectAdminRole":true,"disabledBrrow":false,"disabledFrontDoc":false,"disabledSignVerification":true,"statisticsTimer":3000,"orgId":3863787071237,"tenantId":3863838648069,"statisticsPath":"statistics_nmg","statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"}],"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}],"condition":{"hideDownloadButton":"origin!=999","hidePrintButton":"origin!=999"}}},{"projectId":0,"config":{"controlTopTabRoutes":["/","/results","/profile","/lib-detail","/duplicateCheck","/proofread","/checkIndex"],"platform":"global","pasteCapture":false,"showArticle":true,"showHistory":true,"loginConfig":{"backend":["default"],"front":["default"],"type":"hainan","value":""},"showNavTop":true,"showKnowledge":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":false,"tip":"提示：总大小不超过{0}M","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023-01-01”"},"guideVideo":{"disabled":true,"title":"操作视频","lists":[{"title":"公文库操作视频","lists":[{"title":"男声版","path":"static/video/test2.mp4"},{"title":"女声版","path":"static/video/test1.mp4"}]},{"title":"AI模型能力展示","lists":[{"title":"男声版","path":"static/video/test4.mp4"},{"title":"女声版","path":"static/video/test3.mp4"}]}]},"isClassified":false,"showHotwords":false}}]',
        'Y', 1, '2024-04-28 12:37:56', '001', '2024-09-02 11:08:23.467545', 'admin', '此配置需要全量更新。
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
platform：平台名称
pasteCapture： 是否拦截粘贴
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对7-版式转Word8音频转文本
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
guideVideo: 操作视频
isClassified：是否显示安全文库相关内容
showHotwords: 是否开启热搜词', NULL, NULL);


-- 操作日志和登录日志归档定时任务执行间隔调整为1天
DELETE
FROM "plss-system"."xxl_job_info"
WHERE executor_handler IN ('archiveOperationLog', 'archiveLoginLog');
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (10, 3, '操作日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:52.404', 'fs', NULL, 'CRON',
        '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveOperationLog', NULL, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time",
                                          "author", "alarm_email", "schedule_type", "schedule_conf",
                                          "misfire_strategy", "executor_route_strategy",
                                          "executor_handler", "executor_param",
                                          "executor_block_strategy", "executor_timeout",
                                          "executor_fail_retry_count", "glue_type", "glue_source",
                                          "glue_remark", "glue_updatetime", "child_job_id",
                                          "trigger_status", "trigger_last_time",
                                          "trigger_next_time")
VALUES (9, 3, '登录日志归档', '2024-03-28 16:41:55', '2024-08-15 18:23:43.315', 'fs', NULL, 'CRON',
        '0 0 1 * * ?', 'DO_NOTHING', 'FIRST', 'archiveLoginLog', NULL, 'SERIAL_EXECUTION', 0, 0,
        'BEAN', NULL, NULL, '2024-03-28 16:41:55', NULL, 1, 0, 0);

DELETE
FROM "plss-system"."sys_config"
WHERE config_key = 'system.repository.config';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (205798046516677, '系统文库页签配置', 'system.repository.config',
        '{"tabs":[{"label":"全部库","ctype":[]},{"label":"工作库","ctype":[1]},{"label":"参考库","ctype":[2]},{"label":"模板&范文样例库","ctype":[4,5]},{"label":"安全文库","ctype":[6]}],"query":[]}',
        'Y', 1, '2024-05-17 18:17:11.85', 'admin', '2024-08-14 17:08:01.770487', 'admin', '备份: 内蒙不需要显示该查询条件
		"query": [
    {
		"id": "publishTime",
		"component": "date",
		"type": "metadata",
		"label": "发布日期",
		"data": {
		"metadataId": "1121649841157",
		"metadataValue": "{publishTime}"
		}
    }
  ]', NULL, NULL);

-- 页面模型租户隔离开关配置
INSERT INTO "plss-system".sys_config(config_id, config_name, config_key, config_value, config_type,
                                     status, create_time, create_by, update_time, update_by, remark,
                                     element_type, element_name)
VALUES (260100238426501, '页面模型租户隔离开关', 'page.model.tenant.isolation.switch', 'false', 'Y',
        1, '2024-08-02 11:35:17.386', 'admin', '2024-08-02 14:03:06.005', 'admin',
        '页面模型租户隔离开关。false表示不开启租户隔离', NULL, NULL);


-- 元数据修复任务-执行状态字典类型
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time,
                                         create_by, update_time, update_by, remark)
VALUES (269962167189701, '数据处理状态', 'sys_data_execustatus', '1', '2024-08-16 09:59:26.114',
        '001', '2024-08-16 09:59:26.114', '', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269962680025285, 0, '执行中', '100', 'sys_data_execustatus', '', 'warning', 'N', '1', '001',
        '2024-08-16 10:00:28.714', '', '2024-08-16 10:00:28.714', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269963169005765, 2, '待执行', '300', 'sys_data_execustatus', '', 'info', 'N', '1', '001',
        '2024-08-16 10:01:28.404', '', '2024-08-16 10:01:28.404', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269963412881605, 3, '执行中断', '400', 'sys_data_execustatus', '', 'danger', 'N', '1',
        '001', '2024-08-16 10:01:58.174', '001', '2024-08-16 10:02:04.563', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269963661385925, 4, '已完成', '500', 'sys_data_execustatus', '', 'success', 'N', '1', '001',
        '2024-08-16 10:02:28.509', '', '2024-08-16 10:02:28.509', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type,
                                         css_class, list_class, is_default, status, create_by,
                                         create_time, update_by, update_time, remark)
VALUES (269962990526661, 1, '待执行', '200', 'sys_data_execustatus', '', 'info', 'N', '1', '001',
        '2024-08-16 10:01:06.617', '001', '2024-08-16 10:22:24.804', '');

-- 元数据修复任务-调度参数
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name)
VALUES (274937511995589, '数据处理任务-元数据修复线程数',
        'data.process.task.metadata.repair.thread.num', '3', 'Y', 1, '2024-08-23 10:41:48.007',
        'admin', '2024-08-23 10:45:29.637', 'admin', '', NULL, NULL);
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type,
                                      status, create_time, create_by, update_time, update_by,
                                      remark, element_type, element_name)
VALUES (274936123369669, '数据处理任务-元数据修复批次大小',
        'data.process.task.metadata.repair.batch.size', '1000', 'Y', 1, '2024-08-23 10:38:58.497',
        'admin', '2024-08-23 10:45:47.251', 'admin', '', NULL, NULL);


-- 新增菜单	可视化配置、页面布局、组件管理
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (262293427447365, 0, '可视化配置', 14, 'visualization', '', '', '1', '1', 'M', '1', '1', '',
        'icon-zhanshipeizhi', '001', '2024-08-05 13:57:20.662', '001', '2024-08-05 15:10:22.523',
        '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (263126275591045, 262293427447365, '组件管理', 1, 'package', 'visualization/package', '',
        '2', '1', 'C', '1', '1', '', '#', '001', '2024-08-06 18:11:46.694', '001',
        '2024-08-06 18:12:01.361', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (262295126033989, 262293427447365, '页面布局', 2, 'page', 'visualization/page', '', '2', '1',
        'C', '1', '1', '', '#', '001', '2024-08-05 14:00:48.003', '001', '2024-08-24 20:44:56.456',
        '', 2);

--可视化配置、页面布局、组件管理绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 262293427447365
FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 262295126033989
FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 263126275591045
FROM "plss-system".sys_tenant st;

-- 运营管理员增加 可视化配置、页面布局、组件管理
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
VALUES (1, 262293427447365);
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
VALUES (1, 262295126033989);
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
VALUES (1, 263126275591045);


-- 新增菜单	数据处理任务
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component,
                                    query, is_frame, is_cache, menu_type, visible, status, perms,
                                    icon, create_by, create_time, update_by, update_time, remark,
                                    system_menu_flag)
VALUES (269492923393797, 1, '数据处理任务', 6, 'dataprocess', 'system/dataprocess/index', '', '1',
        '1', 'C', '1', '1', '', '#', '001', '2024-08-15 18:04:45.380', '',
        '2024-08-15 18:04:45.380', '', 2);

--数据处理任务绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 269492923393797
FROM "plss-system".sys_tenant st;

-- 运营管理员增加 数据处理任务
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
VALUES (1, 269492923393797);
-- 入库管理员增加 数据处理任务
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
VALUES (7, 269492923393797);

-- 调度任务定时器
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status,
                                        trigger_last_time, trigger_next_time)
VALUES (279173196621509, 2, '调度数据处理任务', '2024-08-29 10:19:19.350',
        '2024-08-29 10:35:06.714', '陈忠鑫', NULL, 'CRON', '0 * * * * ?', 'DO_NOTHING', 'ROUND',
        'dispatchDataProcessTask', NULL, 'DISCARD_LATER', 0, 0, 'BEAN', NULL, NULL,
        '2024-08-29 10:19:19.350', NULL, 1, 0, 1724898960000);


INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (275043279088837, '{
  "label": "搜索组件",
  "layout": {
    "x": 0,
    "y": 0,
    "w": 14,
    "h": 4,
    "minW": 10,
    "minH": 3
  },
  "node": {
    "type": "l-card",
    "props": {
      "title": "",
      "background": "transparent",
      "padding": 0,
      "bodyStyle": {
        "display": "flex",
        "flexDirection": "column",
        "alignItems": "center",
        "justifyContent": "space-evenly"
      }
    },
    "children": [
      {
        "type": "l-tabs"
      },
      {
        "type": "l-search",
        "style": {
          "margin": "16px 0"
        }
      },
      {
        "type": "l-hot-word"
      }
    ]
  }
}', '2024-08-23 14:16:59.029', 1612860642053, '2024-08-27 13:52:12.190', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (275044225289413, '{
            "label": "列表组件",
            "layout":
            {
                "x": 0,
                "y": 0,
                "w": 12,
                "h": 10,
                "minW": 6,
                "minH": 6
            },
            "node":
            {
                "type": "l-card",
                "children":
                [
                    {
                        "type": "l-lists"
                    }
                ]
            }
        }', '2024-08-23 14:18:54.532', 1612860642053, '2024-08-25 14:42:04.916', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (274964214222021, '{
  "label": "文库入口",
  "layout": {
    "w": 6,
    "h": 2,
    "minW": 2,
    "minH": 2
  },
  "node": {
    "type": "l-button",
    "props": {
      "title": "我的文库",
      "link": "/results"
    },
    "config": [
      {
        "label": "组件属性",
        "lists": [
          {
            "type": "input",
            "key": "title",
            "value": "我的文库",
            "label": "名称"
          },
          {
            "type": "upload",
            "key": "icon",
            "value": "http://172.16.14.176/plss/front/static/themes/red/report.svg",
            "label": "图标",
            "accept": ".png,.jpg,.jpeg,.gif,.svg",
            "maxSize": 500
          },
          {
            "type": "slider",
            "key": "iconSize",
            "value": 28,
            "label": "图标大小",
            "min": 20,
            "max": 60
          },
          {
            "type": "slider",
            "key": "fontWeight",
            "value": 400,
            "label": "字体粗细",
            "step": 100,
            "min": 400,
            "max": 700,
            "showStops": true
          },
          {
            "type": "slider",
            "key": "fontSize",
            "value": 16,
            "label": "字体大小",
            "min": 12,
            "max": 40
          },
          {
            "type": "radio",
            "key": "horizontal",
            "value": false,
            "label": "水平排列"
          },
          {
            "type": "color",
            "key": "color",
            "value": "rgba(13,13,13,0.66)",
            "label": "字体颜色"
          }
        ]
      },
      {
        "label": "数据",
        "lists": [
          {
            "type": "sourceData",
            "key": "sourceData",
            "value": [],
            "label": "数据来源"
          },
          {
            "type": "effectiveScope",
            "key": "effectiveScope",
            "value": [],
            "label": "生效范围"
          }
        ]
      }
    ]
  }
}', '2024-08-23 11:36:07.555', 1612860642053, '2024-08-27 15:09:06.246', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (276468841450693, '{
            "label": "容器组件",
            "layout":
            {
                "w": 12,
                "h": 4,
                "minH": 4,
                "minW": 4
            },
            "node":
            {
                "type": "l-card",
                "props":
                {
                    "padding": 0
                },
                "children":
                [
                    {
                        "type": "l-grid",
                        "children":
                        []
                    }
                ]
            }
        }', '2024-08-25 14:37:17.871', 1612860642053, '2024-08-25 14:37:17.871', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (276471564602565, '{
  "label": "LOGO",
  "layout": {
    "w": 8,
    "h": 2,
    "minW": 2,
    "minH": 2
  },
  "node": {
    "type": "l-button",
    "props": {
      "title": "电子公文库",
      "icon": "http://172.16.14.176/plss/front/static/themes/red/index-logo.svg"
    }
  }
}', '2024-08-25 14:42:50.288', 1612860642053, '2024-08-25 20:52:59.999', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (276655948180677, '{
  "label": "智能应用",
  "layout": {
    "x": 0,
    "y": 0,
    "w": 8,
    "h": 5,
    "minW": 4,
    "minH": 4
  },
  "node": {
    "type": "l-card",
    "props": {
      "title": "智能应用",
      "padding": 10,
      "headerCenter": true
    },
    "children": [
      {
        "type": "l-grid",
        "children": [
          {
            "type": "l-button",
            "layout": {
              "x": 0,
              "y": 0,
              "w": 12,
              "h": 2,
              "minH": 2,
              "minW": 2,
              "i": 0
            },
            "props": {
              "title": "智能对比",
              "icon": "http://172.16.14.176/plss/front/static/themes/red/comparison.svg",
              "link": "/duplicateCheck",
              "horizontal": false
            }
          },
          {
            "type": "l-button",
            "layout": {
              "x": 12,
              "y": 0,
              "w": 12,
              "h": 2,
              "minH": 2,
              "minW": 2,
              "i": 1
            },
            "props": {
              "title": "智能校对",
              "icon": "http://172.16.14.176/plss/front/static/themes/red/contrast.svg",
              "link": "/proofread",
              "horizontal": false
            }
          },
          {
            "type": "l-button",
            "layout": {
              "x": 0,
              "y": 2,
              "w": 12,
              "h": 2,
              "minH": 2,
              "minW": 2,
              "i": 2
            },
            "props": {
              "title": "智能查重",
              "icon": "http://172.16.14.176/plss/front/static/themes/red/check.svg",
              "link": "/checkIndex",
              "horizontal": false
            }
          }
        ]
      }
    ]
  }
}', '2024-08-25 20:57:58.049', 1612860642053, '2024-08-26 11:17:51.786', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (274964391824581, '{
  "label": "文库列表",
  "layout": {
    "x": 0,
    "y": 0,
    "w": 8,
    "h": 4,
    "minW": 4,
    "minH": 4
  },
  "node": {
    "type": "l-card",
    "alias": "library",
    "props": {
      "title": "我的文库",
      "padding": 0
    },
    "children": [
      {
        "type": "l-grid",
        "children": [
          {
            "type": "l-button",
            "layout": {
              "x": 0,
              "y": 0,
              "w": 24,
              "h": 2,
              "minH": 2,
              "minW": 2,
              "i": 0
            },
            "props": {
              "link": "/results"
            },
            "config": [
              {
                "label": "组件属性",
                "lists": [
                  {
                    "type": "input",
                    "key": "title",
                    "value": "我的文库",
                    "label": "名称"
                  },
                  {
                    "type": "upload",
                    "key": "icon",
                    "value": "http://172.16.14.176/plss/front/static/themes/red/report.svg",
                    "label": "图标",
                    "accept": ".png,.jpg,.jpeg,.gif,.svg",
                    "maxSize": 500
                  },
                  {
                    "type": "slider",
                    "key": "iconSize",
                    "value": 28,
                    "label": "图标大小",
                    "min": 20,
                    "max": 60
                  },
                  {
                    "type": "slider",
                    "key": "fontWeight",
                    "value": 400,
                    "label": "字体粗细",
                    "step": 100,
                    "min": 400,
                    "max": 700,
                    "showStops": true
                  },
                  {
                    "type": "slider",
                    "key": "fontSize",
                    "value": 16,
                    "label": "字体大小",
                    "min": 12,
                    "max": 40
                  },
                  {
                    "type": "radio",
                    "key": "horizontal",
                    "value": false,
                    "label": "水平排列"
                  },
                  {
                    "type": "color",
                    "key": "color",
                    "value": "rgba(13,13,13,0.66)",
                    "label": "字体颜色"
                  }
                ]
              },
              {
                "label": "数据",
                "lists": [
                  {
                    "type": "sourceData",
                    "key": "sourceData",
                    "value": [],
                    "label": "数据来源"
                  },
                  {
                    "type": "effectiveScope",
                    "key": "effectiveScope",
                    "value": [],
                    "label": "生效范围"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}', '2024-08-23 11:36:29.236', 1612860642053, '2024-08-27 11:23:16.242', 1612860642053);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time,
                                           update_by)
VALUES (277897108718853, '
{
  "label": "智能查重",
  "layout": {
    "w": 12,
    "h": 2,
    "minW": 2,
    "minH": 2
  },
  "node": {
    "type": "l-button",
    "props": {
      "title": "智能查重",
      "link": "/checkIndex"
    },
    "config": [
      {
        "label": "组件属性",
        "lists": [
          {
            "type": "input",
            "key": "title",
            "value": "智能查重",
            "label": "名称"
          },
          {
            "type": "upload",
            "key": "icon",
            "value": "http://172.16.14.176/plss/front/static/themes/red/report.svg",
            "label": "图标",
            "accept": ".png,.jpg,.jpeg,.gif,.svg",
            "maxSize": 500
          },
          {
            "type": "slider",
            "key": "iconSize",
            "value": 28,
            "label": "图标大小",
            "min": 20,
            "max": 60
          },
          {
            "type": "slider",
            "key": "fontWeight",
            "value": 400,
            "label": "字体粗细",
            "step": 100,
            "min": 400,
            "max": 700,
            "showStops": true
          },
          {
            "type": "slider",
            "key": "fontSize",
            "value": 16,
            "label": "字体大小",
            "min": 12,
            "max": 40
          },
          {
            "type": "radio",
            "key": "horizontal",
            "value": false,
            "label": "水平排列"
          },
          {
            "type": "color",
            "key": "color",
            "value": "rgba(13,13,13,0.66)",
            "label": "字体颜色"
          }
        ]
      },
      {
        "label": "数据",
        "lists": [
          {
            "type": "sourceData",
            "key": "sourceData",
            "value": [],
            "label": "数据来源"
          },
          {
            "type": "effectiveScope",
            "key": "effectiveScope",
            "value": [],
            "label": "生效范围"
          }
        ]
      }
    ]
  }
}', '2024-08-27 15:03:06.903', 1612860642053, '2024-08-27 15:04:06.955', 1612860642053);

-- 添加totp配置
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (287092584197317, 'totp柔性时间回溯', 'totp.flexibility', '3000', 'Y', 1,
        '2024-09-09 14:51:21.469', 'admin', '2024-09-09 15:45:10.770087', 'admin', '', NULL, NULL);
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value",
                                        "config_type", "status", "create_time", "create_by",
                                        "update_time", "update_by", "remark", "element_type",
                                        "element_name")
VALUES (287092394437829, 'totp时间步长', 'totp.step', '3000', 'Y', 1, '2024-09-09 14:50:58.305',
        'admin', '2024-09-09 15:45:15.479211', 'admin', '', NULL, NULL);

-- 入库写日志，备注字段长度修改 新增remark_new字段 旧数据迁移
ALTER TABLE "plss-system"."sys_oper_log"
    ADD "remark_new" TEXT;
UPDATE "plss-system"."sys_oper_log"
SET "remark_new" = "remark";
ALTER TABLE "plss-system"."sys_oper_log" DROP COLUMN "remark";
ALTER TABLE "plss-system"."sys_oper_log" RENAME COLUMN "remark_new" TO "remark";
COMMENT
ON COLUMN "plss-system"."sys_oper_log"."remark" is '备注';