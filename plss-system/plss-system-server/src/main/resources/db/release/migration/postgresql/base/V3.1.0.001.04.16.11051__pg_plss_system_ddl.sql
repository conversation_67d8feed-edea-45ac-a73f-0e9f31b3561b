ALTER TABLE "plss-system".sys_user ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user ALTER COLUMN  default_tenant_id  TYPE varchar(255) USING default_tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_user ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-system".sys_user_role ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_role ALTER COLUMN  role_id  TYPE varchar(255) USING role_id::varchar(255);
ALTER TABLE "plss-system".sys_user_role ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_user_org ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-system".sys_user_org ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_org ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_user_post ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_category ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-system".sys_logininfor ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_message ALTER COLUMN  send_person_id  TYPE varchar(255) USING send_person_id::varchar(255);
ALTER TABLE "plss-system".sys_message ALTER COLUMN  accept_person_id  TYPE varchar(255) USING accept_person_id::varchar(255);
ALTER TABLE "plss-system".sys_oper_log ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_oper_log ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_tenant ALTER COLUMN  id  TYPE varchar(255) USING id::varchar(255);
ALTER TABLE "plss-system".sys_search_history ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_message ALTER COLUMN  send_person_id  TYPE varchar(255) USING send_person_id::varchar(255);
ALTER TABLE "plss-system".sys_message ALTER COLUMN  accept_person_id  TYPE varchar(255) USING accept_person_id::varchar(255);
ALTER TABLE "plss-system".sys_org ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-system".sys_org ALTER COLUMN  parent_id  TYPE varchar(255) USING parent_id::varchar(255);
ALTER TABLE "plss-system".sys_org ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_org ALTER COLUMN  owner_org_id  TYPE varchar(255) USING owner_org_id::varchar(255);
ALTER TABLE "plss-system".sys_page_module ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_page_module ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-system".sys_role_org ALTER COLUMN  role_id  TYPE varchar(255) USING role_id::varchar(255);
ALTER TABLE "plss-system".sys_role_org ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-system".sys_search_collect ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_search_history ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_tenant_menu ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_role_menu ALTER COLUMN  role_id  TYPE varchar(255) USING role_id::varchar(255);
ALTER TABLE "plss-system".sys_post ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_role_data_pms ALTER COLUMN  role_id  TYPE varchar(255) USING role_id::varchar(255);
ALTER TABLE "plss-system".sys_page_model ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_page_model ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-system".sys_page_model ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_page_module_data ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_page_module_data ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-system".sys_layout ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-system".sys_layout ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_user_appearance ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_appearance ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_user_appearance ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-system".sys_user_appearance ALTER COLUMN  id  TYPE varchar(255) USING id::varchar(255);
ALTER TABLE "plss-system".sys_version_remind ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-system".sys_version_remind ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_role ALTER COLUMN  role_id  TYPE varchar(255) USING role_id::varchar(255);
ALTER TABLE "plss-system".sys_role ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_collect ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_operate_approve ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_operate_approve ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_opt_data_pms ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-system".sys_literary_collect ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_remain ALTER COLUMN  send_person_id  TYPE varchar(255) USING send_person_id::varchar(255);
ALTER TABLE "plss-system".sys_remain ALTER COLUMN  handle_person_id  TYPE varchar(255) USING handle_person_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202407 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202408 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202409 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202410 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202411 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202412 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202501 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202502 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202503 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202504 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202505 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202506 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202507 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202508 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202509 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202510 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202511 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202512 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202601 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202602 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202603 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202604 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202605 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202606 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202607 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202608 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202609 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202610 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202611 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202612 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202701 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202702 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202703 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202704 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202705 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202706 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-system".sys_user_behavior_log_202707 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
