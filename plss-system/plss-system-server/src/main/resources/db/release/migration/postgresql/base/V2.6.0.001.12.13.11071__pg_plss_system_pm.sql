--补充初始化文档缺失的文档类型删除按钮
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(240452403176005, 680207111173, '删除', 4, '', '', '', '2', '1', 'F', '1', '1', 'system:record-type:delete', '#', '001', '2024-07-05 17:21:40.001', '', '2024-07-05 17:21:40.001', '', 2);

--文档类型删除按钮绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id)
SELECT st.id, 240452403176005
FROM "plss-system".sys_tenant st;

--文档类型删除按钮绑定所有绑定文档类型停用按钮的角色
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id)
SELECT srm.role_id, 240452403176005
FROM "plss-system".sys_role_menu srm where menu_id = 3868775889669;