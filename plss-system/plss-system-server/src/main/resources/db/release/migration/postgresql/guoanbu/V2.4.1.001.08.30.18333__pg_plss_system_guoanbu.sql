--DML
--停用登录日志和操作日志下的导出按钮
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=3868378096133;
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=3868397821189;

--新增磁盘比例颜色配置
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name) VALUES(287818547562437, '磁盘信息比例颜色配置', 'sys.disk.config', '[{"color":"#88E16E","start":0,"end":0.5},{"color":"#ffcf3f","start":0.5,"end":0.8},{"color":"#f9836b","start":0.8,"end":1}]', 'Y', 1, '2024-09-10 15:28:20.044', 'admin', '2024-09-10 15:28:20.044', 'admin', '', NULL, NULL);

--国安部需求前台更新配置
UPDATE "plss-system".sys_config SET config_name='前端参数配置', config_key='sys.front.config', config_value='[
  {
    "projectId": 1,
    "config": {
      "platform": "ndrc",
      "showNavTop": false
    }
  },
  {
    "projectId": 7,
    "config": {
      "platform": "guoanbu",
      "loginConfig": {
        "backend": [
          "default"
        ],
        "front": [
          "default",
          "ukey"
        ],
        "type": "guoanbu",
        "value": ""
      },
      "disabledFrontDoc": true,
      "showBackendUserTab": [
        {
          "id": "user",
          "lable": "个人信息",
          "isShow": true
        },
        {
          "id": "notify",
          "lable": "我的通知",
          "isShow": true
        },
        {
          "id": "todo",
          "lable": "我的待办",
          "isShow": true
        },
        {
          "id": "borrow",
          "lable": "我的借阅",
          "isShow": true
        },
        {
          "id": "collect",
          "lable": "我的收藏",
          "isShow": true
        },
        {
          "id": "read",
          "lable": "我的阅读",
          "isShow": true
        },
        {
          "id": "share",
          "lable": "我的分享",
          "isShow": true
        },
        {
          "id": "documentWarehousing",
          "lable": "文档入库",
          "isShow": false
        }
      ],
      "showFrontUserTab": [
        {
          "id": "0",
          "lable": "个人信息",
          "isShow": true
        },
        {
          "id": "6",
          "lable": "我的通知",
          "isShow": true
        },
        {
          "id": "4",
          "lable": "我的待办",
          "isShow": true
        },
        {
          "id": "3",
          "lable": "我的借阅",
          "isShow": true
        },
        {
          "id": "2",
          "lable": "我的收藏",
          "isShow": true
        },
        {
          "id": "1",
          "lable": "我的阅读",
          "isShow": true
        },
        {
          "id": "5",
          "lable": "我的分享",
          "isShow": true
        },
        {
          "id": "7",
          "lable": "文档入库",
          "isShow": false
        }
      ]
    }
  },
  {
    "projectId": 9,
    "config": {
      "platform": "xzgf",
      "showLogo": false,
      "showScreen": true
    }
  },
  {
    "projectId": 8,
    "config": {
      "platform": "hainan",
      "isClassified": true
    }
  },
  {
    "projectId": 6,
    "config": {
      "platform": "gzw",
      "isLoginUrl": true
    }
  },
  {
    "projectId": 5,
    "config": {
      "platform": "goxinbu",
      "showFeekBack": {
        "img": "data:image/png;base64,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",
        "feekbackUrl": "https://f.kdocs.cn/g/A2W3tH5r/"
      },
      "recommend": {
        "rootCategoryId": 2804027708677,
        "defaultSelectId": 2804040429573
      }
    }
  },
  {
    "projectId": 4,
    "config": {
      "platform": "pudong",
      "jumpUrl": "https://ythbgptuat.ywxt.sh.cegn.cn/"
    }
  },
  {
    "projectId": 3,
    "config": {
      "platform": "neimeng",
      "hideSelectPermissionRole": true,
      "statisticsTimer": 3000,
      "orgId": 1610564159749,
      "tenantId": 1612752027141,
      "statisticsPath": "statistics_nmg",
      "statisticslabelAlias": {
        "year": [
          "民生服务",
          "生产监管",
          "经济建设",
          "社会保障",
          "数字政府",
          "城乡建设"
        ],
        "month": [
          "党的建设",
          "安全法治",
          "农村建设",
          "经济发展",
          "民生服务",
          "社会治理"
        ],
        "week": [
          "城市管理",
          "行政执法",
          "安全监管",
          "综合经济",
          "社会保障",
          "经济建设"
        ]
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 1024,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        }
      ],
      "ctypePermission": [
        {
          "ctype": 1,
          "hideSetWaterMark": true,
          "hideAddPermission": true,
          "hidePermissionMemo": [
            "can_download",
            "print",
            "download_save"
          ]
        },
        {
          "ctype": 5,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        },
        {
          "ctype": 6,
          "hideCustomPermission": true,
          "hidePermissionMemo": [
            "can_view",
            "can_copy",
            "can_download"
          ]
        }
      ],
      "condition": {
        "showDownloadButton": "origin==999",
        "showPrintButton": "origin==999"
      }
    }
  },
  {
    "projectId": 0,
    "config": {
      "platform": "global",
      "pasteCapture": false,
      "showArticle": true,
      "showHistory": true,
      "showLogo": true,
      "showNavTop": true,
      "showOwner": true,
      "disabledSignVerification": false,
      "advanceSearch": {
        "categoryList": [
          {
            "id": 2804027708677,
            "name": "主题"
          },
          {
            "id": 3575746057989,
            "name": "发文机关"
          }
        ]
      },
      "loginConfig": {
        "backend": [
          "default"
        ],
        "front": [
          "default",
          "ukey"
        ],
        "type": "guoanbu",
        "value": ""
      },
      "showKnowledge": true,
      "showHotwords": false,
      "orcMetadataDefaultValue": {
        "密级": "非密"
      },
      "uploadFileLimit": [
        {
          "sceneType": 1,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "fileCount": 50,
          "maxSizeError": "已超出文件上传上限{0}M",
          "accept": ".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml",
          "acceptError": "该格式文件存在风险，无法入库。"
        },
        {
          "sceneType": 2,
          "multiple": true,
          "tip": "",
          "maxSize": 500,
          "maxSizeError": "",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 3,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.pdf,.ofd,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 4,
          "multiple": false,
          "tip": "提示：总大小不超过{0}M",
          "maxSize": 100,
          "maxSizeError": "总大小不能超过{0}M",
          "accept": "",
          "acceptError": ""
        },
        {
          "sceneType": 5,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 6,
          "multiple": false,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".doc,.docx,.wps,.wpt",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 7,
          "setTimeout": 3000,
          "title": "版式转Word",
          "vendorId": "wps",
          "format": "docx",
          "subtitle": "支持将 OFD/PDF转换为Word",
          "multiple": true,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".ofd,.pdf",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "sceneType": 8,
          "setTimeout": 5000,
          "title": "音频转文本",
          "vendorId": "xf",
          "format": "docx",
          "subtitle": "支持将音频文件转换为Word",
          "multiple": true,
          "tip": "文件格式：{3}，最大不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请",
          "accept": ".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma",
          "acceptError": "仅支持{3} 格式，请"
        },
        {
          "title": "套红模版上传",
          "sceneType": 9,
          "multiple": false,
          "tip": "支持{3}，大小不超过{0}M",
          "maxSize": 30,
          "maxSizeError": "文件限制{0}M以内，请重新上传",
          "accept": ".doc,.docx",
          "acceptError": "仅支持{3} 格式，请重新上传"
        }
      ],
      "hotWords": [],
      "metadataPlaceholder": {
        "发文字号": "请输入完整发文字号，示例：“国函〔2024〕1号",
        "发文机关|发文机关标志": "请输入发文机关名称，示例：“国务院办公厅”",
        "发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间": "请输入日期，示例：“2023年01月01日”"
      },
      "showBackendUserTab": [
        {
          "id": "user",
          "lable": "个人信息",
          "isShow": true,
          "showChangePassword": true,
          "showMyInfo": true
        },
        {
          "id": "notify",
          "lable": "我的通知",
          "isShow": true
        },
        {
          "id": "todo",
          "lable": "我的待办",
          "isShow": true
        },
        {
          "id": "borrow",
          "lable": "我的借阅",
          "isShow": true
        },
        {
          "id": "collect",
          "lable": "我的收藏",
          "isShow": true
        },
        {
          "id": "read",
          "lable": "我的阅读",
          "isShow": true
        },
        {
          "id": "share",
          "lable": "我的分享",
          "isShow": true
        },
        {
          "id": "documentWarehousing",
          "lable": "文档入库",
          "isShow": false
        }
      ],
      "showFrontUserTab": [
        {
          "id": "0",
          "lable": "个人信息",
          "isShow": true,
          "showChangePassword": true,
          "showMyInfo": true
        },
        {
          "id": "6",
          "lable": "我的通知",
          "isShow": true
        },
        {
          "id": "4",
          "lable": "我的待办",
          "isShow": true
        },
        {
          "id": "3",
          "lable": "我的借阅",
          "isShow": true
        },
        {
          "id": "2",
          "lable": "我的收藏",
          "isShow": true
        },
        {
          "id": "1",
          "lable": "我的阅读",
          "isShow": true
        },
        {
          "id": "5",
          "lable": "我的分享",
          "isShow": true
        },
        {
          "id": "7",
          "lable": "文档入库",
          "isShow": false
        }
      ]
    }
  }
]', config_type='Y', status=1, create_time='2024-04-28 12:37:56.000', create_by='001', update_time='2024-09-24 15:39:51.137', update_by='admin', remark='此配置需要全量更新。
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
platform：平台名称
pasteCapture： 是否拦截粘贴
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对7-版式转Word8音频转文本
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
guideVideo: 操作视频
isClassified：是否显示安全文库相关内容
showHotwords: 是否开启热搜词
disabledBrrow: 是否禁用借阅功能', element_type=NULL, element_name=NULL WHERE config_id=192183311754501;

--修改项目版本配置
UPDATE "plss-system".sys_config SET config_value='7',remark= '0:默认标品配置
1:发改委:
[
    "首页:首页数据来自sys.home.section配置,前端控制排版"
]
2:深圳:
[
       "首页:首页数据来自sys.home.section配置,前端控制排版",
    "主题背景颜色需单独配置,在【系统管理】【外观设置】",
    "大事记功能,启用前后端大事记菜单开关",
    "深圳市情功能,启用深圳市情菜单开关",
    "搜索会过滤深圳市情的库"
]
3:内蒙
4.重庆
5.工信部
6.国资委
7.国安部' WHERE config_id=4812811220741;

--添加安全管理菜单及审批通过和审批驳回按钮数据
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(287198923958213, 0, '安全管理', 10, 'safeManage', 'safeManage/index', '', '1', '1', 'C', '1', '1', '', 'icon-minganci', '001', '2024-09-09 18:27:42.402', '001', '2024-09-09 18:28:52.459', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(287204998940613, 287198923958213, '审批通过', 0, '', '', '', '2', '1', 'F', '1', '1', 'system:safety:approve', '#', '001', '2024-09-09 18:40:03.973', '', '2024-09-09 18:40:03.973', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(287205176436677, 287198923958213, '审批驳回', 1, '', '', '', '2', '1', 'F', '1', '1', 'system:safety:reject', '#', '001', '2024-09-09 18:40:25.639', '', '2024-09-09 18:40:25.639', '', 2);

--菜单绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       287198923958213
FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       287204998940613
FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu (tenant_id, menu_id)
SELECT st.ID,
       287205176436677
FROM "plss-system".sys_tenant st;

-- 重置三员权限
DELETE FROM "plss-system".sys_role_menu WHERE role_id=2 OR role_id=3 OR role_id=4;
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 1032745234437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 1033070990853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3867109873669);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3867116717573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3867122862853);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3867132472837);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3867136146437);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3869284419333);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 190099403927045);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 1032351195909);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3866068407557);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3866189144325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3866184399109);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 4969222513925);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 1033087273221);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3091306296069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3091308461573);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3091317844229);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3091322456325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 3091485816325);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(2, 258049632232069);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(4, 1033218025477);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(4, 1033335487237);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(4, 1033482585605);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(3, 287198923958213);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(3, 287204998940613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(3, 287205176436677);

--禁用租户管理页面
UPDATE "plss-system".sys_menu SET   visible='2' WHERE menu_id=1185128382981;

--禁用用户管理和组织机构管理下的导入导出按钮
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=3867141308677;
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=3869299902469;
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=190098293059077;
UPDATE "plss-system".sys_menu SET  status='2' WHERE menu_id=190098022960645;



-- 新增安全管理员审批表
CREATE TABLE "plss-system".sys_operate_approve
(
       id BIGINT NOT NULL,
       operate_type SMALLINT NOT NULL,
       approve_status SMALLINT,
       approve_user VARCHAR(50),
       approve_time TIMESTAMP(6),
       create_by VARCHAR(50),
       create_time TIMESTAMP(6),
       operate_info VARCHAR(256),
       operate_value VARCHAR(256),
       approve_user_name VARCHAR(50),
       create_by_name CHAR(10),
       user_id BIGINT,
       tenant_id BIGINT,
       CONSTRAINT sys_operate_approve_pk PRIMARY KEY (id));

COMMENT ON TABLE "plss-system".sys_operate_approve IS '安全管理员审批表';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_status IS '审批状态   1：待审批 2：通过 3：驳回';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_time IS '审批时间';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_user IS '审批人';
COMMENT ON COLUMN "plss-system".sys_operate_approve.approve_user_name IS '审批人名称';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_by IS '操作人';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_by_name IS '操作人名称';
COMMENT ON COLUMN "plss-system".sys_operate_approve.create_time IS '操作时间';
COMMENT ON COLUMN "plss-system".sys_operate_approve.id IS '主键';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_info IS '操作详情';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_type IS '操作类型';
COMMENT ON COLUMN "plss-system".sys_operate_approve.operate_value IS '操作详情JSON  启用/禁用账号，角色分配用户时 SysOperateInfo ，修改用户信息时 SysOperateInfoUserUpdate';
COMMENT ON COLUMN "plss-system".sys_operate_approve.user_id IS '操作用户id';
COMMENT ON COLUMN "plss-system".sys_operate_approve.tenant_id IS '租户id';

-- 用户表新增审核状态字段
ALTER TABLE "plss-system".sys_user
       ADD COLUMN IF NOT EXISTS data_act_status char NULL;
COMMENT ON COLUMN "plss-system".sys_user.data_act_status IS '原数据实际状态（1=启用,2=停用）';

-- 用户表新增审核状态字段初始化值
UPDATE "plss-system".sys_user
SET data_act_status =  status
WHERE status is not null;

-- 新增安全管理员审批状态和操作类型字典
INSERT INTO "plss-system".sys_dict_type
(dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark)
VALUES(287195899324357, '安全管理员审核状态', 'sys_approve_status', '1', '2024-09-09 18:21:33.180', 'admin', '2024-09-09 18:22:44.943', 'admin', '1：待审批 2：已通过 3：已拒绝');
INSERT INTO "plss-system".sys_dict_type
(dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark)
VALUES(287189794432965, '审核操作类型', 'sys_operate_type', '1', '2024-09-09 18:09:07.954', 'admin', '2024-09-11 11:13:56.934', 'admin', '操作类型 (1:启用账号 2:禁用账号 3:分配用户 4:移除用户 5:修改用户)');


INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287196595603397, 0, '待审批', '1', 'sys_approve_status', '', 'default', 'N', '2', 'admin', '2024-09-09 18:22:58.174', '', '2024-09-09 18:22:58.174', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287196674418629, 0, '已通过', '2', 'sys_approve_status', '', 'default', 'N', '1', 'admin', '2024-09-09 18:23:07.795', '', '2024-09-09 18:23:07.795', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287196748515269, 0, '已拒绝', '3', 'sys_approve_status', '', 'default', 'N', '1', 'admin', '2024-09-09 18:23:16.840', '', '2024-09-09 18:23:16.840', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287194455328709, 0, '启用账号', '1', 'sys_operate_type', '', 'default', 'N', '1', 'admin', '2024-09-09 18:18:36.910', '', '2024-09-09 18:18:36.910', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287194763773893, 0, '禁用账号', '2', 'sys_operate_type', '', 'default', 'N', '1', 'admin', '2024-09-09 18:19:14.562', '', '2024-09-09 18:19:14.562', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287194992601029, 0, '移除用户', '4', 'sys_operate_type', '', 'default', 'N', '1', 'admin', '2024-09-09 18:19:42.496', '', '2024-09-09 18:19:42.496', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(287194898769861, 0, '分配用户', '3', 'sys_operate_type', '', 'default', 'N', '1', 'admin', '2024-09-09 18:19:31.041', 'admin', '2024-09-09 18:19:47.006', '');
INSERT INTO "plss-system".sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(288401669358725, 0, '修改用户', '5', 'sys_operate_type', '', 'default', 'N', '1', 'admin', '2024-09-11 11:14:41.905', '', '2024-09-11 11:14:41.905', '');