DELETE FROM `plss_system`.sys_config WHERE config_key = 'nlp.business.qa.smart.read';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time,
                                      create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES (478266708494277, 'AI智能阅读模块配置', 'nlp.business.qa.smart.read', '{"type":0,"storeTime":240,"taskTime":"0 */5 * * * ?","taskEnable":false}', 'Y', 1, '2025-06-06 17:16:06', 'admin', '2025-06-12 15:38:16', 'admin', 'type=0表示都存;1表示只存聊天记录不存文件; 2都不存
storeTime=问答中的文件存储时间（单位分钟）
taskTime =AI中台定时任务扫描时间
taskEnable =是否开启定时任务', NULL, NULL, 1);

DELETE FROM `plss_system`.xxl_job_info WHERE executor_handler = 'smartReadFileCleanAll';
INSERT INTO `plss_system`.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type,
 schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param,
 executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (479660871305861, 199430670180805, '公文智能阅读兜底全量清理临时文件【每天凌晨1点5分】', '2025-06-08 16:32:32', '2025-06-08 16:37:44', 'zdh', NULL, 'CRON', '0 5 1 * * ?', 'DO_NOTHING', 'FIRST', 'smartReadFileCleanAll', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-06-08 16:32:32', NULL, 1, 0, 1749402300000);


DELETE FROM `plss_system`.sys_config WHERE config_key = 'wr.host.context.path';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(480317493699973, '轻阅读上下文', 'wr.host.context.path', '/plss', 'Y', 1, '2025-06-09 14:48:26.555', 'admin', '2025-06-09 14:48:26.555', 'admin', '', NULL, NULL, 1);

DELETE FROM `plss_system`.sys_config WHERE config_key = 'sys.project.config_0';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time,
                                      create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES(310321439892933, '项目参数配置-pm', 'sys.project.config_0', '{"platform":"global","whetherToCollect":true,"personRole":true,"personPost":false,"showWpsPlugin":false,"wpsPluginUrl":"/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"showFrontThemeChange":true,"hideRememberPassword":false,"font":[{"name":"标准","value":"1","bacgroundcolor":"#efefef","fontSize":"10","show":true},{"name":"大字","value":"2","bacgroundcolor":"#efefef","fontSize":"11","show":true},{"name":"超大字","value":"3","bacgroundcolor":"#efefef","fontSize":"12","show":true}],"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":false,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":100,"maxSizeError":"文件限制{0}M以内","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式"},{"sceneType":11,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.txt,.xml,.et,.ett,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potx,.potm,.ppsx,.ppsm,.uop,.uof,.pdf,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.s2,.caj,.cebx,.xps,.ofd,.aip,.html,.htm,.mht,.mhtml,.vsd,.vsdx,.eml,.msg","acceptError":"仅支持{3} 格式"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":false}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false},{"id":"10","lable":"外观设置","isShow":true}],"showScreen":false,"searchListSet":{"disableDigest":false,"disableMetadata":false},"isOrgLazyLoad":false,"showOrgInteraction":false}', 'Y', 1, '2025-03-06 16:20:22', 'admin', '2025-05-21 10:41:57', 'admin', '标品参数配置，此配置需要全量更新。
showThemeChange:前台个人中心主题变更是否展示
font:个人中心的字号
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
searchListSet搜索列表设置
-disableDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
disabledNavBar是否隐藏导航栏
userDropDisable导航栏下拉隐藏菜单
showScreen: 显示我的筛查
searchListSet搜索列表设置
-disableDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
isOrgLazyLoad 组织架构是否懒加载针对国网项目特殊处理 (国网项目设置为true)', NULL, NULL, 1);
DELETE FROM `plss_system`.sys_config WHERE config_key = 'ai.home.section';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time,
                                      create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES(413122742585221, '首页版本配置-AIChat版', 'ai.home.section', '{"showSkills":false,"content":"金山政务大模型，专注政务领域，一站式解决复杂问题","sideBarItems":{"show":true,"lists":[[{"icon":"MainPage","title":"首页","id":"11","path":"/","show":true}],[{"icon":"NewDialog","title":"新对话","id":"12","path":"/aichathome","show":true},{"icon":"DialogHistory","title":"历史对话","id":"13","path":"/historySpeak","show":true}],[{"icon":"ResourceBase","title":"资源库","id":"14","path":"/airesource","show":true},{"icon":"AiApplication","title":"智能应用","id":"15","path":"/aiIntelligence","show":true}],[{"icon":"Avatar","id":"16","show":true,"component":"UserCenter"}]]},"aiIntelligence":{"content":"智能应用，让高效与智慧触手可及！","navList":[{"icon":"weboffice","title":"公文写作","id":"10","path":"/weboffice","dec":"专业的公文写作助手","show":true},{"icon":"contrast","title":"智能校对","id":"12","path":"/proofread","dec":"检测文字错误和内容表述问题","show":false},{"icon":"comparison","title":"智能对比","id":"11","path":"/ia/contrast","dec":"精准对比文档增删改差异点","show":true},{"icon":"check","title":"智能查重","id":"13","path":"/ia/plagiarism","dec":"快速对文档内容进行查重并标记 ","show":true}]},"aiModel":[{"name":"金山政务大模型","dec":"采用金山政务大模型回答","value":"qingqiu","id":1,"icon":"jinshandamoxing"},{"name":"DeepSeek-R1","dec":"采用DeepSeek-R1推理模型回答","value":"deepSeekR1","icon":"a-DeepSeek_logo1","id":2,"isactive":true}],"userDropDisable":[],"aiResourceVersion":"2"}', 'Y', 1, '2025-03-06 16:20:22', 'admin', '2025-05-21 10:41:57', 'admin', 'sideBarItems：侧边栏项目，每一项id不可重复
showSkills：是否显示常用语', NULL, NULL, 1);

update `plss_system`.sys_config set config_value = '[{"nodeType":1,"nodeFeatures":[{"featId":10001,"featName":"文件上传","featView":1,"featEdit":2},{"featId":10002,"featName":"设置默认入库位置","featView":1,"featEdit":1},{"featId":10003,"featName":"允许入库人员修改入库位置","featView":1,"featEdit":2}],"safetyNodeFeatures":[{"featId":10001,"featName":"文件上传","featView":1,"featEdit":2},{"featId":10002,"featName":"设置默认入库位置","featView":1,"featEdit":1},{"featId":10003,"featName":"允许入库人员修改入库位置","featView":1,"featEdit":2}]},{"nodeType":4,"nodeFeatures":[{"featId":40001,"featName":"文件转换","featView":1,"featEdit":2},{"featId":40002,"featName":"转换成OFD格式","featView":1,"featEdit":2},{"featId":40003,"featName":"扫描件双层OFD处理","featView":1,"featEdit":1},{"featId":40004,"featName":"提取正文","featView":2,"featEdit":1}],"safetyNodeFeatures":[{"featId":40001,"featName":"文件转换","featView":1,"featEdit":2},{"featId":40002,"featName":"转换成OFD格式","featView":1,"featEdit":2},{"featId":40003,"featName":"扫描件双层OFD处理","featView":1,"featEdit":1},{"featId":40004,"featName":"提取正文","featView":2,"featEdit":1}]},{"nodeType":5,"nodeFeatures":[{"featId":50001,"featName":"文件拆分","featView":1,"featEdit":1},{"featId":50002,"featName":"系统自动拆分","featView":1,"featEdit":1},{"featId":50003,"featName":"人工拆分","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":50001,"featName":"文件拆分","featView":2,"featEdit":1},{"featId":50002,"featName":"系统自动拆分","featView":1,"featEdit":1},{"featId":50003,"featName":"人工拆分","featView":1,"featEdit":1}]},{"nodeType":6,"nodeFeatures":[{"featId":60001,"featName":"元数据自动提取","featView":1,"featEdit":1},{"featId":60002,"featName":"ocr提取元数据","featView":2,"featEdit":1},{"featId":60003,"featName":"模型提取元数据","featView":2,"featEdit":1}],"safetyNodeFeatures":[{"featId":60001,"featName":"元数据自动提取","featView":1,"featEdit":1},{"featId":60002,"featName":"ocr提取元数据","featView":2,"featEdit":1},{"featId":60003,"featName":"模型提取元数据","featView":2,"featEdit":1}]},{"nodeType":7,"nodeFeatures":[{"featId":70001,"featName":"标引信息自动提取","featView":1,"featEdit":1},{"featId":70002,"featName":"摘要提取","featView":1,"featEdit":1},{"featId":70003,"featName":"主题分类","featView":1,"featEdit":1},{"featId":70004,"featName":"主题词提取","featView":1,"featEdit":1},{"featId":70005,"featName":"实体识别","featView":1,"featEdit":1},{"featId":70006,"featName":"向量","featView":1,"featEdit":2},{"featId":70007,"featName":"段落评分","featView":1,"featEdit":2}],"safetyNodeFeatures":[{"featId":70001,"featName":"标引信息自动提取","featView":2,"featEdit":2},{"featId":70002,"featName":"摘要提取","featView":1,"featEdit":1},{"featId":70003,"featName":"主题分类","featView":1,"featEdit":1},{"featId":70004,"featName":"主题词提取","featView":1,"featEdit":1},{"featId":70005,"featName":"实体识别","featView":1,"featEdit":1},{"featId":70006,"featName":"向量","featView":1,"featEdit":2},{"featId":70007,"featName":"段落评分","featView":1,"featEdit":2}]},{"nodeType":11,"nodeFeatures":[{"featId":110001,"featName":"人工核对与填充","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":110001,"featName":"人工核对与填充","featView":1,"featEdit":1}]},{"nodeType":8,"nodeFeatures":[{"featId":80001,"featName":"人工审核","featView":1,"featEdit":1},{"featId":80002,"featName":"配置审核人","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":80001,"featName":"人工审核","featView":2,"featEdit":1},{"featId":80002,"featName":"配置审核人","featView":1,"featEdit":1}]},{"nodeType":9,"nodeFeatures":[{"featId":90001,"featName":"入库","featView":1,"featEdit":2},{"featId":90002,"featName":"系统自动生成封面","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":90001,"featName":"入库","featView":1,"featEdit":2},{"featId":90002,"featName":"系统自动生成封面","featView":1,"featEdit":1}]},{"nodeType":13,"nodeFeatures":[{"featId":130001,"featName":"知识提取","featView":1,"featEdit":1},{"featId":130002,"featName":"知识模型","featView":1,"featEdit":1},{"featId":130003,"featName":"实体对齐","featView":1,"featEdit":1},{"featId":130004,"featName":"自动对齐","featView":1,"featEdit":1},{"featId":130005,"featName":"手动对齐","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":130001,"featName":"知识提取","featView":2,"featEdit":1},{"featId":130002,"featName":"知识模型","featView":1,"featEdit":1},{"featId":130003,"featName":"实体对齐","featView":1,"featEdit":1},{"featId":130004,"featName":"自动对齐","featView":1,"featEdit":1},{"featId":130005,"featName":"手动对齐","featView":1,"featEdit":1}]}]'
where config_key = 'record.node.feature.configKey';


INSERT INTO `plss_system`.sys_table_header
(id, config_id, field_type, field_key, field_name, field_show_name, metadata_category_id, metadata_category_name, order_by, status, del_flag, create_by, create_time, update_by, update_time, remark)
VALUES(477499895964997, 2398227485702, 1, 'storeProcess', '入库状态', '入库状态', 0, '', 3, 1, '1', '1612860642053', '2025-06-05 15:16:01', '', '2025-06-05 15:16:01', '');


INSERT INTO `plss_system`.xxl_job_info (id,job_group, job_desc, add_time, update_time,
                                        author,
                                        alarm_email, schedule_type, schedule_conf,
                                        misfire_strategy,
                                        executor_route_strategy, executor_handler,
                                        executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count,
                                        glue_type, glue_source, glue_remark,
                                        glue_updatetime, child_job_id,
                                        trigger_status, trigger_last_time,
                                        trigger_next_time)
VALUES (372073744763078,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '个人库编辑文件自动提交入库【每分钟执行一次】', '2025-06-06 16:00:00',
        '2025-01-07 14:00:00', 'xks', NULL, 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST',
        'personalRepoAutoSubmit', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-06-06 16:00:00', NULL, 1, 0, 0);


DELETE FROM `plss_system`.sys_config WHERE config_key = 'suwell.convert.uri';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(413122742585223, '转换服务', 'suwell.convert.uri', 'http://${convert-uri}', 'Y', 1, '2025-06-09 14:48:26.555', 'admin', '2025-06-09 14:48:26.555', 'admin', '', NULL, NULL, 2);


INSERT INTO `plss_system`.sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES(482342514117893, '个人库编辑延迟入库时间', 'personal.repo.config.autoInterval', '30', 'Y', 1, '2025-06-12 11:28:21.430', 'admin', '2025-06-12 11:28:21.430', 'admin', '单位：分钟
系统控制最少不能少于5分钟', NULL, NULL, 1);

delete from `plss_system`.`sys_menu` sm where sm.menu_id = 347943710608581;
INSERT INTO `plss_system`.`sys_menu` (menu_id, parent_id, menu_name, order_num, `path`, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(347943710608581, 0, '统计分析', 17, 'statistics-data', '', '', '1', '1', 'M', '1', '1', '', 'icon-supervise', '001', '2024-12-04 14:13:17.487', '001', '2024-12-10 15:59:20.670', '', 2);

UPDATE `plss_system`.sys_menu SET menu_name='基础数据管理' WHERE menu_id=2206732500229;

UPDATE `plss_system`.sys_layout SET `content`='%7B%22id%22%3A205064713029061%2C%22name%22%3A%22%E5%85%9A%E6%94%BF%E6%9C%BA%E5%85%B3%E5%85%AC%E6%96%87%E6%A0%BC%E5%BC%8F(GB%2FT9704%E4%B8%802012)%22%2C%22tabGroup%22%3A%5B%7B%22name%22%3A%22%E9%80%9A%E7%94%A8%22%2C%22margins%22%3A%7B%22name%22%3A%22%E9%A1%B5%E8%BE%B9%E8%B7%9D%22%2C%22margin%22%3A%5B%7B%22name%22%3A%22%E4%B8%8A%22%2C%22value%22%3A3.7%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E4%B8%8B%22%2C%22value%22%3A3.5%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E5%B7%A6%22%2C%22value%22%3A2.8%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E5%8F%B3%22%2C%22value%22%3A2.6%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%5D%7D%2C%22pageNumber%22%3A%7B%22name%22%3A%22%E9%A1%B5%E7%A0%81%22%2C%22status%22%3Atrue%2C%22fontValue%22%3A%22%E6%96%B9%E6%AD%A3%E5%B0%8F%E6%A0%87%E5%AE%8B%E7%AE%80%E4%BD%93%22%2C%22fontSizeValue%22%3A%2222%22%2C%22fontStyle%22%3A%22-1-%22%2C%22fontPosition%22%3A%22%E5%A5%87%E5%B7%A6%E5%81%B6%E5%8F%B3%22%2C%22fontBold%22%3Atrue%7D%2C%22contentCorrections%22%3A%7B%22name%22%3A%22%E5%86%85%E5%AE%B9%E7%BA%A0%E6%AD%A3%22%2C%22status%22%3Atrue%2C%22removeSpaces%22%3Atrue%2C%22symbolCorrection%22%3Atrue%7D%7D%2C%7B%22name%22%3A%22%E5%85%AC%E6%96%87%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E6%96%B9%E6%AD%A3%E5%B0%8F%E6%A0%87%E5%AE%8B%E7%AE%80%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2222%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphCenter%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E6%97%A0%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E6%AD%A3%E6%96%87%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%B8%80%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E9%BB%91%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%BA%8C%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E6%A5%B7%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%B8%89%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E5%9B%9B%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%5D%7D' WHERE is_public = 1;

UPDATE `plss_system`.sys_config SET config_value = '{"verifyAk":true,"verifyWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/write/assist/search/**","/v1/auth/login","/v1/user/getInfo","/v1/white/**","/v1/abutment/config/manufacturer_reload","/v1/am/login/ack","/v1/app/**","/v1/redHeadTemplate/**","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo","/v1/material/**","/v1/category/tree","/v1/repository/listByIds"],"appWhiteList":["/v1/permission/**","/v1/aiassist/**","/v1/open/**","/v1/proofread/**","/v1/auth/login","/v1/user/getInfo","/v1/auth/refreshToken","/v1/app/**","/v1/white/**","/v1/write/assist/search/**","/v1/redHeadTemplate/**","/v1/am/login/ack","/v2/si/dreamIt/resource/**","/v2/oa/auth/nwxr/**","/v1/wof/getInfo","/v1/material/**","/v1/category/tree","/v1/repository/listByIds"]}' WHERE config_key = 'open.base.config';

UPDATE `plss_system`.sys_config SET config_value='["10002","10003","10004","10005"]' WHERE config_id=326767287003525;


DELETE FROM `plss_system`.xxl_job_info WHERE executor_handler = 'microLogFileCleanAll';
INSERT INTO `plss_system`.xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type,
                                        schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (485175964451973, 2, '服务文件日志清理操作【每天凌晨1点5分】', '2025-06-16 11:33:01', '2025-06-17 02:23:28', 'zdh', NULL, 'CRON', '0 5 1 * * ?', 'DO_NOTHING', 'FIRST', 'microLogFileCleanAll', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-06-16 11:33:01', NULL, 1, 1750093500000, 1750179900000);


DELETE FROM `plss_system`.sys_config WHERE config_key = 'general.feature.scene.configKey';
INSERT INTO `plss_system`.sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time,
                                      create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES (485173948679301, '备选功能开关', 'general.feature.scene.configKey', '[{"featType":1,"featEnable":true},{"featType":2,"featEnable":false},{"featType":3,"featEnable":true}]', 'Y', 1, '2025-06-16 11:28:55', 'admin', '2025-06-17 01:27:44', 'admin', 'featType=1-ai对接mq去重开关;2-附件提取流程开关;3-附件提取重试消息去重开关；featEnable=false-关闭,true开启', 'json', NULL, 1);



-- 3.3版本更新SQL
INSERT INTO `plss_system`.sys_version
(id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by)
VALUES(9, 'v3.3.0', '新增“智能阅读”；“我的文库”支持问答；写公文新增深度思考、按句溯源；仿写优化；大纲成文优化；继续写支持深度思考；新增生成结语功能；自定义生成提示语；识别文档内表格；新增特定排版功能；修复已知问题。', '1', 'v3.3.0_front.md', '2025-06-18 00:00:00.000', '1', '1', '2025-06-18 00:00:00.000', 1);

INSERT INTO `plss_system`.sys_version
(id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by)
VALUES(10, 'v3.3.0', '修复已知问题。', '2', '', '2025-06-18 00:00:00.000', '1', '1', '2025-06-18 00:00:00.000', 1);

UPDATE `plss_system`.sys_config
SET  config_value='{"front":"v3.3.0","backend":"v3.3.0"}'
WHERE config_id=203685690483910;