
-- 修改 安全入库配置方案的按钮显示控制
delete from "plss-system".sys_config where config_key = 'record.node.feature.configKey';
INSERT INTO "plss-system".sys_config ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (337467535329925, '管理节点功能点细粒度控制', 'record.node.feature.configKey', '[{"nodeType":1,"nodeFeatures":[{"featId":10001,"featName":"文件上传","featView":1,"featEdit":2},{"featId":10002,"featName":"设置默认入库位置","featView":1,"featEdit":1},{"featId":10003,"featName":"允许入库人员修改入库位置","featView":1,"featEdit":2}],"safetyNodeFeatures":[{"featId":10001,"featName":"文件上传","featView":1,"featEdit":2},{"featId":10002,"featName":"设置默认入库位置","featView":1,"featEdit":1},{"featId":10003,"featName":"允许入库人员修改入库位置","featView":1,"featEdit":2}]},{"nodeType":4,"nodeFeatures":[{"featId":40001,"featName":"文件转换","featView":1,"featEdit":2},{"featId":40002,"featName":"转换成OFD格式","featView":1,"featEdit":2},{"featId":40003,"featName":"扫描件双层OFD处理","featView":1,"featEdit":1},{"featId":40004,"featName":"提取正文","featView":2,"featEdit":1}],"safetyNodeFeatures":[{"featId":40001,"featName":"文件转换","featView":1,"featEdit":2},{"featId":40002,"featName":"转换成OFD格式","featView":1,"featEdit":2},{"featId":40003,"featName":"扫描件双层OFD处理","featView":1,"featEdit":1},{"featId":40004,"featName":"提取正文","featView":2,"featEdit":1}]},{"nodeType":5,"nodeFeatures":[{"featId":50001,"featName":"文件拆分","featView":1,"featEdit":1},{"featId":50002,"featName":"系统自动拆分","featView":1,"featEdit":1},{"featId":50003,"featName":"人工拆分","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":50001,"featName":"文件拆分","featView":2,"featEdit":1},{"featId":50002,"featName":"系统自动拆分","featView":1,"featEdit":1},{"featId":50003,"featName":"人工拆分","featView":1,"featEdit":1}]},{"nodeType":6,"nodeFeatures":[{"featId":60001,"featName":"元数据自动提取","featView":1,"featEdit":1},{"featId":60002,"featName":"ocr提取元数据","featView":2,"featEdit":1},{"featId":60003,"featName":"模型提取元数据","featView":2,"featEdit":1}],"safetyNodeFeatures":[{"featId":60001,"featName":"元数据自动提取","featView":1,"featEdit":1},{"featId":60002,"featName":"ocr提取元数据","featView":2,"featEdit":1},{"featId":60003,"featName":"模型提取元数据","featView":2,"featEdit":1}]},{"nodeType":7,"nodeFeatures":[{"featId":70001,"featName":"标引信息自动提取","featView":1,"featEdit":1},{"featId":70002,"featName":"摘要提取","featView":1,"featEdit":1},{"featId":70003,"featName":"主题分类","featView":1,"featEdit":1},{"featId":70004,"featName":"主题词提取","featView":1,"featEdit":1},{"featId":70005,"featName":"实体识别","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":70001,"featName":"标引信息自动提取","featView":2,"featEdit":2},{"featId":70002,"featName":"摘要提取","featView":1,"featEdit":1},{"featId":70003,"featName":"主题分类","featView":1,"featEdit":1},{"featId":70004,"featName":"主题词提取","featView":1,"featEdit":1},{"featId":70005,"featName":"实体识别","featView":1,"featEdit":1}]},{"nodeType":11,"nodeFeatures":[{"featId":110001,"featName":"人工核对与填充","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":110001,"featName":"人工核对与填充","featView":1,"featEdit":1}]},{"nodeType":8,"nodeFeatures":[{"featId":80001,"featName":"人工审核","featView":1,"featEdit":1},{"featId":80002,"featName":"配置审核人","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":80001,"featName":"人工审核","featView":2,"featEdit":1},{"featId":80002,"featName":"配置审核人","featView":1,"featEdit":1}]},{"nodeType":9,"nodeFeatures":[{"featId":90001,"featName":"入库","featView":1,"featEdit":2},{"featId":90002,"featName":"系统自动生成封面","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":90001,"featName":"入库","featView":1,"featEdit":2},{"featId":90002,"featName":"系统自动生成封面","featView":1,"featEdit":1}]},{"nodeType":13,"nodeFeatures":[{"featId":130001,"featName":"知识提取","featView":1,"featEdit":1},{"featId":130002,"featName":"知识模型","featView":1,"featEdit":1},{"featId":130003,"featName":"实体对齐","featView":1,"featEdit":1},{"featId":130004,"featName":"自动对齐","featView":1,"featEdit":1},{"featId":130005,"featName":"手动对齐","featView":1,"featEdit":1}],"safetyNodeFeatures":[{"featId":130001,"featName":"知识提取","featView":2,"featEdit":1},{"featId":130002,"featName":"知识模型","featView":1,"featEdit":1},{"featId":130003,"featName":"实体对齐","featView":1,"featEdit":1},{"featId":130004,"featName":"自动对齐","featView":1,"featEdit":1},{"featId":130005,"featName":"手动对齐","featView":1,"featEdit":1}]}]', 'Y', 1, '2024-11-19 18:59:27.496', 'admin', '2025-02-25 17:24:10.4443', 'admin', 'nodeType=节点类型
featId=功能编号ID可为雪花算法生成[唯一]
featName=功能名称
featView=1展示，2不展示
featEdit=1编辑,2不可编辑
', 'json', NULL, 1);

-- 附件已入库DB全量同步ES,按需求刷数据
delete from "plss-system"."xxl_job_info" where "id" = 408010528443397;
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (408010528443397, 2, '附件已入库DB全量同步ES【每月1号凌晨1点执行】', '2025-02-27 10:59:32.706', '2025-02-27 10:59:32.706', 'zdh', NULL, 'CRON', '0 1 0 1 * ?', 'DO_NOTHING', 'FIRST', 'syncAllAtt', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-02-27 10:59:32.706', NULL, 0, 0, 0);

-- 补充的附件提取流程支持的格式范围
delete from "plss-system".sys_config where config_key = 'record.att.format.limit.configKey';
INSERT INTO "plss-system".sys_config ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (413130789791621, '附件支持提取流程的格式范围', 'record.att.format.limit.configKey', '[{"formatType":1,"formatScopes":["ofd","pdf","xps","ceb","cebx","sep","s2","s10","s72","s92","ps","caj","gd","gw","sfd"]},{"formatType":2,"formatScopes":["doc","dot","wps","wpt","docx","dotx","docm","dotm","rtf","uot","uof","txt","xml"]},{"formatType":3,"formatScopes":["xls","xlt","et","ett","xlsx","xltx","csv","xlsm","xltm","xlsb","uos"]},{"formatType":4,"formatScopes":["pptx","ppt","pot","potx","pps","ppsx","dps","dpt","pptm","potm","ppsm","uop"]},{"formatType":5,"formatScopes":["mht","htm","html","mhtml"]}]', 'Y', 1, '2025-03-06 16:36:44.613', 'admin', '2025-03-06 16:36:44.613', 'admin', '1=版式格式
2=文字格式
3=表格格式
4=演示格式
5=网页格式', NULL, NULL, 1);

-- 新增定时任务 2.7迁移数据，耗时耗资源
DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'migrateTaskAbout';
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count, glue_type, glue_source,
                                        glue_remark, glue_updatetime, child_job_id, trigger_status,
                                        trigger_last_time, trigger_next_time)
VALUES (357976715519173, 2, '2.7迁移数据，耗时耗资源', '2024-12-18 18:25:29.528',
        '2024-12-18 18:25:38.835', 'fs', NULL, 'NONE', '0 0 0 0 0 ? *', 'DO_NOTHING', 'FIRST',
        'migrateTaskAbout', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL,
        '2024-12-18 18:25:29.528', NULL, 0, 0, 0);

DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'fillOrgOwnerId';
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time, author,
                                        alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param,
                                        executor_block_strategy, executor_timeout, executor_fail_retry_count,
                                        glue_type, glue_source, glue_remark, glue_updatetime, child_job_id,
                                        trigger_status, trigger_last_time, trigger_next_time)
VALUES (375689762472581, 3, '刷新所属单位id字典', '2025-01-12 19:02:46.702',
        '2025-01-12 19:02:46.702', 'fs', NULL, 'CRON',
        '* * * * * ?', 'DO_NOTHING', 'FIRST',
        'fillOrgOwnerId', NULL, 'SERIAL_EXECUTION', 0,
        0, 'BEAN', NULL, NULL, '2025-01-12 19:02:46.702',
        NULL, 0, 0, 0);

DELETE FROM "plss-system".sys_config WHERE config_key = 'plss.delivery.accessKey';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value,
                                      config_type, status, create_time, create_by, update_time, update_by, remark, element_type,
                                      element_name, deliver_required_item_flag)
VALUES (378254501913221, '数据订阅秘钥', 'plss.delivery.accessKey',
        '7e178809c013443fa6aff5536a8bc3b3', 'Y', 1, '2025-01-16 10:00:45.253',
        'admin', '2025-01-16 10:00:45.253', 'admin', '需要外网订阅数据时才需要配置，其他情况给空', 'text',
        NULL, 2);

DELETE FROM "plss-system".sys_config WHERE config_key = 'sys.project.config_3';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value,
                                      config_type, status, create_time, create_by, update_time, update_by, remark,
                                      element_type, element_name, deliver_required_item_flag)
VALUES (310320830186949, '项目参数配置-nm', 'sys.project.config_3',
        '{"platform":"neimeng","hideSelectPermissionRole":false,"hideSelectAdminRole":true,"disabledBrrow":true,"disabledFrontDoc":true,"disabledSignVerification":true,"showOrgInteraction":true,"statisticsTimer":3000,"orgId":1610564159749,"tenantId":1612752027141,"statisticsPath":"statistics_nmg","orcMetadataDefaultValue":{"密级":"非密"},"statisticslabelAlias":{"year":["民生服务","生产监管","经济建设","社会保障","数字政府","城乡建设"],"month":["党的建设","安全法治","农村建设","经济发展","民生服务","社会治理"],"week":["城市管理","行政执法","安全监管","综合经济","社会保障","经济建设"]},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":1024,"maxSizeError":"","accept":"","acceptError":""}],"ctypePermission":[{"ctype":1,"hideSetWaterMark":true,"hideAddPermission":true,"hidePermissionMemo":["can_download","print","download_save"]},{"ctype":5,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]},{"ctype":6,"hideCustomPermission":true,"hidePermissionMemo":["can_view","can_copy","can_download"]}],"condition":{"showDownloadButton":"origin==999","showPrintButton":"origin==999"},"disableShowDocOperation":["repo_frontShow"],"showScreen":true}', 'Y', 1, '2024-10-12 10:29:20.716', 'admin', '2025-01-22 15:07:31.145638', 'admin', '内蒙参数配置，此配置需要全量更新。
showMyInfo：控制个人中心个人信息是否展示
showChangePassword：控制个人中心的修改密码是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
showOrgInteraction 组织架构树是否展示父子联动
disableShowDocOperation 文库不展示的权限', NULL, NULL, 1);

DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'syncRemoteDataLake';
INSERT INTO "plss-system".xxl_job_info (id, job_group, job_desc, add_time, update_time,
                                        author, alarm_email, schedule_type, schedule_conf, misfire_strategy,
                                        executor_route_strategy, executor_handler, executor_param, executor_block_strategy,
                                        executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark,
                                        glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (396224586181445, 2, '同步分发服务数据湖数据',
        '2025-02-10 19:20:59.05', '2025-02-10 19:22:10.755', 'fs',
        NULL, 'CRON', '0 0 0/1 * * ?', 'DO_NOTHING',
        'ROUND', 'syncRemoteDataLake', NULL,
        'SERIAL_EXECUTION', 0, 0,
        'BEAN', NULL, NULL, '2025-02-10 19:20:59.05',
        NULL, 1, 0, 1739188800000);

ALTER TABLE "plss-system".sys_org ADD COLUMN owner_org_id int8;

DELETE FROM "plss-system".sys_config WHERE config_key = 'sys.appearance.aipreview';

INSERT INTO  "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES (409572292386949, '智能应用banner图', 'sys.appearance.aipreview', '12', 'Y', 1, '2025-03-01 15:56:57.723', '001', '2025-03-01 16:14:26.224008', 'admin', '', 'img', NULL, 1);

DELETE FROM "plss-system".sys_config WHERE config_key = 'home.section.version';
INSERT INTO  "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES (413122109720453, '首页启用版本配置', 'home.section.version', '{"homeversion":""}', 'Y', 1, '2025-03-06 16:19:05.034', 'admin', '2025-03-07 11:14:03.874881', 'admin', 'aihome AI问答首页,空值 说明启用原首页', NULL, NULL, 1);


DELETE FROM "plss-system".sys_config WHERE config_key = 'ai.home.section';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES (413122742585221, '首页版本配置-AIChat版', 'ai.home.section', '{"content":"金山政务大模型，专注政务领域，一站式解决复杂问题","sideBarItems":{"show":true,"lists":[[{"icon":"MainPage","title":"首页","id":"11","path":"/","show":true}],[{"icon":"NewDialog","title":"新对话","id":"12","path":"/","show":true},{"icon":"DialogHistory","title":"历史对话","id":"13","path":"/historySpeak","show":true}],[{"icon":"ResourceBase","title":"资源库","id":"14","path":"/airesource","show":true},{"icon":"AiApplication","title":"智能应用","id":"15","path":"/aiIntelligence","show":true}],[{"icon":"Avatar","id":"16","show":true,"component":"UserCenter"}]]},"aiIntelligence":{"content":"智能应用，让高效与智慧触手可及！","navList":[{"icon":"weboffice","title":"公文写作","id":"10","path":"/weboffice","dec":"专业的公文写作助手","show":true},{"icon":"contrast","title":"智能校对","id":"12","path":"/proofread","dec":"检测文字错误和内容表述问题","show":true},{"icon":"comparison","title":"智能对比","id":"11","path":"/duplicateCheck","dec":"精准对比文档增删改差异点","show":true},{"icon":"check","title":"智能查重","id":"13","path":"/checkIndex","dec":"快速对文档内容进行查重并标记 ","show":true}]},"aiModel":[{"name":"金山政务大模型","dec":"采用金山政务大模型回答","value":"qingqiu","id":1,"icon":"jinshandamoxing"},{"name":"DeepSeek-R1","dec":"采用DeepSeek-R1推理模型回答","value":"deepSeekR1","icon":"a-DeepSeek_logo1","id":2,"isactive":true}],"userDropDisable":[]}', 'Y', 1, '2025-03-06 16:20:22.288', 'admin', '2025-03-07 11:25:57.625849', 'admin', 'sideBarItems：侧边栏项目，每一项id不可重复', NULL, NULL, 1);


DELETE FROM "plss-system".sys_config WHERE config_key = 'sys.project.config_0';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES (310321439892933, '项目参数配置-pm', 'sys.project.config_0', '{"platform":"global","whetherToCollect":true,"personRole":true,"personPost":false,"showWpsPlugin":false,"wpsPluginUrl":"/documentlibrary-wps/publish.html","knowledgeMapId":2685882319621,"pasteCapture":false,"showHistory":true,"showLogo":true,"showNavTop":true,"showOwner":true,"disabledBrrow":false,"disabledFrontDoc":true,"disabledSignVerification":false,"isNewReader":true,"showFrontThemeChange":true,"font":[{"name":"标准","value":"1","bacgroundcolor":"#efefef","fontSize":"10","show":true},{"name":"大字","value":"2","bacgroundcolor":"#efefef","fontSize":"11","show":true},{"name":"超大字","value":"3","bacgroundcolor":"#efefef","fontSize":"12","show":true}],"advanceSearch":{"categoryList":[{"id":2804027708677,"name":"主题"},{"id":3575746057989,"name":"发文机关"}]},"loginConfig":{"backend":["default"],"front":["default","ukey"],"type":"guoanbu","value":""},"showKnowledge":true,"showHotwords":false,"showArticle":true,"showResultsArticle":false,"showAdvancedSearch":false,"showResultsAdvancedSearch":false,"orcMetadataDefaultValue":{"密级":"非密"},"uploadFileLimit":[{"sceneType":1,"multiple":true,"tip":"","maxSize":500,"fileCount":50,"maxSizeError":"已超出文件上传上限{0}M","accept":".doc,.docx,.wps,.wpt,.rtf,.dot,.dotx,.docm,.dotm,.uot,.uof,.txt,.et,.eet,.xls,.xlsx,.xlt,.xlsm,.csv,.xltx,.dif,.uos,.dbf,.dps,.dpt,.ppt,.pptx,.pot,.pps,.pptm,.potm,.ppsx,.ppsm,.uop,.pdf,.ofd,.ceb,.sep,.gd,.gw,.ps,.s72,.s92,.s10,.caj,.cebx,.xps,.bmp,.jpg,.jpeg,.png,.gif,.tif,.tiff,.html,.htm,.mht,.mhtml,vsd,.vsdx,.dwg,.dwt,.dwf,.dwxf,.eml","acceptError":"该格式文件存在风险，无法入库。"},{"sceneType":2,"multiple":true,"tip":"","maxSize":500,"maxSizeError":"","accept":"","acceptError":""},{"sceneType":3,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":4,"multiple":true,"limit":20,"tip":"提示：总大小不超过{0}M，单次上传文件数量限制{4}个","maxSize":100,"maxSizeError":"总大小不能超过{0}M","accept":"","acceptError":""},{"sceneType":5,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":6,"multiple":false,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".doc,.docx,.wps,.wpt","acceptError":"仅支持{3} 格式，请"},{"sceneType":7,"setTimeout":3000,"title":"版式转Word","vendorId":"wps","format":"docx","subtitle":"支持将 OFD/PDF转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".ofd,.pdf","acceptError":"仅支持{3} 格式，请"},{"sceneType":8,"setTimeout":5000,"title":"音频转文本","vendorId":"xf","format":"docx","subtitle":"支持将音频文件转换为Word","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请","accept":".mp3,.wav,.pcm,.aac,.opus,.flac,.ogg,.m4a,.amr,.speex,.lyb,.ac3,.aac,.ape,.m4r,.mp4,.acc,.wma","acceptError":"仅支持{3} 格式，请"},{"title":"套红模版上传","sceneType":9,"multiple":false,"tip":"支持{3}，大小不超过{0}M","maxSize":30,"maxSizeError":"文件限制{0}M以内，请重新上传","accept":".doc,.docx","acceptError":"仅支持{3} 格式，请重新上传"},{"sceneType":10,"setTimeout":50000,"title":"智能分件","format":"docx","multiple":true,"tip":"文件格式：{3}，最大不超过{0}M","maxSize":30,"limit":100,"maxSizeError":"文件限制{0}M以内","accept":".doc,.docx,.pdf,.ofd,.wps,.wpt","acceptError":"仅支持{3} 格式"}],"hotWords":[],"metadataPlaceholder":{"发文字号":"请输入完整发文字号，示例：“国函〔2024〕1号","发文机关|发文机关标志":"请输入发文机关名称，示例：“国务院办公厅”","发布日期|公布日期|成文日期|印发日期|实施日期|施行日期|报告时间":"请输入日期，示例：“2023年01月01日”"},"showBackendUserTab":[{"id":"user","lable":"个人信息","isShow":true},{"id":"changePassword","lable":"修改密码","isShow":true},{"id":"notify","lable":"我的通知","isShow":true},{"id":"todo","lable":"我的待办","isShow":true},{"id":"borrow","lable":"我的借阅","isShow":true},{"id":"collect","lable":"我的收藏","isShow":true},{"id":"read","lable":"我的阅读","isShow":true},{"id":"share","lable":"我的分享","isShow":true},{"id":"documentWarehousing","lable":"文档入库","isShow":false}],"showFrontUserTab":[{"id":"0","lable":"个人信息","isShow":true},{"id":"9","lable":"修改密码","isShow":true},{"id":"6","lable":"我的通知","isShow":true},{"id":"4","lable":"我的待办","isShow":true},{"id":"3","lable":"我的借阅","isShow":true},{"id":"2","lable":"我的收藏","isShow":true},{"id":"1","lable":"我的阅读","isShow":true},{"id":"5","lable":"我的分享","isShow":true},{"id":"7","lable":"文档入库","isShow":false},{"id":"10","lable":"外观设置","isShow":true}],"showScreen":true,"searchListSet":{"disableDigest":false,"disableMetadata":false},"isOrgLazyLoad":false,"showOrgInteraction":false}', 'Y', 1, '2024-10-12 10:30:35.144', 'admin', '2025-03-05 16:29:49.041844', 'admin', '标品参数配置，此配置需要全量更新。
showThemeChange:前台个人中心主题变更是否展示
font:个人中心的字号
knowledgeMapId：总书记重要讲话id
showResultsArticle：搜索结果页是否展示以文搜文
showResultsAdvancedSearch：搜索结果页是否显示高级搜索按钮
showAdvancedSearch：控制首页的高级搜索按钮是否展示
controlTopTabRoutes：存在里面的路由才展示顶部tab，如：智能搜索，政务问答，公文写作
showFrontNavbarAndSidebar：显示前台头部导航栏
showNavbarAndSidebar：显示后台头部导航栏和左侧侧边栏
platform：平台名称
pasteCapture： 是否拦截粘贴
showLogo：是否展示logo
hideSelectPermissionRole: 隐藏文库权限中的角色
showArticle： 首页是否显示以文搜文
showHistory： 是否显示搜索历史
loginConfig：登录是否显示ukey    ukey default
showNavTop:  是否显示nav模块
showFeekBack： 是否显示意见反馈
showKnowledge：是否展示知识工程
showHotwords：前台是否展示热搜词
statisticsTimer： 数据大屏更新间隔（ms）
statisticsPath： 数据大屏跳转路由
orcMetadataDefaultValue:ocr提取元数据没值则给默认值
uploadFileLimit：文件上传
配置参数  {0} 文件大小 {1} 文件名称 {2} 文件扩展名 {3} accept
- multiple：是否多选
- tip：提示消息
- sceneType：1-文档入库上传2-附件上传3-以文搜文上传4-个人库上传5-智能比对上传6-智能校对
- maxSize：最大大小（MB）
- fileCount：最大文件的个数
- maxSizeError：超出大小提示
- accept：支持上传格式
- acceptError: 格式错误提示
ctypePermission：库权限配置
statisticslabelAlias：数据大屏标签别名
jumpUrl:扫码登录
isLoginUrl: 登录地址
hotWords: 静态的热搜词汇
metadataPlaceholder: 元数据提示占位符
advanceSearch:高级搜索
-categoryList可被搜索的分类列表
searchListSet搜索列表设置
-disableDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
disabledNavBar是否隐藏导航栏
userDropDisable导航栏下拉隐藏菜单
showScreen: 显示我的筛查
searchListSet搜索列表设置
-disableDigest是否隐藏摘要
-disableMetadata是否隐藏元数据
isOrgLazyLoad 组织架构是否懒加载针对国网项目特殊处理 (国网项目设置为true)', NULL, NULL, 1);

update "plss-system".xxl_job_info set job_desc = '触发数据处理任务',update_time = '2024-12-19 10:43:19.458',executor_handler = 'triggerDataProcessTask' where id = 279173196621509;

delete from "plss-system".sys_config where config_key in('data.process.task.metadata.repair.thread.num','data.process.task.metadata.repair.batch.size','data.process.task.config');
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(358581802559429, '数据处理任务配置', 'data.process.task.config', '{"batchSleepSecond":3,"singleSleepSecond":1,"queryBatchSize":500,"allBatchSize":100,"metadataRepairThreadNum":2}', 'Y', 1, '2024-12-19 14:56:32.695', 'admin', '2024-12-19 15:30:55.003', 'admin', '', 'json', NULL, 1);

-- 删除历史菜单 数据处理任务
delete from "plss-system".sys_menu sm where sm.menu_id in(269492923393797,358615101031557,358616130421893,358616759739525);
delete from "plss-system".sys_tenant_menu sm where sm.menu_id in(269492923393797,358615101031557,358616130421893,358616759739525);
delete from "plss-system".sys_role_menu sm where sm.menu_id in(269492923393797,358615101031557,358616130421893,358616759739525);

-- 新增菜单	数据处理
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(358615101031557, 0, '数据处理', 18, 'dataprocess', '', '', '1', '1', 'M', '1', '1', '', 'icon-dataprocess', 'taoyang', '2024-12-19 16:04:17.454', 'taoyang', '2024-12-30 13:55:49.775', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 358615101031557 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(10001, 358615101031557);


-- 新增菜单	批量数据处理
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(358616130421893, 358615101031557, '批量数据处理', 1, 'batchData', 'process-data/batchData/index', '', '1', '1', 'C', '1', '1', '', '#', 'taoyang', '2024-12-19 16:06:23.106', '', '2024-12-19 16:06:23.106', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 358616130421893 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(10001, 358616130421893);

-- 新增菜单	数据处理任务
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(358616759739525, 358615101031557, '数据处理任务', 2, 'dataprocess', 'system/dataprocess/index', '', '2', '1', 'C', '1', '1', '', '#', 'taoyang', '2024-12-19 16:07:39.927', '', '2024-12-19 16:07:39.927', '', 2);
-- 绑定所有租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 358616759739525 FROM "plss-system".sys_tenant st;
-- 运营管理员增加
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) VALUES(10001, 358616759739525);


-- 数据处理任务名称字典
delete from "plss-system".sys_dict_type where dict_type = 'sys_datatask_type';
delete from "plss-system".sys_dict_data where dict_type = 'sys_datatask_type';
INSERT INTO "plss-system".sys_dict_type (dict_id, dict_name, dict_type, status, create_time, create_by, update_time, update_by, remark) VALUES(369216017867461, '数据处理任务名称', 'sys_datatask_type', '1', '2025-01-03 15:31:54.683', 'taoyang249', '2025-01-03 15:31:54.683', '', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369216888578757, 0, '元数据修复-修改名称', '11', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:33:40.970', '', '2025-01-03 15:33:40.970', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217092657861, 1, '元数据修复-修改值类型', '12', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:34:05.881', '', '2025-01-03 15:34:05.881', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217296573125, 2, '元数据修复-修改名称和值类型', '13', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:34:30.773', '', '2025-01-03 15:34:30.773', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217612489413, 3, '文档打标签', '20', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:35:09.337', '', '2025-01-03 15:35:09.337', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217764950725, 4, '元数据校正', '30', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:35:27.948', 'taoyang249', '2025-01-03 15:36:30.323', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217845453509, 5, '基准时间更新', '40', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:35:37.775', 'taoyang249', '2025-01-03 15:36:37.956', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217919492805, 6, '批量数据删除', '50', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:35:46.813', 'taoyang249', '2025-01-03 15:36:42.392', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369217998291653, 7, '批量添加入库位置', '60', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:35:56.432', 'taoyang249', '2025-01-03 15:36:48.030', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369218070643397, 8, '批量文档打标签', '70', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:36:05.264', 'taoyang249', '2025-01-03 15:36:53.861', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369218146501317, 9, '批量设置元数据值', '80', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:36:14.524', 'taoyang249', '2025-01-03 15:37:00.145', '');
INSERT INTO "plss-system".sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES(369218225308357, 10, '按条件修改元数据值', '90', 'sys_datatask_type', '', 'default', 'N', '1', 'taoyang249', '2025-01-03 15:36:24.144', 'taoyang249', '2025-01-03 15:37:04.927', '');



-- 添加前台搜索列表视图配置
delete from "plss-system".sys_config where "config_key" = 'search.list.view.config';
INSERT INTO "plss-system".sys_config ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (338104008837765, '前台搜索列表视图配置', 'search.list.view.config', '{"viewType":1,"repostoryType":[1,2,3],"repostoryIds":[]}', 'Y', 1, '2024-11-20 16:34:22.016', 'admin', '2024-11-20 18:38:25.936013', 'admin', 'listType:1默认视图 2列表视图简洁模式 3列表视图丰富模式
repostoryType：库类型  1工作库 2参考库 3个人库 4模板库 5范文样例库 6安全文库
repostoryIds：库id集合', NULL, NULL, 1);

-- 添加前台搜索列表视图动态表头
delete from "plss-system".sys_table_header_config where "id" = 338031541160581;
INSERT INTO "plss-system".sys_table_header_config ("id", "function_module", "table_type", "table_names", "field_count", "config_type", "repo_id", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark", "code") VALUES (338031541160581, '前台搜索', '搜索列表', 'rc_record', 0, 0, 0, 1, '1', '3', '2024-11-20 14:06:55.865827', '', '2024-11-20 14:06:55.866907', '', NULL);

delete from "plss-system".sys_table_header where CONFIG_ID = 338031541160581;
INSERT INTO "plss-system".sys_table_header (ID, CONFIG_ID, FIELD_TYPE, FIELD_KEY, FIELD_NAME, FIELD_SHOW_NAME, METADATA_CATEGORY_ID, METADATA_CATEGORY_NAME, ORDER_BY, STATUS, DEL_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (338071452713029, 338031541160581, 1, 'title', '标题', '标题', 0, '', 0, 1, '1', '1', '2024-11-20 15:28:07.882097', '', '2024-11-20 15:28:07.894235', '');
INSERT INTO "plss-system".sys_table_header (ID, CONFIG_ID, FIELD_TYPE, FIELD_KEY, FIELD_NAME, FIELD_SHOW_NAME, METADATA_CATEGORY_ID, METADATA_CATEGORY_NAME, ORDER_BY, STATUS, DEL_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (338071452745797, 338031541160581, 2, '1121443148293', '发文字号', '文号/期号', 1160480783109, '公文元数据', 1, 1, '1', '1', '2024-11-20 15:28:07.885291', '', '2024-11-20 15:28:07.894380', '');
INSERT INTO "plss-system".sys_table_header (ID, CONFIG_ID, FIELD_TYPE, FIELD_KEY, FIELD_NAME, FIELD_SHOW_NAME, METADATA_CATEGORY_ID, METADATA_CATEGORY_NAME, ORDER_BY, STATUS, DEL_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (338071452786757, 338031541160581, 2, '2732784525061', '密级', '密级', 1160480783109, '公文元数据', 2, 1, '1', '1', '2024-11-20 15:28:07.889821', '', '2024-11-20 15:28:07.894409', '');
INSERT INTO "plss-system".sys_table_header (ID, CONFIG_ID, FIELD_TYPE, FIELD_KEY, FIELD_NAME, FIELD_SHOW_NAME, METADATA_CATEGORY_ID, METADATA_CATEGORY_NAME, ORDER_BY, STATUS, DEL_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (338071452811333, 338031541160581, 2, '1122381916165', '印发日期', '成文日期', 1160480783109, '公文元数据', 3, 1, '1', '1', '2024-11-20 15:28:07.893210', '', '2024-11-20 15:28:07.894425', '');



-- 用户外观设置
CREATE TABLE "plss-system".sys_user_appearance (
                                                   id int8 NOT NULL, -- 主键
                                                   theme VARCHAR(256) NOT NULL, -- 主题配置
                                                   font_size VARCHAR(2) NOT NULL, -- 字号1，标准，2，大号字体
                                                   user_id int8 NOT NULL, -- 用户id
                                                   create_by int8 NOT NULL, -- 创建人id
                                                   create_time timestamp NOT NULL, -- 创建时间
                                                   modified_by int8 NOT NULL, -- 更新人id
                                                   modified_time timestamp NOT NULL, -- 更新时间
                                                   CONSTRAINT rc_plan_audit_user_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN "plss-system".sys_user_appearance.id IS '主键';
COMMENT ON COLUMN "plss-system".sys_user_appearance.theme IS '主题配置';
COMMENT ON COLUMN "plss-system".sys_user_appearance.font_size IS '字号1，标准，2，大号字体';
COMMENT ON COLUMN "plss-system".sys_user_appearance.user_id IS '用户id';
COMMENT ON COLUMN "plss-system".sys_user_appearance.create_by IS '创建人id';
COMMENT ON COLUMN "plss-system".sys_user_appearance.create_time IS '创建时间';
COMMENT ON COLUMN "plss-system".sys_user_appearance.modified_by IS '更新人id';
COMMENT ON COLUMN "plss-system".sys_user_appearance.modified_time IS '更新时间';

-- 文档类型管理页面添加新增分组、分组重命名、分组删除三个按钮
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373500316247109, 680207111173, '新增分组', 5, '', '', '', '2', '1', 'F', '1', '1', 'system:record-type-group:add', '#', 'admin', '2025-01-09 16:48:20.32525', 'admin', '2025-01-09 16:48:33.176583', '', 2);
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373500986172485, 680207111173, '分组重命名', 6, '', '', '', '2', '1', 'F', '1', '1', 'system:record-type-group:edit', '#', 'admin', '2025-01-09 16:49:42.103117', '', '2025-01-09 16:49:42.103117', '', 2);
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373501142205509, 680207111173, '分组删除', 7, '', '', '', '2', '1', 'F', '1', '1', 'system:record-type-group:delete', '#', 'admin', '2025-01-09 16:50:01.150321', '', '2025-01-09 16:50:01.150321', '', 2);

-- 元数据管理页面添加新增类型、编辑类型、删除类型三个按钮
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373497916728389, 699369213445, '新增类型', 5, '', '', '', '2', '1', 'F', '1', '1', 'system:metadata-type:add', '#', 'admin', '2025-01-09 16:43:27.417735', '', '2025-01-09 16:43:27.417735', '', 2);
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373498328228933, 699369213445, '编辑类型', 6, '', '', '', '2', '1', 'F', '1', '1', 'system:metadata-type:edit', '#', 'admin', '2025-01-09 16:44:17.646562', '', '2025-01-09 16:44:17.646562', '', 2);
INSERT INTO "plss-system".sys_menu("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark", "system_menu_flag") VALUES (373498495091781, 699369213445, '删除类型', 7, '', '', '', '2', '1', 'F', '1', '1', 'system:metadata-type:delete', '#', 'admin', '2025-01-09 16:44:38.015553', '', '2025-01-09 16:44:38.015553', '', 2);

-- 以上按钮绑定租户
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373500316247109 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373500986172485 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373501142205509 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373497916728389 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373498328228933 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 373498495091781 FROM "plss-system".sys_tenant st;

-- 角色绑定以上按鈕
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373500316247109 FROM "plss-system".sys_role_menu where menu_id  = 3868765636613 ;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373500986172485 FROM "plss-system".sys_role_menu where menu_id  = 3868770451461 ;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373501142205509 FROM "plss-system".sys_role_menu where menu_id  = 240452403176005 ;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373497916728389 FROM "plss-system".sys_role_menu where menu_id  = 3868723818757 ;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373498328228933 FROM "plss-system".sys_role_menu where menu_id  = 3868727505413 ;
INSERT INTO "plss-system".sys_role_menu(role_id, menu_id) select role_id, 373498495091781 FROM "plss-system".sys_role_menu where menu_id  = 3868731898117 ;

-- 删除系统管理员角色绑定基础数据管理菜单的数据
delete from "plss-system".sys_role_menu where  menu_id in (4035890139909,4035893468933,3868784745221,4016792224773,4016795066373,680207111173,699369213445,2206751772677,2206732500229,3868085615877,3868089044485,3868117296901,3868723818757,3868727505413,3868731898117,3868765636613,3868770451461,3868775889669,240452403176005,373497916728389,373498328228933,373498495091781,373500316247109,373500986172485,373501142205509 )  and role_id  = 10002;

-- 更新默认排版，勾选内容纠正下的去除空行/空格选择项和符号纠正选择项
UPDATE "plss-system".sys_layout set "content"='%7B%22id%22%3A205064713029061%2C%22name%22%3A%22%E5%85%9A%E6%94%BF%E6%9C%BA%E5%85%B3%E5%85%AC%E6%96%87%E6%A0%BC%E5%BC%8F(GB%2FT9704%E4%B8%802012)%22%2C%22tabGroup%22%3A%5B%7B%22name%22%3A%22%E9%A1%B5%E9%9D%A2%22%2C%22margins%22%3A%7B%22name%22%3A%22%E9%A1%B5%E8%BE%B9%E8%B7%9D%22%2C%22margin%22%3A%5B%7B%22name%22%3A%22%E4%B8%8A%22%2C%22value%22%3A3.7%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E4%B8%8B%22%2C%22value%22%3A3.5%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E5%B7%A6%22%2C%22value%22%3A2.8%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%2C%7B%22name%22%3A%22%E5%8F%B3%22%2C%22value%22%3A2.6%2C%22unit%22%3A%22%E5%8E%98%E7%B1%B3%22%7D%5D%7D%2C%22pageNumber%22%3A%7B%22name%22%3A%22%E9%A1%B5%E7%A0%81%22%2C%22status%22%3Atrue%2C%22fontValue%22%3A%22%E6%96%B9%E6%AD%A3%E5%B0%8F%E6%A0%87%E5%AE%8B%E7%AE%80%E4%BD%93%22%2C%22fontSizeValue%22%3A%2222%22%2C%22fontStyle%22%3A%22-1-%22%2C%22fontPosition%22%3A%22%E5%A5%87%E5%B7%A6%E5%81%B6%E5%8F%B3%22%2C%22fontBold%22%3Atrue%7D%2C%22contentCorrections%22%3A%7B%22name%22%3A%22%E5%86%85%E5%AE%B9%E7%BA%A0%E6%AD%A3%22%2C%22status%22%3Atrue%2C%22removeSpaces%22%3Atrue%2C%22symbolCorrection%22%3Atrue%7D%7D%2C%7B%22name%22%3A%22%E5%85%AC%E6%96%87%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E6%96%B9%E6%AD%A3%E5%B0%8F%E6%A0%87%E5%AE%8B%E7%AE%80%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2222%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphCenter%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E6%97%A0%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E6%AD%A3%E6%96%87%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%B8%80%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E9%BB%91%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%BA%8C%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E6%A5%B7%E4%BD%93%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E4%B8%89%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%2C%7B%22name%22%3A%22%E5%9B%9B%E7%BA%A7%E6%A0%87%E9%A2%98%22%2C%22general%22%3A%7B%22chineseFont%22%3A%22%E4%BB%BF%E5%AE%8B_GB2312%22%2C%22westernFont%22%3A%22Times%20New%20Roman%22%2C%22fontSize%22%3A%2216%22%2C%22bold%22%3Afalse%2C%22alignment%22%3A%22wdAlignParagraphJustify%22%2C%22textDirection%22%3A%22wdReadingOrderLtr%22%7D%2C%22indentation%22%3A%7B%22beforeText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22afterText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%2C%22specialtext%22%3A%7B%22name%22%3A%22%22%2C%22type%22%3A%22%E9%A6%96%E8%A1%8C%E7%BC%A9%E8%BF%9B%22%2C%22value%22%3A2%2C%22unit%22%3A%22%E5%AD%97%E7%AC%A6%22%7D%7D%2C%22spacing%22%3A%7B%22lineSpacing%22%3A%22%E5%9B%BA%E5%AE%9A%E5%80%BC%22%2C%22lineSpacingText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A28%2C%22unit%22%3A%22%E7%A3%85%22%7D%2C%22beforeParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%2C%22afterParagraphText%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A0%2C%22unit%22%3A%22%E8%A1%8C%22%7D%7D%7D%5D%7D' WHERE id=205064713029061;

-- 添加搜索历史条件的md5值
ALTER TABLE "plss-system"."sys_search_history"
    ADD COLUMN "search_md5" varchar(255);

CREATE INDEX "idx_search_history_md5" ON "plss-system"."sys_search_history" USING btree ("search_md5");
COMMENT ON COLUMN "plss-system"."sys_search_history"."search_md5" IS '搜索条件的md5值';

-- 清理一个月以前的搜索历史xxljob
delete from "plss-system"."xxl_job_info" WHERE "id" = 366951520682693;
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (366951520682693, 3, '每天晚上0点清理一个月以前的搜索历史（医疗需求）', '2024-12-31 10:44:46.797', '2024-12-31 10:45:42.047', 'yangjun', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'clearOverOneMonthSearchHistory', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-12-31 10:44:46.797', NULL, 1, 0, 1735660800000);

DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'syncDocProcessAllDbToEs';
INSERT INTO "plss-system".xxl_job_info
(id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type,
 schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param,
 executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (436518615444357, 2, '文档处理表全量同步es中【每月1号凌晨1点执行】', '2025-04-08 17:39:23.795', '2025-04-08 17:39:23.795', 'zdh', NULL, 'CRON', '0 1 0 1 * ?', 'DO_NOTHING', 'FIRST', 'syncDocProcessAllDbToEs', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-04-08 17:39:23.795', NULL, 0, 0, 0);



-- 附件提取任务
DELETE FROM "plss-system"."xxl_job_info" WHERE "id" = 379124901651973;
DELETE FROM "plss-system"."xxl_job_info" WHERE "id" = 379123477186053;
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (379124901651973, 2, '附件历史文件提取【每月1号凌晨1点执行】', '2025-01-17 15:31:35.22', '2025-01-17 15:33:30.624', 'zdh', NULL, 'CRON', '0 1 0 1 * ?', 'DO_NOTHING', 'FIRST', 'recordHistoryAtt', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-01-17 15:31:35.22', NULL, 0, 0, 0);
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (379123477186053, 2, '附件提取批量重试【每天凌晨2点15分执行】', '2025-01-17 15:28:41.333', '2025-01-17 16:01:51.104', 'zdh', NULL, 'CRON', '0 15 2 * * ?', 'DO_NOTHING', 'FIRST', 'retryDocAttStatus', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-01-17 15:28:41.333', NULL, 0, 0, 1737137700000);


--添加订阅数据管理，数据订阅，数据导入菜单
delete from "plss-system".sys_menu where menu_id in (397488410796613, 397487935332933, 397487398683205);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(397488410796613, 397487398683205, '数据订阅', 1, 'dataSubscribe', 'subscription-management/dataSubscribe/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2025-02-12 14:12:14.523', '', '2025-02-12 14:12:14.523', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(397487935332933, 397487398683205, '数据导入', 0, 'dataImport', 'subscription-management/dataImport/index', '', '2', '1', 'C', '1', '1', '', '#', '001', '2025-02-12 14:11:16.483', '', '2025-02-12 14:11:16.483', '', 2);
INSERT INTO "plss-system".sys_menu (menu_id, parent_id, menu_name, order_num, "path", component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, system_menu_flag) VALUES(397487398683205, 0, '订阅数据管理', 19, 'subscription', '', '', '1', '1', 'M', '1', '1', '', 'icon-icon_dingyue', '001', '2025-02-12 14:10:10.976', '001', '2025-02-12 14:26:55.996', '', 2);

--以上菜单绑定租户
delete from "plss-system".sys_tenant_menu where menu_id in (397488410796613, 397487935332933, 397487398683205);
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 397488410796613 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 397487935332933 FROM "plss-system".sys_tenant st;
INSERT INTO "plss-system".sys_tenant_menu(tenant_id, menu_id) SELECT st.id, 397487398683205 FROM "plss-system".sys_tenant st;

-- 运营管理员，系统管理员，超级管理员绑定以上菜单
delete from "plss-system".sys_role_menu where menu_id in (397488410796613, 397487935332933, 397487398683205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 397487398683205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 397487935332933);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10001, 397488410796613);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 397487398683205);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 397487935332933);
INSERT INTO "plss-system".sys_role_menu (role_id, menu_id) VALUES(10007, 397488410796613);

-- 更新用户性别属性类型
ALTER TABLE "plss-system".sys_user ALTER COLUMN sex TYPE varchar(1);


delete from "plss-system".sys_page_module;

INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276655948180677, '{
  "label": "智能应用",
  "icon": "application",
  "layout": {
    "w": 2,
    "h": 2
  },
  "node": {
    "type": "l-button",
    "moduleType": "application",
    "config": {
      "label": "智能应用",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 32,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-12-03 15:22:56.448', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964214222021, '{
  "label": "文库入口",
  "icon": "enter",
  "layout": {
    "w": 2,
    "h": 2
  },
  "node": {
    "type": "l-button",
    "moduleType": "library",
    "config": {
      "label": "我的文库",
      "icon": "/plss/front/static/themes/red/report.svg",
      "iconSize": 32,
      "fontWeight": 400,
      "fontSize": 16,
      "color": "rgba(13,13,13,0.66)",
      "horizontal": false,
      "href": ""
    }
  }
}', '2024-08-22 16:36:07.555', 1612860642053, '2024-12-03 15:23:06.919', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276468841450693, '{
  "label": "容器组件",
  "icon": "container",
  "layout": {
    "w": 6,
    "h": 2,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-container",
    "children": [],
    "config": {
      "label": "",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-25 14:37:17.871', 1612860642053, '2024-12-03 15:23:33.124', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337476010691205, '{
  "label": "图片组件",
  "icon": "image",
  "layout": {
    "w": 12,
    "h": 2
  },
  "node": {
    "type": "l-image",
    "config": {
      "icon": "/plss/front/static/themes/red/index_bg.jpg",
      "isBottom": false,
      "fullWidth": false,
      "horizontalSpacing": 0,
      "borderRadius": 0
    }
  }
}', '2024-11-19 19:16:42.086', 1, '2024-12-03 15:23:42.613', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(338118581332613, '{
  "label": "分类组件",
  "icon": "classify",
  "layout": {
    "w": 4,
    "h": 4,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-search-classify",
    "moduleType": "l-search-classify",
    "config": {
      "isCollapseOpen": "openFirst"
    }
  }
}', '2024-11-20 17:04:00.885', 1, '2024-12-03 15:23:51.508', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(337475096447621, '{
  "label": "筛选组件",
  "icon": "filter",
  "layout": {
    "w": 10,
    "h": 2,
    "minH": 2,
    "minW": 8
  },
  "node": {
    "type": "c-detail-search",
    "moduleType": "c-detail-search",
    "config": {
      "disabledAllOption": false,
      "disabledSearchArticle": false,
      "disabledLibrary": true,
      "disabledAdvancedSearch": true,
      "disabledMultiple": false
    }
  }
}', '2024-11-20 19:14:50.482', 1, '2024-12-03 15:24:03.300', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(276471564602565, '{
  "label": "LOGO",
  "icon": "logo",
  "layout": {
    "w": 4,
    "h": 1
  },
  "node": {
    "type": "l-button",
    "moduleType": "logo",
    "config": {
      "iconSize": 28,
      "fontWeight": 700,
      "fontSize": 20,
      "color": "rgba(255,255,255,1)",
      "horizontal": true,
      "href": ""
    }
  }
}', '2024-08-22 14:42:50.288', 1612860642053, '2025-02-05 11:21:50.536', 1, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275044225289413, '{
  "label": "列表组件",
  "icon": "lists",
  "layout": {
    "w": 6,
    "h": 6,
    "minH": 3,
    "minW": 4
  },
  "node": {
    "type": "l-lists",
    "moduleType": "lists",
    "config": {
      "label": "列表标题",
      "showDigest": false,
      "showTime": false,
      "showMetadata": false,
      "pageSize": 10,
      "requestPath": "queryLatestRecordList",
      "icon": "",
      "pageType": 0,
      "orderType": 0,
      "showTotal": 1
    }
  }
}', '2024-08-22 17:18:54.532', 1612860642053, '2025-01-06 10:52:34.931', 1, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(274964391824581, '{
  "label": "文库列表",
  "icon": "library",
  "layout": {
    "w": 6,
    "h": 4,
    "minH": 2,
    "minW": 2
  },
  "node": {
    "type": "l-container",
    "moduleType": "library-container",
    "children": [],
    "config": {
      "label": "文库列表",
      "center": false,
      "icon": "",
      "background": "rgba(255,255,255,1)",
      "color": ""
    }
  }
}', '2024-08-22 15:36:29.236', 1612860642053, '2024-12-03 15:22:43.624', 299806062537861, 0);
INSERT INTO "plss-system".sys_page_module (id, module_config, create_time, create_by, update_time, update_by, module_type) VALUES(275043279088837, '{
  "label": "首页搜索",
  "icon": "home-search",
  "layout": {
    "w": 12,
    "h": 2,
    "minW": 12,
    "minH": 2
  },
  "node": {
    "type": "c-home-search",
    "moduleType": "c-home-search",
    "config": {
      "disabledTabs": false,
      "disabledHotWord": false,
      "disabledSearchArticle": false,
      "disabledLibrary": true,
      "disabledAdvancedSearch": true,
      "hotWordLabel": "热搜词",
      "tabs": [
        {
          "label": "智能搜索",
          "id": 0,
          "name": "intelligent-search",
          "show": true
        },
        {
          "label": "政务问答",
          "id": 1,
          "name": "chat",
          "show": true
        },
        {
          "label": "公文写作",
          "id": 2,
          "name": "write",
          "show": true
        }
      ],
      "hotWordData": [
        "静态搜索词"
      ],
      "hotWordType": 1
    }
  }
}', '2024-08-22 14:42:59.029', 1612860642053, '2024-12-04 22:31:51.851', 299806062537861, 0);


-- 组织机构id更新 1->20001,2->20002
UPDATE "plss-system".sys_org SET org_id=20001 WHERE org_id=1;
UPDATE "plss-system".sys_org SET org_id=20002 WHERE org_id=2;

UPDATE "plss-system".sys_org SET ancestors = REPLACE(ancestors, ',1,', ',20001,');
UPDATE "plss-system".sys_org SET ancestors = '0,20001' where parent_id = 1 or parent_id = 20001;

UPDATE "plss-system".sys_org SET ancestors = REPLACE(ancestors, ',2,', ',20002,');
UPDATE "plss-system".sys_org SET ancestors = '0,20002' where parent_id = 2 or parent_id = 20002;

UPDATE "plss-system".sys_org SET parent_id=20001 WHERE parent_id=1;
UPDATE "plss-system".sys_org SET parent_id=20002 WHERE parent_id=2;

UPDATE "plss-record".rc_resource_manager SET visitor_id=20001 WHERE vtype=1 and visitor_id=1;
UPDATE "plss-record".rc_resource_manager SET visitor_id=20002 WHERE vtype=1 and visitor_id=2;

UPDATE "plss-record".rc_resource_permission SET visitor_id=20001 WHERE vtype=1 and visitor_id=1;
UPDATE "plss-record".rc_resource_permission SET visitor_id=20002 WHERE vtype=1 and visitor_id=2;

UPDATE "plss-record".rc_record_share_object SET share_object_id=20001 WHERE share_object_type=2 AND share_object_id=1;
UPDATE "plss-record".rc_record_share_object SET share_object_id=20002 WHERE share_object_type=2 AND share_object_id=1;

UPDATE "plss-system".sys_user_org SET org_id=20001 WHERE org_id=1;
UPDATE "plss-system".sys_user_org SET org_id=20002 WHERE org_id=2;

UPDATE "plss-record".rc_repository SET org_id=20001 WHERE org_id=1;
UPDATE "plss-record".rc_repository SET org_id=20002 WHERE org_id=2;

UPDATE "plss-open".op_app_info SET org_id=20001 WHERE org_id=1;
UPDATE "plss-open".op_app_info SET org_id=20002 WHERE org_id=2;




-- 添加ai中台聚合文档数量地址
delete from  "plss-system".sys_config where config_key = 'dp.feature.impl.buttJointOtherKey';
INSERT INTO "plss-system".sys_config
(config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag)
VALUES(3864463331077, '文档处理服务-功能对接其他配置', 'dp.feature.impl.buttJointOtherKey', '{"aiAlgorithmSemaphoreCapacity":10,"aiMiddleSemaphoreCapacity":10,"ocrSemaphoreCapacity":10,"aiMiddleOutTime":600000,"ocrOutTime":600000,"aiAlgorithmOutTime":600000,"flushReleaseTimeRate":5,"aiMiddleKnowledgeAddress":"/openApi/lyricsProcess","aiMiddleSubmitAddress":"/openApi/data/save","aiMiddleSearchAddress":"/openApi/aggregations/searchByFieldList","aiMiddleSearchUrl":"/openApi/data/search","aiMiddleSearchByTextUrl":"/document/findSimilarDoc","aiMiddleExportAddress":"/openApi/expEsRecord","aiMiddleImportAddress":"/openApi/impEsRecord","aiMiddleUpdateEsAddress":"/openApi/data/update","aiMiddleDeleteAddress":"/openApi/data/delete","aiDigestWordNumber":1000,"aiMiddleSwitch":1,"aiAlgorithmTaskAddress":"/task","aiAlgorithmVectorAddress":"/algorithm","aiCallbackAddress":"/process/v1/feature/callback","ocrCallbackAddress":"/process/v1/ocrCallBack/reply","aiMiddleAggSearchUrl":"/openApi/aggregations/aggrCountByField","aiMiddleSearchSecretUrl":"/openApi/searchSecretData","aiMiddleBindSegmentAddress":"/generalSecretarySpeech/bindId","aiMiddleUnBindSegmentAddress":"/generalSecretarySpeech/unbindId","aiMiddleAggByFieldUrl":"/openApi/aggregations/aggrByField"}', 'Y', 1, '2024-01-22 17:12:39.892', '001', '2025-03-05 10:18:51.632', 'admin', 'ocrOutTime=ocr回调超时时间（单位：毫秒）10分钟=600000
aiOutTime= ai中台回调超时时间（单位：毫秒）
aiAlgorithmOutTime=ai算法平台回调超时时间（单位：毫秒）
ocrSemaphoreCapacity=OCR服务平台速率容量
aiMiddleSemaphoreCapacity=AI中台服务速率容量
aiAlgorithmSemaphoreCapacity= AI算法平台速率容量
aiAlgorithmTaskAddress=提交ai算法平台提取任务地址
aiAlgorithmVectorAddress=提交ai算法平台关键词向量化
aiMiddleKnowledgeAddress=提交ai中台知识提取地址
aiMiddleSubmitAddress=ai中台http请求对接提交任务地址
aiMiddleUpdateEsAddress=更新ai中台es的地址
aiMiddleDeleteAddress=删除ai中台文件es的地址
aiMiddleSearchAddress=查询ai中台提取结果地址
aiMiddleSearchUrl=ai中台搜索地址
aiMiddleSearchByTextUrl=ai中台以文搜文地址
callbackAddress=ai回调到文档处理服务地址
aiDigestWordNumber=摘要字数上限
aiMiddleSwitch=对接ai中台开关 1-开 2-关闭
aiMiddleSearchSecretUrl=ai中台安全文件搜索地址
aiMiddleBindSegmentAddress=ai中台绑定文档片段地址
aiMiddleUnBindSegmentAddress=ai中台解绑绑定文档片段地址
flushReleaseTimeRate=刷基准时间每秒速率
aiMiddleAggByFieldUrl:ai中台聚合文档数量地址', NULL, NULL, 1);


-- 更新自认证登录配置中的密码为明文
DELETE FROM "plss-system".sys_config WHERE config_key='sys.auto.login.config';
INSERT INTO "plss-system".sys_config (config_id, config_name, config_key, config_value, config_type, status, create_time, create_by, update_time, update_by, remark, element_type, element_name, deliver_required_item_flag) VALUES(362871892698757, '用户自认证登录配置', 'sys.auto.login.config', '{"enableAutoLogin":true,"authStrList":[{"presentationAuthCode":"5YyX5Lqs5biC5rW35reA5Yy656eR5Lh5bGA","username":"001","password":"Gwk@123456"}]}', 'Y', 1, '2024-12-25 16:24:45.339', 'admin', '2024-12-27 10:53:58.637', 'admin', '用户自认证登录配置
presentationAuthCode：自认证登录code码，使用客户名称，进行Base64之后，进行使用，最长不要超过128个字符 如：北京市海淀区科信局   ---》5YyX5Lqs5biC5rW35reA5Yy656eR5L+h5bGA
username：自认证登录配置的用户名称，如：001，注意：自认证登录只允许配置只有前台权限的用户
password：自认证登录配置的用户密码，如：Gwk@123456', NULL, NULL, 2);

DELETE FROM "plss-system".xxl_job_info WHERE executor_handler = 'syncDocProcessAllDbToEs';
INSERT INTO "plss-system".xxl_job_info
(id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type,
 schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param,
 executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source,
 glue_remark, glue_updatetime, child_job_id, trigger_status, trigger_last_time, trigger_next_time)
VALUES (436518615444357, 2, '文档处理表全量同步es中【每月1号凌晨1点执行】', '2025-04-08 17:39:23.795', '2025-04-08 17:39:23.795', 'zdh', NULL, 'CRON', '0 1 0 1 * ?', 'DO_NOTHING', 'FIRST', 'syncDocProcessAllDbToEs', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-04-08 17:39:23.795', NULL, 0, 0, 0);


-- 修改3.0.0发版更新日志
delete from "plss-system".sys_version where version_no = 'v3.0.0';
insert into "plss-system".sys_version (id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by) values (5, 'v3.0.0', 'UI界面全新改版；AI问答接入“DeepSeek”深度推理模型；新增问答“联网搜索、上传文件”功能；新增“搜索附件”功能；新增“个性化设置-主题&字号”功能；新增“AI仿写”功能；新增“内容排版为公文格式”功能。', '1', 'v3.0.0_front.md', '2025-03-12', '1', '1', '2025-03-12', 1);
insert into "plss-system".sys_version (id, version_no, version_desc, version_type, version_md, publish_time, menu_visible, remind_visible, create_time, create_by) values (6, 'v3.0.0', '增加统计分析功能；列表展示多视图模式；入库文档支持批量添加位置、批量设置元数据值；新增附件入库支持前台检索；新增AI模板排序及提示词显示；新增批量数据处理；可视化配置列表组件增加最新发布。', '2', 'v3.0.0_backend.md', '2025-03-12', '1', '1', '2025-03-12', 1);

-- 版本配置信息
delete from "plss-system".sys_config where config_key = 'sys.version';
INSERT INTO "plss-system".sys_config (config_id,config_name,config_key,config_value,config_type,status,create_time,create_by,update_time,update_by,remark,element_type,element_name) VALUES
    (203685690483910,'版本信息','sys.version','{"front":"v3.0.0","backend":"v3.0.0"}','Y',1,'2025-03-12 00:00:00.000','admin','2025-03-12 00:00:00.000','admin','系统版本信息',NULL,NULL);


-- 20250424 追加
-- 文库管理员新增借阅审批菜单
delete from "plss-system"."sys_role_menu" where role_id = 10007 and menu_id in(2932868278789,
                                                                               3882394998789,
                                                                               4701677326085,
                                                                               4701683196933);

INSERT INTO "plss-system"."sys_role_menu" ("role_id", "menu_id") VALUES (10007, 2932868278789);
INSERT INTO "plss-system"."sys_role_menu" ("role_id", "menu_id") VALUES (10007, 3882394998789);
INSERT INTO "plss-system"."sys_role_menu" ("role_id", "menu_id") VALUES (10007, 4701677326085);
INSERT INTO "plss-system"."sys_role_menu" ("role_id", "menu_id") VALUES (10007, 4701683196933);

-- 添加文库管理员审批开关
delete from "plss-system"."sys_config" where config_key = 'sys.project.config_11';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (323255684280197, '项目参数配置-jxxs', 'sys.project.config_11', '{"platform":"xiushui","ssoLoginAuthCode":true,"isChangeBorrowDay":false,"repoManagerBorrowAudit":true}', 'Y', 1, '2024-10-30 17:05:22.397', 'admin', '2024-12-24 21:22:45.561431', 'admin', '江西修水配置，此配置需要全量更新。
ssoLoginAuthCode：单点登录配置
isChangeBorrowDay：借阅审批是否可以修改天数
repoManagerBorrowAudit:文库管理员角色是否可以借阅审批', NULL, NULL, 1);

-- 刷借阅文档所在的库信息（有文库管理员借阅审批需求的执行此任务）
delete from "plss-system"."xxl_job_info" where id = 362840965580421;
INSERT INTO "plss-system"."xxl_job_info" ("id", "job_group", "job_desc", "add_time", "update_time", "author", "alarm_email", "schedule_type", "schedule_conf", "misfire_strategy", "executor_route_strategy", "executor_handler", "executor_param", "executor_block_strategy", "executor_timeout", "executor_fail_retry_count", "glue_type", "glue_source", "glue_remark", "glue_updatetime", "child_job_id", "trigger_status", "trigger_last_time", "trigger_next_time") VALUES (362840965580421, 2, '刷借阅文档所在的库信息（有文库管理员借阅审批需求的执行此任务）', '2024-12-25 15:21:50.051', '2024-12-25 15:21:50.051', 'yangjun', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'flushBorrowRecordRepo', NULL, 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2024-12-25 15:21:50.051', NULL, 0, 0, 0);


-- 更新文件的下载限制配置
delete from "plss-system".sys_config where config_key = 'record.downloads.files.limits.configKey';
INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark", "element_type", "element_name", "deliver_required_item_flag") VALUES (348711930726917, '反爬虫文件时间内下载个数限制配置', 'record.downloads.files.limits.configKey', '[{"fileType":1,"timeLimit":60,"downCountLimit":100}]', 'Y', 1, '2024-12-05 16:16:14.356', 'admin', '2024-12-05 16:18:01.141443', 'admin', 'fileType=1-主件，2附件
timeLimit=时间(单位分钟)
downloadCountLimit=下载文件个数
', 'json', NULL, 1);

INSERT INTO "plss-system".xxl_job_info (id,job_group, job_desc, add_time, update_time,
                                        author,
                                        alarm_email, schedule_type, schedule_conf,
                                        misfire_strategy,
                                        executor_route_strategy, executor_handler,
                                        executor_param,
                                        executor_block_strategy, executor_timeout,
                                        executor_fail_retry_count,
                                        glue_type, glue_source, glue_remark,
                                        glue_updatetime, child_job_id,
                                        trigger_status, trigger_last_time,
                                        trigger_next_time)
VALUES (372073744763077,(SELECT id FROM "plss-system".xxl_job_group WHERE app_name = 'plss_record'),
        '元数据值校正', '2025-01-07 14:00:00',
        '2025-01-07 14:00:00', 'xks', NULL, 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST',
        'triggerMetadataValueUpdateMsg', NULL,
        'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, NULL, '2025-01-07 14:00:00', NULL, 1, 0, 0);
