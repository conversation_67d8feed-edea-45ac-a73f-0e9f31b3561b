INSERT INTO `plss_system`.`sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `status`, `create_time`, `create_by`, `update_time`, `update_by`, `remark`, `element_type`, `element_name`, `deliver_required_item_flag`) VALUES (362871892698757, '用户自认证登录配置', 'sys.auto.login.config', '{`enableAutoLogin`:true,`authStrList`:[{`presentationAuthCode`:`5Lit5aSu5Yqe5YWs5Y6F`,`username`:`suwellSys`,`password`:`76d759a6c6ee52fd612f99ef0c841bc9`}]}', 'Y', 1, '2024-12-25 16:24:45.339', 'admin', '2024-12-26 14:35:34.745847', 'admin', '用户自认证登录配置
presentationAuthCode：自认证登录code码，使用客户名称，进行Base64之后，进行使用，最长不要超过128个字符 如：北京市海淀区科信局   ---》5YyX5Lqs5biC5rW35reA5Yy656eR5L+h5bGA
username：自认证登录配置的用户名称，如：001，注意：自认证登录只允许配置只有前台权限的用户
password：自认证登录配置的用户密码，如：Gwk@123456', NULL, NULL, 2);