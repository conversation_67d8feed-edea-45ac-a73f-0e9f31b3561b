package com.suwell.plss.system.service.test;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.domain.AccessTrendDateDTO;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.dto.OptTrendDTO;
import com.suwell.plss.system.manager.service.IUserService;
import com.suwell.plss.system.service.SearchHistoryService;
import com.suwell.plss.system.service.SysOperLogService;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = PlssSystemApplication.class)
class SysUserServiceImplTest {

    @Resource
    private IUserService sysUserService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private SearchHistoryService searchHistoryService;

    @Test
    void updatePassword() {
        SysUser user = new SysUser();
        user.setUserId("666");
        user.setPassword(SecurityUtils.encryptPassword("123456"));
        sysUserService.resetPwd(user);
    }

    @Test
    void queryOperLogAccessTrendTest() {
        AccessTrendDateDTO req = DateUtils.getAccessTrendDateDTO();
        List<OptTrendDTO> optTrendDTOS = sysOperLogService.queryOperLogAccessTrend(new Date(req.getThirtyDaysAgo()),
                new Date(req.getTodayBegin()), 1,
                Lists.newArrayList("1612860642053", "243813451571333"));
        List<OptTrendDTO> dtos = searchHistoryService.querySearchHistoryAccessTrend(
                new Date(req.getThirtyDaysAgo()), new Date(req.getTodayBegin()),
                Lists.newArrayList("1612860642053", "243813451571333"));
        System.out.println(JSON.toJSONString(dtos));
    }

}