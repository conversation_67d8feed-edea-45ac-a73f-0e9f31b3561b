package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.mapper.SysOrgMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * SysDeptMapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysOrgMapperTest {

    @Resource
    SysOrgMapper sysOrgMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 查询机构管理数据
     */
    @Test
    public void testSelectOrgList() {
        SysOrg sysOrg = new SysOrg();

        sysOrg.setOrgId("101");
        sysOrg.setParentId("100");
        sysOrg.setOrgName("公司");
        sysOrg.setStatus("0");
        HashMap<String, Object> params = new HashMap<>();
        params.put("dataScope", "1");
        sysOrg.setParams(params);

        List<SysOrg> sysOrgList = sysOrgMapper.selectOrgList(sysOrg);

        log.info("查询机构管理数据成功，查询结果：{}", sysOrgList);
    }

    /**
     * 根据角色ID查询机构树信息
     */
    @Test
    public void testSelectOrgListByRoleId() {
        String roleId = "1";
        String orgCheckStrictly = UserConstants.YSE_STATUS;
        List<String> orgIdList = sysOrgMapper.selectOrgListByRoleId(roleId, orgCheckStrictly,roleId);
        log.info("根据角色ID查询机构树信息成功，查询结果", orgIdList);
    }

    /**
     * 根据机构ID查询信息
     */
    @Test
    public void testSelectOrgById() {
        String orgId = "1";
        SysOrg sysOrg = sysOrgMapper.selectOrgById(orgId);
        log.info("根据机构ID查询信息成功，查询结果：{}", sysOrg);
    }


    /**
     * 根据ID查询所有子机构
     */
    @Test
    public void testSelectChildrenOrgById() {
        String orgId = "1";
        List<SysOrg> sysOrgList = sysOrgMapper.selectChildrenOrgById(orgId);
        log.info("根据ID查询所有子机构成功，查询结果：", sysOrgList);
    }

    /**
     * 根据ID查询所有子机构条数（正常状态）
     */
    @Test
    public void testSelectNormalChildrenOrgById() {
        String orgId = "100";
        String tenantId = "0";
        int i = sysOrgMapper.selectNormalChildrenOrgById(orgId,tenantId);
        log.info("根据ID查询所有子机构条数（正常状态）成功，查询结果：{}", i);
    }

    /**
     * 是否存在子节点
     */
    @Test
    public void testHasChildByOrgId() {
        String orgId = "100";
        String tenantId ="0";
        int i = sysOrgMapper.hasChildByOrgId(orgId,tenantId);
        log.info("是否存在子节点查询成功，查询结果：{}", i);
    }

    /**
     * 查询机构是否存在用户
     */
    @Test
    public void testCheckOrgExistUser() {
        String orgId = "100";
        int i = sysOrgMapper.checkOrgExistUser(orgId);
        log.info("查询机构是否存在用户成功，查询结果：{}", i);
    }

    /**
     * 校验机构名称是否唯一
     */
    @Test
    public void testCheckOrgNameUnique() {
        String orgName = "ff";
        String parentId = "100";
        SysOrg sysOrg = sysOrgMapper.checkOrgNameUnique(orgName, parentId);
        log.info("校验机构名称是否唯一，结果：{}", sysOrg);
    }

    /**
     * 新增机构信息
     */
    @Test
    public void testInsertOrg() {
        SysOrg sysOrg = new SysOrg();

        sysOrg.setParentId("101");
        sysOrg.setOrgName("test");
        sysOrg.setAncestors("0,100,101");
        sysOrg.setOrderNum(50);
        sysOrg.setLeader("test");
        sysOrg.setPhone("test");
        sysOrg.setEmail("test");
        sysOrg.setStatus("0");
        sysOrg.setCreateBy("test");

        int i = sysOrgMapper.insertOrg(sysOrg);
        log.info("新增机构信息成功，新增条数：{}", i);
    }

    /**
     * 修改机构信息
     */
    @Test
    public void testUpdateOrg() {
        SysOrg sysOrg = new SysOrg();

        sysOrg.setOrgId("6344021708933");
        sysOrg.setOrgName("test_test");

        int i = sysOrgMapper.updateOrg(sysOrg);
        log.info("修改机构信息成功，更新条数：{}", i);
    }

    /**
     * 修改所在机构正常状态
     */
    @Test
    public void testUpdateOrgStatusNormal() {
        String[] orgIdList = new String[]{"6344021708933"};
        sysOrgMapper.updateOrgStatusNormal(orgIdList);
        log.info("修改所在机构正常状态成功");
    }

    /**
     * 删除机构管理信息
     */
    @Test
    public void testDeleteOrgById() {
        String orgId = "6344021708933";
        int i = sysOrgMapper.deleteOrgById(orgId);
        log.info("删除机构管理信息成功，删除条数：{}", i);
    }

    /**
     * 删除机构管理信息
     */
    @Test
    public void testselectAllChildrenById() {
        List<Long> orgIdList = new ArrayList<>();
        orgIdList.add(101L);
        orgIdList.add(102L);
//        List<SysOrg> orgList = sysOrgMapper.selectAllChildrenById(orgIdList.stream().map(o -> o.toString()).collect(
//                Collectors.joining(",")));
//        log.info("删除机构管理信息成功，删除条数：{}", i);
    }
}
