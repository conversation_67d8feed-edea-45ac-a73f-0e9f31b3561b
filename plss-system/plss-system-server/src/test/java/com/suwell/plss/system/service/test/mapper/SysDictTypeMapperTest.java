package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.entity.SysDictType;
import com.suwell.plss.system.mapper.SysDictTypeMapper;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * SysDictTypeMapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysDictTypeMapperTest {

    @Resource
    private SysDictTypeMapper sysDictTypeMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 根据条件分页查询字典类型
     */
    @Test
    public void testSelectDictTypeList() {
        SysDictType sysDictType = new SysDictType();

        sysDictType.setDictName("状态");
        sysDictType.setStatus("0");
        sysDictType.setDictType("sys");
        HashMap<String, Object> params = new HashMap<>();
        params.put("beginTime", LocalDate.now().minusMonths(3).toString());
        params.put("endTime", LocalDate.now().toString());
        sysDictType.setParams(params);

        List<SysDictType> sysDictTypeList = sysDictTypeMapper.selectDictTypeList(sysDictType);
        log.info("根据条件分页查询字典类型成功，查询结果：{}", sysDictTypeList);
    }

    /**
     * 根据所有字典类型
     */
    @Test
    public void testSelectDictTypeAll() {
        List<SysDictType> sysDictTypeList = sysDictTypeMapper.selectDictTypeAll();
        log.info("根据所有字典类型成功，查询结果：{}", sysDictTypeList);
    }

    /**
     * 根据字典类型ID查询信息
     */
    @Test
    public void testSelectDictTypeById() {
        Long dictId = 1L;
        SysDictType sysDictType = sysDictTypeMapper.selectDictTypeById(dictId);
        log.info("根据字典类型ID查询信息成功，查询结果：{}", sysDictType);
    }

    /**
     * 根据字典类型查询信息
     */
    @Test
    public void testSelectDictTypeByType() {
        String dictType = "sys_oper_type";
        SysDictType sysDictType = sysDictTypeMapper.selectDictTypeByType(dictType);
        log.info("根据字典类型查询信息成功，查询结果：{}", sysDictType);
    }

    /**
     * 通过字典ID删除字典信息
     */
    @Test
    public void testDeleteDictTypeById() {
        Long dictId = 6333676916613L;
        int i = sysDictTypeMapper.deleteDictTypeById(dictId);
        log.info("通过字典ID删除字典信息成功，删除条数：{}", i);
    }


    /**
     * 批量删除字典类型信息
     */
    @Test
    public void testDeleteDictTypeByIds() {
        Long[] dictIdList = new Long[]{6331236630661L, 6331239913605L};
        int i = sysDictTypeMapper.deleteDictTypeByIds(dictIdList);
        log.info("批量删除字典类型信息成功，删除条数：{}", i);
    }

    /**
     * 新增字典类型信息
     */
    @Test
    public void testInsertDictType() {
        SysDictType sysDictType = new SysDictType();

        sysDictType.setDictId(wfgIdGenerator.next());
        sysDictType.setDictName("test");
        sysDictType.setDictType("test");
        sysDictType.setStatus("1");
        sysDictType.setRemark("test");
        sysDictType.setCreateBy("test");

        int i = sysDictTypeMapper.insertDictType(sysDictType);
        log.info("新增字典类型信息成功，新增条数：{}", i);
    }

    /**
     * 修改字典类型信息
     */
    @Test
    public void testUpdateDictType() {
        SysDictType sysDictType = new SysDictType();
        sysDictType.setDictId(6333676916613L);
        sysDictType.setDictName("test_test");
        int i = sysDictTypeMapper.updateDictType(sysDictType);
        log.info("修改字典类型信息成功，修改条数：{}", i);
    }

    /**
     * 校验字典类型称是否唯一
     */
    @Test
    public void testcheckDictTypeUnique() {
        String dictType = "test";
        SysDictType sysDictType = sysDictTypeMapper.checkDictTypeUnique(dictType);
        log.info("校验字典类型称是否唯一，校验结果：{}", sysDictType);
    }

}
