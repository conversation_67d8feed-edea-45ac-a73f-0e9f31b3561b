package com.suwell.plss.system.service.test.controller;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import java.time.LocalDateTime;

public class GenerateTablesql {

    public static void main(String[] args) {
//        String str = """
//                     CREATE TABLE
//                                                                                                     IF
//                                                                                                     	NOT EXISTS "plss-system".sys_user_behavior_log_%s (
//                                                                                                     		ID INT8 NOT NULL,
//                                                                                                     		project_name VARCHAR ( 64 ) NOT NULL,
//                                                                                                     		gwk_version VARCHAR ( 64 ) NOT NULL,
//                                                                                                     		event_name VARCHAR ( 256 ) NOT NULL,
//                                                                                                     		user_id INT8 NOT NULL,
//                                                                                                     		user_name VARCHAR ( 64 ) NOT NULL,
//                                                                                                     		model VARCHAR ( 64 ),
//                                                                                                     		os VARCHAR ( 64 ),
//                                                                                                     		os_version VARCHAR ( 64 ),
//                                                                                                     		browser VARCHAR ( 64 ),
//                                                                                                     		browser_version VARCHAR ( 64 ),
//                                                                                                     		screen_height INT4 NOT NULL,
//                                                                                                     		screen_width INT4 NOT NULL,
//                                                                                                     		ip VARCHAR ( 64 ) NOT NULL,
//                                                                                                     		DATA TEXT NOT NULL,
//                                                                                                     		create_year INT4 NOT NULL,
//                                                                                                     		create_month INT4 NOT NULL,
//                                                                                                     		create_day INT4 NOT NULL,
//                                                                                                     		create_hour INT4 NOT NULL,
//                                                                                                     		create_minute INT4 NOT NULL,
//                                                                                                     		create_second INT4 NOT NULL,
//                                                                                                     		create_time TIMESTAMP ( 6 ) NOT NULL,
//                                                                                                     		status INT4 NOT NULL DEFAULT 1,
//                                                                                                     		del_flag INT4 NOT NULL DEFAULT 1,
//                                                                                                     		remark VARCHAR ( 512 ),
//                                                                                                     		CONSTRAINT "sys_user_behavior_log_%s_pkey" PRIMARY KEY ( ID )\s
//                                                                                                     	);
//                                                                                                     CREATE INDEX "idx_user_%s_id" ON "plss-system".sys_user_behavior_log_%s USING btree ( user_id );
//                """;
//        LocalDateTime now = LocalDateTime.now();
//        for (int i = 1; i < 37; i++) {
//            String yyyyMM = now.plusMonths(i).format(DateTimeFormatter.ofPattern("yyyyMM"));
//            String format = String.format(str, yyyyMM, yyyyMM, yyyyMM, yyyyMM);
//            System.out.println(format);
//        }

        String str = """
                     CREATE TABLE "plss-record".rc_log_recordview_%s (
                         	ID INT8,
                         	record_id INT8 NOT NULL,
                         	record_name VARCHAR ( 256 ),
                         	user_id INT8 NOT NULL,
                         	user_name VARCHAR ( 256 ),
                         	view_ip VARCHAR ( 256 ),
                         	view_useragent VARCHAR ( 256 ),
                         	view_os VARCHAR ( 256 ),
                         	view_browser VARCHAR ( 256 ),
                         	create_time TIMESTAMP ( 6 ) NOT NULL DEFAULT now( ),
                         	invalid_status INT2 NOT NULL DEFAULT 1,
                         	CONSTRAINT "rc_log_recordview_%s_pkey" PRIMARY KEY ( ID )\s
                         );
                         CREATE INDEX "idx_record_id_%s" ON "plss-record".rc_log_recordview_%s USING btree ( record_id );
                                                                                                                                                                                                  
                """;
        LocalDateTime now = LocalDateTime.now();
        for (int i = 1; i < 42; i = i + 3) {
            LocalDateTime localDateTime = now.plusMonths(i);
            int year = localDateTime.getYear();
            int quarter = (localDateTime.getMonth().getValue() - 1) / 3 + 1;
            String yyyy_q = year + StringPool.UNDERSCORE + "q" + quarter;
            String format = String.format(str, yyyy_q, yyyy_q, yyyy_q, yyyy_q);
            System.out.println(format);
        }

    }

}
