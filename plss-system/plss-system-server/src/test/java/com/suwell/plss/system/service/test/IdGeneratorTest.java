package com.suwell.plss.system.service.test;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 雪花算法的生成策略
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@Slf4j
public class IdGeneratorTest {

    @Resource
    private WFGIdGenerator wfgIdGenerator;

    /**
     * 简化雪花算法的长度，测试生成速度耗时
     */
    @Test
    public void idGeneratorNextTest() {
        long stm = System.currentTimeMillis();
        for (int i = 0; i < 200; i++) {
            System.out.println(wfgIdGenerator.next());
        }
        long etm = System.currentTimeMillis() - stm;
        log.info("idGeneratorNext cos:{}", etm);
    }
}
