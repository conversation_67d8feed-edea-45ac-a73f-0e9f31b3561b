package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysDictData;
import com.suwell.plss.system.mapper.SysDictDataMapper;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Mapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysDictDataMapperTest {

    @Resource
    private SysDictDataMapper sysDictDataMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 根据条件分页查询字典数据
     */
    @Test
    public void testSelectDictDataList() {
        SysDictData sysDictData = new SysDictData();

        sysDictData.setDictType("sys_oper_type");
        sysDictData.setDictLabel("授权");
        sysDictData.setStatus("0");

        List<SysDictData> sysDictDataList = sysDictDataMapper.selectDictDataList(sysDictData);
        log.info("根据条件分页查询字典数据成功，查询结果：{}", sysDictDataList);
    }

    /**
     * 根据字典类型查询字典数据
     */
    @Test
    public void testSelectDictDataByType() {
        String dictType = "sys_oper_type";
        List<SysDictData> sysDictDataList = sysDictDataMapper.selectDictDataByType(dictType);
        log.info("根据字典类型查询字典数据成功，查询结果：{}", sysDictDataList);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     */
    @Test
    public void testSelectDictLabel() {
        String dictType = "sys_oper_type";
        String dictValue = "4";
        String dictLabel = sysDictDataMapper.selectDictLabel(dictType, dictValue);
        log.info("根据字典类型和字典键值查询字典数据信息成功，查询结果：{}", dictLabel);
    }

    /**
     * 根据字典数据ID查询信息
     */
    @Test
    public void testSelectDictDataById() {
        Long dictCode = 22L;
        SysDictData sysDictData = sysDictDataMapper.selectDictDataById(dictCode);
        log.info("根据字典数据ID查询信息成功，查询结果：{}", sysDictData);
    }

    /**
     * 根据字典类型查询字典数据数量
     */
    @Test
    public void testCountDictDataByType() {
        String dictType = "sys_oper_type";
        int count = sysDictDataMapper.countDictDataByType(dictType);
        log.info("根据字典类型查询字典数据数量成功，查询结果：{}", count);
    }

    /**
     * 通过字典ID删除字典数据信息
     */
    @Test
    public void testDeleteDictDataById() {
        Long dictCode = 6333639082501L;
        int i = sysDictDataMapper.deleteDictDataById(dictCode);
        log.info("通过字典ID删除字典数据信息成功，删除条数：{}", i);
    }

    /**
     * 批量删除字典数据信息
     */
    @Test
    public void testDeleteDictDataByIds() {
        Long[] dictCodeList = new Long[]{6333623019909L, 6333626424453L};
        int i = sysDictDataMapper.deleteDictDataByIds(dictCodeList);
        log.info("批量删除字典数据信息成功，删除条数：{}", i);
    }

    /**
     * 新增字典数据信息
     */
    @Test
    public void testInsertDictData() {
        SysDictData sysDictData = new SysDictData();

        sysDictData.setDictSort(22L);
        sysDictData.setDictLabel("test");
        sysDictData.setDictValue("test");
        sysDictData.setDictType("test");
        sysDictData.setCssClass("test");
        sysDictData.setListClass("test");
        sysDictData.setIsDefault("N");
        sysDictData.setStatus("0");
        sysDictData.setRemark("test");
        sysDictData.setCreateBy("test");

        int i = sysDictDataMapper.insertDictData(sysDictData);
        log.info("新增字典数据信息成功，新增条数：{}", i);
    }

    /**
     * 修改字典数据信息
     */
    @Test
    public void testUpdateDictData() {
        SysDictData sysDictData = new SysDictData();

        sysDictData.setDictCode(6333639082501L);
        sysDictData.setDictLabel("test_test");
        int i = sysDictDataMapper.updateDictData(sysDictData);
        log.info("修改字典数据信息成功修改结果：{}", i);
    }

    /**
     * 同步修改字典类型
     */
    @Test
    public void testUpdateDictDataType() {
        String oldDictType = "";
        String newDictType = "";
        int i = sysDictDataMapper.updateDictDataType(oldDictType, newDictType);
        log.info("同步修改字典类型成功，修改条数：{}", i);
    }
}
