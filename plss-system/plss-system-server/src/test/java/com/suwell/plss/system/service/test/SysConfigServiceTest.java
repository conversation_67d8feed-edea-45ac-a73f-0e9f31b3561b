package com.suwell.plss.system.service.test;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysConfig;
import com.suwell.plss.system.service.SysConfigService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * SysConfigService测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysConfigServiceTest {

    @Resource
    SysConfigService sysConfigService;

    /**
     * 查询参数配置信息
     *
     * @return 参数配置信息
     */
    @Test
    public void testSelectConfigById() {
        SysConfig sysConfig = sysConfigService.selectConfigById(1L);
        log.info(sysConfig.toString());
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @return 参数键值
     */
    @Test
    public void testSelectConfigByKey() {
        String configKey = "sys.index.skinName";
        String configValue = sysConfigService.selectConfigByKey(configKey);
        log.info("查询出来的键值：{}", configValue);
    }

    /**
     * 查询参数配置列表
     *
     * @return 参数配置集合
     */
    @Test
    public void testSelectConfigList() {
        SysConfig sysConfig = new SysConfig();
        HashMap<String, Object> params = new HashMap<>();
        params.put("beginTime", "");
        params.put("endTime", "");
        sysConfig.setConfigName("皮肤样式名称");
        List<SysConfig> sysConfigList = sysConfigService.selectConfigList(sysConfig).getList();
        log.info("查询配置参数列表：{}", sysConfigList);
    }

    /**
     * 新增参数配置
     */
    @Test
    public void testInsertConfig() {
        SysConfig sysConfig = new SysConfig();

        sysConfig.setConfigName("测试数据");
        sysConfig.setConfigKey("test");
        sysConfig.setConfigValue("test");
        sysConfig.setConfigType("t");
        sysConfig.setCreateBy("test");
        sysConfig.setRemark("test");

        int i = sysConfigService.insertConfig(sysConfig);
        log.info("新增参数配置成功，插入条数：{}", i);
    }

    /**
     * 修改参数配置
     */
    @Test
    public void testUpdateConfig() {
        SysConfig sysConfig = new SysConfig();

        sysConfig.setConfigId(6322546115845L);
        sysConfig.setConfigName("test_test");

        int i = sysConfigService.updateConfig(sysConfig);
        log.info("修改参数配置成功，修改条数：{}", i);
    }

    /**
     * 批量删除参数信息
     */
    @Test
    public void testDeleteConfigByIds() {
        List<Long> configIdList = new ArrayList<>();
        configIdList.add(6322546115845L);
        sysConfigService.deleteConfigByIds(configIdList);
        log.info("批量删除参数信息成功");
    }

    /**
     * 校验参数键名是否唯一
     */
    @Test
    public void testCheckConfigKeyUnique() {
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigKey("sys.index.skinName");
        boolean checkConfigKeyUnique = sysConfigService.checkConfigKeyUnique(sysConfig);
        log.info("校验参数键名是否唯一:{}", checkConfigKeyUnique);
    }

    @Test
    public void testselectConfigByKeys() {
        List<String> querys = Arrays.asList("record.type.groupId", "sys.appearance.theme");
        List<SysConfig> dict = sysConfigService.selectConfigByKeys(querys);
        log.info(JSON.toJSONString(dict));
    }

}
