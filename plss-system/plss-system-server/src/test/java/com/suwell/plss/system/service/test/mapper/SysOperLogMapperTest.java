package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysOperLog;
import com.suwell.plss.system.mapper.SysOperLogMapper;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Mapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysOperLogMapperTest {

    @Resource
    private SysOperLogMapper sysOperLogMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 新增操作日志
     */
    @Test
    public void testInsertOperlog() {
        SysOperLog sysOperLog = new SysOperLog();
        int i = sysOperLogMapper.insertOperlog(sysOperLog);
        log.info("新增操作日志成功，新增条数：{}", i);
    }

    /**
     * 查询系统操作日志集合
     */
    @Test
    public void testSelectOperLogList() {
        SysOperLog sysOperLog = new SysOperLog();
        List<SysOperLog> sysOperLogList = sysOperLogMapper.selectOperLogList(sysOperLog);
        log.info("查询系统操作日志集合成功，查询结果：{}", sysOperLogList);
    }

    /**
     * 批量删除系统操作日志
     */
    @Test
    public void testDeleteOperLogByIds() {
        Long[] operIdList = new Long[]{};
        int i = sysOperLogMapper.deleteOperLogByIds(operIdList);
        log.info("批量删除系统操作日志成功，删除条数：{}", i);
    }

    /**
     * 查询操作日志详细
     */
    @Test
    public void testSelectOperLogById() {
        Long operId = 0L;
        SysOperLog sysOperLog = sysOperLogMapper.selectOperLogById(operId);
        log.info("查询操作日志详细成功，查询结果：{}", sysOperLog);
    }

    /**
     * 清空操作日志
     */
    @Test
    public void testCleanOperLog() {
        sysOperLogMapper.cleanOperLog();
        log.info("清空操作日志成功");
    }


}
