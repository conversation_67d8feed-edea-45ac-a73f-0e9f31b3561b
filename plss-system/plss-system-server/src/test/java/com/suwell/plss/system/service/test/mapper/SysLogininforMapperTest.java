package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysLogininfor;
import com.suwell.plss.system.mapper.SysLogininforMapper;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Mapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysLogininforMapperTest {

    @Resource
    SysLogininforMapper sysLogininforMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 新增系统登录日志
     */
    @Test
    public void testInsertLogininfor() {
        SysLogininfor sysLogininfor = new SysLogininfor();

        sysLogininfor.setUserName("test");
        sysLogininfor.setStatus("1");
        sysLogininfor.setIpaddr("test");
        sysLogininfor.setMsg("test");

        int i = sysLogininforMapper.insertLogininfor(sysLogininfor);
        log.info("新增系统登录日志成功，新增条数：{}", i);
    }

    /**
     * 批量删除系统登录日志
     */
    @Test
    public void testDeleteLogininforByIds() {
        Long[] infoIdList = new Long[]{6333739827845L, 6319925011077L};
        int i = sysLogininforMapper.deleteLogininforByIds(infoIdList);
        log.info("批量删除系统登录日志成功，删除条数：{}", i);
    }

    /**
     * 清空系统登录日志
     */
    @Test
    public void testCleanLogininfor() {
        sysLogininforMapper.cleanLogininfor();
        log.info("清空系统登录日志成功");
    }


}
