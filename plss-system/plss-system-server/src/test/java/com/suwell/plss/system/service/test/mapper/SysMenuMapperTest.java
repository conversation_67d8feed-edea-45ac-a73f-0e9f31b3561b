package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.entity.SysMenu;
import com.suwell.plss.system.mapper.SysMenuMapper;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Mapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysMenuMapperTest {


    @Resource
    SysMenuMapper sysMenuMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 查询系统菜单列表
     */
    @Test
    public void testSelectMenuList() {
        SysMenu sysMenu = new SysMenu();

        sysMenu.setMenuName("管理");
        sysMenu.setVisible("0");
        sysMenu.setStatus("0");

        List<SysMenu> sysMenuList = sysMenuMapper.selectMenuList(sysMenu);
        log.info("查询系统菜单列表成功，查询结果：{}", sysMenuList);
    }

    /**
     * 根据用户所有权限
     */
    @Test
    public void testSelectMenuPerms() {
        List<String> permsList = sysMenuMapper.selectMenuPerms();
        log.info("根据用户所有权限成功，查询结果：{}", permsList);
    }

    /**
     * 根据用户查询系统菜单列表
     */
    @Test
    public void testSelectMenuListByUserId() {
        SysMenu sysMenu = new SysMenu();

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", 2);
        sysMenu.setParams(params);
        sysMenu.setMenuName("管理");
        sysMenu.setVisible("0");
        sysMenu.setStatus("0");

        List<SysMenu> sysMenuList = sysMenuMapper.selectMenuListByUserId(sysMenu);
        log.info("根据用户查询系统菜单列表成功，查询结果：{}", sysMenuList);
    }

    /**
     * 根据角色ID查询权限
     */
    @Test
    public void testSelectMenuPermsByRoleId() {
        String roleId = "2";
        List<String> permsList = sysMenuMapper.selectMenuPermsByRoleId(roleId);
        log.info("根据角色ID查询权限成功，查询结果:{}", permsList);
    }

    /**
     * 根据用户ID查询权限
     */
    @Test
    public void testSelectMenuPermsByUserId() {
        String userId = "1";
        List<String> permsList = sysMenuMapper.selectMenuPermsByUserId(userId);
        log.info("根据用户ID查询权限成功，查询结果:{}", permsList);
    }

    /**
     * 查询所有菜单
     */
    @Test
    public void testSelectMenuTreeAll() {
        List<SysMenu> sysMenuList = sysMenuMapper.selectMenuTreeAll();
        log.info("查询所有菜单成功，查询结果：{}", sysMenuList);
    }

    /**
     * 根据用户ID查询菜单
     */
    @Test
    public void testSelectMenuTreeByUserId() {
        String userId = "1";
    }

    /**
     * 根据角色ID查询菜单树信息
     */
    @Test
    public void testSelectMenuListByRoleId() {
        String roleId = "1";
        String menuCheckStrictly = UserConstants.YSE_STATUS;
        List<Long> muenIdList = sysMenuMapper.selectMenuListByRoleId(roleId, menuCheckStrictly);
        log.info("根据角色ID查询菜单树信息成功，查询结果：{}", muenIdList);
    }

    /**
     * 根据菜单ID查询信息
     */
    @Test
    public void testSelectMenuById() {
        Long menuId = 1L;
        SysMenu sysMenu = sysMenuMapper.selectMenuById(menuId,"1");
        log.info("根据菜单ID查询信息成功，查询结果：{}", sysMenu);
    }

    /**
     * 是否存在菜单子节点
     */
    @Test
    public void testHasChildByMenuId() {
        Long menuId = 1L;
        int i = sysMenuMapper.hasChildByMenuId(menuId);
        log.info("是否存在菜单子节点查询成功，查询结果：{}", i);
    }

    /**
     * 新增菜单信息
     */
    @Test
    public void testInsertMenu() {
        SysMenu sysMenu = new SysMenu();
        int i = sysMenuMapper.insertMenu(sysMenu);
        log.info("新增菜单信息成功，新增条数：{}", i);
    }

    /**
     * 修改菜单信息
     */
    @Test
    public void testUpdateMenu() {
        SysMenu sysMenu = new SysMenu();

        sysMenu.setMenuId(1060L);
        sysMenu.setMenuName("test_test");

        int i = sysMenuMapper.updateMenu(sysMenu);
        log.info("修改菜单信息成功，修改条数：{}", i);
    }

    /**
     * 删除菜单管理信息
     */
    @Test
    public void testDeleteMenuById() {
        Long menuId = 1060L;
        int i = sysMenuMapper.deleteMenuById(menuId);
        log.info("删除菜单管理信息成功，删除条数：{}", i);
    }

    /**
     * 校验菜单名称是否唯一
     */
    @Test
    public void testCheckMenuNameUnique() {
        String menuName = "日志导出";
        Long parentId = 500L;
        SysMenu sysMenu = sysMenuMapper.checkMenuNameUnique(menuName, parentId);
        log.info("校验菜单名称是否唯一成功，校验结果：{}", sysMenu);
    }


}
