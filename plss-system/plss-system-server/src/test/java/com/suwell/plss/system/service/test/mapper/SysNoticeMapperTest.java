package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.entity.SysNotice;
import com.suwell.plss.system.mapper.SysNoticeMapper;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * Mapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysNoticeMapperTest {

    @Resource
    SysNoticeMapper sysNoticeMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 查询公告信息
     */
    @Test
    public void testSelectNoticeById() {
        Long noticeId = 1L;
        SysNotice sysNotice = sysNoticeMapper.selectNoticeById(noticeId);
        log.info("查询公告信息成功，查询结果：{}", sysNotice);
    }

    /**
     * 查询公告列表
     */
    @Test
    public void testSelectNoticeList() {
        SysNotice sysNotice = new SysNotice();
        sysNotice.setNoticeId(1L);
        List<SysNotice> sysNoticeList = sysNoticeMapper.selectNoticeList(sysNotice);
        log.info("查询公告列表成功，查询结果：{}", sysNoticeList);
    }

    /**
     * 新增公告
     */
    @Test
    public void testInsertNotice() {
        SysNotice sysNotice = new SysNotice();

        sysNotice.setNoticeTitle("test");
        sysNotice.setNoticeType("1");
        sysNotice.setNoticeContent("test");
        sysNotice.setStatus("0");
        sysNotice.setRemark("test");
        sysNotice.setCreateBy("test");

        int i = sysNoticeMapper.insertNotice(sysNotice);
        log.info("新增公告成功,新增条数：{}", i);
    }

    /**
     * 修改公告
     */
    @Test
    public void testUpdateNotice() {
        SysNotice sysNotice = new SysNotice();

        sysNotice.setNoticeId(6334277304965L);
        sysNotice.setNoticeTitle("test_test");

        int i = sysNoticeMapper.updateNotice(sysNotice);
        log.info("修改公告成功，修改条数：{}", i);
    }

    /**
     * 批量删除公告
     */
    @Test
    public void testDeleteNoticeById() {
        Long noticeId = 6334277304965L;
        int i = sysNoticeMapper.deleteNoticeById(noticeId);
        log.info("批量删除公告成功，删除条数：{}", i);
    }

    /**
     * 批量删除公告信息
     */
    @Test
    public void testDeleteNoticeByIds() {
        Long[] noticeIdList = new Long[]{6334292236805L, 6334295711109L};
        int i = sysNoticeMapper.deleteNoticeByIds(noticeIdList);
        log.info("批量删除公告信息成功，删除条数：{}", i);
    }


}
