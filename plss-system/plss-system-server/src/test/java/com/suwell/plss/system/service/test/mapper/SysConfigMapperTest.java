package com.suwell.plss.system.service.test.mapper;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.system.PlssSystemApplication;
import com.suwell.plss.system.api.entity.SysConfig;
import com.suwell.plss.system.mapper.SysConfigMapper;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * SysConfigMapper测试模块
 *
 * <AUTHOR>
 * @date 2023-07-21 14:14
 */
@SpringBootTest(classes = PlssSystemApplication.class)
@WebAppConfiguration
@Slf4j
public class SysConfigMapperTest {

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private WFGIdGenerator wfgIdGenerator;


    /**
     * 查询参数配置信息
     */
    @Test
    public void testSelectConfig() {
        SysConfig sysConfig = new SysConfig();

        sysConfig.setConfigId(1L);
        sysConfig.setConfigKey("sys.index.skinName");

        SysConfig config = sysConfigMapper.selectConfig(sysConfig);
        log.info(config.toString());
    }

    /**
     * 通过ID查询配置
     */
    @Test
    public void testSelectConfigById() {
        SysConfig sysConfig = sysConfigMapper.selectConfigById(1L);
        log.info("查询出来的键值：{}", sysConfig);
    }

    /**
     * 查询参数配置列表
     */
    @Test
    public void testSelectConfigList() {
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigName("皮肤样式名称");
        sysConfig.setConfigType("Y");
        sysConfig.setConfigKey("sys");
        HashMap<String, Object> params = new HashMap<>();
        params.put("beginTime", LocalDate.now().minusMonths(3).toString());
        params.put("endTime", LocalDate.now().toString());
        List<SysConfig> sysConfigList = sysConfigMapper.selectConfigList(sysConfig);
        log.info("查询配置参数列表：{}", sysConfigList);
    }

    /**
     * 根据键名查询参数配置信息
     */
    @Test
    public void testCheckConfigKeyUnique() {
        String configKey = "sys.index.skinName";
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique(configKey);
        log.info("根据键名查询出的配置信息：{}", sysConfig);
    }

    /**
     * 新增参数配置
     */
    @Test
    public void testInsertConfig() {
        SysConfig sysConfig = new SysConfig();

        sysConfig.setConfigName("测试数据");
        sysConfig.setConfigKey("test");
        sysConfig.setConfigValue("test");
        sysConfig.setConfigType("t");
        sysConfig.setCreateBy("test");
        sysConfig.setRemark("test");

        int i = sysConfigMapper.insertConfig(sysConfig);
        log.info("新增参数配置成功，插入条数：{}", i);
    }

    /**
     * 修改参数配置
     */
    @Test
    public void testUpdateConfig() {
        SysConfig sysConfig = new SysConfig();

        sysConfig.setConfigId(6333499428485L);
        sysConfig.setConfigName("test_test");

        int i = sysConfigMapper.updateConfig(sysConfig);
        log.info("修改参数配置成功，修改条数：{}", i);
    }
//

    /**
     * 删除参数配置
     */
    @Test
    public void testDeleteConfigById() {
        Long configId = 6333499428485L;
        int i = sysConfigMapper.deleteConfigById(configId);
        log.info("删除参数配置,删除条数:{}", i);
    }

    /**
     * 批量删除参数信息
     */
    @Test
    public void testDeleteConfigByIds() {
        Long[] configIdList = new Long[]{6333517420805L, 6333520269061L};
        int i = sysConfigMapper.deleteConfigByIds(configIdList);
        log.info("批量删除参数信息成功，删除条数：{}", i);
    }

}
