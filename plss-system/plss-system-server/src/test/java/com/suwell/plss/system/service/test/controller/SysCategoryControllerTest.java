//package com.suwell.plss.system.service.test.controller;
//
//import com.suwell.plss.framework.common.domain.R;
//import com.suwell.plss.system.PlssSystemApplication;
//import com.suwell.plss.system.controller.SysCategoryController;
//import com.suwell.plss.system.dto.request.CategorySaveReq;
//import com.suwell.plss.system.dto.request.CategoryTreeReq;
//import com.suwell.plss.system.dto.request.CategoryUpdateReq;
//import com.suwell.plss.system.dto.response.CategoryImportResp;
//import com.suwell.plss.system.dto.response.CategoryResp;
//import com.suwell.plss.system.dto.response.CategoryTreeResp;
//import com.suwell.plss.system.entity.SysCategory;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.Map;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.web.WebAppConfiguration;
//import org.springframework.web.multipart.MultipartFile;
//
//@SpringBootTest(classes = PlssSystemApplication.class)
//@WebAppConfiguration
//@Slf4j
//public class SysCategoryControllerTest {
//
//    @Resource
//    SysCategoryController sysCategoryController;
//
//    @Test
//    public void testGetWithPathById() {
//        Long id = 194060069125L;
//        R r = sysCategoryController.queryById(id);
//        System.out.println(r.getData().toString());
//    }
//
//    @Test
//    public void testFindChildrenById() {
//        Long id = 194994631941L;
//        R r = sysCategoryController.queryChildrenById(id);
//        System.out.println(r.getData());
//    }
//
//    @Test
//    public void testPageList() {
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("page", 1);
//        params.put("pageSize", 2);
//
//        SysCategory category = new SysCategory();
//        category.setParams(params);
//
//        R r = sysCategoryController.pageList(category);
//
//        System.out.println(r.getData().toString());
//    }
//
//
//    @Test
//    public void testSave() {
//        CategorySaveReq saveCategoryDTO = new CategorySaveReq();
//        saveCategoryDTO.setParentId(194994631941L);
//        saveCategoryDTO.setName("数科网维——武汉分公司——武汉研发一部");
//        saveCategoryDTO.setCtype(1);
//        saveCategoryDTO.setVisitType(1);
//        R<Long> r = sysCategoryController.save(saveCategoryDTO);
//        System.out.println(r.getData().toString());
//    }
//
//    @Test
//    public void testUpdate() {
//        CategoryUpdateReq updateCategoryDTO = new CategoryUpdateReq();
//        updateCategoryDTO.setId(195026471173L);
//        updateCategoryDTO.setNewName("武汉研发中心");
//        R<CategoryResp> r = sysCategoryController.update(updateCategoryDTO);
//        System.out.println(r);
//    }
//
//    @Test
//    public void testDelete() {
//        Long id = 194994631941L;
//        R<Integer> r = sysCategoryController.delete(id);
//        System.out.println(r);
//    }
//
//    @Test
//    public void testTree() {
//        CategoryTreeReq categoryTreeReq = new CategoryTreeReq();
//        categoryTreeReq.setId(194962089477L);
//        R<CategoryTreeResp> r = sysCategoryController.tree(categoryTreeReq);
//        System.out.println(r.getData());
//    }
//
//    @Test
//    public void testImportExcel() throws FileNotFoundException {
//        File file = new File("C:\\Users\\<USER>\\Desktop\\分类导入模板1691840137770.xlsx");
//        MultipartFile multipartFile = null;
//        try (FileInputStream input = new FileInputStream(file)) {
//            multipartFile = new MockMultipartFile(file.getName().concat("temp"), file.getName(),
//                    "text/plain",
//                    input);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        String sessionId = "test";
//        R<CategoryImportResp> result = sysCategoryController.importExcel(multipartFile,
//                sessionId);
//        System.out.println(result.getData());
//
//    }
//
//    @Test
//    public void test() {
//
//    }
//
//}
