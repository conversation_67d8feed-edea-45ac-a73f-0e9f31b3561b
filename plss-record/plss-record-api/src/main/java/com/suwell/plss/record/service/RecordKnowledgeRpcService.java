package com.suwell.plss.record.service;

import com.suwell.plss.framework.common.constant.ServiceNameConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.fallback.RecordKnowledgeRpcServiceFallbackFactory;
import com.suwell.plss.record.model.KnowledgeMergeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeModelAiExtractReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeReq;
import com.suwell.plss.search.standard.dto.response.KnowledgeInfoSourceResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@FeignClient(contextId = "recordKnowledgeRpcService", value = ServiceNameConstants.RECORD_SERVICE,
        path = RecordKnowledgeRpcService.INNER_PREFIX, fallbackFactory = RecordKnowledgeRpcServiceFallbackFactory.class)
public interface RecordKnowledgeRpcService {

    String INNER_PREFIX = "/inner/record/knowledge";

    /**
     * 为ai提供模型配置参数
     * 该方法会返回ai需要的模型配置参数 并保存模型配置参数到数据库
     * @return 模型配置参数
     */
    @GetMapping("/modelSetting4Ai")
    R<String> modelSetting4Ai(@RequestBody RecordKnowledgeModelAiExtractReq aiExtractReq);

    /**
     * 保存文档知识
     * @param recordKnowledgeReq 知识提取内容
     */
    @PostMapping("/saveRecordKnowledge")
    R<Void> saveRecordKnowledge(@RequestBody RecordKnowledgeReq recordKnowledgeReq);

    /**
     * 查询知识来源文档信息
     * @param recordIds
     * @return
     */
    @PostMapping("/queryKnowledgeSourceRecord")
    R<List<KnowledgeInfoSourceResp>> queryKnowledgeSourceRecord(@RequestBody List<Long> recordIds);

    /**
     * 获取概念属性顺序
     * @param conceptNames
     * @return
     */
    @PostMapping("/queryConceptAttrOrder")
    R<Map<String,Integer>> queryConceptAttrOrder(@RequestBody List<String> conceptNames);

    @PostMapping("/mergeUpdateDb")
    R<Void> mergeUpdate(KnowledgeMergeReq req);
}
