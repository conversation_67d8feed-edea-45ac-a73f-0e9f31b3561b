package com.suwell.plss.record.standard.dto.request;

import java.io.Serializable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class SaveThumbnailReq implements Serializable {

    private static final long serialVersionUID = 5314258696117468934L;

    /**
     * 文档id
     */
    @NotNull(message = "文档id必填")
    private Long recordId;

    /**
     * 缩略图id---封面
     */
    @NotNull(message = "缩略图id必填")
    private Long thumbnailId;

}
