package com.suwell.plss.record.standard.dto.response;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/6
 */
@Data
@NoArgsConstructor
public class RecordResp implements Serializable {

    private static final long serialVersionUID = 3878284162353369100L;
    /**
     * 文件id
     */
    private Long id;
    /**
     * 文件类型id
     */
    private Long recordtypeId;
    /**
     * 文件名称
     */
    private String name;
    /**
     * 标题
     */
    private String title;
    /**
     * 摘要
     */
    private String digest;

    /**
     * 高亮摘要
     */
    private String highlightDigest;

    /**
     * 发文日期
     */
    private String dateOfPosting;
    /**
     * 状态(1:启用, 2:禁用)
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改时间
     */
    private Date modifiedTime;
    /**
     * 修改人
     */
    private String modifiedBy;
    /**
     * 来源:接入方标识
     */
    private Integer origin;

    /**
     * 文件密级
     */
    private Integer classified;
}
