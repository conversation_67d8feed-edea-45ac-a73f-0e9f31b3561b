package com.suwell.plss.record.standard.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ==========================
 * 开发：maple
 * 创建时间：2023/11/22
 * 版本：1.0
 * 描述：范文/模板文件
 * ==========================
 */
@Data
public class SampleTemplateFileResp implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 目录id
     */
    private Long catalogueId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件id（minIo）
     */
    private Long originalFileId;
    /**
     * 文件的存储桶
     */
    private String fileBucketName;

    /**
     * 文件的存储名
     */
    private String filePath;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 封面文件id（minIo）
     */
    private Long coverFileId;
    /**
     * 封面文件存储桶
     */
    private String coverFileBucketName;

    /**
     * 封面文件的路径
     */
    private String coverFilePath;

    /**
     * ofd文件id（minIo）
     */
    private Long ofdFileId;
    /**
     * ofd名称
     */
    private String ofdFileBucketName;

    /**
     * OFD文件的存储路径
     */
    private String ofdFilePath;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
