package com.suwell.plss.record.standard.dto.response;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/16
 */
@Data
public class RecordUploadExceptionResp implements Serializable {

    private static final long serialVersionUID = 7399063830700009370L;

    /**
     * 上传时唯一标识
     */
    private String uniqId;

    /**
     * 元数据异常消息
     */
    private List<RecordUploadMetadataExceptionResp> metadataExceptionRespList;


}
