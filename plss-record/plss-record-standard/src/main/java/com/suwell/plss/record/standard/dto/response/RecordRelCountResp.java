package com.suwell.plss.record.standard.dto.response;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件关系数量
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordRelCountResp implements Serializable {

    private static final long serialVersionUID = -5792500291469407975L;

    /**
     * 文件id
     */
    private Long recordId;

    /**
     * 关系数量
     */
    private Long relCount;


}
