package com.suwell.plss.record.standard.dto.response;

import com.suwell.plss.record.standard.enums.MaterialChangeEnum;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/26
 */
@Data
public class MaterialCategoryInfoResp implements Serializable {

    private static final long serialVersionUID = 5737249843384631190L;

    /**
     * 素材id
     */
    private Long materialId;
    /**
     * 素材名称
     * 目前是默认取文档名称
     */
    private String name;

    /**
     * 素材类型
     * 1 通用素材；
     * 2 项目素材；
     * 3 个人素材；
     */
    private Integer materialType;

    /**
     * 素材内容
     */
    private String content;


    /**
     * 来源类型1 直接新增；2 文件划词
     */
    private Integer fromType;

    /**
     * 素材来源业务id
     * from_type等于2时对应文件id
     */
    private Long relationId;

    /**
     * 素材来源业务名称
     * from_type等于2时对应文件名称
     */
    private String relationName;

    /**
     * 来源库id
     */
    private Long repId;

    /**
     * 来源库名称
     */
    private String repName;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifiedTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createUser;

    /**
     * 分类
     */
    private List<MaterialCategoryResp> materialCategoryRespList;

    /**
     * 最新的变更原因
     */
    private String reason;
    /**
     * 变更原因类型
     * 1-用户需求变更
     * 2-设计错误
     * 3-其他
     * @see MaterialChangeEnum.ReasonTypeEnum
     */
    private Integer reasonType;

}
