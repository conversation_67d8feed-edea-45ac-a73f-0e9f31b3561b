package com.suwell.plss.record.standard.service;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.ContainsCtypeReq;
import com.suwell.plss.record.standard.dto.request.ConvertRepoToFolderReq;
import com.suwell.plss.record.standard.dto.request.QueryOrAutoNewRepoReq;
import com.suwell.plss.record.standard.dto.request.RepoCollectReq;
import com.suwell.plss.record.standard.dto.request.RepoMaterialResp;
import com.suwell.plss.record.standard.dto.request.RepoTagQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMaterialViewReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryModifyReq;
import com.suwell.plss.record.standard.dto.request.RepositoryQueryReq;
import com.suwell.plss.record.standard.dto.request.ResourceSearchReq;
import com.suwell.plss.record.standard.dto.response.*;

import com.suwell.plss.record.standard.dto.response.RepoCtypeGroupResp;
import com.suwell.plss.record.standard.dto.response.RepoInfoResp;
import com.suwell.plss.record.standard.dto.response.RepoMetadataResp;
import com.suwell.plss.record.standard.dto.response.RepoTagResp;
import com.suwell.plss.record.standard.dto.response.RepositoryCountResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.dto.response.ResourceSearchResp;
import com.suwell.plss.record.standard.enums.RepositoryEnum.SortType;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 库相关接口
 */
public interface StandardRepositoryFacade {

    /**
     * 保存库实体
     *
     * @param repository 库实体
     */
    Long addRepository(RepositoryAddReq repository);

    /**
     * 删除库实体
     *
     * @param repositoryId 库id
     */
    void removeRepository(Long repositoryId);

    /**
     * 修改库实体
     *
     * @param repository 库实体, id不能为空
     */
    void modifyRepository(RepositoryModifyReq repository);

    /**
     * 根据id获取仓库信息
     *
     * @param repoId 库id
     * @return 库信息
     */
    RepositoryResp queryById(Long repoId);

    /**
     * 查询库列表
     *
     * @param params 查询条件
     * @return 查询结果
     */
    PageUtils<RepositoryResp> queryPage(RepositoryQueryReq params);

    /**
     * 查询素材库列表
     *
     * @param params 查询条件
     * @return 查询结果
     */
    PageUtils<RepositoryResp> queryMaterialRepoPage(RepositoryQueryReq params);

    /**
     * 查询库列表
     *
     * @param params 查询条件
     * @return 查询结果
     */
    List<RepositoryResp> queryList(RepositoryQueryReq params);

    /**
     * 可视化文库列表
     * @return
     */
    List<RepositoryResp> visualPageList();

    /**
     * 根据查询条件分组库
     */
    List<RepoCtypeGroupResp> groupCtype(RepositoryQueryReq req);

    /**
     * 给库添加元数据
     */
    void saveMetadata(RepositoryMetadataAddReq req);

    /**
     * 查询库的元数据列表
     *
     * @param req 查询条件
     * @return 元数据列表
     */
    RepoMetadataResp queryMetadata(RepositoryMetadataQueryReq req);

    /**
     * 查询个人库id
     *
     * @param req 可以为空
     * @return 库id
     */
    Long queryPersonalRepoId(QueryOrAutoNewRepoReq req);

    /**
     * 将库转为目录并放至指定目录下
     */
    void convertRepositoryToFolder(ConvertRepoToFolderReq req);

    /**
     * 列取库的元数据列表
     */
    List<RepoTagResp> listRepoTags(RepoTagQueryReq req);

    List<RepoTagResp> listRepoRecordTags(RepoTagQueryReq req);

    List<RepoInfoResp> listRepoInfo(Long recordId);

    RepositoryCountResp statisticRepoCount(Long tenantId);

    /**
     * 查询内置库id列表
     *
     * @return 内置库id列表
     */
    R<List<Long>> queryInnerRepoList();

    /**
     * 查询私有库列表
     *
     * @return 私有库列表
     */
    R<List<RepoInfoResp>> queryPrivateRepoList(String userId);

    /**
     * 根据库名称查询库信息
     *
     * @param repoNameList 库名称集合
     * @date 2024/4/11 14:19
     */
    List<RepoInfoResp> listRepoBasicInfoByNames(List<String> repoNameList);

    /**
     * 查询指定的库或目录是否在指定的库类型下
     */
    boolean containsByRepoCtype(ContainsCtypeReq req);

    /**
     * 查询用户是所有者的库的数量
     */
    Long countRepoByOwnerId(String userId);

    /**
     * 根据库id查询库列表
     *
     * @param ids 库id列表
     */
    List<RepositoryResp> queryByIds(List<Long> ids);

    void fixMyFolderForRepeat();

    List<RepositoryResp> listSimpleRepoInfo(RepositoryQueryReq params);

    /**
     * 多文库目录树基于名称的检索
     */
    List<ResourceSearchResp> resourceSearch(ResourceSearchReq req);

    /**
     * 文库收藏
     */
    void repoCollect(RepoCollectReq req);

    /**
     * 根据组织机构id查询库数量
     * @param orgIds 组织机构id
     * @return 库数量
     */
    Long countRepoByOrgId(List<String> orgIds);

    /**
     * 文库组织机构信息预填充
     */
    void repoOrgPreFill(HttpServletResponse response);

    /**
     * 导入文库组织机构信息
     */
    void importRepoOrgBatch(MultipartFile file);

    List<RepositoryResp> listByIdsWithSort(List<Long> repoIds, SortType sortType);

    List<MetadataResp> listRepoRecordMd(Long repoId);

    /**
     * 查询展示的单位素材库
     * @param repoName
     * @return
     */
    List<RepoMaterialResp> listRepoMaterials(String repoName, Integer materialView);

    /**
     * 修改展示素材库
     * @param req
     */
    void modifyMaterialViewRepository(RepositoryMaterialViewReq req);
}
