package com.suwell.plss.record.standard.dto.request;

import java.io.Serializable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/2/18
 * @content
 */
@Data
public class DocProcessCountReq implements Serializable {

    /**
     * orgType 1:单位 2:部门
     */
    @NotNull(message = "orgType必填")
    private String orgType;
    /**
     * dateType 1:本周 2:本月 3:本年
     */
    @NotNull(message = "dateType必填")
    private String dateType;

    /**
     * 租户id
     * 这里指定后，优先使用这里的租户id
     */
    private String tenantId;
}
