package com.suwell.plss.record.standard.service;

import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.domain.DataRepoInfo;
import com.suwell.plss.record.standard.domain.DataSubscribeInfo;
import com.suwell.plss.record.standard.dto.request.DataLakePageReq;
import com.suwell.plss.record.standard.dto.request.DataLakeSubscribeReq;
import com.suwell.plss.record.standard.dto.response.DataLakeResp;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DataSubscribeFacade {

    DataSubscribeInfo info();

    void updateInfo(DataSubscribeInfo info);

    void checkSubscribeInfo(String subscribeAddress);

    List<DataRepoInfo> listAvailableRepos(String subscribeAddress, String accessKey);

    /**
     * 订阅指定日期的数据
     */
    void subscribeDiffData(DataLakeSubscribeReq req);

    PageUtils<DataLakeResp> queryPage(DataLakePageReq req);

    void syncRemoteDataLake(int pageSize);
}
