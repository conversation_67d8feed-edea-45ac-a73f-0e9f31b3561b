package com.suwell.plss.record.standard.dto.request;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
public class FileInfoBaseReq implements Serializable {

    private static final long serialVersionUID = 7834882563754292445L;
    /**
     * 主文名称待后缀扩展名称
     */
    private String fileName;
    
    /**
     * 文件对象
     */
    private MultipartFile file;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * @see com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum
     */
    @NotNull(message = "文件的类型不能为空")
    private Integer ctype;
}
