package com.suwell.plss.record.standard.dto.response;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/17
 */
@Data
public class RecordTypeOpenResp implements Serializable {

    private static final long serialVersionUID = 4687425525114457033L;

    /**
     * 文档类型id
     */
    private Long recordTypeId;
    /**
     * 文档类型名称
     */
    private String recordTypeName;
}
