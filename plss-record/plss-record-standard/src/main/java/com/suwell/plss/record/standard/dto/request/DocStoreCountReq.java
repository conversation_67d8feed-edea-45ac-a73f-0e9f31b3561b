package com.suwell.plss.record.standard.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/3/5
 * @content
 */
@Data
public class DocStoreCountReq implements Serializable{

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 开始时间
     */
    @NotEmpty(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotEmpty(message = "结束时间不能为空")
    private String endTime;

}
