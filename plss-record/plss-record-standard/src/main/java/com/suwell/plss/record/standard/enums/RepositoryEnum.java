package com.suwell.plss.record.standard.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface RepositoryEnum {

    @Getter
    @AllArgsConstructor
    enum CType {

        PRIVATE(1, "内部库"),
        COMMON(2, "公共库"),
        PERSONAL(3, "我的文库"),
        TEMPLATE(4, "模版库"),
        SAMPLE_TEXT(5, "范文样例库"),
        SECURITY(6, "安全文库"),
        MATERIAL(7, "素材库"),
        ;

        private final Integer code;
        private final String desc;

    }
    @Getter
    @AllArgsConstructor
    enum Status {

        NORMAL(1, "启用"),
        FORBIDDEN(2, "禁用"),
        ;

        private final Integer code;
        private final String desc;

    }
    @Getter
    @AllArgsConstructor
    enum ShareType {

        ANY_SHARE(1, "无条件共享"),
        CONDITION_SHARE(2, "有条件共享"),
        NON_SHARE(3, "不共享"),
        ;

        private final Integer code;
        private final String desc;

    }

    @Getter
    @AllArgsConstructor
    enum MetaDataType {
        DOC_TYPE(1, "文档类型"),
        RETRIEVE_TYPE(2, "检索类型"),
        TAG(3, "标签"),
        ;

        private final Integer code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    enum RepoQueryType {
        ALL(1, "全部"),
        MANAGEMENT(2, "我管理的"),
        PARTICIPATE(3, "我参与的"),
        ORGANIZATION(4, "当前用户所在单位的"),
        ;

        private final Integer code;
        private final String desc;

        public static RepoQueryType findByCode(int code) {
            for (RepoQueryType value : RepoQueryType.values()) {
                if (value.code == code) {
                    return value;
                }
            }
            return null;
        }
    }


    @Getter
    @AllArgsConstructor
    enum RetrieveType {
        METADATA(1, "元数据"),
        CATEGORY(2, "分类标签"),
        ORGANIZATION(3, "组织架构"),
        FOLDER(4, "目录"),
        ;

        private final Integer code;
        private final String desc;

        public static boolean checkCode(Integer code) {
            for (RetrieveType value : RetrieveType.values()) {
                if (Objects.equals(value.code, code)) {
                    return true;
                }
            }
            return false;
        }

        public static RetrieveType findByCode(Integer code) {
            for (RetrieveType value : RetrieveType.values()) {
                if (Objects.equals(value.code, code)) {
                    return value;
                }
            }
            return METADATA;
        }
    }

    @Getter
    @AllArgsConstructor
    enum SortType {
        COLLECT(1, "置顶排序优先，内置库靠后"),
        NAME_ASC(2, "文库名称汉字正排"),
        ;

        private final Integer code;
        private final String desc;

    }

}
