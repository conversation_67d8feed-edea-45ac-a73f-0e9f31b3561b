package com.suwell.plss.record.standard.dto.response;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 文件所在的目录路径
 */
@Data
public class BatchRecordRelFolderResp implements Serializable {

    private static final long serialVersionUID = -8789696383013863002L;

    /**
     * 文件id
     */
    private Long recordId;

    /**
     * 路径列表
     */
    private List<RecordRelFolderResp> relList;


}
