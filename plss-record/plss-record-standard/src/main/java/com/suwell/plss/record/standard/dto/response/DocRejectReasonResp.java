package com.suwell.plss.record.standard.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2024/7/23
 * @content
 */
@Data
public class DocRejectReasonResp implements Serializable {

    private List<Map<String, List<DocRejectReasonDto>>> data;

    private Set<String> dateSet;

    public void addDto(String date, DocRejectReasonDto docRejectReasonDto) {
        if(this.data == null){
            this.data = new ArrayList<>();
        }
        if(this.dateSet == null){
            this.dateSet = new HashSet<>();
        }
        if(this.dateSet.contains(date)){
            data.get(data.size()-1).get(date).add(docRejectReasonDto);
        }else{
            this.dateSet.add(date);
            Map<String, List<DocRejectReasonDto>> map = new HashMap<>();
            map.put(date, new ArrayList<>());
            map.get(date).add(docRejectReasonDto);
            this.data.add(map);
        }
    }


    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @Data
    public static class DocRejectReasonDto implements Serializable {
        private String time;
        private String nickName;
        private String userId;
        private String reason;
    }
}
