package com.suwell.plss.record.facade;

import com.google.common.collect.Lists;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.ResourcePermissionAddReq;
import com.suwell.plss.record.standard.dto.response.PermissionDicResp;
import com.suwell.plss.record.standard.service.StandardPermissionFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = PlssRecordApplication.class)
class StandardPermissionValidatorFactoryFacadeImplTest {

    @Resource
    private StandardPermissionFacade permissionFacade;

    @Test
    void listPermissionDic() {
        List<PermissionDicResp> list = permissionFacade.listPermissionDic();
        System.out.println(list);
    }

    @Test
    void removePermissionGroup() {
        permissionFacade.removePermissionGroup(Lists.newArrayList(123L));
    }

    @Test
    void addResourcePermission() {
        ResourcePermissionAddReq addReq = new ResourcePermissionAddReq();
        addReq.setResourceId(123L);
        permissionFacade.addResourcePermission(addReq);
    }

//    @Test
//    void removeResourcePermission() {
//        ResourcePermissionRemoveReq removeReq = new ResourcePermissionRemoveReq();
//        removeReq.setResourceId(1L);
//        removeReq.setVisitorId(2L);
//        permissionFacade.removeResourcePermission(removeReq);
//    }

}