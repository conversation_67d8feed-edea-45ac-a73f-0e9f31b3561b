package com.suwell.plss.record.service.impl;

import com.suwell.plss.record.PlssRecordApplication;
import jakarta.annotation.Resource;
import java.io.File;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = PlssRecordApplication.class)
public class DataDeliveryServiceTest {

    @Resource
    private DataDeliveryServiceImpl dataDeliveryService;

//    @Test
//    void testUpload() {
//        long l = System.currentTimeMillis();
//        File file = new File("D:\\tmp\\a.tmp");
//        boolean result = dataDeliveryService.uploadToDeliveryServer(file, 1L, "2025-03-12", "测试数据包", "a.tmp");
//        System.out.println(result);
//        System.out.println(System.currentTimeMillis() - l);
//    }

}
