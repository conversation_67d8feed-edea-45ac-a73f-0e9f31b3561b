package com.suwell.plss.record.facade;

import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.conf.RecordCardConfig;
import com.suwell.plss.record.conf.RecordCardProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;

/***
 *测试类
 *<AUTHOR>
 *@date 2024/1/29 20:39
 *@version 1.0.0
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardRecordInfoFacadeTest {

    /**
     * RecordCardProperties cardConfig = RecordCardConfig.getCardConfig(recordCardConfig.getCardConfigList(),
     *                 recordtypeId, recordCardConfig.getDefaultCardConfigList());
     *
     */

    @Resource
    private RecordCardConfig recordCardConfig;

    @Test
    public void getDefaultCardTest(){
        Long recordtypeId = 784997370885L;
        RecordCardProperties cardConfig = RecordCardConfig.getCardConfig(recordCardConfig.getCardConfigList(), recordtypeId, recordCardConfig.getDefaultCardConfig());
        System.out.println(cardConfig);
    }
}
