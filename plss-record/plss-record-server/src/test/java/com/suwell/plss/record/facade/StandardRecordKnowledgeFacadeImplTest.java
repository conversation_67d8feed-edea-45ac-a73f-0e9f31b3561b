package com.suwell.plss.record.facade;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeAiExtractItem;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgePredicate;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeSubject;
import com.suwell.plss.search.standard.dto.graph.RecordKnowledgeGraph;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
class StandardRecordKnowledgeFacadeImplTest {

    @Resource
    private StandardRecordKnowledgeFacadeImpl standardRecordKnowledgeFacade;

    @Test
    void saveRecordKnowledge() {
        RecordKnowledgeReq recordKnowledgeReq = new RecordKnowledgeReq();
        recordKnowledgeReq.setRecordId(1L);
        recordKnowledgeReq.setTitle("测试标题");
        List<RecordKnowledgeAiExtractItem> knowledgeItemList = new ArrayList<>();
        recordKnowledgeReq.setKnowledgeItemList(knowledgeItemList);
        RecordKnowledgeAiExtractItem recordKnowledgeAiExtractItem = new RecordKnowledgeAiExtractItem();
        knowledgeItemList.add(recordKnowledgeAiExtractItem);
        RecordKnowledgeSubject subject = new RecordKnowledgeSubject();
        subject.setType("人员");
        subject.setValue("张三");
        recordKnowledgeAiExtractItem.setSubject(subject);
        List<RecordKnowledgePredicate> predicates = new ArrayList<>();
        recordKnowledgeAiExtractItem.setPredicates(predicates);
        RecordKnowledgePredicate predicate = new RecordKnowledgePredicate();
        predicates.add(predicate);
        predicate.setPredicate("任职");
        predicate.setObject("总经理");
        RecordKnowledgePredicate predicate2 = new RecordKnowledgePredicate();
        predicates.add(predicate);
        predicate2.setPredicate("年龄");
        predicate2.setObject("22");
        standardRecordKnowledgeFacade.saveRecordKnowledge(recordKnowledgeReq);
    }


    void resolveAiKnowledge2Graph(){
        RecordKnowledgeReq recordKnowledgeReq = new RecordKnowledgeReq();
        recordKnowledgeReq.setRecordId(1L);
        recordKnowledgeReq.setTitle("测试title");
        List<RecordKnowledgeAiExtractItem> knowledgeItemList = new ArrayList<>();
        RecordKnowledgeAiExtractItem recordKnowledgeAiExtractItem = new RecordKnowledgeAiExtractItem();
        RecordKnowledgeSubject recordKnowledgeSubject = new RecordKnowledgeSubject();
        recordKnowledgeSubject.setType("人员");
        recordKnowledgeSubject.setValue("张三");
        List<RecordKnowledgePredicate> predicates = new ArrayList<>();
        RecordKnowledgePredicate recordKnowledgePredicate = new RecordKnowledgePredicate();
        recordKnowledgePredicate.setPredicate("任职");
        recordKnowledgePredicate.setObject("总经理");
        predicates.add(recordKnowledgePredicate);
        recordKnowledgeAiExtractItem.setSubject(recordKnowledgeSubject);
        recordKnowledgeAiExtractItem.setPredicates(predicates);
        knowledgeItemList.add(recordKnowledgeAiExtractItem);
        recordKnowledgeReq.setKnowledgeItemList(knowledgeItemList);
        String modelSetting = "{\"id\":228926328365253,\"name\":\"test1\",\"subjectConceptList\":[{\"conceptName\":\"人员\",\"relationList\":[{\"relationName\":\"被授予奖项\",\"toConcept\":{\"conceptName\":\"表彰奖项\"}},{\"relationName\":\"办理公文\",\"toConcept\":{\"conceptName\":\"公文\"}},{\"relationName\":\"任职\",\"toConcept\":{\"conceptName\":\"职务\"}},{\"relationName\":\"发起请示\",\"toConcept\":{\"conceptName\":\"事项\"}},{\"relationName\":\"处理事项\",\"toConcept\":{\"conceptName\":\"事项\"}}]},{\"conceptName\":\"会议议题\",\"relationList\":[{\"relationName\":\"提及人员\",\"toConcept\":{\"conceptName\":\"人员\"}}]},{\"conceptName\":\"会议\",\"relationList\":[{\"relationName\":\"参会人员\",\"toConcept\":{\"conceptName\":\"人员\"}},{\"relationName\":\"主持人员\",\"toConcept\":{\"conceptName\":\"人员\"}},{\"relationName\":\"列席人员\",\"toConcept\":{\"conceptName\":\"人员\"}},{\"relationName\":\"出席人员\",\"toConcept\":{\"conceptName\":\"人员\"}},{\"relationName\":\"记录人员\",\"toConcept\":{\"conceptName\":\"人员\"}}]},{\"conceptName\":\"表彰奖项\"},{\"conceptName\":\"公文\",\"relationList\":[{\"relationName\":\"提及人员\",\"toConcept\":{\"conceptName\":\"人员\"}}]},{\"conceptName\":\"职务\"},{\"conceptName\":\"事项\"}]}";
        RecordKnowledgeGraph recordKnowledgeGraph = new StandardRecordKnowledgeFacadeImpl().resolveAiKnowledge2Graph(recordKnowledgeReq, modelSetting);
        System.out.println(JSON.toJSONString(recordKnowledgeGraph));
    }
}