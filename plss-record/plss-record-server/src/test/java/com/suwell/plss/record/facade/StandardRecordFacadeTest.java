package com.suwell.plss.record.facade;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.QueryMetadataViewReq;
import com.suwell.plss.record.standard.dto.request.RecordInfoReq;
import com.suwell.plss.record.standard.dto.request.RecordRelationReq;
import com.suwell.plss.record.standard.dto.response.MetaDataValueInfoResp;
import com.suwell.plss.record.standard.dto.response.MetadataSearchResultViewResp;
import com.suwell.plss.record.standard.dto.response.RecordDocumentResp;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * 文件包 协议层 接口单测
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
@RefreshScope
public class StandardRecordFacadeTest {

    @Resource
    private StandardRecordFacade recordFacade;
    @Resource
    private RecordService recordService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private StandardRecordTypeFacade  standardRecordTypeFacade;

    @Test
    void queryMetadataViewByRecordTypesTest() {
        QueryMetadataViewReq req = new QueryMetadataViewReq();
        req.setRecordTypeIds(Lists.newArrayList(2221194907909L,199481897182725L,2221051070725L));
        MetadataSearchResultViewResp resultViewResp = standardRecordTypeFacade.queryMetadataViewByRecordTypes(
                req);
        System.out.println(JSON.toJSONString(resultViewResp));
    }
    @Test
    void queryRelationListTest() {
        RecordRelationReq recordRelationReq = new RecordRelationReq();
        recordRelationReq.setMdValue("通知");
        recordRelationReq.setMdIdList(Lists.newArrayList(1121369818629L, 1121443148293L));
//        List<RecordRelationResp> recordRelationResps = recordFacade.queryRelationList(recordRelationReq);
//        System.out.println(JSON.toJSONString(recordRelationResps));
    }

    @Test
    void queryTest() {
        Map<String, String> metadata2 = new HashMap<>();
        Record recordInfo = null;//recordService.getById(1395777812741L);
        List<MetaDataValueInfoResp> dataValueInfoRespList = recordMetadataValueService.queryMetaDataValueInfoList(
                recordInfo.getId(), recordInfo.getRecordtypeId());
        for (MetaDataValueInfoResp metaDataValueInfoResp : dataValueInfoRespList) {
            metadata2.put(metaDataValueInfoResp.getMdName(), metaDataValueInfoResp.getMdValue());
        }
        log.info("上传文件携带元数据 metadata2:{}", JSON.toJSONString(metadata2));
    }
//
//    /**
//     * 对象存储上传
//     */
//    @Test
//    public void uploadTest() {
//        try {
//            File file = new File("D:\\test\\images\\表设计plss.png");
////            String path = "https://i2.chinanews.com.cn/simg/hd/2023/08/10/bfc06cd188b944f4a6feec117aa32e6d.jpg";
////            MultipartFile multipartFile = FileUploadUtils.createMultipartFile(path, "哈哈.jpg");
//            FileInputStream input = new FileInputStream(file);
//            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                    ContentType.APPLICATION_OCTET_STREAM.toString(), IOUtils.toByteArray(input));
//            System.out.println(multipartFile);
//            //System.out.println(ossService.addUpload(null, multipartFile));
//            String relativePath = FileUploadUtils.extractFilename(multipartFile);
//            OssRespDTO ossRespDTO = fileService.upload(multipartFile.getBytes(), relativePath,
//                    multipartFile.getContentType(), ORIGIN_BUCKET.getOssBucketName());
//
//            System.out.println(JSON.toJSONString(ossRespDTO));
//        } catch (Exception e) {
//            log.error("fail uploadFile", e);
//            throw new RuntimeException(e);
//        }
//
//    }


    /**
     * 测试 文件入库
     */
    @Test
    public void addUrlUploadTest() {

        String webUrl = "http://*************:9000/cma/2023/09/11/cmafile101dba93-28f4-4c05-8351-9f3e1d2e33fa_20230911102936A014.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230918%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230918T074520Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=01956b9bf4c8b3de08292f6f7ef96cb23f118072a5823f439e60a3a1dc4c4f96";

//        String json = "{\"fileInfoReqList\":[{\"attachmentReqList\":[{\"fileName\":\"test.doc\",\"webUrl\":\"http://*************:9000/cma/2023/09/26/cmafilec6c465c0-5bc8-4624-8b49-811699f2b3eb_20230926124637A002.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230926%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230926T044637Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=db028404fabe20f9bc9a58df902a51787e7809e4d692413bbedfae33a9ad00c5\"},{\"fileName\":\"test2.doc\",\"webUrl\":\"http://*************:9000/cma/2023/09/26/cmafileea91e743-f0e9-47cf-92e4-b541343e13c8_20230926124637A003.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230926%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230926T044637Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=c2de30b9703389ba825c1308d4d4ce7e2dc018153a23b4978bde092da0ba0063\"}],\"fileName\":\"test4正文.doc\",\"metadata\":{\"库目录标识\":\"1231448078085\",\"单位层级标识\":\"1\",\"年份\":\"2022年\",\"租户ID\":\"1\",\"历史制度文号\":\"\",\"单位名称标识\":\"气象局\",\"文件名称\":\"test4正文.doc\",\"发布日期\":\"2022-10-15\",\"成文日期\":\"2022-10-16\",\"印发日期\":\"2022-10-15\",\"发文字号\":\"中国第1号\",\"效用状态\":\"有效\",\"标题\":\"测试4444\",\"发文机关标志\":\"办公室\"},\"uniqId\":\"asdzxc3\",\"webUrl\":\"http://*************:9000/cma/2023/09/26/cmafile28395035-506b-45bd-8d0a-659aa0f3473c_20230926124636A001.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230926%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230926T044636Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=7b864f836e4b839d43eb2318ffaf5026186d5a89f54d716c2de527b4091f3e97\"}],\"origin\":1,\"recordTypeId\":452996749317,\"userId\":0}";

        String json = "{\"fileInfoReqList\":[{\"attachmentReqList\":[],\"fileName\":\"中国气象局关于印发《新时代气象高层次科技创新人才计划实施办法》的通知.docx\",\"metadata\":{\"年份\":\"2020年\",\"机构分类\":\"人事司\",\"主题分类\":\"组织人事；综合管理\",\"目录\":\"1536635659013\",\"文件名称\":\"中国气象局关于印发《新时代气象高层次科技创新人才计划实施办法》的通知\",\"发布日期\":\"2020年9月2日\",\"单位名称标识\":\"中国气象局\",\"成文日期\":\"2020年9月2日\",\"机构层级\":\"局机关\",\"发文字号\":\"气人函〔2020〕182号\",\"效用状态\":\"有效\",\"标题\":\"中国气象局关于印发《新时代气象高层次科技创新人才计划实施办法》的通知\",\"发文机关标志\":\"中国气象局\"},\"uniqId\":\"1713232933893\",\"webUrl\":\"http://*************:82/cma/2023/10/17/cmafile4256554b-0aaa-4e57-a345-12c273bcd880_20231017105836A033.docx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20231017%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20231017T064623Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=19e40f50043a8f358880bef39f0d4fa75f9fb939748f227295202fd7c3ad09bb\"}],\"origin\":1,\"recordTypeId\":452996749317,\"userId\":0}";

        RecordInfoReq recordInfoReq = JSON.parseObject(json, RecordInfoReq.class);
//        List<RecordUploadResp> recordUploadRespList = recordFacade.addOnlineUpload(recordInfoReq);
//        System.out.println(JSON.toJSONString(recordUploadRespList));
    }

    @Test
    public void testInfo() {
        RecordDocumentResp resp = recordFacade.queryRecordInfoById(2050443175685L);
        System.out.println(JSON.toJSONString(resp));
        String serverEndpointPrefix = serverBasePathConfig.getSysHostPort()
                + serverBasePathConfig.getSysHostContextPath() + serverBasePathConfig.getSysApiPrefix();
        assertTrue(
                resp.getDocumentRespList().get(0).getOfdWebUrl()
                        .startsWith(serverEndpointPrefix + "/record/v1/readCallBack/info"));

    }

}
