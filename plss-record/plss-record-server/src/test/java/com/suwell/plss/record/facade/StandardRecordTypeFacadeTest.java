package com.suwell.plss.record.facade;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.RecordTypeQueryReq;
import com.suwell.plss.record.standard.dto.response.RecordTypeResp;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 文件类型 协议层 单测
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardRecordTypeFacadeTest {

    @Resource
    private StandardRecordTypeFacade recordTypeFacade;

    /**
     * 文件类型 分页 单测
     */
    @Test
    public void queryPageTest() {
        RecordTypeQueryReq req = new RecordTypeQueryReq();
        PageUtils<RecordTypeResp> recordTypeRespPageUtils = recordTypeFacade.queryPage(req);
        System.out.println(JSON.toJSONString(recordTypeRespPageUtils));
    }

    /**
     * 文件类型 列表
     */
    @Test
    public void queryListTest() {
        RecordTypeQueryReq req = new RecordTypeQueryReq();
        List<RecordTypeResp> recordTypeRespList = recordTypeFacade.queryList(req);
        System.out.println(JSON.toJSONString(recordTypeRespList));
    }

    /**
     * 文件类型id 查询元数据
     */
    @Test
    public void queryMetadataTest() {
        List<TypeMetadataRuleResp> metadataRespList = recordTypeFacade.queryTypeMetadataRule(784997370885L);
        System.out.println(JSON.toJSONString(metadataRespList));
    }

}
