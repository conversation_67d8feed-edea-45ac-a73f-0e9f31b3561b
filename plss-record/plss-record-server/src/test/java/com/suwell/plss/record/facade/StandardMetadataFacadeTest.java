package com.suwell.plss.record.facade;

import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.Metadata;
import com.suwell.plss.record.standard.dto.request.MetadataReq;
import com.suwell.plss.record.standard.service.StandardMetadataFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/8/11
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardMetadataFacadeTest {

    @Resource
    private StandardMetadataFacade standardMetadataFacade;

    /**
     * 元数据项目录入
     */
    @Test
    public void addTest() {
        MetadataReq metadataReq = new MetadataReq();
        metadataReq.setName("份号");
        metadataReq.setNameEn("SerialNumberOfCopies");
        metadataReq.setDefinition("公文印制份数的顺序号");
        //值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)
        metadataReq.setValueType(1);
        metadataReq.setValueRange("6位数字");
        metadataReq.setShortName("SN");
        metadataReq.setComment("可选项，最大出现次数为1");
        metadataReq.setSearchFlag(0);
        metadataReq.setOrderby(1);
        Metadata metadata = DozerUtils.convertToNew(metadataReq, Metadata.class);
        System.out.println(metadata);
        standardMetadataFacade.addMetadata(metadataReq);
    }

    /**
     * 批量加元数据项
     */
    @Test
    public void addBatchTest() {
        List<MetadataReq> metadataReqList = Lists.newArrayList();
        for (int i = 0; i < 19; i++) {
            MetadataReq metadataReq = new MetadataReq();
            metadataReq.setName("份号");
            metadataReq.setNameEn("SerialNumberOfCopies");
            metadataReq.setDefinition("公文印制份数的顺序号");
            //值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)
            metadataReq.setValueType(1);
            metadataReq.setValueRange("6位数字");
            metadataReq.setShortName("SN");
            metadataReq.setComment("可选项，最大出现次数为1");
            metadataReq.setSearchFlag(1);
            metadataReq.setOrderby(1);
            metadataReqList.add(metadataReq);
            Metadata metadata = DozerUtils.convertToNew(metadataReq, Metadata.class);
        }
        standardMetadataFacade.addBatchMetadata(metadataReqList);
    }
}
