package com.suwell.plss.record.facade;

import com.alibaba.fastjson.JSON;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.DocumentHistoryAddReq;
import com.suwell.plss.record.standard.dto.request.DocumentHistoryQueryReq;
import com.suwell.plss.record.standard.service.StandardDocumentHistoryFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
class DocumentHistoryFacadeImplTest {

    @Resource
    private StandardDocumentHistoryFacade documentHistoryFacade;

    @Test
    void addDocumentHistory() {
        DocumentHistoryAddReq documentHistory = new DocumentHistoryAddReq();
        documentHistory.setDocId(1L);
        documentHistory.setFileMd5("1");
        documentHistory.setFileSize(12L);
        documentHistory.setName("a");
        documentHistory.setStatus(1);
        documentHistoryFacade.addDocumentHistory(documentHistory);
    }

    @Test
    void getById() {
        System.out.println(JSON.toJSONString(documentHistoryFacade.getById(238053893637L)));
    }

    @Test
    void removeDocumentHistorys() {
        List<Long> documentHistoryIds = Lists.newArrayList();
        documentHistoryIds.add(236408789253L);
        documentHistoryIds.add(237298715141L);
        documentHistoryFacade.removeDocumentHistorys(documentHistoryIds);
    }

    @Test
    void queryDocumentHistorys() {
        DocumentHistoryQueryReq documentHistoryQueryReq = new DocumentHistoryQueryReq();
//        documentHistoryQueryReq.setPage(1L);
//        documentHistoryQueryReq.setPageSize(10L);
        System.out.println(JSON.toJSONString(documentHistoryFacade.queryDocumentHistorys(documentHistoryQueryReq)));
    }
}