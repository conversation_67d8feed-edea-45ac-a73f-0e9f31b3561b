package com.suwell.plss.record.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.mq.dto.base.RepoFolderPathDTO;
import com.suwell.plss.record.domain.RecordUpUnderDTO;
import com.suwell.plss.record.standard.dto.request.MetadataRepoFolderReq;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public class DemoTest {


    @Test
    void pinyinTest() {
        // 返回全部拼音 默认分隔符为空格，可以添加第二个参数分隔符// ce shi
        String str2 = PinyinUtil.getPinyin("测试", "");
        String str = "哈哈";
        System.out.println(str2);
        System.out.println(com.suwell.plss.framework.common.utils.StringUtils.getAllFirstLetter(str));
    }

    @Test
    void jsonTest() {
        String repoFolderMetadataValue = "[{\"folderIds\":[1269833072389,1277685788933],\"repoId\":1269833072389,\"repoStatus\":1},{\"folderIds\":[1269798553093,1269805416453],\"repoId\":1269798553093,\"repoStatus\":1}]";
        List<MetadataRepoFolderReq> metadataRepoFolderReqList = JSON.parseArray(repoFolderMetadataValue,
                new TypeReference<MetadataRepoFolderReq>() {
                }.getType());
        List<Long> folderOrRepoIdList = Lists.newArrayList();
        for (MetadataRepoFolderReq metadataRepoFolderReq : metadataRepoFolderReqList) {
            List<Long> folderIds = metadataRepoFolderReq.getFolderIds();
            folderOrRepoIdList.add(folderIds.get(folderIds.size() - 1));
        }
        System.out.println(JSON.toJSONString(folderOrRepoIdList));
    }

    @Test
    void jsTest() {
        String repoFolderMetadataValue = "[{\"folderIds\":[1269833072389,1277685788933],\"repoId\":1269833072389,\"repoStatus\":1}]";
        List<MetadataRepoFolderReq> metadataRepoFolderReqList = JSON.parseArray(repoFolderMetadataValue,
                new TypeReference<MetadataRepoFolderReq>() {
                }.getType());
        List<RepoFolderPathDTO> repoFolderList = Lists.newArrayList();
        for (MetadataRepoFolderReq metadataRepoFolderReq : metadataRepoFolderReqList) {
            RepoFolderPathDTO repoFolderPathDTO = new RepoFolderPathDTO();
            repoFolderPathDTO.setRepoId(metadataRepoFolderReq.getRepoId());
            repoFolderPathDTO.setRepoStatus(metadataRepoFolderReq.getRepoStatus());
            repoFolderPathDTO.setFolderIds(metadataRepoFolderReq.getFolderIds());
            repoFolderList.add(repoFolderPathDTO);
        }
        System.out.println(JSON.toJSONString(repoFolderList));

    }

    @Test
    void map2Test() {
        //db
        Map<String, String> metadata1 = new HashMap<>();
        metadata1.put("标题", "标题2");
        metadata1.put("目录", "目录232");
        metadata1.put("机构层级", "层级");
        metadata1.put("发文字号", "字号");

        //view
        Map<String, String> metadata2 = new HashMap<>();
        metadata2.put("标题", "标题");
        metadata2.put("目录", "好好");

        metadata2.forEach((key, value) -> metadata1.merge(key, value, (v1, v2) ->
                StringUtils.isNotBlank(v2) ? v2 : StringPool.EMPTY));
        System.out.println(JSON.toJSONString(metadata1));
    }

    @Test
    void mapTest() {
        //db
        Map<String, String> metadata2 = new HashMap<>();
        metadata2.put("标题", "");
        metadata2.put("目录", "");

        //ocr
        Map<String, String> metadata1 = new HashMap<>();
        metadata1.put("标题", "");
        metadata1.put("目录", "目录232");

//        metadata2.forEach((key, value) -> metadata1.merge(key, value, (v1, v2) ->
//                StringUtils.isNotBlank(v2) ? v2 : v1));
        metadata2.forEach((key, value) -> metadata1.merge(key, value, (v1, v2) -> {
            if (key.equals("标题")) {
                return StringUtils.isBlank(v2) && StringUtils.isBlank(v1) ? "v2"
                        : StringUtils.isNotBlank(v2) && StringUtils.isBlank(v1) ? v2 : v1;
            } else {
                return StringUtils.isNotBlank(v2) ? v2 : v1;
            }
        }));
        System.out.println(JSON.toJSONString(metadata1));

        HashMap<String, String> maps = new HashMap<>();
        maps.put("1", "q");
        maps.put("2", "");
        maps.put("3", "");
        maps.put("4", "");
        maps.put("5", "");
        for (String key : maps.keySet()) {
            System.out.println("Key-->" + key + "   Value-->" + maps.get(key));
        }
        System.out.println(JSON.toJSONString(maps));


    }

    @Test
    void jsonStrTest() {
        List<MetadataRepoFolderReq> list = Lists.newArrayList();
        MetadataRepoFolderReq metadataRepoFolderDTO = new MetadataRepoFolderReq();
        metadataRepoFolderDTO.setRepoId(1L);
        metadataRepoFolderDTO.setRepoStatus(1);
        metadataRepoFolderDTO.setFolderIds(Lists.newArrayList(1L, 11L, 12L, 13L));

        MetadataRepoFolderReq metadataRepoFolderDTO1 = new MetadataRepoFolderReq();
        metadataRepoFolderDTO1.setRepoId(2L);
        metadataRepoFolderDTO1.setRepoStatus(2);
        metadataRepoFolderDTO1.setFolderIds(Lists.newArrayList(2L, 21L, 22L, 23L));

        list.add(metadataRepoFolderDTO);
        list.add(metadataRepoFolderDTO1);
        System.out.println(JSON.toJSONString(list));

        List<MetadataRepoFolderReq> list1 = JSON.parseObject(JSON.toJSONString(list), List.class);
        System.out.println(JSON.toJSONString(list1));
    }

    @Test
    void reUtilTest2() {
        Long recordId = 2L;
//        List<Long> list = Lists.newArrayList(1L);
//        List<Long> list = Lists.newArrayList(1L, 2L);
        List<Long> list = Lists.newArrayList(1L, 2L, 3L);
        if (CollUtil.isNotEmpty(list)) {
            RecordUpUnderDTO recordUpUnderDTO = new RecordUpUnderDTO();
            if (list.get(0).equals(recordId) && list.size() == 1) {
                recordUpUnderDTO.setRecordId(recordId);
            } else if (list.get(0).equals(recordId) && list.size() == 2) {
                recordUpUnderDTO.setRecordId(recordId);
                recordUpUnderDTO.setRecordUnderId(list.get(1));
            } else if (list.get(1).equals(recordId) && list.size() == 2) {
                recordUpUnderDTO.setRecordUpId(list.get(0));
                recordUpUnderDTO.setRecordId(recordId);
            } else {
                recordUpUnderDTO.setRecordUpId(list.get(0));
                recordUpUnderDTO.setRecordId(recordId);
                recordUpUnderDTO.setRecordUnderId(list.get(2));
            }
            System.out.println(JSON.toJSONString(recordUpUnderDTO));
        }
    }
}
