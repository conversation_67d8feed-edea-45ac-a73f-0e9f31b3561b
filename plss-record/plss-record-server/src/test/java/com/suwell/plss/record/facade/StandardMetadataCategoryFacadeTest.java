package com.suwell.plss.record.facade;

import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.MetadataCategory;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryMetadataReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryReq;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.service.StandardMetadataCategoryFacade;
import com.suwell.plss.record.standard.service.StandardMetadataFacade;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/8/11
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardMetadataCategoryFacadeTest {

    @Resource
    private StandardMetadataCategoryFacade standardMetadataCategoryFacade;
    @Resource
    private StandardMetadataFacade standardMetadataFacade;

    /**
     * 元数据分类录入
     */
    @Test
    public void addMetadataCategoryTest() {
        MetadataCategoryReq metadataCategoryReq = new MetadataCategoryReq();
        metadataCategoryReq.setName("电子公文元数据");
        MetadataCategory metadataCategory = DozerUtils.convertToNew(metadataCategoryReq, MetadataCategory.class);
        System.out.println(metadataCategory);
        //standardMetadataCategoryFacade.addMetadataCategory(metadataCategoryReq);
    }

    /**
     * 元数据分类 关联元数据
     */
    @Test
    public void addMetadata() {
        MetadataCategoryMetadataReq metadataCategoryMetadataReq = new MetadataCategoryMetadataReq();
        metadataCategoryMetadataReq.setMetadataCategoryId(234644759557L);
        List<MetadataResp> metadataRespList = standardMetadataFacade.queryList(null);
        List<Long> ids = metadataRespList.stream().map(MetadataResp::getId).collect(Collectors.toList());
        metadataCategoryMetadataReq.setMdId(0L);

//        System.out.println(JSON.toJSONString(metadataCategoryMetadataReq));
//        standardMetadataCategoryFacade.addMetadata(metadataCategoryMetadataReq);
    }
}
