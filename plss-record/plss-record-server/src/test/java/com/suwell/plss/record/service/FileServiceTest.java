//package com.suwell.plss.record.service;
//
//import com.alibaba.fastjson2.JSON;
//import com.google.common.collect.Lists;
//import com.suwell.plss.framework.common.utils.FileUploadUtils;
//import com.suwell.plss.framework.file.configure.OssConfig;
//import com.suwell.plss.framework.file.domain.OssRespDTO;
//import com.suwell.plss.framework.file.service.FileService;
//import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO;
//import com.suwell.plss.record.PlssRecordApplication;
//import com.suwell.plss.record.service.impl.FolderServiceImpl;
//import com.suwell.plss.record.standard.enums.RecordEnum.OssBucketEnum;
//import java.io.File;
//import java.io.FileInputStream;
//import java.util.List;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.compress.utils.IOUtils;
//import org.apache.http.entity.ContentType;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.web.multipart.MultipartFile;
//
///**
// * 对象存储单测
// *
// * <AUTHOR>
// * @date 2023/8/1
// */
//@Slf4j
//@SpringBootTest(classes = PlssRecordApplication.class)
//public class FileServiceTest {
//
//    @Resource
//    private OssConfig ossConfig;
//    @Resource
//    private FileService fileService;
//    @Resource
//    private FolderServiceImpl folderService;
//    @Resource
//    private RecordService recordService;
//
//    /**
//     * 对象存储上传
//     */
//    @Test
//    public void uploadFileTest() {
//        try {
////            File file = new File("D:\\历史制度性文件收集表.xls");
//            File file = new File("D:\\test\\images\\表设计plss.png");
////            System.out.println(file.getName());
//            FileInputStream input = new FileInputStream(file);
//            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
//                    ContentType.APPLICATION_OCTET_STREAM.toString(), IOUtils.toByteArray(input));
//
//            //文件上传
//            String relativePath = FileUploadUtils.extractFilename(multipartFile);
//            OssRespDTO dto = fileService.upload(multipartFile.getBytes(), relativePath,
//                    multipartFile.getContentType(), ossConfig.getBucketName());
//
//            System.out.println(JSON.toJSONString(dto));
//            System.out.println(fileService.shareUrl(relativePath));
//        } catch (Exception e) {
//            log.error("fail uploadFile", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    /**
//     * 集成es 对象存储上传
//     */
//    @Test
//    public void uploadFile11Test() {
//        try {
//            MultipartFile multipartFile = FileUploadUtils.createMultipartFile(
//                    "http://*************:9000/cma/2023/08/21/%E9%B2%81%E6%B0%94%E5%85%9A%E5%8F%91%E3%80%942020%E3%80%9570%E5%8F%B7-%E4%B8%AD%E5%85%B1%E5%B1%B1%E4%B8%9C%E7%9C%81%E6%B0%94%E8%B1%A1%E5%B1%80%E5%85%9A%E7%BB%84%E5%85%B3%E4%BA%8E%E5%BC%80%E5%B1%95%E8%9E%8D%E5%85%A5%E4%B8%9A%E5%8A%A1%E6%8A%93%E5%85%9A%E5%BB%BA%E5%B7%A5%E4%BD%9C%E7%9A%84%E9%80%9A%E7%9F%A5_20230821160910A001.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230821%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230821T081021Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=bcdd18ca7fa7e36ceb38ad25a4df28e796d253c176f78f9152b91c4da9c726a3",
//                    "你好.doc");
//            String path = FileUploadUtils.extractFilename(multipartFile);
//            OssRespDTO dto = fileService.upload(multipartFile.getBytes(), path,
//                    multipartFile.getContentType(), OssBucketEnum.ORIGIN_BUCKET.getOssBucketName());
//            System.out.println(JSON.toJSONString(dto));
//        } catch (Exception e) {
//            log.error("fail uploadFile", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//
//    @Test
//    void getCurrentId() {
////        Long currentFolderId = folderService.getCurrentFolderId("科技创新", "科技合作");
////        System.out.println(currentFolderId);
//    }
//
//    @Test
//    void getRecordRel() {
//        List<MqRecordPathDTO> recordRel = recordService.getRecordRel(Lists.newArrayList(576548179205L));
//        System.out.println(recordRel);
//    }
//
//}
