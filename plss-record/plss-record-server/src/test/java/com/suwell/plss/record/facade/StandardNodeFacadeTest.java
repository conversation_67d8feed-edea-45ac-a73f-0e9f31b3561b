package com.suwell.plss.record.facade;

import static com.suwell.plss.framework.mq.enums.EventType.FILE_1_OFD;
import static com.suwell.plss.framework.mq.enums.EventType.FILE_2_OFD;
import static com.suwell.plss.framework.mq.enums.EventType.GET_CLASSIFY;
import static com.suwell.plss.framework.mq.enums.EventType.GET_ENTITY;
import static com.suwell.plss.framework.mq.enums.EventType.GET_KEYWORD;
import static com.suwell.plss.framework.mq.enums.EventType.GET_METADATA;
import static com.suwell.plss.framework.mq.enums.EventType.GET_SUMMARY;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_AI;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_CONVERT_SPLIT;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_CONVERT_SPLIT_AUTO;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_CONVERT_SPLIT_AUTO_MANUALLY_REVIEW;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_CONVERT_SPLIT_MANUALLY_REVIEW;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_FILE;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_FILE_MODIFY_REPO_POSITION;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_FILE_REPO_POSITION;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_IN_STORAGE;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_IN_STORAGE_THUMBNAIL;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_MANUALLY_REVIEW;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_MANUALLY_REVIEW_AUDIT_PEOPLE;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_MANUALLY_REVIEW_PADDING;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_NOT_EXECUTION;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_UN_SENSITIVE;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_2_OFD;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_CLASSIFY;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_ENTITY;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_KEYWORD;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_SEGMENT;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_SUMMARY;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_AI_VECTOR;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_ATTACHMENT;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_FILE;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_MAIN_FILE;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_METADATA;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_OFD;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_OFD_DESENSITIZATION;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_REPO_POSITION;
import static com.suwell.plss.record.standard.enums.RecordEnum.NodeSpecificationEnum.NODE_SPECIFICATION_TEXT;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.CheckBoxButton;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.PopupBoxButton;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.RadioBox;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.RadioBoxButton;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.RepoPositionButton;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.SwitchBoxButton;
import com.suwell.plss.record.standard.domain.SelectedPeopleDTO;
import com.suwell.plss.record.standard.dto.request.NodeAddReq;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.service.StandardNodeFacade;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/11/7
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardNodeFacadeTest {

    @Resource
    private StandardNodeFacade standardNodeFacade;

    /**
     * 添加 文件上传 节点基本信息
     */
    @Test
    void addNodeAllNewTest() {
        addNodeUploadTest();
        addNodeConvertTest();
//        addNodeDesensitizeTest();
//        addNodeSplitNewTest();
        addNodeMetadataNewTest();
        addNodeAiTest();
        addNodeManualProcessTest();
        addNodeStoreTest();
    }

    @Test
    void queryListTest() {
        System.out.println(JSON.toJSONString(standardNodeFacade.queryList()));
    }

//
//    /**
//     * 添加 文件上传 节点基本信息
//     */
//    @Test
//    void addNodeAllTest() {
//        addNodeUploadTest();
//        addNodeAttTest();
//        addNodeConvertTest();
//        addNodeSplitTest();
//        addNodeDesensitizeTest();
//        addNodeMetadataTest();
//        addNodeManualReviewTest();
//        addNodeAiTest();
//        addNodeStoreTest();
//        addNodeKnowledgeRepoTest();
//    }

    /**
     * 添加 文件上传 节点基本信息
     */
    @Test
    void addNodeUploadTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_UPLOAD.getDesc());
        req.setNodeType(NodeTypeEnum.NT_UPLOAD.getCode());
        req.setRemark("上传需要入库的文件");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_FILE.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_FILE.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_UPLOAD.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        RepoPositionButton repoPositionButton = new RepoPositionButton();
        repoPositionButton.setCode(PIPELINE_FILE_REPO_POSITION.getCode());
        repoPositionButton.setCandidateCode(Lists.newArrayList(PIPELINE_FILE_REPO_POSITION.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        repoPositionButton.setDesc("设置默认入库位置：");
        repoPositionButton.setRepoIdList(Lists.newArrayList());
        definedConfJsonDTO.setRepoPositionButton(repoPositionButton);

//        // RadioBoxButton
//        RadioBoxButton radioBoxButton = new RadioBoxButton();
//        radioBoxButton.setDesc("入库文件来源");
//        List<RadioBox> radioBoxList = Lists.newArrayList();
//        RadioBox radioBox1 = new RadioBox();
//        radioBox1.setCode(PIPELINE_FILE_USER_UPLOAD.getCode());
//        radioBox1.setDesc("用户上传");
//        RadioBox radioBox2 = new RadioBox();
//        //PIPELINE_FILE_INTERFACE_UPLOAD
//        //PIPELINE_FILE_PURCHASE_UPLOAD
//        radioBox2.setCode(PIPELINE_NOT_EXECUTION.getCode());
//        radioBox2.setDesc("接口上传");
//        radioBoxList.add(radioBox1);
//        radioBoxList.add(radioBox2);
//        radioBoxButton.setRadioBoxList(radioBoxList);
//        definedConfJsonDTO.setRadioBoxButton(radioBoxButton);

        // CheckboxButton
        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
        checkBoxButton1.setCode(PIPELINE_FILE_MODIFY_REPO_POSITION.getCode());
        checkBoxButton1.setCandidateCode(Lists.newArrayList(PIPELINE_FILE_MODIFY_REPO_POSITION.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton1.setDesc("允许文件入库人员修改入库位置");
        checkBoxButtonList.add(checkBoxButton1);
        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);

        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_FILE).stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE, NODE_SPECIFICATION_ATTACHMENT,
                        NODE_SPECIFICATION_METADATA, NODE_SPECIFICATION_REPO_POSITION)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }

//    /**
//     * 添加 att 节点基本信息
//     */
//    @Test
//    void addNodeAttTest() {
//        NodeAddReq req = new NodeAddReq();
//        req.setName(NodeTypeEnum.NT_ADD_ATTACHMENT.getDesc());
//        req.setNodeType(NodeTypeEnum.NT_ADD_ATTACHMENT.getCode());
//        req.setRemark("将附件添加至文档");
//        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
//        definedConfJsonDTO.setNodeType(req.getNodeType());
//
//        //1-开启，2-关闭
//        Map<Integer, String> switchBoxButton = Maps.newHashMap();
//        switchBoxButton.put(PIPELINE_ATTACHMENT.getCode(), "是否需要添加附件");
//        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
//
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE, NODE_SPECIFICATION_ATTACHMENT)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setInputScope(inputScope);
//
//        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE, NODE_SPECIFICATION_ATTACHMENT)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setOutputScope(outputScope);
//        standardNodeFacade.addNode(req);
//    }

    /**
     * 添加 转换 节点基本信息
     */
    @Test
    void addNodeConvertTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_FILE_CONVERT.getDesc());
        req.setNodeType(NodeTypeEnum.NT_FILE_CONVERT.getCode());
        req.setRemark("文件转换");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_NOT_EXECUTION.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(FILE_2_OFD.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_FILE_CONVERT.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

//        // RadioBoxButton
//        RadioBoxButton radioBoxButton = new RadioBoxButton();
//        radioBoxButton.setDesc("");
//        List<RadioBox> radioBoxList = Lists.newArrayList();
//        RadioBox radioBox1 = new RadioBox();
//        radioBox1.setCode(PIPELINE_NOT_EXECUTION.getCode());
//        radioBox1.setCandidateCode(Lists.newArrayList(FILE_1_OFD.getCode(),
//                PIPELINE_NOT_EXECUTION.getCode()));
//        radioBox1.setDesc("转换成OFD格式");
//
//        RadioBox radioBox2 = new RadioBox();
//        radioBox2.setCode(PIPELINE_NOT_EXECUTION.getCode());
//        radioBox2.setCandidateCode(Lists.newArrayList(FILE_2_OFD.getCode(),
//                PIPELINE_NOT_EXECUTION.getCode()));
//        radioBox2.setDesc("双层OFD处理");

        // CheckboxButton
        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
        checkBoxButton1.setCode(PIPELINE_NOT_EXECUTION.getCode());
        checkBoxButton1.setCandidateCode(Lists.newArrayList(FILE_1_OFD.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton1.setDesc("转换成OFD格式");

        CheckBoxButton checkBoxButton2 = new CheckBoxButton();
        checkBoxButton2.setCode(PIPELINE_NOT_EXECUTION.getCode());
        checkBoxButton2.setCandidateCode(Lists.newArrayList(FILE_2_OFD.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton2.setDesc("扫描件双层OFD处理");
        checkBoxButtonList.add(checkBoxButton1);
        checkBoxButtonList.add(checkBoxButton2);

        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);
        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        System.out.println(JSON.toJSONString(req));
//        standardNodeFacade.addNode(req);
    }

//    /**
//     * 添加 拆分 节点基本信息
//     */
//    @Test
//    void addNodeSplitTest() {
//        NodeAddReq req = new NodeAddReq();
//        req.setName(NodeTypeEnum.NT_FILE_SPLIT.getDesc());
//        req.setNodeType(NodeTypeEnum.NT_FILE_SPLIT.getCode());
//        req.setRemark("书籍、刊物等按照文章进行拆分入库");
//        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
//        definedConfJsonDTO.setNodeType(req.getNodeType());
//
//        //1-开启，2-关闭
//        Map<Integer, String> switchBoxButton = Maps.newHashMap();
//        switchBoxButton.put(PIPELINE_CONVERT_SPLIT.getCode(), NodeTypeEnum.NT_FILE_SPLIT.getDesc());
//        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
//
//        // CheckboxButton
//        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
//        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
//        checkBoxButton1.setCode(PIPELINE_CONVERT_SPLIT_AUTO_MANUALLY_REVIEW.getCode());
//        checkBoxButton1.setDesc("系统自动拆分且人工核对");
//        checkBoxButtonList.add(checkBoxButton1);
//        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setInputScope(inputScope);
//
//        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setOutputScope(outputScope);
//        standardNodeFacade.addNode(req);
//    }

    /**
     * 添加 拆分 节点基本信息
     */
    @Test
    void addNodeSplitNewTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_FILE_SPLIT.getDesc());
        req.setNodeType(NodeTypeEnum.NT_FILE_SPLIT.getCode());
        req.setRemark("书籍、刊物等按照文章进行拆分入库");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_NOT_EXECUTION.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_CONVERT_SPLIT.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_FILE_SPLIT.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        // RadioBoxButton
        RadioBoxButton radioBoxButton = new RadioBoxButton();
        radioBoxButton.setDesc("");
        List<RadioBox> radioBoxList = Lists.newArrayList();
        RadioBox radioBox1 = new RadioBox();
        radioBox1.setCode(PIPELINE_CONVERT_SPLIT_AUTO_MANUALLY_REVIEW.getCode());
        radioBox1.setCandidateCode(Lists.newArrayList(PIPELINE_CONVERT_SPLIT_AUTO_MANUALLY_REVIEW.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        radioBox1.setDesc(PIPELINE_CONVERT_SPLIT_AUTO_MANUALLY_REVIEW.getDesc());
        RadioBox radioBox2 = new RadioBox();
        //PIPELINE_FILE_INTERFACE_UPLOAD
        //PIPELINE_FILE_PURCHASE_UPLOAD
        radioBox2.setCode(PIPELINE_NOT_EXECUTION.getCode());
        radioBox2.setCandidateCode(Lists.newArrayList(PIPELINE_CONVERT_SPLIT_AUTO.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));

        radioBox2.setDesc(PIPELINE_CONVERT_SPLIT_AUTO.getDesc());

        RadioBox radioBox3 = new RadioBox();
        radioBox3.setCode(PIPELINE_NOT_EXECUTION.getCode());
        radioBox3.setCandidateCode(Lists.newArrayList(PIPELINE_CONVERT_SPLIT_MANUALLY_REVIEW.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        radioBox3.setDesc(PIPELINE_CONVERT_SPLIT_MANUALLY_REVIEW.getDesc());

        radioBoxList.add(radioBox1);
        radioBoxList.add(radioBox2);
        radioBoxList.add(radioBox3);
        radioBoxButton.setRadioBoxList(radioBoxList);
        definedConfJsonDTO.setRadioBoxButton(radioBoxButton);

        req.setDefinedConfigJson(definedConfJsonDTO);
        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }

//    /**
//     * 添加 元数据 节点基本信息
//     */
//    @Test
//    void addNodeMetadataTest() {
//        NodeAddReq req = new NodeAddReq();
//        req.setName(NodeTypeEnum.NT_METADATA_FILL.getDesc());
//        req.setNodeType(NodeTypeEnum.NT_METADATA_FILL.getCode());
//        req.setRemark("按照文档类型和模型提取元数据");
//        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
//        definedConfJsonDTO.setNodeType(req.getNodeType());
//
//        //1-开启，2-关闭
//        Map<Integer, String> switchBoxButton = Maps.newHashMap();
//        switchBoxButton.put(GET_METADATA.getCode(), NodeTypeEnum.NT_METADATA_FILL.getDesc());
//        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
//
//        // CheckboxButton
//        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
//        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
//        checkBoxButton1.setCode(PIPELINE_OCR_MANUALLY_REVIEW.getCode());
//        checkBoxButton1.setDesc("需要人工核对");
//        checkBoxButtonList.add(checkBoxButton1);
//        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setInputScope(inputScope);
//
//        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_2_OFD,
//                        NODE_SPECIFICATION_METADATA, NODE_SPECIFICATION_TEXT)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setOutputScope(outputScope);
//        standardNodeFacade.addNode(req);
//    }

    /**
     * 添加 元数据 节点基本信息
     */
    @Test
    void addNodeMetadataNewTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_METADATA_FILL.getDesc());
        req.setNodeType(NodeTypeEnum.NT_METADATA_FILL.getCode());
        req.setRemark("按照文档类型和模型提取元数据");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(GET_METADATA.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(GET_METADATA.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_METADATA_FILL.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_2_OFD,
                        NODE_SPECIFICATION_METADATA, NODE_SPECIFICATION_TEXT)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }


    /**
     * 添加 脱敏 节点基本信息
     */
    @Test
    void addNodeDesensitizeTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_FILE_DESENSITIZE.getDesc());
        req.setNodeType(NodeTypeEnum.NT_FILE_DESENSITIZE.getCode());
        req.setRemark("文件入库前需人工屏蔽敏感词，入库后系统仍可自动识别敏感词库里的敏感词。");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_NOT_EXECUTION.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_UN_SENSITIVE.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_FILE_DESENSITIZE.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_OFD_DESENSITIZATION)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }
//
//    /**
//     * 添加 脱敏 节点基本信息
//     */
//    @Test
//    void addNodeManualReviewTest() {
//        NodeAddReq req = new NodeAddReq();
//        req.setName(NodeTypeEnum.NT_MANUAL_REVIEW.getDesc());
//        req.setNodeType(NodeTypeEnum.NT_MANUAL_REVIEW.getCode());
//        req.setRemark("数据提取的准确性校正数据重复性检测，是否涉及敏感内容");
//        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
//        definedConfJsonDTO.setNodeType(req.getNodeType());
//
//        //1-开启，2-关闭
//        Map<Integer, String> switchBoxButton = Maps.newHashMap();
//        switchBoxButton.put(PIPELINE_MANUALLY_REVIEW.getCode(), "是否需要手动对文件进行脱敏处理");
//        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        // RadioBoxButton
//        RadioBoxButton radioBoxButton = new RadioBoxButton();
//        radioBoxButton.setDesc("审核");
//        List<RadioBox> radioBoxList = Lists.newArrayList();
//        RadioBox radioBox1 = new RadioBox();
//        radioBox1.setCode(PIPELINE_MANUALLY_REVIEW_ALL_AUDIT.getCode());
//        radioBox1.setDesc("所有均审核");
//        RadioBox radioBox2 = new RadioBox();
////        PIPELINE_MANUALLY_REVIEW_CONDITION_AUDIT
//        radioBox2.setCode(PIPELINE_NOT_EXECUTION.getCode());
//        radioBox2.setDesc("条件触发审核");
//        radioBoxList.add(radioBox1);
//        radioBoxList.add(radioBox2);
//        radioBoxButton.setRadioBoxList(radioBoxList);
//
//        // CheckboxButton
//        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
//        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
//        checkBoxButton1.setCode(PIPELINE_MANUALLY_REVIEW_SENSITIVE_CONTENT.getCode());
//        checkBoxButton1.setDesc("涉及敏感内容");
//        checkBoxButtonList.add(checkBoxButton1);
//        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        SelectBoxButton selectBoxButton = new SelectBoxButton();
//        selectBoxButton.setCode(PIPELINE_MANUALLY_REVIEW_AUDIT_PEOPLE.getCode());
//        selectBoxButton.setDesc("配置审核人");
//        Map<Long, String> selectBox = Maps.newHashMap();
//        selectBox.put(11111L, "张三");
//        selectBoxButton.setSelectBox(selectBox);
//
//        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setInputScope(inputScope);
//
//        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setOutputScope(outputScope);
//        standardNodeFacade.addNode(req);
//    }

    /**
     * 添加 AI 节点基本信息
     */
    @Test
    void addNodeAiTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_CITATION_INFO.getDesc());
        req.setNodeType(NodeTypeEnum.NT_CITATION_INFO.getCode());
        req.setRemark("提取文档摘要、关键词、实体、自动分类等");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());
        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_NOT_EXECUTION.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_AI.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_CITATION_INFO.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        // CheckboxButton
        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
        checkBoxButton1.setCode(GET_SUMMARY.getCode());
        checkBoxButton1.setCandidateCode(Lists.newArrayList(GET_SUMMARY.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton1.setDesc("摘要提取");
        CheckBoxButton checkBoxButton2 = new CheckBoxButton();
        checkBoxButton2.setCode(GET_CLASSIFY.getCode());
        checkBoxButton2.setCandidateCode(Lists.newArrayList(GET_CLASSIFY.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton2.setDesc("主题分类");
        CheckBoxButton checkBoxButton3 = new CheckBoxButton();
        checkBoxButton3.setCode(GET_KEYWORD.getCode());
        checkBoxButton3.setCandidateCode(Lists.newArrayList(GET_KEYWORD.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton3.setDesc("主题词提取");
        CheckBoxButton checkBoxButton4 = new CheckBoxButton();
        checkBoxButton4.setCode(GET_ENTITY.getCode());
        checkBoxButton4.setCandidateCode(Lists.newArrayList(GET_ENTITY.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton4.setDesc("实体识别");
//        CheckBoxButton checkBoxButton5 = new CheckBoxButton();
//        checkBoxButton5.setCode(GET_SEGMENT.getCode());
//        checkBoxButton5.setDesc("提取分段");
//        CheckBoxButton checkBoxButton6 = new CheckBoxButton();
//        checkBoxButton6.setCode(GET_VECTOR.getCode());
//        checkBoxButton6.setDesc("向量化");
        checkBoxButtonList.add(checkBoxButton1);
        checkBoxButtonList.add(checkBoxButton2);
        checkBoxButtonList.add(checkBoxButton3);
        checkBoxButtonList.add(checkBoxButton4);
//        checkBoxButtonList.add(checkBoxButton5);
//        checkBoxButtonList.add(checkBoxButton6);
        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);
        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_2_OFD).stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_AI_SUMMARY, NODE_SPECIFICATION_AI_CLASSIFY,
                        NODE_SPECIFICATION_AI_SEGMENT, NODE_SPECIFICATION_AI_KEYWORD,
                        NODE_SPECIFICATION_AI_ENTITY, NODE_SPECIFICATION_AI_VECTOR)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }


    /**
     * 添加 脱敏 节点基本信息
     */
    @Test
    void addNodeManualProcessTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_MANUAL_PROCESS.getDesc());
        req.setNodeType(NodeTypeEnum.NT_MANUAL_PROCESS.getCode());
        req.setRemark("核对元数据和标引信息提取内容是否准确；也支持人工填充元数据和标引信息。");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_NOT_EXECUTION.getCode());
        switchBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_MANUALLY_REVIEW_PADDING.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_MANUAL_PROCESS.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setOutputScope(outputScope);
        standardNodeFacade.addNode(req);
    }


    /**
     * 添加 人工审核 节点基本信息
     */
    @Test
    void addNodeAuditorTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_MANUAL_REVIEW.getDesc());
        req.setNodeType(NodeTypeEnum.NT_MANUAL_REVIEW.getCode());
        req.setRemark("审核数据提取的准确性、是否涉及敏感内容");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_MANUALLY_REVIEW.getCode());
        switchBoxButton.setCandidateCode(
                Lists.newArrayList(PIPELINE_MANUALLY_REVIEW.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_MANUAL_REVIEW.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        PopupBoxButton popupBoxButton = new PopupBoxButton();
        popupBoxButton.setCode(PIPELINE_MANUALLY_REVIEW_AUDIT_PEOPLE.getCode());
        popupBoxButton.setCandidateCode(Lists.newArrayList(PIPELINE_MANUALLY_REVIEW_AUDIT_PEOPLE.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        popupBoxButton.setDesc(PIPELINE_MANUALLY_REVIEW_AUDIT_PEOPLE.getDesc());
        popupBoxButton.setIdList(Lists.newArrayList());
        SelectedPeopleDTO sp = new SelectedPeopleDTO();

//        List<PeopleDTO> peopleDTOList = Lists.newArrayList();
//        PeopleDTO peopleDTO = new PeopleDTO();
//        peopleDTO.setId(1L);
//        peopleDTO.setOrgId(Lists.newArrayList(222L));
//        peopleDTOList.add(peopleDTO);
//        sp.setP(peopleDTOList);
        sp.setP(Lists.newArrayList());
        sp.setRid(Lists.newArrayList());
        popupBoxButton.setSp(sp);

        definedConfJsonDTO.setPopupBoxButton(popupBoxButton);

        req.setDefinedConfigJson(definedConfJsonDTO);

        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        String outputScope = Lists.newArrayList(NODE_SPECIFICATION_MAIN_FILE)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(outputScope);
        standardNodeFacade.addNode(req);
    }

    /**
     * 添加 脱敏 节点基本信息
     */
    @Test
    void addNodeStoreTest() {
        NodeAddReq req = new NodeAddReq();
        req.setName(NodeTypeEnum.NT_STORE.getDesc());
        req.setNodeType(NodeTypeEnum.NT_STORE.getCode());
        req.setRemark("入库操作");
        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
        definedConfJsonDTO.setNodeType(req.getNodeType());

        //1-开启，2-关闭
        SwitchBoxButton switchBoxButton = new SwitchBoxButton();
        switchBoxButton.setCode(PIPELINE_IN_STORAGE.getCode());
        switchBoxButton.setCandidateCode(
                Lists.newArrayList(PIPELINE_IN_STORAGE.getCode(), PIPELINE_NOT_EXECUTION.getCode()));
        switchBoxButton.setDesc(NodeTypeEnum.NT_STORE.getDesc());
        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);

        // CheckboxButton
        List<CheckBoxButton> checkBoxButtonList = Lists.newArrayList();
        CheckBoxButton checkBoxButton1 = new CheckBoxButton();
        checkBoxButton1.setCode(PIPELINE_NOT_EXECUTION.getCode());
        checkBoxButton1.setCandidateCode(Lists.newArrayList(PIPELINE_IN_STORAGE_THUMBNAIL.getCode(),
                PIPELINE_NOT_EXECUTION.getCode()));
        checkBoxButton1.setDesc("生成缩略图");
        checkBoxButtonList.add(checkBoxButton1);
        definedConfJsonDTO.setCheckBoxButtonList(checkBoxButtonList);

        req.setDefinedConfigJson(definedConfJsonDTO);
        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_REPO_POSITION,
                        NODE_SPECIFICATION_OFD, NODE_SPECIFICATION_METADATA)
                .stream().map(c -> c.getCode().toString())
                .collect(Collectors.joining(","));
        req.setInputScope(inputScope);

        standardNodeFacade.addNode(req);
    }

//    /**
//     * 添加 脱敏 节点基本信息
//     */
//    @Test
//    void addNodeKnowledgeRepoTest() {
//        NodeAddReq req = new NodeAddReq();
//        req.setName(NodeTypeEnum.NT_KNOWLEDGE_REPO.getDesc());
//        req.setNodeType(NodeTypeEnum.NT_KNOWLEDGE_REPO.getCode());
//        req.setRemark("内置库");
//        DefinedConfigJsonDTO definedConfJsonDTO = new DefinedConfigJsonDTO();
//        definedConfJsonDTO.setNodeType(req.getNodeType());
//        //1-开启，2-关闭
//        Map<Integer, String> switchBoxButton = Maps.newHashMap();
//        switchBoxButton.put(PIPELINE_FIXATION_STORAGE.getCode(), "入内置库节点");
//        definedConfJsonDTO.setSwitchBoxButton(switchBoxButton);
//
//        req.setDefinedConfigJson(definedConfJsonDTO);
//
//        String inputScope = Lists.newArrayList(NODE_SPECIFICATION_REPO_POSITION, NODE_SPECIFICATION_MAIN_FILE)
//                .stream().map(c -> c.getCode().toString())
//                .collect(Collectors.joining(","));
//        req.setInputScope(inputScope);
//        standardNodeFacade.addNode(req);
//    }


}
