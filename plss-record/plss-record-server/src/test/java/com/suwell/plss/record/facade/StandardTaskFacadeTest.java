package com.suwell.plss.record.facade;

import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.standard.domain.PlanNodeDto;
import com.suwell.plss.record.util.NodeCacheUtil;
import java.io.File;
import java.util.Map;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @create 2023/11/17
 * @content
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardTaskFacadeTest {

    @Resource
    FileService fileService;
    @Resource
    private NodeCacheUtil nodeCacheUtil;

    @Test
    void planNodeMapTest() {
        Long planId = 2250309949701L;
        final Map<Long, PlanNodeDto> planNodeMap = nodeCacheUtil.getPlanNodeMap(planId);
        System.out.println(planNodeMap);
    }

    @Test
    void innerDownloadTest() {
        File file = FileUtils.getFile("D:\\logs\\xxx.doc");
//        fileService.innerDownload(3131583419141L, FileUtil.getOutputStream(file));
    }

}
