package com.suwell.plss.record.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordRel;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.RecordMigrateService;
import java.util.List;
import java.util.Set;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/10/9
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class DataExportRecordRelServiceTest {
    @Resource
    private RecordMigrateService recordMigrateService;
    @Resource
    private RecordRelService recordRelService;
    /**
     * 测试
     */
    @Test
    void executeExportRecordRelTest() {
        List<Long> recordList = Lists.newArrayList();
        recordList.add(108784303787398L);
        List<Long> incrementRecordIdList = recordMigrateService.exportRecord(recordList, "D:\\A");
        System.out.println(JSON.toJSONString(incrementRecordIdList));
    }

    /**
     * 测试
     */
    @Test
    void importExportRecordRelTest() {
        List<RecordRel> list = recordRelService.list(Wrappers.<RecordRel>lambdaQuery()
                .in(RecordRel::getReferId, Lists.newArrayList(108784303787398L))
                .or()
                .in(RecordRel::getRecordId, Lists.newArrayList(108784303787398L)));
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    void importExportRecordRelTest1() {
        List<RecordRel> recordRelList = CommonUtil.deseriallizeList("D:\\A\\189735a7bcfe476baa6a0cdf7fbaaba4.rcr", RecordRel.class);
        System.out.println(recordRelList);
    }

    public static void main(String[] args) {
        List<Long> reqList = Lists.newArrayList(1L, 2L, 4L);
        Set<Long> resultIds = Sets.newHashSet(reqList);

        // 查出结果
        List<Long> recordRelByIds = Lists.newArrayList(1L, 2L,3L);

        // 查询结果里面的差集合
        List<Long> subtractToList = CollUtil.subtractToList(recordRelByIds, resultIds).stream().toList();
        System.out.println(subtractToList);//[3]
        resultIds.addAll(subtractToList);

        System.out.println(resultIds);// [1, 2, 4, 3]


        List<Long> subtractToList2 = CollUtil.subtractToList(Lists.newArrayList(1L, 2L, 3L,5L,7L),
                resultIds).stream().toList();
        System.out.println(subtractToList2);
        resultIds.addAll(subtractToList2);

        System.out.println(resultIds);

        List<Long> list = CollUtil.subtractToList(resultIds, reqList).stream().toList();
        System.out.println(list);
    }
}
