package com.suwell.plss.record.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.RetryMsg;
import com.suwell.plss.record.enums.PersonalRecordStatusEnums;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskReq;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class DataLoadTaskServiceTest {

    @Resource
    private DataLoadTaskService dataLoadTaskService;
    @Resource
    private RetryMsgService retryMsgService;
    /**
     * 测试
     */
    @Test
    void saveRecordProcessDetailTest() {
        DataLoadTaskReq req = new DataLoadTaskReq();
        req.setPage(1);
        req.setPageSize(20);
        System.out.println(JSON.toJSONString(dataLoadTaskService.queryPage(req)));
    }

    @Test
    void retryMsgServiceTest() {
        final List<RetryMsg> retryMsgList = retryMsgService.list(Wrappers.<RetryMsg>query().lambda()
                .eq(RetryMsg::getBizType, PersonalRecordStatusEnums.RetryMsgBizTypeEnum.ATTACHMENT_EXTRACTION.getCode())
                .eq(RetryMsg::getStatus, NormalState.FORBIDDEN.getCode())
                .lt(RetryMsg::getDocId, Long.MAX_VALUE)
                .orderByDesc(RetryMsg::getModifiedTime)
                .orderByDesc(RetryMsg::getDocId).last(" LIMIT " + 500));
        System.out.println(retryMsgList);
    }
}
