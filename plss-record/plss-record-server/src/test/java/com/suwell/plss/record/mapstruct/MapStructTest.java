package com.suwell.plss.record.mapstruct;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.entity.LogRecordView;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/25
 */
public class MapStructTest{

        @Test
        void testStruct() {
            LogRecordView recordLogReq = new LogRecordView();
            recordLogReq.setUserId("1");
            IPage<LogRecordView> iPage = new Page<>();
            iPage.setRecords(List.of(recordLogReq));
            iPage.setTotal(2);
            iPage.setSize(10);
            iPage.setCurrent(1);
            Page<RecordLogResp> resut =  RecordLogConvertor.INSTANCE.toRespList(iPage);

            System.out.println(JSON.toJSONString(resut));
        }
}
