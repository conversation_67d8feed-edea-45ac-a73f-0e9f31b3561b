package com.suwell.plss.record.service;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.RecordTypeQueryReq;
import com.suwell.plss.record.standard.dto.response.RecordTypeResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class RecordTypeServiceTest {

    @Resource
    private RecordTypeService recordTypeService;

    @Test
    void queryPageTest() {
        RecordTypeQueryReq req = new RecordTypeQueryReq();
        req.setPage(1);
        req.setPageSize(10);
        PageUtils<RecordTypeResp> pageUtils = recordTypeService.queryPage(req);

        System.out.println(JSON.toJSONString(pageUtils));
    }
}
