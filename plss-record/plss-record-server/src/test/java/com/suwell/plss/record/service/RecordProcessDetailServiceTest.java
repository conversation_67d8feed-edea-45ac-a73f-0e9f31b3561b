package com.suwell.plss.record.service;

import static com.suwell.plss.framework.mq.enums.EventType.FILE_2_OFD;
import static com.suwell.plss.framework.mq.enums.EventType.GET_METADATA;
import static com.suwell.plss.framework.mq.enums.EventType.GET_TEXT;
import static com.suwell.plss.framework.mq.enums.EventType.getEnum;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.RecordProcessDetail;
import com.suwell.plss.record.enums.RecordProcessTypeEnum;
import com.suwell.plss.record.standard.enums.RecordProcessStatusEnum;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/10/16
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class RecordProcessDetailServiceTest {

    @Resource
    private RecordProcessDetailService recordProcessDetailService;

    /**
     * 测试
     */
    @Test
    void saveRecordProcessDetailTest() {
        recordProcessDetailService.saveBatchRecordProcessDetail(111L, "哈哈",
                Lists.newArrayList(FILE_2_OFD, GET_TEXT, GET_METADATA));
    }

    @Test
    void queryRecordProcessDetailTest() {
        List<RecordProcessDetail> list = recordProcessDetailService.list(Wrappers.<RecordProcessDetail>query().lambda()
                .eq(RecordProcessDetail::getRecordProcessStatus, NormalState.NORMAL.getCode())
                .eq(RecordProcessDetail::getRecordId, 111L));
        if (CollUtil.isNotEmpty(list)) {
            Integer eventCode = list.stream().map(RecordProcessDetail::getRecordProcessType)
                    .collect(Collectors.toList()).get(0);
            RecordProcessTypeEnum anEnum = RecordProcessTypeEnum.getEnum(getEnum(eventCode));
            System.out.println(anEnum.getRecordProcessStatusEnum().getCode());
            RecordProcessStatusEnum recordProcessStatusEnum = anEnum.getRecordProcessStatusEnum();
            System.out.println(recordProcessStatusEnum.getCode()
                    + "===" + recordProcessStatusEnum.getRecordStatusEnum().getCode());
        }
    }
}
