package com.suwell.plss.record.facade;

import com.alibaba.fastjson.JSON;
import com.suwell.plss.framework.common.enums.RecordRelEnum;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.RecordRelAddReq;
import com.suwell.plss.record.standard.dto.request.RecordRelDelReq;
import com.suwell.plss.record.standard.dto.request.RecordRelQueryReq;
import com.suwell.plss.record.standard.service.StandardRecordRelFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
class RecordRelFacadeImplTest {
    @Resource
    private StandardRecordRelFacade recordRelFacade;

    @Test
    void addRecordRel() {
        List<RecordRelAddReq> recordRel= Lists.newArrayList();
//        RecordRelAddReq req=new RecordRelAddReq();
////        req.setRecordId(2L);
////        req.setReferId(1L);
//        req.setRecordId(5L);
//        req.setReferId(2L);
//        recordRel.add(req);
//        Integer ctype= RecordRelEnum.EVOLUTION.getCode();
        RecordRelAddReq req=new RecordRelAddReq();
        req.setRecordId(6L);
        req.setReferId(1L);
        recordRel.add(req);
        RecordRelAddReq req2=new RecordRelAddReq();
        req2.setRecordId(6L);
        req2.setReferId(2L);
        recordRel.add(req2);
        Integer ctype= RecordRelEnum.RELY_ON.getCode();
        recordRelFacade.addRecordRel(recordRel,ctype);
    }

    @Test
    void removeRecordRel() {
        List<RecordRelDelReq> recordRel=Lists.newArrayList();
        RecordRelDelReq r=new RecordRelDelReq();
        r.setRecordId(6L);
        r.setReferId(1L);
        r.setCtype(RecordRelEnum.RELY_ON.getCode());
        RecordRelDelReq r1=new RecordRelDelReq();
        r1.setRecordId(6L);
        r1.setReferId(2L);
        r1.setCtype(RecordRelEnum.RELY_ON.getCode());
        recordRel.add(r);
        recordRel.add(r1);
        recordRelFacade.removeRecordRel(recordRel);
    }

    @Test
    void queryRecordRels() {
        RecordRelQueryReq recordRel=new RecordRelQueryReq();
        recordRel.setRecordId(526878960133L);
        Integer ctype= RecordRelEnum.EVOLUTION.getCode();
        recordRel.setCtype(ctype);
        System.out.println(JSON.toJSONString(recordRelFacade.queryRecordRels(recordRel)));
//        RecordRelQueryReq recordRel=new RecordRelQueryReq();
//        recordRel.setRecordId(5L);
//        Integer ctype= RecordRelEnum.EVOLUTION.getCode();
//        recordRel.setCtype(ctype);
//        System.out.println(JSON.toJSONString(recordRelFacade.queryRecordRels(recordRel)));
    }
}