package com.suwell.plss.record.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.suwell.plss.record.standard.dto.request.RecordDocumentCheckReq;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @Date 2023/11/21
 */

@SpringBootTest
class DocumentServiceTest {

    @Autowired
    DocumentService documentService;

    @Test
    void checkedDocumentUploaded() {
        RecordDocumentCheckReq recordDocumentCheckReq = new RecordDocumentCheckReq();
        recordDocumentCheckReq.setFile_md5("99423a9a6df2ae2a9d711065e3d89fca");
        assertTrue(documentService.checkedDocumentUploaded(recordDocumentCheckReq));

        RecordDocumentCheckReq recordDocumentCheckReq2 = new RecordDocumentCheckReq();
        recordDocumentCheckReq2.setFile_md5("9942349a6df2ae2342349d711065e3d89fca");
        assertFalse(documentService.checkedDocumentUploaded(recordDocumentCheckReq2));

        RecordDocumentCheckReq recordDocumentCheckReq3 = new RecordDocumentCheckReq();
//        recordDocumentCheckReq3.setName("中共中国气象局党组关于适应新时代要求大力发现培养选拔年轻干部的实施意见.doc");
        assertTrue(documentService.checkedDocumentUploaded(recordDocumentCheckReq3));

        RecordDocumentCheckReq recordDocumentCheckReq4 = new RecordDocumentCheckReq();
        assertTrue(documentService.checkedDocumentUploaded(recordDocumentCheckReq4));
    }
}