package com.suwell.plss.record.service;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.mapper.RecordMapper;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/8/14
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class DataProcessTaskServiceTest {

    @Resource
    private RecordMapper recordMapper;

    /**
     * 测试
     */
//    @Test
//    void queryRecordIdRepairMetadata() {
//        List<Long> list = recordMapper.queryRecordIdRepairMetadata(9223372036854775807L,
//                4570067600133L, new Date(), 5);
//        System.out.println(JSON.toJSONString(list));
//    }
//
//    /**
//     * 测试
//     */
//    @Test
//    void countResidualRecordIdRepairMetadataTest() {
//        Long residual = recordMapper.countResidualRecordIdRepairMetadata(9223372036854775807L,
//                4570067600133L, new Date());
//        System.out.println(residual);
//    }
//
//    /**
//     * 测试
//     */
//    @Test
//    void countRecordIdRepairMetadata() {
//        Long counted = recordMapper.countRecordIdRepairMetadata(4570067600133L, new Date());
//        System.out.println(counted);
//    }
}
