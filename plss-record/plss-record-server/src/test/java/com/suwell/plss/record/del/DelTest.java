package com.suwell.plss.record.del;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.service.FileService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class DelTest {

    @Resource
    private FileService fileService;

    @Test
    @DSTransactional
    void exportDataTest() {
        ArrayList<Document> list = new ArrayList<>();
        Document document = new Document();
        document.setFileId(323192870085509L);
        document.setOfdFileId(323193030091653L);
        document.setTxtFileId(323193049367429L);
        list.add(document);

        List<Long> prepareDel = list.stream()
                .flatMap(ele -> Stream.of(ele.getFileId(), ele.getOfdFileId(), ele.getTxtFileId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        fileService.deleteBatch(prepareDel);
    }

}
