package com.suwell.plss.record.facade;

import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.RepositoryAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryModifyReq;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.service.StandardRepositoryFacade;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 库相关单测
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardRepositoryFacadeTest {

    @Resource
    private StandardRepositoryFacade repositoryFacade;

    @Test
    public void addRepository() {
        RepositoryAddReq req = new RepositoryAddReq();
        req.setRepositoryName("wwwwwwwww");
        req.setCtype(1);
        req.setImageUrl("aaaaaaaaaaaaaaaaaaaaa");
        req.setShareType(1);
        repositoryFacade.addRepository(req);
    }

    @Test
    public void removeRepository() {
        repositoryFacade.removeRepository(1L);
    }

    @Test
    public void modifyRepository() {
        RepositoryModifyReq req = new RepositoryModifyReq();
        req.setRepoId(236944704261L);
        req.setRepositoryName("ddddddd");
        req.setCtype(1);
        req.setImageUrl("aaaaaaaaaaaaaaaaaaaaa");
        req.setShareType(1);
        repositoryFacade.modifyRepository(req);
    }

    @Test
    public void queryById() {
        RepositoryResp resp = repositoryFacade.queryById(236944704261L);
        System.out.println(resp);
    }


}
