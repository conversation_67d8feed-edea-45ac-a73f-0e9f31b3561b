package com.suwell.plss.record.facade;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.Node;
import com.suwell.plss.record.service.NodeService;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.RepoPositionButton;
import com.suwell.plss.record.standard.dto.request.PlanAddReq;
import com.suwell.plss.record.standard.dto.request.PlanNodeConfigReq;
import com.suwell.plss.record.standard.dto.request.PlanNodeReq;
import com.suwell.plss.record.standard.dto.request.PlanRecordInfoReq;
import com.suwell.plss.record.standard.service.StandardNodeFacade;
import com.suwell.plss.record.standard.service.StandardPlanFacade;
import java.io.File;
import java.io.FileInputStream;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2023/11/8
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardPlanFacadeTest {

    @Resource
    private StandardPlanFacade standardPlanFacade;
    @Resource
    private StandardNodeFacade standardNodeFacade;
    @Resource
    private NodeService nodeService;

    /**
     * 测试入库流程方案
     *
     * @throws Exception
     */
    @Test
    void addPlanRecordUploadTest() throws Exception {
        PlanRecordInfoReq planRecordInfoReq = new PlanRecordInfoReq();
        planRecordInfoReq.setPlanId(2204054850821L);
        //2053346118149 制度树/信息网络/数据资料
        //2053345234437 制度树/办公管理/综合管理
        File file = FileUtils.getFile("D:\\test\\images\\表设计plss.png");
//            String path = "https://i2.chinanews.com.cn/simg/hd/2023/08/10/bfc06cd188b944f4a6feec117aa32e6d.jpg";
//            MultipartFile multipartFile = FileUploadUtils.createMultipartFile(path, "哈哈.jpg");
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), IOUtils.toByteArray(input));
        planRecordInfoReq.setFiles(new MultipartFile[]{multipartFile});
        standardPlanFacade.addPlanRecordUpload(planRecordInfoReq);
        input.close();
    }

    /**
     * ASSEMBLY_PIPELINE_0("1,4,6,9", "文件上传->转换->OCR->入库"),
     */
    @Test
    void addPlanNode1Test() {
        PlanNodeReq planNodeReq = new PlanNodeReq();
        PlanAddReq planAddReq = new PlanAddReq();
        planAddReq.setName("北京入库方案一");
        planAddReq.setRemark("入库方案一");
        planAddReq.setRecordTypeId(11L);
        planNodeReq.setPlanAddReq(planAddReq);

        List<PlanNodeConfigReq> planNodeConfigReqList = Lists.newArrayList();
        PlanNodeConfigReq planNodeConfigReq1 = new PlanNodeConfigReq();
        planNodeConfigReq1.setNodeId(2203794586629L);
        planNodeConfigReq1.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq1.setOrderNum(1);
        Long nodeId = planNodeConfigReq1.getNodeId();
        Node node = nodeService.getById(nodeId);
        DefinedConfigJsonDTO definedConfigJsonDTO = JSON.parseObject(node.getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        RepoPositionButton repoPositionButton = definedConfigJsonDTO.getRepoPositionButton();
        //2053346118149 制度树/信息网络/数据资料
        repoPositionButton.setRepoIdList(Lists.newArrayList(2053346118149L));
        definedConfigJsonDTO.setRepoPositionButton(repoPositionButton);
        planNodeConfigReq1.setDefinedConfigJson(definedConfigJsonDTO);

        PlanNodeConfigReq planNodeConfigReq2 = new PlanNodeConfigReq();
        planNodeConfigReq2.setNodeId(2203794762501L);
        planNodeConfigReq2.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq2.setOrderNum(2);
        DefinedConfigJsonDTO definedConfigJsonDTO2 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq2.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq2.setDefinedConfigJson(definedConfigJsonDTO2);

        PlanNodeConfigReq planNodeConfigReq3 = new PlanNodeConfigReq();
        planNodeConfigReq3.setNodeId(2203794972677L);
        planNodeConfigReq3.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq3.setOrderNum(3);
        DefinedConfigJsonDTO definedConfigJsonDTO3 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq3.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq3.setDefinedConfigJson(definedConfigJsonDTO3);

        PlanNodeConfigReq planNodeConfigReq4 = new PlanNodeConfigReq();
        planNodeConfigReq4.setNodeId(2203795181061L);
        planNodeConfigReq4.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq4.setOrderNum(4);
        DefinedConfigJsonDTO definedConfigJsonDTO4 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq4.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq4.setDefinedConfigJson(definedConfigJsonDTO4);

//        PlanNodeConfigReq planNodeConfigReq5 = new PlanNodeConfigReq();
//        planNodeConfigReq5.setNodeId(2186043192325L);
//        planNodeConfigReq5.setStatus(NormalState.NORMAL.getCode());
//        planNodeConfigReq5.setOrderNum(5);
//        DefinedConfigJsonDTO definedConfigJsonDTO5 = JSON.parseObject(
//                nodeService.getById(planNodeConfigReq5.getNodeId()).getDefinedConfigJson(),
//                DefinedConfigJsonDTO.class);
//        planNodeConfigReq5.setDefinedConfigJson(definedConfigJsonDTO5);

        planNodeConfigReqList.add(planNodeConfigReq1);
        planNodeConfigReqList.add(planNodeConfigReq2);
        planNodeConfigReqList.add(planNodeConfigReq3);
        planNodeConfigReqList.add(planNodeConfigReq4);
//        planNodeConfigReqList.add(planNodeConfigReq5);

//        planNodeReq.setPlanNodeConfigReqList(planNodeConfigReqList);
        standardPlanFacade.addPlanNode(planNodeReq);
    }

    /**
     * ASSEMBLY_PIPELINE_1("1,2,4,6,9", "文件上传->附件->转换->OCR->AI->入库"),
     */
    @Test
    void addPlanNode2Test() {
        PlanNodeReq planNodeReq = new PlanNodeReq();
        PlanAddReq planAddReq = new PlanAddReq();
        planAddReq.setName("京都入库方案二");
        planAddReq.setRemark("入库方案二");
        planAddReq.setRecordTypeId(452996749317L);
        planNodeReq.setPlanAddReq(planAddReq);

        List<PlanNodeConfigReq> planNodeConfigReqList = Lists.newArrayList();
        PlanNodeConfigReq planNodeConfigReq1 = new PlanNodeConfigReq();
        planNodeConfigReq1.setNodeId(2203794586629L);
        planNodeConfigReq1.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq1.setOrderNum(1);
        Long nodeId = planNodeConfigReq1.getNodeId();
        Node node = nodeService.getById(nodeId);
        DefinedConfigJsonDTO definedConfigJsonDTO = JSON.parseObject(node.getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        RepoPositionButton repoPositionButton = definedConfigJsonDTO.getRepoPositionButton();
        //2053345234437 制度树/办公管理/综合管理
        repoPositionButton.setRepoIdList(Lists.newArrayList(2053345234437L));
        definedConfigJsonDTO.setRepoPositionButton(repoPositionButton);

        planNodeConfigReq1.setDefinedConfigJson(definedConfigJsonDTO);

        PlanNodeConfigReq planNodeConfigReq6 = new PlanNodeConfigReq();
        planNodeConfigReq6.setNodeId(2203794680325L);
        planNodeConfigReq6.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq6.setOrderNum(2);
        DefinedConfigJsonDTO definedConfigJsonDTO6 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq6.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq6.setDefinedConfigJson(definedConfigJsonDTO6);

        PlanNodeConfigReq planNodeConfigReq2 = new PlanNodeConfigReq();
        planNodeConfigReq2.setNodeId(2203794762501L);
        planNodeConfigReq2.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq2.setOrderNum(3);
        DefinedConfigJsonDTO definedConfigJsonDTO2 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq2.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq2.setDefinedConfigJson(definedConfigJsonDTO2);

        PlanNodeConfigReq planNodeConfigReq3 = new PlanNodeConfigReq();
        planNodeConfigReq3.setNodeId(2203794972677L);
        planNodeConfigReq3.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq3.setOrderNum(4);
        DefinedConfigJsonDTO definedConfigJsonDTO3 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq3.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq3.setDefinedConfigJson(definedConfigJsonDTO3);

        PlanNodeConfigReq planNodeConfigReq4 = new PlanNodeConfigReq();
        planNodeConfigReq4.setNodeId(2203795129093L);
        planNodeConfigReq4.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq4.setOrderNum(5);
        DefinedConfigJsonDTO definedConfigJsonDTO4 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq4.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq4.setDefinedConfigJson(definedConfigJsonDTO4);

        PlanNodeConfigReq planNodeConfigReq5 = new PlanNodeConfigReq();
        planNodeConfigReq5.setNodeId(2203795181061L);
        planNodeConfigReq5.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq5.setOrderNum(6);
        DefinedConfigJsonDTO definedConfigJsonDTO5 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq5.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq5.setDefinedConfigJson(definedConfigJsonDTO5);

        planNodeConfigReqList.add(planNodeConfigReq1);
        planNodeConfigReqList.add(planNodeConfigReq2);
        planNodeConfigReqList.add(planNodeConfigReq3);
        planNodeConfigReqList.add(planNodeConfigReq4);
        planNodeConfigReqList.add(planNodeConfigReq5);
        planNodeConfigReqList.add(planNodeConfigReq6);
//        planNodeReq.setPlanNodeConfigReqList(planNodeConfigReqList);
        standardPlanFacade.addPlanNode(planNodeReq);
    }


    /**
     * ASSEMBLY_PIPELINE_00("1,4,6,7,9", "文件上传->转换->OCR->AI->入库"),
     */
    @Test
    void addPlanNode01Test() {
        PlanNodeReq planNodeReq = new PlanNodeReq();
        PlanAddReq planAddReq = new PlanAddReq();
//        planAddReq.setName(ASSEMBLY_PIPELINE_UPLOAD_CONVERT_OCR_AI_STORAGE.getDesc());
        planAddReq.setRemark("WH入库方案二");
        planAddReq.setRecordTypeId(452996749317L);
//        planAddReq.setLastFlag(1);
        planNodeReq.setPlanAddReq(planAddReq);

        List<PlanNodeConfigReq> planNodeConfigReqList = Lists.newArrayList();
        PlanNodeConfigReq planNodeConfigReq1 = new PlanNodeConfigReq();
        planNodeConfigReq1.setNodeId(2203794586629L);
        planNodeConfigReq1.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq1.setOrderNum(1);
        Long nodeId = planNodeConfigReq1.getNodeId();
        Node node = nodeService.getById(nodeId);
        DefinedConfigJsonDTO definedConfigJsonDTO = JSON.parseObject(node.getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        RepoPositionButton repoPositionButton = definedConfigJsonDTO.getRepoPositionButton();
        //2053345234437 制度树/办公管理/综合管理
        repoPositionButton.setRepoIdList(Lists.newArrayList(2053345234437L));
        definedConfigJsonDTO.setRepoPositionButton(repoPositionButton);

        planNodeConfigReq1.setDefinedConfigJson(definedConfigJsonDTO);

        PlanNodeConfigReq planNodeConfigReq2 = new PlanNodeConfigReq();
        planNodeConfigReq2.setNodeId(2203794762501L);
        planNodeConfigReq2.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq2.setOrderNum(2);
        DefinedConfigJsonDTO definedConfigJsonDTO2 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq2.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq2.setDefinedConfigJson(definedConfigJsonDTO2);

        PlanNodeConfigReq planNodeConfigReq3 = new PlanNodeConfigReq();
        planNodeConfigReq3.setNodeId(2203794972677L);
        planNodeConfigReq3.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq3.setOrderNum(3);
        DefinedConfigJsonDTO definedConfigJsonDTO3 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq3.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq3.setDefinedConfigJson(definedConfigJsonDTO3);

        PlanNodeConfigReq planNodeConfigReq4 = new PlanNodeConfigReq();
        planNodeConfigReq4.setNodeId(2203795129093L);
        planNodeConfigReq4.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq4.setOrderNum(4);
        DefinedConfigJsonDTO definedConfigJsonDTO4 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq4.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq4.setDefinedConfigJson(definedConfigJsonDTO4);

//        PlanNodeConfigReq planNodeConfigReq6 = new PlanNodeConfigReq();
//        planNodeConfigReq6.setNodeId(2203795129093L);
//        planNodeConfigReq6.setStatus(NormalState.NORMAL.getCode());
//        planNodeConfigReq6.setOrderNum(4);
//        DefinedConfigJsonDTO definedConfigJsonDTO6 = JSON.parseObject(
//                nodeService.getById(planNodeConfigReq6.getNodeId()).getDefinedConfigJson(),
//                DefinedConfigJsonDTO.class);
//        planNodeConfigReq6.setDefinedConfigJson(definedConfigJsonDTO6);

        PlanNodeConfigReq planNodeConfigReq5 = new PlanNodeConfigReq();
        planNodeConfigReq5.setNodeId(2203795181061L);
        planNodeConfigReq5.setStatus(NormalState.NORMAL.getCode());
        planNodeConfigReq5.setOrderNum(5);
        DefinedConfigJsonDTO definedConfigJsonDTO5 = JSON.parseObject(
                nodeService.getById(planNodeConfigReq5.getNodeId()).getDefinedConfigJson(),
                DefinedConfigJsonDTO.class);
        planNodeConfigReq5.setDefinedConfigJson(definedConfigJsonDTO5);

        planNodeConfigReqList.add(planNodeConfigReq1);
        planNodeConfigReqList.add(planNodeConfigReq2);
        planNodeConfigReqList.add(planNodeConfigReq3);
        planNodeConfigReqList.add(planNodeConfigReq4);
        planNodeConfigReqList.add(planNodeConfigReq5);
//        planNodeConfigReqList.add(planNodeConfigReq6);
//        planNodeReq.setPlanNodeConfigReqList(planNodeConfigReqList);
        standardPlanFacade.addPlanNode(planNodeReq);
    }


}
