package com.suwell.plss.record.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.entity.RecordMetadataValue;
import com.suwell.plss.record.standard.dto.response.MetaDataValueInfoResp;
import java.util.List;
import java.util.Map;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/12/12
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class MetadataValueServiceTest {

    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private RecordMetadataValueHistoryService recordMetadataValueHistoryService;

    /**
     * 测试
     */
    @Test
    void saveRecordProcessDetailTest() {
        List<MetaDataValueInfoResp> dataValueInfoRespList = recordMetadataValueService.queryRecordTypeMetaDataValue(
                2959288648965L, 452996749317L);
        System.out.println(JSON.toJSONString(dataValueInfoRespList));
    }


    /**
     * 多数据源动态切换
     */
    @Test
    void transferQueryListTest() {
        List<Long> recordIdList = recordMetadataValueHistoryService.listQueryByRecordId(Long.MAX_VALUE, 20);
        List<RecordMetadataValue> valueQueryList = recordMetadataValueHistoryService.transferMetadataValueQueryList(
                recordIdList);
        System.err.println(valueQueryList.size());
        System.out.println(JSON.toJSONString(valueQueryList));
        recordMetadataValueService.saveDistributeShardingMetadataValue(valueQueryList);
    }

    /**
     * 删除
     */
    @Test
    void removeTest() {
        recordMetadataValueService.truncateShardingMetadataValueAll();
    }

    /**
     * 测试
     */
    @Test
    void page() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", 1);
        params.put("pageSize", 10);
//        List<MetadataValue> list = metadataValueService.list(Wrappers.<MetadataValue>lambdaQuery());

//        List<MetadataValue> list = metadataValueService.list(Wrappers.<MetadataValue>lambdaQuery()
//                .in(MetadataValue::getRecordId, Lists.newArrayList(3671064476165L)));
        /*
        List<MetadataValue> list = metadataValueService.list(Wrappers.<MetadataValue>lambdaQuery()
                .in(MetadataValue::getRecordId, Lists.newArrayList(3671064474118L, 3671064477958L)));
        System.out.println(JSON.toJSONString(list));
         */

//        // 必须带上分片键查询 【推荐】
//        List<MetadataValue> listOK = metadataValueService.list(Wrappers.<MetadataValue>lambdaQuery()
//                .eq(MetadataValue::getRecordId, 3671064476165L).last("limit 10"));
//        System.out.println(listOK);

        // 必须带上分片键查询【错误案例】
//        List<MetadataValue> list = metadataValueService.list(Wrappers.<MetadataValue>lambdaQuery().last("limit 10"));
//        System.out.println(JSON.toJSONString(list));
//        PageUtils pageUtils = metadataValueService.queryPage(params);
//        System.out.println(JSON.toJSONString(pageUtils));

    }

}
