package com.suwell.plss.record.service;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryQueryReq;
import com.suwell.plss.record.standard.dto.response.MetadataCategoryResp;
import com.suwell.plss.record.standard.service.StandardMetadataCategoryFacade;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class RecordServiceTest {

    @Resource
    private RecordService recordService;
    @Resource
    private StandardRecordTypeFacade recordTypeFacade;
    @Resource
    private StandardMetadataCategoryFacade standardMetadataCategoryFacade;
    @Test
    void queryUpUnderTest() {
////        RecordUpUnderDTO recordUpUnderDTO = recordService.queryUpUnder(1737929367557L);
//        System.out.println(JSON.toJSONString(recordUpUnderDTO));
    }


    @Test
    void getGroupTest() {
        MetadataCategoryQueryReq req = new MetadataCategoryQueryReq();
        req.setPage(1);
        req.setPageSize(999);
        req.setName("");
//        PageUtils<RecordTypeResp> page = recordTypeFacade.queryPage(req);
//        System.out.println(JSON.toJSONString(page));

        PageUtils<MetadataCategoryResp> page = standardMetadataCategoryFacade.queryPage(req);
        System.out.println(JSON.toJSONString(page));

    }
}
