package com.suwell.plss.record.service;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.convert.RecordMetadataValueConvertUtils;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordMetadataValue;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/6/18
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class RecordMetadataValueServiceTest {

    /**
     * 文件关联元数据值表
     */
    public static final String PG_TABLE_NAME_TEMPLATE_SQL = "CREATE TABLE \"plss-record\".\"TABLE_NAME_TEMPLATE\" (\n"
            + "  \"record_id\" int8 NOT NULL,\n"
            + "  \"metadata_value_json\" text NOT NULL,\n"
            + "  \"create_time\" timestamp(6) NOT NULL,\n"
            + "  \"modified_time\" timestamp(6) NOT NULL,\n"
            + "  CONSTRAINT \"TABLE_NAME_TEMPLATE_pkey\" PRIMARY KEY (\"record_id\")\n"
            + ")\n"
            + ";\n"
            + "\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"record_id\" IS '文件recordid';\n"
            + "\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"metadata_value_json\" IS '文件关联的元数数据json示例[{\"createTime\":\"2024-06-13 16:54:19.89\",\"dataType\":2,\"id\":2804032686853,\"mdId\":199417399627077,\"mdName\":\"标题\",\"mdValue\":\"概要设计\",\"modifiedTime\":\"2024-06-13 16:54:19.89\",\"recordId\":2804040896773}]\n"
            + "';\n"
            + "\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"create_time\" IS '创建时间';\n"
            + "\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"modified_time\" IS '修改时间';\n"
            + "\n"
            + "COMMENT ON TABLE \"plss-record\".\"TABLE_NAME_TEMPLATE\" IS '文件关联元数据值表';\n\n";
    public static final String DM_TABLE_NAME_TEMPLATE_SQL = "CREATE TABLE \"plss-record\".\"TABLE_NAME_TEMPLATE\"\n"
            + "(\n"
            + "\"record_id\" BIGINT NOT NULL,\n"
            + "\"metadata_value_json\" TEXT NOT NULL,\n"
            + "\"create_time\" TIMESTAMP(6) NOT NULL,\n"
            + "\"modified_time\" TIMESTAMP(6) NOT NULL,\n"
            + "NOT CLUSTER PRIMARY KEY(\"record_id\")) STORAGE(ON \"MAIN\", CLUSTERBTR) ;\n"
            + "\n"
            + "COMMENT ON TABLE \"plss-record\".\"TABLE_NAME_TEMPLATE\" IS '文件关联元数据值表';\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"create_time\" IS '创建时间';\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"metadata_value_json\" IS '文件关联的元数数据json示例[{\"createTime\":\"2024-06-13 16:54:19.89\",\"dataType\":2,\"id\":2804032686853,\"mdId\":199417399627077,\"mdName\":\"标题\",\"mdValue\":\"概要设计\",\"modifiedTime\":\"2024-06-13 16:54:19.89\",\"recordId\":2804040896773}]';\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"modified_time\" IS '修改时间';\n"
            + "COMMENT ON COLUMN \"plss-record\".\"TABLE_NAME_TEMPLATE\".\"record_id\" IS '文件recordid';\n\n";


    public static final String PG_TABLE_NAME_TEMPLATE_ALERT_SQL = "ALTER TABLE \"plss-record\".TABLE_NAME_TEMPLATE \n"
            + "  ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;\n"
            + "\n"
            + "COMMENT ON COLUMN \"plss-record\".TABLE_NAME_TEMPLATE.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';\n\n";

    public static final String DM_TABLE_NAME_TEMPLATE_ALERT_SQL ="alter table \"plss-record\".TABLE_NAME_TEMPLATE add column(doc_att_status INT default (2) not null );\n"
            + "\n"
            + "comment on column \"plss-record\".TABLE_NAME_TEMPLATE.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';\n\n";
    public static final String MYSQL_TABLE_NAME_TEMPLATE_ALERT_SQL ="ALTER TABLE `plss_record`.`TABLE_NAME_TEMPLATE` \n"
            + "ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;\n\n";

    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private RecordMetadataValueHistoryService recordMetadataValueHistoryService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private RecordService recordService;

    public static void main(String[] args) {
        // pg
        createShardingTable(PG_TABLE_NAME_TEMPLATE_ALERT_SQL, "rc_document_", "D:\\logs\\2\\rc_document_pg.sql");
        // dm
        createShardingTable(DM_TABLE_NAME_TEMPLATE_ALERT_SQL, "rc_document_", "D:\\logs\\2\\rc_document_dm.sql");
        // mysql
        createShardingTable(MYSQL_TABLE_NAME_TEMPLATE_ALERT_SQL, "rc_document_", "D:\\logs\\2\\rc_document_mysql.sql");
        System.err.println(RecordMetadataValueConvertUtils.getActualTableName(345979640522437L));
    }

    private static void createShardingTable(String tableNameTemplateSql, String tablePrefix, String sqlPath) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < 40; i++) {
            stringBuilder.append(tableNameTemplateSql.replaceAll("TABLE_NAME_TEMPLATE", tablePrefix + i));
        }
        FileUtil.writeUtf8String(stringBuilder.toString(), sqlPath);
    }

    @Test
    void getActualRecordMetadataValueTableNameTest() {
        recordMetadataValueService.truncateShardingMetadataValueAll();
    }

    @Test
    void truncateShardingMetadataValueAllTest() {
        System.err.println(RecordMetadataValueConvertUtils.getActualTableName(248915675541189L));
    }

    @Test
    void addShardingMetadataValueTest() {
        for (int i = 100; i <= 101; i++) {
            Long recordId = wfgIdGenerator.next();
            System.err.println(RecordMetadataValueConvertUtils.getActualTableName(recordId));
            List<MetadataValueDTO> metadataValueDTOList = Lists.newArrayList();
            MetadataValueDTO metadataValueDTO = new MetadataValueDTO();
            metadataValueDTO.setId(wfgIdGenerator.next());
            metadataValueDTO.setRecordId(recordId);
            metadataValueDTO.setMdId(4570067600133L);
            metadataValueDTO.setMdName("标题");
            metadataValueDTO.setMdValue("12");
            metadataValueDTO.setCreateTime(new Date());
            metadataValueDTO.setModifiedTime(new Date());
            metadataValueDTO.setDataType(1);
            metadataValueDTOList.add(metadataValueDTO);
            recordMetadataValueService.incrementUpdateRecordMetadataValue(recordId, metadataValueDTOList);
        }
    }

    /**
     * 测试
     */
    @Test
    void countTest() {
        long count = recordMetadataValueService.count();
        System.out.println(count);
    }

    /**
     * MetadataValue 表抽取数据
     */
    @Test
    void listMetadataValueTest() {
        List<MetadataValueDTO> metadataValueDTOS = recordMetadataValueHistoryService.listQueryByMetadataValueRecordId(
                Lists.newArrayList(4624305170183L, 4624307055623L));
        System.out.println(metadataValueDTOS);
    }

    /**
     * MetadataValue 表抽取数据
     */
    @Test
    void listMetadataValueTest2() {
        List<MetadataValueDTO> metadataValueDTOS = recordMetadataValueHistoryService.listQueryPageByRecordId(
                Long.MAX_VALUE, 5);
        System.out.println(metadataValueDTOS);
    }

    @Test
    void getById() {
//        System.out.println(JSON.toJSONString(recordMetadataValueService.getById(4624307055623L)));
        Long recordId = 393953417660997L;
        Record oneById = recordService.getOneById(recordId);
        List<MetadataValueDTO> metadataValueDTOS = recordMetadataValueService.queryByRecordId(recordId);
        System.out.println(JSON.toJSONString(metadataValueDTOS));
        List<TypeMetadataRuleResp> typeMetadataRule = recordTypeMetadataService.queryTypeMetadataRule(oneById.getRecordtypeId());
        System.out.println(JSON.toJSONString(typeMetadataRule));

//        Long recordId = 195061050279429L;
////        List<MetadataValueDTO> metadataValueDTOS = recordMetadataValueService.queryByRecordId(recordId);
////        System.out.println(JSON.toJSONString(metadataValueDTOS));
//        RecordTypeMetadata one = recordTypeMetadataService.getOne(
//                Wrappers.<RecordTypeMetadata>query().lambda().eq(RecordTypeMetadata::getRecordtypeId, recordId));
//
//        System.out.println(JSON.toJSONString(one));
    }

    /**
     * 测试
     */
    @Test
    void listTest() {
//        List<RecordMetadataValue> list = recordMetadataValueService.list();
//        System.out.println(list);

        // 不支持
//        RecordMetadataValuePagReq req = new RecordMetadataValuePagReq();
//        req.setPage(1);
//        req.setPageSize(5);
//        PageUtils<RecordMetadataValue> recordMetadataValuePageUtils = recordMetadataValueService.queryPage(req);
//        System.out.println(JSON.toJSONString(recordMetadataValuePageUtils));

        // namespace = sharding-pg
        List<RecordMetadataValue> metadataValueList = recordMetadataValueService.list(
                Wrappers.<RecordMetadataValue>query().lambda()
                        .lt(RecordMetadataValue::getRecordId, Long.MAX_VALUE)
                        .orderByDesc(RecordMetadataValue::getRecordId)
                        .last(" LIMIT " + 5));
        System.out.println(JSON.toJSONString(metadataValueList));
    }

}
