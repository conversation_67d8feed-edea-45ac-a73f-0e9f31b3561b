package com.suwell.plss.record.facade;

import com.google.common.collect.Lists;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.FolderAddReq;
import com.suwell.plss.record.standard.dto.request.FolderEditReq;
import com.suwell.plss.record.standard.dto.request.FolderMoveReq;
import com.suwell.plss.record.standard.dto.response.FolderResp;
import com.suwell.plss.record.standard.service.StandardFolderFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 目录相关单测
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardFolderFacadeTest {

    @Resource
    private StandardFolderFacade folderFacade;

    @Test
    public void addFolder() {
        FolderAddReq addReq = new FolderAddReq();
        addReq.setParentId(237760204549L);
        addReq.setFolderName("哈哈哈");
        addReq.setImageUrl("aaaaaaaaaaaaaaaaaaaaa");
        addReq.setOrgId("0");
        System.out.println(addReq);
        folderFacade.addFolder(addReq);
    }

    @Test
    public void getById() {
        FolderResp byId = folderFacade.queryById(237760204549L);
        System.out.println(byId);
    }

    @Test
    public void removeFolders() {
        folderFacade.removeFolders(Lists.newArrayList(1L));
    }

    @Test
    public void editFolder() {
        FolderEditReq editReq = new FolderEditReq();
        editReq.setFolderId(237760204549L);
        editReq.setFolderName("ddddddddd");
        folderFacade.editFolder(editReq);
    }

    @Test
    public void queryFolders() {
//        FolderQueryReq queryReq = new FolderQueryReq();
//        queryReq.setPage(1L);
//        queryReq.setPageSize(10L);
//        queryReq.setFolderName("folder");
//        List<FolderResp> list = folderFacade.queryFolders(queryReq);
//        System.out.println(JSON.toJSONString(list));
    }


    @Test
    public void moveFolders() {
        Long targetFolderId = 237312476165L;
        List<Long> fromFolderIds = Lists.newArrayList(237775826693L);
        FolderMoveReq moveReq = new FolderMoveReq();
        moveReq.setFromFolderIds(fromFolderIds);
        moveReq.setTargetId(targetFolderId);
        folderFacade.moveFolders(moveReq);
    }

}
