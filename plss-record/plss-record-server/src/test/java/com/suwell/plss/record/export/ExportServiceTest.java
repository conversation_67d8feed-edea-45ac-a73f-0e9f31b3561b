package com.suwell.plss.record.export;

import com.suwell.plss.record.migration.DataImmigrateService;
import jakarta.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.suwell.plss.record.PlssRecordApplication;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class ExportServiceTest {
    @Resource
    private DataImmigrateService dataImmigrateService;

    @Test
    void exportDataTest() {

    }

    @Test
    void importDataTest() {
        dataImmigrateService.importTask("/Users/<USER>/app/", "test", 1, false, false);
    }
}
