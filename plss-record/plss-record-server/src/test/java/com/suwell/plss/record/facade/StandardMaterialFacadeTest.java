package com.suwell.plss.record.facade;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.PlssRecordApplication;
import com.suwell.plss.record.standard.dto.request.MaterialQueryReq;
import com.suwell.plss.record.standard.dto.response.MaterialCategoryInfoResp;
import com.suwell.plss.record.standard.service.StandardMaterialFacade;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2023/11/27
 */
@Slf4j
@SpringBootTest(classes = PlssRecordApplication.class)
public class StandardMaterialFacadeTest {

    @Resource
    private StandardMaterialFacade standardMaterialFacade;

    /**
     * 测试入库流程方案
     *
     * @throws Exception
     */
    @Test
    void queryPageTest() {
        MaterialQueryReq materialQueryReq = new MaterialQueryReq();
        materialQueryReq.setPage(1);
        materialQueryReq.setPageSize(10);
        PageUtils<MaterialCategoryInfoResp> pageUtils = standardMaterialFacade.queryPage(
                materialQueryReq);
        System.out.println(JSON.toJSONString(pageUtils));
    }
}
