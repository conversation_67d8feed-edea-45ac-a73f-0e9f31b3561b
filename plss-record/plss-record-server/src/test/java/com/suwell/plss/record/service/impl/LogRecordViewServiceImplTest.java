package com.suwell.plss.record.service.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.record.entity.LogRecordView;
import com.suwell.plss.record.service.LogRecordViewService;
import com.suwell.plss.record.standard.dto.request.RecordLogReq;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import com.suwell.plss.record.standard.service.StandardRecordLogFacade;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * <AUTHOR>
 * @Date 2023/11/25
 */

@SpringBootTest
@Slf4j
class LogRecordViewServiceImplTest {

    @Autowired
    StandardRecordLogFacade standardRecordLogFacade;

    @Autowired
    LogRecordViewService logRecordViewService;

    @Autowired
    WFGIdGenerator wfgIdGenerator;


    @Test
    void addData() {
        LogRecordView logRecordView = new LogRecordView();
        logRecordView.setId(wfgIdGenerator.next());
        logRecordView.setRecordId(2026781730821L);
        logRecordView.setRecordName("关于转发中组部《党政领导干部选拔任用工作有关事项报告办法（试行）》的通知.doc");
        logRecordView.setUserId("1");
        logRecordView.setUserName("testname");
        logRecordView.setViewIp("ip");
        logRecordView.setViewUseragent("useragent");
        logRecordView.setViewOs("os");
        logRecordView.setViewBrowser("wBrowser");
        logRecordView.setCreateTime(new Date());

        logRecordViewService.save(logRecordView);

        logRecordView.setId(wfgIdGenerator.next());
        logRecordView.setRecordId(2026781730821L);
        logRecordView.setRecordName("关于转发中组部《党政领导干部选拔任用工作有关事项报告办法（试行）》的通知.doc");
        logRecordView.setUserId("2");
        logRecordView.setUserName("testname");
        logRecordView.setViewIp("ip");
        logRecordView.setViewUseragent("useragent");
        logRecordView.setViewOs("os");
        logRecordView.setViewBrowser("wBrowser");
        logRecordView.setCreateTime(new Date());

        logRecordViewService.save(logRecordView);
    }

    @Test
    void queryRecordViewLog() {

        RecordLogReq recordLogReq = new RecordLogReq();
        recordLogReq.setUserId("1");
        recordLogReq.setPage(1);
        recordLogReq.setPageSize(10);
        Page<RecordLogResp> recordLogRespPageUtils = standardRecordLogFacade.queryRecordViewLog(recordLogReq);
        System.out.println(JSON.toJSONString(recordLogRespPageUtils));

        RecordLogReq recordLogReq2 = new RecordLogReq();
        recordLogReq2.setRecordId(2026781730821L);
        recordLogReq2.setPage(1);
        recordLogReq2.setPageSize(10);
        Page<RecordLogResp> recordLogRespPageUtils2 = standardRecordLogFacade.queryRecordViewLog(recordLogReq2);
        assertTrue(recordLogRespPageUtils2.getTotal() == 2);
    }


}