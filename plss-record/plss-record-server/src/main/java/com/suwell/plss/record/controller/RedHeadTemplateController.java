package com.suwell.plss.record.controller;


import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.entity.RedHeaderTemplate;
import com.suwell.plss.record.service.RedHeadTemplateService;
import com.suwell.plss.record.standard.dto.request.RedHeadTemplateDeleteReq;
import com.suwell.plss.record.standard.dto.request.RedHeadTemplateRenameReq;
import com.suwell.plss.record.standard.dto.request.RedHeadTemplateReq;
import com.suwell.plss.record.standard.dto.request.RedHeadTemplateUploadReq;
import com.suwell.plss.record.standard.dto.response.RedHeadTemplateResp;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;

/**
 * 红头模版控制层
 * <AUTHOR> <br/>
 * @date 2024/6/26 <br/>
 * &#064;Copyright  博客：<a href="https://eliauku.gitee.io/">...</a>  ||  per aspera and astra <br/>
 */
@RestController
@RequestMapping("/redTemplate")
@RequiredArgsConstructor
public class RedHeadTemplateController {

    private final RedHeadTemplateService redHeadTemplateService;

    /**
     * 获取红头模版列表
     */
    @PostMapping("/list")
    public R<PageUtils<RedHeadTemplateResp>> list(@RequestBody RedHeadTemplateReq req) {

        return R.ok(redHeadTemplateService.list(req));

    }

    /**
     * 新增保存套红模版
     */
    @PostMapping("/save")
    public R<Void> save(@Valid @ModelAttribute RedHeadTemplateUploadReq redHeadTemplateUploadReq) {

        return redHeadTemplateService.save(redHeadTemplateUploadReq);
    }

    /**
     * 后台套红模版上传
     */
    @PostMapping("/backgroundSave")
    public R<Void> backgroundSave(@Valid @ModelAttribute RedHeadTemplateUploadReq redHeadTemplateUploadReq) {

        return redHeadTemplateService.backgroundSave(redHeadTemplateUploadReq);
    }

    /**
     * 后台套红模版重命名
     */
    @PostMapping("/rename")
    public R<Void> rename(@RequestBody @Valid RedHeadTemplateRenameReq redHeadTemplateRenameReq) {
        return redHeadTemplateService.rename(redHeadTemplateRenameReq);
    }

    /**
     * 重新上传文件
     */
    @PostMapping("/upload")
    public R<Void> upload(@RequestParam("id") String templateId, @RequestParam("templateFile") MultipartFile file) {
        return redHeadTemplateService.upload(templateId, file);
    }

    /**
     * 删除套红模版
     */
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody @Valid RedHeadTemplateDeleteReq redHeadTemplateDeleteReq) {

        return redHeadTemplateService.delete( redHeadTemplateDeleteReq);
    }
}
