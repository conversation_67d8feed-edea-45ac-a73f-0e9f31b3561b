package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.standard.dto.request.BatchUploadFileToPerReq;
import com.suwell.plss.record.standard.dto.response.UploadPersonalResp;
import com.suwell.plss.record.standard.service.StandardDocumentFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * ==========================
 * 开发：wei.wang
 * 创建时间：2023-12-12 17:30
 * 版本：1.0
 * 描述：
 * ==========================
 */
@Slf4j
@RestController
@RequestMapping("/v1/document/personal")
public class DocumentPersonalController {

    @Resource
    private StandardDocumentFacade standardDocumentFacade;

    @PostMapping("/uploadFile")
    public R<List<UploadPersonalResp>> uploadFile(BatchUploadFileToPerReq req) {
        req.setOrigin("personal");
        req.setFileSource(1);
        req.setUserId(SecurityUtils.getUserId());
        if (req.getFiles() == null) {
            return R.error("files is null");
        }
        if(CollectionUtils.isEmpty(req.getFolderIds()) && StringUtils.isEmpty(req.getPath())){
            return R.error("上传地址不能为空");
        }
        List<UploadPersonalResp> records = standardDocumentFacade.batchUploadFileToPersonal(req);
        return R.ok(records);
    }

}
