package com.suwell.plss.record.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.record.entity.KnowledgeAttribute;
import com.suwell.plss.record.entity.KnowledgeRelation;
import com.suwell.plss.record.entity.KnowledgeRelationAttribute;
import com.suwell.plss.record.mapper.KnowledgeRelationMapper;
import com.suwell.plss.record.service.KnowledgeAttributeService;
import com.suwell.plss.record.service.KnowledgeRelationAttributeService;
import com.suwell.plss.record.service.KnowledgeRelationService;
import com.suwell.plss.record.standard.dto.request.KnowledgeRelationReq;
import com.suwell.plss.record.standard.dto.response.KnowledgeAttributeResp;
import com.suwell.plss.record.standard.dto.response.KnowledgeRelationResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/06/12
 * @content
 */
@Slf4j
@Service
public class KnowledgeRelationServiceImpl extends ServiceImpl<KnowledgeRelationMapper, KnowledgeRelation> implements KnowledgeRelationService {

    @Resource
    private KnowledgeRelationMapper relationMapper;

    @Resource
    private KnowledgeRelationAttributeService relationAttributeService;

    @Resource
    private KnowledgeAttributeService attributeService;

    @Override
    public List<KnowledgeRelationResp> queryRelationList(KnowledgeRelationReq req) {
        List<KnowledgeRelationResp> relationRespList = relationMapper.queryRelationList(req);
        if(CollectionUtils.isEmpty(relationRespList)){
            return Collections.emptyList();
        }
        List<Long> relationIdList = relationRespList.stream().map(KnowledgeRelationResp::getId).collect(Collectors.toList());
        List<KnowledgeRelationAttribute> relationAttributes = relationAttributeService.list(new LambdaQueryWrapper<KnowledgeRelationAttribute>()
                .in(KnowledgeRelationAttribute::getRelationId, relationIdList));
        Map<Long, List<KnowledgeRelationAttribute>> relationAttributeMap = relationAttributes.stream()
                .collect(Collectors.groupingBy(KnowledgeRelationAttribute::getRelationId));
        List<Long> attributeIds = relationAttributes.stream().map(KnowledgeRelationAttribute::getAttributeId).collect(Collectors.toList());
        List<KnowledgeAttribute> knowledgeAttributes = new ArrayList<>();
        if(!CollectionUtils.isEmpty(attributeIds)){
            knowledgeAttributes = attributeService.listByIds(attributeIds);
        }
        Map<Long, KnowledgeAttribute> attributeMap = knowledgeAttributes.stream().collect(Collectors.toMap(KnowledgeAttribute::getId, Function.identity()));
        for (KnowledgeRelationResp relationResp : relationRespList) {
            List<KnowledgeRelationAttribute> attributes = relationAttributeMap.get(relationResp.getId());
            if(CollectionUtils.isEmpty(attributes)){
                continue;
            }
            relationResp.setAttributeList(attributes.stream().map(attribute -> {
                KnowledgeAttribute knowledgeAttribute = attributeMap.get(attribute.getAttributeId());
                if(knowledgeAttribute == null){
                    return null;
                }
                KnowledgeAttributeResp attributeResp = new KnowledgeAttributeResp();
                attributeResp.setId(knowledgeAttribute.getId());
                attributeResp.setName(knowledgeAttribute.getName());
                attributeResp.setDescription(knowledgeAttribute.getDescription());
                attributeResp.setValueType(knowledgeAttribute.getValueType());
                return attributeResp;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return relationRespList;
    }
}
