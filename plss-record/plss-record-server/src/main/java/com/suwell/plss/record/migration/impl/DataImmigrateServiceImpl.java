package com.suwell.plss.record.migration.impl;

import static com.suwell.plss.record.enums.RecordRedisKeyEnum.DATA_IMPORT_LOAD_TASK;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.DATA_IMPORT_LOAD_TASK_HEALTH;
import static com.suwell.plss.record.standard.enums.RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
import static com.suwell.plss.record.standard.enums.RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_NORMAL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.suwell.plss.document.process.service.DocumentProcessRpcService;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.conf.OpenWpsCheckConfig;
import com.suwell.plss.record.domain.RecordReviewDTO;
import com.suwell.plss.record.entity.RecordReview;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.DataImmigrateService;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.service.RecordReviewService;
import com.suwell.plss.record.standard.enums.RecordEnum.DataLoadTaskOverReason;
import com.suwell.plss.record.standard.service.DataLoadTaskFacade;
import com.suwell.plss.record.standard.service.ForbiddenSearchinfoFacade;
import jakarta.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.logging.FileHandler;
import java.util.logging.Handler;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataImmigrateServiceImpl implements DataImmigrateService {

    private final Logger logger = Logger.getLogger(this.getClass().getName());
    private final int MAX_THREADS = 32;

    @Resource
    private DocumentMigrateServiceImpl documentMigrateService;
    @Resource
    private KnowledgeMigrateServiceImpl knowledgeMigrateService;
    @Resource
    private RecordMigrateServiceImpl recordMigrateService;
    @Resource
    private FolderMigrateServiceImpl folderMigrateService;

    @Resource
    private DocumentProcessRpcService documentProcessRpcService;
    @Resource
    private RedisService redisService;
    @Resource
    private DataLoadTaskFacade dataLoadTaskFacade;
    @Resource
    private OpenWpsCheckConfig openWpsCheckConfig;
    @Resource
    private RecordReviewService reviewService;
    @Resource
    private ForbiddenSearchinfoFacade forbiddenService;

    @Override
    public boolean stopImport(String dirname) {
        List<String> list = redisService.get(DATA_IMPORT_LOAD_TASK.getKey(), List.class, String.class);
        return !StringUtils.isEmpty(dirname) && !list.contains(dirname);
    }

    @Override
    public void heatHeart(Long taskId) {
        if (!redisService.hasKey(DATA_IMPORT_LOAD_TASK_HEALTH.getKey() + taskId)) {
            redisService.setEx(DATA_IMPORT_LOAD_TASK_HEALTH.getKey() + taskId,
                    taskId + StringPool.EMPTY, 5, TimeUnit.MINUTES);
        } else {
            redisService.expire(DATA_IMPORT_LOAD_TASK_HEALTH.getKey() + taskId, 5, TimeUnit.MINUTES);
        }
    }

    @Override
    public void importTask(String prefixPath, String dirname, long taskId, boolean overwrite, boolean needDelFile) {
        DataLoadTaskOverReason ret = DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
        JSONObject mapNum = new JSONObject();
        try {
            dataLoadTaskFacade.modifiedStart(taskId);
            ret = this.importDataThread(prefixPath, dirname, taskId, overwrite);
        } catch (Exception ex) {
            logger.log(Level.WARNING, ex.getMessage(), ex);
            log.error(ex.getMessage(), ex);
        } finally {
            dataLoadTaskFacade.modifiedStop(taskId, ret, mapNum.toJSONString());
            if (needDelFile) {
                for (Handler handler : logger.getHandlers()) {
                    handler.close();
                }
                FileUtil.del(Paths.get(prefixPath, dirname).toString());
            }
            dataLoadTaskFacade.nextStart(prefixPath, overwrite);
        }
    }


    @SuppressWarnings("rawtypes")
    private DataLoadTaskOverReason importDataThread(String prefixPath, String dirname, Long taskId, boolean overwrite) {
        try {
            Path dirPath = Paths.get(prefixPath, dirname);
            if (!Files.exists(dirPath)) {
                logger.warning("import path not exists");
                return DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
            }

            FileHandler fileHandler = new FileHandler(dirPath + "/import.log");
            fileHandler.setFormatter(new SimpleFormatter());
            logger.addHandler(fileHandler);

            File[] files = FileUtils.getFile(dirPath.toString()).listFiles();
            ExecutorService finalExecutors = TtlExecutors.getTtlExecutorService(
                    Executors.newFixedThreadPool(MAX_THREADS));
            List<CompletableFuture<Long>> list = Arrays.stream(files).map(file -> CompletableFuture.supplyAsync(() -> {
                if (file.isDirectory()) {
                    return null;
                }
                this.heatHeart(taskId);
                String filename = file.getName();
                String extName = "." + FileNameUtil.extName(filename);
                // es数据
                importEsRecord(extName, file.getAbsolutePath(), logger);
                // db数据
                recordMigrateService.importList(extName, file.getAbsolutePath(), overwrite, logger);
                documentMigrateService.importList(extName, file.getAbsolutePath(), overwrite, logger);
                folderMigrateService.importList(extName, file.getAbsolutePath(), overwrite, logger);
                knowledgeMigrateService.importList(extName, file.getAbsolutePath(), overwrite, logger);

                if (extName.equals(ExportConstant.SUFFIX_ES_VECTOR_RECORD)) {
                    // .esx文件名就是recordId
                    String mainName = FileNameUtil.mainName(filename);
                    return Long.parseLong(mainName);
                } else {
                    return null;
                }
            }, finalExecutors)).toList();
            CompletableFuture[] array = list.toArray(new CompletableFuture[0]);
            CompletableFuture.allOf(array).join();
            if (openWpsCheckConfig.isWithDataImportAudit()) {
                List<Long> recordIds = list.stream().map(CompletableFuture::join)
                        .filter(Objects::nonNull).toList();
                importRecordAfter(recordIds);
            }
            return DATA_LOAD_TASK_OVER_REASON_NORMAL;

        } catch (Exception ex) {
            logger.info(ex.getMessage());
            return DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
        }
    }

    public void importRecordAfter(List<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        for (Long recordId : recordIds) {
            RecordReviewDTO record = reviewService.getRecordReviewByRecordId(recordId);
            RecordReview recordReview = record.toEntity();
            // 标记为已经完成入库
            recordReview.setStatus(3);
            reviewService.updateRecordReview(recordReview);
            // 删除审核不通过的record
            if (recordReview.getReviewStatus() == 3) {
                forbiddenService.deleteRecord(recordId, true);
            }
        }
    }

    private void importEsRecord(String extName, String filepath, Logger logger) {
        switch (extName) {
            case ExportConstant.SUFFIX_ES_RECORD:
                List<Map<String, Object>> nlpRecordList = CommonUtil.deserializeContentToMap(filepath);
                if (nlpRecordList.isEmpty()) {
                    return;
                }
                try {
                    // 伴写审核数据过滤
                    reviewRecordFilter(nlpRecordList);
                    if (CollUtil.isEmpty(nlpRecordList)) {
                        return;
                    }
                    // 导入es数据
                    documentProcessRpcService.impEsEntity(nlpRecordList, ExportConstant.NLP_RECORD);
                    CommonUtil.renameFile(filepath);
                } catch (Exception ex) {
                    logger.info("import nlp_record error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
                }
                break;
            case ExportConstant.SUFFIX_ES_VECTOR_RECORD:
                List<Map<String, Object>> list = CommonUtil.deserializeContentToMap(filepath);
                if (list.isEmpty()) {
                    return;
                }
                try {
                    // 伴写审核数据过滤
                    reviewRecordFilter(list);
                    if (CollUtil.isEmpty(list)) {
                        return;
                    }
                    // 导入vector的数据
                    documentProcessRpcService.impEsEntity(list, ExportConstant.NLP_RECORD_VECTOR);
                    CommonUtil.renameFile(filepath);
                } catch (Exception ex) {
                    logger.info(
                            "import nlp_record_vector error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
                }
                break;
        }
    }

    private void reviewRecordFilter(List<Map<String, Object>> list) {
        List<Long> recordIds = list.stream().map(m -> m.get("recordId"))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(Long::parseLong)
                .distinct().toList();
        List<Long> longs = reviewService.queryRecordReviewFilterByRecordIds(recordIds);
        if (CollUtil.isEmpty(longs)) {
            return;
        }
        list.removeIf(m -> {
            Long recordId = (Long) m.get("recordId");
            return recordId != null && longs.contains(recordId);
        });
    }
}
