package com.suwell.plss.record.thirdparty;

import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;

/**
 * 获取正文
 *
 * <AUTHOR>
 * @date 2023/9/18
 */
public interface ThirdPartyOcrTextApi {


    /**
     * 获取正文
     *
     * @param mqRecordReqDTO 文件消息体
     */
    void getOcrText(MqRecordReqDTO mqRecordReqDTO);

    /**
     * 获取正文,不依赖ocr和转换服务 提取纯文字的word文档
     *
     * @param mqRecordReqDTO 文件消息体
     */
    void getOcrTextDoc(MqRecordReqDTO mqRecordReqDTO);


    /**
     * 获取文件元数据
     *
     * @param mqRecordReqDTO 文件消息体
     */
    void getOcrMetadata(MqRecordReqDTO mqRecordReqDTO);

}
