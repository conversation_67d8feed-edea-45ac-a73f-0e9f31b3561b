package com.suwell.plss.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.suwell.plss.record.standard.enums.MaterialCategoryEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * 素材与分类关系
 *
 * <AUTHOR>
 * @date 2023/11/25
 */
@Data
@TableName("rc_material_category")
public class MaterialCategory implements Serializable {

    private static final long serialVersionUID = -3365510210427465610L;

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 分类id
     */
    private Long categoryId;
    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类类型
     * 1 分类标签；2 素材标签
     *@see MaterialCategoryEnum.CategoryTypeEnum
     */
    private Integer categoryType;


}
