package com.suwell.plss.record.controller;

import static com.suwell.plss.framework.common.enums.CommonRedisKeyEnum.DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK;
import static com.suwell.plss.framework.common.enums.CommonRedisKeyEnum.DP_AI_KNOWLEDGE_RESULT_EXTRACT_MQ_REPEAT_CHECK;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.redis.utils.SnowflakeIdToBitmapOffsetUtils;
import com.suwell.plss.record.domain.BitMapDTO;
import com.suwell.plss.record.util.RecordCommonUtils;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排查问题操作
 * <AUTHOR>
 * @date 2025/4/29
 */
@RefreshScope
@Slf4j
@RestController
@RequestMapping("/v1/at/operation")
public class AtOperationController {
    @Resource
    private RedisService redisService;
    @Resource
    private RecordCommonUtils recordCommonUtils;
    @PostMapping("/bitMap")
    public R<Object> bitMap(@RequestBody  @Validated BitMapDTO bitMapDTO) {
        log.info("清除bitMap的req:{}", JSON.toJSONString(bitMapDTO));
        if (Integer.valueOf(1).equals(bitMapDTO.getCleanType())) {
            if (CollUtil.isNotEmpty(bitMapDTO.getDocIdList())) {
                bitMapDTO.getDocIdList().forEach(x->{
                    String[] toBitmap = SnowflakeIdToBitmapOffsetUtils.convertBizPkToBitmap(
                            DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK.getKey(), x);
                    redisService.setBit(toBitmap[0], Long.parseLong(toBitmap[1]), false);
                });
            }
            if (CollUtil.isNotEmpty(bitMapDTO.getRecordIdList())) {
                bitMapDTO.getRecordIdList().forEach(x->{
                    String[] toBitmap = SnowflakeIdToBitmapOffsetUtils.convertBizPkToBitmap(
                            DP_AI_KNOWLEDGE_RESULT_EXTRACT_MQ_REPEAT_CHECK.getKey(), x);
                    redisService.setBit(toBitmap[0], Long.parseLong(toBitmap[1]), false);
                });
            }
        }

        // 清除 docId全部
        if (Integer.valueOf(2).equals(bitMapDTO.getCleanType())) {
            for (int i = 0; i < 1024; i++) {
                redisService.delete(DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK.getKey() + i);
            }
        }
        // 清除 recordId 全部
        if (Integer.valueOf(3).equals(bitMapDTO.getCleanType())) {
            for (int i = 0; i < 1024; i++) {
                redisService.delete(DP_AI_KNOWLEDGE_RESULT_EXTRACT_MQ_REPEAT_CHECK.getKey() + i);
            }
        }

        // 清除 docId 和 recordId 全部
        if (Integer.valueOf(4).equals(bitMapDTO.getCleanType())) {
            for (int i = 0; i < 1024; i++) {
                redisService.delete(DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK.getKey() + i);
                redisService.delete(DP_AI_KNOWLEDGE_RESULT_EXTRACT_MQ_REPEAT_CHECK.getKey() + i);
            }
        }
        return R.ok(bitMapDTO.getCleanType());
    }

    /**
     *
     * @return
     */
    @PostMapping("/getBitMapValue")
    public R<Map<String,Object>> getRecordBitMap(@RequestBody @Validated BitMapDTO bitMapDTO) {
        Map<String,Object> map = Maps.newHashMap();
        if (Integer.valueOf(2).equals(bitMapDTO.getCleanType())) {
            if (CollUtil.isNotEmpty(bitMapDTO.getDocIdList())) {
                List<Boolean> docBitMapValue = Lists.newArrayList();
                for (Long docId : bitMapDTO.getDocIdList()) {
                    docBitMapValue.add(recordCommonUtils.getMarkMqData(docId,
                            DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK.getKey()));
                }
                map.put("docBitMapValueList", docBitMapValue);
            }
        }

        if (Integer.valueOf(3).equals(bitMapDTO.getCleanType())) {
            if (CollUtil.isNotEmpty(bitMapDTO.getRecordIdList())) {
                List<Boolean> recordBitMapValue = Lists.newArrayList();
                for (Long recordId : bitMapDTO.getRecordIdList()) {
                    recordBitMapValue.add(recordCommonUtils.getMarkMqData(recordId,
                            DP_AI_KNOWLEDGE_RESULT_EXTRACT_MQ_REPEAT_CHECK.getKey()));
                }
                map.put("recordBitMapValueList", recordBitMapValue);
            }
        }
        return R.ok(map);
    }
}
