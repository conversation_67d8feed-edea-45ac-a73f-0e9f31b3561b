package com.suwell.plss.record.conf;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.record.standard.domain.DataSubscribeCustomInfo;
import com.suwell.plss.record.standard.domain.DataSubscribeInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@RefreshScope
@Configuration
@Slf4j
public class DataDeliveryConf implements InitializingBean {
    @Value("${plss.data.publish.info:}")
    private String publishInfoStr;

    @Value("${plss.data.subscribe.info:}")
    private String subscribeInfoStr;

    @Value("${plss.data.subscribe.customInfo:}")
    private String subscribeCustomInfoStr;

    @Getter
    private List<DataPublishInfo> publishInfoList;

    @Getter
    @Setter
    private DataSubscribeInfo subscribeInfo;

    @Getter
    private DataSubscribeCustomInfo subscribeCustomInfo;


    @Override
    public void afterPropertiesSet() {
        try {
            List<DataPublishInfo> publishInfoList = new ArrayList<>();
            if (StringUtils.isNotEmpty(publishInfoStr)) {
                List<DataPublishInfo> configInfoList = JSON.parseArray(publishInfoStr, DataPublishInfo.class);
                if (Objects.nonNull(configInfoList)) {
                    publishInfoList = configInfoList;
                }
            }
            for (DataPublishInfo dataPublishConfigInfo : publishInfoList) {
                dataPublishConfigInfo.validate();
            }
            this.publishInfoList = publishInfoList;


            //订阅配置
            DataSubscribeInfo info = new DataSubscribeInfo();
            info.init();
            if (StringUtils.isNotEmpty(subscribeInfoStr)) {
                DataSubscribeInfo configInfo = JSONObject.parseObject(subscribeInfoStr, DataSubscribeInfo.class);
                if (Objects.nonNull(configInfo)) {
                    info = configInfo;
                }
            }
            this.subscribeInfo = info;

            //订阅定制配置
            this.subscribeCustomInfo = new DataSubscribeCustomInfo();
            if (StringUtils.isNotEmpty(subscribeCustomInfoStr)) {
                this.subscribeCustomInfo = JSONObject.parseObject(subscribeCustomInfoStr, DataSubscribeCustomInfo.class);
            }
        }catch (Exception e){
            log.error("加载数据推送和订阅配置异常",e);
        }
    }
}
