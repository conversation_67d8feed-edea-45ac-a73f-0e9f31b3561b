package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.RecordMakeBachTagReq;
import com.suwell.plss.record.standard.dto.request.RecordRemoveBachTagReq;
import com.suwell.plss.record.standard.dto.response.RecordMakeBachTagResp;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件打标签管理
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@Slf4j
@RestController
@RequestMapping("/v1/recordOperateTag")
public class RecordTagController {

    @Resource
    private StandardRecordFacade recordFacade;

    /**
     * 文件批量打标签
     */
    @PostMapping("/recordBatchMakeTag")
    public R<RecordMakeBachTagResp> recordBatchMakeTag(@RequestBody @Validated RecordMakeBachTagReq req) {
        return R.ok(recordFacade.modifyRecordBatchMakeTag(req));
    }

    /**
     * 移除标签
     */
    @PostMapping("/recordBatchRemoveTag")
    public R<Void> recordBatchRemoveTag(@RequestBody RecordRemoveBachTagReq req) {
        recordFacade.modifyRecordBatchRemoveTag(req);
        return R.ok();
    }
}
