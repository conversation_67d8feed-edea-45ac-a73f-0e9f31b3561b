package com.suwell.plss.record.migration;

import com.suwell.plss.document.process.dto.request.ExportRecordReq;
import com.suwell.plss.record.standard.dto.request.ExportFilterReq;
import java.util.Map;
public interface DataEmigrateService {
    String PATH_PREFIX = "/swdata/builtin/";
//    String PATH_PREFIX = "/Users/<USER>/app/builtin/";
    String LOG_FILENAME = "export.log";

    Map<String, Object> estimateFilter(ExportFilterReq req);
    Map<String, Object> exportFilter(ExportFilterReq req);
    void exportTask(long taskId);
    void exportMultiTask(long taskId);
    void exportByRecordId(ExportRecordReq req);
}
