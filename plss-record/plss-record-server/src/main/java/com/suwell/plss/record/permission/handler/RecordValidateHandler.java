package com.suwell.plss.record.permission.handler;


import cn.hutool.core.collection.CollectionUtil;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.VisitType;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.permission.validator.DataPermissionValidator;
import com.suwell.plss.record.permission.validator.ValidateContext;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.dto.request.RecordBorrowPermissionsReq;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.system.api.service.DictRpcService;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 文件的设置权限校验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecordValidateHandler extends DataPermissionValidator {

    @Lazy
    @Resource
    private RecordMetadataValueService mdValueService;
    @Resource
    private DictRpcService dictRpcService;

    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;

    @Override
    public void postProcessBeforeHandle(ValidateContext context, Long recordId, PermissionMask... basePermission) {
        log.info("文件自定义前置校验");
        if (context.isOnlyManagerValid()) {
            return;
        }
        if (super.classifiedSwitchConfig.isMetaTypeSwitch()) {
            Integer classified = getRecordMdClassified(recordId);
            if (Objects.nonNull(classified)) {
                if (!SecurityUtils.getUserClassified().matchResource(classified)) {
                    context.hasError("文件密级不匹配");
                    return;
                }
            }
            log.warn("文件没有密级元数据,id:{},", recordId);
        }
        // 文件创建人有所有权
        Long count = recordService.countWithWrapper(List.of(recordId), null);
        if (count == 0) {
            context.hasError("文件不存在");
            return;
        }
        // 查借阅权限
        RecordBorrowPermissionsReq borrowReq = new RecordBorrowPermissionsReq();
        borrowReq.setRecordId(recordId);
        borrowReq.setUserIds(listResourceVisitorIds());
        List<Integer> borrowPermissions = recordBorrowService.borrowPermissions(borrowReq);
        if (CollectionUtil.isEmpty(borrowPermissions)) {
            return;
        }
        Set<PermissionMask> collect = borrowPermissions.stream().map(PermissionMask.BORROW_MASKS::get)
                .flatMap(Collection::stream).collect(Collectors.toSet());
        for (PermissionMask mask : basePermission) {
            if (collect.contains(mask)) {
                context.pass();
                return;
            }
        }
    }

    @Override
    public void postProcessAfterHandle(ValidateContext context, Long recordId, PermissionMask... basePermission) {
        log.info("文件自身权限校验不通过，开启继承权限校验");
    }

    @NotNull
    @Override
    public ResourceType getResourceType() {
        return ResourceType.RECORD;
    }

    @Override
    public boolean isPrivateResource(Long resourceId) {
        Record byId = recordService.getOneById(resourceId);
        return Objects.nonNull(byId) && Objects.equals(byId.getVisitType(), VisitType.PRIVATE.getCode());
    }

    @Override
    public void makePrivate(Long resourceId) {
        Record record = new Record();
        record.setId(resourceId);
        record.setVisitType(VisitType.PRIVATE.getCode());
        recordService.modifyById(record);
    }

    @Override
    public void cancelPrivate(Long resourceId) {
        Record record = new Record();
        record.setId(resourceId);
        record.setVisitType(VisitType.PUBLIC.getCode());
        recordService.modifyById(record);
    }

    @Override
    public Integer getClassified(Long resourceId) {
        if (!super.classifiedSwitchConfig.isSafetySwitch()) {
            return null;
        }
        Integer classified = null;
        if (super.classifiedSwitchConfig.isMetaTypeSwitch()) {
            classified = getRecordMdClassified(resourceId);
        } else {
            Record byId = recordService.getOneById(resourceId);
            if (Objects.nonNull(byId)) {
                classified = byId.getClassified();
            }
        }
        return classified;
    }

    private BidiMap<Integer, String> recordClassifiedMap() {
        R<DualHashBidiMap<Integer, String>> r = dictRpcService.recordClassifiedMap();
        log.info("获取文件密级字典结果:{}", r);
        if (r.isSuccess()) {
            return r.getData();
        }
        log.info("获取文件密级字典失败");
        return new DualHashBidiMap<>();
    }

    private Integer getRecordMdClassified(Long recordId) {
        BidiMap<Integer, String> classifiedMap = recordClassifiedMap();
        List<MetadataValueDTO> mdList = mdValueService.queryByRecordId(recordId);
        for (MetadataValueDTO dto : mdList) {
            if (Objects.equals(dto.getMdName(), plssFixedMetadataNameConfig.getFixedSecurityClassificationName())) {
                return classifiedMap.getKey(dto.getMdValue());
            }
        }
        return null;
    }

}
