package com.suwell.plss.record.migration;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class DataExportReq {
    /**
     * 库
     */
    public Long repoId;

    /**
     * 目录
     */
    public List<Long> listFolderId;

    /**
     * 入库时间段-开始
     */
    public Date putLibTimeMin;

    /**
     * 入库时间段-结束
     */
    public Date putLibTimeMax;

    /**
     * 元数据查询
     */
    public List<MetadataValueReq> listMetadata;

    /**
     * 来源:接入方标识
     */
    public Long origin = 999L;

    public int page;
    public int pageSize;

    public Long limit;
}
