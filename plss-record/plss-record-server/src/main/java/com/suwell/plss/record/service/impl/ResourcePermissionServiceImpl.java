package com.suwell.plss.record.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.record.entity.ResourcePermission;
import com.suwell.plss.record.mapper.ResourcePermissionMapper;
import com.suwell.plss.record.service.ResourcePermissionService;
import org.springframework.stereotype.Service;


@Service("resourcePermissionService")
public class ResourcePermissionServiceImpl extends ServiceImpl<ResourcePermissionMapper, ResourcePermission> implements
        ResourcePermissionService {

}