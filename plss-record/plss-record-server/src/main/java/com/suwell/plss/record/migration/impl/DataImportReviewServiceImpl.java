package com.suwell.plss.record.migration.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.suwell.plss.framework.common.utils.EncryptUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.record.conf.OpenWpsCheckConfig;
import com.suwell.plss.record.domain.RecordReviewDTO;
import com.suwell.plss.record.entity.*;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.migration.DataImportReviewData;
import com.suwell.plss.record.migration.DataImportReviewRecord;
import com.suwell.plss.record.migration.DataImportReviewRecordDocument;
import com.suwell.plss.record.migration.DataImportReviewResult;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.service.RecordReviewService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.AuditFileAsyncResp;
import com.suwell.plss.record.standard.dto.response.AuditShortTextResp;
import com.suwell.plss.record.standard.dto.response.RecordReviewPageResp;
import com.suwell.plss.record.standard.dto.response.RecordReviewVersionListResp;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataImportReviewServiceImpl {
    private static final String biz = "docer_v2";
    private static final String scene = "material_new";
    private static final Long templateRepoID = 5435880395013L;
    private final java.util.logging.Logger logger = Logger.getLogger(this.getClass().getName());
    @Resource
    private RecordService recordService;
    @Resource
    private RecordReviewService reviewService;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileService fileService;
    @Resource
    private OpenWpsCheckConfig openWpsCheckConfig;

    public RecordEnum.DataLoadTaskOverReason importDataReview(String prefixPath, InternalImportReviewReq req) {
        try {
            DataImportReviewData data = parseImportData(prefixPath, req.getDirname());
            if (data == null || data.getRecords() == null || data.getRecords().isEmpty()) {
                return RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
            }
            List<DataImportReviewRecord> reviewRecordList = buildImportData(data);
            for (DataImportReviewRecord record : reviewRecordList) {
                reviewAndSaveRecord(record, req.getVersion());
            }
            return RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_NORMAL;
        } catch (Exception ex) {
            logger.info("import data send review failed: " + ex.getMessage());
            return RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_EXCEPTION;
        }
    }

    public RecordReviewPageResp recordReviewQueryPage(RecordReviewPageReq req) {
        try {
            List<RecordReviewDTO> recordReviewDTOList = reviewService.recordReviewPage(req);
            List<Object> dataList = new ArrayList<>();
            Long nextID = null;
            List<Long> recordIDList = new ArrayList<>();
            for (RecordReviewDTO recordReviewDTO : recordReviewDTOList) {
                recordIDList.add(recordReviewDTO.getRecordId());
            }
            Map<Long, String> coverImgURLMap = getCoverImgURLMap(recordIDList);
            for (RecordReviewDTO recordReviewDTO : recordReviewDTOList) {
                dataList.add(recordReviewDTO.buildRespData(coverImgURLMap.get(recordReviewDTO.getRecordId())));
                nextID = recordReviewDTO.getId();
            }
            RecordReviewPageResp resp = new RecordReviewPageResp();
            resp.setRecordList(dataList);
            resp.setNextStartId(nextID);
            return resp;
        } catch (Exception ex) {
            logger.info("record review query page failed: " + ex.getMessage());
            return null;
        }
    }

    private Map<Long, String> getCoverImgURLMap(List<Long> recordIDList) {
//        List<Document> coverList = documentService.list(new LambdaQueryWrapper<Document>().in(Document::getRecordId, recordIDList)
//                .eq(Document::getCtype, RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode()));
        List<Document> coverList = documentService.listByRecordIds(recordIDList).stream()
                .filter(d -> Objects.equals(d.getCtype(), RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode()))
                .toList();
        Map<Long, Document> coverMap = coverList.stream()
                .collect(Collectors.toMap(Document::getRecordId, Function.identity(), (k1, k2) -> k2));
        Map<Long, String> coverImgURLMap = new HashMap<>();
        for (Long recordID : recordIDList) {
            Document cover = coverMap.get(recordID);
            if (cover == null) {
                continue;
            }
            FileTempUrlReq coverUrlReq = new FileTempUrlReq();
            coverUrlReq.setFileId(cover.getFileId());
            coverImgURLMap.put(recordID, fileService.tempUrl(coverUrlReq));
        }
        return coverImgURLMap;
    }

    public RecordReviewVersionListResp getRecordReviewVersionList(RecordReviewVersionListReq req) {
        try {
            List<String> versionList = reviewService.recordReviewVersionList(req);
            RecordReviewVersionListResp resp = new RecordReviewVersionListResp();
            resp.setVersion(versionList);
            return resp;
        } catch (Exception ex) {
            logger.info("record review get version list failed: " + ex.getMessage());
            return null;
        }
    }

    public void setReviewResult(RecordReviewSetResultReq req) {
        try {
            String fileinx = req.getFileinx();
            Long id = Long.parseLong(fileinx);
            RecordReviewDTO reviewDTO = reviewService.getRecordReviewById(id);
            RecordReview recordReview = reviewDTO.toEntity();
            recordReview.setReviewResult(req.buildReviewResult());
            recordReview.setReviewStatus(req.buildReviewStatus());
            recordReview.setStatus(req.buildStatus());
            Date reviewTime = new Date(System.currentTimeMillis());
            recordReview.setReviewTime(reviewTime);
            reviewService.updateRecordReview(recordReview);
        } catch (Exception ex) {
            logger.info("set review result failed: " + ex.getMessage());
        }
    }

    public String getReviewRecordDownloadURL(String fileinx) {
        try {
            Long id = Long.parseLong(fileinx);
            RecordReviewDTO reviewDTO = reviewService.getRecordReviewById(id);
            FileTempUrlReq req = new FileTempUrlReq();
            req.setFileId(reviewDTO.getFileId());
            return fileService.tempUrl(req);
        } catch (Exception ex) {
            logger.info("set review result failed: " + ex.getMessage());
            return "";
        }
    }

    public DataImportReviewData parseImportData(String prefixPath, String dirname) {
        Path dirPath = Paths.get(prefixPath, dirname);
        if (!Files.exists(dirPath)) {
            logger.warning("import path not exists");
            return null;
        }

        File folder = new File(dirPath.toString());
        File[] files = folder.listFiles();

        List<Record> records = new ArrayList<>();
        List<Document> documents = new ArrayList<>();
        List<FileRecord> fileRecords = new ArrayList<>();
        List<FolderRecord> folderRecords = new ArrayList<>();
        List<FolderRel> folderRels = new ArrayList<>();
        List<Repository> repositories = new ArrayList<>();
        for (File file : files) {
            if (file.isDirectory()) {
                continue;
            }

            String extName = "." + FileNameUtil.getSuffix(file);

            switch (extName) {
                case ExportConstant.SUFFIX_RECORD:
                    List<Record> parsedRecordList = parseRecordList(file.getAbsolutePath());
                    if (parsedRecordList != null && !parsedRecordList.isEmpty()) {
                        records.addAll(parsedRecordList);
                    }
                    break;
                case ExportConstant.SUFFIX_DOCUMENT:
                    List<Document> parsedDocumentList = parseDocumentList(file.getAbsolutePath());
                    if (parsedDocumentList != null && !parsedDocumentList.isEmpty()) {
                        documents.addAll(parsedDocumentList);
                    }
                    break;
                case ExportConstant.SUFFIX_FILE:
                    List<FileRecord> parsedRecordFileList = parseRecordFileList(file.getAbsolutePath());
                    if (parsedRecordFileList != null && !parsedRecordFileList.isEmpty()) {
                        fileRecords.addAll(parsedRecordFileList);
                    }
                    break;
                case ExportConstant.SUFFIX_METADATA:
                    break;
                case ExportConstant.SUFFIX_METADATA_VALUE:
                    break;
                case ExportConstant.SUFFIX_REPO_RECORD:
                    break;
                case ExportConstant.SUFFIX_FOLDER:
                    break;
                case ExportConstant.SUFFIX_FOLDER_RECORD:
                    List<FolderRecord> parsedFolderRecordList = parseFolderRecordList(file.getAbsolutePath());
                    if (parsedFolderRecordList != null && !parsedFolderRecordList.isEmpty()) {
                        folderRecords.addAll(parsedFolderRecordList);
                    }
                    break;
                case ExportConstant.SUFFIX_FOLDER_REL:
                    List<FolderRel> parsedFolderRelList = parseFolderRelList(file.getAbsolutePath());
                    if (parsedFolderRelList != null && !parsedFolderRelList.isEmpty()) {
                        folderRels.addAll(parsedFolderRelList);
                    }
                    break;
                case ExportConstant.SUFFIX_METADATA_CATEGORY:
                    break;
                case ExportConstant.SUFFIX_METADATA_CATEGORY_METADATA:
                    break;
                case ExportConstant.SUFFIX_REPOSITORY:
                    List<Repository> parseRepositoryList = parseRepositoryList(file.getAbsolutePath());
                    if (parseRepositoryList != null && !parseRepositoryList.isEmpty()) {
                        repositories.addAll(parseRepositoryList);
                    }
                    break;
                case ExportConstant.SUFFIX_RESOURCE_MANAGER:
                    break;
                case ExportConstant.SUFFIX_ES_RECORD:
                    break;
                default:
                    break;
            }
        }
        DataImportReviewData reviewData = new DataImportReviewData();
        reviewData.setRecords(records);
        reviewData.setDocuments(documents);
        reviewData.setFileRecords(fileRecords);
        reviewData.setFolderRecords(folderRecords);
        reviewData.setFolderRels(folderRels);
        reviewData.setRepositories(repositories);
        return reviewData;
    }

    public List<DataImportReviewRecord> buildImportData(DataImportReviewData data) {
        Map<Long, List<Long>> folderRelMap = new HashMap<>();
        for (FolderRel folderRel : data.getFolderRels()) {
            List<Long> repoIds = folderRelMap.get(folderRel.getDescendantId());
            if (repoIds == null || repoIds.isEmpty()) {
                repoIds = new ArrayList<>();
            }
            repoIds.add(folderRel.getAncestorId());
            folderRelMap.put(folderRel.getDescendantId(), repoIds);
        }

        Map<Long, Repository> repositoriesMap = new HashMap<>();
        for (Repository repository : data.getRepositories()) {
            repositoriesMap.put(repository.getId(), repository);
        }

        Map<Long, Repository> recordToRepoMap = new HashMap<>();
        for (FolderRecord folderRecord : data.getFolderRecords()) {
            List<Long> repoIds = folderRelMap.get(folderRecord.getFolderId());
            Repository repo = null;
            for (Long repoId : repoIds) {
                repo = repositoriesMap.get(repoId);
                if (repo != null) {
                    break;
                }
            }
            recordToRepoMap.put(folderRecord.getRecordId(), repo);
        }

        Map<Long, DataImportReviewRecord> recordMap = new HashMap<>();
        for (Record record : data.getRecords()) {
            DataImportReviewRecord dataImportReviewRecord = new DataImportReviewRecord();
            dataImportReviewRecord.recordId = record.getId();
            dataImportReviewRecord.name = record.getName();
            dataImportReviewRecord.title = record.getTitle();
            Repository repo = recordToRepoMap.get(record.getId());
            dataImportReviewRecord.repoId = repo.getId();
            dataImportReviewRecord.recordtypeId = record.getRecordtypeId();
            dataImportReviewRecord.recordType = 1;
            if (Objects.equals(repo.getId(), templateRepoID)) {
                dataImportReviewRecord.recordType = 2;
            }
            recordMap.put(record.getId(), dataImportReviewRecord);
        }

        Map<Long, FileRecord> fileRecordMap = new HashMap<>();
        for (FileRecord fileRecord : data.getFileRecords()) {
            fileRecordMap.put(fileRecord.getId(), fileRecord);
        }

        for (Document document : data.getDocuments()) {
            DataImportReviewRecord record = recordMap.get(document.getRecordId());
            if (record == null) {
                continue;
            }
            if (record.documentList == null) {
                record.documentList = new ArrayList<>();
            }
            FileRecord fileRecord = fileRecordMap.get(document.getFileId());
            if (fileRecord == null) {
                continue;
            }

            DataImportReviewRecordDocument dataImportReviewRecordDocument = new DataImportReviewRecordDocument();
            dataImportReviewRecordDocument.setId(document.getId());
            dataImportReviewRecordDocument.setFileId(document.getFileId());
            dataImportReviewRecordDocument.setCtype(document.getCtype());
            dataImportReviewRecordDocument.setAttachmentType(document.getAttachmentType());
            dataImportReviewRecordDocument.setFileExt(fileRecord.getFileExt());
            dataImportReviewRecordDocument.setMd5(document.getFileMd5());
            dataImportReviewRecordDocument.setFileSize(document.getFileSize());
            dataImportReviewRecordDocument.setFileName(document.getName());
            record.documentList.add(dataImportReviewRecordDocument);
        }

        List<DataImportReviewRecord> reviewRecordList = new ArrayList<>(recordMap.values());
        reviewRecordList.sort(Comparator.comparing(DataImportReviewRecord::getRecordId));
        return reviewRecordList;
    }

    private List<Record> parseRecordList(String filepath) {
        List<Record> listRecord = deseriallizeList(filepath, Record.class);
        if (listRecord.isEmpty()) {
            return null;
        }

        try {
            List<Long> listRepeat = recordService.getListByIds(
                    listRecord.stream().map(Record::getId).toList()).stream()
                    .map(Record::getId).toList();
            if (!listRepeat.isEmpty()) {
                logger.info("repeat record id count: " + listRepeat.size());
                listRecord.removeIf(record -> listRepeat.contains(record.getId()));
            }
        } catch (Exception ex) {
            logger.info("import record error " + ex.getMessage());
        }


        return listRecord;
    }

    private List<Document> parseDocumentList(String filepath) {
        List<Document> listDocument = deseriallizeList(filepath, Document.class);
        if (listDocument.isEmpty()) {
            return null;
        }

        try {
            List<Long> listRepeat = documentService.listRepeatDocument(listDocument).stream().map(Document::getId).toList();
            if (!listRepeat.isEmpty()) {
                logger.info("repeat document id count: " + listRepeat.size());
                listDocument.removeIf(document -> listRepeat.contains(document.getId()));
            }
        } catch (Exception ex) {
            logger.info("import document error " + ex.getMessage());
        }

        return listDocument;
    }

    private List<FileRecord> parseRecordFileList(String filepath) {
        List<FileRecord> listFileRecord = deseriallizeList(filepath, FileRecord.class);
        if (listFileRecord.isEmpty()) {
            return null;
        }
        List<FileRecord> origin = new ArrayList<>(listFileRecord);

        List<Long> listRepeat = fileService.batchList(listFileRecord.stream().map(FileRecord::getId).toList())
                .stream().map(FileRecord::getId).toList();
//      List<Long> listRepeat = fileService..list(new LambdaQueryWrapper<FileRecord>().in(FileRecord::getId,
//                        listFileRecord.stream().map(FileRecord::getId).collect(Collectors.toList())))
//                .stream().map(FileRecord::getId).collect(Collectors.toList());
        if (!listRepeat.isEmpty()) {
            logger.info("repeat file id count: " + listRepeat.size());
            listFileRecord.removeIf(fileRecord -> listRepeat.contains(fileRecord.getId()));
        }
        // 下载文件需要先上传&入库
        insertFileRecord(filepath, listFileRecord);
        return origin;
    }

    private void insertFileRecord(String filepath, List<FileRecord> listFileRecord) {
        final String dirpath = FileUtil.getParent(filepath, 1);
        listFileRecord.forEach(fileRecord -> {
            String orignalFile = dirpath + File.separator + fileRecord.getRelativePath() + File.separator + fileRecord.getFileName();
            Boolean bRet = uploadFile(orignalFile, fileRecord.getOriginName(), fileRecord.getFileName(), fileRecord.getRelativePath());
            if (bRet) {
                fileService.saveOrUpdateBatch(List.of(fileRecord));
            }
        });
    }

    public Boolean uploadFile(String filepath, String originName, String fileName, String relativePath) {
        try {
            if (StringUtils.isEmpty(filepath) || !Files.exists(Paths.get(filepath))) {
                logger.info("file not exists: " + originName + ":" + fileName);
                return false;
            }

            FileInputStream fis = new FileInputStream(filepath);

            FileTransferReq req = new FileTransferReq();
            req.setFileData(fis);
            req.setOriginName(originName);
            req.setFileName(fileName);
            req.setRelativePath(relativePath);

            fileService.transferIn(req);

            return true;

        } catch (Exception ex) {
            logger.info(ex.getMessage());
            return false;
        }
    }

    private List<FolderRecord> parseFolderRecordList(String filepath) {
        List<FolderRecord> listFolderRecord = deseriallizeList(filepath, FolderRecord.class);
        if (listFolderRecord.isEmpty()) {
            return null;
        }
        return listFolderRecord;
    }

    private List<FolderRel> parseFolderRelList(String filepath) {
        List<FolderRel> listFolderRel = deseriallizeList(filepath, FolderRel.class);
        if (listFolderRel.isEmpty()) {
            return null;
        }
        return listFolderRel;
    }

    private List<Repository> parseRepositoryList(String filepath) {
        List<Repository> listRepository = deseriallizeList(filepath, Repository.class);
        if (listRepository.isEmpty()) {
            return null;
        }
        return listRepository;
    }

    private <T> List<T> deseriallizeList(String filepath, Class<T> clazz) {

        try {
            String jsonCrypt = new String(Files.readAllBytes(Paths.get(filepath)));
            String json = EncryptUtils.decryptWithAES(jsonCrypt, ExportConstant.AES_KEY);

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            List<T> list =
                    objectMapper.readValue(json,
                            objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));

            list = list.stream().distinct().collect(Collectors.toList());

            return list;

        } catch (Exception ex) {
            logger.info(ex.getMessage());
        }

        return new ArrayList<>();
    }

    private DataImportReviewResult reviewRecord(RecordReview recordReview) {
        AuditFileAsyncReq auditFileAsyncReq = new AuditFileAsyncReq();
        auditFileAsyncReq.setBiz(biz);
        auditFileAsyncReq.setFileinx(recordReview.getId().toString());
        auditFileAsyncReq.setExtraId(recordReview.getFileId().toString());
        auditFileAsyncReq.setScene(scene);
        auditFileAsyncReq.setFileName(recordReview.getName());
        auditFileAsyncReq.setSha1(recordReview.getMd5());
        auditFileAsyncReq.setFsize(recordReview.getFileSize());
        auditFileAsyncReq.setUserId(0L);
        Map<String, Object> fileScope = new HashMap<>();
        fileScope.put("link_ctime", System.currentTimeMillis() / 1000);
        auditFileAsyncReq.setFileScope(fileScope);
        return asyncAuditFile(auditFileAsyncReq);
    }

    private RecordReview reviewAndSaveRecord(DataImportReviewRecord record, String version) {
        RecordReview recordReview = new RecordReview();
        // 入库&送审去重
        RecordReviewDTO recordReviewDTO = reviewService.getRecordReviewByRecordId(record.getRecordId());
        if (recordReviewDTO != null && recordReviewDTO.getVersion().equals(version)) {
            return recordReviewDTO.toEntity();
        }
        for (DataImportReviewRecordDocument document : record.documentList) {
            // 只送审doc和docx
            if (!Objects.equals(document.getFileExt(), "doc") && !Objects.equals(document.getFileExt(), "docx")) {
                continue;
            }
            // 只送审主文件
            if (document.getCtype() == null || document.getCtype() != 1) {
                continue;
            }
            recordReview.setId(IdWorker.getId());
            recordReview.setRecordId(record.getRecordId());
            recordReview.setVersion(version);
            recordReview.setName(document.getFileName());
            recordReview.setTitle(record.getTitle());
            recordReview.setDocumentId(document.getId());
            recordReview.setFileId(document.getFileId());
            recordReview.setRepoId(record.getRepoId());
            recordReview.setContent(document.getContent());
            recordReview.setRecordtypeId(record.getRecordtypeId());
            recordReview.setRecordType(record.getRecordType());
            recordReview.setCtype(document.getCtype());
            recordReview.setAttachmentType(document.getAttachmentType());
            recordReview.setMd5(document.getMd5());
            recordReview.setFileSize(document.getFileSize());
            DataImportReviewResult result = reviewRecord(recordReview);
            recordReview.setReviewTime(result.getReviewTime());
            recordReview.setReviewResult(result.getReviewResult());
            recordReview.setReviewStatus(result.getReviewStatus());
            recordReview.setStatus(result.getStatus());
            break;
        }
        List<RecordReview> rl = new ArrayList<>();
        rl.add(recordReview);
        reviewService.saveBatchRecordReview(rl);
        return recordReview;
    }

    private DataImportReviewResult asyncAuditFile(AuditFileAsyncReq req) {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("biz", req.getBiz());
        String fileinx = req.getFileinx();
        params.put("fileinx", fileinx);
        params.put("extra_id", req.getExtraId());
        params.put("scene", req.getScene());
        params.put("file_name", req.getFileName());
        params.put("sha1", req.getSha1());
        params.put("fsize", req.getFsize());
        params.put("file_scope", req.getFileScope());
        params.put("user_id", req.getUserId());

        String date = SignUtil.getGMTDate();
        String contentMd5 = SignUtil.getContentMd5(params);
        String dmcAppid = openWpsCheckConfig.getDmcAppid();
        String dmcAppKey = openWpsCheckConfig.getDmcAppKey();
        String authorization = SignUtil.getAuthorization(openWpsCheckConfig.getAppid(), dmcAppid, dmcAppKey, contentMd5, date);
        String auditFileUrl = openWpsCheckConfig.getAuditFileUrl();
        cn.hutool.http.HttpRequest httpRequest =
                cn.hutool.http.HttpRequest.post(auditFileUrl).header("Content-MD5", contentMd5)
                        .header("Date", date).header("Content-Type", "application/json")
                        .header("Authorization", authorization).body(JSON.toJSONString(params));
        HttpResponse httpResponse = httpRequest.execute();
        AuditFileAsyncResp resp = null;
        if (httpResponse.isOk()) {
            resp = JSON.parseObject(httpResponse.body(), AuditFileAsyncResp.class);
        }
        DataImportReviewResult result = new DataImportReviewResult();
        result.setReviewStatus(0);
        result.setStatus(1);
        if (resp != null) {
            Date reviewTime = new Date(System.currentTimeMillis());
            result.setReviewTime(reviewTime);
            result.setReviewResult(resp.buildReviewResult());
            result.setReviewStatus(resp.getReviewStatus());
            result.setStatus(resp.getStatus());
        }
        return result;
    }

    private DataImportReviewResult auditShortText(AuditShortTextReq req) {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("content", req.content);
        params.put("from", biz);
        params.put("scene", scene);

        String date = SignUtil.getGMTDate();
        String contentMd5 = SignUtil.getContentMd5(params);
        String dmcAppid = openWpsCheckConfig.getDmcAppid();
        String dmcAppKey = openWpsCheckConfig.getDmcAppKey();
        String authorization = SignUtil.getAuthorization(openWpsCheckConfig.getAppid(), dmcAppid, dmcAppKey, contentMd5, date);
        cn.hutool.http.HttpRequest httpRequest =
                cn.hutool.http.HttpRequest.post(openWpsCheckConfig.getAuditFileUrl()).header("Content-MD5", contentMd5)
                        .header("Date", date).header("Content-Type", "application/json")
                        .header("Authorization", authorization).body(JSON.toJSONString(params));
        HttpResponse httpResponse = httpRequest.execute();
        AuditShortTextResp resp = null;
        if (httpResponse.isOk()) {
            resp = JSON.parseObject(httpResponse.body(), AuditShortTextResp.class);
        }
        DataImportReviewResult result = new DataImportReviewResult();
        Date reviewTime = new Date(System.currentTimeMillis());
        result.setReviewTime(reviewTime);
        if (resp != null) {
            result.setReviewResult(resp.buildReviewResult());
            result.setReviewStatus(resp.getReviewStatus());
            result.setStatus(resp.getStatus());
        }
        return result;
    }

    public static void main(String[] args) {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("biz", "docer_v2");
        String fileinx = "472535513868165";
        params.put("fileinx", "472535513868165");
        params.put("extra_id", "471519928690629");
        params.put("scene", "material_new");
        params.put("file_name","为什么说多边主义是人类社会进步的重要标志.docx");
        params.put("sha1", "f82b63663f3132afdf487c103fa1a2ee");
        params.put("fsize", 38620);
        Map<String, Object> scope = new HashMap<>();
        scope.put("link_ctime",System.currentTimeMillis() / 1000);
        params.put("file_scope", scope);
        params.put("user_id",0);

        String date = SignUtil.getGMTDate();
        String contentMd5 = SignUtil.getContentMd5(params);
        String dmcAppid = "AK20240422HKLPPD11";
        String dmcAppKey = "a8ee224e6ee6e3f008677e7aa03c62c8";
        String authorization = SignUtil.getAuthorization("suwell", dmcAppid, dmcAppKey, contentMd5, date);
        String auditFileUrl = "http://api.rv.ksoapp.com/async/v2/audit/file";
        cn.hutool.http.HttpRequest httpRequest =
                cn.hutool.http.HttpRequest.post(auditFileUrl).header("Content-MD5", contentMd5)
                        .header("Date", date).header("Content-Type", "application/json")
                        .header("Authorization", authorization).body(JSON.toJSONString(params));
        HttpResponse httpResponse = httpRequest.execute();
        System.out.println(httpResponse);
    }
}
