package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.PageHelperUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.record.domain.ResourceChainDTO;
import com.suwell.plss.record.entity.RcMaskingConfig;
import com.suwell.plss.record.entity.RcMaskingMetadata;
import com.suwell.plss.record.entity.RcMaskingRange;
import com.suwell.plss.record.entity.RcMaskingWord;
import com.suwell.plss.record.mapper.RcMaskingConfigMapper;
import com.suwell.plss.record.permission.factory.PermissionValidatorFactory;
import com.suwell.plss.record.service.RcMaskingConfigService;
import com.suwell.plss.record.service.RcMaskingMetadataService;
import com.suwell.plss.record.service.RcMaskingRangeService;
import com.suwell.plss.record.service.RcMaskingWordService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.standard.domain.BasePageCondition;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.dto.request.MaskingConfigAddReq;
import com.suwell.plss.record.standard.dto.request.MaskingConfigEnableReq;
import com.suwell.plss.record.standard.dto.request.MaskingMetadataAddReq;
import com.suwell.plss.record.standard.dto.request.MaskingRangeAddReq;
import com.suwell.plss.record.standard.dto.request.MaskingWordAddReq;
import com.suwell.plss.record.standard.dto.request.RepoMaskingConfigAddReq;
import com.suwell.plss.record.standard.dto.response.MaskingConfigResp;
import com.suwell.plss.record.standard.dto.response.MaskingMetadataResp;
import com.suwell.plss.record.standard.dto.response.MaskingRangeResp;
import com.suwell.plss.record.standard.dto.response.MaskingWordResp;
import com.suwell.plss.record.standard.dto.response.SensitiveCategoryResp;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.enums.RecordEnum.MaskingConfigStatusEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.MaskingConfigTypeEnum;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.service.OrgRpcService;
import com.suwell.plss.system.api.service.RoleRpcService;
import com.suwell.plss.system.api.service.UserRpcService;

import java.util.*;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service("rcMaskingConfigService")
@Slf4j
public class RcMaskingConfigServiceImpl extends ServiceImpl<RcMaskingConfigMapper, RcMaskingConfig> implements
        RcMaskingConfigService {

    @Resource
    private RcMaskingRangeService maskingRangeService;
    @Resource
    private RcMaskingMetadataService maskingMetadataService;

    @Resource
    private RcMaskingWordService maskingWordService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private PermissionValidatorFactory permissionValidatorFactory;
    @Resource
    private UserRpcService userRpcService;
    @Resource
    private OrgRpcService orgRpcService;
    @Resource
    private RoleRpcService roleRpcService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;


    @Override
    public PageUtils<MaskingConfigResp> pageList(BasePageCondition req) {
        Page<SensitiveCategoryResp> page = PageHelperUtils.startPage(req.getPage(), req.getPageSize());
        List<RcMaskingConfig> list = this.list(
                Wrappers.<RcMaskingConfig>lambdaQuery().eq(RcMaskingConfig::getConfigType,
                        MaskingConfigTypeEnum.GLOBAL_CONFIGURATION.getCode()));
//                        .eq(RcMaskingConfig::getStatus, MaskingConfigStatusEnum.ENABLE.getCode()));
        PageHelperUtils.clearPage();
        if (Objects.isNull(list)) {
            return new PageUtils<>(null, page.getTotal(), page.getPageSize(), page.getPages());
        }
        List<MaskingConfigResp> result = DozerUtils.convertListToNew(list, MaskingConfigResp.class);
        for (MaskingConfigResp maskingConfigResp : result) {
            maskingConfigResp.setRangeList(getRange(maskingConfigResp.getId()));
            maskingConfigResp.setMetaDataList(getMetadata(maskingConfigResp.getId()));
            maskingConfigResp.setWordList(getWord(maskingConfigResp.getId()));
        }

        return new PageUtils<>(result, page.getTotal(), page.getPageSize(), page.getPages());
    }

    @Override
    public void repoEnableConfig(MaskingConfigEnableReq req) {
        log.info("repoEnableConfig req: {}", req);
        List<RcMaskingConfig> list = this.list(
                Wrappers.<RcMaskingConfig>lambdaQuery().eq(RcMaskingConfig::getRepoId, req.getRepoId()));
        if (CollectionUtils.isEmpty(list)) {
            this.save(RcMaskingConfig.builder()
                    .id(wfgIdGenerator.next())
                    .configType(MaskingConfigTypeEnum.LIBRARY_CONFIGURATION.getCode())
                    .status(req.getStatus())
                    .repoId(req.getRepoId()).status(req.getStatus()).build());
        }
        this.update(Wrappers.<RcMaskingConfig>lambdaUpdate().set(RcMaskingConfig::getStatus, req.getStatus())
                .eq(RcMaskingConfig::getRepoId, req.getRepoId()));
    }

    @Override
    public List<MaskingConfigResp> getMaskingConfigByRepoIds(List<Long> repoIds) {
        List<MaskingConfigResp> resps = Lists.newArrayList();
        if (CollectionUtils.isEmpty(repoIds)) {
            return resps;
        }
        repoIds.forEach(repoId -> {
            List<MaskingConfigResp> maskingConfigByRepoId = this.getMaskingConfigByRepoId(repoId,
                    MaskingConfigStatusEnum.ENABLE.getCode());
            if (CollectionUtils.isNotEmpty(maskingConfigByRepoId)) {
                resps.addAll(maskingConfigByRepoId);
            }
        });
        return resps;
    }

    @Override
    public void saveMaskingConfigList(RepoMaskingConfigAddReq req) {
        log.info("saveMaskingConfigList req: {}", req);
        if (CollectionUtils.isEmpty(req.getMaskingConfigAddReqList())) {
            throw new BizException(RecordBizError.CONFIG_IS_NULL);
        }
        req.getMaskingConfigAddReqList().stream().forEach(config -> {
            long configId = wfgIdGenerator.next();
            RcMaskingConfig rcMaskingConfig = RcMaskingConfig.builder()
                    .configType(config.getConfigType())
                    .repoId(req.getRepoId())
                    .metadataStatus(config.getMetadataStatus())
                    .wordStatus(config.getWordStatus())
                    .processMode(config.getProcessMode())
                    .status(MaskingConfigStatusEnum.DISABLE.getCode())
                    .id(configId)
                    .build();
            this.save(rcMaskingConfig);

            this.saveRange(config.getRangeList(), configId);

            this.saveMetadata(config.getMetaDataList(), configId);

            this.saveWord(config.getWordList(), configId);
        });


    }

    @Override
    public void updateMaskingConfigList(RepoMaskingConfigAddReq req) {
        log.info("updateMaskingConfigList req: {}", req);

        List<RcMaskingConfig> list = this.list(
                Wrappers.<RcMaskingConfig>lambdaQuery().eq(RcMaskingConfig::getRepoId,
                        req.getRepoId()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(RecordBizError.CONFIG_NOT_EXIST);
        }
        this.removeBatchByIds(list);
        List<Long> configIds = list.stream().map(RcMaskingConfig::getId).collect(Collectors.toList());
        maskingRangeService.remove(
                Wrappers.<RcMaskingRange>lambdaQuery().in(RcMaskingRange::getMaskingConfigId, configIds));
        maskingMetadataService.remove(
                Wrappers.<RcMaskingMetadata>lambdaQuery().in(RcMaskingMetadata::getMaskingConfigId, configIds));
        maskingWordService.remove(
                Wrappers.<RcMaskingWord>lambdaQuery().in(RcMaskingWord::getMaskingConfigId, configIds));
        if (CollectionUtils.isEmpty(req.getMaskingConfigAddReqList())) {
            return;
        }
        saveMaskingConfigList(req);

    }

    @Override
    public List<MaskingConfigResp> recordInfo(Long recordId, String userId) {

        List<MaskingConfigResp> maskingConfigResps = new ArrayList<>();
        //获取库
        List<ResourceChainDTO> resourceChainDTOS = permissionValidatorFactory.getValidator(ResourceType.RECORD)
                .listResourceChains(recordId);
        if (CollectionUtils.isEmpty(resourceChainDTOS)) {
            return maskingConfigResps;
        }
        List<Long> repoIds = resourceChainDTOS.stream().map(ResourceChainDTO::getRepoId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(repoIds)) {
            return maskingConfigResps;
        }
        List<MaskingConfigResp> maskingConfigByRepoIds = getMaskingConfigByRepoIds(repoIds);
        if (CollectionUtils.isNotEmpty(maskingConfigByRepoIds)) {
            //库配置
            List<MaskingConfigResp> maskingConfigRespList = roleFiltering(maskingConfigByRepoIds, userId);
            if (CollectionUtils.isNotEmpty(maskingConfigRespList)) {
                for (MaskingConfigResp maskingConfigResp : maskingConfigRespList) {
                    if (maskingConfigResp.getStatus().equals(MaskingConfigStatusEnum.ENABLE.getCode())) {
                        maskingConfigResps.add(maskingConfigResp);
                    }
                }
            }
        }
        //全局配置
        List<RcMaskingConfig> configList = this.lambdaQuery()
                .eq(RcMaskingConfig::getConfigType, MaskingConfigTypeEnum.GLOBAL_CONFIGURATION.getCode())
                .eq(RcMaskingConfig::getStatus, MaskingConfigStatusEnum.ENABLE.getCode()).list();
        if (CollectionUtils.isNotEmpty(configList)) {
            List<MaskingConfigResp> configRespList = configList.stream().map(config -> {
                MaskingConfigResp maskingConfigResp = DozerUtils.convertToNew(config, MaskingConfigResp.class);
                maskingConfigResp.setRangeList(getRange(config.getId()));
                maskingConfigResp.setMetaDataList(getMetadata(config.getId()));
                maskingConfigResp.setWordList(getWord(config.getId()));
                return maskingConfigResp;
            }).collect(Collectors.toList());
            List<MaskingConfigResp> maskingConfigRespList = roleFiltering(configRespList, userId);
            if (CollectionUtils.isNotEmpty(maskingConfigRespList)) {
                maskingConfigResps.addAll(maskingConfigRespList);
            }
        }
        if (CollectionUtils.isEmpty(maskingConfigResps)) {
            return maskingConfigResps;
        }
        return setMedateValue(recordId, maskingConfigResps);
    }

    @Override
    @DSTransactional
    public void saveMaskingConfig(MaskingConfigAddReq req) {
        log.info("saveMaskingConfig req: {}", req);

        long configId = wfgIdGenerator.next();
        RcMaskingConfig rcMaskingConfig = RcMaskingConfig.builder()
                .configType(req.getConfigType())
                .repoId(Objects.nonNull(req.getRepoId()) ? req.getRepoId() : 0L)
                .metadataStatus(req.getMetadataStatus())
                .wordStatus(req.getWordStatus())
                .processMode(req.getProcessMode())
                .id(configId)
                .build();
        //库配置默认禁用
        if (Objects.nonNull(req.getRepoId())) {
            rcMaskingConfig.setStatus(MaskingConfigStatusEnum.DISABLE.getCode());
        }
        this.save(rcMaskingConfig);

        this.saveRange(req.getRangeList(), configId);

        this.saveMetadata(req.getMetaDataList(), configId);

        this.saveWord(req.getWordList(), configId);

    }

    @Override
    @DSTransactional
    public void updateMaskingConfig(MaskingConfigAddReq req) {
        log.info("updateMaskingConfig req: {}", req);

        UpdateWrapper<RcMaskingConfig> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("config_type", req.getConfigType());
        updateWrapper.set("repo_id", Objects.isNull(req.getRepoId()) ? 0 : req.getRepoId());
        updateWrapper.set("process_mode", req.getProcessMode());
        updateWrapper.set("metadata_status", req.getMetadataStatus());
        updateWrapper.set("word_status", req.getWordStatus());
        if (Objects.nonNull(req.getStatus())) {
            updateWrapper.set("status", req.getStatus());
        }
        updateWrapper.eq("id", req.getConfigId());
        this.update(updateWrapper);

        //删除
        maskingRangeService.remove(
                Wrappers.<RcMaskingRange>lambdaQuery().eq(RcMaskingRange::getMaskingConfigId, req.getConfigId()));
        maskingMetadataService.remove(
                Wrappers.<RcMaskingMetadata>lambdaQuery().eq(RcMaskingMetadata::getMaskingConfigId, req.getConfigId()));
        maskingWordService.remove(
                Wrappers.<RcMaskingWord>lambdaQuery().eq(RcMaskingWord::getMaskingConfigId, req.getConfigId()));

        //添加
        this.saveRange(req.getRangeList(), req.getConfigId());
        this.saveMetadata(req.getMetaDataList(), req.getConfigId());
        this.saveWord(req.getWordList(), req.getConfigId());


    }

    @Override
    public void deleteMaskingConfig(Long id) {
        this.removeById(id);
        //删除
        maskingRangeService.remove(
                Wrappers.<RcMaskingRange>lambdaQuery().eq(RcMaskingRange::getMaskingConfigId, id));
        maskingMetadataService.remove(
                Wrappers.<RcMaskingMetadata>lambdaQuery().eq(RcMaskingMetadata::getMaskingConfigId, id));
        maskingWordService.remove(
                Wrappers.<RcMaskingWord>lambdaQuery().eq(RcMaskingWord::getMaskingConfigId, id));

    }

    @Override
    public MaskingConfigResp getMaskingConfigById(Long id) {
        log.info("getMaskingConfigById id={}", id);
        RcMaskingConfig config = this.getById(id);
        if (Objects.isNull(config)) {
            return null;
        }
        MaskingConfigResp maskingConfigResp = DozerUtils.convertToNew(config, MaskingConfigResp.class);
        maskingConfigResp.setRangeList(getRange(id));
        maskingConfigResp.setMetaDataList(getMetadata(id));
        maskingConfigResp.setWordList(getWord(id));

        return maskingConfigResp;


    }

    @Override
    public List<MaskingConfigResp> getMaskingConfigByRepoId(Long repoId, Integer type) {
        log.info("getMaskingConfigByRepoId repoId={}", repoId);
        List<RcMaskingConfig> list = lambdaQuery()
                .eq(RcMaskingConfig::getRepoId, repoId)
                .eq(Objects.nonNull(type), RcMaskingConfig::getStatus, type)
                .list();

        List<MaskingConfigResp> resps = DozerUtils.convertListToNew(list, MaskingConfigResp.class);
        for (MaskingConfigResp maskingConfigResp : resps) {
            maskingConfigResp.setRangeList(getRange(maskingConfigResp.getId()));
            maskingConfigResp.setMetaDataList(getMetadata(maskingConfigResp.getId()));
            maskingConfigResp.setWordList(getWord(maskingConfigResp.getId()));
        }

        return resps;

    }

    private List<MaskingConfigResp> setMedateValue(Long recordId, List<MaskingConfigResp> maskingConfigResps) {
        List<Long> mdIds = Lists.newArrayList();
        maskingConfigResps.forEach(resp -> {
            if (CollectionUtils.isNotEmpty(resp.getMetaDataList())) {
                resp.getMetaDataList().forEach(metadataResp -> {
                    mdIds.add(metadataResp.getMdId());
                });
            }
        });
        if (CollectionUtils.isEmpty(mdIds)) {
            return maskingConfigResps;
        }

        List<MetadataValueDTO> metadataValues = recordMetadataValueService
                .queryFilterRecordMetadataValue(recordId, mdIds);
        return maskingConfigResps.stream().map(resp -> {
            if (CollectionUtils.isNotEmpty(resp.getMetaDataList())) {
                resp.getMetaDataList().forEach(metadataResp -> {
                    metadataValues.forEach(metadataValue -> {
                        if (metadataResp.getMdId().equals(metadataValue.getMdId())) {
                            metadataResp.setMdValue(metadataValue.getMdValue());
                        }
                    });
                });
            }
            return resp;
        }).collect(Collectors.toList());
    }


    private List<MaskingConfigResp> roleFiltering(List<MaskingConfigResp> maskingConfigByRepoIds, String userId) {
        List<MaskingConfigResp> result = Lists.newArrayList();

        List<String> rangeIds = Lists.newArrayList();
        rangeIds.add(userId);
        R<List<String>> orgs = orgRpcService.queryOrgByUserId(userId);
        if (orgs.isSuccess()) {
            rangeIds.addAll(orgs.getData());
        }
        // TODO queryByUserId 角色已改造-敏感词库，没有角色选项
        if(!ksoAuthConfig.isEnable()){
            R<List<String>> roles = roleRpcService.queryByUserId(userId);
            if (roles.isSuccess()) {
                rangeIds.addAll(roles.getData());
            }
        }
        R<SysUser> user = userRpcService.queryById(userId);
        if (user.isSuccess()) {
            rangeIds.add(user.getData().getUserId());
        }

        log.info("roleFiltering rangeIds={}", rangeIds);
        for (MaskingConfigResp maskingConfigByRepoId : maskingConfigByRepoIds) {
            if (maskingConfigByRepoId.getStatus().equals(MaskingConfigStatusEnum.ENABLE.getCode())
                    && CollectionUtils.isNotEmpty(maskingConfigByRepoId.getRangeList())) {
                log.info("roleFiltering maskingConfigByRepoId.getRangeList()={}", maskingConfigByRepoId.getRangeList());
                if (maskingConfigByRepoId.getRangeList().stream().map(MaskingRangeResp::getRangeId)
                        .collect(Collectors.toList()).removeAll(
                                new HashSet<>(rangeIds))) {
                    result.add(maskingConfigByRepoId);
                }
            }
        }
        return result;
    }

    private List<MaskingRangeResp> getRange(Long configId) {
        List<RcMaskingRange> list = maskingRangeService.list(
                Wrappers.<RcMaskingRange>lambdaQuery().eq(RcMaskingRange::getMaskingConfigId, configId));
        List<String> userIdList = list.stream().filter(range -> range.getRangeType().equals(0)).map(o->String.valueOf(o.getRangeId())).toList();
        if (CollectionUtils.isNotEmpty(userIdList)){
            R<List<SysUser>> r = userRpcService.getBatchInfo(userIdList);
            if(r.isSuccess()){
                List<SysUser> userList = r.getData();
               if(CollUtil.isNotEmpty(userList)){
                   Map<String, String> userAvatarMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getAvatar));
                   list.stream().filter(range -> range.getRangeType().equals(0)).forEach(range -> {
                       range.setAvatar(userAvatarMap.getOrDefault(String.valueOf(range.getRangeId()), ""));
                   });
               }
            }
        }
        List<String> orgIdList = list.stream().filter(range -> range.getRangeType().equals(2)).map(o->String.valueOf(o.getRangeId())).toList();
        if (CollectionUtils.isNotEmpty(orgIdList)){
            R<List<SysOrg>> r = orgRpcService.queryByIdList(orgIdList);
            if(r.isSuccess()){
                List<SysOrg> orgList = r.getData();
                Map<String, String> orgAvatarMap = orgList.stream().collect(Collectors.toMap(SysOrg::getOrgId, SysOrg::getAbsPath));
                list.stream().filter(range -> range.getRangeType().equals(2)).forEach(rcMaskingRange -> {
                    rcMaskingRange.setAbsPath(orgAvatarMap.getOrDefault(String.valueOf(rcMaskingRange.getRangeId()), ""));
                });
            }
        }

        return DozerUtils.convertListToNew(list, MaskingRangeResp.class);
    }

    private List<MaskingMetadataResp> getMetadata(Long configId) {
        List<RcMaskingMetadata> list = maskingMetadataService.list(
                Wrappers.<RcMaskingMetadata>lambdaQuery().eq(RcMaskingMetadata::getMaskingConfigId, configId));

        return DozerUtils.convertListToNew(list, MaskingMetadataResp.class);
    }

    private List<MaskingWordResp> getWord(Long configId) {
        List<RcMaskingWord> list = maskingWordService.list(
                Wrappers.<RcMaskingWord>lambdaQuery().eq(RcMaskingWord::getMaskingConfigId, configId));
        return DozerUtils.convertListToNew(list, MaskingWordResp.class);
    }

    private void saveRange(List<MaskingRangeAddReq> list, Long configId) {
        List<RcMaskingRange> ranges = list.stream().map(range -> {
            RcMaskingRange rcMaskingRange = DozerUtils.convertToNew(range, RcMaskingRange.class);
            rcMaskingRange.setMaskingConfigId(configId);
            return rcMaskingRange;
        }).collect(Collectors.toList());
        maskingRangeService.saveBatch(ranges);

    }

    private void saveMetadata(List<MaskingMetadataAddReq> list, Long configId) {
        List<RcMaskingMetadata> metadatas = list.stream().map(md -> {
            RcMaskingMetadata rcMaskingMetadata = DozerUtils.convertToNew(md, RcMaskingMetadata.class);
            rcMaskingMetadata.setMaskingConfigId(configId);
            return rcMaskingMetadata;
        }).collect(Collectors.toList());
        maskingMetadataService.saveBatch(metadatas);
    }

    private void saveWord(List<MaskingWordAddReq> list, Long configId) {
        List<RcMaskingWord> words = list.stream().map(w -> {
            RcMaskingWord rcMaskingWord = DozerUtils.convertToNew(w, RcMaskingWord.class);
            rcMaskingWord.setMaskingConfigId(configId);
            return rcMaskingWord;
        }).collect(Collectors.toList());
        maskingWordService.saveBatch(words);
    }

}