package com.suwell.plss.record.dataprocesstask;

import com.suwell.plss.framework.common.utils.SpringUtils;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import java.util.Map;
import java.util.Optional;

/**
 * 数据处理任务执行器
 *
 * <AUTHOR>
 * @since 2024/11/1 11:19
 */
public interface DataProcessTaskExecutor {

    /**
     * 获取业务类型
     */
    BusinessType getBusinessType();

    /**
     * 任务前置方法（暂未实现）
     *
     * @param taskId 任务id
     */
    void before(Long taskId);

    /**
     * 任务执行方法
     *
     * @param taskId 任务id
     */
    void execute(Long taskId);

    /**
     * 返回业务类型对应的执行器
     *
     * @param businessType 业务类型
     */
    static DataProcessTaskExecutor getExecutor(BusinessType businessType) {
        Map<String, DataProcessTaskExecutor> executorMap = SpringUtils.getBeanOfType(DataProcessTaskExecutor.class);
        Optional<DataProcessTaskExecutor> first = executorMap.values().stream()
                .filter(executor -> executor.getBusinessType().equals(businessType)).findFirst();
        return first.orElse(null);
    }

    /**
     * 批次检查（暂未实现）
     *
     * @param taskId 任务id
     * @return true表示任务继续，false表示任务结束
     */
    static boolean batchCheck(Long taskId) {
        return true;
    }

    /**
     * 批次回调（暂未实现）
     *
     * @param taskId 任务id
     */
    static void batchCallback(Long taskId) {

    }

    default void addTask(String jsonParam){

    }

}
