package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.*;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.common.utils.PageHelperUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.*;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.datapermission.enums.VisitUserType;
import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO;
import com.suwell.plss.framework.redis.idempotent.Idempotent;
import com.suwell.plss.framework.security.utils.ClassifiedUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.domain.*;
import com.suwell.plss.record.entity.*;
import com.suwell.plss.record.enums.PermissionLog.OperateType;
import com.suwell.plss.record.enums.ResourceManagerEnums.ManagerType;
import com.suwell.plss.record.mapper.RepositoryMapper;
import com.suwell.plss.record.permission.factory.PermissionValidatorFactory;
import com.suwell.plss.record.service.*;
import com.suwell.plss.record.standard.domain.FolderRecordDTO;
import com.suwell.plss.record.standard.domain.ResourceDTO;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.request.ResourcePermissionAddReq.Visitor;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordMetadataValueTypeEnum;
import com.suwell.plss.record.standard.enums.RepositoryEnum;
import com.suwell.plss.record.standard.enums.RepositoryEnum.*;
import com.suwell.plss.search.api.http.SearchRpcEntrance;
import com.suwell.plss.search.standard.dto.request.newsSearch.AggRecordCountQueryReq;
import com.suwell.plss.search.standard.dto.response.SearchAggCountResp;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.entity.SysMessage;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.SysMessageEnum;
import com.suwell.plss.system.api.service.CategoryRpcService;
import com.suwell.plss.system.api.service.MessageRpcService;
import com.suwell.plss.system.api.service.OrgRpcService;
import com.suwell.plss.system.api.service.UserRpcService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("repositoryService")
public class RepositoryServiceImpl extends ServiceImpl<RepositoryMapper, Repository> implements RepositoryService {

    private static final String PERSONAL_REPO_FMT = "用户【%s】个人库";
    private static final String AUTO_CREATE_REPO_FMT = "queryRepoIdOrSave-%s-%s";

    @Resource
    private RepositoryMapper repositoryMapper;
    @Resource
    private FolderService folderService;
    @Resource
    private FolderRelService folderRelService;
    @Resource
    private FolderRecordService folderRecordService;
    @Resource
    private RepoMetadataService repoMetadataService;
    @Resource
    private PermissionValidatorFactory validatorFactory;
    @Resource
    private PermissionService permissionService;
    @Resource
    private UserRpcService userRpcService;
    @Resource
    private RecordTypeService recordTypeService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private OrgRpcService orgRpcService;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private ResourceManagerService resourceManagerService;
    @Resource
    private ResourcePermissionLogService resourcePermissionLogService;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private FileService fileService;
    @Lazy
    @Resource
    private RecordService recordService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SearchRpcEntrance searchRpcEntrance;
    @Resource
    private ExecutorService folderQueryPool;
    @Resource
    private RepoCollectService repoCollectService;
    @Resource
    private MessageRpcService messageRpcService;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Lazy
    @Resource
    private MaterialService materialService;

    @NotNull
    private static ResourceManager getResourceManager(String ownerId, Long repoId) {
        ResourceManager manager = new ResourceManager();
        manager.setRtype(ResourceType.REPOSITORY.getCode());
        manager.setVtype(VisitUserType.USER.getCode());
        manager.setResourceId(repoId);
        manager.setVisitorId(ownerId);
        manager.setManagerType(ManagerType.OWNER.getCode());
        manager.setCreateTime(new Date());
        manager.setCreateByName(SecurityUtils.getUsername());
        manager.setCreateBy(SecurityUtils.getUserId());
        return manager;
    }

    @Override
    public RepositoryResp queryById(Long repoId) {
        Repository repository = getById(repoId);
        if (Objects.isNull(repository)) {
            return null;
        }
        RepositoryResp resp = DozerUtils.convertToNew(repository, RepositoryResp.class);
        String fileId = resp.getImageUrl();
        if (StringUtils.isNotBlank(fileId)) {
            Optional.ofNullable(fileService.getInfo(Long.parseLong(fileId)))
                    .ifPresent(info -> resp.setWebImageUrl(info.getPublicAccessUrl()));
        }
        R<SysUser> r = userRpcService.queryById(resp.getOwnerId());
        if (r.isSuccess()) {
            Optional.ofNullable(r.getData()).ifPresent(d -> resp.setOwnerName(d.getNickName()));
        }
        if (Objects.nonNull(resp.getOrgId())) {
            R<SysOrg> orgR = orgRpcService.queryById(resp.getOrgId());
            if (orgR.isSuccess()) {
                Optional.ofNullable(orgR.getData()).ifPresent(d -> resp.setOrgName(d.getOrgName()));
            }
        }
        return resp;
    }

    @Override
    public PageUtils<RepositoryResp> queryPage(RepositoryQueryReq params) {
        wrapRepositoryQueryReq(params);
        Page<Repository> page = PageHelperUtils.startPage(params.getPage(), params.getPageSize());
        List<Repository> list = repositoryMapper.pageQuery(params);
        page.close();
        List<RepositoryResp> converted = convertRespList(list, params);
        return new PageUtils<>(converted, page.getTotal(), page.getPageSize(), page.getPages());
    }

    @Override
    public PageUtils<RepositoryResp> queryMaterialRepoPage(RepositoryQueryReq params) {
        LambdaQueryWrapper<Repository> repoLqw = new LambdaQueryWrapper<>();
        repoLqw.eq(ObjectUtils.isNotEmpty(params.getCtype()), Repository::getCtype, params.getCtype())
                .eq(Repository::getStatus, RepositoryEnum.Status.NORMAL.getCode());
        Page<Repository> page = PageHelperUtils.startPage(params.getPage(), params.getPageSize());
        List<Repository> repositoryList = this.list(repoLqw);
        page.close();
        if (CollectionUtils.isEmpty(repositoryList)) {
            return new PageUtils<>(List.of(), page.getTotal(), page.getPageSize(), page.getPages());
        }
        List<RepositoryResp> converted = convertRespList(repositoryList, params);
        return new PageUtils<>(converted, page.getTotal(), page.getPageSize(), page.getPages());
    }

    @Override
    public List<RepositoryResp> queryList(RepositoryQueryReq params) {
        wrapRepositoryQueryReq(params);
        List<Repository> list = repositoryMapper.pageQuery(params);
        return convertRespList(list, params);
    }

    private void wrapRepositoryQueryReq(RepositoryQueryReq params) {
        if (Objects.isNull(params.getTenantId())) {
            params.setTenantId(SecurityUtils.getTenantId());
        }
        if (Objects.isNull(params.getUserId())) {
            params.setUserId(SecurityUtils.getUserId());
        }
        RepoQueryType byCode = RepoQueryType.findByCode(params.getQueryType());
        AssertUtils.notNull(byCode, RecordBizError.REPO_QUERY_TYPE_ILLEGAL);
        List<String> visitorIds = validatorFactory.getValidator(ResourceType.REPOSITORY).listResourceVisitorIds();
        if (RepoQueryType.MANAGEMENT == byCode || RepoQueryType.PARTICIPATE == byCode) {
            params.setVisitorIds(visitorIds);
        }
        if (RepoQueryType.ORGANIZATION == byCode) {
            List<ResourceManager> list = resourceManagerService.lambdaQuery()
                    .select(ResourceManager::getVisitorId)
                    .eq(ResourceManager::getVtype, VisitUserType.USER.getCode())
                    .list();
            List<String> managerIdList = list.stream().map(ResourceManager::getVisitorId).distinct().toList();
            R<Map<String, List<String>>> r = userRpcService.getUserDeptIdV2(managerIdList);
            if (!r.isSuccess()) {
                throw new ServiceException("获取用户单位信息失败");
            }
            Map<String, List<String>> data = r.getData();
            List<String> orgList = SecurityUtils.getOrgList();
            List<String> ownerIds = Lists.newArrayList(visitorIds);
            data.forEach((userId, orgIds) -> {
                if (orgIds.removeAll(orgList)) {
                    ownerIds.add(userId);
                }
            });
            params.setVisitorIds(ownerIds);
        }
    }

    private List<RepositoryResp> convertRespList(List<Repository> list, RepositoryQueryReq params) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 查owner名称
        List<RepositoryResp> respList = DozerUtils.convertListToNew(list, RepositoryResp.class);
        Set<String> userIds = respList.stream().map(RepositoryResp::getOwnerId).collect(Collectors.toSet());
        R<List<SysUser>> batchInfo = userRpcService.getBatchInfo(Lists.newArrayList(userIds));
        if (batchInfo.isSuccess()) {
            Map<String, SysUser> userMap = batchInfo.getData().stream()
                    .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));
            respList.forEach(resp -> {
                resp.setResourceId(resp.getId());
                resp.setResourceName(resp.getName());
                SysUser user = userMap.get(resp.getOwnerId());
                if (Objects.nonNull(user)) {
                    resp.setOwnerName(user.getNickName());
                    resp.setOwnerAvatar(user.getAvatar());
                }
            });
        }
        // 查组织名称
        Set<String> orgIds = respList.stream().map(RepositoryResp::getOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(orgIds)) {
            R<List<SysOrg>> listR = orgRpcService.queryByIdList(orgIds);
            if (listR.isSuccess()) {
                Map<String, SysOrg> orgMap = listR.getData().stream()
                        .collect(Collectors.toMap(SysOrg::getOrgId, Function.identity()));
                respList.forEach(resp -> {
                    SysOrg org = orgMap.get(resp.getOrgId());
                    if (Objects.nonNull(org)) {
                        resp.setOrgName(org.getOrgName());
                    }
                });
            }
        }
        // 查文库内文件总数
        if (params.getQueryRecordCount()) {
            boolean containsSafetyRepo = respList.stream().anyMatch(resp -> Objects.nonNull(resp.getClassified()));
            List<Long> collect = respList.stream().map(RepositoryResp::getId).toList();
            Map<Long, Long> recorCountMap = queryRepoRecordCount(collect, containsSafetyRepo)
                    .stream()
                    .collect(Collectors.toMap(FolderRecordDTO::getFolderId, FolderRecordDTO::getRecordCount));
            respList.forEach(resp -> {
                Long count = recorCountMap.getOrDefault(resp.getId(), 0L);
                resp.setRecordCount(count);
            });
            //查询素材数量
            if (CollUtil.contains(params.getCtypeList(), CType.MATERIAL.getCode())) {
                List<RepoMaterialCountDTO> matList = materialService.queryMaterialCountByRepoId(
                        collect);
                Map<Long, Long> matMap = matList.stream()
                        .collect(Collectors.toMap(RepoMaterialCountDTO::getRepoId, RepoMaterialCountDTO::getCount));
                respList.forEach(resp -> {
                    Long count = matMap.getOrDefault(resp.getId(), 0L);
                    resp.setRecordCount(count);
                });

            }
        }
        // 查询文库是否为叶子节点，用于前端判断是否可以展开
        if (params.isCheckLeaf()) {
            List<Long> repoIds = respList.stream().map(RepositoryResp::getId).toList();
            Map<Long, Long> collect = folderRelService.groupByParentId(repoIds);
            respList.forEach(resp -> {
                Long count = collect.getOrDefault(resp.getId(), 0L);
                resp.setLeaf(count <= 1);
            });
        }
        // 查询当前用户对文库的管理操作
        if (params.isManageOperateList()) {
            List<Long> repoIds = respList.stream().map(RepositoryResp::getId).toList();
            Map<Long, List<PermissionOperateResp>> map = permissionService.mapRepoManageOperate(repoIds);
            respList.forEach(resp -> {
                List<PermissionOperateResp> operateList = map.get(resp.getId());
                resp.setOperateList(operateList);
            });
        }
        return respList;
    }

    @Override
    public List<RepoCtypeGroupResp> groupCtype(RepositoryQueryReq req) {
        wrapRepositoryQueryReq(req);
        return repositoryMapper.groupCtype(req);
    }

    @Override
    @DSTransactional
    public Long addRepository(RepositoryAddReq req) {
        String tenantId = Objects.nonNull(req.getTenantId()) ? req.getTenantId() : SecurityUtils.getTenantId();
        if (!req.isPassValid()) {
            // 库名称重复校验
            Long repeatNameCount = lambdaQuery().eq(Repository::getTenantId, tenantId)
                    .eq(Repository::getName, req.getRepositoryName()).count();
            AssertUtils.isTrue(repeatNameCount == 0, RecordBizError.REPOSITORY_NAME_REPEAT);
            // 密级文库级别校验
            Integer classified = req.getClassified();
            if (Objects.nonNull(classified)) {
                AssertUtils.isTrue(ClassifiedUtils.anyMatch(classified), RecordBizError.REPO_CLASSIFIED_OVERSTEP);
            }
        }

        String userId = Objects.isNull(req.getOwnerId()) ? SecurityUtils.getUserId() : req.getOwnerId();
        Date now = new Date();
        Repository repo = new Repository();
        repo.setCreateBy(userId);
        repo.setCreateTime(now);
        repo.setModifiedBy(userId);
        repo.setModifiedTime(now);
        repo.setName(req.getRepositoryName());
        repo.setCtype(req.getCtype());
        repo.setImageUrl(req.getImageUrl());
        repo.setShareType(req.getShareType());
        repo.setStatus(NormalState.NORMAL.getCode());
        repo.setOwnerId(userId);
        repo.setTenantId(tenantId);
        repo.setShowFront(BooleanUtils.toInteger(req.isShowFront()));
        repo.setClassified(req.getClassified());
        repo.setOrgId(req.getOrgId());
        repo.setMaterialView(2);
        repositoryMapper.insert(repo);

        Long count = lambdaQuery().eq(Repository::getTenantId, tenantId).count();
        FolderRelDTO rel = new FolderRelDTO();
        rel.setAncestorId(repo.getId());
        rel.setDescendantId(repo.getId());
        rel.setTenantId(tenantId);
        rel.setOrderby(count.intValue());
        String nickname = getUserNickname(userId);
        rel.setCreateBy(userId);
        rel.setCreateByName(nickname);
        rel.setCreateTime(now);
        folderRelService.saveRelation(rel);
        // 在创建库的时候，暂不强制校验必须有权限设置
        modifyRepositoryDefaultPerm(repo.getId(), req.getPermissionRanges());
        folderRelService.lambdaUpdate()
                .set(FolderRel::getOrderby, count)
                .eq(FolderRel::getAncestorId, repo.getId()).update();
        Long repoId = repo.getId();
        // 管理员录入
        ResourceManager manager = new ResourceManager();
        manager.setVisitorId(repo.getOwnerId());
        manager.setVtype(VisitUserType.USER.getCode());
        manager.setResourceId(repoId);
        manager.setRtype(ResourceType.REPOSITORY.getCode());
        manager.setManagerType(ManagerType.OWNER.getCode());
        manager.setCreateBy(userId);
        manager.setCreateTime(now);
        manager.setCreateByName(rel.getCreateByName());
        resourceManagerService.save(manager);
        return repoId;
    }

    @Override
    @DSTransactional
    public void removeRepository(RepositoryRemoveReq req) {
        Long repositoryId = req.getRepoId();
        Repository repository = getById(repositoryId);
        AssertUtils.notNull(repository, RecordBizError.REPO_INFO_NOT_EXIST);
        // 如果当前登录人是owner对库有控制权
        if (!req.isValidPass() && !Objects.equals(SecurityUtils.getUserId(), repository.getOwnerId())) {
            throw new ServiceException("暂无权限删除库");
        }
        // 获取库中的所有目录
        List<Long> folderIds = folderRelService.listDescendantId(Lists.newArrayList(repositoryId));
        List<Long> recordIds = folderRecordService.queryRecordIdByFolderId(folderIds);
        // 库中有文件时，不允许删除
        AssertUtils.isTrue(CollectionUtil.isEmpty(recordIds), RecordBizError.REPO_REFUSE_REMOVE_CAUSE_RECORD);

        // 1 删除库下的目录信息
        FolderRemoveReq removeReq = new FolderRemoveReq();
        removeReq.setFolderIds(Lists.newArrayList(repositoryId));
        removeReq.setInnerValidPass(req.isValidPass());
        folderService.removeFolders(removeReq);
        // 2 删除库信息
        repositoryMapper.deleteById(repositoryId);
        // 3 删除库的权限
        permissionService.batchRemoveResourcePermission(folderIds);
        resourceManagerService.remove(new LambdaQueryWrapper<ResourceManager>()
                .in(ResourceManager::getResourceId, folderIds));
        // 4 删除库的元数据信息
        repoMetadataService.lambdaUpdate()
                .eq(RepoMetadata::getRepoId, repositoryId).remove();
    }

    @Override
    @DSTransactional
    public void modifyRepository(RepositoryModifyReq repository) {
        Long repoId = repository.getRepoId();
        Repository repo = repositoryMapper.selectById(repoId);
        AssertUtils.notNull(repo, RecordBizError.REPO_INFO_NOT_EXIST);

        // 权限校验
        validatorFactory.getValidator(ResourceType.REPOSITORY).validate(repoId, PermissionMask.NEW_UPLOAD_RENAME);

        AtomicBoolean modifyFlag = new AtomicBoolean(false);
        Optional.ofNullable(repository.getRepositoryName()).ifPresent(name -> {
            if (!Objects.equals(name, repo.getName())) {
                Long repeatNameCount = lambdaQuery()
                        .eq(Repository::getTenantId, repo.getTenantId())
                        .eq(Repository::getName, name)
                        .count();
                AssertUtils.isTrue(repeatNameCount == 0, RecordBizError.REPOSITORY_NAME_REPEAT);
                repo.setName(name);
                modifyFlag.set(true);
            }
        });
        Optional.ofNullable(repository.getShareType()).ifPresent(shareType -> {
            if (!Objects.equals(shareType, repo.getShareType())) {
                repo.setShareType(shareType);
                modifyFlag.set(true);
            }
        });
        Optional.ofNullable(repository.getOrgId()).ifPresent(orgId -> {
            if (!Objects.equals(orgId, repo.getOrgId())) {
                repo.setOrgId(orgId);
                modifyFlag.set(true);
            }
        });
        Optional.ofNullable(repository.getOwnerId()).ifPresent(ownerId -> {
            if (!Objects.equals(ownerId, repo.getOwnerId())) {
                // 管理员录入
                resourceManagerService.remove(new LambdaQueryWrapper<ResourceManager>()
                        .eq(ResourceManager::getResourceId, repoId)
                        .in(ResourceManager::getVisitorId, repo.getOwnerId(), ownerId));
                ResourceManager manager = getResourceManager(ownerId, repoId);
                resourceManagerService.save(manager);
                ResourcePermissionLog log = ResourcePermissionLog.fromResourceManager(manager);
                log.setOperateType(OperateType.INSERT.getCode());
                String userId = SecurityUtils.getUserId();
                String nickname = getUserNickname(userId);
                log.setOperatorId(userId);
                log.setOperator(nickname);
                resourcePermissionLogService.save(log);
                repo.setOwnerId(ownerId);
                //变更文库所有者发送消息
                SysMessage sysMessage = new SysMessage();
                sysMessage.setType(SysMessageEnum.TypeEnum.REPO_OWNER_CHANGE.getCode());
                sysMessage.setSendPersonId(userId);
                sysMessage.setAcceptPersonId(ownerId);
                sysMessage.setTitle(String.format(SysMessageEnum.TypeEnum.REPO_OWNER_CHANGE.getDec(), repo.getName()));
                sysMessage.setContent(
                        String.format(SysMessageEnum.TypeEnum.REPO_OWNER_CHANGE.getContent(), repo.getName()));
                messageRpcService.batchSaveMessage(List.of(sysMessage));

                modifyFlag.set(true);
            }
        });
        Optional.ofNullable(repository.getCtype()).ifPresent(cType -> {
            if (!Objects.equals(cType, repo.getCtype())) {
                repo.setCtype(cType);
                modifyFlag.set(true);
            }
        });
        Optional.ofNullable(repository.getImageUrl()).ifPresent(imageUrl -> {
            if (!Objects.equals(imageUrl, repo.getImageUrl())) {
                repo.setImageUrl(imageUrl);
                modifyFlag.set(true);
            }
        });
        int showFront = BooleanUtils.toInteger(repository.isShowFront());
        if (!Objects.equals(showFront, repo.getShowFront())) {
            repo.setShowFront(showFront);
            modifyFlag.set(true);
        }
        Optional.ofNullable(repository.getPermissionRanges())
                .ifPresent(ranges -> modifyRepositoryDefaultPerm(repoId, ranges));
        if (!modifyFlag.get()) {
            return;
        }
        repo.setModifiedBy(SecurityUtils.getUserId());
        repo.setModifiedTime(new Date());
        repositoryMapper.updateById(repo);
    }

    private void modifyRepositoryDefaultPerm(Long repoId, List<PermissionRange> rangeList) {
        if (CollectionUtil.isEmpty(rangeList)) {
            return;
        }
        ResourcePermissionAddReq addReq = new ResourcePermissionAddReq();
        List<Visitor> visitors = DozerUtils.convertListToNew(rangeList, Visitor.class);
        addReq.setVisitorList(visitors);
        addReq.setRtype(ResourceType.REPOSITORY.getCode());
        addReq.setResourceId(repoId);
        addReq.setAuth(false);
        permissionService.addResourcePermission(addReq);
    }

    @Override
    @DSTransactional
    public void saveMetadata(RepositoryMetadataAddReq req) {
        // 权限校验
        Long repoId = req.getRepoId();
        if (!req.isValidPass()) {
            validatorFactory.getValidator(ResourceType.REPOSITORY).validate(repoId, PermissionMask.PERMISSION_SET);
        }
        if (!req.isAppendOnly()) {
            // 先删后加
            repoMetadataService.remove(new LambdaQueryWrapper<RepoMetadata>()
                    .eq(RepoMetadata::getRepoId, repoId));
        }
        List<RepoMetadata> saveList = Lists.newArrayList();
        String userId = SecurityUtils.getUserId();
        Date now = new Date();
        // 文档类型
        Optional.ofNullable(req.getDocTypeList()).ifPresent(docTypeList -> {
            RepoMetadata docMetadata = new RepoMetadata();
            docMetadata.setType(MetaDataType.DOC_TYPE.getCode());
            docMetadata.setJsonValue(JSON.toJSONString(docTypeList));
            saveList.add(docMetadata);
        });
        // 检索类型
        Optional.ofNullable(req.getMetadataList()).ifPresent(mdList -> mdList.forEach(md -> {
            Integer retrieveType = md.getRetrieveType();
            AssertUtils.isTrue(RetrieveType.checkCode(retrieveType), RecordBizError.REPO_MD_RETRIEVE_ILLEGAL);
            RepoMetadata metadata = new RepoMetadata();
            metadata.setType(MetaDataType.RETRIEVE_TYPE.getCode());
            metadata.setRetrieveType(retrieveType);
            metadata.setJsonValue(JSON.toJSONString(md));
            saveList.add(metadata);
        }));
        // 挂载标签
        Optional.ofNullable(req.getTags()).ifPresent(tags -> tags.forEach(tag -> {
            RepoMetadata tagMD = new RepoMetadata();
            tagMD.setType(MetaDataType.TAG.getCode());
            tagMD.setJsonValue(tag.toString());
            saveList.add(tagMD);
        }));
        if (CollectionUtil.isEmpty(saveList)) {
            log.warn("user:{}, save repo:{} empty metadata", userId, repoId);
            return;
        }
        saveList.forEach(md -> {
            md.setRepoId(repoId);
            md.setCreateBy(userId);
            md.setCreateTime(now);
            md.setModifyBy(userId);
            md.setModifyTime(now);
        });
        repoMetadataService.saveBatch(saveList);
    }

    @Override
    public RepoMetadataResp queryMetadata(RepositoryMetadataQueryReq req) {
        List<RepoMetadata> list = repoMetadataService.lambdaQuery()
                .eq(RepoMetadata::getRepoId, req.getRepoId())
                .lt(RepoMetadata::getType, MetaDataType.TAG.getCode())
                .orderByAsc(RepoMetadata::getId).list();
        RepoMetadataResp resp = new RepoMetadataResp();
        resp.setRepoId(req.getRepoId());
        if (CollectionUtil.isEmpty(list)) {
            return resp;
        }
        Map<Integer, List<String>> collect = list.stream().collect(Collectors.groupingBy(RepoMetadata::getType,
                Collectors.mapping(RepoMetadata::getJsonValue, Collectors.toList())));
        List<String> docTypeJson = collect.get(MetaDataType.DOC_TYPE.getCode());
        Optional.ofNullable(docTypeJson)
                .ifPresent(json -> resp.setDocTypeList(JSON.parseArray(json.get(0), RepositoryMetadataTree.class)));
        List<String> retrieveTypeJson = collect.getOrDefault(MetaDataType.RETRIEVE_TYPE.getCode(),
                Collections.emptyList());
        List<RepositoryMetadataTree> metadataTreeList = retrieveTypeJson.stream()
                .map(jsonStr -> JSON.parseObject(jsonStr, RepositoryMetadataTree.class))
                .collect(Collectors.toList());
        resp.setMetadataList(metadataTreeList);
        showName(resp, req.isForFront());
        return resp;
    }

    @Override
    @DSTransactional
    public Long queryRepoIdOrSave(QueryOrAutoNewRepoReq req) {
        String userId = Objects.isNull(req.getUserId()) ? SecurityUtils.getUserId() : req.getUserId();
        // 个人库忽略租户，做法是固定租户为0
        String tenantId = Objects.isNull(req.getTenantId()) ? SecurityUtils.getTenantId() : req.getTenantId();
        Integer ctype = req.getCtype();
        boolean personalRepo = Objects.equals(CType.PERSONAL.getCode(), ctype);
        tenantId = personalRepo ? "0" : tenantId;
        Repository one = lambdaQuery()
                .eq(StringUtils.isNotBlank(req.getRepoName()), Repository::getName, req.getRepoName())
                .eq(personalRepo, Repository::getCreateBy, userId)
                .eq(Repository::getCtype, ctype)
                .eq(Repository::getTenantId, tenantId).one();
        if (Objects.nonNull(one)) {
            return one.getId();
        }
        RLock lock = redissonClient.getLock(String.format(AUTO_CREATE_REPO_FMT, userId, tenantId));
        if (!lock.tryLock()) {
            throw new ServiceException("重复请求，请稍后重试");
        }
        try {
            RepositoryAddReq addReq = new RepositoryAddReq();
            addReq.setCtype(ctype);
            addReq.setShareType(ShareType.CONDITION_SHARE.getCode());
            addReq.setOwnerId(userId);
            addReq.setShowFront(false);
            addReq.setTenantId(tenantId);
            if (personalRepo) {
                String repositoryName = String.format(PERSONAL_REPO_FMT, userId);
                addReq.setRepositoryName(repositoryName);
            } else {
                addReq.setRepositoryName(req.getRepoName());
            }
            return addRepository(addReq);
        } finally {
            lock.unlock();
        }
    }

    @Override
    @DSTransactional
    public void convertRepositoryToFolder(ConvertRepoToFolderReq req) {
        Long repoId = req.getRepoId();
        Repository repository = getById(repoId);
        AssertUtils.notNull(repository, RecordBizError.REPO_INFO_NOT_EXIST);
        // 如果当前登录人是owner对库有控制权
        if (!req.isValidPass()) {
            validatorFactory.getValidator(ResourceType.REPOSITORY).validate(repoId, PermissionMask.PERMISSION_SET);
        }
        Long targetId = req.getTargetId();
        Repository targetRepo = getById(targetId);
        Folder targetFolder = folderService.getById(targetId);
        if (Objects.isNull(targetFolder) && Objects.isNull(targetRepo)) {
            throw new ServiceException("目标库或目录不存在");
        }
        // 建个同库名的目录
        FolderAddReq addReq = new FolderAddReq();
        addReq.setParentId(targetId);
        addReq.setFolderName(repository.getName());
        addReq.setInnerPassCheck(true);
        Long sameFolderId = folderService.addFolder(addReq);
        // 移动库下的文件至新目录下
        List<Long> recordIds = folderRecordService.listRecordIdsByFolderIds(List.of(repoId), false);
        if (CollectionUtil.isNotEmpty(recordIds)) {
            RecordMoveReq moveReq = new RecordMoveReq();
            moveReq.setFromFolderOrRepoId(repoId);
            moveReq.setTargetFolderOrRepoId(sameFolderId);
            moveReq.setRecordIdList(recordIds);
            moveReq.setInnerValidPass(true);
            folderService.moveRecords(moveReq);
        }
        // 移动一级目录至目标下
        List<FolderRel> firstChildrenFolder = folderRelService.lambdaQuery()
                .eq(FolderRel::getAncestorId, repoId)
                .eq(FolderRel::getDistance, 1).list();
        if (CollectionUtil.isNotEmpty(firstChildrenFolder)) {
            List<Long> folderIds = firstChildrenFolder.stream().map(FolderRel::getDescendantId)
                    .collect(Collectors.toList());
            FolderMoveReq moveReq = new FolderMoveReq();
            moveReq.setTargetId(sameFolderId);
            moveReq.setFromFolderIds(folderIds);
            moveReq.setInnerValidPass(true);
            folderService.moveFolders(moveReq);
        }
        // 删除原库
        RepositoryRemoveReq removeReq = new RepositoryRemoveReq();
        removeReq.setValidPass(true);
        removeReq.setRepoId(repoId);
        removeRepository(removeReq);
    }

    @Override
    public List<RepoTagResp> listRepoTags(RepoTagQueryReq req) {
        List<RepoTagResp> list = repositoryMapper.listRepoTags(req);
        List<Long> tagIds = list.stream().map(RepoTagResp::getId).collect(Collectors.toList());
        R<List<SysCategory>> r = categoryRpcService.getByIdList(tagIds);
        if (r.isSuccess()) {
            Map<Long, String> categoryMap = r.getData().stream()
                    .collect(Collectors.toMap(SysCategory::getId, SysCategory::getName));
            list.forEach(tag -> tag.setName(categoryMap.get(tag.getId())));
        }
        return list;
    }

    @Override
    public List<RepoTagResp> listRepoRecordTags(RepoTagQueryReq req) {
        /*
         TODO 和佳桥沟通过，医疗个人库内的标签方案在下个版本换方案
         初步计划是在分类标签中，每个人创建自己的标签，然后在个人库内选择标签
         */
        return Collections.emptyList();
//        req.setMdName(FIXED_METADATA_CATEGORY.getName());
//        List<String> list = repositoryMapper.listRepoRecordTags(req);
//        List<Long> tagIds = list.stream().filter(StringUtils::isNotBlank)
//                .map(s -> s.split(","))
//                .flatMap(Arrays::stream).map(Long::parseLong).distinct().collect(Collectors.toList());
//        if (CollectionUtil.isEmpty(tagIds)) {
//            return Collections.emptyList();
//        }
//        R<List<SysCategory>> r = categoryRpcService.getByIdList(tagIds);
//        if (!r.isSuccess()) {
//            return Collections.emptyList();
//        }
//        return r.getData().stream().map(category -> {
//            RepoTagResp resp = new RepoTagResp();
//            resp.setId(category.getId());
//            resp.setName(category.getName());
//            return resp;
//        }).collect(Collectors.toList());
    }

    @Override
    public List<RepoCountResp> repoCountForCategory(RepoCountReq req) {
        RepoMetadataDTO condition = new RepoMetadataDTO(null, req.getRepoType());
        List<RepoMetadata> list = repoMetadataService.listConditionRepoMD(condition);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<Long, Long> collect = list.stream()
                .collect(Collectors.groupingBy(md -> Long.parseLong(md.getJsonValue()),
                        Collectors.counting()));
        R<List<SysCategory>> categoryR = categoryRpcService.getByIdList(Lists.newArrayList(collect.keySet()));
        if (!categoryR.isSuccess()) {
            throw new ServiceException("查询分类名称失败");
        }
        Map<Long, String> categoryMap = categoryR.getData().stream()
                .collect(Collectors.toMap(SysCategory::getId, SysCategory::getName));
        List<RepoCountResp> respList = Lists.newArrayList();
        collect.forEach((categoryId, count) -> {
            RepoCountResp resp = new RepoCountResp();
            resp.setCategoryId(categoryId);
            resp.setCategoryName(categoryMap.get(categoryId));
            resp.setRepoCount(count);
            respList.add(resp);
        });
        return respList;
    }

    @Override
    public List<RepoRecordBulkingResp> repoRecordBulking(RepoRecordBulkingReq req) {
        final String datePattern = StringUtils.isNotBlank(req.getDatePattern()) ?
                req.getDatePattern() : DateUtils.YYYY_MM_DD;
        RepoMetadataDTO condition = new RepoMetadataDTO(req.getCategoryId(), null);
        List<RepoMetadata> mdList = repoMetadataService.listConditionRepoMD(condition);
        if (CollectionUtil.isEmpty(mdList)) {
            return Collections.emptyList();
        }
        List<Long> repoIds = mdList.stream().map(RepoMetadata::getRepoId).collect(Collectors.toList());
        RepoRecordCountDTO countDTO = new RepoRecordCountDTO();
        countDTO.setRepoIds(repoIds);
        countDTO.setStartTime(req.getStartTime().toString() + " 00:00:00");
        countDTO.setEndTime(req.getEndTime().toString() + " 23:59:59");

        List<FolderRecord> folderRecordList = folderRecordService.countRepoRecord(countDTO);
        Map<String, Long> collect = folderRecordList.stream().collect(Collectors.groupingBy(
                folderRecord -> new SimpleDateFormat(datePattern).format(folderRecord.getCreateTime()),
                Collectors.counting()));

        return req.dateIntervals(datePattern).stream().map(interval -> {
            RepoRecordBulkingResp resp = new RepoRecordBulkingResp();
            resp.setDate(interval);
            resp.setBulkingCount(collect.getOrDefault(interval, 0L));
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RepoRecordCountResp> repoRecordCount(RepoRecordCountReq req) {
        RepoMetadataDTO condition = new RepoMetadataDTO();
        condition.setCategoryId(req.getCategoryId());
        List<RepoMetadata> mdList = repoMetadataService.listConditionRepoMD(condition);
        if (CollectionUtil.isEmpty(mdList)) {
            return Collections.emptyList();
        }
        // 查询库信息
        List<Long> repoIds = mdList.stream().map(RepoMetadata::getRepoId).collect(Collectors.toList());
        List<Repository> repositoryList = listByIds(repoIds);
        RepoRecordCountDTO countDTO = new RepoRecordCountDTO();
        countDTO.setRepoIds(repoIds);
        countDTO.setMdId(req.getMdId());
        List<FolderRecord> folderRecordList = folderRecordService.countRepoRecord(countDTO);
        Map<Long, Long> collect = folderRecordList.stream()
                .collect(Collectors.groupingBy(FolderRecord::getRepoId, Collectors.counting()));
        return repositoryList.stream().map(repository -> {
                    RepoRecordCountResp resp = new RepoRecordCountResp();
                    resp.setRepoId(repository.getId());
                    resp.setRepoName(repository.getName());
                    resp.setRecordCount(collect.getOrDefault(repository.getId(), 0L));
                    return resp;
                }).sorted(Comparator.comparing(RepoRecordCountResp::getRecordCount).reversed())
                .limit(10).collect(Collectors.toList());
    }

    @Override
    public List<RepoInfoResp> listRepoInfo(Long recordId) {
        List<MqRecordPathDTO> recordRel = recordService.getRecordRel(Lists.newArrayList(recordId));
        return recordRel.stream().map(MqRecordPathDTO::getRelList)
                .flatMap(Collection::stream).map(rel -> {
                    RepositoryResp repositoryResp = queryById(rel.getRepoId());
                    RepoInfoResp resp = new RepoInfoResp();
                    resp.setId(repositoryResp.getId());
                    resp.setName(repositoryResp.getName());
                    resp.setOwnerId(repositoryResp.getOwnerId());
                    resp.setOwnerName(repositoryResp.getOwnerName());
                    resp.setCtype(repositoryResp.getCtype());
                    resp.setShareType(repositoryResp.getShareType());
                    return resp;
                }).collect(Collectors.toList());
    }

    @Override
    public RepositoryCountResp statisticRepoCount(Long tenantId) {
        return repositoryMapper.statisticRepoCount(tenantId);
    }

    /**
     * 回显时展示的名称
     */
    @SuppressWarnings("unchecked")
    private void showName(RepoMetadataResp resp, boolean forFront) {
        // 文档类型名称
        fillDocTypeName(resp.getDocTypeList(), forFront);
        // 检索项名称
        CompletableFuture<Void>[] futures = resp.getMetadataList().stream().map(retrieveMetadata ->
                        CompletableFuture.runAsync(() ->
                                fillSearchName(retrieveMetadata, forFront, resp.getRepoId()), folderQueryPool))
                .toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(futures).join();
    }

    private void fillDocTypeName(List<RepositoryMetadataTree> docTypeList, boolean forFront) {
        if (CollectionUtil.isEmpty(docTypeList)) {
            return;
        }
        List<Long> docTypeIds = docTypeList.stream().map(RepositoryMetadataTree::getNodeId)
                .collect(Collectors.toList());
        Map<Long, List<RepositoryMetadataTree>> childrenMap;
        if (forFront) {
            RecordTypeQueryReq req = new RecordTypeQueryReq();
            req.setRecordTypeIds(docTypeIds);
            req.setQuerySearchMd(true);
            req.setQueryListMd(false);
            List<RecordTypeMetadataDTO> list = recordTypeMetadataService.queryMetadata(req);
            childrenMap = list.stream()
                    .collect(Collectors.groupingBy(RecordTypeMetadataDTO::getRecordtypeId,
                            Collectors.mapping(rmd -> {
                                RepositoryMetadataTree tree = new RepositoryMetadataTree();
                                tree.setNodeId(rmd.getMdId());
                                String mdName = rmd.getMdName();
                                String aliasName = rmd.getAliasName();
                                if(StringUtils.isNotEmpty(aliasName)){
                                    tree.setNodeName(aliasName);
                                }else{
                                    tree.setNodeName(mdName);
                                }
                                return tree;
                            }, Collectors.toList())));
        } else {
            childrenMap = Maps.newHashMap();
        }
        Map<Long, RecordType> docTypeMap = recordTypeService.listByIds(docTypeIds).stream()
                .collect(Collectors.toMap(RecordType::getId, Function.identity()));
        docTypeList.forEach(docType -> {
            Long nodeId = docType.getNodeId();
            RecordType node = docTypeMap.get(nodeId);
            docType.setChildren(childrenMap.getOrDefault(nodeId, Collections.emptyList()));
            Optional.ofNullable(node).ifPresent(recordType -> docType.setNodeName(recordType.getName()));
        });
    }

    private void fillSearchName(RepositoryMetadataTree retrieveMetadata, boolean forFront, Long repoId) {
        if (retrieveMetadata.isFixed()) {
            return;
        }
        List<Long> nodeIds = Lists.newArrayList();
        recursiveCollectNodeId(nodeIds, retrieveMetadata);
        if (CollectionUtil.isEmpty(nodeIds)) {
            return;
        }
        boolean levelSwitch = retrieveMetadata.isLevelSwitch();
        List<Integer> levels = retrieveMetadata.getLevels();
        Map<Long, String> nameMap = null;
        Integer retrieveType = retrieveMetadata.getRetrieveType();
        switch (RetrieveType.findByCode(retrieveType)) {
            case METADATA:
                if (forFront) {
                    RecordTypeMetadata one = recordTypeMetadataService.lambdaQuery()
                            .eq(RecordTypeMetadata::getRecordtypeId, retrieveMetadata.getExtraBindId())
                            .eq(RecordTypeMetadata::getMdId, retrieveMetadata.getNodeId()).one();
                    if (Objects.isNull(one)) {
                        return;
                    }
                    List<RepositoryMetadataTree> children = new ArrayList<>();
                    if (one.getValueType()
                        >= RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_LIST.getCode()) {
                        String substring = one.getValueRange().substring(2, one.getValueRange().length() - 2);
                        children = Arrays.stream(substring.split("\\|")).map(s -> {
                            RepositoryMetadataTree child = new RepositoryMetadataTree();
                            child.setNodeName(s);
                            return child;
                        }).collect(Collectors.toList());
                    } else {
                        if (Objects.equals(one.getMdName(), plssFixedMetadataNameConfig.getFixedYearName())) {
                            //年份元数据
                            R<List<String>> yearListR = searchRpcEntrance.getYearListByRepoId(repoId);
                            if (yearListR.isSuccess()) {
                                List<String> yearList = yearListR.getData();
                                children = yearList.stream().map(v -> {
                                    RepositoryMetadataTree child = new RepositoryMetadataTree();
                                    child.setNodeName(v);
                                    return child;
                                }).collect(Collectors.toList());
                            }
                        }
                    }
                    retrieveMetadata.setChildren(children);
                }
                nameMap = metadataService.listByIds(nodeIds).stream()
                        .collect(Collectors.toMap(Metadata::getId, Metadata::getName));
                break;
            case FOLDER:
                if (levelSwitch) {
                    List<FolderTreeResp> folderTree = folderService.queryFolderTreeByLevel(
                            retrieveMetadata.getNodeId(),
                            levels);
                    List<RepositoryMetadataTree> treeList = recursiveFolderTree(folderTree);
                    retrieveMetadata.setChildren(treeList);
                    return;
                }
                nameMap = folderService.listByIds(nodeIds).stream()
                        .collect(Collectors.toMap(Folder::getId, Folder::getName));
                break;
            case ORGANIZATION:
                if (levelSwitch) {
                    R<List<SysOrg>> orgTreeR = orgRpcService.queryOrgTreeByLevel(retrieveMetadata.getNodeId()+"",
                            levels);
                    if (orgTreeR.isSuccess()) {
                        List<RepositoryMetadataTree> treeList = recursiveOrgTree(orgTreeR.getData());
                        retrieveMetadata.setChildren(treeList);
                    } else {
                        log.warn("根据层级查询单位子树失败，parentId:{},levels:{}", retrieveMetadata.getNodeId(),
                                levels);
                    }
                    return;
                }
                R<List<SysOrg>> orgR = orgRpcService.queryByIdList(nodeIds.stream().map(String::valueOf).collect(Collectors.toList()));
                if (orgR.isSuccess()) {
                    nameMap = orgR.getData().stream()
                            .collect(Collectors.toMap(s-> Convert.toLong(s.getOrgId()), SysOrg::getOrgName));
                } else {
                    log.warn("根据单位id查询单位列表失败，params:{}", nodeIds);
                }
                break;
            case CATEGORY:
                if (levelSwitch) {
                    R<List<SysCategory>> categoryTreeR = categoryRpcService.queryCategoryTreeByLevel(
                            retrieveMetadata.getNodeId(), levels);
                    if (categoryTreeR.isSuccess()) {
                        List<RepositoryMetadataTree> treeList = recursiveCategoryTree(categoryTreeR.getData());
                        retrieveMetadata.setChildren(treeList);
                    } else {
                        log.warn("查询分类树失败:{}", categoryTreeR.getMsg());
                    }
                    return;
                }
                R<List<SysCategory>> cateGoryR = categoryRpcService.getByIdList(nodeIds);
                if (cateGoryR.isSuccess()) {
                    nameMap = cateGoryR.getData().stream()
                            .collect(Collectors.toMap(SysCategory::getId, SysCategory::getName));
                } else {
                    log.warn("根据分类id查询分类列表失败:{}", cateGoryR.getMsg());
                }
                break;
        }
        if (CollectionUtil.isEmpty(nameMap)) {
            return;
        }
        recursiveFillName(retrieveMetadata, nameMap);
    }

    private void recursiveCollectNodeId(List<Long> nodeIds, RepositoryMetadataTree tree) {
        if (Objects.isNull(tree)) {
            return;
        }
        nodeIds.add(tree.getNodeId());
        Optional.ofNullable(tree.getChildren())
                .ifPresent(children -> children.forEach(md -> recursiveCollectNodeId(nodeIds, md)));
    }

    private void recursiveFillName(RepositoryMetadataTree treeNode, Map<Long, String> nameMap) {
        if (Objects.isNull(treeNode)) {
            return;
        }
        // 这里优先使用已经配置的名称，用于适配对于前端展示的和后端配置不一致的情况
        if (StringUtils.isEmpty(treeNode.getNodeName())) {
            treeNode.setNodeName(nameMap.get(treeNode.getNodeId()));
        }
        List<RepositoryMetadataTree> children = treeNode.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        children.forEach(node -> recursiveFillName(node, nameMap));
    }

    private List<RepositoryMetadataTree> recursiveFolderTree(List<FolderTreeResp> folderTree) {
        if (CollectionUtil.isEmpty(folderTree)) {
            return Collections.emptyList();
        }
        return folderTree.stream().map(folder -> {
            RepositoryMetadataTree tree = new RepositoryMetadataTree();
            tree.setNodeId(folder.getId());
            tree.setNodeName(folder.getName());
            List<RepositoryMetadataTree> treeList = recursiveFolderTree(folder.getChildren());
            tree.setChildren(treeList);
            return tree;
        }).collect(Collectors.toList());
    }

    private List<RepositoryMetadataTree> recursiveOrgTree(List<SysOrg> orgTree) {
        if (CollectionUtil.isEmpty(orgTree)) {
            return Collections.emptyList();
        }
        return orgTree.stream().map(org -> {
            RepositoryMetadataTree tree = new RepositoryMetadataTree();
            tree.setNodeId(Convert.toLong(org.getOrgId()));
            tree.setNodeName(org.getOrgName());
            List<RepositoryMetadataTree> treeList = recursiveOrgTree(org.getChildren());
            tree.setChildren(treeList);
            return tree;
        }).collect(Collectors.toList());
    }

    private List<RepositoryMetadataTree> recursiveCategoryTree(List<SysCategory> categoryTree) {
        if (CollectionUtil.isEmpty(categoryTree)) {
            return Collections.emptyList();
        }
        return categoryTree.stream().map(category -> {
            RepositoryMetadataTree tree = new RepositoryMetadataTree();
            tree.setNodeId(category.getId());
            tree.setNodeName(category.getName());
            List<RepositoryMetadataTree> treeList = recursiveCategoryTree(category.getChildren());
            tree.setChildren(treeList);
            return tree;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RepoInfoResp> listRepoBasicInfoByNames(List<String> repoNameList) {
        LambdaQueryWrapper<Repository> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Repository::getStatus, NormalState.NORMAL.getCode())
                .in(Repository::getName, repoNameList);
        List<Repository> repositoryList = this.list(queryWrapper);
        List<RepoInfoResp> infoList = new ArrayList<>(repositoryList.size());
        if (CollectionUtils.isEmpty(repositoryList)) {
            return infoList;
        }
        for (Repository s : repositoryList) {
            RepoInfoResp infoResp = new RepoInfoResp();
            infoResp.setId(s.getId());
            infoResp.setName(s.getName());
            infoResp.setOwnerId(s.getOwnerId());
            infoList.add(infoResp);
        }
        return infoList;
    }

    @Override
    public boolean containsByRepoCtype(ContainsCtypeReq req) {
        Long count = lambdaQuery().in(Repository::getCtype, req.getRepoTypeList())
                .in(Repository::getId, req.getResourceIdList())
                .count();
        if (count > 0) {
            return true;
        }
        List<Long> list = folderRelService.listAncestorId(req.getResourceIdList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        count = lambdaQuery().in(Repository::getCtype, req.getRepoTypeList())
                .in(Repository::getId, list)
                .count();
        return count > 0;
    }

    private String getUserNickname(String userId) {
        R<SysUser> r = userRpcService.queryById(userId);
        SysUser data = r.getData();
        if (!r.isSuccess() || Objects.isNull(data)) {
            throw new ServiceException("获取用户信息失败");
        }
        return data.getNickName();
    }

    @Override
    @DSTransactional
    public void fixMyFolderForRepeat() {
        Long count = lambdaQuery().eq(Repository::getCtype, CType.PERSONAL.getCode()).count();
        long pages = count % 300 == 0 ? count / 300 : count / 300 + 1;
        for (int i = 1; i <= pages; i++) {
            handleRepeatMyFolder(i);
        }
    }

    @Override
    public List<ResourceSearchResp> resourceSearch(ResourceSearchReq req) {
        if (CollectionUtil.isEmpty(req.getVisitorIds())) {
            List<String> visitorIds = validatorFactory.getValidator(ResourceType.REPOSITORY).listResourceVisitorIds();
            req.setVisitorIds(visitorIds);
        }
        List<ResourceSearchResp> repoList = repositoryMapper.repoSearchManage(req);
        repoList.forEach(resp -> resp.setFolderChain(List.of(resp.cloneBase())));
        List<ResourceSearchResp> folderList = repositoryMapper.repoSearchFolder(req);
        List<Long> folderIds = folderList.stream().map(ResourceSearchResp::getResourceId).toList();
        List<FolderRelResp> folderRelList = folderService.queryRelations(folderIds);
        Map<Long, List<ResourceDTO>> collect = folderRelList.stream()
                .collect(Collectors.toMap(FolderRelResp::getFolderId, FolderRelResp::getFolderChain));
        folderList.forEach(folder -> {
            List<ResourceDTO> list = collect.getOrDefault(folder.getResourceId(), List.of());
            List<ResourceSearchResp> chainList = list.stream()
                    .map(ResourceConverter.convertSearchResp())
                    .toList();
            chainList.get(0).setCollectTime(folder.getCollectTime());
            folder.setFolderChain(chainList);
        });
        repoList.addAll(folderList);
        if (req.isForTree()) {
            return buildSearchTree(repoList);
        }
        return repoList;
    }

    @Override
    @Idempotent
    public void repoCollect(RepoCollectReq req) {
        Long repoId = req.getRepoId();
        Repository repository = getById(repoId);
        AssertUtils.notNull(repository, RecordBizError.REPO_INFO_NOT_EXIST);
        String userId = Objects.isNull(req.getUserId()) ? SecurityUtils.getUserId() : req.getUserId();
        RepoCollect one = repoCollectService.lambdaQuery()
                .eq(RepoCollect::getUserId, userId)
                .eq(RepoCollect::getRepoId, repoId)
                .one();
        if (Objects.isNull(one)) {
            RepoCollect collect = new RepoCollect();
            collect.setRepoId(repoId);
            collect.setUserId(userId);
            collect.setCreateTime(new Date());
            repoCollectService.save(collect);
        } else {
            repoCollectService.lambdaUpdate()
                    .eq(RepoCollect::getUserId, userId)
                    .eq(RepoCollect::getRepoId, repoId)
                    .remove();
        }
    }

    @Override
    public void fillRepoOrgId() {
        List<Repository> list = listNeedFillOrg();
        if (CollectionUtil.isEmpty(list)) {
            log.info("无需填充组织id");
            return;
        }
        Set<String> userIds = list.stream().map(Repository::getOwnerId).collect(Collectors.toSet());
        R<Map<String, String>> mapR = userRpcService.mapUserOrg(userIds);
        if (!mapR.isSuccess()) {
            throw new ServiceException("查询用户组织信息失败");
        }
        Map<String, String> map = mapR.getData();
        for (Repository repository : list) {
            String orgId = map.get(repository.getOwnerId());
            if (Objects.nonNull(orgId)) {
                repository.setOrgId(orgId);
            }
        }
        updateBatchById(list, 300);
    }

    @Override
    public List<StoreRankStatisticByOrgResp> storeStatisticByOrg(StoreRankStatisticByOrgReq req) {
        List<Repository> list = lambdaQuery()
                .select(Repository::getId, Repository::getOrgId)
                .in(Repository::getCtype, CType.PRIVATE.getCode(), CType.COMMON.getCode())
                .eq(Objects.nonNull(req.getTenantId()), Repository::getTenantId, req.getTenantId())
                .isNotNull(Repository::getOrgId)
                .list();
        if (CollectionUtil.isEmpty(list)) {
            return List.of();
        }
        // orgId -> repoId
        Map<String, List<Long>> orgRepoMap = list.stream().collect(Collectors.groupingBy(Repository::getOrgId,
                Collectors.mapping(Repository::getId, Collectors.toList())));
        List<Long> repoIdList = list.stream().map(Repository::getId).toList();
        AggRecordCountQueryReq queryReq = new AggRecordCountQueryReq();
        queryReq.setRepoIds(repoIdList);
        queryReq.setAggField("repoFolders.repoId");
        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRecordCount(queryReq);
        if (listR.isError()) {
            throw new ServiceException("查询库下文件数量失败");
        }
        Map<Long, Long> repoRecordCountMap = listR.getData().stream()
                .collect(Collectors.toMap(SearchAggCountResp::getId, SearchAggCountResp::getCount));
        List<StoreRankStatisticByOrgResp> respList = Lists.newArrayList();
        orgRepoMap.forEach((orgId, repoIds) -> {
            StoreRankStatisticByOrgResp resp = new StoreRankStatisticByOrgResp();
            resp.setOrgId(orgId);
            long count = repoIds.stream().map(repoRecordCountMap::get).filter(Objects::nonNull)
                    .mapToLong(Long::longValue).sum();
            resp.setNum(count);
            respList.add(resp);
        });
        return respList;
    }

    @Override
    public void repoOrgPreFill(HttpServletResponse response) {
        List<Repository> list = listNeedFillOrg();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<RepositoryResp> respList = list.stream().map(repo -> {
            RepositoryResp resp = new RepositoryResp();
            resp.setId(repo.getId());
            resp.setName(repo.getName());
            resp.setOwnerId(repo.getOwnerId());
            return resp;
        }).toList();
        Set<String> ownerIds = respList.stream().map(RepositoryResp::getOwnerId).collect(Collectors.toSet());
        R<List<SysUser>> listR = userRpcService.listUserOrgForRepo(ownerIds);
        if (listR.isError()) {
            throw new ServiceException("查询用户组织信息失败");
        }
        Map<String, SysUser> collect = listR.getData().stream()
                .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));
        for (RepositoryResp repo : respList) {
            SysUser sysUser = collect.get(repo.getOwnerId());
            if (Objects.nonNull(sysUser)) {
                repo.setOwnerName(sysUser.getNickName());
                repo.setOrgId(sysUser.getOrgId());
                repo.setOrgName(sysUser.getOrgName());
            }
        }
        ExcelUtil<RepositoryResp> excelUtil = new ExcelUtil<>();
        excelUtil.exportExcel(response, respList, "库组织信息");
    }

    @Override
    @DSTransactional
    @SneakyThrows
    public void importRepoOrgBatch(MultipartFile file) {
        if (!SecurityUtils.isAdmin()) {
            throw new ServiceException("无权限操作");
        }
        ExcelUtil<RepositoryResp> excelUtil = new ExcelUtil<>(RepositoryResp.class);
        try (InputStream is = file.getInputStream()) {
            List<RepositoryResp> respList = excelUtil.importExcel(is);
            if (CollectionUtil.isEmpty(respList)) {
                throw new ServiceException("导入数据为空");
            }
            List<Repository> updateList = respList.stream().map(resp -> {
                if (Objects.isNull(resp.getOrgId()) || Objects.isNull(resp.getId())) {
                    return null;
                }
                Repository repository = new Repository();
                repository.setId(resp.getId());
                repository.setOrgId(resp.getOrgId());
                return repository;
            }).filter(Objects::nonNull).toList();
            updateBatchById(updateList, 200);
        }
    }

    private List<Repository> listNeedFillOrg() {
        return lambdaQuery()
                .select(Repository::getId, Repository::getName, Repository::getOwnerId)
                .ne(Repository::getShareType, ShareType.ANY_SHARE.getCode())
                .ne(Repository::getCtype, CType.PERSONAL.getCode())
                .isNull(Repository::getOrgId).list();
    }


    private List<ResourceSearchResp> buildSearchTree(List<ResourceSearchResp> list) {
        // 先根据库分组在合并树，这样保证路径一定是有效的
        Map<ResourceSearchResp, List<List<ResourceSearchResp>>> collect = list.stream().collect(
                Collectors.groupingBy(resp -> resp.getFolderChain().get(0),
                        Collectors.mapping(ResourceSearchResp::getFolderChain, Collectors.toList())));
        return collect.values().stream().map(this::chainToTree).collect(Collectors.toList());
    }

    private ResourceSearchResp chainToTree(List<List<ResourceSearchResp>> chains) {
        Map<Long, ResourceSearchResp> nodeMap = Maps.newHashMap();
        ResourceSearchResp tree = null;
        for (List<ResourceSearchResp> chain : chains) {
            ResourceSearchResp parent = null;
            for (ResourceSearchResp resp : chain) {
                ResourceSearchResp currentNode = nodeMap.computeIfAbsent(resp.getResourceId(),
                        resourceId -> resp.cloneBase());
                if (Objects.isNull(parent)) {
                    tree = currentNode;
                } else {
                    List<ResourceSearchResp> children = parent.getChildren();
                    if (!children.contains(currentNode)) {
                        children.add(currentNode);
                    }
                }
                parent = currentNode;
            }
        }
        return tree;
    }

    private void handleRepeatMyFolder(int page) {
        List<Repository> repositoryList = lambdaQuery()
                .select(Repository::getId)
                .eq(Repository::getCtype, CType.PERSONAL.getCode())
                .orderByAsc(Repository::getId)
                .last(String.format("limit %s offset %s", 300, (page - 1) * 300))
                .list();
        List<Long> repoIds = repositoryList.stream().map(Repository::getId).toList();
        // 查询 我的文档
        List<Folder> folderList = folderService.listMyFolderByRepoIds(repoIds);
        Map<Long, List<Folder>> collect = folderList.stream().collect(Collectors.groupingBy(Folder::getRepoId));
        collect.forEach((repoId, folders) -> {
            if (CollectionUtil.isEmpty(folders)) {
                return;
            }
            if (folders.size() == 1) {
                Folder folder = folders.get(0);
                if ("0".equals(folder.getTenantId())) {
                    return;
                }
                folder.setTenantId("0");
                folderService.updateById(folder);
                folderRelService.lambdaUpdate()
                        .set(FolderRel::getTenantId, 0L)
                        .eq(FolderRel::getAncestorId, repoId)
                        .eq(FolderRel::getDescendantId, folder.getId())
                        .update();
                return;
            }
            // 这里size一定为2
            Folder failFolder;
            Folder remainFolder;
            if ("0".equals(folders.get(0).getTenantId())) {
                remainFolder = folders.get(0);
                failFolder = folders.get(1);
            } else {
                remainFolder = folders.get(1);
                failFolder = folders.get(0);
            }
            // 将文件移动到租户id为0的“我的文档”下
            List<Long> recordIdList = folderRecordService.queryRecordIdByFolderId(List.of(failFolder.getId()));
            if (CollectionUtil.isNotEmpty(recordIdList)) {
                RecordMoveReq moveReq = new RecordMoveReq();
                moveReq.setInnerValidPass(true);
                moveReq.setFromFolderOrRepoId(failFolder.getId());
                moveReq.setTargetFolderOrRepoId(remainFolder.getId());
                moveReq.setRecordIdList(recordIdList);
                moveReq.setUserId(remainFolder.getCreateBy());
                folderService.moveRecords(moveReq);
            }
            // 将目录移动到租户id为0的“我的文档”下
            List<FolderRel> relList = folderRelService.lambdaQuery()
                    .eq(FolderRel::getAncestorId, failFolder.getId())
                    .ne(FolderRel::getDistance, 0)
                    .list();
            if (CollectionUtil.isNotEmpty(relList)) {
                List<Long> list = relList.stream().map(FolderRel::getDescendantId).toList();
                FolderMoveReq moveReq = new FolderMoveReq();
                moveReq.setInnerValidPass(true);
                moveReq.setFromFolderIds(list);
                moveReq.setTargetId(remainFolder.getId());
                moveReq.setUserId(remainFolder.getCreateBy());
                folderService.moveFolders(moveReq);
            }
            // 删除租户不为0的“我的文档”和关系
            FolderRemoveReq removeReq = new FolderRemoveReq();
            removeReq.setFolderIds(List.of(failFolder.getId()));
            removeReq.setInnerValidPass(true);
            removeReq.setForceRemove(true);
            folderService.removeFolders(removeReq);
        });
    }

    @DSTransactional
    public boolean saveOrUpdateBatchById(List<Repository> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }

        if (isUpdate) {
            for (int i = 0; i < entityList.size(); i += DEFAULT_BATCH_SIZE) {
                List<Repository> batch = entityList.subList(i, Math.min(i + DEFAULT_BATCH_SIZE, entityList.size()));
                saveOrUpdateBatch(batch);
            }
            return true;
        } else {
            return entityList.stream().allMatch(entity -> {
                Repository repository = repositoryMapper.selectById(entity.getId());
                if (repository == null) {
                    return repositoryMapper.insert(entity) > 0;
                }
                return true;
            });
        }
    }

    @Override
    public boolean saveOrUpdateBatch(List<Repository> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }
        entityList.forEach(entity -> {
            QueryWrapper<Repository> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", entity.getId());

            long count = repositoryMapper.selectCount(queryWrapper);
            if (count > 0) {
                if (!isUpdate) {
                    return;
                }
                update(entity, queryWrapper);
            } else {
                save(entity);
            }
        });
        return true;
    }

    @Override
    public List<RepositoryResp> listByIdsWithSort(List<Long> repoIds, SortType sortType) {
        if (CollectionUtils.isEmpty(repoIds) || Objects.isNull(sortType)) {
            return List.of();
        }
        return switch (sortType) {
            case COLLECT -> repositoryMapper.listByIdsSortByCollect(repoIds, SecurityUtils.getUserId());
            case NAME_ASC -> {
                List<Repository> repositoryList = listByIds(repoIds);
                repositoryList.sort(Comparator.comparing(Repository::getName, Collator.getInstance(Locale.CHINESE)));
                yield DozerUtils.convertListToNew(repositoryList, RepositoryResp.class);
            }
        };
    }


    @Override
    public List<MetadataResp> listRepoRecordMd(Long repoId) {
        AggRecordCountQueryReq req = new AggRecordCountQueryReq();
        req.setRepoIds(List.of(repoId));
        req.setAggField("recordTypeId");
        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRecordCount(req);
        if (listR.isError()) {
            throw new ServiceException("查询文档类型失败");
        }
        List<Long> recordTypeIds = listR.getData().stream().map(SearchAggCountResp::getId).toList();
        List<RecordTypeMetadataDTO> list = recordTypeMetadataService.listMdByRecordTypeIds(recordTypeIds);
        return list.stream().map(rd -> {
            MetadataResp resp = new MetadataResp();
            resp.setId(rd.getMdId());
            resp.setName(rd.getMdName());
            resp.setValueType(rd.getValueType());
            resp.setFixedDataEdit(null);
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FolderRecordDTO> queryRepoRecordCount(List<Long> repoIds, boolean safetyQuery) {
        if (CollectionUtil.isEmpty(repoIds)) {
            return List.of();
        }
        AggRecordCountQueryReq req = new AggRecordCountQueryReq();
        req.setRepoIds(repoIds);
        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRepoRecordCount(req);
        if (listR.isError()) {
            throw new ServiceException("查询文档数量失败");
        }
        List<SearchAggCountResp> data = listR.getData();
        if (CollectionUtil.isEmpty(data)) {
            return List.of();
        }
        return data.stream().map(resp -> {
            FolderRecordDTO dto = new FolderRecordDTO();
            dto.setFolderId(resp.getId());
            dto.setRecordCount(resp.getCount());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void modifyMaterialViewRepository(RepositoryMaterialViewReq req) {
        Long repoId = req.getRepoId();
        Repository repo = repositoryMapper.selectById(repoId);
        AssertUtils.notNull(repo, RecordBizError.REPO_INFO_NOT_EXIST);

        // 权限校验
        validatorFactory.getValidator(ResourceType.REPOSITORY).validate(repoId, PermissionMask.NEW_UPLOAD_RENAME);
        repo.setMaterialView(req.getMaterialView());
        repo.setModifiedBy(SecurityUtils.getUserId());
        repo.setModifiedTime(new Date());
        repositoryMapper.updateById(repo);
    }

}