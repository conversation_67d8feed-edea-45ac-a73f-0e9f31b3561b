package com.suwell.plss.record.conf;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 摘要的配置规则
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "dp.feature.cat.summary")
public class FeatureCutSummaryConfig {

    /**
     * json字符串,示例
     * {"cutLength":300,"distanceLength":50}
     */
    private String configKey;

    /**
     * 解析配置成对象
     *
     * @param configKey 配置json
     * @return
     */
    public CutSummaryProperties getCutSummaryConfig(String configKey) {
        return JSON.parseObject(configKey, CutSummaryProperties.class);
    }

}
