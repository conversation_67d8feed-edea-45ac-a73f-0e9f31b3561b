package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.FILE_2_OFD;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordConvertProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyConvertOfdApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 异步调用 获取ofd
 *
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyConvertOfdAsyncApiImpl implements ThirdPartyConvertOfdApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getConvertOfd(MqRecordReqDTO mqRecordReqDTO) {
        log.info("异步发送转换消息 async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordConvertProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), FILE_2_OFD));
        // TODO: 2023/10/22 上流程配置方案后 开启 
//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(FILE_2_OFD));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordConvertProcessEvent<>(mqRecordReqDTO, FILE_2_OFD)));
    }
}
