package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.service.ForbiddenSearchinfoFacade;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/6/27
 * @content
 */
@RestController
@RequestMapping("/v1/forbiddenSearchinfo")
public class ForbiddenSearchinfoController {

    @Resource
    private ForbiddenSearchinfoFacade forbiddenSearchinfoFacade;

    @PostMapping(value = {"/searchRecord","/searchRecord/{fcq}","/searchRecord/{fcq}/{num}","/searchRecordLimitNum/{num}"})
    public R<Void> searchRecord(@PathVariable(required = false,name = "fcq") String fcq,@PathVariable(required = false,name="num") Integer num, @RequestBody List<String> forbiddenWords){
        forbiddenSearchinfoFacade.searchRecord(forbiddenWords,fcq,num,1);
        return R.ok();
    }

    @PostMapping(value = {"/searchRecordByEntity","/searchRecordByEntity/{num}"})
    public R<Void> searchRecordByEntity(@PathVariable(required = false,name="num") Integer num,@RequestBody List<String> forbiddenWords){
        forbiddenSearchinfoFacade.searchRecord(forbiddenWords,null,num,2);
        return R.ok();
    }

    @PostMapping(value = {"/markRecordByCorrupt", "/markRecordByCorrupt/{analyzer}"})
    public R<Void> markRecord(@PathVariable(required = false, name = "analyzer") String analyzer, @RequestBody List<String> forbiddenWords){
        forbiddenSearchinfoFacade.searchBothRecord(forbiddenWords, analyzer);
        return R.ok();
    }

    @PostMapping(value = "/deleteRecord")
    public R<Void> deleteRecord(@RequestBody List<Long> recordIds){
        forbiddenSearchinfoFacade.deleteRecord(recordIds,false);
        return R.ok();
    }

    @PostMapping(value = {"/deleteRecordByforbiddenWordId","/deleteRecordByforbiddenWordId/{repeat}"})
    public R<Void> deleteRecordByforbiddenWordId(@PathVariable(required = false,name="repeat") Boolean repeat,@RequestBody List<Long> forbiddenWordIds){
        forbiddenSearchinfoFacade.deleteRecordByforbiddenWordId(forbiddenWordIds,false,repeat);
        return R.ok();
    }

    @PostMapping(value = "/asyncDeleteRecord")
    public R<Void> syncDeleteRecord(@RequestBody List<Long> recordIds){
        forbiddenSearchinfoFacade.deleteRecord(recordIds,true);
        return R.ok();
    }

    @PostMapping(value = {"/asyncDeleteRecordByforbiddenWordId","/asyncDeleteRecordByforbiddenWordId/{repeat}"})
    public R<Void> syncDeleteRecordByforbiddenWordId(@PathVariable(required = false,name="repeat") Boolean repeat,@RequestBody List<Long> forbiddenWordIds){
        forbiddenSearchinfoFacade.deleteRecordByforbiddenWordId(forbiddenWordIds,true,repeat);
        return R.ok();
    }

    @PostMapping(value = {"/syncDeleteRecordByforbiddenWords","/syncDeleteRecordByforbiddenWords/{repeat}"})
    public R<Void> deleteRecordByforbiddenWords(@PathVariable(required = false,name="repeat") Boolean repeat,@RequestBody List<String> forbiddenWords){
        forbiddenSearchinfoFacade.deleteRecordByforbiddenWords(forbiddenWords,true,repeat);
        return R.ok();
    }
}
