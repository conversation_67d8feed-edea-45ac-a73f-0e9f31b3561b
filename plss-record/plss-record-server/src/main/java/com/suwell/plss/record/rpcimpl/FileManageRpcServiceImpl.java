package com.suwell.plss.record.rpcimpl;

import com.suwell.plss.framework.common.constant.HttpStatus;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.ServletUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.security.auth.AuthUtil;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.request.ReplaceRecordReq;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.TempUrlResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.service.StandardFileFacade;
import com.suwell.plss.system.api.domain.LoginUser;
import feign.Response;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(FileManageRpcService.INNER_PREFIX)
public class FileManageRpcServiceImpl implements FileManageRpcService {

    @Resource
    private StandardFileFacade standardFileFacade;


    @Override
    public R<UploadResp> generalUpload(UploadReq req) {
        UploadResp resp = standardFileFacade.generalUpload(req);
        return R.ok(resp);
    }

    @Override
    public Response downloadFile(Long fileId) {
        standardFileFacade.downloadFile(fileId, ServletUtils.getResponse());
        return null;
    }

    @Override
    public Response downloadRecordFile(Long recordId, Integer type) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        HttpServletResponse response = ServletUtils.getResponse();
        if (type == 0) {
            String fileExt = standardFileFacade.typeFileExt(recordId, 1);
            if (StringUtils.isEmpty(fileExt) ||
                    (!Arrays.asList("doc", "docx").contains(fileExt.toLowerCase(Locale.ENGLISH)))) {
                response.setStatus(HttpStatus.NOT_FOUND);
                return null;
            }
            type = 3;
        }
        standardFileFacade.downloadRecordFile(recordId, type, response);
        return null;
    }

    @Override
    public Response downloadRecordFile(Long recordId, Integer type, String authorization) {
        return downloadRecordFile(recordId, type);
    }

    @Override
    public R<Void> replaceRecordFile(ReplaceRecordReq req) {
        standardFileFacade.replaceRecordFile(req);
        return R.ok();
    }

    @Override
    public R<UploadResp> getInfo(Long fileId) {
        UploadResp info = standardFileFacade.getInfo(fileId);
        return R.ok(info);
    }

    @Override
    public R<List<UploadResp>> batchInfo(List<Long> fileIds) {
        List<UploadResp> list = standardFileFacade.batchInfo(fileIds);
        return R.ok(list);
    }

    @Override
    public R<String> tempUrl(FileTempUrlReq req) {
        String tempUrl = standardFileFacade.tempUrl(req);
        return R.ok(tempUrl);
    }

    @Override
    public R<List<TempUrlResp>> batchTempUrl(List<FileTempUrlReq> req) {
        List<TempUrlResp> list = standardFileFacade.batchTempUrl(req);
        return R.ok(list);
    }

    @Override
    public R<Long> removeRepeatFile(Long fileId) {
        Long count = standardFileFacade.removeRepeatFile(fileId);
        return R.ok(count);
    }

    @Override
    public R<Void> removeBatchFile(List<Long> fileIds) {
        standardFileFacade.removeBatchFile(fileIds);
        return R.ok();
    }
}
