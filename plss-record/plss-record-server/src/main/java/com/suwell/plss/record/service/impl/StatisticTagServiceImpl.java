package com.suwell.plss.record.service.impl;

import static com.suwell.plss.framework.common.filter.TraceFilter.HEADER_NAME_TRACE_ID;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.STATISTIC_TAG_COUNT;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.STATISTIC_TAG_TOTAL_MONTH;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.STATISTIC_TAG_TOTAL_NUM;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.STATISTIC_TAG_TOTAL_WEEKS;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.STATISTIC_TAG_TOTAL_YEAR;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.WeekMonthYear;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.convert.RecordMetadataValueConvertUtils;
import com.suwell.plss.record.entity.RecordMetadataValue;
import com.suwell.plss.record.entity.StatisticTag;
import com.suwell.plss.record.mapper.StatisticTagMapper;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.StatisticTagService;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticTagServiceImpl extends ServiceImpl<StatisticTagMapper, StatisticTag> implements
        StatisticTagService {

    private static final int PAGE_SIZE = 1000;
    private final RedisService redisService;
    private final RecordMetadataValueService recordMetadataValueService;
    private final PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @DSTransactional
    @Override
    public boolean statisticTagJob() {
        String seqId = IdWorker.getIdStr();
        MDC.put(HEADER_NAME_TRACE_ID, seqId);
        boolean executeOk = false;
        Long docId = redisService.get(STATISTIC_TAG_COUNT.getKey(), Long.class);
        Long maxDocId = Objects.nonNull(docId) ? docId : Long.MAX_VALUE;
        log.info("统计标签 get redis maxDocId:{}", maxDocId);

        List<RecordMetadataValue> metadataValueList = recordMetadataValueService.list(
                Wrappers.<RecordMetadataValue>query().lambda()
                        .lt(RecordMetadataValue::getRecordId, maxDocId)
                        .orderByDesc(RecordMetadataValue::getRecordId).last(" LIMIT " + PAGE_SIZE));
        if (CollUtil.isNotEmpty(metadataValueList)) {
            for (RecordMetadataValue metadataValue : metadataValueList) {
                if (StringUtils.isBlank(metadataValue.getMetadataValueJson())) {
                    log.warn("recordId:{},MetadataValueJson存在空串的值:{}", metadataValue.getRecordId(),
                            metadataValue.getMetadataValueJson());
                    continue;
                }
                String lowerCase = metadataValue.getMetadataValueJson().toLowerCase(Locale.ENGLISH);
                if (lowerCase.contains(StringPool.NULL)) {
                    log.warn("recordId:{},MetadataValueJson存在NULL的值:{}", metadataValue.getRecordId(),
                            metadataValue.getMetadataValueJson());
                    continue;
                }

                List<MetadataValueDTO> metadataValueDTOS = RecordMetadataValueConvertUtils.metadataJson2MetadataValueDTO(
                        metadataValue.getMetadataValueJson());
                if (CollUtil.isEmpty(metadataValueDTOS)) {
                    log.warn("recordId:{}元数据不存在:{}", metadataValue.getRecordId(), metadataValueDTOS);
                    continue;
                }
                MetadataValueDTO mv = metadataValueDTOS.stream()
                        .filter(o -> plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(o.getMdName()))
                        .findAny().orElse(null);
                if (Objects.isNull(mv)) {
                    log.warn("recordId:{}系统分类元数据不存在:{}", metadataValue.getRecordId());
                    continue;
                }
                if (StringUtils.isBlank(mv.getMdValue())) {
                    log.warn("recordId:{},系统分类标签存在空串的值:{}", metadataValue.getRecordId(), mv.getMdValue());
                    continue;
                }
                String lowerCaseValue = mv.getMdValue().toLowerCase(Locale.ENGLISH);
                if (lowerCaseValue.contains(StringPool.NULL)) {
                    log.warn("recordId:{},系统分类标签存在NULL的值:{}", metadataValue.getRecordId(), mv.getMdValue());
                    continue;
                }
                saveOrUpdateStatisticTag(mv.getRecordId(),
                        mv.getMdValue(), metadataValue.getModifiedTime());

            }
        }

        Long maxId = CollUtil.isNotEmpty(metadataValueList)
                ? metadataValueList.get(metadataValueList.size() - 1).getRecordId() : maxDocId;
        log.info("统计标签的偏移量 set redis ==maxDocId====>" + maxId + "======");
        redisService.setEx(STATISTIC_TAG_COUNT.getKey(), maxId, 7, TimeUnit.DAYS);

        if (CollUtil.isEmpty(metadataValueList) || metadataValueList.size() < PAGE_SIZE) {
            log.info("统计标签结束 end  in next round period clear start ");
            redisService.delete(STATISTIC_TAG_COUNT.getKey());
            redisService.set(STATISTIC_TAG_TOTAL_NUM.getKey(), count(Wrappers.<StatisticTag>query()
                    .select("distinct(tag_id) ")));

            WeekMonthYear weekMonthYear = DateUtils.getWeekMonthYear(new Date());
            List<StatisticTag> weekList = selectCountTagNumber(weekMonthYear.getBeginOfWeek(),
                    weekMonthYear.getEndOfWeek());

            List<StatisticTag> monthList = selectCountTagNumber(weekMonthYear.getBeginOfMonth(),
                    weekMonthYear.getEndOfMonth());

            List<StatisticTag> yearList = selectCountTagNumber(weekMonthYear.getBeginOfYear(),
                    weekMonthYear.getEndOfYear());
            redisService.set(STATISTIC_TAG_TOTAL_WEEKS.getKey(), weekList);
            redisService.set(STATISTIC_TAG_TOTAL_MONTH.getKey(), monthList);
            redisService.set(STATISTIC_TAG_TOTAL_YEAR.getKey(), yearList);
            // 清空标签表数据重新统计 ，tract 表
//            truncateStatisticTag();
            executeOk = true;
        }
        MDC.remove(HEADER_NAME_TRACE_ID);
        return executeOk;
    }

    @DSTransactional
    @Override
    public void saveOrUpdateStatisticTag(Long recordId, String mdValue, Date mdModifiedTime) {
        List<Long> categoryLastIdList = Arrays.stream(mdValue.split(StringPool.COMMA))
                .map(Long::parseLong).collect(Collectors.toList());
        for (Long tagId : categoryLastIdList) {
            StatisticTag statisticTag = getOne(Wrappers.<StatisticTag>query().lambda()
                    .eq(StatisticTag::getRecordId, recordId)
                    .eq(StatisticTag::getTagId, tagId));
            if (Objects.isNull(statisticTag)) {
                statisticTag = new StatisticTag();
                statisticTag.setRecordId(recordId);
                statisticTag.setTagId(tagId);
                statisticTag.setTagModifiedTime(mdModifiedTime);
                statisticTag.setTagNum(1L);
                statisticTag.setCreateTime(new Date());
                statisticTag.setModifiedTime(new Date());
                baseMapper.insert(statisticTag);
            } else {
                statisticTag.setTagNum(statisticTag.getTagNum() + 1);
                statisticTag.setTagModifiedTime(mdModifiedTime);
                statisticTag.setModifiedTime(new Date());
                baseMapper.updateById(statisticTag);
            }
        }

    }

    @Override
    public void truncateStatisticTag() {
        baseMapper.truncateStatisticTag();
    }

    @Override
    public List<StatisticTag> selectCountTagNumber(Date beginDate, Date endDate) {
        return baseMapper.selectCountTagNumber(beginDate, endDate);
    }


}
