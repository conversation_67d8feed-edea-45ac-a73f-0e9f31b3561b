package com.suwell.plss.record.facade;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.enums.RecordRelEnum;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordRel;
import com.suwell.plss.record.entity.RecordType;
import com.suwell.plss.record.service.*;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.dto.request.RecordRelAddReq;
import com.suwell.plss.record.standard.dto.request.RecordRelCheckReq;
import com.suwell.plss.record.standard.dto.request.RecordRelDelReq;
import com.suwell.plss.record.standard.dto.request.RecordRelQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordRelQueryRequ;
import com.suwell.plss.record.standard.dto.request.RecordRelationAddReq;
import com.suwell.plss.record.standard.dto.request.RecordRelationEditReq;
import com.suwell.plss.record.standard.dto.response.FileNameDuplicateDetectionResp;
import com.suwell.plss.record.standard.dto.response.RecordRelQueryLabelsResp;
import com.suwell.plss.record.standard.dto.response.RecordRelQueryResp;
import com.suwell.plss.record.standard.dto.response.RecordRelationInfoResp;
import com.suwell.plss.record.standard.service.StandardRecordRelFacade;
import com.suwell.plss.system.api.enums.SystemBizError;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER;

/**
 * @ClassName DocumentHistoryFacadeImpl
 * <AUTHOR>
 * @Date 2023-8-11 11:17
 * @Version 1.0
 **/
@Slf4j
@Service
public class StandardRecordRelFacadeImpl implements StandardRecordRelFacade {

    @Resource
    private RecordRelService recordRelService;

    @Resource
    private RecordService recordService;

    @Resource
    private RecordMetadataValueService recordMetadataValueService;

    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    private RecordTypeService recordTypeService;
    ;

    @Value("${record.server.rel.filter:}")
    private String recordRelFilter;


    @Resource
    private FolderService folderService;

    @Resource
    private DocumentService documentService;

    @DSTransactional
    @Override
    public void addRecordRel(List<RecordRelAddReq> recordRel, Integer ctype) {
        AssertUtils.notNull(recordRel, SystemBizError.RECORD_REL_NOT_NULL);
        AssertUtils.notNull(ctype, SystemBizError.RECORD_RELTYPE_NOT_NULL);
        if (RecordRelEnum.EVOLUTION.getCode().equals(ctype)) {
            AssertUtils.lessThan(recordRel.size(), 2, SystemBizError.RECORD_REL_NOT_MORE);
            RecordRelAddReq recordRelAddReq = recordRel.get(0);
            LambdaQueryWrapper<RecordRel> wrapper = new LambdaQueryWrapper<>();
            wrapper.and(w -> w.or().or(w1 -> w1.eq(RecordRel::getRecordId, recordRelAddReq.getRecordId())
                            .eq(RecordRel::getReferId, recordRelAddReq.getReferId()).eq(RecordRel::getCtype, ctype))
                    .or().or(w2 -> w2.eq(RecordRel::getReferId, recordRelAddReq.getRecordId())
                            .eq(RecordRel::getRecordId, recordRelAddReq.getReferId()).eq(RecordRel::getCtype, ctype)));
            List<RecordRel> rel = recordRelService.list(wrapper);
            AssertUtils.lessThan(rel.size(), 1, SystemBizError.RECORD_REL_NOT_REPEAT);

        }
        recordRelService.addRecordRel(recordRel, ctype);
    }

    @DSTransactional
    @Override
    public void addRecordRel(RecordRelationEditReq req) {
        log.info("添加{}关系 req:{}", RecordRelEnum.getEnum(req.getCtype()), JSON.toJSONString(req));

        List<RecordRelAddReq> recordRelAddReqList = Lists.newArrayList();
        RecordRelAddReq recordRelAddReq = new RecordRelAddReq();
        recordRelAddReq.setRecordId(req.getRecordId());
        recordRelAddReq.setRecordRelName(req.getRecordRelName());
        recordRelAddReq.setReferId(req.getReferId());
        recordRelAddReq.setReferRelName(req.getReferRelName());
        recordRelAddReq.setCtype(req.getCtype());
        recordRelAddReqList.add(recordRelAddReq);
        log.info("添加{}关系:add req:{}", RecordRelEnum.getEnum(req.getCtype()), JSON.toJSONString(req));
        addRecordRel(recordRelAddReqList, req.getCtype());
    }

    @Override
    public void updateRecordRel(RecordRelAddReq recordRel) {
        log.info("更新文件关系req:{}", JSON.toJSONString(recordRel));
        LambdaQueryWrapper<RecordRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordRel::getRecordId, recordRel.getRecordId());
        wrapper.eq(RecordRel::getReferId, recordRel.getReferId());
        wrapper.eq(RecordRel::getCtype, recordRel.getCtype());
        RecordRel rel = recordRelService.getOne(wrapper);
        if (RecordRelEnum.EVOLUTION.getCode().equals(recordRel.getCtype())) {
            RecordRelCheckReq checkReq = new RecordRelCheckReq();
            checkReq.setRecordId(recordRel.getReferNewId());
            checkReq.setCtype(recordRel.getCtype());
            AssertUtils.lessThan(this.checkRecordRel(checkReq), 1, SystemBizError.RECORD_REL_REPEAT);
//            LambdaQueryWrapper<RecordRel> wrapper2 = new LambdaQueryWrapper<>();
//            wrapper2.eq(RecordRel::getReferId, recordRel.getReferNewId());
//            wrapper2.eq(RecordRel::getCtype, recordRel.getCtype());
//            List<RecordRel> list = recordRelService.list(wrapper2);
//            AssertUtils.lessThan(list.size(), 1, SystemBizError.RECORD_REL_REPEAT);
        }
        rel.setReferId(recordRel.getReferNewId());
        rel.setReferRelName(recordRel.getReferRelName());
        recordRelService.update(rel, wrapper);
    }

    @Override
    public int checkRecordRel(RecordRelCheckReq recordRel) {
        LambdaQueryWrapper<RecordRel> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(recordRel.getRecordId() != null, RecordRel::getReferId, recordRel.getRecordId());
//        wrapper2.eq(recordRel.getRefId()!=null,RecordRel::getReferId, recordRel.getRefId());
        wrapper2.eq(recordRel.getCtype() != null, RecordRel::getCtype, recordRel.getCtype());
        List<RecordRel> list = recordRelService.list(wrapper2);
        return list.size();
    }

    @DSTransactional
    @Override
    public void removeRecordRel(List<RecordRelDelReq> recordRel) {
        AssertUtils.notNull(recordRel, SystemBizError.RECORD_REL_NOT_NULL);
        recordRelService.removeRecordRel(recordRel);
    }

    @DSTransactional
    @Override
    public void removeRecordRel(RecordRelationEditReq req) {
        log.info("删除{},req:{}", RecordRelEnum.getEnum(req.getCtype()), JSON.toJSONString(req));

        List<RecordRelDelReq> recordRelDelReqList = Lists.newArrayList();
        RecordRelDelReq recordRelDelReq = new RecordRelDelReq();
        recordRelDelReq.setRecordId(req.getRecordId());
        recordRelDelReq.setReferId(req.getReferId());
        recordRelDelReq.setCtype(req.getCtype());
        recordRelDelReqList.add(recordRelDelReq);
        log.info("删除 {}关系,removeRecordRel req:{}", RecordRelEnum.getEnum(req.getCtype()), JSON.toJSONString(req));
        removeRecordRel(recordRelDelReqList);
    }


    @Override
    public Map<Long, Map<String, List<RecordRelQueryResp>>> queryRecordRel(RecordRelQueryRequ req) {
        Map<Long, Map<String, List<RecordRelQueryResp>>> result = new HashMap<>();
        List<Long> recordIds = req.getRecordIds();
        if (!CollectionUtils.isEmpty(recordIds)) {
            Set<Long> recordSet = new HashSet<>();
            List<RecordRel> recordRelList = recordRelService.queryRecordRel(req);
            Map<Integer, List<RecordRel>> typeMap = recordRelList.stream()
                    .collect(Collectors.groupingBy(RecordRel::getCtype));
            for (Integer cType : req.getCtype()) {
                List<RecordRel> recordRels = typeMap.get(cType);
                if (CollUtil.isEmpty(recordRels)) {
                    continue;
                }
                CollUtil.addAll(recordSet, recordRels.stream().map(RecordRel::getReferId).collect(Collectors.toList()));
                CollUtil.addAll(recordSet,
                        recordRels.stream().map(RecordRel::getRecordId).collect(Collectors.toList()));
                List<Record> records = recordService.getListByIds(recordSet);
                Map<Long, Record> recordMap = records.stream()
                        .collect(Collectors.toMap(Record::getId, Function.identity()));

                List<MetadataValueDTO> metadataValueDTOS = recordMetadataValueService.listByBatchRecordId(
                        Lists.newArrayList(recordSet));
                List<MetadataValueDTO> publicDataMetaList = metadataValueDTOS.stream()
                        .filter(o -> plssFixedMetadataNameConfig.getFixedReleaseDateName().equals(o.getMdName()))
                        .collect(Collectors.toList());

                Map<Long, String> publishDateMap = publicDataMetaList.stream().collect(
                        Collectors.toMap(MetadataValueDTO::getRecordId, MetadataValueDTO::getMdValue));
                //
                for (Long recordId : recordIds) {
                    Map<String, List<RecordRelQueryResp>> relMap = new HashMap<>();
                    for (RecordRel recordRel : recordRels) {
                        Long id = null;
                        String relName = "";
                        if (recordId.equals(recordRel.getRecordId())) {
                            id = recordRel.getReferId();
                            relName = recordRel.getRecordRelName();
                        } else if (recordId.equals(recordRel.getReferId())) {
                            id = recordRel.getRecordId();
                            relName = recordRel.getReferRelName();
                        } else {
                            continue;
                        }
                        //根据配置 过滤不展示的关系
                        if (!includeRelName(relName)) {
                            continue;
                        }
                        List<RecordRelQueryResp> recordRelQueryResps = relMap.get(relName);
                        if (CollUtil.isEmpty(recordRelQueryResps)) {
                            recordRelQueryResps = new ArrayList<>();
                            relMap.put(relName, recordRelQueryResps);
                        }
                        RecordRelQueryResp resp = new RecordRelQueryResp();
                        resp.setRecordId(id);
                        Record existRecord = recordMap.get(id);
                        if (existRecord == null) {
                            //文件不存在
                            continue;
                        }
                        resp.setRecordTitle(existRecord.getTitle());
                        List<RecordRelQueryLabelsResp> labels = new ArrayList<>();
                        RecordRelQueryLabelsResp dateLabel = new RecordRelQueryLabelsResp();
                        dateLabel.setLabelName("发布日期");
                        dateLabel.setLabelValue(publishDateMap.get(id));
                        labels.add(dateLabel);
                        //全部查询显示来源
                        if (StringUtils.isBlank(req.getRepoId())) {
                            RecordRelQueryLabelsResp sourceLabel = new RecordRelQueryLabelsResp();
                            sourceLabel.setLabelName("来源");
                            Record record = recordService.getOneById(recordId);
                            RecordType recordType = recordTypeService.getById(record.getRecordtypeId());
                            sourceLabel.setLabelValue(recordType.getName());
                            labels.add(sourceLabel);
                        }
                        resp.setLabels(labels);
                        recordRelQueryResps.add(resp);
                    }
                    if (CollUtil.isNotEmpty(relMap)) {
                        result.put(recordId, relMap);
                    }
                }
            }

        }
        return result;
    }

    /**
     * 过滤关系名称  同时存在多种关系，搜索列表只展示一种
     *
     * @param recordName
     * @return
     */
    private boolean includeRelName(String recordName) {
        //未配置 不过滤
        if (StringUtils.isBlank(recordRelFilter)) {
            return true;
        }
        String[] relNames = recordRelFilter.split(",");
        for (String name : relNames) {
            if (name.equals(recordName)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public List<Long> queryRecordRels(RecordRelQueryReq recordRel) {

        return recordRelService.queryRecordRels(recordRel);
    }

    @Override
    public List<Long> queryCurrentRecordRels(RecordRelQueryReq recordRel) {
        return recordRelService.queryCurrentRecordRels(recordRel);
    }

    @Override
    public void modifyRecordRelation(RecordRelationAddReq recordRelationReq) {
        recordRelService.updateRecordRelation(recordRelationReq);
    }

    @Override
    public RecordRelationInfoResp queryRelation(Long recordId) {
        return recordRelService.queryRelation(recordId);
    }


    @Override
    public FileNameDuplicateDetectionResp fileNameDuplicateDetection(String fileName) {
        return recordService.fileNameDuplicateDetection(fileName);
    }

    @Override
    public String queryTitle(Long id,Long recordId) {
        if(!ObjectUtils.isEmpty(id)){
            Document document = documentService.getByIdAndRecordId(id, recordId);
            if(ObjectUtils.isEmpty(document)){
                return "";
            }
            if(!document.getCtype().equals(RECORD_DOCUMENT_TYPE_MASTER.getCode())){
                return document.getName().substring(0,document.getName().lastIndexOf("."));
            }
        }

        return  recordService.queryTitle(recordId);


    }

}
