package com.suwell.plss.record.migration.impl;

import cn.hutool.core.io.FileUtil;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.uuid.IdUtils;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.DocumentMd5Rel;
import com.suwell.plss.record.entity.FileRecord;
import com.suwell.plss.record.entity.FileRecordMd5Rel;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.DocumentMigrateService;
import com.suwell.plss.record.service.DocumentMd5RelService;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileRecordMd5RelService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.standard.dto.request.FileTransferReq;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

@Service
public class DocumentMigrateServiceImpl implements DocumentMigrateService {
    @Resource
    private DocumentService documentService;
    @Resource
    private DocumentMd5RelService documentMd5RelService;
    @Resource
    private FileService fileService;
    @Resource
    private FileRecordMd5RelService fileRecordMd5RelService;

    /*
     * export
     * rc_document
     * rc_document_md5_rel
     * rc_file
     * rc_file_md5_rel
     */
    @Override
    public void exportDocument(List<Long> listRecordId, String savePath) {
        // get document with record
        List<Document> listDocument = documentService.listByRecordIds(listRecordId);
        if (listDocument.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_DOCUMENT;
        CommonUtil.seriallizeList(listDocument, filepath);

//        exportDocumentMd5Rel(listRecordId, savePath);
        exportFileRecord(listDocument, savePath);
    }
    private void exportDocumentMd5Rel(List<Long> listRecordId, String savePath) {
        // get document with record
        List<DocumentMd5Rel> listDocumentMd5Rel = documentMd5RelService.listByRecordIds(listRecordId);
        if (listDocumentMd5Rel.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_DOCUMENT_MD5REL;
        CommonUtil.seriallizeList(listDocumentMd5Rel, filepath);
    }
    private void exportFileRecord(List<Document> listDocument, String savePath) {
        Set<Long> listFileIds = new HashSet<>();
        listFileIds.addAll(
                listDocument.stream().map(Document::getFileId).filter(Objects::nonNull).toList());
        this.exportFileSet(listFileIds, savePath);
        listFileIds.clear();

        listFileIds.addAll(
                listDocument.stream().map(Document::getOfdFileId).filter(Objects::nonNull).toList());
        this.exportFileSet(listFileIds, savePath);
        listFileIds.clear();

        listFileIds.addAll(
                listDocument.stream().map(Document::getTxtFileId).filter(Objects::nonNull).toList());
        this.exportFileSet(listFileIds, savePath);
    }
    private void exportFileSet(Set<Long> listFileIds, String savePath) {
        List<FileRecord> listFileRecord = fileService.batchList(listFileIds);

        if (listFileRecord.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_FILE;
        CommonUtil.seriallizeList(listFileRecord, filepath);

//        exportFileMd5Rel(listFileIds, savePath);

        this.seriallizeFile(listFileRecord, savePath);
    }
    private void exportFileMd5Rel(Set<Long> listFileId, String savePath) {

        if (listFileId.isEmpty()) {
            return;
        }

        List<FileRecordMd5Rel> listFileRecordMdRel = fileRecordMd5RelService.batchList(listFileId);

        if (listFileRecordMdRel.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_FILE_MD5REL;
        CommonUtil.seriallizeList(listFileRecordMdRel, filepath);
    }
    private void seriallizeFile(List<FileRecord> listFileRecord, String savePath) {

        try {
            // download origin file and ofd file and text file
            listFileRecord.forEach(fileRecord -> exportFile(fileRecord.getId(), savePath, fileRecord.getRelativePath(),
                    fileRecord.getFileName()));

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    private void exportFile(long fileId, String path, String relativePath, String filename) {

        if (StringUtils.isEmpty(path)) {
            return;
        }

        File orignalFile = FileUtil.touch(path + File.separator + relativePath + File.separator + filename);
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(orignalFile);
            fileService.transferOut(fileId, fileOutputStream);

            fileOutputStream.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void importList(String extName, String filepath, boolean overwrite, Logger logger) {
        switch (extName) {
            case ExportConstant.SUFFIX_DOCUMENT:
                importDocument(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_DOCUMENT_MD5REL:
//                importDocumentMd5Rel(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_FILE:
                importFileRecord(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_FILE_MD5REL:
//                importFileMd5Rel(filepath, overwrite, logger);
                break;
            default:
                break;
        }
    }
    private void importDocument(String filepath, boolean overwrite, Logger logger) {

        List<Document> listDocument = CommonUtil.deseriallizeList(filepath, Document.class);
        if (listDocument.isEmpty()) {
            return;
        }

        try {
            documentService.saveOrUpdateBatch(listDocument, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import document error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importDocumentMd5Rel(String filepath, boolean overwrite, Logger logger) {

    }
    private void importFileRecord(String filepath, boolean overwrite, Logger logger) {

        List<FileRecord> listFileRecord = CommonUtil.deseriallizeList(filepath, FileRecord.class);
        if (listFileRecord.isEmpty()) {
            return;
        }

        if (!overwrite) {
            List<Long> listRepeat = fileService.batchList(listFileRecord.stream().map(FileRecord::getId).toList())
                    .stream().map(FileRecord::getId).toList();
            if (!listRepeat.isEmpty()) {
                logger.info("repeat file id count: " + StringEscapeUtils.escapeJavaScript(listRepeat.size() + ""));
                listFileRecord.removeIf(fileRecord -> listRepeat.contains(fileRecord.getId()));
            }
        }

        // md5 repeat
        // List<Long> listMd5 = doRepeatMd5File(listFileRecord);
        // if (!listMd5.isEmpty()) {
        // logger.info("repeat file md5 count: " + listMd5.size());
        // listFileRecord.removeIf(fileRecord -> listMd5.contains(fileRecord.getId()));
        // }

        // fileService.saveBatch(listFileRecord);
        final String dirpath = FileUtil.getParent(filepath, 1);
        listFileRecord.forEach(fileRecord -> {
            String orignalFile =
                    dirpath + File.separator + fileRecord.getRelativePath() + File.separator + fileRecord.getFileName();
            Boolean bRet = uploadFile(orignalFile, fileRecord.getOriginName(), fileRecord.getFileName(),
                    fileRecord.getRelativePath(), logger);
            if (bRet) {
                fileService.saveOrUpdateBatch(List.of(fileRecord));
            }
        });

        CommonUtil.renameFile(filepath);
    }
    private void importFileMd5Rel(String filepath, boolean overwrite, Logger logger) {
    }
    private Boolean uploadFile(String filepath, String originName, String fileName, String relativePath, Logger logger) {

        try {
            if (StringUtils.isEmpty(filepath) || !Files.exists(Paths.get(filepath))) {
                logger.info("file not exists: " + StringEscapeUtils.escapeJavaScript(originName) + ":" + StringEscapeUtils.escapeJavaScript(fileName));
                return false;
            }

            FileInputStream fis = new FileInputStream(filepath);

            FileTransferReq req = new FileTransferReq();
            req.setFileData(fis);
            req.setOriginName(originName);
            req.setFileName(fileName);
            req.setRelativePath(relativePath);

            fileService.transferIn(req);

            return true;

        } catch (Exception ex) {
            logger.info(ex.getMessage());
            return false;
        }
    }
}
