package com.suwell.plss.record.migration.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.suwell.plss.framework.common.utils.uuid.IdUtils;
import com.suwell.plss.record.entity.Folder;
import com.suwell.plss.record.entity.FolderRecord;
import com.suwell.plss.record.entity.FolderRel;
import com.suwell.plss.record.entity.Repository;
import com.suwell.plss.record.entity.ResourceManager;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.migration.FolderMigrateService;
import com.suwell.plss.record.service.FolderRecordService;
import com.suwell.plss.record.service.FolderRelService;
import com.suwell.plss.record.service.FolderService;
import com.suwell.plss.record.service.RepositoryService;
import com.suwell.plss.record.service.ResourceManagerService;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

@Service
public class FolderMigrateServiceImpl implements FolderMigrateService {

    @Resource
    private FolderService folderService;
    @Resource
    private FolderRecordService folderRecordService;
    @Resource
    private FolderRelService folderRelService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private ResourceManagerService resourceManagerService;

    private final ThreadLocal<Set<Long>> listFolderIdLocal = ThreadLocal.withInitial(HashSet::new);

    @Override
    public void init() {
        listFolderIdLocal.set(new HashSet<>());
    }

    @Override
    public void clear() {
        this.listFolderIdLocal.remove();
    }

    /*
     * export
     * rc_folder_record
     */
    @Override
    public void exportFolderRecord(List<Long> listRecordId, String savePath) {
        // get document with record
        List<FolderRecord> listFolderRecord = folderRecordService
                .listWithWrapper(new LambdaQueryWrapper<FolderRecord>().in(FolderRecord::getRecordId, listRecordId));

        if (listFolderRecord.isEmpty()) {
            return;
        }
        Set<Long> listFolderId = listFolderIdLocal.get();
        listFolderId.addAll(listFolderRecord.stream().map(FolderRecord::getFolderId).collect(Collectors.toSet()));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_FOLDER_RECORD;
        CommonUtil.seriallizeList(listFolderRecord, filepath);

    }

    /*
     * export
     * rc_folder_rel
     * rc_folder
     * rc_repository
     * rc_resource_manager
     */
    @Override
    public void exportFolder(String savePath) {

        exportFolderRel(savePath);
        Set<Long> listFolderId = listFolderIdLocal.get();
        if (listFolderId.isEmpty()) {
            return;
        }

        List<Folder> listFolder = folderService.list(new LambdaQueryWrapper<Folder>()
                .in(Folder::getId, listFolderId));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_FOLDER;
        CommonUtil.seriallizeList(listFolder, filepath);

        this.exportRepository(savePath);
        this.exportResourceManager(savePath);
    }

    private void exportFolderRel(String savePath) {
        Set<Long> listFolderId = listFolderIdLocal.get();
        List<FolderRel> listFolderRel = folderRelService.list(new LambdaQueryWrapper<FolderRel>()
                .in(!listFolderId.isEmpty(), FolderRel::getDescendantId, listFolderId));

        if (listFolderRel.isEmpty()) {
            return;
        }

        List<FolderRel> listFR = listFolderRel.stream().filter(folderRel -> folderRel.getDistance() > 0)
                .collect(Collectors.toList());
        do {
            listFR = folderRelService.list(new LambdaQueryWrapper<FolderRel>().in(!listFR.isEmpty(),
                    FolderRel::getAncestorId, listFR.stream().map(FolderRel::getDescendantId).distinct()
                            .collect(Collectors.toList())));

            listFolderRel.addAll(listFR);

            listFR = listFR.stream().filter(folderRel -> folderRel.getDistance() > 0)
                    .collect(Collectors.toList());

        } while (!listFR.isEmpty());

        listFolderRel = listFolderRel.stream().distinct().collect(Collectors.toList());

        listFolderId.addAll(listFolderRel.stream().map(FolderRel::getAncestorId).collect(Collectors.toSet()));

        // add foler self
        List<FolderRel> listFolderSelf = folderRelService.list(new LambdaQueryWrapper<FolderRel>()
                .eq(FolderRel::getDistance, 0)
                .in(FolderRel::getAncestorId, listFolderId));
        listFolderRel.addAll(listFolderSelf.stream().distinct().toList());
        listFolderRel = listFolderRel.stream().distinct().collect(Collectors.toList());

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_FOLDER_REL;
        CommonUtil.seriallizeList(listFolderRel, filepath);
    }

    private void exportRepository(String savePath) {
        Set<Long> listFolderId = listFolderIdLocal.get();
        if (listFolderId.isEmpty()) {
            return;
        }

        List<Repository> listRepository = repositoryService.list(new LambdaQueryWrapper<Repository>()
                .in(Repository::getId, listFolderId));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_REPOSITORY;
        CommonUtil.seriallizeList(listRepository, filepath);
    }

    private void exportResourceManager(String savePath) {
        Set<Long> listFolderId = listFolderIdLocal.get();
        if (listFolderId.isEmpty()) {
            return;
        }

        List<ResourceManager> listResourceManager = resourceManagerService.list(
                new LambdaQueryWrapper<ResourceManager>()
                        .in(ResourceManager::getResourceId, listFolderId));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RESOURCE_MANAGER;
        CommonUtil.seriallizeList(listResourceManager, filepath);
    }


    @Override
    public void importList(String extName, String filepath, boolean overwrite, Logger logger) {
        switch (extName) {
            case ExportConstant.SUFFIX_FOLDER:
                importFolder(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_FOLDER_RECORD:
                importFolderRecord(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_FOLDER_REL:
                importFolderRel(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_REPOSITORY:
                importRepository(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RESOURCE_MANAGER:
                importResourceManager(filepath, overwrite, logger);
                break;
            default:
                break;
        }
    }

    private void importFolderRecord(String filepath, boolean overwrite, Logger logger) {
        List<FolderRecord> listFolderRecord = CommonUtil.deseriallizeList(filepath, FolderRecord.class);
        if (listFolderRecord.isEmpty()) {
            return;
        }

        try {
            folderRecordService.saveOrUpdateBatch(listFolderRecord, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import folderRecord error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }

    private void importFolderRel(String filepath, boolean overwrite, Logger logger) {
        List<FolderRel> listFolderRel = CommonUtil.deseriallizeList(filepath, FolderRel.class);
        if (listFolderRel.isEmpty()) {
            return;
        }

        try {
            folderRelService.saveOrUpdateBatch(listFolderRel, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import folderRel error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }

    private void importFolder(String filepath, boolean overwrite, Logger logger) {

        List<Folder> listFolder = CommonUtil.deseriallizeList(filepath, Folder.class);
        if (listFolder.isEmpty()) {
            return;
        }

        try {
            folderService.saveOrUpdateBatch(listFolder, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import folder error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }

    private void importRepository(String filepath, boolean overwrite, Logger logger) {

        List<Repository> listRepository = CommonUtil.deseriallizeList(filepath, Repository.class);
        if (listRepository.isEmpty()) {
            return;
        }

        try {
            repositoryService.saveOrUpdateBatch(listRepository, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import repository error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }

    private void importResourceManager(String filepath, boolean overwrite, Logger logger) {

        List<ResourceManager> listResourceManager = CommonUtil.deseriallizeList(filepath, ResourceManager.class);
        if (listResourceManager.isEmpty()) {
            return;
        }

        try {
            resourceManagerService.saveOrUpdateBatch(listResourceManager, overwrite);
            CommonUtil.renameFile(filepath);

        } catch (Exception ex) {
            logger.info("import ResourceManager error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }

}
