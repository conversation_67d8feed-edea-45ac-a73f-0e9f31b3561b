package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.framework.common.utils.SpringUtils;
import com.suwell.plss.framework.datasource.sharding.DataSourceConfiguration;
import com.suwell.plss.record.entity.TaskFlow;
import com.suwell.plss.record.mapper.TaskFlowMapper;
import com.suwell.plss.record.service.TaskFlowService;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023/11/10
 * @content
 */
@Slf4j
@Service
@DS(DataSourceConfiguration.SHARDING_DATA_SOURCE_NAME)
public class TaskFlowServiceImpl extends ServiceImpl<TaskFlowMapper, TaskFlow> implements TaskFlowService {

    @Override
    public List<TaskFlow> listWithWrapper(Collection<Long> recordId, LambdaQueryWrapper<TaskFlow> wrapper) {
        if (CollUtil.isEmpty(recordId)) {
            return List.of();
        }
        if (Objects.isNull(wrapper)) {
            wrapper = new LambdaQueryWrapper<>();
        }
        return list(wrapper.in(TaskFlow::getRecordId, recordId));
    }

    @Override
    public Long countWithWrapper(Collection<Long> recordId, LambdaQueryWrapper<TaskFlow> wrapper) {
        if (CollUtil.isEmpty(recordId)) {
            return 0L;
        }
        return count(wrapper.in(TaskFlow::getRecordId, recordId));
    }

    @Override
    public TaskFlow getOneWithWrapper(Long recordId, LambdaQueryWrapper<TaskFlow> wrapper) {
        if (Objects.isNull(recordId)) {
            return null;
        }
        if (Objects.isNull(wrapper)) {
            wrapper = new LambdaQueryWrapper<>();
        }
        return getOne(wrapper.eq(TaskFlow::getRecordId, recordId));
    }

    @Override
    public void saveOne(TaskFlow taskFlow) {
        if (Objects.isNull(taskFlow)) {
            return;
        }
        save(taskFlow);
    }

    @Override
    public void updateByWrapper(Long recordId, LambdaUpdateWrapper<TaskFlow> wrapper) {
        if (Objects.isNull(recordId) || Objects.isNull(wrapper) || wrapper.isEmptyOfWhere()) {
            return;
        }
        update(wrapper.eq(TaskFlow::getRecordId, recordId));
    }

    @Override
    public void deleteByWrapper(Collection<Long> recordIds, LambdaUpdateWrapper<TaskFlow> wrapper) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        if (Objects.isNull(wrapper)) {
            wrapper = new LambdaUpdateWrapper<>();
        }
        remove(wrapper.in(TaskFlow::getRecordId, recordIds));
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void migrateTaskFlow() {
        Long count = lambdaQuery().count();
        if (count == 0) {
            return;
        }
        long pageSize = 1000;
        long page = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
        Long maxId = 0L;
        TaskFlowServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        for (int i = 0; i < page; i++) {
            List<TaskFlow> list = lambdaQuery()
                    .orderByAsc(TaskFlow::getId)
                    .gt(TaskFlow::getId, maxId)
                    .last("limit " + pageSize).list();
            if (CollUtil.isEmpty(list)) {
                return;
            }
            maxId = list.get(list.size() - 1).getId();
            aopProxy.remove(new LambdaQueryWrapper<TaskFlow>()
                    .in(TaskFlow::getId, list.stream().map(TaskFlow::getId).collect(Collectors.toList()))
                    .in(TaskFlow::getRecordId,
                            list.stream().map(TaskFlow::getRecordId).collect(Collectors.toSet())));
            aopProxy.saveBatch(list, list.size());
        }
        log.info("迁移task_flow数据至分表完成，总数：{}", count);
    }
}
