package com.suwell.plss.record.dataprocesstask.executor.query;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.SleepUtils;
import com.suwell.plss.record.conf.DataProcessTaskConfig;
import com.suwell.plss.record.conf.DataProcessTaskConfig.DataProcessTaskProp;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.standard.dto.request.DataProcessTaskBatchDataProcessAddReq;
import com.suwell.plss.record.standard.dto.request.FolderMultiRecordReq;
import com.suwell.plss.record.standard.dto.response.BatchRecordProcessQueryResp;
import com.suwell.plss.record.standard.service.DataProcessTaskFacade;
import com.suwell.plss.record.standard.service.StandardFolderFacade;
import com.suwell.plss.search.standard.dto.request.newsSearch.BackendSearchReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchSortOption;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/19 10:37
 */
@Service
@Slf4j
public class BatchAddRepositoryPositionExecutor implements DataProcessTaskExecutor {

    @Resource
    private DataProcessTaskConfig dataProcessTaskConfig;

    @Resource
    private DataProcessTaskService dataProcessTaskService;

    @Resource
    private DataProcessTaskFacade dataProcessTaskFacade;

    @Resource
    private StandardFolderFacade standardFolderFacade;

    @Override
    public BusinessType getBusinessType() {
        return BusinessType.BATCH_ADD_REPOSITORY_POSITION;
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-开始执行,taskId={}", logPrefix, taskId);

        DataProcessTaskProp config = dataProcessTaskConfig.getConfig();
        Integer batchSleepSecond = config.getBatchSleepSecond();
        Integer batchSize = config.getQueryBatchSize();
        while (true) {
            SleepUtils.sleepSecond(batchSleepSecond);

            DataProcessTask task = dataProcessTaskService.getById(taskId);
            DataProcessTaskBatchDataProcessAddReq req = JSON.parseObject(task.getBusinessData(),
                    DataProcessTaskBatchDataProcessAddReq.class);
            BackendSearchReq searchCondition = req.getSearchCondition();

            Long offset = task.getOffsetPosition();
            log.info("{}任务-批次开始,taskId={},offset={}", logPrefix, taskId, offset);
            try {
                SearchSortOption searchSortOption = new SearchSortOption();
                searchSortOption.setSortField("recordId");
                searchSortOption.setSortType("asc");

                searchCondition.setPage(1).setPageSize(batchSize).setSortOptions(List.of(searchSortOption)).
                        setRecordIdStart(offset);
                if (searchCondition.getUploadTimeEnd() == null) {
                    searchCondition.setUploadTimeEnd(task.getCreateTime());
                }
                PageUtils<BatchRecordProcessQueryResp> pageUtils = dataProcessTaskFacade.batchRecordProcessQuery(
                        searchCondition);
                List<BatchRecordProcessQueryResp> recordList = pageUtils.getList();
                log.info("{}任务-获取到批次数据,taskId={},offset={},data={}", logPrefix, taskId, offset,
                        JSON.toJSONString(recordList));

                Long offsetNew = offset;
                int recordListSize = 0;
                if (CollectionUtils.isNotEmpty(recordList)) {
                    offsetNew = recordList.get(recordList.size() - 1).getRecordId();
                    recordListSize = recordList.size();

                    List<Long> recordIdList = recordList.stream().map(BatchRecordProcessQueryResp::getRecordId)
                            .collect(Collectors.toList());
                    FolderMultiRecordReq folderMultiRecordReq = new FolderMultiRecordReq();
                    folderMultiRecordReq.setRecordIdList(recordIdList);
                    folderMultiRecordReq.setFolderOrRepoIdList(req.getFolderOrRepoIdList());
                    folderMultiRecordReq.setUserId(task.getCreateBy());
                    folderMultiRecordReq.setValidPass(true);
                    folderMultiRecordReq.setIgnoreNotExistFolder(true);
                    standardFolderFacade.addMultiFolders(folderMultiRecordReq);
                }

                if (recordListSize < batchSize) {
                    dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetNew);
                    log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,
                            task.getTotalCount(), offsetNew);
                    break;
                } else {
                    dataProcessTaskService.finishBatch(taskId, task.getExecutedCount() + recordListSize, offsetNew);
                    log.info("{}任务-批次完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,
                            task.getExecutedCount() + recordListSize, offsetNew);
                }
            } catch (Exception e) {
                dataProcessTaskService.breakTask(taskId);
                log.error("{}任务-任务中断,taskId={},em={}", logPrefix, taskId, e.getMessage(), e);
                break;
            }
        }
    }
}
