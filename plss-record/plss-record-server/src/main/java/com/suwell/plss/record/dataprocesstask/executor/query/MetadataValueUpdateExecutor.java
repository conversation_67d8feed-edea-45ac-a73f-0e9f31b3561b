package com.suwell.plss.record.dataprocesstask.executor.query;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.SleepUtils;
import com.suwell.plss.record.conf.DataProcessTaskConfig;
import com.suwell.plss.record.dataprocesstask.AbstractTaskCondition;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.dataprocesstask.executor.TaskConditionFactory;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.enums.DataProcessTaskEnum;
import com.suwell.plss.record.enums.TaskConditionEnums;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.standard.dto.MetadataValueUpdateBaseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @create 2024/12/25
 * @content
 */
@Slf4j
@Service
public class MetadataValueUpdateExecutor implements DataProcessTaskExecutor {

    @Resource
    private DataProcessTaskConfig dataProcessTaskConfig;
    @Resource
    private DataProcessTaskService dataProcessTaskService;
    @Resource
    private TaskConditionFactory conditionFactory;

    @Override
    public DataProcessTaskEnum.BusinessType getBusinessType() {
        return DataProcessTaskEnum.BusinessType.BATCH_CONDITION_SET_METADATA_VALUE;
    }

    @Override
    public void addTask(String jsonParam) {
        try{
            MetadataValueUpdateBaseDto dto = JSON.parseObject(jsonParam, MetadataValueUpdateBaseDto.class);
            Integer type = dto.getType();
            AbstractTaskCondition impl = conditionFactory.getConditionImpl(TaskConditionEnums.ConditionTypeEnum.convert(type));
            Long totalCount = impl.getTotalCount(jsonParam);
            if(totalCount == null || totalCount == 0){
                log.warn("jsonParam:{},任务数据为空",jsonParam);
                return;
            }
            dataProcessTaskService.addTask(
                    DataProcessTaskEnum.BusinessType.BATCH_CONDITION_SET_METADATA_VALUE,
                    0,
                    jsonParam,
                    totalCount,
                    0L
            );
            log.info("jsonParam:{},任务添加成功",jsonParam);
        }catch (Exception e){
            log.error("jsonParam:{},任务添加失败",jsonParam, e);
        }
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-开始执行,taskId={}", logPrefix, taskId);

        DataProcessTaskConfig.DataProcessTaskProp config = dataProcessTaskConfig.getConfig();
        Integer batchSleepSecond = config.getBatchSleepSecond();
        Integer batchSize = config.getQueryBatchSize();

        DataProcessTask task = dataProcessTaskService.getById(taskId);
        final AtomicReference<Long> offset = new AtomicReference<>(task.getOffsetPosition());
        String businessData = task.getBusinessData();

        MetadataValueUpdateBaseDto dto = JSON.parseObject(task.getBusinessData(), MetadataValueUpdateBaseDto.class);
        Integer type = dto.getType();
        AbstractTaskCondition impl = conditionFactory.getConditionImpl(TaskConditionEnums.ConditionTypeEnum.convert(type));

        impl.init(businessData,batchSize);
        while (impl.hasNext()){
            SleepUtils.sleepSecond(batchSleepSecond);
            impl.startLog(l->{
                log.info("{}任务-批次开始,taskId={},offset={}", logPrefix, taskId, offset.get());
            });
            try{
                impl.updateData(impl.queryData(businessData));
                impl.finishLog(l->{
                    offset.set(l.get(l.size() - 1));
                    if (l.size() < batchSize){
                        dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offset.get());
                        log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,task.getTotalCount(), offset.get());
                        return true;
                    }else{
                        dataProcessTaskService.finishBatch(taskId, task.getExecutedCount() + l.size(), offset.get());
                        log.info("{}任务-批次完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,
                                task.getExecutedCount() + l.size(), offset.get());
                        return false;
                    }
                });
            }catch (Exception e){
                impl.breakLog(l->{
                    dataProcessTaskService.breakTask(taskId);
                    log.error("{}任务-任务中断,taskId={},em={}", logPrefix, taskId, e.getMessage(), e);
                });
            }
        }
        impl.finish();
    }
}
