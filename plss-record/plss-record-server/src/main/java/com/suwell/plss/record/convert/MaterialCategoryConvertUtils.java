package com.suwell.plss.record.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.suwell.plss.record.entity.MaterialCategory;
import com.suwell.plss.record.standard.dto.request.MaterialCategoryAddReq;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/25
 */
public class MaterialCategoryConvertUtils {

    public static List<MaterialCategory> materialCategoryAddReqList2MaterialCategoryList(
            List<MaterialCategoryAddReq> categoryAddReqList, Long materialId) {
        if (CollUtil.isNotEmpty(categoryAddReqList)) {
            return categoryAddReqList.stream().map(x -> {
                MaterialCategory materialCategory = new MaterialCategory();
                materialCategory.setMaterialId(materialId);
                materialCategory.setCategoryId(x.getCategoryId());
                materialCategory.setCategoryName(x.getCategoryName());
                materialCategory.setCategoryType(x.getCategoryType());
                return materialCategory;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
