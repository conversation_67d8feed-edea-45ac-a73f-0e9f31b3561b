package com.suwell.plss.record.controller;

import static com.suwell.plss.framework.common.constant.SecurityConstants.SLC_VALIDSTR;
import static com.suwell.plss.record.service.impl.TransferServiceImpl.HOME_EXPORT_DIR;
import static com.suwell.plss.record.service.impl.TransferServiceImpl.HOME_EXPORT_ZIP_PATH;
import static com.suwell.plss.record.standard.enums.RecordBizError.FILE_DOWNLOAD_COUNT_LIMIT;
import static com.suwell.plss.record.standard.enums.RecordEnum.PubLibSyncEsWay.SYNC_ES_WAY_REPAIR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.suwell.plss.document.process.dto.request.QueryTempToolsDTO;
import com.suwell.plss.document.process.dto.response.QueryTempToolsResp;
import com.suwell.plss.document.process.service.DocumentProcessRpcService;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO;
import com.suwell.plss.framework.mq.enums.EventType;
import com.suwell.plss.framework.mq.enums.MessagePriorityEnum;
import com.suwell.plss.framework.mq.events.BaseInfoFanoutCUDEvent;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.client.adapter.KsoCloudDocDownAdapter;
import com.suwell.plss.record.client.kso.request.CloudDocDownNewReq;
import com.suwell.plss.record.conf.AiExtractCountConfig;
import com.suwell.plss.record.conf.FeatureImplNewConfig;
import com.suwell.plss.record.convert.RecordMetadataValueConvertUtils;
import com.suwell.plss.record.domain.RecordReleaseTimeParamDto;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.knowledge.AiKnowledgeExtractUtils;
import com.suwell.plss.record.knowledge.AiKnowledgeReq;
import com.suwell.plss.record.service.AiKnowledgeEngineeringService;
import com.suwell.plss.record.service.AttFlowService;
import com.suwell.plss.record.service.DistributeTransferService;
import com.suwell.plss.record.service.DocProcessService;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.service.OperateAttESService;
import com.suwell.plss.record.service.OperateDocProcessESService;
import com.suwell.plss.record.service.OperateESService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RepairDataService;
import com.suwell.plss.record.service.ResetDataService;
import com.suwell.plss.record.service.TransferService;
import com.suwell.plss.record.standard.dto.request.AttFlowReq;
import com.suwell.plss.record.standard.service.StandardBatchDocFacade;
import com.suwell.plss.record.util.RecordCommonUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024/1/4
 * @content
 */
@RefreshScope
@Slf4j
@RestController
@RequestMapping("/v1/test")
public class TestController {
    @Resource
    AiKnowledgeExtractUtils aiKnowledgeExtractUtils;
    @Resource
    private RepairDataService repairDataService;
    @Resource
    private StandardBatchDocFacade standardBatchDocFacade;
    @Resource
    private RedisService redisService;
    @Resource
    private TransferService transferService;
    @Resource
    private OperateESService operateESService;
    @Resource
    private OperateDocProcessESService operateDocProcessESService;

    @Resource
    private DocProcessService docProcessService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private FeatureImplNewConfig featureImplNewConfig;
    @Resource
    private AiKnowledgeEngineeringService aiKnowledgeEngineeringService;
    @Resource
    private RecordService recordService;
    @Resource
    private DocumentProcessRpcService documentProcessRpcService;
    @Resource
    private Redisson redisson;
    @Resource
    private ApplicationContext context;
    @Resource
    private KsoCloudDocDownAdapter ksoCloudDocDownAdapter;

    @Value("${record.db2es.pageSize:1000}")
    private Integer pageSize;
    //    /**
//     * 知识提取
//     *
//     * @return
//     */
//    @GetMapping("/basconfig")
//    public R<Object> basconfig() {
////        ButtJointOtherProperties buttJointProperties = featureImplNewConfig.getButtJointProperties(
////                featureImplNewConfig.getButtJointOtherKey());
//        return R.ok(JSON.toJSONString(buttJointProperties));
//    }
    @Resource
    private DistributeTransferService distributeTransferService;

//    public static void main(String[] args) {
//        String zipFilePath = HOME_EXPORT_ZIP_PATH + HOME_EXPORT_DIR + ".zip";
//        ZipUtil.unzip(FileUtil.file(zipFilePath), FileUtil.file(HOME_EXPORT_ZIP_PATH), StandardCharsets.UTF_8);
//    }
    @Resource
    private ResetDataService resetDataService;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;

    @Resource
    private AiExtractCountConfig aiExtractCountConfig;
    @Resource
    private RecordCommonUtils recordCommonUtils;
    @Resource
    private AttFlowService attFlowService;
    @Resource
    private OperateAttESService operateAttESService;
    @PostMapping("/attFlow")
    public R<Object> attFlow(@RequestBody AttFlowReq attFlowReq) {
        attFlowService.triggerAttFlow(attFlowReq.getRecordId(), attFlowReq.getPriorityValue());
        return R.ok();
    }

    @PostMapping("/attRetry")
    public R<Object> attRetry(@RequestBody AttFlowReq attFlowReq) {
        attFlowService.attRetry(attFlowReq.getRecordId(), attFlowReq.getPriorityValue());
        return R.ok();
    }

    @PostMapping("/attLib")
    public R<Object> attLib(@RequestBody List<Long> recordIds) {
        if (CollUtil.isNotEmpty(recordIds)) {
            recordIds.forEach(recordId -> operateAttESService.putRecordAttEs(recordId, SYNC_ES_WAY_REPAIR, null, false));
        }
        return R.ok();
    }


    /**
     *
     * @return
     */
    @GetMapping("/downloadLimit")
    public R<Object> downloadLimit() {
        AssertUtils.isTrue(recordCommonUtils.checkFileDownloadLimit("1","HHHH"),
                FILE_DOWNLOAD_COUNT_LIMIT);
        return R.ok();
    }

    /**
     *
     * @return
     */
    @GetMapping("/fixed")
    public R<Map<String, Object>> dhai() {
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedRepoPositionName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedSysCategoryName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedKeywordName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedOrganizationName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedPersonName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedLocationName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedDateName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedKnowledgeExtractionName());

        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getDocTitleName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getUnitLevelName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedYearName());
        map.put(IdUtil.fastSimpleUUID(), plssFixedMetadataNameConfig.getFixedReleaseDateName());

        map.put(IdUtil.fastSimpleUUID(), aiExtractCountConfig.getAiExtractCount(aiExtractCountConfig.getConfigKey()));
        map.put("测试redis断开重连问题", redisService.increment("dhai_test111111"));
        return R.ok(map);
    }

    @GetMapping("/001")
    public R<Void> test001(@RequestParam("num") int num, @RequestParam("type") int type) {
        Integer priorityValue = 0;
        if (num > 0) {
            switch (type) {
                case 1:
                    repairDataService.resetAiRecordFinishStorage(num, priorityValue);
                    break;
                case 2:
                    repairDataService.repeatGetOfd(num, priorityValue);
                    break;
                case 3:
                    repairDataService.repeatGetText(num, priorityValue);
                    break;
                case 4:
                    repairDataService.repeatGetMetadata(num, priorityValue);
                    break;
                case 5:
                    repairDataService.repairFinishStorage("",num,false, priorityValue);
                    break;
                default:
            }

        }
        return R.ok();
    }

    /**
     * 批量删除处理中和处理异常的文档
     *
     * @return
     */
    @GetMapping("/batchDeleteProcessingAndExceptionDoc")
    public R<Void> batchDeleteProcessingAndExceptionDoc() {
        standardBatchDocFacade.batchDeleteProcessingAndExceptionDoc();
        return R.ok();
    }

    /**
     * 迁移数据
     *
     * @return
     */
    @GetMapping("/transfer")
    public R<String> transfer() {
        return R.ok(transferService.transfer());
    }

    /**
     * 迁移数据
     *
     * @return
     */
    @GetMapping("/unzip")
    public R<String> unzip() {
        String zipFilePath = HOME_EXPORT_ZIP_PATH + HOME_EXPORT_DIR + ".zip";
        ZipUtil.unzip(FileUtil.file(zipFilePath), FileUtil.file(HOME_EXPORT_ZIP_PATH), StandardCharsets.UTF_8);
        return R.ok();
    }

    /**
     * 1.0文档数据迁移
     *
     * @date 2024/2/27 13:58
     */
    @GetMapping("/previous/large/transfer")
    public R<String> transferPreviousLargeVersion() {
        return R.ok(transferService.transferV2());
    }

    /**
     * 批量删除处理中和处理异常的文档
     *
     * @return
     */
    @GetMapping("/deleteRedisKey")
    public R<Void> deleteRedisKey(@RequestParam("redisKey") String redisKey) {
        log.info("删除的redis key :{}", redisKey);
        redisService.delete(redisKey);
        return R.ok();
    }
    /**
     * 批量删除处理中和处理异常的文档
     *
     * @return
     */
    @GetMapping("/setunitsck")
    public R<Void> setunitsck() {
        redisService.setEx("DHAI_HAHA_TESTS_V1", Long.MAX_VALUE, 5, TimeUnit.DAYS);
        return R.ok();
    }

    /**
     * 同步修复 es中的数据
     *
     * @return
     */
    @PostMapping("/syncRecordEs")
    public R<Void> syncRecordEs(@RequestBody List<Long> recordIds) {
        log.info("同步的文件id:{}", recordIds);
        recordIds.forEach(recordId -> operateESService.putLibraryEsSync(recordId, SYNC_ES_WAY_REPAIR, MessagePriorityEnum.LEVEL_0.getValue()));
        return R.ok();
    }

    /**
     * 同步修复syncDocProcess2es es中的数据
     *
     * @return
     */
    @PostMapping("/syncDocProcess2es")
    public R<Void> syncDocProcess(@RequestBody List<Long> recordIds) {
        log.info("同步的文件id:{}", recordIds);
        recordIds.forEach(recordId -> operateDocProcessESService.saveOrUpdateEsDocProcess(
                docProcessService.getByRecordId(recordId), false));
        return R.ok();
    }



    /**
     * 知识提取
     *
     * @param req
     * @return
     */
    @PostMapping("/sup/knowledge")
    public R<List<Long>> test002(@RequestBody AiKnowledgeReq req) {
        return R.ok(aiKnowledgeExtractUtils.FileTask(req));
    }

    /**
     * 知识工程提取 测试使用
     *
     * @param num
     * @return
     */
    @Deprecated
    @GetMapping("/sup/knowledgeEngineering")
    public R<List<Long>> testKnowledgeEngineering(@RequestParam("num") int num) {
//        final List<Record> recordList = recordService.list(Wrappers.<Record>query().lambda()
//                .eq(Record::getRecordStatus, RECORD_STATUS_PASS.getCode())
//                .lt(Record::getId, Long.MAX_VALUE)
//                .isNotNull(Record::getTitle)
//                .orderByDesc(Record::getId).last(" LIMIT " + num));
//
//        List<Long> recordIdList = Lists.newArrayList();
//        if (CollUtil.isNotEmpty(recordList)) {
//            String predicates = "[{\"安全检査\":[\"检査人员\",\"被检査单位\",\"联系人\",\"所属行业\",\"文号\",\"检查内容\",\"检査地址\"]},{\"单位\":[\"审批人\",\"审核人\"]}]";
//            recordIdList = recordList.stream().map(Record::getId).collect(Collectors.toList());
//            log.info("已经入库的文件进行知识工程提取的文件id:{}", recordIdList);
//            aiKnowledgeEngineeringService.sendAiKnowledgeTaskEvent(recordIdList, "", predicates);
//        }
        return R.ok();
    }

    /**
     * 知识工程提取 测试使用
     *
     * @param ids
     * @return
     */
    @Deprecated
    @PostMapping("/sup/sendKnowledgeTask")
    public R<List<Record>> testKnowledgeEngineeringV2(@RequestBody Long[] ids) {
//        final List<Record> recordList = recordService.list(Wrappers.<Record>query().lambda()
//                .eq(Record::getRecordStatus, RECORD_STATUS_PASS.getCode())
//                .isNotNull(Record::getTitle)
//                .in(Record::getId, Arrays.asList(ids)));
//
//        if (CollUtil.isNotEmpty(recordList)) {
//            String predicates = "[{\"安全检査\":[\"检査人员\",\"被检査单位\",\"联系人\",\"所属行业\",\"文号\",\"检查内容\",\"检査地址\"]},{\"单位\":[\"审批人\",\"审核人\"]}]";
//            List<Long> recordIdList = recordList.stream().map(Record::getId).collect(Collectors.toList());
//            log.info("已经入库的文件进行知识工程提取的文件id:{}", recordIdList);
//            aiKnowledgeEngineeringService.sendAiKnowledgeTaskEvent(recordIdList, "", predicates);
//            return R.ok(recordList);
//        }
        return R.ok(Lists.newArrayList());
    }

    /**
     * http://localhost:8060/v1/test/distributeTransferMetadataValueData
     * 分发元数据值分表数据
     *
     * @return
     */
    @GetMapping("/distributeTransferMetadataValueData")
    public R<Void> distributeTransferMetadataValueData() {
        distributeTransferService.distributeTransferMetadataValueData();
        return R.ok();
    }

    /**
     * http://localhost:8060/v1/test/statisticTag
     * 分发元数据值分表数据
     *
     * @return
     */
    @GetMapping("/statisticTag")
    public R<Void> statisticTag() {
        resetDataService.statisticTag();
        return R.ok();
    }

    /**
     * 通过recordId查询元数据的真实存储表
     */
    @PostMapping("/sup/metadataValueActualTableName")
    public R<Map<Long, String>> metadataValueActualTableName(@RequestBody Long[] ids) {
        Map<Long, String> map = Maps.newHashMap();
        for (Long id : ids) {
            map.put(id, RecordMetadataValueConvertUtils.getActualTableName(id));
        }
        return R.ok(map);
    }

    /**
     * 通过recordId查询元数据的真实存储表
     */
    @PostMapping("/repairRecordReleaseTimeToEs")
    public R<Void> metadataValueActualTableName(@RequestBody RecordReleaseTimeParamDto recordReleaseTimeParamDto) {
        log.info("修复文档releaseTime字段到es,req:{}", JSON.toJSONString(recordReleaseTimeParamDto));
        repairDataService.repairRecordReleaseTimeToEs(JSON.toJSONString(recordReleaseTimeParamDto), MessagePriorityEnum.LEVEL_0.getValue());
        return R.ok();
    }

    @RequestMapping("/main")
    public Object main(String key) throws Exception {
        return "hello " + key;
    }

    /**
     * 测试动态变更日志级别
     */
    @GetMapping("/ded/haha")
    public R<Void> hahhaa() {
        log.info("info测试的问题,req:{}",System.currentTimeMillis());
        log.debug("debug测试的问题,req:{}", System.currentTimeMillis());
        log.error("error测试的问题,req:{}", System.currentTimeMillis());
        return R.ok();
    }


    /**
     * 修复文件基本扩展信息:文档类型，文件类型，来源，文件md5
     */
    @PostMapping("/recordExtendToEs")
    public R<Void> recordExtendToEs(@RequestBody Long[] ids) {
        repairDataService.recordExtendToEs(MessagePriorityEnum.LEVEL_0.getValue());
        return R.ok();
    }

    @GetMapping("/redis/redisson")
    public void redissonTest() throws Exception {
        RLock redissonLock = redisson.getLock("TEST:LOCK");
        redissonLock.lock();
        try {
            // TODO 执行的业务代码
            log.info("执行业务逻辑，测试加日志");
            TimeUnit.SECONDS.sleep(10);
        } finally {
            // 当前线程还持有锁的状态时进行解锁,杜绝异常现象发生
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }

    @GetMapping("/redis/info")
    public R<Object> redisTest() {

        // 此种获取ok
        redisService.setEx("TEST11", SLC_VALIDSTR, 5, TimeUnit.MINUTES);
        String maxDocId = redisService.get("TEST11", String.class);
        System.out.println(maxDocId);

        // 结论 排查，setStr 与 setEx 序列化方式不同导致获取字符串类型报错，字符串序列化json格式报错
        // 建议，字符串方式set 要单独处理
        String ss = "TEST292222";
        redisService.setStr(ss, SLC_VALIDSTR);
        String str = redisService.getStr(ss);
//        // 此种获取字符串方式失败
//        String o = redisService.get(ss, String.class);
//        System.out.println(o);
        return R.ok(str);
    }


    @GetMapping("/syncs/db2es")
    public R<Object> syncDb2es() {
        // recordId已入库db全量同步es并且触发附件的提取流程
        resetDataService.syncAllDbToEs(500, true, MessagePriorityEnum.LEVEL_0.getValue());
        return R.ok();
    }

    @GetMapping("/syncs/masterFile/es")
    public R<Object> syncMasterFileDb2es() {
        // recordId已入库db全量同步es 只有主件
        new Thread(() -> {
            resetDataService.syncAllDbToEs("", pageSize,false, false, MessagePriorityEnum.LEVEL_0.getValue());
        }).start();
        return R.ok();
    }


    @Resource
    private DocumentService documentService;
    @Resource
    private FileService fileService;


    @GetMapping("/db2esRepo")
    public R<Object> db2esRepo(@RequestBody @Validated  QueryTempToolsDTO req) {
        // recordId已入库db全量同步es并且触发附件的提取流程
        R<QueryTempToolsResp> r = documentProcessRpcService.queryTempTools(req);
        log.info("rpc接口调用结果:"+JSON.toJSONString(r));
        if (r.isSuccess()) {
            QueryTempToolsResp data = r.getData();
            if (CollUtil.isNotEmpty(data.getRecordIdList())) {
                data.getRecordIdList().forEach(recordId ->
                        operateESService.putLibraryEsSync(recordService.getOneById(recordId),
                            SYNC_ES_WAY_REPAIR, req.getAtt(), MessagePriorityEnum.LEVEL_0.getValue(), false));
            } else{
                log.info("rpc接口调用结果为空");
                return R.error(r.getCode(), "rpc接口查询空数据.入参:"+JSON.toJSONString(req));
            }
            List<Long> list = documentService.listByRecordIds(r.getData().getRecordIdList())
                    .stream().flatMap(doc -> Stream.of(doc.getFileId(), doc.getOfdFileId(), doc.getTxtFileId()))
                    .filter(Objects::nonNull).toList();
            fileService.reHashFile(list);
            return R.ok(r.getData());
        } else {
            return R.error(r.getCode(), "rpc接口调用错误入参:"+JSON.toJSONString(req));
        }

    }

    /**
     * 测试kso下载
     * @param recordId
     * @param response
     */
    @GetMapping("/ksoCloudDocDownload")
    public void ksoCloudDocDownload(@RequestParam("recordId") Long recordId,  HttpServletResponse response) {
        ksoCloudDocDownAdapter.ksoCloudDocDownload(new CloudDocDownNewReq(recordId), response);
    }

    @GetMapping("/publishPriorityMsg")
    public void publishPriorityMsg(@RequestParam("num") Integer num,  @RequestParam("priorityValue") Integer priorityValue) {
        MqRecordPathDTO move = new MqRecordPathDTO();
        move.setRecordId(1L);
        move.setDocId(Long.valueOf(priorityValue));
        for (int i = 0; i < num; i++) {
            context.publishEvent(new BaseInfoFanoutCUDEvent<>(move, priorityValue, EventType.RECORD_MOVE));
        }
    }


}
