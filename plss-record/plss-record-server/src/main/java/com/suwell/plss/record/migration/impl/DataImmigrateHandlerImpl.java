package com.suwell.plss.record.migration.impl;

import static com.suwell.plss.record.enums.RecordRedisKeyEnum.DATA_IMPORT_LOAD_TASK;
import static com.suwell.plss.record.enums.RecordRedisKeyEnum.DATA_IMPORT_LOAD_TASK_HEALTH;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.migration.DataImmigrateHandler;
import com.suwell.plss.record.migration.DataImmigrateService;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 数据导入处理器
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
@Slf4j
@Service
public class DataImmigrateHandlerImpl implements DataImmigrateHandler {
    @Lazy
    @Resource
    private DataImmigrateService dataImmigrateService;
    @Resource
    private RedisService redisService;

    @Override
    public void start(Long taskId, String prefixPath, String dataDir, boolean overwrite, boolean needDelFile) {
        log.info("启动导入任务,taskId:{},dataDir:{}", taskId, dataDir);
        // 启动程序
        List<String> list = redisService.get(DATA_IMPORT_LOAD_TASK.getKey(), List.class, String.class);
        log.info("redis中跑任务标记目录列表list:{}", JSON.toJSONString(list));

        if (CollUtil.isNotEmpty(list)) {
            if (!list.contains(dataDir)) {
                list.add(dataDir);
                redisService.set(DATA_IMPORT_LOAD_TASK.getKey(), list);
            }
        } else {
            redisService.set(DATA_IMPORT_LOAD_TASK.getKey(), Lists.newArrayList(dataDir));
        }

        dataImmigrateService.importTask(prefixPath, dataDir, taskId, overwrite, needDelFile);
    }

    @Override
    public void stop(Long taskId, String dataDir) {
        log.info("停止导入任务,taskId:{},dataDir:{}", taskId, dataDir);
        //  雄哥的停止程序
        List<String> list = redisService.get(DATA_IMPORT_LOAD_TASK.getKey(), List.class, String.class);
        log.info("redis中跑任务标记目录列表list:{}", JSON.toJSONString(list));
        if (CollUtil.isNotEmpty(list)) {
            if (list.contains(dataDir)) {
                list.remove(dataDir);
                redisService.set(DATA_IMPORT_LOAD_TASK.getKey(), list);
            }
        }
    }

    @Override
    public boolean health(Long taskId, String dataDir) {
        // 雄哥 导入程序的健康检查 是否运行正常
        return redisService.hasKey(DATA_IMPORT_LOAD_TASK_HEALTH.getKey() + taskId.toString());
    }
}
