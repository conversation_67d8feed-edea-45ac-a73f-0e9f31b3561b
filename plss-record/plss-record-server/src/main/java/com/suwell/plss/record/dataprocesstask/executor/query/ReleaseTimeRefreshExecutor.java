package com.suwell.plss.record.dataprocesstask.executor.query;

import static com.suwell.plss.record.standard.enums.RecordEnum.PubLibSyncEsWay.SYNC_ES_RELEASE_TIME;
import static com.suwell.plss.record.standard.enums.RecordStatusEnum.RECORD_STATUS_PASS;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.SleepUtils;
import com.suwell.plss.record.conf.DataProcessTaskConfig;
import com.suwell.plss.record.conf.DataProcessTaskConfig.DataProcessTaskProp;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.service.OperateESService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.ReleaseTimeRefreshTaskDto;
import com.suwell.plss.record.standard.dto.request.RecordQueryReq;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/14 11:13
 */
@Service
@Slf4j
public class ReleaseTimeRefreshExecutor implements DataProcessTaskExecutor {
    @Resource
    private DataProcessTaskService dataProcessTaskService;
    @Resource
    private RecordService recordService;
    @Resource
    private OperateESService operateESService;

    @Resource
    private DataProcessTaskConfig dataProcessTaskConfig;

    @Override
    public BusinessType getBusinessType() {
        return BusinessType.RELEASE_TIME_REFRESH;
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-开始执行,taskId={}",logPrefix, taskId);

        DataProcessTaskProp config = dataProcessTaskConfig.getConfig();
        Integer batchSleepSecond = config.getBatchSleepSecond();
        Integer batchSize = config.getQueryBatchSize();
        while (true) {
            SleepUtils.sleepSecond(batchSleepSecond);

            DataProcessTask task = dataProcessTaskService.getById(taskId);
            ReleaseTimeRefreshTaskDto taskDto = JSON.parseObject(task.getBusinessData(), ReleaseTimeRefreshTaskDto.class);
            Long recordTypeId = taskDto.getRecordTypeId();

            Long offset = task.getOffsetPosition();
            log.info("{}任务-批次开始,taskId={},offset={}",logPrefix, taskId,offset);
            try{
                RecordQueryReq req = new RecordQueryReq();
                req.setPage(1);
                req.setPageSize(batchSize);
                req.setOrderItems(List.of(OrderItem.asc("id")));
                req.setRecordTypeId(recordTypeId).setRecordStatus(RECORD_STATUS_PASS.getCode())
                        .setLeCreateTime(task.getCreateTime()).setGtRecordId(offset);
                PageUtils<Record> page = recordService.pageFromDB(req);
                List<Record> recordList = page.getList();
                log.info("{}任务-获取到批次数据,taskId={},offset={},data={}", logPrefix, taskId, offset,
                        JSON.toJSONString(recordList));

                Long offsetNew = offset;
                if(CollectionUtils.isNotEmpty(recordList)){
                    offsetNew = recordList.get(recordList.size() - 1).getId();
                }
                for (Record record : recordList) {
                    operateESService.putLibraryEsSync(record, SYNC_ES_RELEASE_TIME, null);
                }
                if(recordList.size() < batchSize){
                    dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetNew);
                    log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}",logPrefix, taskId,
                            task.getTotalCount(), offsetNew);
                    break;
                }else{
                    dataProcessTaskService.finishBatch(taskId, task.getExecutedCount() + recordList.size(), offsetNew);
                    log.info("{}任务-批次完成,taskId={},executedCount={},offsetNew={}",logPrefix, taskId,
                            task.getExecutedCount() + recordList.size(), offsetNew);
                }
            }catch (Exception e){
                dataProcessTaskService.breakTask(taskId);
                log.error("{}任务-任务中断,taskId={},em={}",logPrefix, taskId, e.getMessage(), e);
                break;
            }
        }

    }
}
