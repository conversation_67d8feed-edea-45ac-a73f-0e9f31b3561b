package com.suwell.plss.record.controller;

import cn.hutool.core.collection.CollUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.ContainsCtypeReq;
import com.suwell.plss.record.standard.dto.request.ConvertRepoToFolderReq;
import com.suwell.plss.record.standard.dto.request.QueryOrAutoNewRepoReq;
import com.suwell.plss.record.standard.dto.request.RepoCollectReq;
import com.suwell.plss.record.standard.dto.request.RepoTagQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMaterialViewReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryModifyReq;
import com.suwell.plss.record.standard.dto.request.RepositoryQueryReq;
import com.suwell.plss.record.standard.dto.request.ResourceSearchReq;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.dto.response.RepoCtypeGroupResp;
import com.suwell.plss.record.standard.dto.response.RepoInfoResp;
import com.suwell.plss.record.standard.dto.response.RepoMetadataResp;
import com.suwell.plss.record.standard.dto.response.RepoTagResp;
import com.suwell.plss.record.standard.dto.response.RepositoryCountResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.dto.response.ResourceSearchResp;
import com.suwell.plss.record.standard.service.StandardRepositoryFacade;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * 库
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-08 18:47:30
 */
@RestController
@RequestMapping("/v1/repository")
public class RepositoryController {

    @Resource
    private StandardRepositoryFacade standardRepositoryFacade;

    public static final String MODE_NAME = "'后台-文库管理'";


    /**
     * 分页查询库列表
     */
    @PostMapping("/queryPage")
    public R<PageUtils<RepositoryResp>> queryPage(@RequestBody RepositoryQueryReq params) {
        PageUtils<RepositoryResp> page = standardRepositoryFacade.queryPage(params);
        return R.ok(page);
    }

    /**
     * 查询库列表
     */
    @PostMapping("/queryList")
    public R<List<RepositoryResp>> queryList(@RequestBody RepositoryQueryReq params) {
        List<RepositoryResp> list = standardRepositoryFacade.queryList(params);
        return R.ok(list);
    }

    /**
     * 可视化文库列表
     */
    @PostMapping("/visualPage/list")
    public R<List<RepositoryResp>> visualPageList() {
        List<RepositoryResp> list = standardRepositoryFacade.visualPageList();
        return R.ok(list);
    }


    /**
     * 按照库类型分组统计库数量
     */
    @PostMapping("/groupCtype")
    public R<List<RepoCtypeGroupResp>> groupCtype(@RequestBody RepositoryQueryReq params) {
        List<RepoCtypeGroupResp> list = standardRepositoryFacade.groupCtype(params);
        return R.ok(list);
    }

    /**
     * 信息
     */
    @PostMapping("/info/{id}")
    public R<RepositoryResp> info(@PathVariable("id") Long id) {
        RepositoryResp repository = standardRepositoryFacade.queryById(id);

        return R.ok(repository);
    }

    /**
     * 保存
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<Long> save(@RequestBody RepositoryAddReq repository) {
        return R.ok(standardRepositoryFacade.addRepository(repository));
    }

    /**
     * 修改
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R<Void> update(@RequestBody RepositoryModifyReq repository) {
        standardRepositoryFacade.modifyRepository(repository);

        return R.ok();
    }

    /**
     * 修改
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/updateMaterialView")
    public R<Void> update(@RequestBody RepositoryMaterialViewReq req) {
        standardRepositoryFacade.modifyMaterialViewRepository(req);
        return R.ok();
    }


    /**
     * 删除库
     */
    @Log(title = MODE_NAME, businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@PathVariable Long id) {
        standardRepositoryFacade.removeRepository(id);

        return R.ok();
    }

    /**
     * 新增库的元数据
     */
    @PostMapping("/saveMetadata")
    public R<Void> saveMetadata(@RequestBody RepositoryMetadataAddReq req) {
        standardRepositoryFacade.saveMetadata(req);
        return R.ok();
    }

    /**
     * 查询库的元数据
     */
    @PostMapping("/queryMetadata")
    public R<RepoMetadataResp> queryMetadata(@RequestBody RepositoryMetadataQueryReq req) {
        RepoMetadataResp resp = standardRepositoryFacade.queryMetadata(req);
        return R.ok(resp);
    }

    /**
     * 查询库的标签列表
     */
    @PostMapping("/listRepoTags")
    public R<List<RepoTagResp>> listRepoTags(@RequestBody RepoTagQueryReq req) {
        List<RepoTagResp> list = standardRepositoryFacade.listRepoTags(req);
        return R.ok(list);
    }

    /**
     * 查询库内文件的标签列表
     */
    @PostMapping("/listRepoRecordTags")
    public R<List<RepoTagResp>> listRepoRecordTags(@RequestBody RepoTagQueryReq req) {
        List<RepoTagResp> list = standardRepositoryFacade.listRepoRecordTags(req);
        return R.ok(list);
    }

    /**
     * 查询个人库id
     */
    @PostMapping("/queryPersonalRepoId")
    public R<Long> queryPersonalRepoId(@RequestBody(required = false) QueryOrAutoNewRepoReq req) {
        Long repoId = standardRepositoryFacade.queryPersonalRepoId(req);
        return R.ok(repoId);
    }


    /**
     * 将库转化一个目录，保留库的完整性
     */
    @PostMapping("/convertRepositoryToFolder")
    public R<Void> convertRepositoryToFolder(@RequestBody ConvertRepoToFolderReq req) {
        standardRepositoryFacade.convertRepositoryToFolder(req);
        return R.ok();
    }

    /**
     * 统计文库共享数量
     */
    @PostMapping("/statistic/repoCount")
    public R<RepositoryCountResp> statisticRepoCount(@RequestParam(value = "tenantId", required = false)
    Long tenantId) {
        RepositoryCountResp resp = standardRepositoryFacade.statisticRepoCount(tenantId);
        return R.ok(resp);
    }

    /**
     * 根据文件id查询所在文库信息
     */
    @Deprecated
    @PostMapping("/query/listRepoInfo/{recordId}")
    R<List<RepoInfoResp>> listRepoInfo(@PathVariable("recordId") Long recordId) {
        List<RepoInfoResp> list = standardRepositoryFacade.listRepoInfo(recordId);
        return R.ok(list);
    }

    /**
     * 根据文件id查询所在文库信息
     */
    @PostMapping("/query/listRepoInfoV2")
    R<List<RepoInfoResp>> listRepoInfoV2(@RequestBody Long recordId) {
        List<RepoInfoResp> list = standardRepositoryFacade.listRepoInfo(recordId);
        if (CollUtil.isEmpty(list)) {
            return R.ok(list);
        }
        Set<Long> repoIdSet = new HashSet<>();
        List<RepoInfoResp> repos = new ArrayList<>();
        for (RepoInfoResp repoInfoResp : list) {
            Long repoId = repoInfoResp.getId();
            if (!repoIdSet.contains(repoId)) {
                RepoInfoResp resp = new RepoInfoResp();
                resp.setId(repoId);
                resp.setName(repoInfoResp.getName());
                repos.add(resp);
                repoIdSet.add(repoId);
            }
        }
        return R.ok(repos);
    }

    /**
     * 查询指定的库或目录是否在指定的库类型下
     */
    @PostMapping("/containsByRepoCtype")
    R<Boolean> containsByRepoCtype(@RequestBody ContainsCtypeReq req) {
        boolean contains = standardRepositoryFacade.containsByRepoCtype(req);
        return R.ok(contains);
    }

    /**
     * 修复个人库下的，我的文档重复目录
     * TODO 0831版本删除
     */
    @PostMapping("/fixMyFolderForRepeat")
    public R<Void> fixMyFolderForRepeat() {
        standardRepositoryFacade.fixMyFolderForRepeat();
        return R.ok();
    }


    /**
     * 多文库目录树基于名称的检索
     */
    @PostMapping("/resourceSearch")
    public R<List<ResourceSearchResp>> resourceSearch(@RequestBody ResourceSearchReq req) {
        List<ResourceSearchResp> list = standardRepositoryFacade.resourceSearch(req);
        return R.ok(list);
    }

    /**
     * 文库收藏
     */
    @PostMapping("/repoCollect")
    public R<Void> repoCollect(@RequestBody RepoCollectReq req) {
        standardRepositoryFacade.repoCollect(req);
        return R.ok();
    }

    /**
     * 文库组织机构信息预填充
     */
    @GetMapping("/repoOrgPreFill")
    public void repoOrgPreFill(HttpServletResponse response) {
        standardRepositoryFacade.repoOrgPreFill(response);
    }

    /**
     * 文库组织机构信息预填充
     */
    @PostMapping("/importRepoOrgBatch")
    public R<Void> importRepoOrgBatch(MultipartFile file) {
        standardRepositoryFacade.importRepoOrgBatch(file);
        return R.ok();
    }

    /**
     * 获取文库内文件元数据列表
     */
    @PostMapping("/listRepoRecordMd/{repoId}")
    public R<List<MetadataResp>> listRepoRecordMd(@PathVariable Long repoId) {
        List<MetadataResp> list = standardRepositoryFacade.listRepoRecordMd(repoId);
        return R.ok(list);
    }


}
