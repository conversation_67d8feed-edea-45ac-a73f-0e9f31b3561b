package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_ENTITY;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordAiEntityProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractEntitiesApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyNlpExtractEntitiesAsyncApiImpl implements ThirdPartyNlpExtractEntitiesApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getEntity(MqRecordReqDTO mqRecordReqDTO) {
        log.info("async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordAiEntityProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_ENTITY));
//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(GET_ENTITY));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordAiEntityProcessEvent<>(mqRecordReqDTO, GET_ENTITY)));
    }
}
