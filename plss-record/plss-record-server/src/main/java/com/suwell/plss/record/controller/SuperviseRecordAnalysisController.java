package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.RecordFileStoreReq;
import com.suwell.plss.record.standard.dto.response.DataDistributionResp;
import com.suwell.plss.record.standard.dto.response.RecordFileStoreResp;
import com.suwell.plss.record.standard.service.SuperviseRecordAnalysisFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * record指标监管分析
 *
 * <AUTHOR>
 * @date 2024/7/29
 */
@RestController
@RequestMapping("/v1/supervise/analysis")
public class SuperviseRecordAnalysisController {

    @Resource
    private SuperviseRecordAnalysisFacade superviseRecordAnalysisFacade;

    /**
     * 数据量分布
     */
    @PostMapping("/dataDistribution")
    public R<DataDistributionResp> dataDistribution() {
        return R.ok(superviseRecordAnalysisFacade.dataDistribution());
    }

    /**
     * 文件入库情况
     */
    @PostMapping("/recordFileStore")
    public R<List<RecordFileStoreResp>> recordFileStore(@Validated @RequestBody RecordFileStoreReq req) {
        List<RecordFileStoreResp> recordFileStoreRespList = superviseRecordAnalysisFacade.recordFileStore(req);
        return R.ok(recordFileStoreRespList);
    }


}
