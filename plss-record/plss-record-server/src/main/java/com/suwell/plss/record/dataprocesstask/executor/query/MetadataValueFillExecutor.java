package com.suwell.plss.record.dataprocesstask.executor.query;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.domain.RepairMetadataStateDTO;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.entity.DocProcess;
import com.suwell.plss.record.enums.DataProcessTaskEnum;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.service.DocProcessService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordTypeMetadataService;
import com.suwell.plss.record.standard.dto.request.BatchSetMetadataValReq;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/12/24
 * @content
 */
@Slf4j
@Service
public class MetadataValueFillExecutor implements DataProcessTaskExecutor {

    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private DataProcessTaskService dataProcessTaskService;
    @Resource
    private DocProcessService docProcessService;

    @Override
    public DataProcessTaskEnum.BusinessType getBusinessType() {
        return DataProcessTaskEnum.BusinessType.BATCH_SET_METADATA_VALUE;
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-任务开始,taskId={}",logPrefix, taskId);
        while (true) {
            DataProcessTask task = dataProcessTaskService.getById(taskId);
            BatchSetMetadataValReq req = JSON.parseObject(task.getBusinessData(),BatchSetMetadataValReq.class);
            List<Long> recordIdList = req.getRecordIdList();
            Set<Long> finishRecordIdSet = Optional.ofNullable(req.getFinishRecordIdSet()).orElseGet(HashSet::new);

            DocProcess docProcess = null;
            for (Long recordId : recordIdList) {
                docProcess = docProcessService.getByRecordId(recordId);
                if(docProcess == null){
                    finishRecordIdSet.add(recordId);
                    log.error("{}任务-文档不存在,recordId={}",logPrefix, recordId);
                }else{
                    break;
                }
            }

            if(docProcess == null){
                // 完成
                long offsetPosition = Long.parseLong(recordIdList.size() + "");
                dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetPosition);
                log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,task.getTotalCount(), offsetPosition);
                return;
            }

            // 取元数据的规则定义
            List<TypeMetadataRuleResp> typeMetadataRules = recordTypeMetadataService.queryTypeMetadataRule(docProcess.getRecordTypeId());

            try{
                for (Long recordId : recordIdList) {
                    if(finishRecordIdSet.contains(recordId)){
                        continue;
                    }
                    recordMetadataValueService.updateMetaData(
                            null,
                            req.getMetadata(),
                            typeMetadataRules,
                            recordId,
                            true,
                            t->{},
                            false
                    );
                    finishRecordIdSet.add(recordId);
                }
                long offsetPosition = Long.parseLong(finishRecordIdSet.size() + "");
                dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetPosition);
                log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,task.getTotalCount(), offsetPosition);
                break;
            }catch (Exception e){
                req.setFinishRecordIdSet(finishRecordIdSet);
                dataProcessTaskService.lambdaUpdate()
                        .set(DataProcessTask::getBusinessData, JSON.toJSONString(req))
                        .eq(DataProcessTask::getTaskId,taskId)
                        .update();
                dataProcessTaskService.breakTask(taskId);
                log.error("{}任务-任务中断,taskId={},em={}", logPrefix, taskId, e.getMessage(), e);
                break;
            }
        }
    }
}
