package com.suwell.plss.record.migration.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.uuid.IdUtils;
import com.suwell.plss.record.convert.RecordMetadataValueConvertUtils;
import com.suwell.plss.record.entity.Metadata;
import com.suwell.plss.record.entity.MetadataCategory;
import com.suwell.plss.record.entity.MetadataCategoryMetadata;
import com.suwell.plss.record.entity.MetadataValue;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordMetadataValue;
import com.suwell.plss.record.entity.RecordRel;
import com.suwell.plss.record.entity.RecordType;
import com.suwell.plss.record.entity.RecordTypeMetadata;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.RecordMigrateService;
import com.suwell.plss.record.service.MetadataCategoryMetadataService;
import com.suwell.plss.record.service.MetadataCategoryService;
import com.suwell.plss.record.service.MetadataService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordRelService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RecordTypeMetadataService;
import com.suwell.plss.record.service.RecordTypeService;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.system.api.domain.SysCategoryRelationDTO;
import com.suwell.plss.system.api.domain.request.CategoryListReq;
import com.suwell.plss.system.api.domain.request.CategoryRelListReq;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.service.CategoryRpcService;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;
@Slf4j
@Service
public class RecordMigrateServiceImpl implements RecordMigrateService {
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    private RecordService recordService;
    @Resource
    private RecordRelService recordRelService;
    @Resource
    private RecordTypeService recordTypeService;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private MetadataCategoryMetadataService metadataCategoryMetadataService;
    @Resource
    private MetadataCategoryService metadataCategoryService;
    @Resource
    private CategoryRpcService categoryRpcService;

    private final ThreadLocal<Set<Long>> listRecordTypeIdLocal = ThreadLocal.withInitial(HashSet::new);
    private final ThreadLocal<Set<Long>> listMetadataIdLocal = ThreadLocal.withInitial(HashSet::new);
    private final ThreadLocal<Set<Long>> listMetadataCategoryIdLocal = ThreadLocal.withInitial(HashSet::new);

    @Override
    public void init() {
        listRecordTypeIdLocal.set(new HashSet<>());
        listMetadataIdLocal.set(new HashSet<>());
        listMetadataCategoryIdLocal.set(new HashSet<>());
    }

    @Override
    public void clear() {
        this.listMetadataIdLocal.remove();
        this.listMetadataCategoryIdLocal.remove();
        this.listRecordTypeIdLocal.remove();
    }
    /*
     * export
     * rc_record_rel
     * rc_record
     * rc_record_metadata_value
     * rc_metadata_value
     * sys_category_relation
     * sys_category
     */
    @Override
    public List<Long> exportRecord(List<Long> listRecordId, String savePath) {
        if (CollUtil.isEmpty(listRecordId)) {
            return Lists.newArrayList();
        }

        List<Long> listRecordIdEx = this.exportRecordRel(listRecordId, savePath);

        List<Record> listRecord = recordService.getListByIds(listRecordIdEx);
        if (listRecordIdEx.size() > listRecord.size()) {
            List<Long> list = CollUtil.subtractToList(listRecordIdEx, listRecord.stream().map(Record::getId).toList());
            log.warn("导出记录表数据不完整,缺失记录ID:{}", list);
        }

        Set<Long> collect = listRecord.stream().map(Record::getRecordtypeId).collect(Collectors.toSet());
        listRecordTypeIdLocal.get().addAll(collect);

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD;
        CommonUtil.seriallizeList(listRecord, filepath);

        this.exportRecordId(listRecord, savePath);
        this.exportRecordMetadataValue(listRecordIdEx, savePath);

        return listRecord.stream().map(Record::getId).toList();
    }

    public void exportRecordId(List<Record> listRecord, String savePath) {

        String filepath = savePath + "/recordId/" + IdUtils.simpleUUID() + ".rid";
        FileUtil.touch(filepath);

        try {
            FileOutputStream out = new FileOutputStream(filepath);
            for (Record rc : listRecord) {
                String content = rc.getId() + ", " + rc.getName() + "\n";
                out.write(content.getBytes());
            }
            out.flush();
            out.close();

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /*
     * export
     * rc_recordtype
     * rc_recordtype_metadata
     * rc_metadata
     * rc_metadata_category_metadata
     * rc_metadata_category
     */
    @Override
    public void exportRecordType(String savePath) {
        Set<Long> listRecordTypeId = listRecordTypeIdLocal.get();
        if (listRecordTypeId.isEmpty()) {
            return;
        }

        List<RecordType> listRecordType = recordTypeService
                .list(new LambdaQueryWrapper<RecordType>().in(RecordType::getId, listRecordTypeId));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_TYPE;
        CommonUtil.seriallizeList(listRecordType, filepath);

        this.exportRecordTypeMetadata(savePath);
        this.exportMetadata(savePath);
    }
    /*
    * export
    * rc_record_rel
     */
    private List<Long> exportRecordRel(List<Long> listRecordId, String savePath) {
        log.debug("导出记录关联表,listRecordId:{}", listRecordId);
        if (CollUtil.isEmpty(listRecordId)) {
            return Lists.newArrayList();
        }

        // 去重过滤recordId
        Set<Long> resultIds = Sets.newConcurrentHashSet(listRecordId);
        List<Long> tempSubtractToList = Lists.newArrayList(listRecordId);
        while (true) {
            List<Long> recordRelByIds = recordRelService.queryRecordRelByIds(tempSubtractToList);
            if (CollUtil.isEmpty(recordRelByIds)) {
                break;
            }

            // 查询结果里面的差集合
            tempSubtractToList = CollUtil.subtractToList(recordRelByIds, resultIds).stream().toList();
            log.debug("查询结果里面的差集合:{}", tempSubtractToList);
            if (CollUtil.isEmpty(tempSubtractToList)) {
                break;
            }
            resultIds.addAll(tempSubtractToList);
        }

        // 文档关系 做序列化磁盘操作
        log.debug("导出记录关联表,结果:{}", resultIds);
        if (CollUtil.isEmpty(resultIds)) {
            return Lists.newArrayList();
        }
        List<RecordRel> recordRelList = recordRelService.list(Wrappers.<RecordRel>lambdaQuery()
                .in(RecordRel::getRecordId, resultIds)
                .or()
                .in(RecordRel::getReferId, listRecordId)
                .orderByDesc(RecordRel::getReferId));
        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_REL;
        CommonUtil.seriallizeList(recordRelList, filepath);

        return CollUtil.unionDistinct(resultIds, listRecordId).stream().toList();
    }
    private void exportRecordTypeMetadata(String savePath) {
        Set<Long> listRecordTypeId = listRecordTypeIdLocal.get();
        List<RecordTypeMetadata> listRecordTypeMetadata = recordTypeMetadataService
                .list(new LambdaQueryWrapper<RecordTypeMetadata>()
                        .in(RecordTypeMetadata::getRecordtypeId, listRecordTypeId));

        if (listRecordTypeMetadata.isEmpty()) {
            return;
        }

        listMetadataIdLocal.get().addAll(listRecordTypeMetadata.stream()
                .map(RecordTypeMetadata::getMdId).collect(Collectors.toSet()));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_TYPE_METADATA;
        CommonUtil.seriallizeList(listRecordTypeMetadata, filepath);
    }
    private void exportMetadata(String savePath) {
        Set<Long> listMetadataId = listMetadataIdLocal.get();
        if (CollUtil.isEmpty(listMetadataId)) {
            return;
        }

        List<Metadata> listMetadata = metadataService.listByIds(listMetadataId);

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_METADATA;
        CommonUtil.seriallizeList(listMetadata, filepath);

        this.exportMetadataCategoryMetadata(savePath);
        this.exportMetadataCategory(savePath);
    }
    private void exportRecordMetadataValue(List<Long> listRecordId, String savePath) {
        if (CollUtil.isEmpty(listRecordId)) {
            return;
        }

        List<RecordMetadataValue> listRecordMetadataValue = recordMetadataValueService
                .list(new LambdaQueryWrapper<RecordMetadataValue>()
                        .in(RecordMetadataValue::getRecordId, listRecordId));

        if (listRecordMetadataValue.isEmpty()) {
            return;
        }

        List<MetadataValue> listMetadataValue = new ArrayList<>();
        for (RecordMetadataValue recordMetadataValue : listRecordMetadataValue) {
            if (StringUtils.isEmpty(recordMetadataValue.getMetadataValueJson())) {
                continue;
            }

            List<MetadataValueDTO> listMetadataValueDto = RecordMetadataValueConvertUtils
                    .metadataJson2MetadataValueDTO(recordMetadataValue.getMetadataValueJson());

            listMetadataValue.addAll(listMetadataValueDto.stream().map(dto -> {
                MetadataValue metadataValue = new MetadataValue();
                metadataValue.setId(dto.getId());
                metadataValue.setRecordId(dto.getRecordId());
                metadataValue.setMdId(dto.getMdId());
                metadataValue.setMdName(dto.getMdName());
                metadataValue.setMdValue(dto.getMdValue());
                metadataValue.setCreateTime(dto.getCreateTime());
                metadataValue.setModifiedTime(dto.getModifiedTime());
                metadataValue.setDataType(dto.getDataType());
                return metadataValue;
            }).toList());

            listMetadataIdLocal.get().addAll(listMetadataValueDto.stream().map(MetadataValueDTO::getMdId).toList());
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_METADATA_VALUE;
        CommonUtil.seriallizeList(listRecordMetadataValue, filepath);

        this.exportMetadataValue(listMetadataValue, savePath);
        // 导出系统分类表及系统分类关系表
        this.exportCategory(listRecordMetadataValue, savePath);
    }
    private void exportMetadataValue(List<MetadataValue> listMetadataValue, String savePath) {
        if (CollUtil.isEmpty(listMetadataValue)) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_METADATA_VALUE;
        CommonUtil.seriallizeList(listMetadataValue, filepath);
    }
    private void exportMetadataCategoryMetadata(String savePath) {
        Set<Long> listMetadataId = listMetadataIdLocal.get();
        List<MetadataCategoryMetadata> listMetadataCategoryMetadata =
                metadataCategoryMetadataService.list(new LambdaQueryWrapper<MetadataCategoryMetadata>()
                        .in(MetadataCategoryMetadata::getMdId, listMetadataId));

        if (listMetadataCategoryMetadata.isEmpty()) {
            return;
        }

        listMetadataCategoryIdLocal.get().addAll(listMetadataCategoryMetadata.stream().map(MetadataCategoryMetadata::getCategoryId)
                .collect(Collectors.toSet()));

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_METADATA_CATEGORY_METADATA;
        CommonUtil.seriallizeList(listMetadataCategoryMetadata, filepath);
    }
    private void exportMetadataCategory(String savePath) {
        Set<Long> listMetadataCategoryId = listMetadataCategoryIdLocal.get();
        if (CollUtil.isEmpty(listMetadataCategoryId)) {
            return;
        }

        List<MetadataCategory> listMetadataCategory = metadataCategoryService.listByIds(listMetadataCategoryId);
        if (listMetadataCategory.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_METADATA_CATEGORY;
        CommonUtil.seriallizeList(listMetadataCategory, filepath);
    }
    private void exportCategory(List<RecordMetadataValue> listRecordMetadataValue, String savePath) {

        List<RecordMetadataValue> listRecordMetadataValueNew = listRecordMetadataValue.stream()
                .filter(v -> StringUtils.isNotBlank(v.getMetadataValueJson())
                        && CollUtil.isNotEmpty(RecordMetadataValueConvertUtils.metadataJson2MetadataValueDTO(
                                v.getMetadataValueJson())
                            .stream()
                            .filter(x -> plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(x.getMdName())
                                && StringUtils.isNotBlank(x.getMdValue())).toList())
                ).toList();

        if (CollUtil.isEmpty(listRecordMetadataValueNew)) {
            log.warn("导出系统分类表数据为空....");
            return;
        }

        Set<Long> categoryLastIdSet = listRecordMetadataValueNew.stream()
                .map(x -> RecordMetadataValueConvertUtils.metadataJson2MetadataValueDTO(x.getMetadataValueJson()))
                .flatMap(List::stream)
                .toList().stream()
                .filter(x -> plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(x.getMdName())).toList().stream()
                .map(y -> Arrays.stream(y.getMdValue().split(StringPool.COMMA))
                        .map(Long::parseLong).collect(Collectors.toSet()))
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        log.info("rpc req:{}", JSON.toJSONString(categoryLastIdSet));
        R<List<List<SysCategory>>> toRootIdList = categoryRpcService.getToRootIdList(Lists.newArrayList(categoryLastIdSet));
        log.info("获取全链路分类ID rpc rsp:{}", JSON.toJSONString(toRootIdList));
        if (toRootIdList.isSuccess()) {
            List<List<SysCategory>> categoryList = toRootIdList.getData();
            if (CollUtil.isNotEmpty(categoryList)) {
                log.info("获取全链路分类ID rsp:{}", JSON.toJSONString(categoryList));
                Set<Long> categoryAllSet = Sets.newHashSet();
                for (List<SysCategory> categoryPathId : categoryList) {
                    List<SysCategory> categoryPathNewId = categoryPathId.stream().filter(Objects::nonNull).toList();
                    List<Long> categoryIdList = categoryPathNewId.stream().map(SysCategory::getId).toList();
                    categoryAllSet.addAll(categoryIdList);
                }

                if (CollUtil.isEmpty(categoryAllSet)) {
                    log.warn("获取全链路分类总集合ID为空....");
                    return;
                }

                // 导出系统分类 和系统分类关系表数据
                R<List<SysCategory>> queryCategoryList =  categoryRpcService.queryCategoryList(
                        Lists.newArrayList(categoryAllSet));

                if (queryCategoryList.isSuccess()) {
                    List<SysCategory> categories = queryCategoryList.getData();
                    if (CollUtil.isNotEmpty(categories)) {
                        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_CATEGORY;
                        CommonUtil.seriallizeList(categories, filepath);
                    }
                    R<List<SysCategoryRelationDTO>> r = categoryRpcService.queryCategoryRelationList(
                            Lists.newArrayList(categoryAllSet));
                    if (r.isSuccess()) {
                        List<SysCategoryRelationDTO> categoryRelations = r.getData();
                        if (CollUtil.isNotEmpty(categoryRelations)) {
                            String crFilePath =  savePath + "/" + IdUtils.simpleUUID()
                                    + ExportConstant.SUFFIX_CATEGORY_REL;
                            CommonUtil.seriallizeList(categoryRelations, crFilePath);
                        }
                    } else {
                        log.error("导出系统分类关系表失败,返回结果{},入参:{}", JSON.toJSONString(r), categoryAllSet);
                    }
                } else {
                    log.error("导出系统分类表失败,返回结果{},入参:{}", JSON.toJSONString(queryCategoryList), categoryAllSet);
                }
            }
        }
    }

    @Override
    public void importList(String extName, String filepath, boolean overwrite, Logger logger) {
        switch (extName) {
            case ExportConstant.SUFFIX_RECORD:
                importRecord(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_METADATA:
                importMetadata(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_METADATA_VALUE:
                importRecordMetadataValue(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_METADATA_CATEGORY:
                importMetadataCategory(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_METADATA_CATEGORY_METADATA:
                importMetadataCategoryMetadata(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_TYPE:
                importRecordType(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_TYPE_METADATA:
                importRecordTypeMetadata(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_REL:
                importRecordRel(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_CATEGORY:
                importCategory(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_CATEGORY_REL:
                importCategoryRel(filepath, overwrite, logger);
                break;
            default:
                break;
        }
    }
    private void importRecord(String filepath, boolean overwrite, Logger logger) {
        List<Record> listRecord = CommonUtil.deseriallizeList(filepath, Record.class);
        if (listRecord.isEmpty()) {
            return;
        }
        recordService.saveOrUpdateBatch(listRecord, overwrite);
        CommonUtil.renameFile(filepath);
    }
    private void importCategoryRel(String filepath, boolean overwrite, Logger logger) {
        List<SysCategoryRelationDTO> categoryRelations = CommonUtil.deseriallizeList(filepath,
                SysCategoryRelationDTO.class);
        if (CollUtil.isEmpty(categoryRelations)) {
            return;
        }

        try {
            CategoryRelListReq req = new CategoryRelListReq();
            req.setEntityList(categoryRelations);
            req.setUpdate(overwrite);

            categoryRpcService.saveOrUpdateBatchCategoryRelation(req);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import 导入系统分类关系失败 error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importCategory(String filepath, boolean overwrite, Logger logger) {
        List<SysCategory> categoryList = CommonUtil.deseriallizeList(filepath, SysCategory.class);
        if (CollUtil.isEmpty(categoryList)) {
            return;
        }

        try {
            CategoryListReq req = new CategoryListReq();
            req.setEntityList(categoryList);
            req.setUpdate(overwrite);

            categoryRpcService.saveOrUpdateBatchCategory(req);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import 导入系统分类失败 error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importRecordRel(String absolutePath, boolean overwrite, Logger logger) {
        List<RecordRel> recordRelList = CommonUtil.deseriallizeList(absolutePath, RecordRel.class);
        if (CollUtil.isEmpty(recordRelList)) {
            return;
        }

        try {
            recordRelService.saveOrUpdateBatch(recordRelList, overwrite);
            CommonUtil.renameFile(absolutePath);
        } catch (Exception ex) {
            logger.info("import recordRel error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importRecordMetadataValue(String filepath, boolean overwrite, Logger logger) {

        List<RecordMetadataValue> listRecordMetadataValue = CommonUtil.deseriallizeList(filepath,
                RecordMetadataValue.class);
        if (listRecordMetadataValue.isEmpty()) {
            return;
        }

        try {
            recordMetadataValueService.saveOrUpdateBatch(listRecordMetadataValue, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import metadata value error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importMetadata(String filepath, boolean overwrite, Logger logger) {

        List<Metadata> listMetadata = CommonUtil.deseriallizeList(filepath, Metadata.class);
        if (listMetadata.isEmpty()) {
            return;
        }

        try {
            metadataService.saveOrUpdateBatch(listMetadata, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import metadata error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importMetadataCategoryMetadata(String filepath, boolean overwrite, Logger logger) {

        List<MetadataCategoryMetadata> listMetadataCategoryMetadata =
                CommonUtil.deseriallizeList(filepath, MetadataCategoryMetadata.class);
        if (listMetadataCategoryMetadata.isEmpty()) {
            return;
        }

        try {
            metadataCategoryMetadataService.saveOrUpdateBatch(listMetadataCategoryMetadata, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import metadataCategoryMetadata error " + StringEscapeUtils.escapeJavaScript(
                    ex.getMessage() + ""));
        }
    }
    private void importMetadataCategory(String filepath, boolean overwrite, Logger logger) {

        List<MetadataCategory> listMetadataCategory = CommonUtil.deseriallizeList(filepath, MetadataCategory.class);
        if (listMetadataCategory.isEmpty()) {
            return;
        }

        try {
            metadataCategoryService.saveOrUpdateBatch(listMetadataCategory, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import metadataCategory error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importRecordType(String filepath, boolean overwrite, Logger logger) {
        List<RecordType> listRecordType = CommonUtil.deseriallizeList(filepath, RecordType.class);
        if (listRecordType.isEmpty()) {
            return;
        }

        try {
            recordTypeService.saveOrUpdateBatch(listRecordType, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import recordType error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    private void importRecordTypeMetadata(String filepath, boolean overwrite, Logger logger) {
        List<RecordTypeMetadata> listRecordTypeMetadata = CommonUtil.deseriallizeList(filepath,
                RecordTypeMetadata.class);
        if (listRecordTypeMetadata.isEmpty()) {
            return;
        }

        try {
            recordTypeMetadataService.saveOrUpdateBatch(listRecordTypeMetadata, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import recordTypeMetadata error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
}
