package com.suwell.plss.record.migration;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@Data
public class MetadataValueReq implements Serializable {

    private static final long serialVersionUID = -1143449801219767822L;
    /**
     * 元数据的id
     */
    private Long metadataId;
    /**
     * 元数据操作符 1:包含 2:等于 3:大于 4:小于 5:范围 6:前缀 7:后缀 8:模糊
     */
    private int op;
    /**
     * 元数据值
     */
    private List<String> listMetadataValue;
}
