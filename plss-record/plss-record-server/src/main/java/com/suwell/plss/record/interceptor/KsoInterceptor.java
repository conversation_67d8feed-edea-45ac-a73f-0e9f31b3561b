package com.suwell.plss.record.interceptor;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.utils.ForestDataType;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.domain.KsoHeaderIn;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.security.service.KsoAccessTokenService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 14:32 <br/>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KsoInterceptor<T> implements Interceptor<T> {

    private static final DateTimeFormatter customFormatter = DateTimeFormatter.ofPattern(
            "EEE, dd MMM yyyy HH:mm:ss zzz", Locale.US);
    private final KsoAccessTokenService ksoAccessTokenService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Override
    public boolean beforeExecute(ForestRequest req) {

        final String type = req.getType().getName();
        String path = req.getURI().getPath();
        final String jsonBody = req.getBody().encodeToString(ForestDataType.JSON);
        final String contentType = req.getContentType();
        String queryString = req.queryString();
        if (StringUtils.isNotEmpty(queryString)) {
            path = path + "?" + queryString;
        }
        final KsoHeaderIn ksoHeaderIn = new KsoHeaderIn();
        ksoHeaderIn.setBodyJson(jsonBody);
        ksoHeaderIn.setContentType(contentType);
        ksoHeaderIn.setUri(path);
        ksoHeaderIn.setMethod(type);

        final String secretKey = ksoAuthConfig.getSecretKey();
        final String accessKey = ksoAuthConfig.getAccessKey();

        final String accessToken = ksoAccessTokenService.getAppAccessToken(SecurityUtils.getTenantId());

        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("GMT"));
        String rfc1123Date = now.format(customFormatter);
        req.addHeader("X-Kso-Date", rfc1123Date);
        req.addHeader("Authorization", accessToken);
        String sign = kso1Sign(ksoHeaderIn.getMethod(), ksoHeaderIn.getUri(),
                ksoHeaderIn.getContentType() == null ? "" : ksoHeaderIn.getContentType(),
                rfc1123Date, ksoHeaderIn.getBodyJson().getBytes(StandardCharsets.UTF_8), accessKey, secretKey);
        req.addHeader("X-Kso-Authorization", sign);

        return true;
    }

    private static String kso1Sign(String method, String uri, String contentType, String ksoDate, byte[] requestBody,
                                   String accessKey, String secretKey) {
        try {
            String ksoSignature = getKso1Signature(method, uri, contentType, ksoDate, requestBody, secretKey);

            return String.format("KSO-1 %s:%s", accessKey, ksoSignature);
        } catch (Exception e) {
            throw new ServiceException("KSO-1 签名失败", e);
        }
    }

    private static String getKso1Signature(String method, String uri, String contentType, String ksoDate,
                                           byte[] requestBody, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        String sha256Hex = "";
        if (requestBody != null && requestBody.length > 0) {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(requestBody);
            sha256Hex = bytesToHex(hash);
        }
        String dataToSign = "KSO-1" + method + uri + contentType + ksoDate + sha256Hex;
        Mac mac = Mac.getInstance("HmacSHA256");

        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8),
                "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] macBytes = mac.doFinal(dataToSign.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(macBytes);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    public static void main(String[] args) {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("GMT"));
        String rfc1123Date = now.format(customFormatter);
        String uri = "/v7/depts/13/members?recursive=true&status=active&status=notactive&status=disabled&with_total=true";
        String ak = "com.kingsoft.plss";
        String sk = "SKnsbornwjxpkvem";
        String method = "GET";
        String s = kso1Sign(method, uri, "", rfc1123Date, null, ak, sk);
        System.out.println(rfc1123Date);
        System.out.println(s);
    }


}
