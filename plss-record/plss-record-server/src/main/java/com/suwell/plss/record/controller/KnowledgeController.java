package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.BasePageCondition;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.service.StandardKnowledgeFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 知识工程
 *
 * <AUTHOR>
 * @create 2024/06/12
 * @content
 */
@RestController
@RequestMapping("/v1/knowledge")
@Slf4j
public class KnowledgeController {

    @Resource
    private StandardKnowledgeFacade standardKnowledgeFacade;

    /**
     * 保存概念
     *
     * @param req 概念参数
     */

    @PostMapping("/concept/save")
    public R<Void> saveConcept(@RequestBody KnowledgeConceptReq req) {
        try {
            standardKnowledgeFacade.saveConcept(req);
            return R.ok();
        } catch (Exception e){
            log.error("保存概念失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除概念
     *
     * @param req 概念参数
     */
    @PostMapping("/concept/delete")
    public R<Void> deleteConcept(@RequestBody KnowledgeConceptReq req) {
        try {
            standardKnowledgeFacade.deleteConcept(req);
            return R.ok();
        }catch (Exception e){
            log.error("删除概念失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询概念列表
     *
     * @param req 查询概念参数
     */
    @PostMapping("/concept/list")
    public R<List<KnowledgeConceptResp>> queryConceptList(@RequestBody KnowledgeConceptReq req) {
        return R.ok(standardKnowledgeFacade.queryConceptList(req));
    }

    /**
     * 查询概念下关系列表
     *
     * @param req 查询概念关系参数
     */
    @PostMapping("/concept/relation/list")
    public R<List<KnowledgeRelationResp>> queryConceptRelationList(@RequestBody KnowledgeConceptReq req) {
        try {
            return R.ok(standardKnowledgeFacade.queryConceptRelationList(req));
        }catch (Exception e){
            log.error("查询概念下关系列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询属性列表
     *
     * @param req 概念参数
     */
    @PostMapping("/attribute/list")
    public R<List<KnowledgeAttributeResp>> queryAttributeList(@RequestBody KnowledgeAttributeReq req) {
        try {
            return R.ok(standardKnowledgeFacade.queryAttributeList(req));
        } catch (Exception e){
            log.error("查询属性列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 保存属性
     *
     * @param req 属性参数
     */
    @PostMapping("/attribute/save")
    public R<Void> saveAttribute(@RequestBody KnowledgeAttributeReq req) {
        try {
            standardKnowledgeFacade.saveAttribute(req);
            return R.ok();
        } catch (Exception e){
            log.error("保存属性失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除属性
     *
     * @param req 属性参数
     */
    @PostMapping("/attribute/delete")
    public R<Void> deleteAttribute(@RequestBody KnowledgeAttributeReq req) {
        try {
            standardKnowledgeFacade.deleteAttribute(req);
            return R.ok();
        } catch (Exception e){
            log.error("删除属性失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 保存关系
     *
     * @param req 关系参数
     */
    @PostMapping("/relation/save")
    public R<Void> saveRelation(@RequestBody KnowledgeRelationReq req) {
        try {
            standardKnowledgeFacade.saveRelation(req);
            return R.ok();
        }catch (Exception e) {
            log.error("保存关系失败:{}", e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询关系列表
     *
     * @param req 关系参数
     */
    @PostMapping("/relation/list")
    public R<List<KnowledgeRelationResp>> queryRelationList(@RequestBody KnowledgeRelationReq req) {
        try {
            return R.ok(standardKnowledgeFacade.queryRelationList(req));
        } catch (Exception e){
            log.error("查询关系列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除关系
     *
     * @param req 关系参数
     */
    @PostMapping("/relation/delete")
    public R<List<KnowledgeRelationResp>> deleteRelation(@RequestBody KnowledgeRelationReq req) {
        try {
            standardKnowledgeFacade.deleteRelation(req);
            return R.ok();
        } catch (Exception e){
            log.error("删除关系失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 模型下根据概念查询关系列表
     *
     * @param req
     * @return
     */
    @PostMapping("/model/queryRelationByConcept")
    public R<List<KnowledgeModelRelationResp>> queryRelationByConcept(@RequestBody KnowledgeConceptReq req) {
        Long conceptId = req.getId();
        if (conceptId == null) {
            return R.error("conceptId不能为空");
        }
        try {
            return R.ok(standardKnowledgeFacade.queryRelationByConcept(conceptId));
        }catch (Exception e){
            log.error("查询模型下根据概念查询关系列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 模型列表  分页
     *
     * @param req
     * @return
     */
    @PostMapping("/model/list")
    public R<PageUtils<KnowledgeModelResp>> modelList(@Valid @RequestBody BasePageCondition req) {
        return R.ok(standardKnowledgeFacade.listModel(req));
    }

    /**
     * 新增模型
     *
     * @param req
     * @return
     */
    @PostMapping("/model/save")
    public R<Long> modelSave(@Validated(value = ReqGroup.Add.class) @RequestBody KnowledgeModelReq req) {
        try {
            return R.ok(standardKnowledgeFacade.saveModel(req));
        } catch (Exception e){
            log.error("新增模型失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 编辑保存模型
     *
     * @param req
     * @return
     */
    @PostMapping("/model/edit")
    public R<Void> modelEdit(@Validated(value = ReqGroup.Edit.class) @RequestBody KnowledgeModelReq req) {
        try {
            standardKnowledgeFacade.editModel(req);
            return R.ok();
        }catch (Exception e){
            log.error("编辑保存模型失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 重命名模型
     *
     * @param req
     * @return
     */
    @PostMapping("/model/rename")
    public R<Void> modelRename(@Validated(value = ReqGroup.Rename.class) @RequestBody KnowledgeModelReq req) {
        try {
            standardKnowledgeFacade.renameModel(req);
            return R.ok();
        } catch (Exception e){
            log.error("重命名模型失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除模型
     *
     * @param req
     * @return
     */
    @PostMapping("/model/delete")
    public R<Void> modelDelete(@Validated(value = ReqGroup.Del.class) @RequestBody KnowledgeModelReq req) {
        try {
            standardKnowledgeFacade.deleteModel(req);
            return R.ok();
        } catch (Exception e){
            log.error("删除模型失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 模型详情
     *
     * @param req
     * @return
     */
    @PostMapping("/model/detail")
    public R<KnowledgeModelResp> modelDetail(@Validated(value = ReqGroup.Del.class) @RequestBody KnowledgeModelReq req) {
        return R.ok(standardKnowledgeFacade.modelDetail(req));
    }

    /**
     * 模型列表 （入库方案下拉框）
     *
     * @return 已配置的模型
     */
    @GetMapping("/model/enabled_list")
    public R<List<KnowledgeModelResp>> listEnableModel() {
        return R.ok(standardKnowledgeFacade.listEnableModel());
    }


    /**
     * 模型刷新
     *
     * @param req
     * @return
     */
    @PostMapping("/model/refresh")
    public R<KnowledgeModelResp> modelRefresh(@RequestBody KnowledgeModelReq req) {
        return R.ok(standardKnowledgeFacade.modelRefresh(req));
    }


    /**
     * 查询模型范围
     * 概念下的属性
     */
    @PostMapping("/model/scope/concept/attribute")
    public R<List<String>> modelScopeConceptAttribute(@RequestBody KnowledgeModelScopeReq req) {
        return R.ok(standardKnowledgeFacade.modelScopeConceptAttribute(req));
    }

    /**
     * 查询模型范围
     * 关系下的属性
     */
    @PostMapping("/model/scope/relation/attribute")
    public R<List<String>> modelScopeRelationAttribute(@RequestBody KnowledgeModelScopeReq req) {
        return R.ok(standardKnowledgeFacade.modelScopeRelationAttribute(req));
    }

    /**
     * 查询模型范围
     * 关系下的属性
     */
    @PostMapping("/model/scope/concept")
    public R<List<String>> modelScopeConcept(@RequestBody KnowledgeModelScopeReq req) {
        return R.ok(standardKnowledgeFacade.modelScopeConcept(req));
    }

    /**
     * 查询模型范围
     * 关系下的属性
     */
    @PostMapping("/model/scope/relation")
    public R<List<String>> modelScopeRelation(@RequestBody KnowledgeModelScopeReq req) {
        return R.ok(standardKnowledgeFacade.modelScopeRelation(req));
    }

}
