package com.suwell.plss.record.dataprocesstask;

import com.suwell.plss.record.enums.TaskConditionEnums;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @create 2024/12/25
 * @content
 */
public abstract class AbstractTaskCondition {

    /**
     * 获取条件类型
     * @return
     */
    public abstract TaskConditionEnums.ConditionTypeEnum getConditionType();

    /**
     * 获取处理数据总数
     * @param paramJson
     * @return
     */
    public abstract Long getTotalCount(String paramJson);

    /**
     * 初始化
     * @param paramJson
     * @param batchSize
     */
    public abstract void init(String paramJson,Integer batchSize);

    /**
     * 是否有下一批数据
     * @return
     */
    public abstract boolean hasNext();

    /**
     * 完成
     */
    public abstract void finish();

    /**
     * 开始日志
     * @param consumer
     */
    public abstract void startLog(Consumer<List<Long>> consumer);

    /**
     * 完成日志
     * @param func
     */
    public abstract void finishLog(Function<List<Long>,Boolean> func);

    /**
     * 中断日志
     * @param consumer
     */
    public abstract void breakLog(Consumer<List<Long>> consumer);

    /**
     * 查询数据
     * @param paramJson
     * @return
     */
    public abstract List<Long> queryData(String paramJson);

    /**
     * 更新数据
     * @param recordIdList
     */
    public abstract void updateData(List<Long> recordIdList);

}
