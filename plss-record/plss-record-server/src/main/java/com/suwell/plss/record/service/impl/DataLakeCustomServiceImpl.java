package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.data.delivery.sdk.constant.DataPackageConstant;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.record.conf.DataDeliveryConf;
import com.suwell.plss.record.conf.OpenWpsCheckConfig;
import com.suwell.plss.record.entity.DataLake;
import com.suwell.plss.record.entity.DataLakeCustom;
import com.suwell.plss.record.enums.DataLakeEnums;
import com.suwell.plss.record.mapper.DataLakeCustomMapper;
import com.suwell.plss.record.migration.DataImmigrateHandler;
import com.suwell.plss.record.migration.impl.DataImportReviewServiceImpl;
import com.suwell.plss.record.service.DataDeliveryServer;
import com.suwell.plss.record.service.DataLakeCustomService;
import com.suwell.plss.record.standard.domain.DataSubscribeInfo;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskAddReq;
import com.suwell.plss.record.standard.dto.request.InternalImportReviewReq;
import com.suwell.plss.record.standard.enums.DataDeliveryEnum.TaskType;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.standard.service.DataLoadTaskFacade;
import jakarta.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Slf4j
public class DataLakeCustomServiceImpl extends ServiceImpl<DataLakeCustomMapper, DataLakeCustom> implements
        DataLakeCustomService, DataDeliveryServer {

    @Resource
    private DataDeliveryConf dataDeliveryConf;

    @Value("${java.io.tmpdir}")
    private String sharedPath;

    @Resource
    private OpenWpsCheckConfig openWpsCheckConfig;

    @Resource
    private AsyncTaskExecutor asyncTaskExecutor;

    @Resource
    private DataImportReviewServiceImpl dataImportReviewService;

    private final String PATH_PREFIX = "/swdata/builtin/";

    @Resource
    private DataLoadTaskFacade dataLoadTaskFacade;

    @Override
    @DSTransactional
    public void syncRemoteDataLake(int pageSize) {
        DataSubscribeInfo info = dataDeliveryConf.getSubscribeInfo();

        if (!info.isSubscribeSwitch()) {
            throw new ServiceException("数据订阅未开启,无法同步数据湖定制数据");
        }
        DataLakeCustom maxDataLake = lambdaQuery().select(DataLakeCustom::getId)
                .orderByDesc(DataLakeCustom::getId)
                .last("limit 1").one();
        long size = pageSize <= 0 || pageSize > 500 ? 500 : pageSize;
        Long lastId = Objects.isNull(maxDataLake) ? 0 : maxDataLake.getId();
        log.info("开始同步数据湖定制数据,pageSize:{},lastId:{}", size, lastId);

        JSONObject body = new JSONObject();
        body.put("page", 1);
        body.put("pageSize", size);
        body.put("minId", lastId);
        log.info("请求数据湖定制数据:{}", body);
        try (HttpResponse response = getHttpRequest(info.getSubscribeAddress(), DATA_PACKAGE_LIST_CUSTOM, info.getAccessKey())
                .method(Method.POST).contentType("application/json")
                .body(body.toJSONString()).timeout(5000).execute()) {
            if (response.getStatus() != 200) {
                throw new ServiceException("请求数据湖定制数据失败");
            }
            R<PageUtils<DataLakeCustom>> r = JSONObject.parseObject(response.body(), new TypeReference<>() {
            });
            if (r.isError()) {
                log.error("请求数据湖定制数据失败:{}", r);
                throw new ServiceException(r.getMsg());
            }
            List<DataLakeCustom> list = r.getData().getList();
            list.forEach(dataLake -> dataLake.setStatus(DataLakeEnums.NOT_IMPORT.getCode()));
            saveOrUpdateBatch(list);
        } catch (Exception e) {
            throw new ServiceException("请求数据湖定制数据失败", e);
        }
    }

    @Override
    @DSTransactional
    public void subscribeDiffData(LocalDate date, List<String> customKeyList) {
        log.info("开始订阅增量数据,date:{},customKeyList={}",date,JSON.toJSONString(customKeyList));
        DataSubscribeInfo info = dataDeliveryConf.getSubscribeInfo();
        if (!info.isSubscribeSwitch()) {
            throw new ServiceException("自动订阅开关未开启");
        }
        if (CollectionUtil.isEmpty(customKeyList)) {
            return;
        }
        List<DataLakeCustom> list = lambdaQuery()
                .eq(DataLakeCustom::getFileDate, date)
                .in(DataLakeCustom::getCustomKey, customKeyList)
                .list();
        log.info("查询出待订阅dataLake:{}",list);
        if (CollectionUtil.isEmpty(list)) {
            log.info("指定日期没有数据, date:{}, customKeyList:{}", date, customKeyList);
            return;
        }
        for (DataLakeCustom dataLakeCustom : list) {
            String taskKey = requestFileTask(dataLakeCustom.getCustomKey(), info.getSubscribeAddress(), info.getAccessKey(),
                    date);
            if (StringUtils.isEmpty(taskKey)) {
                log.error("请求订阅文件失败, dataLake:{}", dataLakeCustom);
                continue;
            }
            dataLakeCustom.setStatus(DataLakeEnums.PREPARING.getCode());
            dataLakeCustom.setTaskKey(taskKey);
            updateById(dataLakeCustom);
        }
    }

    private String requestFileTask(String customKey, String url, String ak, LocalDate date) {
        JSONObject jsonObject = JSONObject.of();
        jsonObject.put("downType", 3);
        jsonObject.put("startDate", date.toString());
        jsonObject.put("endDate", date.toString());
        jsonObject.put("customKey", customKey);
        try (HttpResponse response = getHttpRequest(url, SUBSCRIBE_TASK_CUSTOM, ak).method(Method.POST)
                .body(jsonObject.toJSONString())
                .contentType("application/json")
                .timeout(10000)
                .execute()) {
            log.info("request download task response:{}", response);
            if (response.getStatus() != 200) {
                return null;
            }
            return Optional.of(JSONObject.parseObject(response.body()))
                    .map(json -> json.getJSONObject("data"))
                    .map(json -> json.getString("taskId"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("请求订阅文件任务异常", e);
            return null;
        }
    }

    @Override
    public void downloadFile(DataLakeCustom custom) {
        DataSubscribeInfo info = dataDeliveryConf.getSubscribeInfo();
        boolean ready = checkDownTaskState(custom, info.getSubscribeAddress(), info.getAccessKey());
        if (!ready) {
            log.warn("下载任务未准备就绪, dataLake:{}", custom);
            return;
        }
        String dataDir = "/subscribeFolder/" + System.currentTimeMillis() + "/";
        // 容器内地址
        Path downloadPath = Paths.get(sharedPath, dataDir);
        log.info("下载文件到临时目录:{}, dataLake:{}", downloadPath, custom);
        boolean dealt = dealSubscribeFile(custom.getTaskKey(), info, downloadPath);
        if (!dealt) {
            log.error("下载文件失败, dataLake:{}", custom);
            custom.setStatus(DataLakeEnums.IMPORT_FAIL.getCode());
            updateById(custom);
            return;
        }
        // 将文件移动到共享盘中，方便数据导入
        FileUtil.moveContent(downloadPath, Paths.get(DataImmigrateHandler.PATH_PREFIX, dataDir), true);
        FileUtil.del(downloadPath);
        //审核开关
        if (openWpsCheckConfig.isWithDataImportAudit()) {
            //送审
            custom.setDataDir(dataDir);
            custom.setStatus(DataLakeEnums.SEND_REVIEW_ING.getCode());
            updateById(custom);
            asyncTaskExecutor.submit(() -> {
                InternalImportReviewReq reviewReq = new InternalImportReviewReq();
                reviewReq.setDirname(dataDir);
                reviewReq.setVersion(dataDir);
                RecordEnum.DataLoadTaskOverReason dataLoadTaskOverReason = dataImportReviewService.importDataReview(PATH_PREFIX, reviewReq);
                if(dataLoadTaskOverReason == RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_EXCEPTION){
                    //送审异常
                    custom.setStatus(DataLakeEnums.REVIEW_FAIL.getCode());
                }else if(dataLoadTaskOverReason == RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_NORMAL){
                    //送审中
                    custom.setStatus(DataLakeEnums.REVIEW_ING.getCode());
                }
                updateById(custom);
            });
        }else{
            DataLoadTaskAddReq addReq = new DataLoadTaskAddReq();
            addReq.setDataDir(dataDir);
            addReq.setTaskTitle(custom.getDataName());
            addReq.setTaskDesc(TaskType.AUTO.getDesc());
            addReq.setTaskType(TaskType.AUTO.getCode());
            addReq.setNeedDelFile(true);
            Long taskId = dataLoadTaskFacade.addDataLoadTask(addReq);
            if (taskId != null) {
                custom.setTaskId(taskId);
                custom.setStatus(DataLakeEnums.IMPORTING.getCode());
                updateById(custom);
            }
        }
    }

    private boolean checkDownTaskState(DataLakeCustom custom, String url, String sk) {
        JSONObject param = JSONObject.of();
        param.put("downType", 3);
        param.put("taskId", custom.getTaskKey());
        try (HttpResponse response = getHttpRequest(url, SUBSCRIBE_TASK_STATE_CUSTOM, sk)
                .method(Method.POST)
                .timeout(10000)
                .body(param.toJSONString())
                .execute()) {
            if (response.getStatus() != 200) {
                log.error("查询下载任务状态失败, response:{}", response);
                return false;
            }
            JSONObject jsonObject = JSONObject.parseObject(response.body());
            log.info("查询下载任务状态:{}", jsonObject);
            // 3 数据准备就绪，可以下载
            return jsonObject.getBooleanValue("success") &&
                    Objects.equals(jsonObject.getJSONObject("data").getInteger("downState"), 3);
        } catch (Exception e) {
            log.error("查询下载任务状态异常", e);
            return false;
        }
    }

    private boolean dealSubscribeFile(String taskKey, DataSubscribeInfo info, Path downloadPath) {
        JSONObject jsonObject = JSONObject.of();
        jsonObject.put("downType", 3);
        jsonObject.put("taskId", taskKey);
        long start = System.currentTimeMillis();
        try (HttpResponse response = getHttpRequest(info.getSubscribeAddress(), SUBSCRIBE_DOWNLOAD_CUSTOM, info.getAccessKey())
                .method(Method.POST)
                .body(jsonObject.toJSONString())
                .contentType("application/json")
                .timeout(10000)
                .execute()) {
            if (response.getStatus() != 200) {
                log.error("下载文件失败, response:{}", response);
                return false;
            }
            log.info("request download file interface cost: {}ms", System.currentTimeMillis() - start);
            String filename = response.header(DataPackageConstant.DATA_NAME_HEADER);
            if (StringUtils.isEmpty(filename)) {
                log.error("下载文件失败，未知文件名, filename:{}", filename);
                return false;
            }
            filename = URLDecoder.decode(filename, StandardCharsets.UTF_8);
            if (!filename.endsWith(".zip")) {
                log.error("下载文件失败，文件名不是zip格式, filename:{}", filename);
                return false;
            }
            start = System.currentTimeMillis();
            Path path = downloadPath.resolve(filename);
            InputStream is = response.bodyStream();
            FileUtil.writeFromStream(new BufferedInputStream(is, 1024 * 1024), path.toFile());
            log.info("writeFromStream cost: {}ms", System.currentTimeMillis() - start);
            start = System.currentTimeMillis();
            ZipUtil.unzip(path.toFile(), downloadPath.toFile());
            log.info("unzip cost: {}ms", System.currentTimeMillis() - start);
            start = System.currentTimeMillis();
            FileUtil.del(path);
            log.info("delete temp file cost: {}ms", System.currentTimeMillis() - start);
            return true;
        }
    }
}
