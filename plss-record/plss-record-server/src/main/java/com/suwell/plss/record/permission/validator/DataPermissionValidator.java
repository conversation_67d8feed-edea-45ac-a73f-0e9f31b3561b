package com.suwell.plss.record.permission.validator;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.suwell.plss.framework.common.config.ClassifiedSwitchConfig;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.enums.UserClassifiedEnum;
import com.suwell.plss.framework.common.enums.VisitType;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.exception.auth.NotPermissionException;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.datapermission.enums.VisitUserType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.domain.ResourceChainDTO;
import com.suwell.plss.record.entity.FolderRecord;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.Repository;
import com.suwell.plss.record.entity.ResourceManager;
import com.suwell.plss.record.entity.ResourcePermission;
import com.suwell.plss.record.mapper.ResourceManagerMapper;
import com.suwell.plss.record.mapper.ResourcePermissionMapper;
import com.suwell.plss.record.service.FolderRecordService;
import com.suwell.plss.record.service.FolderService;
import com.suwell.plss.record.service.RecordBorrowService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RepositoryService;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.record.standard.enums.RepositoryEnum;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;

/**
 * 数据权限验证器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class DataPermissionValidator implements RecordValidator {

    private static final String DEFAULT_ERROR_MSG = "权限校验失败，出现未知错误，请联系管理员";

    @Resource
    private ExecutorService dataPermissionCheckPool;
    @Lazy
    @Resource
    protected ResourcePermissionMapper resourcePermissionMapper;
    @Lazy
    @Resource
    protected ResourceManagerMapper resourceManagerMapper;
    @Lazy
    @Resource
    protected FolderRecordService folderRecordService;
    @Lazy
    @Resource
    protected FolderService folderService;
    @Lazy
    @Resource
    protected RecordService recordService;
    @Lazy
    @Resource
    protected RepositoryService repositoryService;
    @Lazy
    @Resource
    protected RecordBorrowService recordBorrowService;
    @Lazy
    @Resource
    protected ClassifiedSwitchConfig classifiedSwitchConfig;
    @Lazy
    @Resource
    protected KsoAuthConfig ksoAuthConfig;

    @Override
    public List<Long> filterValid(List<Long> resourceIds, PermissionMask... masks) {
        List<Long> invalidList = filterInvalid(resourceIds, masks);
        List<Long> validList = Lists.newArrayList(resourceIds);
        validList.removeAll(invalidList);
        return validList;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Long> filterInvalid(List<Long> resourceIds, PermissionMask... masks) {
        CompletableFuture<Long>[] array = resourceIds.stream().map(resourceId -> CompletableFuture.supplyAsync(() -> {
            validate(resourceId, masks);
            return null;
        }, dataPermissionCheckPool).exceptionally(t -> resourceId)).toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(array).join();
        return Arrays.stream(array).map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList());
    }


    @Override
    public void validate(Long resourceId, PermissionMask... masks) throws NotPermissionException {
        if (Objects.isNull(resourceId) || masks.length == 0) {
            log.error("基础权限校验，传递参数为空，resourceId:【{}】, masks:【{}】", resourceId, Arrays.toString(masks));
            throw new NotPermissionException(DEFAULT_ERROR_MSG);
        }
        ValidateContext context = new ValidateContext();
        context.managerValid(onlyManager(masks));
        try {
            // 前置业务自定义校验
            postProcessBeforeHandle(context, resourceId, masks);
        } catch (Exception e) {
            log.error("前置校验失败", e);
            throw new NotPermissionException(DEFAULT_ERROR_MSG);
        }
        if (context.isHasError()) {
            throw new NotPermissionException(context.getErrorMsg());
        }
        if (context.isPassAll()) {
            return;
        }
        if (context.isUseBaseValid()) {
            try {
                // 基础通用校验
                baseCheck(context, resourceId, masks);
            } catch (NotPermissionException e) {
                log.error("基础通用校验失败", e);
                context.hasError(DEFAULT_ERROR_MSG);
            }
            if (context.isHasError()) {
                throw new NotPermissionException(context.getErrorMsg());
            }
        }
        try {
            // 后置业务自定义校验
            postProcessAfterHandle(context, resourceId, masks);
        } catch (Exception e) {
            log.error("后置校验失败", e);
            context.hasError(DEFAULT_ERROR_MSG);
        }
        if (context.isHasError()) {
            throw new NotPermissionException(context.getErrorMsg());
        }
    }

    @NotNull
    @Override
    public List<String> listResourceVisitorIds() {
        List<String> visitorIds = Lists.newArrayList();
        if (ksoAuthConfig.isEnable()) {
            String userId = VisitUserType.USER.wrapId(SecurityUtils.getUserId());
            visitorIds.add(userId);
            List<String> orgIds = VisitUserType.ORGANIZATION.wrapIdList(SecurityUtils.getOrgList());
            visitorIds.addAll(orgIds);
            List<String> roleIds = VisitUserType.USER_ROLE.wrapIdList(SecurityUtils.getRoleList());
            visitorIds.addAll(roleIds);
            return visitorIds;
        }
        String userId = SecurityUtils.getUserId();
        visitorIds.add(userId);
        visitorIds.addAll(SecurityUtils.getOrgList());
        visitorIds.addAll(SecurityUtils.getRoleList());
        return visitorIds;
    }

    private void baseCheck(ValidateContext context, Long resourceId, PermissionMask... basicPermission) {
        // 资源校验
        List<String> visitorIds = listResourceVisitorIds();
        List<ResourceChainDTO> chainDTOS = listResourceChains(resourceId);
        if (CollectionUtil.isEmpty(chainDTOS)) {
            context.hasError("资源链路为空，对资源没有任何权限");
            return;
        }
        List<Long> repoIds = chainDTOS.stream().map(ResourceChainDTO::getRepoId)
                .collect(Collectors.toList());
        Long count = resourceManagerMapper.selectCount(new LambdaQueryWrapper<ResourceManager>()
                .in(ResourceManager::getResourceId, repoIds)
                .in(ResourceManager::getVisitorId, visitorIds));
        if (count > 0) {
            context.pass();
            return;
        }
        Long shared = repositoryService.lambdaQuery()
                .in(Repository::getId, repoIds)
                .eq(Repository::getShareType, RepositoryEnum.ShareType.ANY_SHARE.getCode())
                .count();
        if (shared > 0) {
            context.pass();
            return;
        }
        // 新版权限后端只有管理权限，所以只判断是否为管理员即可，权限组中也不存在管理权限，所以一下校验忽略
        Set<Long> needAuthResourceIds = listNeedAuthResourceIds(chainDTOS, resourceId, !isPrivateResource(resourceId));
        if (hasPermission(needAuthResourceIds, visitorIds, basicPermission)) {
            context.pass();
            return;
        }
        context.hasError("暂无权限，请联系管理员");
    }

    public boolean hasPermission(Set<Long> resourceIds, List<String> visitorIds, PermissionMask... masks) {
        if (CollectionUtil.isEmpty(resourceIds) || CollectionUtil.isEmpty(visitorIds) || masks.length == 0) {
            return false;
        }
        List<ResourcePermission> list = resourcePermissionMapper.selectList(
                new LambdaQueryWrapper<ResourcePermission>()
                        .in(ResourcePermission::getResourceId, resourceIds)
                        .in(ResourcePermission::getVisitorId, visitorIds));
        for (ResourcePermission permission : list) {
            for (PermissionMask mask : masks) {
                if (mask.checkMask(permission.getPvalue())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取资源的有效的私有资源id
     * 查询截止到距离当前资源最近的私有资源
     *
     * @param resourceId 资源id
     * @return 截止到距离当前资源最近的私有资源
     */
    @Override
    public List<ResourceChainDTO> listResourceChains(Long resourceId) {
        List<ResourceChainDTO> result = Lists.newArrayList();
        switch (getResourceType()) {
            case REPOSITORY:
            case FOLDER:
                ResourceChainDTO folderChain = folderService.getResourceChain(resourceId);
                if (Objects.nonNull(folderChain)) {
                    result.add(folderChain);
                }
                return result;
            case RECORD:
                List<Long> recordIds = Lists.newArrayList(resourceId);
                Map<Long, List<ResourceChainDTO>> recordChains = batchListRecordChains(recordIds);
                return recordChains.getOrDefault(resourceId, Lists.newArrayList());
        }
        return result;
    }

    @Override
    public Map<Long, List<ResourceChainDTO>> batchListRecordChains(List<Long> recordIds) {
        if (getResourceType() != ResourceType.RECORD) {
            throw new ServiceException("资源类型不匹配");
        }
        List<Record> records = recordService.getListByIds(recordIds);
        if (CollectionUtil.isEmpty(records)) {
            return Map.of();
        }
        Map<Long, Boolean> recordPrivateMap = records.stream()
                .collect(Collectors.toMap(Record::getId,
                        r -> Objects.equals(r.getVisitType(), VisitType.PRIVATE.getCode())));

        List<FolderRecord> folderRecords = folderRecordService.listWithWrapper(new LambdaQueryWrapper<FolderRecord>()
                .select(FolderRecord::getRecordId, FolderRecord::getFolderId)
                .in(FolderRecord::getRecordId, recordIds));
        Map<Long, List<Long>> recordFolderMap = folderRecords.stream()
                .collect(Collectors.groupingBy(FolderRecord::getRecordId,
                        Collectors.mapping(FolderRecord::getFolderId, Collectors.toList())));
        List<Long> allFolderIds = folderRecords.stream().map(FolderRecord::getFolderId).collect(Collectors.toList());
        List<ResourceChainDTO> folderChains = folderService.listResourceChain(allFolderIds);
        Map<Long, ResourceChainDTO> folderChainMap = folderChains.stream()
                .collect(Collectors.toMap(ResourceChainDTO::getResourceId, Function.identity()));
        Map<Long, List<ResourceChainDTO>> result = Maps.newHashMap();
        recordFolderMap.forEach((recordId, folderIds) -> {
            List<ResourceChainDTO> list = folderIds.stream().map(folderId -> {
                ResourceChainDTO resourceChain = folderChainMap.get(folderId);
                if (Objects.isNull(resourceChain)) {
                    return null;
                }
                ResourceChainDTO clone = resourceChain.clone();
                clone.getResourceChain().add(recordId);
                clone.setPrivateResource(recordPrivateMap.getOrDefault(recordId, false));
                if (clone.isPrivateResource()) {
                    clone.getPrivateResourceIds().add(recordId);
                }
                return clone;
            }).filter(Objects::nonNull).toList();
            result.put(recordId, list);
        });
        return result;
    }

    @Override
    public Set<Long> listNeedAuthResourceIds(List<ResourceChainDTO> chainDTOS, Long targetResourceId,
            boolean showExtends) {
        if (!showExtends) {
            return Sets.newHashSet(targetResourceId);
        }
        return chainDTOS.stream().map(chainDTO -> {
            List<Long> chain = chainDTO.getResourceChain();
            List<Long> privateResourceIds = chainDTO.getPrivateResourceIds();
            int privateSize = privateResourceIds.size();
            // 没有私有时，返回整个链路
            if (privateSize == 0) {
                return chain;
            } else if (privateSize == 1) {
                /*
                 链路中有一个私有资源时，区分两种情况:
                 1、私有资源在最后，则继承的链路去掉最后一个即可
                 2、私有资源在中间，则截取私有资源到最后
                 */
                Long aLong = privateResourceIds.get(0);
                int i = chain.indexOf(aLong);
                if (i == chain.size() - 1) {
                    return chain.subList(0, i);
                }
                return chain.subList(i, chain.size());
            } else {
                /*
                 有多个私有资源时，加入最后一个资源是私有的，由于获取的是其资源的继承权限，所以忽略掉
                 所以只截取倒数第二个到链路最后即可
                 */
                Long lastFront = privateResourceIds.get(privateResourceIds.size() - 2);
                return chain.subList(chain.indexOf(lastFront), chain.size() - 1);
            }
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }


    public abstract void postProcessBeforeHandle(ValidateContext context, Long resourceId,
            PermissionMask... basicPermission);

    public abstract void postProcessAfterHandle(ValidateContext context, Long resourceId,
            PermissionMask... basicPermission);

    private boolean onlyManager(PermissionMask... masks) {
        return Arrays.stream(masks).anyMatch(PermissionMask::isManagePermission);
    }

    @Override
    public boolean matchResource(Long resourceId, UserClassifiedEnum... userClassifieds) {
        if (classifiedSwitchConfig.isSafetySwitch()) {
            UserClassifiedEnum userClassified;
            if (userClassifieds.length == 0) {
                userClassified = SecurityUtils.getUserClassified();
            } else {
                userClassified = userClassifieds[0];
            }
            Integer classified = getClassified(resourceId);
            boolean matched = userClassified.matchResource(classified);
            if (!matched) {
                log.warn("资源密级不匹配，资源id:{}, 资源类型:{},资源密级:{},用户密级:{}", resourceId, getResourceType(),
                        classified, userClassified);
            }
            return matched;
        }
        // 未开启密级开关，直接返回true
        return true;
    }
}
