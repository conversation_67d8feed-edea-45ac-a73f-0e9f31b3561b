package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_KEYWORD;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordAiKeywordProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractKeywordApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyNlpExtractKeywordAsyncApiImpl implements ThirdPartyNlpExtractKeywordApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getKeyword(MqRecordReqDTO mqRecordReqDTO) {
        log.info("async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordAiKeywordProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_KEYWORD));

//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(GET_KEYWORD));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordAiKeywordProcessEvent<>(mqRecordReqDTO, GET_KEYWORD)));
    }
}
