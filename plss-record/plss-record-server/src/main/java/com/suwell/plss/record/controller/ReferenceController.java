package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.ReferenceAddReq;
import com.suwell.plss.record.standard.dto.request.ReferenceMapReq;
import com.suwell.plss.record.standard.dto.response.MaterialChangeCategoryResp;
import com.suwell.plss.record.standard.dto.response.ReferencesInfoResp;
import com.suwell.plss.record.standard.service.StandardReferenceRecordFacade;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/15
 */
@RestController
@RequestMapping("/v1/reference")
public class ReferenceController {

    @Resource
    private StandardReferenceRecordFacade standardReferenceFacade;

    /**
     * 分页列表
     */
    @PostMapping("/list/{recordId}")
    public R<List<ReferencesInfoResp>> queryList(@PathVariable Long recordId) {
        return R.ok(standardReferenceFacade.queryList(recordId));
    }

    /**
     * 添加参考
     */
    @PostMapping("/add")
    public R<Void> addReference(@RequestBody ReferenceAddReq req) {
        standardReferenceFacade.addReference(req);
        return R.ok();
    }

    /**
     * 删除参考
     */
    @PostMapping("/del")
    public R<MaterialChangeCategoryResp> del(@RequestBody ReferenceMapReq req) {
        standardReferenceFacade.removeReferenceById(req);
        return R.ok();
    }

}
