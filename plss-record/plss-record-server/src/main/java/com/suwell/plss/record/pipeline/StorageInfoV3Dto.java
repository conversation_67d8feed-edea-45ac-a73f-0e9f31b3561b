package com.suwell.plss.record.pipeline;

import com.suwell.plss.record.enums.TaskDocTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/6
 * @content
 */
@Data
public class StorageInfoV3Dto implements Serializable {

    /**
     * 提前生成docId
     */
    private Long docId;
    /**
     * 文件id
     */
    private Long fileId;
    /**
     * 原文件名称
     */
    private String fileName;
    /**
     * 文件类型<br/>
     *
     * @see TaskDocTypeEnum
     */
    private Integer ctype;
    /**
     * 标题
     */
    private String title;
    /**
     * txt文件名称
     */
    private String txtFileName;
    /**
     * txt文件id
     */
    private Long txtFileId;
    /**
     * 摘要
     */
    private String summary;

    /**
     * 缩略图文档id
     */
    private Long pngDocId;
    /**
     * 缩略图文件id
     */
    private Long pngFileId;
    /**
     * 缩略图文件名称
     */
    private String pngFileName;
    /**
     * 附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件<br/>
     *
     * @see com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum
     */
    private Integer attachmentType;

    /**
     * 文件密级：100-秘密  200-机密</br>
     * 字典表：rc_file_classified
     */
    private Integer classified;

    /**
     * 跳过的节点类型
     */
    private Map<Integer, Boolean> skipNodeTypeMap;
}
