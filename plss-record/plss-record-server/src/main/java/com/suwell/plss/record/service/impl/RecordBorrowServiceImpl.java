package com.suwell.plss.record.service.impl;

import static com.suwell.plss.record.standard.enums.RecordBizError.THE_PARAMETER_IS_EMPTY;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.constant.SymbolPool;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.datasource.sharding.DataSourceConfiguration;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.domain.RecordBorrowQueryDto;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordBorrow;
import com.suwell.plss.record.entity.RecordRepo;
import com.suwell.plss.record.entity.ResourceManager;
import com.suwell.plss.record.mapper.RecordBorrowMapper;
import com.suwell.plss.record.service.*;
import com.suwell.plss.record.standard.dto.request.RecordBorrowAddReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowEditReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowPermissionsReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowStatusReq;
import com.suwell.plss.record.standard.dto.request.RecordLogReq;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.enums.RecordBorrowEnum.BorrowStatusEnum;
import com.suwell.plss.record.standard.enums.RecordBorrowEnum.InvalidStatusEnum;
import com.suwell.plss.record.standard.enums.RecordBorrowEnum.RecordBorrowStatusEnum;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import com.suwell.plss.system.api.domain.request.RoleListReq;
import com.suwell.plss.system.api.domain.request.RoleReq;
import com.suwell.plss.system.api.entity.SysMessage;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysRemain;
import com.suwell.plss.system.api.entity.SysRole;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.SysMessageEnum;
import com.suwell.plss.system.api.enums.SysMessageEnum.TypeEnum;
import com.suwell.plss.system.api.enums.SysRemainEnum;
import com.suwell.plss.system.api.enums.SysRemainEnum.CheckStatusEnum;
import com.suwell.plss.system.api.enums.SysRemainEnum.DeleteEnum;
import com.suwell.plss.system.api.enums.SysRemainEnum.StatusEnum;
import com.suwell.plss.system.api.service.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date : 2023/10/26
 */
@Slf4j
@Service
@RefreshScope
public class RecordBorrowServiceImpl extends ServiceImpl<RecordBorrowMapper, RecordBorrow> implements
        RecordBorrowService {


    private static final String BORROWING_APPROVER = "借阅审批人";
    @Resource
    private RecordBorrowMapper borrowMapper;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    private RecordService recordService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private OrgRpcService orgRpcService;
    @Resource
    private LogRecordViewService logRecordViewService;
    @Resource
    private MessageRpcService sysMessageSerivice;
    @Resource
    private RemainRpcService remainRpcService;
    @Resource
    private UserRpcService userRpcService;
    @Resource
    private RoleRpcService roleRpcService;
//    @Value("${record.borrow.borrowPermissionGroupId:0}")
//    private Long borrowPermissionGroupId;

    @Resource
    private StandardRecordFacade recordFacade;

    @Resource
    private ResourceManagerService resourceManagerService;

    @Resource
    private RecordRepoService recordBorrowRepoService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private ConfigRpcService configRpcService;

    @Override
    public Page<RecordBorrowInfoResp> queryPage(RecordBorrowQueryReq request) {
        log.info("borrow query request:{}", request);
        String borrowBy = request.getBorrowBy();
        List<String> roleList = SecurityUtils.getRoleList();
        String userId = SecurityUtils.getUserId();
        String tenantId = SecurityUtils.getTenantId();
        List<String> orgList = SecurityUtils.getOrgList();
        List<String> visitorList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(orgList)) {
            visitorList.addAll(orgList);
        }
        if (CollUtil.isNotEmpty(roleList)) {
            visitorList.addAll(roleList);
        }
        visitorList.add(userId);
        log.info("借阅查询当前登录用户visitorList:{}", visitorList);
        if(CollUtil.isEmpty(roleList) && borrowBy == null){
            return new Page<>(request.getPage(), request.getPageSize());
        }
        //只有文库管理员角色没有借阅审批人角色
        //10008:借阅审批人  10007:文库管理员
        R<List<SysRole>> localRoleListR = roleRpcService.getLocalRoleList(roleList);
        List<Integer> roleTypeList = List.of();
        if(localRoleListR.isSuccess() && CollUtil.isNotEmpty(localRoleListR.getData())){
            roleTypeList = localRoleListR.getData().stream().map(SysRole::getRoleType).toList();
        }
        if(CollUtil.isNotEmpty(roleTypeList) && !roleTypeList.contains(7) && !roleTypeList.contains(8) && borrowBy == null){
            //既没有文库管理员角色也没有借阅审批人角色 并且不是按借阅人查询
            return new Page<>(request.getPage(), request.getPageSize());
        }
        Page<RecordBorrowInfoResp> recordBorrowInfoRespPage = new Page<>(request.getPage(), request.getPage());
        Page<RecordBorrow> page = new Page<>(request.getPage(), request.getPageSize());
        RecordBorrowQueryDto recordBorrowQueryDto = new RecordBorrowQueryDto();
        log.info("借阅查询当前登录用户租户ID:{}", tenantId);
        if (Convert.toLong(tenantId) > 0) {
            recordBorrowQueryDto.setTenantId(SecurityUtils.getTenantId());
        }
        recordBorrowQueryDto.setBorrowBy(request.getBorrowBy());
        recordBorrowQueryDto.setBorrowByName(request.getBorrowByName());
        recordBorrowQueryDto.setRecordName(request.getRecordName());
        recordBorrowQueryDto.setOrgId(request.getOrgId());
        if (Objects.nonNull(request.getStatus())) {
            if (BorrowStatusEnum.APPROVED.getCode().equals(request.getStatus())) {
                recordBorrowQueryDto.setStatusList(Arrays.asList(2,3,4,5));
            } else {
                recordBorrowQueryDto.setStatusList(Collections.singletonList(request.getStatus()));
            }
        }
        recordBorrowQueryDto.setBorrowDateBefore(request.getBorrowDateBefore());
        recordBorrowQueryDto.setBorrowDateAfter(request.getBorrowDateAfter());
        recordBorrowQueryDto.setRecordIds(request.getRecordIds());
        if(borrowAuditByRepoManager()){
            log.info("开启管理员审批开关");
            if(CollUtil.isNotEmpty(roleTypeList) && roleTypeList.contains(7) && !roleTypeList.contains(8)){
                log.info("只有文库管理员角色没有借阅审批人角色");
                //有文库管理员角色没有借阅审批人角色 过滤数据权限 只能看文库管理员管理库的数据
                List<ResourceManager> resources = resourceManagerService.list(new LambdaQueryWrapper<ResourceManager>()
                        .in(ResourceManager::getVisitorId, visitorList)
                        .eq(ResourceManager::getRtype, ResourceType.REPOSITORY.getCode()));
                log.info("文库管理员管理库的数据:{}", JSON.toJSONString(resources));
                if(CollUtil.isEmpty(resources)){
                    return new Page<>(request.getPage(), request.getPageSize());
                }
                List<Long> manageRepoIds = resources.stream().map(ResourceManager::getResourceId).toList();
                recordBorrowQueryDto.setRepoIds(manageRepoIds);
            }
        }
        Page<RecordBorrow> recordBorrowPage = borrowMapper.queryBorrowPage(page,recordBorrowQueryDto);
        recordBorrowInfoRespPage.setTotal(recordBorrowPage.getTotal());
        recordBorrowInfoRespPage.setSize(recordBorrowPage.getSize());
        recordBorrowInfoRespPage.setCurrent(recordBorrowPage.getCurrent());
        if (CollectionUtils.isNotEmpty(recordBorrowPage.getRecords())) {
            List<Long> recordIdList = recordBorrowPage.getRecords().stream().map(RecordBorrow::getRecordId)
                    .collect(Collectors.toList());
            List<RecordInfoResp> recordInfoResps = recordFacade.queryList(recordIdList);
            Map<Long, Integer> recordMap = recordInfoResps.stream()
                    .filter(r -> ObjectUtils.isNotEmpty(r.getClassified()))
                    .collect(Collectors.toMap(RecordInfoResp::getId, RecordInfoResp::getClassified));
            Calendar calendar = new GregorianCalendar();
            recordBorrowInfoRespPage.setRecords(recordBorrowPage.getRecords().stream().map(b -> {
                RecordBorrowInfoResp recordBorrowInfoResp = DozerUtils.convertToNew(b, RecordBorrowInfoResp.class);
                recordBorrowInfoResp.setBorrowName(b.getBorrowByName());
                recordBorrowInfoResp.setAuditName(b.getAuditByName());
//                recordBorrowInfoResp.setOrgName(getOrgNameById(b.getOrgId()));
                recordBorrowInfoResp.setMetaDataValueList(getMeteadataList(b.getRecordId()));
                //浏览次数逻辑
                recordBorrowInfoResp.setBrowseNum(getBorrowNum(b));
                if (BorrowStatusEnum.LOANED.getCode().equals(recordBorrowInfoResp.getStatus())
                        || BorrowStatusEnum.RETURNED.getCode().equals(recordBorrowInfoResp.getStatus())) {
                    if (Objects.nonNull(recordBorrowInfoResp.getWithdrawDate())
                            && recordBorrowInfoResp.getWithdrawDate().getTime() > recordBorrowInfoResp.getBorrowDate()
                            .getTime()) {
                        recordBorrowInfoResp.setExpirationDate(recordBorrowInfoResp.getWithdrawDate());
                    } else {
                        calendar.setTime(recordBorrowInfoResp.getAuditDate());
                        //自动归还时间=借阅时间+借阅天数+1
                        calendar.add(Calendar.DATE, recordBorrowInfoResp.getBorrowDays() + 1);
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        recordBorrowInfoResp.setExpirationDate(calendar.getTime());
                    }
                } else {
                    recordBorrowInfoResp.setExpirationDate(null);
                }
                if (recordMap.containsKey(b.getRecordId())) {
                    recordBorrowInfoResp.setClassified(recordMap.get(b.getRecordId()));
                }
                return recordBorrowInfoResp;
            }).collect(Collectors.toList()));
        }
        return recordBorrowInfoRespPage;
    }

    /**
     * 是否可以由文库管理员审批
     */
    private boolean borrowAuditByRepoManager() {
        //判断文库管理员审批开关
        R<JSONObject> projectConfigR = configRpcService.getProjectConfig();
        if(projectConfigR.isError()){
            log.error("获取项目配置信息失败:{}",projectConfigR.getMsg());
            throw new BizException("查询借阅文档失败","获取项目配置信息失败" + projectConfigR.getMsg());
        }
        JSONObject projectConfig = projectConfigR.getData();
        if(projectConfig == null){
            return false;
        }
        Boolean repoManagerBorrowAudit = projectConfig.getBoolean("repoManagerBorrowAudit");
        return repoManagerBorrowAudit != null && repoManagerBorrowAudit;
    }

    @Override
    @DSTransactional
    public void addBorrowInfo(RecordBorrowAddReq request) {
        log.info("borrow add request:{}", request);
        validationParameters(request);
        long borrowId = wfgIdGenerator.next();
        //借阅信息封装
        List<RecordBorrow> list = request.getBorrows().stream().map(r -> {
            RecordBorrow recordBorrow = new RecordBorrow();
            recordBorrow.setRecordId(r.getRecordId());
            recordBorrow.setRecordName(r.getRecordName());
            recordBorrow.setBorrowId(borrowId);
            recordBorrow.setRecordTenantId(r.getTenantId());
            recordBorrow.setBorrowBy(request.getBorrowBy());
            recordBorrow.setBorrowByName(request.getBorrowByName());
            recordBorrow.setBorrowDate(request.getBorrowDate());
            recordBorrow.setOrgId(request.getOrgId());
            recordBorrow.setOrgName(request.getOrgName());
            recordBorrow.setBorrowDays(request.getBorrowDays());
            recordBorrow.setBorrowPurpose(request.getBorrowPurpose());
            recordBorrow.setBorrowFor(request.getBorrowFor());
            recordBorrow.setUsage(request.getUsage());
            recordBorrow.setStatus(BorrowStatusEnum.PENDING_APPROVAL.getCode());
            recordBorrow.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            return recordBorrow;
        }).collect(Collectors.toList());
        //借阅信息入库
        this.saveBatch(list);
        //保存借阅文档所在的库信息
        saveBorrowRepoByRecordIds(list.stream().map(RecordBorrow::getRecordId).collect(Collectors.toList()));
        //给审批人发送消息
        List<SysRemain> remains = Lists.newArrayList();
        list.forEach(r -> {
            List<SysUser> users = getRoleUserByTenantId(r.getRecordTenantId());
//            if (Objects.nonNull(users)) {
//                users.forEach(user -> {
//                    SysMessage message = new SysMessage();
//                    message.setType(TypeEnum.BORROWING_APPROVAL.getCode());
//                    message.setTitle("借阅申请");
//                    message.setContent(
//                            r.getBorrowByName() + ",申请《" + r.getRecordName() + "》借阅文档，借阅" + r.getBorrowDays()
//                                    + "天");
//                    message.setAcceptPersonId(user.getUserId());
//                    message.setAcceptPersonName(user.getNickName());
//                    message.setSendPersonId(SecurityUtils.getUserId());
//                    message.setRecordId(r.getRecordId());
//                    messages.add(message);
//                });
//            }
            if (CollUtil.isNotEmpty(users)) {
                SysRemain remain = new SysRemain();
                remain.setId(wfgIdGenerator.next());
                remain.setType(SysRemainEnum.TypeEnum.MESSAGE_IN_BORROW_CHECK.getCode());
                remain.setTitle(r.getRecordName());
                remain.setContent(
                        r.getBorrowByName() + ",申请《" + r.getRecordName() + "》借阅文档，借阅" + r.getBorrowDays()
                                + "天");
                remain.setAcceptPersonId(
                        users.stream().map(u -> SymbolPool.AT+u.getUserId()+SymbolPool.AT).collect(Collectors.joining(",")));
                remain.setSendPersonId(SecurityUtils.getUserId());
                remain.setSendTime(new Date());
                remain.setBusinessId(r.getId());
                remain.setStatus(StatusEnum.REMAIN_UNDONE.getCode());
                remains.add(remain);
            }
        });
        remainRpcService.batchSaveRemain(remains);
    }

    @Override
    @DSTransactional
    public void saveBorrowRepoByRecordIds(List<Long> list) {
        if(CollUtil.isEmpty(list)){
            return;
        }
        long start = System.currentTimeMillis();
        //删除之前的借阅关联库信息
        recordBorrowRepoService.remove(Wrappers.<RecordRepo>lambdaQuery().in(RecordRepo::getRecordId, list));
        for (Long recordId : list) {
            List<RepoInfoResp> respList = repositoryService.listRepoInfo(recordId);
            if (CollUtil.isNotEmpty(respList)) {
                List<RecordRepo> recordBorrowRepos = respList.stream().map(r -> {
                    RecordRepo recordBorrowRepo = new RecordRepo();
                    recordBorrowRepo.setRecordId(recordId);
                    recordBorrowRepo.setRepoId(r.getId());
                    recordBorrowRepo.setCreateTime(new Date());
                    return recordBorrowRepo;
                }).collect(Collectors.toList());
                recordBorrowRepoService.saveBatch(recordBorrowRepos);
            }
        }
        log.info("saveBorrowRepo cost time:{}", System.currentTimeMillis() - start);
    }

    private void validationParameters(RecordBorrowAddReq request) {
//        if (request.getBorrowDate().getTime() > new Date().getTime()) {
//            throw new BizException(RecordBizError.BORROW_DATE_ERROR);
//        }

        if (!request.getUsage().contains("1")) {
            throw new BizException(RecordBizError.BORROW_USAGES_ERROR);
        }
        if (request.getBorrowDays() <= 0) {
            throw new BizException(RecordBizError.BORROW_DAYS_ERROR);
        }
        if (request.getBorrowDays() > 999) {
            request.setBorrowDays(999);
        }
        R<SysUser> user = userRpcService.queryById(request.getBorrowBy());
        if (!user.isSuccess() || Objects.isNull(user.getData())) {
            throw new BizException(RecordBizError.BORROWER_DOES_NOT_EXIST);
        }

        List<Record> records = recordService.getListByIds(
                request.getBorrows().stream().map(RecordBorrowReq::getRecordId).collect(Collectors.toList()));

        request.getBorrows().forEach(r -> {
            if (Objects.isNull(r.getRecordId())) {
                throw new BizException(RecordBizError.BORROW_RECORD_ID_IS_EMPTY);
            }
            Record res = records.stream().filter(record -> record.getId().equals(r.getRecordId())).findFirst()
                    .orElseThrow(() -> new BizException(RecordBizError.BORROW_RECORD_NOT_EXIST));

            r.setRecordName(res.getTitle());
            r.setTenantId(res.getTenantId());
        });

    }

    private void validationAuditParameters(RecordBorrowEditReq request) {
//        if (request.getAuditDate().getTime() > new Date().getTime()) {
//            throw new BizException(RecordBizError.BORROW_DATE_ERROR);
//        }
        if (Objects.nonNull(request.getUsage())) {
            if (!request.getUsage().contains("1")) {
                throw new BizException(RecordBizError.BORROW_USAGES_ERROR);
            }
        }
        R<SysUser> sysUserR = userRpcService.queryById(request.getAuditBy());
        if (!sysUserR.isSuccess() || Objects.isNull(sysUserR.getData())) {
            throw new BizException(RecordBizError.REVIEWER_DOES_NOT_EXIST);
        }

        List<Record> records = recordService.getListByIds(
                request.getBorrows().stream().map(RecordBorrowReq::getRecordId).collect(Collectors.toList()));
        request.getBorrows().forEach(r -> {
            if (Objects.isNull(r.getRecordId())) {
                throw new BizException(RecordBizError.BORROW_RECORD_ID_IS_EMPTY);
            }
            Record res = records.stream().filter(record -> record.getId().equals(r.getRecordId())).findFirst()
                    .orElseThrow(() -> new BizException(RecordBizError.BORROW_RECORD_NOT_EXIST));
            r.setRecordName(res.getTitle());
            r.setTenantId(res.getTenantId());
        });

    }

    private List<SysUser> getRoleUserByTenantId(String tenantId) {
        RoleListReq req = new RoleListReq();
        req.setRoleName(BORROWING_APPROVER);
        R<List<SysRole>> roleAll = roleRpcService.getRoleAll(req);
        if (!roleAll.isSuccess() || Objects.isNull(roleAll.getData())) {
            return Lists.newArrayList();
        }
        RoleReq roleReq = new RoleReq();
        roleReq.setRoleIdList(roleAll.getData().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        roleReq.setTenantId(tenantId);
        R<List<String>> userIds = roleRpcService.queryUserByRoleId(roleReq);
        if (!userIds.isSuccess() && Objects.isNull(userIds.getData())) {
            return Lists.newArrayList();
        }
        R<List<SysUser>> info = userRpcService.getBatchInfo(userIds.getData());
        if (!info.isSuccess() && Objects.isNull(info.getData())) {
            return Lists.newArrayList();
        }
        return info.getData();
    }

    @Override
    public void updateBorrowInfo(RecordBorrowAddReq request) {
        log.info("borrow update request:{}", request);
        validationParameters(request);
        //借阅信息封装
        List<RecordBorrow> list = request.getBorrows().stream().map(r -> {
            RecordBorrow recordBorrow = new RecordBorrow();
            recordBorrow.setRecordId(r.getRecordId());
            recordBorrow.setRecordName(r.getRecordName());
            recordBorrow.setId(request.getId());
            recordBorrow.setBorrowId(request.getBorrowId());
            recordBorrow.setRecordTenantId(r.getTenantId());
            recordBorrow.setBorrowBy(request.getBorrowBy());
            recordBorrow.setBorrowByName(request.getBorrowByName());
            recordBorrow.setBorrowDate(request.getBorrowDate());
            recordBorrow.setOrgId(request.getOrgId());
            recordBorrow.setOrgName(request.getOrgName());
            recordBorrow.setBorrowDays(request.getBorrowDays());
            recordBorrow.setBorrowPurpose(request.getBorrowPurpose());
            recordBorrow.setBorrowFor(request.getBorrowFor());
            recordBorrow.setUsage(request.getUsage());
            recordBorrow.setStatus(BorrowStatusEnum.PENDING_APPROVAL.getCode());
            recordBorrow.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            return recordBorrow;
        }).collect(Collectors.toList());

        boolean b = this.updateBatchById(list);
    }

    @Override
    public RecordBorrowInfoResp auditInfo(Long id) {

        RecordBorrow b = this.getById(id);

        Calendar calendar = new GregorianCalendar();
        RecordBorrowInfoResp recordBorrowInfoResp = DozerUtils.convertToNew(b, RecordBorrowInfoResp.class);
        recordBorrowInfoResp.setBorrowName(b.getBorrowByName());
        recordBorrowInfoResp.setAuditName(b.getAuditByName());
//                recordBorrowInfoResp.setOrgName(getOrgNameById(b.getOrgId()));
        recordBorrowInfoResp.setMetaDataValueList(getMeteadataList(b.getRecordId()));
        //浏览次数逻辑
        recordBorrowInfoResp.setBrowseNum(getBorrowNum(b));
        if (BorrowStatusEnum.LOANED.getCode().equals(recordBorrowInfoResp.getStatus())
                || BorrowStatusEnum.RETURNED.getCode().equals(recordBorrowInfoResp.getStatus())) {
            if (Objects.nonNull(recordBorrowInfoResp.getWithdrawDate())
                    && recordBorrowInfoResp.getWithdrawDate().getTime() > recordBorrowInfoResp.getBorrowDate()
                    .getTime()) {
                recordBorrowInfoResp.setExpirationDate(recordBorrowInfoResp.getWithdrawDate());
            } else {
                calendar.setTime(recordBorrowInfoResp.getAuditDate());
                //自动归还时间=借阅时间+借阅天数+1
                calendar.add(Calendar.DATE, recordBorrowInfoResp.getBorrowDays() + 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                recordBorrowInfoResp.setExpirationDate(calendar.getTime());
            }
        } else {
            recordBorrowInfoResp.setExpirationDate(null);
        }
        return recordBorrowInfoResp;
    }

    @Override
    @DSTransactional
    public void audit(RecordBorrowEditReq request) {
        log.info("borrow audit request:{}", request);
        validationAuditParameters(request);

        List<RecordBorrow> list = request.getBorrows().stream().map(b -> {
            RecordBorrow recordBorrow = new RecordBorrow();
            recordBorrow.setId(b.getId());
            recordBorrow.setAuditBy(request.getAuditBy());
            recordBorrow.setAuditByName(request.getAuditByName());
            recordBorrow.setRecordId(b.getRecordId());
            recordBorrow.setRecordName(b.getRecordName());
            recordBorrow.setBorrowBy(b.getBorrowBy());
            recordBorrow.setAuditDate(request.getAuditDate());
            recordBorrow.setStatus(request.getStatus());
            recordBorrow.setRejectReason(StringUtils.isNotEmpty(request.getRejectReason()) ? request.getRejectReason() : "");
            recordBorrow.setBorrowDays(ObjectUtils.isNotEmpty(request.getBorrowDays()) && request.getBorrowDays() > 0
                    ? request.getBorrowDays() : null);
            if (Objects.nonNull(request.getUsage())) {
                recordBorrow.setUsage(request.getUsage());
            }
            return recordBorrow;
        }).collect(Collectors.toList());
        List<Long> recordBorrowIdList = list.stream().map(RecordBorrow::getId).collect(Collectors.toList());
        String remark = null;
        RecordBorrow rb = this.getById(recordBorrowIdList.get(0));
        if(list.size()<=1 && ObjectUtils.isNotEmpty(request.getBorrowDays())){
            remark = rb.getBorrowByName() + ",申请《" + rb.getRecordName() + "》借阅文档，借阅" + request.getBorrowDays()
                    + "天";
        }

        remainRpcService.updateStatus(recordBorrowIdList,
                request.getStatus().equals(BorrowStatusEnum.LOANED.getCode()) ?
                        CheckStatusEnum.PASS.getCode() : CheckStatusEnum.REJECT.getCode(),
                StringUtils.isNotEmpty(request.getRejectReason()) ? request.getRejectReason() : "");

        //给借阅者发送消息
        sysMessageSerivice.batchSaveMessage(list.stream().map(r -> {
            SysMessage message = new SysMessage();
            TypeEnum typeEnum = r.getStatus().equals(BorrowStatusEnum.LOANED.getCode()) ? TypeEnum.RECORD_BORROW_PASS
                    : TypeEnum.RECORD_BORROW_REJECT;
            message.setType(typeEnum.getCode());
            message.setTitle(String.format(typeEnum.getDec(), r.getRecordName()));
            String content = String.format(typeEnum.getContent(), r.getRecordName(), r.getRejectReason());
            if(list.size()<=1 && ObjectUtils.isNotEmpty(request.getBorrowDays()) && !request.getBorrowDays().equals(rb.getBorrowDays())){
                content  = content + "，借阅权限为";
            }
            message.setContent(content);
            message.setAcceptPersonId(r.getBorrowBy());
            message.setAcceptPersonName(r.getBorrowByName());
            message.setSendPersonId(SecurityUtils.getUserId());
            message.setRecordId(r.getRecordId());
            return message;
        }).collect(Collectors.toList()));

        //更新表
        this.updateBatchById(list);
    }

    @Override
    @DSTransactional
    public void borrowReturn() {
        log.info("borrow return start");

        List<RecordBorrow> list = this.list(
                Wrappers.<RecordBorrow>lambdaQuery().eq(RecordBorrow::getStatus, BorrowStatusEnum.LOANED.getCode()));
        if (list.isEmpty()) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<RecordBorrow> returnList = list.stream()
                .filter(borrow -> now.minusDays(borrow.getBorrowDays())
                        .isAfter(LocalDateTime.ofInstant(borrow.getAuditDate().toInstant(), ZoneId.systemDefault())))
                .map(borrow -> {
                    borrow.setStatus(BorrowStatusEnum.RETURNED.getCode());
                    return borrow;
                })
                .collect(Collectors.toList());

        log.info("borrow return records:{}", returnList);

        //更新表
        this.updateBatchById(returnList);

        //给借阅者发送消息
        sysMessageSerivice.batchSaveMessage(returnList.stream().map(r -> {
            SysMessage message = new SysMessage();
            message.setType(SysMessageEnum.TypeEnum.RECORD_BORROW_EXPIRE.getCode());
            message.setTitle(String.format(SysMessageEnum.TypeEnum.RECORD_BORROW_EXPIRE.getDec(), r.getRecordName()));
            message.setContent(
                    String.format(SysMessageEnum.TypeEnum.RECORD_BORROW_EXPIRE.getContent(), r.getRecordName()));
            message.setAcceptPersonId(r.getBorrowBy());
            message.setAcceptPersonName(r.getBorrowByName());
            message.setSendPersonId(SecurityUtils.getUserId());
            message.setRecordId(r.getRecordId());
            return message;
        }).collect(Collectors.toList()));

    }

    @Override
    @DSTransactional
    public void withdraw(List<Long> ids) {
        log.info("borrow return start");

        List<RecordBorrow> list = this.list(
                Wrappers.<RecordBorrow>lambdaQuery().in(RecordBorrow::getId, ids)
                        .eq(RecordBorrow::getStatus, BorrowStatusEnum.LOANED.getCode()));
        if (list.isEmpty()) {
            throw new BizException(RecordBizError.BORROW_NOT_EXIST);
        }
        List<RecordBorrow> returnList = list.stream().map(borrow -> {
            borrow.setStatus(BorrowStatusEnum.RETURNED.getCode());
            borrow.setWithdrawDate(new Date());
            return borrow;
        }).collect(Collectors.toList());

        log.info("borrow return records:{}", returnList);

        //更新表
        this.updateBatchById(returnList);
        //给借阅者发送消息
        sysMessageSerivice.batchSaveMessage(list.stream().map(r -> {
            SysMessage message = new SysMessage();
            message.setType(TypeEnum.RECORD_BORROW_RECOVER.getCode());
            message.setTitle(String.format(TypeEnum.RECORD_BORROW_RECOVER.getDec(), r.getRecordName()));
            message.setContent(String.format(TypeEnum.RECORD_BORROW_RECOVER.getContent(), r.getRecordName()));
            message.setAcceptPersonId(r.getBorrowBy());
            message.setAcceptPersonName(r.getBorrowByName());
            message.setSendPersonId(SecurityUtils.getUserId());
            message.setRecordId(r.getRecordId());
            return message;
        }).collect(Collectors.toList()));
    }

    @Override
    public List<RecordBorrowStatusResp> status(RecordBorrowStatusReq request) {
        log.info("borrow status request:{}", request);

        List<RecordBorrow> list = this.list(
                Wrappers.<RecordBorrow>lambdaQuery().eq(RecordBorrow::getBorrowBy, request.getBorrowBy())
                        .eq(RecordBorrow::getOrgId, request.getOrgId())
                        .in(RecordBorrow::getRecordId, request.getRecordIds()));

        return request.getRecordIds().stream().map(recordId -> {
            RecordBorrowStatusResp recordBorrowStatusResp = new RecordBorrowStatusResp();
            recordBorrowStatusResp.setRecordId(recordId);
            recordBorrowStatusResp.setStatus(RecordBorrowStatusEnum.NOT_LENT_OUT.getCode());
            if (CollectionUtils.isNotEmpty(list)) {
                for (RecordBorrow recordBorrow : list) {
                    if (recordId.equals(recordBorrow.getRecordId())) {
                        if (recordBorrow.getStatus().equals(BorrowStatusEnum.LOANED.getCode())) {
                            recordBorrowStatusResp.setStatus(RecordBorrowStatusEnum.LOANED.getCode());
                        }
                        if (recordBorrow.getStatus().equals(BorrowStatusEnum.PENDING_APPROVAL.getCode())) {
                            recordBorrowStatusResp.setStatus(RecordBorrowStatusEnum.LOANED_NO_AUDIT.getCode());
                        }
                    }
                }
            }
            return recordBorrowStatusResp;
        }).collect(Collectors.toList());

    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public List<Integer> borrowPermissions(RecordBorrowPermissionsReq req) {
        BeanValidators.defaultValidate(req);
        log.info("borrowPermissions req:{}", req);

        List<Integer> list = Lists.newArrayList();
        RecordBorrow borrow = lambdaQuery()
                .eq(RecordBorrow::getStatus, BorrowStatusEnum.LOANED.getCode())
                .eq(RecordBorrow::getRecordId, req.getRecordId())
                .in(RecordBorrow::getBorrowBy, req.getUserIds()).one();
        if (Objects.isNull(borrow)) {
            return list;
        }
        for (String s : borrow.getUsage().split(";")) {
            list.add(Integer.parseInt(s));
        }
        return list;
    }

    @Override
    public Map<Long, List<Integer>> batchBorrowPermissions(List<RecordBorrowPermissionsReq> reqs) {
        reqs.forEach(BeanValidators::defaultValidate);
        log.info("batchBorrowPermissions reqs:{}", reqs);
        List<Long> recordId = reqs.stream().map(RecordBorrowPermissionsReq::getRecordId).toList();
        List<String> userIds = reqs.stream().map(RecordBorrowPermissionsReq::getUserIds).flatMap(Collection::stream)
                .toList();

        List<RecordBorrow> list = lambdaQuery().eq(RecordBorrow::getStatus, BorrowStatusEnum.LOANED.getCode())
                .in(RecordBorrow::getRecordId, recordId)
                .in(RecordBorrow::getBorrowBy, userIds)
                .list();
        return list.stream().collect(Collectors.toMap(RecordBorrow::getRecordId,
                b -> {
                    List<Integer> list1 = Lists.newArrayList();
                    for (String s : b.getUsage().split(";")) {
                        list1.add(Integer.parseInt(s));
                    }
                    return list1;
                }));
    }

    @Override
    public Set<Long> getRecordId(List<Long> borrowBys) {
        AssertUtils.isTrue(CollectionUtils.isNotEmpty(borrowBys), THE_PARAMETER_IS_EMPTY);

        log.info("getRecordId borrowBys:{}", borrowBys);

        List<RecordBorrow> list = this.lambdaQuery()
                .eq(RecordBorrow::getStatus, BorrowStatusEnum.LOANED.getCode())
                .in(RecordBorrow::getBorrowBy, borrowBys)
                .list();

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }

        return list.stream().map(RecordBorrow::getRecordId).collect(Collectors.toSet());
    }

    @Override
    public Boolean borrowAgainToJudge(RecordBorrowStatusReq request) {
        log.info("borrowAgainToJudge request:{}", request);

        Boolean result = true;
        List<RecordBorrow> list = this.list(
                Wrappers.<RecordBorrow>lambdaQuery().eq(RecordBorrow::getBorrowBy, request.getBorrowBy())
                        .eq(RecordBorrow::getOrgId, request.getOrgId())
                        .eq(RecordBorrow::getDelFlag, DeleteEnum.NO_DELETE.getCode())
                        .in(RecordBorrow::getRecordId, request.getRecordIds()));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        for (RecordBorrow recordBorrow : list) {
            if (recordBorrow.getStatus().equals(BorrowStatusEnum.LOANED.getCode()) || recordBorrow.getStatus()
                    .equals(BorrowStatusEnum.PENDING_APPROVAL.getCode())) {
                result = false;
            }
        }
        return result;
    }

    @Override
    public void updateInvalidStatus(List<Long> recordIds) {
        this.update(Wrappers.<RecordBorrow>lambdaUpdate()
                .set(RecordBorrow::getInvalidStatus, InvalidStatusEnum.INVALID.getCode())
                .in(RecordBorrow::getRecordId, recordIds));
    }


    private Long getBorrowNum(RecordBorrow b) {
        if (b.getStatus().equals(BorrowStatusEnum.LOANED.getCode())) {
            RecordLogReq recordLogReq = new RecordLogReq();
            recordLogReq.setRecordId(b.getRecordId());
            recordLogReq.setUserId(b.getBorrowBy());
            recordLogReq.setStm(b.getAuditDate());
            recordLogReq.setEtm(new Date());
            return logRecordViewService.queryRecordViewLogCount(recordLogReq);
        }
        return 0L;
    }

    private Map<Long, String> getMeteadataList(Long recordId) {
        Record record = recordService.getOneById(recordId);
        if (Objects.nonNull(record)) {
            List<MetaDataValueInfoResp> metaDataValueInfoRespList = recordMetadataValueService.queryRecordTypeMetaDataValue(
                    recordId, record.getRecordtypeId());
            List<MetaDataValueInfoResp> metaDataValueInfoRespNewList = Lists.newArrayList();
            if (CollUtil.isNotEmpty(metaDataValueInfoRespList)) {
                // 将目录的元数据置空
                for (MetaDataValueInfoResp metaDataValueInfoResp : metaDataValueInfoRespList) {
                    if (plssFixedMetadataNameConfig.getFixedRepoPositionName().equals(metaDataValueInfoResp.getMdName())) {
                        metaDataValueInfoResp.setMdValue(StringPool.EMPTY);
                    }
                    metaDataValueInfoRespNewList.add(metaDataValueInfoResp);
                }
            }
            Map<Long, String> map = new HashMap<>();
            for (MetaDataValueInfoResp metaDataValueInfoResp : metaDataValueInfoRespNewList) {
                map.put(metaDataValueInfoResp.getMdId(),
                        StringUtils.isNotEmpty(metaDataValueInfoResp.getMdValue()) ? metaDataValueInfoResp.getMdValue()
                                : StringPool.EMPTY);
            }
            return map;
        }
        return new HashMap<>();
    }

    private String getUserNameById(String id) {
        R<SysUser> sysUserR = userRpcService.queryById(id);
        if (sysUserR.isSuccess() && sysUserR.getData() != null) {
            return sysUserR.getData().getNickName();
        }
        return StringPool.EMPTY;
    }

    private String getOrgNameById(String id) {
        R<SysOrg> sysOrgR = orgRpcService.queryById(id);
        if (sysOrgR.isSuccess() && sysOrgR.getData() != null) {
            return sysOrgR.getData().getOrgName();
        }
        return StringPool.EMPTY;
    }

    @Override
    public void revoke(Long id) {
        this.lambdaUpdate().set(RecordBorrow::getDelFlag, DeleteEnum.DELETE.getCode())
                .eq(RecordBorrow::getId, id).update();
        remainRpcService.revoke(id);
    }

    @Override
    public List<RecordBorrowInfoResp> getByRecordIds(List<Long> recordIds) {
        List<RecordBorrow> list = this.lambdaQuery().in(RecordBorrow::getRecordId, recordIds).list();
        return DozerUtils.convertListToNew(list,
                RecordBorrowInfoResp.class);
    }
}