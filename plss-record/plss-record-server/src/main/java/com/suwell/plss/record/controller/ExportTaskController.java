package com.suwell.plss.record.controller;

import com.suwell.plss.document.process.dto.request.ExportRecordReq;
import com.suwell.plss.record.migration.DataEmigrateService;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import jakarta.annotation.Resource;

import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.domain.ExportTaskDTO;
import com.suwell.plss.record.service.ExportTaskService;
import com.suwell.plss.record.standard.dto.request.ExportFilterReq;
import com.suwell.plss.record.standard.dto.request.ExportTaskQueryReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据导出任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Slf4j
@RestController
@RequestMapping("/v1/exportTask")
public class ExportTaskController {

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private DataEmigrateService dataEmigrateService;

    @Resource
    private AsyncTaskExecutor asyncTaskExecutor;

    @Resource
    private Redisson redisson;

    @PostMapping(value = "/page")
    public R<PageUtils<ExportTaskDTO>> page(@RequestBody ExportTaskQueryReq queryReq) {

        PageUtils<ExportTaskDTO> page = exportTaskService.pageExportTask(queryReq);
        return R.ok(page);
    }

    @PostMapping("/info/{id}")
    public R<ExportTaskDTO> queryById(@PathVariable Long id) {

        ExportTaskDTO exportTaskDto = exportTaskService.queryById(id);
        return R.ok(exportTaskDto);
    }

    @PostMapping("/add")
    public R<Object> addExportTask(@RequestBody ExportTaskQueryReq queryReq) {
        long id = exportTaskService.addExportTask(queryReq);
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        return R.ok(map);
    }

    @PostMapping(value = "/delete/{id}")
    public R<Void> delete(@PathVariable("id") String id) {
        exportTaskService.removeById(id);
        return R.ok();
    }

    @PostMapping(value = "/update")
    public R<Void> update(@RequestBody ExportTaskDTO exportTaskDto) {
        exportTaskService.updateExportTask(exportTaskDto);
        return R.ok();
    }

    @PostMapping("/estimatedb")
    public R<Object> estimatedb(@RequestBody ExportFilterReq queryReq) {
        return R.ok(this.dataEmigrateService.estimateFilter(queryReq));
    }

    @PostMapping("/estimate")
    public R<Object> estimate(@RequestBody ExportFilterReq queryReq) {
        return R.ok(this.dataEmigrateService.estimateFilter(queryReq));
    }
//    @PostMapping("/estimateTask/{id}")
//    public R<Object> estimateTask(@PathVariable("id") long id) {
//        return R.ok(this.dataEmigrateService.estimateExport(PATH_PREFIX, id));
//    }

    @PostMapping("/exportFilter")
    public R<Object> exportFilter(@RequestBody ExportFilterReq queryReq) {
        return R.ok(this.dataEmigrateService.exportFilter(queryReq));
    }
    @PostMapping("/export/{id}")
    public R<ExportTaskDTO> export(@PathVariable Long id) {

        RLock redissonLock = redisson.getLock("EXPORT_TASK_DATA_LOCK");
        try {
            // 加锁失败(加锁时间45m)
            if (!redissonLock.tryLock(30L, 60L, TimeUnit.MINUTES)) {
                return R.error("running...");
            }
            // 业务逻辑
            asyncTaskExecutor.submit(() -> {
                this.dataEmigrateService.exportMultiTask(id);
            });

        } catch (Exception e) {
            log.error("export lock error: {}", e);
        } finally {
            redissonLock.unlock();
        }

        return R.ok();
    }

    @PostMapping("/exportdb/{id}")
    public R<ExportTaskDTO> exportdb(@PathVariable Long id) {

        RLock redissonLock = redisson.getLock("EXPORT_TASK_DATA_LOCK");
        try {
            // 加锁失败(加锁时间45m)
            if (!redissonLock.tryLock(30L, 60L, TimeUnit.MINUTES)) {
                return R.error("running...");
            }
            // 业务逻辑
            asyncTaskExecutor.submit(() -> {
                dataEmigrateService.exportMultiTask(id);
            });

        } catch (Exception e) {
            log.error("export lock error: {}", e);
        } finally {
            redissonLock.unlock();
        }

        return R.ok();
    }

    @PostMapping("/exportByRecordId")
    public R<Void> exportByRecordId(@RequestBody ExportRecordReq req) {
        dataEmigrateService.exportByRecordId(req);
        return R.ok();
    }
}
