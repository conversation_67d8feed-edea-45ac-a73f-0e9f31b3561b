package com.suwell.plss.record.pipeline.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_METADATA;
import static com.suwell.plss.framework.mq.enums.EventType.GET_METADATA_FROM_MODEL;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordFileTypeEnum.RECORD_FILE_TYPE_DOUBLE_OFD;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.suwell.plss.framework.common.enums.CommonData;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.mq.dto.base.MqDocProcessBaseDTO;
import com.suwell.plss.framework.mq.dto.base.MqExtendParamDTO;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.enums.EventType;
import com.suwell.plss.framework.mq.events.NodeMetadataFillProcessEvent;
import com.suwell.plss.framework.mq.events.RecordAiMetadataProcessEvent;
import com.suwell.plss.framework.mq.events.RecordReFlowProcessEvent;
import com.suwell.plss.record.entity.DocProcess;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.Plan;
import com.suwell.plss.record.entity.RecordProcessDetail;
import com.suwell.plss.record.entity.RecordType;
import com.suwell.plss.record.entity.TaskDoc;
import com.suwell.plss.record.enums.DocProcessStatusEnums;
import com.suwell.plss.record.enums.StoreWayEnum;
import com.suwell.plss.record.enums.TaskDocTypeEnum;
import com.suwell.plss.record.pipeline.StorageInfoDto;
import com.suwell.plss.record.service.RecordTypeMetadataService;
import com.suwell.plss.record.service.RecordTypeService;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO;
import com.suwell.plss.record.standard.domain.MqNodeProcessDto;
import com.suwell.plss.record.standard.dto.request.TaskDocStatusReq;
import com.suwell.plss.record.standard.dto.request.TaskNodeReq;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.enums.TaskDocStatusEnum;
import com.suwell.plss.record.thirdparty.ThirdPartyOcrTextApi;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/7
 * @content
 */
@Slf4j
@Component
public class MetadataFillNodeHandler extends AbstractNodeHandler {

    @Resource
    private ThirdPartyOcrTextApi thirdPartyOcrTextApi;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private RecordTypeService recordTypeService;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.NT_METADATA_FILL;
    }

    @Override
    public void sendMq(MqNodeProcessDto dto) {
        context.publishEvent(new NodeMetadataFillProcessEvent<>(dto, dto.getPriorityValue()));
    }

    @Override
    public boolean needBreak(TaskNodeReq req) {
        final TaskDoc taskDoc = getTaskDoc(req.getTaskDocId(),req.getRecordId());
        return TaskDocTypeEnum.TDT_ATTACHMENT.getCode() == taskDoc.getCtype();
    }

    @Override
    public MqDocProcessBaseDTO prePending(Long taskDocId, Long recordId) {
        Date nowDate = new Date();
        docProcessService.updateByWrapper(List.of(recordId), new LambdaUpdateWrapper<DocProcess>()
                .set(DocProcess::getProcessStatus,
                        DocProcessStatusEnums.ProcessStatusEnum.PS_SYSTEM_IN_PROCESS.getCode())
                .set(DocProcess::getProcessSubStatus,
                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_IN_PROGRESS.getCode())
                .set(DocProcess::getModifiedTime, nowDate)
                .eq(DocProcess::getTaskDocId, taskDocId));

        return new MqDocProcessBaseDTO(
                recordId,
                DocProcessStatusEnums.ProcessStatusEnum.PS_SYSTEM_IN_PROCESS.getCode(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_IN_PROGRESS.getCode(),
                nowDate.getTime()
        );
    }

    @Override
    @DSTransactional
    public Integer nodeExecuteLogic(TaskNodeReq req) {

        MqDocProcessBaseDTO mqDto = prePending(req.getTaskDocId(), req.getRecordId());

        // 主文件ocr，附件不处理
        final TaskDoc taskDoc = getTaskDoc(req.getTaskDocId(),req.getRecordId());
        final String storageInfo = taskDoc.getStorageInfo();
        final StorageInfoDto storageInfoDto = JSON.parseObject(storageInfo, StorageInfoDto.class);

        final String fileName = storageInfoDto.getFileName();
        String suffix = FileNameUtil.getSuffix(fileName);

        // 方案事件
        final Set<EventType> planEventSet = getEventTypes(req.getCurrentNodeId(), req.getPlanId());
        final EventType eventType = eventProcess(planEventSet, taskDoc.getRecordId(), fileName, req.getUserId(),
                req.getNickName());

        TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            docProcessESService.updateEsDocProcessBase(mqDto);

            final Document document = documentService.getMasterDocByRecordId(taskDoc.getRecordId());

            MqRecordReqDTO mqRecordReqDTO = new MqRecordReqDTO();
            mqRecordReqDTO.setRecordId(taskDoc.getRecordId());
            mqRecordReqDTO.setNodeId(req.getCurrentNodeId());
            mqRecordReqDTO.setDocId(taskDoc.getDocId());

            if (OFD.equalsIgnoreCase(suffix) || document.getOfdFileId() != null) {
                // 源文件是ofd文件 或者 已经转换为ofd文件
                mqRecordReqDTO.setSuffix(StringPool.DOT + OFD);
                mqRecordReqDTO.setRecordFileType(RECORD_FILE_TYPE_DOUBLE_OFD.getCode());
            } else {
                mqRecordReqDTO.setSuffix(StringPool.DOT + suffix);
            }

            if (document.getOfdFileId() != null) {
                mqRecordReqDTO.setFileId(document.getOfdFileId());
            } else {
                mqRecordReqDTO.setFileId(document.getFileId());
            }

            MqExtendParamDTO extendParam = new MqExtendParamDTO();
            extendParam.setOrigin(req.getOrigin());
            extendParam.setStoreWay(req.getStoreWay());
            mqRecordReqDTO.setExtendParam(extendParam);

            if (eventType == GET_METADATA_FROM_MODEL) {
                extendParam.setTxtFileId(document.getTxtFileId());
                final Plan plan = getPlan(req.getPlanId());
                final Long recordTypeId = plan.getRecordTypeId();

                final RecordType one = recordTypeService.lambdaQuery().select(RecordType::getName)
                        .eq(RecordType::getId, recordTypeId).one();
                extendParam.setRecordType(one.getName());
                // 取元数据规则的定义
                List<TypeMetadataRuleResp> typeMetadataRules = recordTypeMetadataService.queryTypeMetadataRule(
                        recordTypeId);
//                extendParam.setMetadata(typeMetadataRules.stream().map(o-> MetadataDefined.builder()
//                        .name(o.getMdName())
//                        .valueType(o.getValueType())
//                        .valueRegex(o.getValueRange())
//                        .build()).collect(Collectors.toList()));

                JSONArray jsonArray = new JSONArray();
                Map<String, List<String>> map = new HashMap<>();
                map.put("公文",
                        typeMetadataRules.stream().map(TypeMetadataRuleResp::getMdName).collect(Collectors.toList()));
                jsonArray.add(map);
                extendParam.setPredicates(jsonArray.toString());
                //模型提取元数据
                context.publishEvent(new RecordAiMetadataProcessEvent<>(mqRecordReqDTO, req.getPriorityValue(), GET_METADATA_FROM_MODEL));
            } else {
                //OCR提取元数据
                thirdPartyOcrTextApi.getOcrMetadata(mqRecordReqDTO);
            }
        });
        return TaskDocStatusEnum.TDS_PENDING.getCode();
    }

    private EventType eventProcess(Set<EventType> planEventSet, Long recordId, String fileName, String userId,
            String nickName) {
        EventType eventType = planEventSet.contains(GET_METADATA_FROM_MODEL) ? GET_METADATA_FROM_MODEL : GET_METADATA;
        EventType anotherEventType = eventType == GET_METADATA_FROM_MODEL ? GET_METADATA : GET_METADATA_FROM_MODEL;
        // 重置相关事件
        recordProcessDetailService.lambdaUpdate()
                .set(RecordProcessDetail::getRecordProcessStatus, CommonData.NormalState.NORMAL.getCode())
                .set(RecordProcessDetail::getEndTime, null)
                .eq(RecordProcessDetail::getRecordId, recordId)
                .in(RecordProcessDetail::getRecordProcessType, eventType.getCode())
                .eq(RecordProcessDetail::getCtype,RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .update();
        // 重置无关事件
        recordProcessDetailService.lambdaUpdate()
                .set(RecordProcessDetail::getRecordProcessStatus, CommonData.NormalState.NORMAL.getCode())
                .set(RecordProcessDetail::getEndTime, new Date())
                .eq(RecordProcessDetail::getRecordId, recordId)
                .eq(RecordProcessDetail::getRecordProcessType, anotherEventType.getCode())
                .eq(RecordProcessDetail::getCtype,RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .update();

        recordProcessDetailService.saveBatchRecordProcessDetail(
                recordId,
                fileName,
                filterEventTypeList(recordId, CollUtil.newArrayList(eventType)),
                userId,
                nickName
        );
        return eventType;
    }

    @DSTransactional
    @Override
    public void preFail(Long taskDocId,Long recordId) {
        Date nowDate = new Date();
        docProcessService.updateByWrapper(List.of(recordId), new LambdaUpdateWrapper<DocProcess>()
                .set(DocProcess::getProcessStatus,
                        DocProcessStatusEnums.ProcessStatusEnum.PS_EXCEPTION_DOCUMENT.getCode())
                .set(DocProcess::getProcessSubStatus,
                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_EXCEPTION.getCode())
                .set(DocProcess::getModifiedTime, nowDate)
                .eq(DocProcess::getTaskDocId, taskDocId));
        TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            docProcessESService.updateEsDocProcessBase(new MqDocProcessBaseDTO(
                    recordId,
                    DocProcessStatusEnums.ProcessStatusEnum.PS_EXCEPTION_DOCUMENT.getCode(),
                    DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_EXCEPTION.getCode(),
                    nowDate.getTime()
            ));
        });
    }

    @Override
    public Integer findTaskStatus(TaskDocStatusReq req) {
        return findTaskStatus(req, CollUtil.newArrayList(
                EventType.GET_METADATA, GET_METADATA_FROM_MODEL
        ));
    }

    /**
     * 查询开关配置（ocr元数据提取 或  模型元数据提取）
     *
     * @param nodeId
     * @param planId
     * @return
     */
    private Set<EventType> getEventTypes(Long nodeId, Long planId) {
        final DefinedConfigJsonDTO configDto = getConfigDto(nodeId, planId);
        final DefinedConfigJsonDTO.RadioBoxButton radioBoxButton = configDto.getRadioBoxButton();
        final List<DefinedConfigJsonDTO.RadioBox> radioBoxList = radioBoxButton.getRadioBoxList();
        return radioBoxList.stream()
                .filter(o -> o.getCode() != -1)
                .map(o -> EventType.getEnum(o.getCode()))
                .collect(Collectors.toSet());
    }
}
