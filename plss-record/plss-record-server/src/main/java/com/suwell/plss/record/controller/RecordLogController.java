package com.suwell.plss.record.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.RecordLogReq;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import com.suwell.plss.record.standard.service.StandardRecordLogFacade;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/11/25
 */

@Slf4j
@RestController
@RequestMapping("/v1/log")
public class RecordLogController {

    @Autowired
    StandardRecordLogFacade standardRecordLogFacade;

    @RequestMapping("/recordview")
    public R<Page<RecordLogResp>> queryRecordViewLog(@Valid @NotNull @RequestBody RecordLogReq req) {
        return R.ok(standardRecordLogFacade.queryRecordViewLog(req));

    }

}
