package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.MetadataQueryReq;
import com.suwell.plss.record.standard.dto.request.MetadataReq;
import com.suwell.plss.record.standard.dto.request.MetadataTypeReq;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.dto.response.MetadataTypeResp;
import com.suwell.plss.record.standard.service.StandardMetadataFacade;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * 元数据项 管理
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@RestController
@RequestMapping("/v1/metadata")
public class MetadataController {

    public static final String MODE_NAME = "'后台-元数据管理'";
    @Resource
    private StandardMetadataFacade metadataFacade;

    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<MetadataResp>> queryPage(@RequestBody MetadataQueryReq req) {
        PageUtils<MetadataResp> page = metadataFacade.queryPage(req);
        return R.ok(page);
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    public R<List<MetadataResp>> list(@RequestBody MetadataQueryReq req) {
        List<MetadataResp> metadataRespList = metadataFacade.queryList(req);
        return R.ok(metadataRespList);
    }

    /**
     * 业务元数据列表
     */
    @PostMapping("/biz/list")
    public R<List<MetadataResp>> bizList(@RequestBody MetadataQueryReq req) {
        List<MetadataResp> metadataRespList = metadataFacade.queryBizList(req);
        return R.ok(metadataRespList);
    }
    /**
     * 通过文档类型查询入库位置元数据
     */
    @PostMapping("/queryRepoDocType")
    public R<MetadataTypeResp> queryRepoDocType(@RequestBody @Validated MetadataTypeReq req) {
        return R.ok(metadataFacade.queryRepoDocType(req));
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    public R<MetadataResp> info(@RequestBody Long id) {
        return R.ok(metadataFacade.queryById(id));
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody MetadataReq metadataReq) {
        metadataFacade.addMetadata(metadataReq);
        return R.ok();
    }

    /**
     * 批量导入 添加保存
     * //--
     */
    @PostMapping("/saveBatch")
    public R<Void> saveBatch(@RequestBody List<MetadataReq> metadataReqList) {
        metadataFacade.addBatchMetadata(metadataReqList);
        return R.ok();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody MetadataReq metadataReq) {
        metadataFacade.modifyById(metadataReq);
        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody Long[] ids) {
        metadataFacade.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 元数据导入
     */
    @Log(title = MODE_NAME, businessType = BusinessType.IMPORT, isSaveResponseData = false)
    @PostMapping("/importExcel")
    public R<Map<String, Set<Integer>>> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("fileType") String fileType) {
        Map<String, Set<Integer>> resp = metadataFacade.importExcel(file, fileType);
        return R.ok(resp);
    }

    /**
     * 可视化元数据列表
     */
    @PostMapping("/visualPage/list")
    public R<List<MetadataResp>> visualPageList() {
        List<MetadataResp> metadataRespList = metadataFacade.visualPageList();
        return R.ok(metadataRespList);
    }
}
