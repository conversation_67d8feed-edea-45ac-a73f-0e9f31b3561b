package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.response.RecordTypeStatisticsResp;
import com.suwell.plss.record.standard.service.AppDataStatisticsFacade;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 应用数据统计
 *
 * <AUTHOR>
 * @create 2024/11/1
 * @content
 */
@RestController
@RequestMapping("/v1/appDataStatistics")
public class AppDataStatisticsController {

    @Resource
    private AppDataStatisticsFacade appDataStatisticsFacade;

    /**
     * 文档类型统计
     * @return
     */
    @PostMapping("/recordTypeStatistics")
    public R<List<RecordTypeStatisticsResp>> getRecordTypeStatistics() {
        return R.ok(appDataStatisticsFacade.getRecordTypeStatistics());
    }

}
