package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suwell.plss.record.entity.ResourceManager;
import com.suwell.plss.record.mapper.ResourceManagerMapper;
import com.suwell.plss.record.service.ResourceManagerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ResourceManagerServiceImpl extends ServiceImpl<ResourceManagerMapper, ResourceManager> implements
        ResourceManagerService {

    @Resource
    private ResourceManagerMapper resourceManagerMapper;

    @Override
    public List<ResourceManager> queryInResourceManager(List<ResourceManager> list) {
        return resourceManagerMapper.queryInResourceManager(list);
    }


    @DSTransactional
    public boolean saveOrUpdateBatchById(List<ResourceManager> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }

        if (isUpdate) {
            for (int i = 0; i < entityList.size(); i += DEFAULT_BATCH_SIZE) {
                List<ResourceManager> batch = entityList.subList(i, Math.min(i + DEFAULT_BATCH_SIZE, entityList.size()));
                saveOrUpdateBatch(batch);
            }
            return true;
        } else {
            return entityList.stream().allMatch(entity -> {
                ResourceManager resourceManager = resourceManagerMapper.selectById(entity.getId());
                if (resourceManager == null) {
                    return resourceManagerMapper.insert(entity) > 0;
                }
                return true;
            });
        }
    }

    @Override
    public boolean saveOrUpdateBatch(List<ResourceManager> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }
        entityList.forEach(entity -> {
            QueryWrapper<ResourceManager> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("visitor_id", entity.getVisitorId());
            queryWrapper.eq("resource_id", entity.getResourceId());

            long count = resourceManagerMapper.selectCount(queryWrapper);
            if (count > 0) {
                if (!isUpdate) {
                    return;
                }
                update(entity, queryWrapper);
            } else {
                save(entity);
            }
        });

        return true;
    }
}