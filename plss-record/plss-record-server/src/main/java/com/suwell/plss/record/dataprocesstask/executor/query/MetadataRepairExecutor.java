package com.suwell.plss.record.dataprocesstask.executor.query;

import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.domain.RepairMetadataStateDTO;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.RepairMetadataService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 元数据修复执行器
 *
 * <AUTHOR>
 * @since 2024/11/1 11:20
 */
@Service
@Slf4j
public class MetadataRepairExecutor implements DataProcessTaskExecutor {

    @Resource
    private RepairMetadataService repairMetadataService;

    @Override
    public BusinessType getBusinessType() {
        return BusinessType.METADATA_REPAIR;
    }

    @Override
    public void before(Long taskId) {

    }
    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-任务开始,taskId={}",logPrefix, taskId);
        while (true) {
            RepairMetadataStateDTO stateDTO = repairMetadataService.repairMetadata(taskId);
            if (!stateDTO.getInterrupted() && stateDTO.getFinished()) {
                log.info("{}任务-任务完成,taskId={}",logPrefix,  taskId);
                return;
            }
            if (stateDTO.getInterrupted() && !stateDTO.getFinished()) {
                log.info("{}任务-任务中断,taskId={}",logPrefix,  taskId);
                return;
            }
        }
    }
}
