package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.DataProcessTaskBatchDataProcessAddReq;
import com.suwell.plss.record.standard.dto.request.DataProcessTaskReq;
import com.suwell.plss.record.standard.dto.response.BatchRecordProcessQueryResp;
import com.suwell.plss.record.standard.dto.response.DataProcessTaskResp;
import com.suwell.plss.record.standard.service.DataProcessTaskFacade;
import com.suwell.plss.search.standard.dto.request.newsSearch.BackendSearchReq;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 数据处理任务管理
 *
 * <AUTHOR>
 * @date 2024/8/12
 */
@RestController
@RequestMapping("/v1/dataProcessTask")
public class DataProcessTaskController {

    @Resource
    private DataProcessTaskFacade dataProcessTaskFacade;

    /**
     * 数据处理任务-分页查询
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    public R<PageUtils<DataProcessTaskResp>> queryPage(@RequestBody DataProcessTaskReq req) {
        return R.ok(dataProcessTaskFacade.queryPage(req));
    }

    /**
     * 数据处理任务-重试
     */
    @PostMapping("/retry/{taskId}")
    public R<Void> retry(@PathVariable Long taskId) {
        dataProcessTaskFacade.retry(taskId);
        return R.ok();
    }

    /**
     * 数据处理任务-批量删除
     */
    @PostMapping("/deleteBatch")
    public R<Void> deleteBatch(@RequestBody List<Long> taskIdList) {
        dataProcessTaskFacade.deleteBatch(taskIdList);
        return R.ok();
    }

    /**
     * 批量数据处理页面-添加任务
     */
    @PostMapping("/batchDataProcessAdd")
    public R<Void> batchDataProcessAdd(@RequestBody @Valid DataProcessTaskBatchDataProcessAddReq req) {
        dataProcessTaskFacade.batchDataProcessAdd(req);
        return R.ok();
    }

    /**
     * 批量数据处理页面-查询
     */
    @PostMapping("/batchRecordProcessQuery")
    public R<PageUtils<BatchRecordProcessQueryResp>> batchRecordProcessQuery(@RequestBody BackendSearchReq req) {
        return R.ok(dataProcessTaskFacade.batchRecordProcessQuery(req));
    }

}
