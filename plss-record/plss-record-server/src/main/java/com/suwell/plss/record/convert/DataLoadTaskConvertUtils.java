package com.suwell.plss.record.convert;

import static com.suwell.plss.record.standard.enums.RecordEnum.DataLoadTaskOverReason.DATA_LOAD_TASK_OVER_REASON_WAITING;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.suwell.plss.record.entity.DataLoadTask;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskAddReq;
import com.suwell.plss.record.standard.dto.response.DataLoadTaskResp;
import com.suwell.plss.record.standard.enums.DataDeliveryEnum.TaskType;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public class DataLoadTaskConvertUtils {

    public static List<DataLoadTaskResp> dataLoadTask2DataLoadTaskRespList(List<DataLoadTask> dataLoadTaskList) {
        if (CollUtil.isNotEmpty(dataLoadTaskList)) {
            return dataLoadTaskList.stream().map(DataLoadTaskConvertUtils::dataLoadTask2DataLoadTaskResp)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public static DataLoadTaskResp dataLoadTask2DataLoadTaskResp(DataLoadTask dataLoadTask) {
        DataLoadTaskResp dataLoadTaskResp = new DataLoadTaskResp();
        dataLoadTaskResp.setTaskId(dataLoadTask.getTaskId());
        dataLoadTaskResp.setTaskTitle(dataLoadTask.getTaskTitle());
        dataLoadTaskResp.setTaskDesc(dataLoadTask.getTaskDesc());
        dataLoadTaskResp.setStartDatetime(dataLoadTask.getStartDatetime());
        dataLoadTaskResp.setEndDatetime(dataLoadTask.getEndDatetime());
        dataLoadTaskResp.setTaskReason(dataLoadTask.getTaskReason());
        dataLoadTaskResp.setTaskExtendParam(dataLoadTask.getTaskExtendParam());
        dataLoadTaskResp.setDataDir(dataLoadTask.getDataDir());
        dataLoadTaskResp.setModifiedByAccount(dataLoadTask.getModifiedByAccount());
        dataLoadTaskResp.setTaskType(dataLoadTask.getTaskType());
        return dataLoadTaskResp;
    }

    public static DataLoadTask dataLoadTaskAddReq2DataLoadTask(DataLoadTaskAddReq req, Long taskId,
                                                               String userId, String userName) {
        DataLoadTask dataLoadTask = new DataLoadTask();
        dataLoadTask.setTaskId(taskId);
        dataLoadTask.setTaskTitle(req.getTaskTitle());
        dataLoadTask.setTaskDesc(req.getTaskDesc());
        Integer taskType = req.getTaskType();
        if (Objects.isNull(taskType)) {
            taskType = TaskType.MANUAL.getCode();
        }
        dataLoadTask.setTaskType(taskType);
        Date now = new Date();
//        dataLoadTask.setStartDatetime(now);
        dataLoadTask.setTaskReason(DATA_LOAD_TASK_OVER_REASON_WAITING.getCode());
        dataLoadTask.setCreateBy(userId);
        dataLoadTask.setModifiedBy(userId);
        dataLoadTask.setModifiedByAccount(userName);
        dataLoadTask.setCreateTime(now);
        dataLoadTask.setModifiedTime(now);
        dataLoadTask.setDataDir(req.getDataDir());
        return dataLoadTask;
    }
}
