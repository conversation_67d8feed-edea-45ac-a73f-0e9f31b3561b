package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_SUMMARY;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordAiSummaryProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractDigestApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyNlpExtractDigestAsyncApiImpl implements ThirdPartyNlpExtractDigestApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getDigest(MqRecordReqDTO mqRecordReqDTO) {
        log.info("async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordAiSummaryProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_SUMMARY));
//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(GET_SUMMARY));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordAiSummaryProcessEvent<>(mqRecordReqDTO, GET_SUMMARY)));
    }

}
