package com.suwell.plss.record.rpcimpl;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.service.FileExchangeFileIdRpcService;
import com.suwell.plss.record.standard.dto.request.ThirdFileExchangeFileIdReq;
import com.suwell.plss.record.standard.dto.response.ThirdFileExchangeFileIdResp;
import com.suwell.plss.record.standard.service.FileExchangeFileIdFacade;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @description:
 * @author: rui.guo
 * @date: 2025/2/24
 */
@Slf4j
@RestController
@RequestMapping(FileExchangeFileIdRpcService.INNER_PREFIX)
public class FileExchangeFileIdRpcServiceImpl implements FileExchangeFileIdRpcService {

    @Resource
    private FileExchangeFileIdFacade fileExchangeFileIdFacade;


    @Override
    public R<ThirdFileExchangeFileIdResp> queryFileExchangeFileId(ThirdFileExchangeFileIdReq thirdFileExchangeFileIdReq) {
        return R.ok(fileExchangeFileIdFacade.queryFileExchangeFileId(thirdFileExchangeFileIdReq));
    }
}
