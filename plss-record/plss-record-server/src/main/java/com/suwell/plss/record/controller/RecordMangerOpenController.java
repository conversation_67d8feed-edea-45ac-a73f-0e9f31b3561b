package com.suwell.plss.record.controller;

import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_TYPE_ID_NOT_EXIST_ERROR;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.PlanOpenQueryReq;
import com.suwell.plss.record.standard.dto.request.PlanOpenReq;
import com.suwell.plss.record.standard.dto.request.PlanQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeOpenReq;
import com.suwell.plss.record.standard.dto.response.PlanNodeUploadPositionResp;
import com.suwell.plss.record.standard.dto.response.PlanOpenResp;
import com.suwell.plss.record.standard.dto.response.PlanResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeOpenResp;
import com.suwell.plss.record.standard.service.StandardPlanFacade;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件对接管理开放接口
 *
 * <AUTHOR>
 * @date 2024/4/17
 */
@Slf4j
@RestController
@RequestMapping("/v1/recordMangerOpen")
public class RecordMangerOpenController {

    @Resource
    private StandardRecordTypeFacade recordTypeFacade;
    @Resource
    private StandardPlanFacade standardPlanFacade;

    /**
     * 文档类型列表
     */
    @PostMapping("/recordType/list")
    public R<List<RecordTypeOpenResp>> list(@RequestBody RecordTypeOpenReq req) {
        List<RecordTypeOpenResp> recordTypeRespList = recordTypeFacade.queryList(req);
        return R.ok(recordTypeRespList);
    }

    /**
     * 对外封装根据文件类型id,找方案列表
     */
    @PostMapping("/listByRecordTypeId")
    public R<List<PlanOpenResp>> listByRecordTypeId(@RequestBody PlanOpenReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        AssertUtils.isFalse(Objects.isNull(req.getRecordTypeId()), RECORD_TYPE_ID_NOT_EXIST_ERROR);
        List<PlanNodeUploadPositionResp> respList = standardPlanFacade.queryPlanNodeUploadPositionList(
                req.getRecordTypeId());
        if (CollUtil.isNotEmpty(respList)) {
            List<PlanOpenResp> collect = respList.stream().map(x -> {
                PlanOpenResp planOpenResp = new PlanOpenResp();
                planOpenResp.setId(x.getId());
                planOpenResp.setName(x.getName());
                return planOpenResp;
            }).collect(Collectors.toList());
            return R.ok(collect);
        }
        return R.ok(Lists.newArrayList());
    }

    /**
     * 有效的入库方案列表
     */
    @PostMapping("/plan/list")
    public R<List<PlanOpenResp>> queryPage(@RequestBody PlanOpenQueryReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        PlanQueryReq planQueryReq = new PlanQueryReq();
        planQueryReq.setStatus(NormalState.NORMAL.getCode());
        planQueryReq.setName(req.getName());
        planQueryReq.setPage(1);
        planQueryReq.setPageSize(999);
        PageUtils<PlanResp> page = standardPlanFacade.queryPage(planQueryReq);
        List<PlanResp> planRespList = page.getList();
        if (CollUtil.isNotEmpty(planRespList)) {
            List<PlanOpenResp> planOpenRespList = planRespList.stream().map(x -> {
                PlanOpenResp planOpenResp = new PlanOpenResp();
                planOpenResp.setId(x.getId());
                planOpenResp.setName(x.getName());
                return planOpenResp;
            }).collect(Collectors.toList());
            log.info("rsp:{}", JSON.toJSONString(planOpenRespList));
            return R.ok(planOpenRespList);
        }
        return R.ok(Lists.newArrayList());
    }


}
