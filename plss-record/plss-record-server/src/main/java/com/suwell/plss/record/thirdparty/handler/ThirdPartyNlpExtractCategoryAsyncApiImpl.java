package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_CLASSIFY;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordAiClassifyProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractCategoryApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 异步调用ai 获取分类
 *
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyNlpExtractCategoryAsyncApiImpl implements ThirdPartyNlpExtractCategoryApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getClassify(MqRecordReqDTO mqRecordReqDTO) {
        log.info("异步发送提取分类消息 async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordAiClassifyProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_CLASSIFY));
//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(GET_CLASSIFY));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordAiClassifyProcessEvent<>(mqRecordReqDTO, GET_CLASSIFY)));
    }
}
