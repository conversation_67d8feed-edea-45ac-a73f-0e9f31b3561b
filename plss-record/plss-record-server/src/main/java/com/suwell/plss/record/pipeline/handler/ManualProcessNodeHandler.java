package com.suwell.plss.record.pipeline.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.mq.dto.base.MqDocProcessBaseDTO;
import com.suwell.plss.framework.mq.events.NodeManualProcessEvent;
import com.suwell.plss.record.entity.DocProcess;
import com.suwell.plss.record.entity.TaskDoc;
import com.suwell.plss.record.enums.DocProcessStatusEnums;
import com.suwell.plss.record.enums.TaskDocTypeEnum;
import com.suwell.plss.record.pipeline.StorageInfoV3Dto;
import com.suwell.plss.record.standard.domain.MqNodeProcessDto;
import com.suwell.plss.record.standard.dto.request.TaskDocStatusReq;
import com.suwell.plss.record.standard.dto.request.TaskNodeReq;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.enums.TaskDocStatusEnum;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/15
 * @content
 */
@Component
public class ManualProcessNodeHandler extends AbstractNodeHandler {

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.NT_MANUAL_PROCESS;
    }

    @Override
    public void sendMq(MqNodeProcessDto dto) {
        context.publishEvent(new NodeManualProcessEvent<>(dto, dto.getPriorityValue()));
    }

    @Override
    public boolean needBreak(TaskNodeReq req) {
        final TaskDoc taskDoc = getTaskDoc(req.getTaskDocId(), req.getRecordId());
        StorageInfoV3Dto storageInfoV3Dto = getStorageInfoV3Dto(taskDoc);
        Map<Integer, Boolean> skipNodeTypeMap = storageInfoV3Dto.getSkipNodeTypeMap();
        boolean skipFlag =
                CollUtil.isNotEmpty(skipNodeTypeMap) && Boolean.TRUE.equals(skipNodeTypeMap.get(getType().getCode()));
        return TaskDocTypeEnum.TDT_ATTACHMENT.getCode() == taskDoc.getCtype() || skipFlag;
    }

    @Override
    public MqDocProcessBaseDTO prePending(Long taskDocId, Long recordId) {
        Date nowDate = new Date();
        docProcessService.updateByWrapper(List.of(recordId), new LambdaUpdateWrapper<DocProcess>()
                .set(DocProcess::getProcessStatus,
                        DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode())
                .set(DocProcess::getProcessSubStatus,
                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_MANUAL_CHECKING_AND_POPULATING.getCode())
                .set(DocProcess::getModifiedTime, new Date())
                .eq(DocProcess::getTaskDocId, taskDocId));

        return new MqDocProcessBaseDTO(
                recordId,
                DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_MANUAL_CHECKING_AND_POPULATING.getCode(),
                nowDate.getTime()
        );
    }

    @Override
    public void preFail(Long taskDocId, Long recordId) {

    }

    @Override
    @DSTransactional
    public Integer nodeExecuteLogic(TaskNodeReq req) {
        //
        MqDocProcessBaseDTO mqDto = prePending(req.getTaskDocId(), req.getRecordId());
        TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            docProcessESService.updateEsDocProcessBase(mqDto);
        });
        return TaskDocStatusEnum.TDS_MANUAL_PROCESS.getCode();
    }

    @Override
    public Integer findTaskStatus(TaskDocStatusReq req) {
        return TaskDocStatusEnum.TDS_SUCCESS.getCode();
    }
}
