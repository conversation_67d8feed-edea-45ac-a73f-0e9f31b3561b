package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.PlanCheckReq;
import com.suwell.plss.record.standard.dto.request.PlanCurrentUserReq;
import com.suwell.plss.record.standard.dto.request.PlanEditeEnableReq;
import com.suwell.plss.record.standard.dto.request.PlanNodeEditReq;
import com.suwell.plss.record.standard.dto.request.PlanNodeReq;
import com.suwell.plss.record.standard.dto.request.PlanQueryReq;
import com.suwell.plss.record.standard.dto.request.PlanRecordInfoReq;
import com.suwell.plss.record.standard.dto.request.PlanSearchReq;
import com.suwell.plss.record.standard.dto.response.PlanDetailsResp;
import com.suwell.plss.record.standard.dto.response.PlanNodeEventResp;
import com.suwell.plss.record.standard.dto.response.PlanNodeUploadPositionResp;
import com.suwell.plss.record.standard.dto.response.PlanResp;
import com.suwell.plss.record.standard.service.StandardPlanFacade;
import java.util.Arrays;
import java.util.List;
import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.*;

/**
 * 入库配置方案
 *
 * <AUTHOR>
 * @date 2023/11/4
 */
@RestController
@RequestMapping("/v1/plan")
public class PlanController {

    @Resource
    private StandardPlanFacade standardPlanFacade;

    public static final String MODE_NAME = "'后台-入库方案配置'";


    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<PlanResp>> queryPage(@RequestBody PlanQueryReq planQueryReq) {
        PageUtils<PlanResp> page = standardPlanFacade.queryPage(planQueryReq);
        return R.ok(page);
    }

    /**
     * 分页列表
     */
    @PostMapping("/info/{planId}")
    public R<PlanDetailsResp> info(@PathVariable Long planId) {
        return R.ok(standardPlanFacade.queryPlanById(planId));
    }

    /**
     * 根据文件类型id,找方案（上传界面下拉框）
     */
    @PostMapping("/list/{recordTypeId}")
    public R<List<PlanNodeUploadPositionResp>> listByRecordTypeId(@PathVariable(name = "recordTypeId") Long recordTypeId,@RequestParam(required = false) Long folderId,@RequestParam(required = false,defaultValue = "false") Boolean forSecure) {
        return R.ok(standardPlanFacade.queryPlanNodeUploadPositionList(recordTypeId,folderId,forSecure));
    }

    /**
     * 根据文件类型id,找方案
     */
    @PostMapping("/infoList")
    public R<List<PlanResp>> planList(@RequestBody PlanSearchReq req) {
        List<PlanResp> planRespList = standardPlanFacade.queryPlanList(req);
        return R.ok(planRespList);
    }


    /**
     * 配置方案-新增
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/savePlan")
    public R<Void> savePlanNode(@RequestBody PlanNodeReq planNodeReq) {
        standardPlanFacade.addPlanNode(planNodeReq);
        return R.ok();
    }

    /**
     * 配置方案
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/updatePlan")
    public R<Void> updatePlan(@RequestBody PlanNodeEditReq planNodeEditReq) {
        standardPlanFacade.modifyPlan(planNodeEditReq);
        return R.ok();
    }

    /**
     * 入库方案上传文件
     */
    @PostMapping("/planRecordUpload")
    public R<Void> planRecordUpload(@ModelAttribute PlanRecordInfoReq planRecordInfoReq) {
        standardPlanFacade.addPlanRecordUpload(planRecordInfoReq);
        return R.ok();
    }


    /**
     * 删除
     */
    @Log(title = MODE_NAME,businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody Long[] ids) {
        standardPlanFacade.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 开启禁用状态
     *
     * @param req
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/enable")
    public R<Void> enable(@RequestBody PlanEditeEnableReq req) {
        standardPlanFacade.updateEnable(req);
        return R.ok();
    }

    /**
     * 查询当前人指定租户下可见的入库方案：用户，角色，组织机构
     */
    @PostMapping("/currentUserHavePlan")
    public R currentUserHavePlan(@RequestBody PlanCurrentUserReq planCurrentUserReq){
        List<PlanResp> planResps = standardPlanFacade.currentUserHavePlan(planCurrentUserReq);

        return R.ok(planResps);
    }

    /**
     * 校验入库方案是否可用
     * @param req
     * @return
     */
    @PostMapping("/checkPlan")
    public R<Boolean> checkPlan(@RequestBody PlanCheckReq req){
        return R.ok(standardPlanFacade.checkPlan(req));
    }

    /**
     * 获取方案节点隐藏事件
     * @return
     */
    @PostMapping("/getPlanNodeHideEvent")
    public R<PlanNodeEventResp> getPlanNodeHideEvent(){
        return R.ok(standardPlanFacade.getPlanNodeHideEvent());
    }


}
