package com.suwell.plss.record.permission.validator;

import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.enums.ResouceClassifiedEnum;
import com.suwell.plss.framework.common.enums.UserClassifiedEnum;
import com.suwell.plss.framework.common.exception.auth.NotPermissionException;
import com.suwell.plss.framework.datapermission.BasicResource;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.record.domain.ResourceChainDTO;
import com.suwell.plss.record.standard.enums.PermissionMask;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;

/**
 * 文件基础服务的验证器
 *
 * <AUTHOR>
 */
public interface RecordValidator {

    /**
     * 单数据校验
     *
     * @param resourceId 资源id
     * @param masks 权限掩码
     */
    void validate(Long resourceId, PermissionMask... masks) throws NotPermissionException;

    /**
     * 过滤出<b>有权限</b>的资源
     *
     * @param resourceIds 资源id
     * @param masks 权限掩码
     * @return 返回有效的数据集合
     */
    List<Long> filterValid(List<Long> resourceIds, PermissionMask... masks);

    /**
     * 过滤出<b>有权限</b>的资源
     * <b>保留没有权限的数据</b>
     *
     * @param resources 资源列表
     * @param masks 权限掩码
     * @return 返回有效的数据集合
     */
    default <A extends BasicResource> List<A> retainInvalidData(List<A> resources, PermissionMask... masks) {
        List<Long> resourceIds = resources.stream().map(BasicResource::getResourceId).collect(Collectors.toList());
        List<Long> validIds = filterValid(resourceIds, masks);
        List<A> list = Lists.newArrayList(resources);
        list.removeIf(source -> validIds.contains(source.getResourceId()));
        return list;
    }

    /**
     * 过滤出<b>没有权限</b>的资源
     *
     * @param resourceIds 资源id
     * @param masks 权限掩码
     * @return 返回有效的数据集合
     */
    List<Long> filterInvalid(List<Long> resourceIds, PermissionMask... masks);

    /**
     * 过滤出<b>没有权限</b>的资源
     * <b>保留有权限的数据</b>
     *
     * @param resources 资源列表
     * @param masks 权限掩码
     * @return 返回有效的数据集合
     */
    default <A extends BasicResource> List<A> retainValidData(List<A> resources, PermissionMask... masks) {
        List<Long> resourceIds = resources.stream().map(BasicResource::getResourceId).collect(Collectors.toList());
        List<Long> validIds = filterInvalid(resourceIds, masks);
        List<A> list = Lists.newArrayList(resources);
        list.removeIf(source -> validIds.contains(source.getResourceId()));
        return list;
    }

    /**
     * 资源类型
     *
     * @return 资源类型
     */
    @NotNull
    ResourceType getResourceType();

    /**
     * 获取资源访问者
     *
     * @return 资源访问者ids
     */
    @NotNull
    List<String> listResourceVisitorIds();

    /**
     * 获取资源鉴权链
     *
     * @param resourceId 资源id
     * @return 链路数据
     */
    List<ResourceChainDTO> listResourceChains(Long resourceId);

    Map<Long, List<ResourceChainDTO>> batchListRecordChains(List<Long> recordIds);

    /**
     * 过滤需要鉴权的资源id
     *
     * @param chainDTOS 资源链路
     * @return 需要鉴权的资源id
     */
    Set<Long> listNeedAuthResourceIds(List<ResourceChainDTO> chainDTOS, Long targetResourceId, boolean showExtends);

    boolean isPrivateResource(Long resourceId);

    /**
     * 使资源私有
     */
    void makePrivate(Long resourceId);

    /**
     * 取消私有
     */
    void cancelPrivate(Long resourceId);

    Integer getClassified(Long resourceId);

    /**
     * 用户是否匹配资源
     *
     * @param resourceId 资源id
     * @return 是否匹配
     */
    boolean matchResource(Long resourceId, UserClassifiedEnum... userClassifiedEnum);

}
