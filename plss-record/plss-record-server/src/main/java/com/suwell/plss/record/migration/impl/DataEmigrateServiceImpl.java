package com.suwell.plss.record.migration.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.suwell.plss.document.process.dto.request.ExportRecordReq;
import com.suwell.plss.document.process.dto.request.MetadataIdValue;
import com.suwell.plss.document.process.dto.request.SearchExpRecordReq;
import com.suwell.plss.document.process.dto.response.SearchExpRecordResp;
import com.suwell.plss.document.process.service.DocumentProcessRpcService;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.uuid.IdUtils;
import com.suwell.plss.record.entity.ExportFilter;
import com.suwell.plss.record.entity.ExportTask;
import com.suwell.plss.record.migration.*;
import com.suwell.plss.record.service.ExportFilterService;
import com.suwell.plss.record.service.ExportTaskService;
import com.suwell.plss.record.standard.dto.request.ExportFilterReq;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.logging.FileHandler;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;
import java.util.stream.Collectors;

import static com.suwell.plss.framework.common.exception.enums.GlobalBizError.BAD_REQUEST;

@Service
public class DataEmigrateServiceImpl implements DataEmigrateService {
    private final Logger logger = Logger.getLogger(this.getClass().getName());
    private FileHandler fileHandler;

    @Resource
    private ExportTaskService exportTaskService;
    @Resource
    private ExportFilterService exportFilterService;
    @Resource
    private DocumentMigrateService documentMigrationService;
    @Resource
    private KnowledgeMigrateService knowledgeMigrationService;
    @Resource
    private RecordMigrateService recordMigrateService;
    @Resource
    private FolderMigrateService folderMigrationService;
    @Resource
    private DocumentProcessRpcService documentProcessRpcService;

    private final ExecutorService finalExecutors = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors()));
    ;

    @Override
    public Map<String, Object> estimateFilter(ExportFilterReq req) {

        AssertUtils.notNull(req, BAD_REQUEST);

        ExportFilter exportFilter = this.reqToFilter(req);
        long nCount = this.estimateSearchFilter(exportFilter);

        Map<String, Object> map = new HashMap<>();
        map.put("total", nCount);

        return map;
    }

    private long estimateSearchFilter(ExportFilter exportFilter) {

        if (exportFilter == null) {
            return 0;
        }

        SearchExpRecordReq req = this.filterToExpReq(exportFilter);

        PageUtils<Long> result = this.searchPage(req);

        return result.getTotalCount();
    }

    @Override
    public Map<String, Object> exportFilter(ExportFilterReq req) {
        if (req == null) {
            return null;
        }
        if (StringUtils.isEmpty(req.getPrefixPath())) {
            req.setPrefixPath(PATH_PREFIX);
        }

        ExportFilter exportFilter = this.reqToFilter(req);

        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String dirname = "auto_" + currentTime.format(formatter);

        this.initExport(req.getPrefixPath(), dirname);

        long nCount = this.exportSubtask(exportFilter, req.getPrefixPath(), dirname);

        this.closeExport();

        Map<String, Object> map = new HashMap<>();
        map.put("total", nCount);
        map.put("path", Paths.get(req.getPrefixPath(), dirname).toString());

        return map;
    }

    @Override
    public void exportMultiTask(long taskId) {
        try {
            ExportTask exportTask = exportTaskService.getById(taskId);
            if (Objects.isNull(exportTask) || exportTask.getStatus() > 1) {
                nextTask();
                return;
            }

            this.initExport(PATH_PREFIX, exportTask.getDirPath());

            exportTask.setStartTime(new Date());
            exportTask.setStatus(2);
            exportTaskService.updateById(exportTask);

            List<ExportFilter> listExportFilter = exportFilterService.list(
                    new LambdaQueryWrapper<ExportFilter>().eq(ExportFilter::getTaskId, taskId));

            CompletableFuture[] array = listExportFilter.stream().map(
                    exportFilter -> CompletableFuture.runAsync(() -> {
                        try {
                            long nCount = this.exportSubtask(exportFilter, PATH_PREFIX, exportTask.getDirPath());
                            exportFilter.setTotalRecord(nCount);
                            exportFilterService.updateById(exportFilter);
                        } catch (Exception e) {
                            logger.warning("Error processing exportFilter: " + e.getMessage());
                        }
                    }, finalExecutors)).toArray(CompletableFuture[]::new);

            // 等待所有任务完成
            CompletableFuture.allOf(array).join();

            exportTask.setFinishTime(new Date());
            exportTask.setStatus(3);
            exportTaskService.updateById(exportTask);

            if (fileHandler != null) {
                try {
                    fileHandler.close();
                } catch (Exception e) {
                    logger.warning("Error closing fileHandler: " + e.getMessage());
                }
            }

            TimeUnit.SECONDS.sleep(3);
        } catch (Exception ex) {
            logger.info(ex.getMessage());
        } finally {
            this.closeExport();
            this.nextMultiTask();
        }
    }

    @Override
    public void exportTask(long taskId) {

        try {
            ExportTask exportTask = exportTaskService.getById(taskId);
            if (Objects.isNull(exportTask) || exportTask.getStatus() > 1) {
                nextTask();
                return;
            }

            this.initExport(PATH_PREFIX, exportTask.getDirPath());

            exportTask.setStartTime(new Date());
            exportTask.setStatus(2);
            exportTaskService.updateById(exportTask);

            List<ExportFilter> listExportFilter = exportFilterService.list(
                    new LambdaQueryWrapper<ExportFilter>().eq(ExportFilter::getTaskId, taskId));

            listExportFilter.forEach(exportFilter -> {
                long nCount = this.exportSubtask(exportFilter, PATH_PREFIX, exportTask.getDirPath());
                exportFilter.setTotalRecord(nCount);
                exportFilterService.updateById(exportFilter);
            });

            exportTask.setFinishTime(new Date());
            exportTask.setStatus(3);
            exportTaskService.updateById(exportTask);

            fileHandler.close();

            TimeUnit.SECONDS.sleep(3);
        } catch (Exception ex) {
            logger.info(ex.getMessage());
        } finally {
            this.closeExport();
            nextTask();
        }
    }

    private long exportSubtask(ExportFilter exportFilter, String prefixPath, String dirname) {

        SearchExpRecordReq req = this.filterToExpReq(exportFilter);
        req.setPrefixPath(prefixPath);
        req.setDirname(dirname);
        req.setIsExport(true);

        return this.exportData(req);
    }

    private void initExport(String prefixPath, String dirname) {

        try {
//            if (runFlag.get()) {
//                return;
//            }
            folderMigrationService.init();
            recordMigrateService.init();
//            runFlag.set(true);

            Path dirPath = Paths.get(prefixPath, dirname);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }

            fileHandler = new FileHandler(Paths.get(dirPath.toString(), LOG_FILENAME).toString());
            fileHandler.setFormatter(new SimpleFormatter());
            logger.addHandler(fileHandler);
        } catch (Exception ex) {
//            runFlag.set(false);
            throw new RuntimeException("init logger error!!!");
        }
    }

    private void closeExport() {
//        if (runFlag.get()) {
//            runFlag.set(false);
//        }

        logger.removeHandler(fileHandler);
        if (fileHandler != null) {
            fileHandler.close();
        }

        this.folderMigrationService.clear();
        this.recordMigrateService.clear();
    }

    private ExportFilter reqToFilter(ExportFilterReq req) {
        return ExportFilter.builder().limitRecord(req.getLimitRecord())
                .repoId(req.getRepoId())
                .repoName(req.getRepoName())
                .folderIds(req.getFolderIds())
                .categoryIds(req.getCategoryIds())
                .putLibMin(req.getPutLibMin())
                .putLibMax(req.getPutLibMax())
                .mdValueJson(req.getMdValueJson())
                .notRecordStatus(req.getNotRecordStatus())
                .fileType(req.getFileType())
                .esQueryDsl(req.getEsQueryDsl())
                .build();
    }

    private SearchExpRecordReq filterToExpReq(ExportFilter exportFilter) {

        SearchExpRecordReq req = new SearchExpRecordReq();

        req.setRepoId(exportFilter.getRepoId());
        req.setPutLibTimeMin(exportFilter.getPutLibMin());
        req.setPutLibTimeMax(exportFilter.getPutLibMax());
        req.setLimit(exportFilter.getLimitRecord());
        req.setFileType(exportFilter.getFileType());
        req.setEsQueryDsl(exportFilter.getEsQueryDsl());
        int pageSize = 100;
        req.setPageSize(pageSize);
        req.setPage(1);

        if (StringUtils.isNotEmpty(exportFilter.getFolderIds())) {
            List<Long> listFolderId = Arrays.stream(exportFilter.getFolderIds().split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());
            req.setListFolderId(listFolderId);
        }

        if (StringUtils.isNotEmpty(exportFilter.getCategoryIds())) {
            List<Long> listCategoryId = Arrays.stream(exportFilter.getCategoryIds().split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());
            req.setListCategoryId(listCategoryId);
        }

        if (StringUtils.isNotEmpty(exportFilter.getMdValueJson())) {
            String jsonData = exportFilter.getMdValueJson();
            List<MetadataIdValue> listMetadataIdValue = JSONArray.parseArray(jsonData, MetadataIdValue.class);
            req.setListMetadata(listMetadataIdValue);
        }

        if (StringUtils.isNotEmpty(exportFilter.getNotRecordStatus())) {
            List<Long> listNotRecordStatus = Arrays.stream(exportFilter.getNotRecordStatus().split(","))
                    .map(Long::parseLong).collect(Collectors.toList());
            req.setListNotRecordStatus(listNotRecordStatus);
        }

        return req;
    }

    private void nextTask() {
        ExportTask exportTask = exportTaskService.list(new LambdaQueryWrapper<ExportTask>()
                .eq(ExportTask::getStatus, 1)
                .orderByAsc(ExportTask::getStartTime)).stream().findFirst().orElse(null);

        System.out.println("next_task: " + exportTask);
//        System.out.println("runFlag: " + runFlag.get());

        if (exportTask == null) {
            return;
        }

        exportTask(exportTask.getId());
    }

    private void nextMultiTask() {
        ExportTask exportTask = exportTaskService.list(new LambdaQueryWrapper<ExportTask>()
                .eq(ExportTask::getStatus, 1)
                .orderByAsc(ExportTask::getStartTime)).stream().findFirst().orElse(null);

        System.out.println("next_task: " + exportTask);
//        System.out.println("runFlag: " + runFlag.get());

        if (exportTask == null) {
            return;
        }

        exportMultiTask(exportTask.getId());
    }

    private Long exportData(SearchExpRecordReq req) {
        try {
            if (req.getLimit() != null && req.getLimit() > 0) {
                req.setTotal(req.getLimit());
                if (req.getLimit() < req.getPageSize()) {
                    req.setPageSize(req.getLimit().intValue());
                }
            }

//            PageUtils<Long> recordIdPage = this.searchPage(req);
            SearchExpRecordResp resp = this.pageNlpRecord(req);
            if (resp.getTotal() == 0) {
                logger.info("pageNlpRecord 返回结果为空或总数为0， 提前结束导出, 参数为：" + JSONObject.toJSONString(req));
                logger.info("pageNlpRecord 返回结果为空或总数为0， 提前结束导出, 响应值为：" + JSONObject.toJSONString(resp));
                return 0L;
            }

            if (req.getTotal() <= 0 || resp.getTotal() < req.getTotal()) {
                req.setTotal(resp.getTotal());
            }
            logger.info(String.format("total: %d", req.getTotal()));
            logger.info(String.format("page: 1, size: %d", resp.getListRecordId().size()));
            String savePath = Paths.get(req.getPrefixPath(), req.getDirname()).toString();
            this.exportByRecord(resp.getListRecordId(), savePath);

            int totalPage = (int) Math.ceil(req.getTotal() / (double) req.getPageSize());
            for (int i = 2; i <= totalPage; ++i) {
                req.setPage(i);
                resp = this.pageNlpRecord(req);
                if (resp.getTotal() == 0) {
                    logger.warning("pageNlpRecord 返回结果为空或总数为0， 提前结束导出");
                    break;
                }
                logger.info(String.format("page: %d, size: %d", i, resp.getListRecordId().size()));

                this.exportByRecord(resp.getListRecordId(), savePath);
            }

            this.exportPublic(savePath);

            return req.getTotal();

        } catch (Exception ex) {
            logger.log(Level.WARNING, "数据导出发生异常：", ex);
        }

        return 0L;
    }

    private PageUtils<Long> searchPage(SearchExpRecordReq req) {
        try {
            String filename = IdUtils.simpleUUID() + ExportConstant.SUFFIX_ES_RECORD;
            req.setFilename(filename);

            R<SearchExpRecordResp> result = documentProcessRpcService.searchExport(req);
            if (result.isError()) {
                logger.info(result.getMsg());
                return PageUtils.empty();
            }

            if (StringUtils.isNotEmpty(result.getData().getScrollId())) {
                req.setScrollId(result.getData().getScrollId());
            }

            return new PageUtils<>(result.getData().getListRecordId(), result.getData().getTotal(),
                    req.getPageSize(), req.getPage());
        } catch (Exception ex) {
            logger.info(ex.getMessage());
            return PageUtils.empty();
        }
    }


    private SearchExpRecordResp pageNlpRecord(SearchExpRecordReq req) {
        // 查询实体列表
        String filename = IdUtils.simpleUUID() + ExportConstant.SUFFIX_ES_RECORD;
        req.setFilename(filename);
        R<SearchExpRecordResp> result = documentProcessRpcService.searchExportJson(req);
        if (result.isError() || Objects.isNull(result.getData())) {
            throw new RuntimeException("查询nlp_record失败: " + result.getMsg());
        }
        if (StringUtils.isNotEmpty(result.getData().getScrollId())) {
            req.setScrollId(result.getData().getScrollId());
        }
        SearchExpRecordResp data = result.getData();
        if (CollUtil.isNotEmpty(data.getListRecordId())) {
            // 导出nlp_record
            String filepath = Paths.get(req.getPrefixPath(), req.getDirname(), req.getFilename()).toString();
            CommonUtil.seriallizeList(data.getNlpRecordList(), filepath);
            exportRecordVector(data.getListRecordId(), Paths.get(req.getPrefixPath(), req.getDirname()).toString());
        }
        return data;
    }

    private void exportRecordVector(List<Long> listRecordId, String dirname) {
        if (CollUtil.isEmpty(listRecordId)) {
            return;
        }
        Lists.partition(listRecordId, 10).forEach(recordIds -> {
            // 导出nlp_record_vector
            long l = System.currentTimeMillis();
            R<List<JSONObject>> vectorListR = documentProcessRpcService.searchVectorExportJson(recordIds);
            if (vectorListR.isError()) {
                throw new RuntimeException("导出nlp_record_vector失败: " + vectorListR.getMsg());
            }
            logger.info(String.format("exportRecordVector cost: %dms, request recordSize: %d, found recordVectorSize: %d",
                    System.currentTimeMillis() - l,
                    recordIds.size(),
                    vectorListR.getData().size()));

            Map<Long, List<JSONObject>> vectorRecord = vectorListR.getData().stream()
                    .collect(Collectors.groupingBy(vector -> vector.getLong("recordId")));
            if (vectorRecord.size() != recordIds.size()) {
                throw new RuntimeException("导出nlp_record_vector的数量和预期record数量不一致，实际vector数量: " +
                        vectorRecord.size() + ", 预期数量: " + recordIds.size());
            }
            vectorRecord.forEach((recordId, vectors) -> {
                String vectorFilepath = Paths.get(dirname,
                        recordId + ExportConstant.SUFFIX_ES_VECTOR_RECORD).toString();
                CommonUtil.seriallizeList(vectors, vectorFilepath);
            });
        });
    }

    private void exportByRecord(List<Long> listRecordId, String dirname) {
        listRecordId = this.recordMigrateService.exportRecord(listRecordId, dirname);
        logger.info(String.format("record real size: %d", listRecordId.size()));

        if (listRecordId.isEmpty()) {
            return;
        }

        this.documentMigrationService.exportDocument(listRecordId, dirname);
        this.knowledgeMigrationService.exportKnowledge(listRecordId, dirname);
        this.folderMigrationService.exportFolderRecord(listRecordId, dirname);
    }

    private void exportPublic(String savePath) {
        this.recordMigrateService.exportRecordType(savePath);
        this.folderMigrationService.exportFolder(savePath);
    }

    //export data by record id
    public void exportByRecordId(ExportRecordReq req) {
        req.setPrefixPath(PATH_PREFIX);
        req.setFilename(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")));

        this.exportByRecord(req.getRecordIdList(), req.getDirname());
        this.exportPublic(req.getDirname());

        this.documentProcessRpcService.exportByRecordId(req);
    }

}
