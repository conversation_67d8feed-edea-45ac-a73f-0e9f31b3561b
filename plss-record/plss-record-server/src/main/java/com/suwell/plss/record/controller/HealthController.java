package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> 健康
 */
@RestController
@Slf4j
@RequestMapping("/v1/k8s/health")
public class HealthController {

    /**
     * 接口健康测试
     *
     * @param
     * @return
     */
    @GetMapping("/ok")
    public R<String> reportOk() {
        return R.ok();
    }
}
