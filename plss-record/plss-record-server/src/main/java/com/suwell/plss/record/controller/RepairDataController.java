package com.suwell.plss.record.controller;

import com.suwell.plss.record.service.RecordService;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Slf4j
@RestController
@RequestMapping("/v1/repair")
public class RepairDataController {

    @Resource
    private RecordService recordService;

    @PostMapping("/repairRecordPerm")
    public void repairRecordPerm(@RequestBody(required = false) List<Long> recordIds) {
        log.info("repairRecordPerm recordIds:{}", recordIds);
        recordService.updateRecordPerm(recordIds);
    }

}
