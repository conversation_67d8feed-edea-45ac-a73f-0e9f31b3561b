package com.suwell.plss.record.facade;

import static com.suwell.plss.record.standard.enums.RecordBizError.METADATA_RECORD_TYPE_ID_NOT_NULL;
import static com.suwell.plss.record.standard.enums.RecordBizError.METADATA_RECORD_TYPE_NAME_UNIQUE_NAME;
import static com.suwell.plss.record.standard.enums.RecordBizError.METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_ONE;
import static com.suwell.plss.record.standard.enums.RecordBizError.METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_TYPE_ERROR;
import static com.suwell.plss.record.standard.enums.RecordBizError.METADATA_RECORD_TYPE_UNIQUE_NAME;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_INFO_CATEGORY_ID_NOT_NULL;
import static com.suwell.plss.record.standard.enums.RecordStatusEnum.RECORD_STATUS_PASS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.enums.CommonData.YesEnum;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.LambdaUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.conf.RecordPersonalMetadataConfig;
import com.suwell.plss.record.convert.RecordTypeConvertUtils;
import com.suwell.plss.record.convert.RecordTypeMetadataConvertUtils;
import com.suwell.plss.record.domain.PersonalMetadataDTO;
import com.suwell.plss.record.domain.RecordTypeMetadataDTO;
import com.suwell.plss.record.entity.Metadata;
import com.suwell.plss.record.entity.Plan;
import com.suwell.plss.record.entity.RecordType;
import com.suwell.plss.record.entity.RecordTypeMetadata;
import com.suwell.plss.record.entity.RepoMetadata;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.service.MetadataService;
import com.suwell.plss.record.service.PlanService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RecordTypeMetadataService;
import com.suwell.plss.record.service.RecordTypeService;
import com.suwell.plss.record.service.RepoMetadataService;
import com.suwell.plss.record.standard.dto.MetadataSearchResultViewDTO;
import com.suwell.plss.record.standard.dto.ReleaseTimeRefreshTaskDto;
import com.suwell.plss.record.standard.dto.request.MetadataValueDefineReq;
import com.suwell.plss.record.standard.dto.request.MoveRecordTypeGroupReq;
import com.suwell.plss.record.standard.dto.request.PlanSearchReq;
import com.suwell.plss.record.standard.dto.request.QueryMetadataViewReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeMetadataCategoryAddEditReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeMetadataEditReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeMetadataReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeOpenReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataTree;
import com.suwell.plss.record.standard.dto.request.SearchableMetadataReq;
import com.suwell.plss.record.standard.dto.response.MetadataSearchResultViewResp;
import com.suwell.plss.record.standard.dto.response.PersonalRecordTypeMetadataResp;
import com.suwell.plss.record.standard.dto.response.PersonalRecordTypeMetadataResp.MetadataIdName;
import com.suwell.plss.record.standard.dto.response.PlanResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeMetadataFrontResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeMetadataResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeMetadataRuleResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeNumResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeOpenResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeResp;
import com.suwell.plss.record.standard.dto.response.SearchableMetadataResp;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordMetadataValueTypeEnum;
import com.suwell.plss.record.standard.enums.RepositoryEnum;
import com.suwell.plss.record.standard.service.StandardPlanFacade;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import com.suwell.plss.search.api.http.SearchRpcEntrance;
import com.suwell.plss.search.standard.dto.request.newsSearch.AggRecordCountQueryReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchRecordV2QueryReq;
import com.suwell.plss.search.standard.dto.response.SearchAggCountResp;
import com.suwell.plss.system.api.domain.LoginUser;
import com.suwell.plss.system.api.service.CategoryRpcService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 文件类型 接口实现
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@Service
@RefreshScope
public class StandardRecordTypeFacadeImpl implements StandardRecordTypeFacade {

    @Resource
    private RecordTypeService recordTypeService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private RepoMetadataService repoMetadataService;

    @Value("${record.type.defaultGroupId:2340466262277}")
    private Long recordTypeDefaultGroupId;
    @Resource
    private RecordPersonalMetadataConfig personalMetadataConfig;
    @Resource
    private MetadataService metadataService;
    @Resource
    private StandardPlanFacade standardPlanFacade;
    @Resource
    private PlanService planService;

    @Resource
    private DataProcessTaskService dataProcessTaskService;
    @Resource
    private RecordService recordService;
    @Resource
    private SearchRpcEntrance searchRpcEntrance;

    @Override
    public PageUtils<RecordTypeResp> queryPage(RecordTypeQueryReq req) {
        log.info("user account:{},req:{}", SecurityUtils.getUsername(), JSON.toJSONString(req));
        BeanValidators.defaultValidate(req);
        PageUtils<RecordTypeResp> pageUtils = recordTypeService.queryPage(req);
        log.info("rsp size:{}", pageUtils.getList().size());
        return pageUtils;
    }

    @Override
    public List<RecordTypeResp> queryList(RecordTypeQueryReq req) {
        log.info("user account:{},req:{}", SecurityUtils.getUsername(), JSON.toJSONString(req));

        LambdaQueryWrapper<RecordType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Objects.nonNull(req.getStatus()), RecordType::getStatus, req.getStatus());
        lambdaQueryWrapper.in(CollUtil.isNotEmpty(req.getIdList()), RecordType::getId, req.getIdList());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(req.getName()), RecordType::getName, req.getName());
        lambdaQueryWrapper.orderByAsc(RecordType::getId);
        List<RecordType> recordTypeList = recordTypeService.list(lambdaQueryWrapper);
        log.info("rsp recordTypeList size:{}", CollUtil.isNotEmpty(recordTypeList)
                ? recordTypeList.size() : StringPool.ZERO);

        return DozerUtils.convertListToNew(recordTypeList, RecordTypeResp.class);
    }

    @Override
    public List<RecordTypeResp> queryByIds(List<Long> ids) {
        List<RecordType> list = recordTypeService.list(
                new LambdaQueryWrapper<RecordType>().in(RecordType::getId, ids).eq(RecordType::getStatus,
                        Integer.valueOf(UserConstants.YSE_STATUS)));
        return DozerUtils.convertListToNew(list, RecordTypeResp.class);

    }

    @Override
    public RecordTypeMetadataResp queryById(Long id) {
        log.info("req id:{}", id);
        RecordTypeMetadataResp recordTypeMetadataResp = recordTypeService.queryRecordTypeById(id);
        log.info("rsp:{}", JSON.toJSONString(recordTypeMetadataResp));
        return recordTypeMetadataResp;
    }

    @DSTransactional
    @Override
    public boolean addRecordType(RecordTypeReq recordTypeReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        log.info("req:{}", JSON.toJSONString(recordTypeReq));
        AssertUtils.isNull(recordTypeService.getOne(Wrappers.<RecordType>query().lambda()
                .eq(RecordType::getName, recordTypeReq.getName())), METADATA_RECORD_TYPE_UNIQUE_NAME);

        RecordType recordType = new RecordType();
        Long recordTypeId = wfgIdGenerator.next();
        recordType.setId(recordTypeId);
        recordType.setName(recordTypeReq.getName());
        recordType.setStatus(NormalState.NORMAL.getCode());
        recordType.setCreateTime(new Date());
        recordType.setModifiedTime(new Date());
        recordType.setCreateBy(loginUser.getUserid());
        recordType.setModifiedBy(loginUser.getUserid());
        if (CollUtil.isNotEmpty(recordTypeReq.getRecordTypeMetadataList())) {
            List<RecordTypeMetadata> recordTypeMetadataList = recordTypeReq.getRecordTypeMetadataList()
                    .stream().map(x -> {
                        RecordTypeMetadata recordTypeMetadata = new RecordTypeMetadata();
                        recordTypeMetadata.setRecordtypeId(recordTypeId);
                        recordTypeMetadata.setMdId(x.getMdId());
                        recordTypeMetadata.setValueType(x.getValueType());
                        recordTypeMetadata.setValueRange(x.getValueRange());
                        recordTypeMetadata.setSearchFlag(x.getSearchFlag());
                        recordTypeMetadata.setOrderby(x.getOrderby());
                        recordTypeMetadata.setMdcategoryId(x.getMdcategoryId());
                        return recordTypeMetadata;
                    }).collect(Collectors.toList());
            recordTypeMetadataService.saveBatch(recordTypeMetadataList);
        }
        return recordTypeService.save(recordType);
    }

    @Override
    public boolean addBatchRecordType(List<RecordTypeReq> recordTypeReqList) {
        log.info("req:{}", JSON.toJSONString(recordTypeReqList));
        List<RecordType> recordTypeList = DozerUtils.convertListToNew(recordTypeReqList, RecordType.class);
        return recordTypeService.saveBatch(recordTypeList);
    }

    @DSTransactional
    @Override
    public void modifyById(RecordTypeReq recordTypeReq) {
        log.info("req:{}", JSON.toJSONString(recordTypeReq));
        AssertUtils.isTrue(Objects.nonNull(recordTypeReq.getId()), METADATA_RECORD_TYPE_ID_NOT_NULL);

        AssertUtils.isNull(recordTypeService.getOne(Wrappers.<RecordType>query().lambda()
                .eq(RecordType::getName, recordTypeReq.getName())
                .ne(RecordType::getId, recordTypeReq.getId())), METADATA_RECORD_TYPE_UNIQUE_NAME);

        RecordType recordType = new RecordType();
        recordType.setName(recordTypeReq.getName());
        recordType.setId(recordTypeReq.getId());
        recordType.setModifiedTime(new Date());
        recordType.setModifiedBy(SecurityUtils.getLoginUser().getUserid());
        recordTypeService.updateById(recordType);

        recordTypeMetadataService.remove(Wrappers.<RecordTypeMetadata>query().lambda()
                .eq(RecordTypeMetadata::getRecordtypeId, recordTypeReq.getId()));
        if (CollUtil.isNotEmpty(recordTypeReq.getRecordTypeMetadataList())) {
            List<RecordTypeMetadata> recordTypeMetadataList = recordTypeReq.getRecordTypeMetadataList()
                    .stream().map(x -> {
                        RecordTypeMetadata recordTypeMetadata = new RecordTypeMetadata();
                        recordTypeMetadata.setRecordtypeId(recordTypeReq.getId());
                        recordTypeMetadata.setMdId(x.getMdId());
                        recordTypeMetadata.setValueType(x.getValueType());
                        recordTypeMetadata.setValueRange(x.getValueRange());
                        recordTypeMetadata.setSearchFlag(x.getSearchFlag());
                        return recordTypeMetadata;
                    }).collect(Collectors.toList());
            recordTypeMetadataService.saveBatch(recordTypeMetadataList);
        }
    }

    @Override
    public void removeByIds(List<Long> ids) {
        log.info("req ids:{}", ids);
        AssertUtils.isTrue(CollUtil.isNotEmpty(ids), RECORD_INFO_CATEGORY_ID_NOT_NULL);
        recordTypeService.removeRecordTypeMetadataByIds(ids);
    }

    @Override
    public List<TypeMetadataRuleResp> queryTypeMetadataRule(Long recordTypeId) {
        log.info("req recordTypeId:{}", recordTypeId);
        return recordTypeMetadataService.queryTypeMetadataRule(recordTypeId);
    }

    @Override
    public List<TypeMetadataRuleResp> batchQueryTypeMetadataRule(List<Long> recordTypeIdList) {
        return recordTypeMetadataService.batchQueryTypeMetadataRule(recordTypeIdList);
    }

    @Override
    public PersonalRecordTypeMetadataResp queryPersonalRecordTypeMetadata(Long recordTypeId) {
        List<RecordTypeMetadata> list = recordTypeMetadataService.list(Wrappers.<RecordTypeMetadata>query().lambda()
                .eq(RecordTypeMetadata::getRecordtypeId, recordTypeId));
        Map<Long, RecordTypeMetadata> recordTypeMetadataMap = Maps.newHashMap();
        for (RecordTypeMetadata recordTypeMetadata : list) {
            recordTypeMetadataMap.put(recordTypeMetadata.getMdId(), recordTypeMetadata);
        }

        List<PersonalMetadataDTO> personalMetadataDTOList = personalMetadataConfig.getPersonalMetadataDTO(
                personalMetadataConfig.getConfig());
        if (CollUtil.isNotEmpty(personalMetadataDTOList)) {
            personalMetadataDTOList.sort(Comparator.comparing(PersonalMetadataDTO::getOrderNum));
            List<String> mdNameList = personalMetadataDTOList.stream().map(PersonalMetadataDTO::getMdName)
                    .collect(Collectors.toList());
            List<Metadata> metadataList = metadataService.list(Wrappers.<Metadata>query().lambda()
                    .in(Metadata::getName, mdNameList));
            if (CollUtil.isEmpty(metadataList)) {
                return new PersonalRecordTypeMetadataResp();
            }

            List<Metadata> metadataResp = metadataList.stream()
                    .filter(v -> recordTypeMetadataMap.containsKey(v.getId()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(metadataResp)) {
                return new PersonalRecordTypeMetadataResp();
            }
            PersonalRecordTypeMetadataResp personalRecordTypeMetadataResp = new PersonalRecordTypeMetadataResp();
            personalRecordTypeMetadataResp.setRecordTypeId(recordTypeId);
            personalRecordTypeMetadataResp.setMetadataIdNameList(metadataResp.stream().map(x -> {
                MetadataIdName metadataIdName = new MetadataIdName();
                metadataIdName.setMdId(x.getId());
                metadataIdName.setMdName(x.getName());
                return metadataIdName;
            }).collect(Collectors.toList()));
            return personalRecordTypeMetadataResp;
        }
        return new PersonalRecordTypeMetadataResp();
    }

    @Override
    public List<RecordTypeMetadataRuleResp> conditionQueryMetadata(RecordTypeQueryReq req) {
        log.info("req :{}", req);
        List<RecordTypeMetadataDTO> metadataList = recordTypeMetadataService.queryMetadata(req);
        log.info("rsp RecordTypeMetadataDTO metadataList size:{}", CollUtil.size(metadataList));
        return metadataList.stream().map(x -> {
            RecordTypeMetadataRuleResp metadataResp = new RecordTypeMetadataRuleResp();
            metadataResp.setRecordtypeId(x.getRecordtypeId());
            metadataResp.setId(x.getMdId());
            metadataResp.setName(x.getMdName());
            metadataResp.setValueType(x.getValueType());
            metadataResp.setValueRange(x.getValueRange());
            metadataResp.setSearchFlag(x.getSearchFlag());
            metadataResp.setMdcategoryId(x.getMdcategoryId());
            metadataResp.setOrderby(x.getOrderby());
            metadataResp.setRequired(x.getRequired());
            metadataResp.setOptEdit(x.getOptEdit());
            metadataResp.setOptView(x.getOptView());
            MetadataValueDefineReq define = JSON.parseObject(x.getValueRule(), MetadataValueDefineReq.class);
            metadataResp.setDefine(define);
            metadataResp.setPinyin(x.getPinyin());
            metadataResp.setMnemonic(x.getMnemonic());
            metadataResp.setComment(x.getRemark());
            metadataResp.setBorrowView(x.getBorrowView());
            metadataResp.setDetailsView(x.getDetailsView());
            metadataResp.setSearchResultView(x.getSearchResultView());
            metadataResp.setSearchFieldWay(x.getSearchFieldWay());
            return metadataResp;
        }).collect(Collectors.toList());
    }

    @Override
    @DSTransactional
    public void addMetadata(RecordTypeMetadataReq req) {
        log.info("req:{}", req);

        AssertUtils.isNull(recordTypeService.getOne(Wrappers.<RecordType>query().lambda()
                        //.eq(RecordType::getStatus, NormalState.NORMAL.getCode())
                        .eq(RecordType::getName, req.getRecordTypeName().trim())),
                METADATA_RECORD_TYPE_NAME_UNIQUE_NAME);

        List<RecordTypeMetadataCategoryAddEditReq> mdReqList = req.getCategoryAddReqList();
        List<RecordTypeMetadataCategoryAddEditReq> sortMdReqList = mdReqList.stream()
                .filter(mdReq -> YesEnum.YES.getCode().equals(mdReq.getOptSortStandardItem())).toList();
        AssertUtils.isTrue(sortMdReqList.size() <= 1, METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_ONE);
        if (CollectionUtils.isNotEmpty(sortMdReqList)) {
            RecordTypeMetadataCategoryAddEditReq sortMdReq = sortMdReqList.get(0);
            AssertUtils.isTrue(RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_DATA.getCode()
                    .equals(sortMdReq.getValueType())
                    || RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_DATA_TIME.getCode()
                    .equals(sortMdReq.getValueType()), METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_TYPE_ERROR);
        }

        RecordType recordType = new RecordType();
        Long recordTypeId = wfgIdGenerator.next();
        recordType.setId(recordTypeId);
        recordType.setName(req.getRecordTypeName().trim());
        recordType.setFrontView(req.getFrontView());
        recordType.setStatus(NormalState.NORMAL.getCode());
        recordType.setGroupTypeId(Objects.nonNull(req.getGroupTypeId()) ?
                req.getGroupTypeId() : recordTypeDefaultGroupId);
        recordType.setCreateTime(new Date());
        recordType.setModifiedTime(new Date());
        String userid = SecurityUtils.getLoginUser().getUserid();
        recordType.setCreateBy(userid);
        recordType.setModifiedBy(userid);
        recordType.setRemark(req.getRemark());
        LambdaUtils.ifNotNull(req.getFileTypeJsonDto(), x -> recordType.setFileTypeJson(JSON.toJSONString(x)));
        recordTypeService.save(recordType);

        //保存元数据
        recordTypeMetadataService.saveBatch(
                RecordTypeMetadataConvertUtils.recordTypeMetadataCategoryAddEditReqList2RecordTypeMetadataList(
                        req.getCategoryAddReqList(), recordTypeId));
    }

    @Override
    @DSTransactional
    public void modifyMetadata(RecordTypeMetadataEditReq req) {
        log.info("req:{}", req);
        AssertUtils.isNull(recordTypeService.getOne(Wrappers.<RecordType>query().lambda()
                        .eq(RecordType::getName, req.getRecordTypeName().trim())
                        .ne(RecordType::getId, req.getRecordTypeId())),
                METADATA_RECORD_TYPE_NAME_UNIQUE_NAME);

        List<RecordTypeMetadataCategoryAddEditReq> mdReqList = req.getCategoryAddReqList();
        List<RecordTypeMetadataCategoryAddEditReq> sortMdReqList = mdReqList.stream()
                .filter(mdReq -> YesEnum.YES.getCode().equals(mdReq.getOptSortStandardItem())).toList();
        AssertUtils.isTrue(sortMdReqList.size() <= 1, METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_ONE);

        Long sortMdReqId = -1L;
        if (CollectionUtils.isNotEmpty(sortMdReqList)) {
            RecordTypeMetadataCategoryAddEditReq sortMdReq = sortMdReqList.get(0);
            AssertUtils.isTrue(RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_DATA.getCode()
                    .equals(sortMdReq.getValueType())
                    || RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_DATA_TIME.getCode()
                    .equals(sortMdReq.getValueType()), METADATA_RECORD_TYPE_SORT_STANDARD_ITEM_TYPE_ERROR);
            sortMdReqId = sortMdReq.getMdId();
        }

        RecordTypeMetadata sortMd = recordTypeMetadataService.getOne(Wrappers.<RecordTypeMetadata>lambdaQuery()
                .eq(RecordTypeMetadata::getRecordtypeId, req.getRecordTypeId())
                .eq(RecordTypeMetadata::getOptSortStandardItem, YesEnum.YES.getCode()));
        Long sortMdId = Objects.nonNull(sortMd) ? sortMd.getMdId() : -1L;
        if (!sortMdId.equals(sortMdReqId)) {
//            long recordCount = recordService.count(Wrappers.<Record>query().lambda()
//                    .eq(Record::getRecordtypeId, req.getRecordTypeId())
//                    .eq(Record::getRecordStatus, RECORD_STATUS_PASS.getCode())
//                    .eq(Record::getStatus, NormalState.NORMAL.getCode())
//                    .le(Record::getCreateTime, new Date()));
            SearchRecordV2QueryReq searchReq = new SearchRecordV2QueryReq();
            searchReq.setRecordTypeIds(List.of(req.getRecordTypeId())).setRecordStatus(RECORD_STATUS_PASS.getCode())
                    .setRecordCreateTimeEnd(System.currentTimeMillis());
            R<Long> recordCountR = searchRpcEntrance.innerSearchRecordCount(searchReq);
            long recordCount = 1;
            if (recordCountR.isSuccess()) {
                recordCount = recordCountR.getData();
            }
            if (recordCount > 0) {
                ReleaseTimeRefreshTaskDto taskDto = new ReleaseTimeRefreshTaskDto();
                taskDto.setRecordTypeId(req.getRecordTypeId());

                dataProcessTaskService.addTask(BusinessType.RELEASE_TIME_REFRESH, 0,
                        JSON.toJSONString(taskDto), recordCount, 0L);
            }
        }

        RecordType recordType = new RecordType();
        recordType.setId(req.getRecordTypeId());
        recordType.setName(req.getRecordTypeName().trim());
        recordType.setFrontView(req.getFrontView());
        recordType.setGroupTypeId(Objects.nonNull(req.getGroupTypeId()) ?
                req.getGroupTypeId() : recordTypeDefaultGroupId);
        recordType.setModifiedTime(new Date());
        recordType.setModifiedBy(SecurityUtils.getLoginUser().getUserid());
        recordType.setRemark(req.getRemark());
        LambdaUtils.ifNotNull(req.getFileTypeJsonDto(), x -> recordType.setFileTypeJson(JSON.toJSONString(x)));
        recordTypeService.updateById(recordType);

        List<RecordTypeMetadataCategoryAddEditReq> categoryAddReqList = req.getCategoryAddReqList();
        if (CollUtil.isNotEmpty(categoryAddReqList)) {
            //删除
            recordTypeMetadataService.remove(Wrappers.<RecordTypeMetadata>query().lambda()
                    .eq(RecordTypeMetadata::getRecordtypeId, req.getRecordTypeId()));

            //保存元数据
            recordTypeMetadataService.saveBatch(
                    RecordTypeMetadataConvertUtils.recordTypeMetadataCategoryAddEditReqList2RecordTypeMetadataList(
                            categoryAddReqList, req.getRecordTypeId()));
        }
    }

    @Override
    public boolean removeMetadata(Long recordTypeId) {
        return recordTypeMetadataService.remove(Wrappers.<RecordTypeMetadata>query().lambda()
                .eq(RecordTypeMetadata::getRecordtypeId, recordTypeId));
    }

    @DSTransactional
    @Override
    public void removeRecordTypeGroup(Long groupId) {
        categoryRpcService.delete(groupId);
        RecordType recordType = new RecordType();
        recordType.setGroupTypeId(recordTypeDefaultGroupId);
        recordTypeService.update(recordType, Wrappers.<RecordType>query().lambda()
                .eq(RecordType::getGroupTypeId, groupId));

    }

    @Override
    public void updateRecordTypeGroup(MoveRecordTypeGroupReq req) {
        RecordType recordType = new RecordType();
        recordType.setGroupTypeId(req.getGroupId());
        recordTypeService.update(recordType, Wrappers.<RecordType>query().lambda()
                .in(RecordType::getId, req.getIds()));
    }

    /**
     * 查询文件类型前端显示的元数据
     */
    @Override
    public Map<String, List<RecordTypeMetadataFrontResp>> queryRecordMetadata() {
        List<RecordTypeMetadata> list = recordTypeMetadataService.lambdaQuery()
                .eq(RecordTypeMetadata::getDetailsView, 1)
                .or()
                .eq(RecordTypeMetadata::getSearchResultView, 1).list();
        return list.stream().collect(Collectors.groupingBy(x -> String.valueOf(x.getRecordtypeId()),
                Collectors.mapping(this::getRecordTypeMetadataFrontResp, Collectors.toList())));
    }


    private RecordTypeMetadataFrontResp getRecordTypeMetadataFrontResp(RecordTypeMetadata x) {
        RecordTypeMetadataFrontResp metadataResp = new RecordTypeMetadataFrontResp();
        metadataResp.setId(x.getMdId());
        metadataResp.setName(x.getMdName());
        metadataResp.setPinyin(x.getPinyin());
        metadataResp.setMnemonic(x.getMnemonic());
        metadataResp.setBorrowView(x.getBorrowView());
        metadataResp.setDetailsView(x.getDetailsView());
        metadataResp.setSearchResultView(x.getSearchResultView());
        metadataResp.setSearchFieldWay(x.getSearchFieldWay());
        metadataResp.setComment(x.getRemark());
        metadataResp.setValueType(x.getValueType());
        metadataResp.setValueRange(x.getValueRange());
        metadataResp.setSearchFlag(x.getSearchFlag());
        metadataResp.setOrderby(x.getOrderby());
        metadataResp.setRequired(x.getRequired());
        metadataResp.setOptEdit(x.getOptEdit());
        metadataResp.setOptView(x.getOptView());
        MetadataValueDefineReq define = JSON.parseObject(x.getValueRule(), MetadataValueDefineReq.class);
        metadataResp.setDefine(define);
        metadataResp.setRecordtypeId(x.getRecordtypeId());
        metadataResp.setMdcategoryId(x.getMdcategoryId());
        return metadataResp;
    }

    @DSTransactional
    @Override
    public void updateStatus(Long recordTypeId, Integer status) {
        //修改文档类型状态
        recordTypeService.lambdaUpdate().set(RecordType::getStatus, status).eq(RecordType::getId, recordTypeId)
                .update();
        //如果是禁用文档类型则同步禁用关联的入库方案
        if (status == 2) {
            //查询所有关联的入库方案
            PlanSearchReq planSearchReq = new PlanSearchReq();
            planSearchReq.setRecordTypeId(recordTypeId);
            List<PlanResp> planRespList = standardPlanFacade.queryPlanList(planSearchReq);
            if (CollectionUtil.isNotEmpty(planRespList)) {
                List<Plan> plans = new ArrayList<>();
                planRespList.forEach(o -> {
                    Plan plan = new Plan();
                    plan.setId(o.getId());
                    plan.setStatus(2);
                    plans.add(plan);
                });
                //禁用入库方案
                planService.updateBatchById(plans);
            }
        }

    }

    @Override
    public List<RecordTypeNumResp> getRecordPercent(String tenantId) {
        List<RecordType> recordTypeList = recordTypeService.lambdaQuery()
                .eq(RecordType::getStatus, NormalState.NORMAL.getCode()).list();
        if (CollUtil.isEmpty(recordTypeList)) {
            return new ArrayList<>();
        }
        // 使用传入的租户id 或者 登录的租户id
        List<Long> recordTypeIdList = recordTypeList.stream().map(RecordType::getId).toList();
        List<RecordTypeNumResp> recordTypeNumRespList = new ArrayList<>();
        AggRecordCountQueryReq queryReq = new AggRecordCountQueryReq();
        queryReq.setAggField("recordTypeId");
        if(ObjectUtils.isNotEmpty(tenantId)){
            queryReq.setTenantIds(List.of(tenantId));
        }
        queryReq.setRecordStatus(400);
        queryReq.setStoreProcess(2L);
        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRecordCount(queryReq);
        if (listR.isError()) {
            log.error("统计文档存储数量失败:" + listR.getMsg());
            throw new BizException("统计文档存储数量失败");
        }
        List<SearchAggCountResp> data = listR.getData();

        data.sort(new Comparator<SearchAggCountResp>() {
            @Override
            public int compare(SearchAggCountResp o1, SearchAggCountResp o2) {
                return o2.getCount().compareTo(o1.getCount());
            }
        });
        Long count = 0L;
        long totalCount = data.stream().mapToLong(SearchAggCountResp::getCount).sum();
        Map<Long, RecordType> recordTypeMap = recordTypeList.stream()
                .collect(Collectors.toMap(RecordType::getId, r -> r));
        for (int i = 0; i < data.size(); i++) {
            RecordType recordType = recordTypeMap.get(data.get(i).getId());
            if (ObjectUtils.isNotEmpty(recordType) && recordTypeMap.containsKey(recordType.getId())) {
                RecordTypeNumResp recordTypeResp = new RecordTypeNumResp();
                BeanUtils.copyProperties(recordType, recordTypeResp);
                recordTypeResp.setNum(0L);
                recordTypeResp.setProportion(BigDecimal.ZERO);
                if (recordTypeMap.containsKey(recordType.getId())) {
                    Long recordCount = data.get(i).getCount();
                    recordTypeResp.setNum(recordCount);
                    count = count + recordCount;
                    recordTypeResp.setProportion(BigDecimal.valueOf(recordCount).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP));
                }
                recordTypeNumRespList.add(recordTypeResp);
            }
            if (recordTypeNumRespList.size() > 6) {
                break;
            }
        }
//        BigDecimal other = BigDecimal.valueOf(100);
//        for (RecordTypeNumResp recordTypeNumResp : recordTypeNumRespList) {
//            other = other.subtract(recordTypeNumResp.getProportion());
//        }
//        RecordTypeNumRes p recordTypeResp = new RecordTypeNumResp();
//        recordTypeResp.setName("其他");
//        recordTypeResp.setNum(totalCount - count);
//        recordTypeResp.setProportion(other);
//        recordTypeNumRespList.add(recordTypeResp);
        return recordTypeNumRespList;
    }

    @Override
    public Map<Long, Long> queryTypeMetadataClassify(List<Long> recordTypeIdList, String name) {
        log.info("req:{},元数据的名称{}", JSON.toJSONString(recordTypeIdList), name);
        List<RecordTypeMetadata> list = recordTypeMetadataService.list(Wrappers.<RecordTypeMetadata>query().lambda()
                .eq(RecordTypeMetadata::getMdName, name)
                .in(RecordTypeMetadata::getRecordtypeId, recordTypeIdList));
        if (CollUtil.isNotEmpty(list)) {
            Map<Long, Long> map = Maps.newHashMap();
            for (RecordTypeMetadata recordTypeMetadata : list) {
                map.put(recordTypeMetadata.getRecordtypeId(), recordTypeMetadata.getMdId());
            }
            return map;
        }
        return Maps.newHashMap();
    }

    @Override
    public List<RecordTypeOpenResp> queryList(RecordTypeOpenReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        List<RecordType> list = recordTypeService.list(Wrappers.<RecordType>query().lambda()
                .eq(RecordType::getStatus, NormalState.NORMAL.getCode())
                .like(StringUtils.isNotBlank(req.getRecordTypeName()),
                        RecordType::getName, req.getRecordTypeName())
                .orderByDesc(RecordType::getId));
        return RecordTypeConvertUtils.recordTypeList2RecordTypeOpenRespList(list);
    }

    @Override
    public List<SearchableMetadataResp> querySearchableMetadata(SearchableMetadataReq searchableMetadataReq) {
        List<Long> recordTypeIds = searchableMetadataReq.getRecordTypeIds();
        List<Long> repoIds = searchableMetadataReq.getRepoIds();
        Set<Long> recordTypeIdSet = new HashSet<>();
        if (CollUtil.isNotEmpty(repoIds)) {
            List<RepoMetadata> repoMetadataList = repoMetadataService.list(new LambdaQueryWrapper<RepoMetadata>()
                    .in(RepoMetadata::getRepoId, repoIds)
                    .eq(RepoMetadata::getType, RepositoryEnum.MetaDataType.DOC_TYPE.getCode()));
            if (CollUtil.isNotEmpty(repoMetadataList)) {
                List<String> jsonValueList = repoMetadataList.stream().map(RepoMetadata::getJsonValue).toList();
                List<Long> recordTypeList = new ArrayList<>();
                for (String jsonValue : jsonValueList) {
                    List<RepositoryMetadataTree> repositoryMetadataTrees = JSON.parseArray(jsonValue,
                            RepositoryMetadataTree.class);
                    if (CollUtil.isNotEmpty(repositoryMetadataTrees)) {
                        recordTypeList.addAll(
                                repositoryMetadataTrees.stream().map(RepositoryMetadataTree::getNodeId).toList());
                    }
                }
                if (CollUtil.isNotEmpty(recordTypeList)) {
                    recordTypeIdSet.addAll(recordTypeList);
                }
            }
        }
        if (CollUtil.isNotEmpty(recordTypeIds)) {
            recordTypeIdSet.addAll(recordTypeIds);
        }
        if (CollUtil.isEmpty(recordTypeIdSet)) {
            return Collections.emptyList();
        }
        List<RecordTypeMetadata> searchableMetadataList = recordTypeMetadataService.list(
                new LambdaQueryWrapper<RecordTypeMetadata>()
                        .in(RecordTypeMetadata::getRecordtypeId, recordTypeIdSet)
                        .eq(RecordTypeMetadata::getSearchFlag, 1));
        if (CollUtil.isEmpty(searchableMetadataList)) {
            return Collections.emptyList();
        }
        List<String> excludeMetadataNameList = Arrays.asList("标题", "系统分类", "入库位置", "知识提取");
        Map<Long, RecordTypeMetadata> metadataMap = searchableMetadataList.stream()
                .filter(recordTypeMetadata -> !excludeMetadataNameList.contains(recordTypeMetadata.getMdName()))
                .collect(Collectors
                        .toMap(RecordTypeMetadata::getMdId, Function.identity(), (v1, v2) -> v2));
        List<SearchableMetadataResp> metadataRespList = new ArrayList<>();
        for (RecordTypeMetadata value : metadataMap.values()) {
            SearchableMetadataResp metadataResp = new SearchableMetadataResp();
            metadataResp.setId(value.getMdId());
            metadataResp.setName(value.getMdName());
            Integer valueType = value.getValueType();
            metadataResp.setValueType(valueType);
            if (Objects.equals(valueType,
                    RecordEnum.RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_LIST.getCode())) {
                //固定选项元数据
                String valueRule = value.getValueRule();
                if (StringUtils.isNotEmpty(valueRule)) {
                    JSONObject jsonObject = JSON.parseObject(valueRule);
                    JSONArray selectItemsJsonArray = jsonObject.getJSONArray("selectItems");
                    if (CollUtil.isNotEmpty(selectItemsJsonArray)) {
                        List<String> selectItems = selectItemsJsonArray.stream().map(Object::toString)
                                .collect(Collectors.toList());
                        metadataResp.setSelectItems(selectItems);
                    }
                }
            }
            metadataRespList.add(metadataResp);
        }
        return metadataRespList;
    }

    @Override
    public MetadataSearchResultViewResp queryMetadataViewByRecordTypes(QueryMetadataViewReq req) {
        log.info("查询搜索结果展示的元数据,req:{}", JSON.toJSONString(req));
        List<RecordTypeMetadata> list = recordTypeMetadataService.list(new LambdaQueryWrapper<RecordTypeMetadata>()
                .eq(RecordTypeMetadata::getSearchResultView, Integer.valueOf(StringPool.ONE))
                .in(RecordTypeMetadata::getRecordtypeId, Sets.newHashSet(req.getRecordTypeIds())));

        MetadataSearchResultViewResp resultViewResp = new MetadataSearchResultViewResp();
        Map<Long, List<MetadataSearchResultViewDTO>> matadataMap = Maps.newHashMap();
        if (CollUtil.isEmpty(list)) {
            resultViewResp.setMatadataMap(matadataMap);
            return resultViewResp;
        }
        // 将查询结果转换为 MetadataSearchResultViewDTO 列表
        List<MetadataSearchResultViewDTO> dtoList = list.stream()
                .map(this::convertToDto).toList();

        // 按 recordTypeId 分组
        matadataMap = dtoList.stream()
                .collect(Collectors.groupingBy(MetadataSearchResultViewDTO::getRecordTypeId,
                        Collectors.mapping(Function.identity(), Collectors.toList())));

        // 对每个 recordTypeId 对应的列表按 orderBy 升序排序
        matadataMap.forEach((recordTypeId, metadataList) -> metadataList.sort(
                Comparator.comparing(MetadataSearchResultViewDTO::getOrderBy)));
        log.info("查询搜索结果展示的元数据,rsp:{}", JSON.toJSONString(matadataMap));
        resultViewResp.setMatadataMap(matadataMap);
        return resultViewResp;
    }

    private MetadataSearchResultViewDTO convertToDto(RecordTypeMetadata metadata) {
        MetadataSearchResultViewDTO dto = new MetadataSearchResultViewDTO();
        dto.setRecordTypeId(metadata.getRecordtypeId());
        dto.setMdId(metadata.getMdId());
        dto.setMdName(metadata.getMdName());
        dto.setValueType(metadata.getValueType());
        dto.setOrderBy(metadata.getOrderby());
        return dto;
    }
}

