package com.suwell.plss.record.rpcimpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.AccessTrendDTO;
import com.suwell.plss.framework.common.domain.AccessTrendDateDTO;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.DateUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.entity.RcStatisticData;
import com.suwell.plss.record.enums.StatisticDataTypeEnum;
import com.suwell.plss.record.service.RcStatisticDataService;
import com.suwell.plss.record.service.RecordLogViewRpcService;
import com.suwell.plss.record.standard.dto.request.RecordLogReq;
import com.suwell.plss.record.standard.dto.response.AccessTrendResp;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import com.suwell.plss.record.standard.service.StandardRecordLogFacade;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date : 2023/12/13
 */
@Slf4j
@RestController
@RequestMapping(RecordLogViewRpcService.INNER_PREFIX)
public class RecordLogViewRpcServiceImpl implements RecordLogViewRpcService {

    @Resource
    private StandardRecordLogFacade standardRecordLogFacade;
    @Resource
    private RcStatisticDataService statisticDataService;

    @Override
    public R updateInvalidStatus(List<Long> recordIds) {
        standardRecordLogFacade.updateInvalidStatus(recordIds);
        return R.ok();
    }

    @Override
    public R<PageUtils<RecordLogResp>> queryRecordViewLog(RecordLogReq req) {
        Page<RecordLogResp> page = standardRecordLogFacade.queryRecordViewLog(req);
        return R.ok(new PageUtils<>(page));
    }

    @Override
    public R<AccessTrendResp> userVisitStatistic(AccessTrendDateDTO req) {
        String tenantId = SecurityUtils.getTenantId();
        log.info("当前人所在的租户:{}", tenantId);
        LambdaQueryWrapper<RcStatisticData> sdLqw = new LambdaQueryWrapper<>();
        sdLqw.eq(RcStatisticData::getDataType, StatisticDataTypeEnum.VISIT_INFO.getCode());
        sdLqw.le(RcStatisticData::getBusinessDate, new Date(req.getThirtyDaysAgo()))
                .lt(RcStatisticData::getBusinessDate, new Date(req.getTodayBegin()));
//        sdLqw.eq(RcStatisticData::getTenantId, tenantId);
        sdLqw.orderByAsc(RcStatisticData::getBusinessDate);
        List<RcStatisticData> statisticDataList = statisticDataService.list(sdLqw);
        AccessTrendResp accessTrendResp = new AccessTrendResp();
        // 环比总数
        List<RcStatisticData> sixStatisticDataList = statisticDataService.list(
                Wrappers.<RcStatisticData>query().lambda()
                        .eq(RcStatisticData::getDataType, StatisticDataTypeEnum.VISIT_INFO.getCode())
                        .ge(RcStatisticData::getBusinessDate, new Date(req.getSixtyDaysAgo()))
                        .lt(RcStatisticData::getBusinessDate, new Date(req.getThirtyDaysAgo())));

        // 同比总数 产品沟通暂不做
//        List<RcStatisticData> yearStatisticDataList = statisticDataService.list(
//                Wrappers.<RcStatisticData>query().lambda()
//                        .eq(RcStatisticData::getDataType, StatisticDataTypeEnum.VISIT_INFO.getCode())
//                        .ge(RcStatisticData::getBusinessDate, new Date(req.getLastYearTodayThirtyDaysAgo()))
//                        .lt(RcStatisticData::getBusinessDate, new Date(req.getLastYearToday())));

        Long totalCount = 0L;
        if (CollUtil.isNotEmpty(statisticDataList)) {
            totalCount = statisticDataList.stream().mapToLong(RcStatisticData::getQuantity).sum();
        }

        Long sixTotalCount = 0L;
        if (CollUtil.isNotEmpty(sixStatisticDataList)) {
            sixTotalCount = sixStatisticDataList.stream().mapToLong(RcStatisticData::getQuantity).sum();
        }
//
//        Long yearTotalCount = 0L;
//        if (CollUtil.isNotEmpty(yearStatisticDataList)) {
//            yearTotalCount = yearStatisticDataList.stream().mapToLong(RcStatisticData::getQuantity).sum();
//        }

        accessTrendResp.setTotalCount(totalCount);
        if (sixTotalCount != 0) {
            Long c = totalCount - sixTotalCount;
            accessTrendResp.setChainRate(NumberUtil.div(c.doubleValue(), sixTotalCount.doubleValue()) * 100);
        } else {
            accessTrendResp.setChainRate(0.0D);
        }
//        if (yearTotalCount != 0) {
//            Long c = totalCount - yearTotalCount;
//            accessTrendResp.setAlikeRate(NumberUtil.div(c.doubleValue(), yearTotalCount.doubleValue()) * 100);
//        } else {
//            accessTrendResp.setAlikeRate(0.0D);
//        }

        if (CollUtil.isNotEmpty(statisticDataList)) {
            List<AccessTrendDTO> accessTrendDTOList = statisticDataList.stream().map(x -> {
                AccessTrendDTO accessTrendDTO = new AccessTrendDTO();
                accessTrendDTO.setDay(x.getBusinessDate().getTime());
                accessTrendDTO.setDayCount(x.getQuantity());
                return accessTrendDTO;
            }).collect(Collectors.toList());
            accessTrendResp.setAccessTrendList(accessTrendDTOList);
        } else {
            accessTrendResp.setAccessTrendList(DateUtils.initAccessTrendList(new Date(req.getTodayBegin()), 30));
        }
        return R.ok(accessTrendResp);
    }

}
