package com.suwell.plss.record.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.suwell.plss.framework.common.config.jasypt.JasyptDTO;
import com.suwell.plss.framework.common.config.jasypt.JasyptUtils;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.StringEncryptorReq;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/11/13
 */
@Slf4j
@RestController
@RequestMapping("/v1/jasypt")
public class JasyptController {
    @Resource
    private StringEncryptor stringEncryptor;

    @Value("${jasypt.jasyptType:}")
    private String jasyptType;

    /**
     * 生成连接密文串
     * @param req
     * @return
     */
    @PostMapping("/encrypts")
    public R<Map<String, String>> encrypt(@RequestBody StringEncryptorReq req) {
        log.info("加密前的数据:{}", JSON.toJSONString(req));
        if (StringUtils.isBlank(jasyptType)) {
            return R.error("没有配置加解密器类型");
        }

        if (Objects.nonNull(req) && StringUtils.isNotBlank(req.getMsg())) {
            Map<String, String> map = Maps.newHashMap();
            String encrypt = stringEncryptor.encrypt(req.getMsg());
            map.put("jasyptType",jasyptType);
            map.put("encrypt", encrypt);
            map.put("decrypt", stringEncryptor.decrypt(encrypt));
            return R.ok(map);
        }
        return R.ok();
    }

    /**
     * 解密密文串
     * @param req 请求参数，msg字段包含需要解密的密文
     * @return 解密结果
     */
    @PostMapping("/decrypts")
    public R<Map<String, String>> decrypt(@RequestBody StringEncryptorReq req) {
        log.info("解密前的数据:{}", JSON.toJSONString(req));
        if (StringUtils.isBlank(jasyptType)) {
            return R.error("没有配置加解密器类型");
        }

        if (Objects.nonNull(req) && StringUtils.isNotBlank(req.getMsg())) {
            try {
                Map<String, String> map = Maps.newHashMap();
                String decrypt = stringEncryptor.decrypt(req.getMsg());
                map.put("jasyptType", jasyptType);
                map.put("encrypt", req.getMsg());
                map.put("decrypt", decrypt);
                return R.ok(map);
            } catch (Exception e) {
                log.error("解密失败，可能是密文格式不正确: {}", e.getMessage());
                return R.error("解密失败，请检查密文格式是否正确");
            }
        }
        return R.error("请提供需要解密的密文");
    }

    /**
     * 测试动态变更日志级别
     */
    @GetMapping("/secrets")
    public R< Map<String, String>> secrets(@RequestParam("keyId") String keyId) {
        JasyptDTO appSecret = JasyptUtils.getAppSecret();
        Map<String, String> map = Maps.newHashMap();
        map.put("keyId", StringUtils.isNotBlank(keyId) ? keyId : appSecret.getKeyId());
        map.put("config_key", appSecret.getConfigKey());
        map.put("secret_key", appSecret.getSecretKey());
        return R.ok(map);
    }
}
