package com.suwell.plss.record.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.record.entity.ResourcePermissionLog;
import com.suwell.plss.record.service.PermissionService;
import com.suwell.plss.record.standard.dto.request.PermissionGroupAddReq;
import com.suwell.plss.record.standard.dto.request.PermissionGroupModifyReq;
import com.suwell.plss.record.standard.dto.request.PermissionOperateBatchReq;
import com.suwell.plss.record.standard.dto.request.PermissionOperateReq;
import com.suwell.plss.record.standard.dto.request.PermissionQueryReq;
import com.suwell.plss.record.standard.dto.request.PermissionRecordLogReq;
import com.suwell.plss.record.standard.dto.request.PermissionRecordsQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordPermReq;
import com.suwell.plss.record.standard.dto.request.ResourceManagerAddReq;
import com.suwell.plss.record.standard.dto.request.ResourceManagerQueryReq;
import com.suwell.plss.record.standard.dto.request.ResourceNatureChangeReq;
import com.suwell.plss.record.standard.dto.request.ResourcePermissionAddReq;
import com.suwell.plss.record.standard.dto.request.UserPermConditionReq;
import com.suwell.plss.record.standard.dto.response.PermissionBatchOperateResp;
import com.suwell.plss.record.standard.dto.response.PermissionDicResp;
import com.suwell.plss.record.standard.dto.response.PermissionGroupResp;
import com.suwell.plss.record.standard.dto.response.PermissionOperateResp;
import com.suwell.plss.record.standard.dto.response.PermissionRecordResp;
import com.suwell.plss.record.standard.dto.response.PermissionResp;
import com.suwell.plss.record.standard.dto.response.RecordPermissionResp;
import com.suwell.plss.record.standard.dto.response.ResourceManagerResp;
import com.suwell.plss.record.standard.dto.response.ResourcePermissionResp;
import com.suwell.plss.record.standard.dto.response.UserPermissionResp;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.service.StandardPermissionFacade;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 库门面实现
 */
@Slf4j
@Service
public class StandardPermissionFacadeImpl implements StandardPermissionFacade {

    @Resource
    private PermissionService permissionService;

    @Override
    public List<PermissionDicResp> listPermissionDic() {
        return permissionService.listPermissionDic();
    }

    @Override
    public void addPermissionGroup(List<PermissionGroupAddReq> list) {
        AssertUtils.notNull(list, RecordBizError.PERMISSION_GROUP_NOT_NULL);
        BeanValidators.defaultValidate(list);
        permissionService.addPermissionGroup(list);
    }

    @Override
    public void modifyPermissionGroup(PermissionGroupModifyReq req) {
        BeanValidators.defaultValidate(req);
        permissionService.modifyPermissionGroup(req);
    }

    @Override
    public void removePermissionGroup(List<Long> permissionGroupIdList) {
        AssertUtils.isTrue(CollectionUtil.isNotEmpty(permissionGroupIdList), RecordBizError.PERMISSION_ID_NOT_NULL);
        permissionService.removePermissionGroup(permissionGroupIdList);
    }

    @Override
    public List<PermissionGroupResp> listPermissionGroup() {
        return permissionService.listPermissionGroup();
    }

    @Override
    public void addResourcePermission(ResourcePermissionAddReq addReq) {
        BeanValidators.defaultValidate(addReq);
        permissionService.addResourcePermission(addReq);
    }

    @Override
    public void addResourceManagers(ResourceManagerAddReq addReq) {
        BeanValidators.defaultValidate(addReq);
        permissionService.addResourceManagers(addReq);
    }

    @Override
    public List<PermissionResp> listResourcePermission(PermissionQueryReq req) {
        BeanValidators.defaultValidate(req);
        return permissionService.listResourcePermission(req);
    }

    @Override
    public ResourcePermissionResp getResourcePermission(PermissionQueryReq req) {
        BeanValidators.defaultValidate(req);
        return permissionService.getResourcePermission(req);
    }

    @Override
    public List<ResourceManagerResp> listResourceManagers(ResourceManagerQueryReq req) {
        BeanValidators.defaultValidate(req);
        return permissionService.listResourceManagers(req);
    }

    @Override
    public void resourceNatureChange(ResourceNatureChangeReq req) {
        BeanValidators.defaultValidate(req);
        permissionService.resourceNatureChange(req);
    }

    @Override
    public List<PermissionOperateResp> listResourceOperate(PermissionOperateReq req) {
        BeanValidators.defaultValidate(req);
        return permissionService.listResourceOperate(req);
    }

    @Override
    public Map<Long, List<PermissionOperateResp>> listBatchResourceOperate(PermissionOperateBatchReq req) {
        List<PermissionOperateReq> reqList = req.getResourceIds().stream().map(resourceId -> {
            PermissionOperateReq operateReq = new PermissionOperateReq();
            operateReq.setResourceId(resourceId);
            operateReq.setResourceType(req.getResourceType());
            return operateReq;
        }).collect(Collectors.toList());
        return batchListResourceOperate(reqList).stream()
                .collect(Collectors.toMap(PermissionBatchOperateResp::getResourceId,
                        PermissionBatchOperateResp::getOperateList));
    }

    @Override
    public List<PermissionBatchOperateResp> batchListResourceOperate(List<PermissionOperateReq> reqList) {
        if (CollectionUtil.isEmpty(reqList)) {
            return Collections.emptyList();
        }
        reqList.forEach(BeanValidators::defaultValidate);
        return permissionService.batchListResourceOperate(reqList);
    }

    @Override
    public UserPermissionResp listUserResources() {
        return permissionService.listUserResources();
    }

    @Override
    public UserPermissionResp listUserResourcesWithCondition(UserPermConditionReq req) {
        return permissionService.listUserResourcesWithCondition(req);
    }

    @Override
    public PermissionRecordResp listPermRecord(PermissionRecordsQueryReq queryReq) {
        BeanValidators.defaultValidate(queryReq);
        return permissionService.listPermRecord(queryReq);
    }

    @Override
    public PageUtils<PermissionRecordResp> listPermRecords(PermissionRecordLogReq req) {
        PageUtils<ResourcePermissionLog> page = permissionService.listPermRecords(req);
        List<PermissionRecordResp> list = DozerUtils.convertListToNew(page.getList(), PermissionRecordResp.class);
        return new PageUtils<>(list, page.getTotalCount(), page.getPageSize(), page.getPage());
    }

    @Override
    public RecordPermissionResp getRecordPermissionInfo(RecordPermReq req) {
        BeanValidators.defaultValidate(req);
        return permissionService.getRecordPermissionInfo(req);
    }
}
