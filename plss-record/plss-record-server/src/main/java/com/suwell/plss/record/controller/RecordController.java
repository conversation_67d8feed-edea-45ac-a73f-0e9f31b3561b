package com.suwell.plss.record.controller;

import static com.suwell.plss.record.enums.ProjectFileSizeSceneTypeEnum.UPLOAD_DOCUMENT;
import static com.suwell.plss.record.standard.enums.EncryptFileTypeEnum.FILE_TYPE_INTERNALLY;
import static com.suwell.plss.record.standard.enums.EncryptFileTypeEnum.FILE_TYPE_PROJECT;
import static com.suwell.plss.record.standard.enums.RecordBizError.CATEGORY_TAG_BIND_ERROR;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_MAX_ID_LIMIT_ERROR;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER_TEXT;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.ServerBaseConfig;
import com.suwell.plss.framework.common.constant.SecurityConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.NumberUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.enums.FileStateEnum;
import com.suwell.plss.record.migration.impl.DataImportReviewServiceImpl;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.standard.dto.RecordConfirmImportDTO;
import com.suwell.plss.record.standard.dto.ReqRecongnitionDto;
import com.suwell.plss.record.standard.dto.request.FileInfoBaseV31Req;
import com.suwell.plss.record.standard.dto.request.FileInfoBaseV3Req;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineBaseReq;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineFrontReq;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineReq;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineV31Req;
import com.suwell.plss.record.standard.dto.request.FileInfoOfflineV3Req;
import com.suwell.plss.record.standard.dto.request.InternalImportReviewReq;
import com.suwell.plss.record.standard.dto.request.PersonLibUploadReq;
import com.suwell.plss.record.standard.dto.request.RecordDocumentCheckReq;
import com.suwell.plss.record.standard.dto.request.RecordEditReq;
import com.suwell.plss.record.standard.dto.request.RecordFixCoverReq;
import com.suwell.plss.record.standard.dto.request.RecordMakeTagReq;
import com.suwell.plss.record.standard.dto.request.RecordOfflineBaseReq;
import com.suwell.plss.record.standard.dto.request.RecordOfflineFrontReq;
import com.suwell.plss.record.standard.dto.request.RecordOfflineReq;
import com.suwell.plss.record.standard.dto.request.RecordOfflineV31Req;
import com.suwell.plss.record.standard.dto.request.RecordOfflineV3Req;
import com.suwell.plss.record.standard.dto.request.RecordOrderByReq;
import com.suwell.plss.record.standard.dto.request.RecordPermReq;
import com.suwell.plss.record.standard.dto.request.RecordQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordRelAddReq;
import com.suwell.plss.record.standard.dto.request.RecordRelQueryRequ;
import com.suwell.plss.record.standard.dto.request.RecordRelationAddReq;
import com.suwell.plss.record.standard.dto.request.RecordRelationEditReq;
import com.suwell.plss.record.standard.dto.request.RecordRenameReq;
import com.suwell.plss.record.standard.dto.request.RecordReviewPageReq;
import com.suwell.plss.record.standard.dto.request.RecordReviewSetResultReq;
import com.suwell.plss.record.standard.dto.request.RecordReviewVersionListReq;
import com.suwell.plss.record.standard.dto.request.RecordStorehouseBatchReq;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.FileNameDuplicateDetectionResp;
import com.suwell.plss.record.standard.dto.response.RecordDocumentMetadataResp;
import com.suwell.plss.record.standard.dto.response.RecordDocumentResp;
import com.suwell.plss.record.standard.dto.response.RecordInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordMakeTagResp;
import com.suwell.plss.record.standard.dto.response.RecordOfflineResp;
import com.suwell.plss.record.standard.dto.response.RecordOriginResp;
import com.suwell.plss.record.standard.dto.response.RecordPermissionResp;
import com.suwell.plss.record.standard.dto.response.RecordRelQueryResp;
import com.suwell.plss.record.standard.dto.response.RecordRelationInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordReviewPageResp;
import com.suwell.plss.record.standard.dto.response.RecordReviewVersionListResp;
import com.suwell.plss.record.standard.dto.response.RecordStatusResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.OriginEnum;
import com.suwell.plss.record.standard.service.StandardDocumentFacade;
import com.suwell.plss.record.standard.service.StandardMetadataFacade;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import com.suwell.plss.record.standard.service.StandardRecordRelFacade;
import com.suwell.plss.record.util.RecordCommonUtils;
import com.suwell.plss.system.api.domain.request.CategoryLinkAddReq;
import com.suwell.plss.system.api.service.CategoryRpcService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件表(文档合集)
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@Slf4j
@RestController
@RequestMapping("/v1/file")
public class RecordController {

    public static final String MODE_NAME = "'后台-文档入库'";
    @Resource
    private StandardRecordFacade recordFacade;
    @Resource
    private FileService fileService;
    @Resource
    private StandardDocumentFacade standardDocumentFacade;
    @Resource
    private StandardRecordRelFacade recordRelFacade;
    @Resource
    private StandardMetadataFacade standardMetadataFacade;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private RecordCommonUtils recordCommonUtils;
    @Resource
    private AsyncTaskExecutor asyncTaskExecutor;
    @Resource
    private DataImportReviewServiceImpl dataImportReviewService;

    @Resource
    private ServerBaseConfig serverBaseConfig;
    private final String PATH_PREFIX = "/swdata/builtin/";

    /**
     * 信息 OK
     */
    @PostMapping("/page")
    public R<PageUtils<RecordInfoResp>> page(@RequestBody RecordQueryReq req) {
        PageUtils<RecordInfoResp> fromDB = recordFacade.pageFromDB(req);
        return R.ok(fromDB);
    }

    /**
     * 信息 OK
     */
    @PostMapping("/info")
    public R<RecordDocumentMetadataResp> info(@RequestBody Long id, HttpServletRequest request) {
        ReqRecongnitionDto reqRecongnitionDto = new ReqRecongnitionDto();
        reqRecongnitionDto.setIp(request.getHeader(SecurityConstants.REAL_IP));
        reqRecongnitionDto.setUserAgent(request.getHeader(SecurityConstants.USERAGENT));

        return R.ok(recordFacade.queryById(id, reqRecongnitionDto));
    }

    /**
     * 查询沿革、依托 参考的关系
     */
    @PostMapping("/queryRelation")
    public R<RecordRelationInfoResp> queryRelation(@RequestBody Long recordId) {
        RecordRelationInfoResp recordRelationInfoResp = recordRelFacade.queryRelation(recordId);
        return R.ok(recordRelationInfoResp);
    }

    /**
     * 更新沿革、依托 参考的关系
     */
    @PostMapping("/updateRelation")
    public R<Void> updateRelation(@RequestBody RecordRelationAddReq recordRelationReq) {
        recordRelFacade.modifyRecordRelation(recordRelationReq);
        return R.ok();
    }

    /**
     * 前台文件上传
     *
     * @param req
     * @return
     */
    @Deprecated
    @PostMapping("/frontUpload")
    public R<RecordOfflineResp> frontUpload(@ModelAttribute RecordOfflineFrontReq req) {

        final List<FileInfoOfflineFrontReq> fileInfoReqList = req.getFileInfoReqList();
        List<FileInfoOfflineFrontReq> removeList = Lists.newArrayList();
        StringBuilder toast = new StringBuilder("上传文档中与已入库文档存在重复，已自动过滤重复文档");

        findRepeatFile(fileInfoReqList, removeList, FileInfoOfflineFrontReq::getFileMd5);
        fileInfoReqList.removeAll(removeList);
        if (fileInfoReqList.isEmpty()) {
            final Date nowDate = new Date();
            RecordOfflineResp resp = new RecordOfflineResp();

            resp.addDtos(removeList.stream().map(o -> RecordOfflineResp.FileDto.builder().fileName(o.getFileName())
                            .createTime(nowDate).state(FileStateEnum.FS_REPEAT.getCode()).build())
                    .collect(Collectors.toList()));

            return R.ok(resp, toast.toString());
        }
        req.setFileInfoReqList(fileInfoReqList);

        RecordOfflineV3Req recordOfflineV3Req = buildRecordOfflineFrontReq(req);
        log.info("frontUpload req:{}", recordOfflineV3Req);
        final RecordOfflineResp resp = recordFacade.addUploadV4(recordOfflineV3Req);
        if (!removeList.isEmpty()) {
            resp.addDtos(removeList
                    .stream().map(o -> RecordOfflineResp.FileDto.builder().fileName(o.getFileName())
                            .createTime(resp.getCreateTime()).state(FileStateEnum.FS_REPEAT.getCode()).build())
                    .collect(Collectors.toList()));
            return R.ok(resp, toast.toString());
        }
        return R.ok(resp);
    }

    /**
     * 文件上传请求标品改版【接口入库】
     */
    @PostMapping("/uploadV2")
    public R<Void> uploadV2(@ModelAttribute RecordOfflineReq recordOfflineReq) throws IOException {
        // 若填写recordId则校验文件id的合法性
        List<FileInfoOfflineReq> fileInfoList = recordOfflineReq.getFileInfoReqList();
        List<Long> recordIdList = fileInfoList.stream().map(FileInfoOfflineReq::getRecordId)
                .collect(Collectors.toList());
        AssertUtils.isTrue(NumberUtils.verifyRecordIdList(recordIdList), RECORD_MAX_ID_LIMIT_ERROR);
        log.info("接口对接上传 recordIdList:{}", com.alibaba.fastjson2.JSON.toJSONString(recordIdList));

        if (CollUtil.isNotEmpty(recordOfflineReq.getRepoPositionList())) {
            List<Long> repoPositionList = recordOfflineReq.getRepoPositionList();
            //repoPositionList 去除 空
            recordOfflineReq.setRepoPositionList(repoPositionList.stream()
                    .filter(Objects::nonNull).collect(Collectors.toList()));
        }
        /**
         * MD5相同的文件停止上传，并且将响应结果返回至接口 //todo 此处为暂时实现，实时对比库是否有重复文件，数据量过大接口压测时会有性能瓶颈，后续处理。 正确的方案应该为使用布隆过滤器实现。
         */
        List<FileInfoOfflineReq> removeList = Lists.newArrayList();
        StringBuilder toast = new StringBuilder("上传文档中与已入库文档存在重复，已自动过滤重复文档");

        if (serverBaseConfig.getMd5Switch()) {
            List<FileInfoOfflineReq> fileInfoReqList = recordOfflineReq.getFileInfoReqList();
            findRepeatFile(fileInfoReqList, removeList, o -> {
                try {
                    return o.getFileMd5();
                } catch (IOException e) {
                    return "";
                }
            });

            fileInfoReqList.removeAll(removeList);
            if (fileInfoReqList.isEmpty()) {
                return R.ok(null, toast.toString());
            }
            recordOfflineReq.setFileInfoReqList(fileInfoReqList);
        }
        // recordFacade.addUploadV2(recordOfflineReq);

        RecordOfflineV3Req recordOfflineV3Req = buildRecordOfflineV3Req(recordOfflineReq);
        log.info("addUploadV3 req:{}", recordOfflineV3Req);
        recordFacade.addUploadV3(recordOfflineV3Req);
        if (!removeList.isEmpty()) {
            return R.ok(null, toast.toString());
        }
        return R.ok();
    }

    /**
     * 根据文件md5筛选出重复文件
     *
     * @param fileInfoReqList
     * @param removeList
     */
    private <T> void findRepeatFile(List<T> fileInfoReqList, List<T> removeList, Function<T, String> function) {
        // 按照md5分组
        final Map<String, List<T>> listMap = fileInfoReqList.stream().collect(Collectors.groupingBy(o -> {
            try {
                return function.apply(o);
            } catch (Exception e) {
                log.error("file={},计算文件md5异常！", o, e);
                return "";
            }
        }));

        // 删掉重复的
        final Map<RecordDocumentCheckReq, List<T>> collectMap = listMap.entrySet().stream().filter(e -> {
            if (e.getValue().size() == 1) {
                return true;
            } else {
                // 产品规定，重复文件全部不上传
                removeList.addAll(e.getValue());
                return false;
            }
        }).collect(Collectors.toMap(e -> {
            RecordDocumentCheckReq recordDocumentCheckReq = new RecordDocumentCheckReq();
            recordDocumentCheckReq.setFile_md5(e.getKey());
            return recordDocumentCheckReq;
        }, Map.Entry::getValue));

        collectMap.forEach((key, value) -> {
            boolean exist = standardDocumentFacade.checkedDocumentUploaded(key);
            if (exist) {
                removeList.addAll(value);
            }
        });
    }

    /**
     * 文件上传请求标品改版v3
     *
     * @param recordOfflineReq
     * @return
     * @throws IOException
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT, remark = "#recordOfflineReq.getRemark()")
    @PostMapping("/uploadV3")
    public R<RecordOfflineResp> uploadV3(@ModelAttribute RecordOfflineReq recordOfflineReq) throws IOException {
        log.info("后台文件的上传 req:{}", recordOfflineReq);

        // 数据安检 文件大小
        recordCommonUtils.checkFileSize(recordOfflineReq.getFileTotalSize(), UPLOAD_DOCUMENT.getCode());

        if (CollUtil.isNotEmpty(recordOfflineReq.getRepoPositionList())) {
            List<Long> repoPositionList = recordOfflineReq.getRepoPositionList();
            //repoPositionList 去除 空
            recordOfflineReq.setRepoPositionList(repoPositionList.stream()
                    .filter(Objects::nonNull).collect(Collectors.toList()));
        }

        StringBuilder toast = new StringBuilder("上传文档中与已入库文档存在重复，已自动过滤重复文档");
        List<FileInfoOfflineReq> removeList = Lists.newArrayList();
        if (serverBaseConfig.getMd5Switch()) {
            List<FileInfoOfflineReq> fileInfoReqList = recordOfflineReq.getFileInfoReqList();

            findRepeatFile(fileInfoReqList, removeList, o -> {
                try {
                    return o.getFileMd5();
                } catch (IOException e) {
                    return "";
                }
            });
            fileInfoReqList.removeAll(removeList);
            if (fileInfoReqList.isEmpty()) {
                final Date nowDate = new Date();
                RecordOfflineResp resp = new RecordOfflineResp();

                resp.addDtos(removeList.stream()
                        .map(o -> RecordOfflineResp.FileDto.builder().fileName(o.getFile().getOriginalFilename())
                                .createTime(nowDate).state(FileStateEnum.FS_REPEAT.getCode()).build())
                        .collect(Collectors.toList()));

                return R.ok(resp, toast.toString());
            }
            recordOfflineReq.setFileInfoReqList(fileInfoReqList);
        }
        // recordFacade.addUploadV2(recordOfflineReq);

        RecordOfflineV3Req recordOfflineV3Req = buildRecordOfflineV3Req(recordOfflineReq);
        log.info("addUploadV3 req:{}", recordOfflineV3Req);
        final RecordOfflineResp resp = recordFacade.addUploadV4(recordOfflineV3Req);
        if (!removeList.isEmpty()) {
            resp.addDtos(removeList.stream()
                    .map(o -> RecordOfflineResp.FileDto.builder().fileName(o.getFile().getOriginalFilename())
                            .createTime(resp.getCreateTime()).state(FileStateEnum.FS_REPEAT.getCode()).build())
                    .collect(Collectors.toList()));
            return R.ok(resp, toast.toString());
        }
        return R.ok(resp);

    }

    private RecordOfflineV3Req buildRecordOfflineV3Req(RecordOfflineReq recordOfflineReq) {
        List<FileInfoOfflineReq> fileInfoReqListNew = recordOfflineReq.getFileInfoReqList();
        List<FileInfoOfflineV3Req> fileInfoOfflineV3ReqList = Lists.newArrayList();
        // 文件密级校验及文件传参数量校验
        recordCommonUtils.checkClassified(recordOfflineReq.getRepoPositionList(), fileInfoReqListNew);
        for (FileInfoOfflineReq fileInfoOfflineReq : fileInfoReqListNew) {
//            recordCommonUtils.checkFileType(fileInfoOfflineReq.getFile(), UPLOAD_DOCUMENT.getCode());
            recordCommonUtils.checkFileType(fileInfoOfflineReq.getFile(), recordOfflineReq.getRecordTypeId());
            FileInfoOfflineV3Req fileInfoOfflineV3Req = new FileInfoOfflineV3Req();
            fileInfoOfflineV3Req.setRecordId(fileInfoOfflineReq.getRecordId());
            // 主件
            List<FileInfoBaseV3Req> fileInfoBaseV3ReqList = Lists.newArrayList();
            fileInfoBaseV3ReqList.add(
                    buildFileInfoBaseV3Req(fileInfoOfflineReq.getFile(),
                            fileInfoOfflineReq.getFile().getOriginalFilename(),
                            RECORD_DOCUMENT_TYPE_MASTER.getCode(), recordOfflineReq.getOrigin(),
                            fileInfoOfflineReq.getMainClassified()));

            extracted(recordOfflineReq, fileInfoOfflineV3ReqList, fileInfoOfflineReq, fileInfoOfflineV3Req,
                    fileInfoBaseV3ReqList);
        }
        recordCommonUtils.remove();
        RecordOfflineV3Req recordOfflineV3Req = new RecordOfflineV3Req();
        recordOfflineV3Req.setRecordTypeId(recordOfflineReq.getRecordTypeId());
        recordOfflineV3Req.setPlanId(recordOfflineReq.getPlanId());
        recordOfflineV3Req.setFileInfoReqList(fileInfoOfflineV3ReqList);
        recordOfflineV3Req.setOrigin(recordOfflineReq.getOrigin());
        recordOfflineV3Req.setDataType(recordOfflineReq.getDataType());
        recordOfflineV3Req.setStoreWay(recordOfflineReq.getStoreWay());
        recordOfflineV3Req.setRealOrigin(recordOfflineReq.getRealOrigin());
        return recordOfflineV3Req;
    }

    private void extracted(RecordOfflineBaseReq recordOfflineReq, List<FileInfoOfflineV3Req> fileInfoOfflineV3ReqList,
            FileInfoOfflineBaseReq fileInfoOfflineReq, FileInfoOfflineV3Req fileInfoOfflineV3Req,
            List<FileInfoBaseV3Req> fileInfoBaseV3ReqList) {
        // 附件
        MultipartFile[] attFileArr = fileInfoOfflineReq.getAttFile();
        List<Integer> attFileTypeList = fileInfoOfflineReq.getAttFileTypeList();
        List<Integer> classifiedList = fileInfoOfflineReq.getClassifiedList();
        if (Objects.nonNull(attFileArr) && attFileArr.length > 0) {
            final int attFileTypeSize = Optional.ofNullable(attFileTypeList).map(List::size).orElse(0);
            final int classifiedSize = Optional.ofNullable(classifiedList).map(List::size).orElse(0);
            final int attFileLen = attFileArr.length;

            if (attFileTypeSize == 0) {
                attFileTypeList = IntStream.range(0, attFileLen)
                        .map(i -> DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode()).boxed().collect(Collectors.toList());
            }
            if (classifiedSize == 0) {
                classifiedList = IntStream.range(0, attFileLen).mapToObj(i -> (Integer) null)
                        .collect(Collectors.toList());
            }

            // 一一对应
            for (int i = 0; i < attFileLen; i++) {
                final FileInfoBaseV3Req fileInfoBaseV3Req =
                        buildFileInfoBaseV3Req(attFileArr[i], attFileArr[i].getOriginalFilename(),
                                RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(), recordOfflineReq.getOrigin(),
                                classifiedList.get(i));
                fileInfoBaseV3Req.setAttachmentType(attFileTypeList.get(i));
                fileInfoBaseV3ReqList.add(fileInfoBaseV3Req);
            }
        }

        // 正文
        MultipartFile contentFile = fileInfoOfflineReq.getContentFile();
        if (Objects.nonNull(contentFile)) {
            fileInfoBaseV3ReqList.add(this.buildFileInfoBaseV3Req(contentFile, contentFile.getOriginalFilename(),
                    RECORD_DOCUMENT_TYPE_MASTER_TEXT.getCode(), recordOfflineReq.getOrigin(), null));
        }

        // 缩略图
        final MultipartFile pngFile = fileInfoOfflineReq.getPngFile();
        if (Objects.nonNull(pngFile)) {
            fileInfoBaseV3ReqList.add(this.buildFileInfoBaseV3Req(pngFile, pngFile.getOriginalFilename(),
                    RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode(), recordOfflineReq.getOrigin(), null));
        }
        // 文件信息
        fileInfoOfflineV3Req.setFileInfoBaseReqList(fileInfoBaseV3ReqList);
        fileInfoOfflineV3Req.setMetadata(StringUtils.isNotBlank(fileInfoOfflineReq.getMetadata())
                ? fileInfoOfflineReq.getMetadata() : StringPool.EMPTY);
        fileInfoOfflineV3Req.setRepoIdList(recordOfflineReq.getRepoPositionList());
        fileInfoOfflineV3Req.setSummary(fileInfoOfflineReq.getSummary());
        fileInfoOfflineV3ReqList.add(fileInfoOfflineV3Req);
    }

    private RecordOfflineV3Req buildRecordOfflineFrontReq(RecordOfflineFrontReq recordOfflineReq) {
        final List<FileInfoOfflineFrontReq> fileInfoReqListNew = recordOfflineReq.getFileInfoReqList();
        List<FileInfoOfflineV3Req> fileInfoOfflineV3ReqList = Lists.newArrayList();
        for (FileInfoOfflineFrontReq fileInfoOfflineReq : fileInfoReqListNew) {
            // checkFileType(fileInfoOfflineReq.getFile());
            FileInfoOfflineV3Req fileInfoOfflineV3Req = new FileInfoOfflineV3Req();
            // 主件
            List<FileInfoBaseV3Req> fileInfoBaseV3ReqList = Lists.newArrayList();

            FileInfoBaseV3Req mainReq = new FileInfoBaseV3Req();
            mainReq.setFileName(fileInfoOfflineReq.getFileName());
            mainReq.setFileId(fileInfoOfflineReq.getFileId());
            mainReq.setDocId(fileInfoOfflineReq.getDocId());
            mainReq.setCtype(RECORD_DOCUMENT_TYPE_MASTER.getCode());

            fileInfoBaseV3ReqList.add(mainReq);
            extracted(recordOfflineReq, fileInfoOfflineV3ReqList, fileInfoOfflineReq, fileInfoOfflineV3Req,
                    fileInfoBaseV3ReqList);
        }
        RecordOfflineV3Req recordOfflineV3Req = new RecordOfflineV3Req();
        recordOfflineV3Req.setRecordTypeId(recordOfflineReq.getRecordTypeId());
        recordOfflineV3Req.setPlanId(recordOfflineReq.getPlanId());
        recordOfflineV3Req.setFileInfoReqList(fileInfoOfflineV3ReqList);
        recordOfflineV3Req.setOrigin(recordOfflineReq.getOrigin());
        recordOfflineV3Req.setDataType(recordOfflineReq.getDataType());
        recordOfflineV3Req.setStoreWay(recordOfflineReq.getStoreWay());
        return recordOfflineV3Req;
    }

    private FileInfoBaseV3Req buildFileInfoBaseV3Req(MultipartFile file, String fileName, Integer ctype, Integer origin,
            Integer classified) {
        if (Objects.nonNull(file)) {
            FileInfoBaseV3Req fileInfoBaseV3Req = new FileInfoBaseV3Req();
            fileInfoBaseV3Req.setFileName(fileName);
            UploadReq uploadReqCnt = new UploadReq();
            uploadReqCnt.setFileData(file);
            uploadReqCnt.setOriginName(fileInfoBaseV3Req.getFileName());
            uploadReqCnt.setRequireEncrypt(true);
            uploadReqCnt.setFileType(OriginEnum.ORIGIN_INLAY_DATA.getCode().equals(origin)
                    ? FILE_TYPE_INTERNALLY.getCode() : FILE_TYPE_PROJECT.getCode());
            UploadResp uploadResp = fileService.generalUpload(uploadReqCnt);
            fileInfoBaseV3Req.setFileId(uploadResp.getId());
            fileInfoBaseV3Req.setCtype(ctype);
            fileInfoBaseV3Req.setClassified(classified);
            return fileInfoBaseV3Req;
        }
        return null;
    }

    /**
     * 数据迁移上传
     */
    @PostMapping("/transferUpload")
    public R<Void> transferUpload(@ModelAttribute RecordOfflineV31Req req) {
        log.info("transferUpload req:{}", req);

        // 若填写recordId则校验文件id的合法性
        List<FileInfoOfflineV31Req> fileInfoReqList = req.getFileInfoReqList();
        List<Long> recordIdList = fileInfoReqList.stream().map(FileInfoOfflineV31Req::getRecordId)
                .collect(Collectors.toList());
        AssertUtils.isTrue(NumberUtils.verifyRecordIdList(recordIdList), RECORD_MAX_ID_LIMIT_ERROR);

        RecordOfflineV3Req recordOfflineV3Req = buildRecordOfflineV3Req(req);
        log.info("transferUpload req:{}", recordOfflineV3Req);
        recordFacade.addUploadV3(recordOfflineV3Req);
        return R.ok();
    }

    private RecordOfflineV3Req buildRecordOfflineV3Req(RecordOfflineV31Req req) {
        List<FileInfoOfflineV3Req> fileInfoOfflineV3ReqList = Lists.newArrayList();
        String userId = SecurityUtils.getUserId();
        List<FileInfoOfflineV31Req> fileInfoOfflineV31ReqList = req.getFileInfoReqList();
        for (FileInfoOfflineV31Req viewFile : fileInfoOfflineV31ReqList) {
            FileInfoOfflineV3Req fileInfoOfflineV3Req = new FileInfoOfflineV3Req();
            fileInfoOfflineV3Req.setRecordId(viewFile.getRecordId());
            fileInfoOfflineV3Req.setMetadata(viewFile.getMetadata());
            fileInfoOfflineV3Req.setRepoIdList(viewFile.getRepoIdList());
            fileInfoOfflineV3Req.setSummary(viewFile.getSummary());

            // 获取分类的叶子节点id
            CategoryLinkAddReq categoryLinkAddReq = new CategoryLinkAddReq();
            categoryLinkAddReq.setUserId(userId);
            categoryLinkAddReq.setNameLinkList(viewFile.getCategoryNameList());
            R<List<Long>> r = categoryRpcService.queryOrAddLink(categoryLinkAddReq);
            AssertUtils.isTrue(r.isSuccess(), CATEGORY_TAG_BIND_ERROR);
            fileInfoOfflineV3Req.setCategoryIdList(r.getData());

            List<FileInfoBaseV3Req> fileInfoBaseV3ReqList = Lists.newArrayList();
            List<FileInfoBaseV31Req> fileInfoBaseReqList = viewFile.getFileInfoBaseReqList();
            for (FileInfoBaseV31Req fileInfoBaseV31Req : fileInfoBaseReqList) {
                if (RECORD_DOCUMENT_TYPE_MASTER.getCode().equals(fileInfoBaseV31Req.getCtype())) {
                    recordCommonUtils.checkFileType(fileInfoBaseV31Req.getFile(), UPLOAD_DOCUMENT.getCode());
                }
                fileInfoBaseV3ReqList.add(buildFileInfoBaseV3Req(fileInfoBaseV31Req.getFile(),
                        fileInfoBaseV31Req.getFileName(), fileInfoBaseV31Req.getCtype(), req.getOrigin(),
                        req.getClassified()));
            }
            fileInfoOfflineV3Req.setFileInfoBaseReqList(fileInfoBaseV3ReqList);

            // 收集文件信息
            fileInfoOfflineV3ReqList.add(fileInfoOfflineV3Req);
        }

        RecordOfflineV3Req recordOfflineV3Req = new RecordOfflineV3Req();
        recordOfflineV3Req.setRecordTypeId(req.getRecordTypeId());
        recordOfflineV3Req.setPlanId(req.getPlanId());
        recordOfflineV3Req.setFileInfoReqList(fileInfoOfflineV3ReqList);
        recordOfflineV3Req.setOrigin(req.getOrigin());
        recordOfflineV3Req.setDataType(req.getDataType());
        return recordOfflineV3Req;
    }

    /**
     * 文件上传请求标品改版
     */
    @PostMapping("/personLibUpload")
    public R<Void> personLibUpload(@ModelAttribute PersonLibUploadReq req) {
        recordFacade.addPersonLibUpload(req);
        return R.ok();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RecordEditReq recordEditReq) {
        recordFacade.modifyById(recordEditReq);
        return R.ok();
    }


    /**
     * 批量文件入库操作
     */
    @PostMapping("/storehouseBatch")
    public R<Void> storehouseBatch(@RequestBody RecordStorehouseBatchReq recordStorehouseBatchReq) {
        recordFacade.addStorehouseBatch(recordStorehouseBatchReq);
        return R.ok();
    }

    /**
     * 删除 OK
     */
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody Long[] ids) {
        recordFacade.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 标品文件详情
     *
     * @param id
     * @return
     */
    @GetMapping("/recordinfo/{id}")
    public R<RecordDocumentResp> docInfo(@PathVariable("id") Long id, HttpServletRequest request) {

        ReqRecongnitionDto reqRecongnitionDto = new ReqRecongnitionDto();
        reqRecongnitionDto.setIp(request.getHeader(SecurityConstants.REAL_IP));
        reqRecongnitionDto.setUserAgent(request.getHeader(SecurityConstants.USERAGENT));

        return R.ok(recordFacade.queryRecordInfoById(id, reqRecongnitionDto));
    }


    /**
     * 添加 沿革、依托、参考的关系
     *
     * @param req
     */
    @PostMapping("/addRecordRel")
    public R<RecordRelationInfoResp> addRecordRel(@RequestBody RecordRelationEditReq req) {
        recordRelFacade.addRecordRel(req);
        return R.ok();
    }

    /**
     * 更新沿革
     *
     * @param req
     */
    @PostMapping("/updateRecordRel")
    public R<Void> updateRecordRel(@RequestBody RecordRelAddReq req) {
        recordRelFacade.updateRecordRel(req);
        return R.ok();
    }

    /**
     * 删除沿革、依托 参考的关系
     */
    @PostMapping("/removeRecordRel")
    public R<RecordRelationInfoResp> removeRecordRel(@RequestBody RecordRelationEditReq req) {
        recordRelFacade.removeRecordRel(req);
        return R.ok();
    }

    /**
     * 查询沿革、依托 参考的关系
     *
     * @return
     */
    @PostMapping("/record_rel")
    public R<Map<Long, Map<String, List<RecordRelQueryResp>>>>
    queryRecordRel(@RequestBody @Validated RecordRelQueryRequ req) {
        return R.ok(recordRelFacade.queryRecordRel(req));
    }

    /**
     * @param metadataName 元数据项的名称
     * @param metadataValue 元数据项的值
     */
    @Deprecated
    @PostMapping("/queryByRecordIds")
    public R<List<Long>> queryByRecordIds(@RequestBody String metadataName,
            @RequestParam("metadataValue") String metadataValue) {
        List<Long> list = standardMetadataFacade.queryByRecordIds(metadataName, metadataValue);
        return R.ok(list);
    }

    /**
     * 分页列表
     */
    @PostMapping("/originList")
    public R<List<RecordOriginResp>> queryOriginList() {
        return R.ok(recordFacade.queryOriginList());
    }

    /**
     * 查询文件的状态配置
     */
    @PostMapping("/queryRecordStatusList")
    public R<List<RecordStatusResp>> queryRecordStatusList() {
        return R.ok(recordFacade.queryRecordStatusList());
    }

    /**
     * 查询文件打的标签
     */
    @PostMapping("/queryMakeTag/{recordId}")
    public R<List<RecordMakeTagResp>> queryMakeTag(@PathVariable Long recordId) {
        return R.ok(recordFacade.queryMakeTag(recordId));
    }

    /**
     * 文件打标签
     */
    @PostMapping("/recordMakeTag")
    public R<Void> recordMakeTag(@RequestBody RecordMakeTagReq req) {
        recordFacade.modifyRecordMakeTag(req);
        return R.ok();
    }

    /**
     * 同步全量文件至es中
     */
    @PostMapping("/integralRecordPush")
    public R<Void> integralRecordPush() {
        // TODO 全量发送文件入es的数据
        return R.ok();
    }

    @PostMapping("/getRecordPermissionInfo")
    public R<RecordPermissionResp> getRecordPermissionInfo(@RequestBody RecordPermReq req) {
        RecordPermissionResp resp = recordFacade.getRecordPermissionInfo(req);
        return R.ok(resp);
    }

    @PostMapping("/masterOfdJudge")
    public R<Boolean> masterOfdJudge(@RequestBody Long recordId) {
        boolean masterOfdJudge = recordFacade.masterOfdJudge(recordId);
        return R.ok(masterOfdJudge);
    }

    @PostMapping("/updateCover")
    public R<Void> updateCover(@RequestBody RecordFixCoverReq req) {
        recordFacade.updateCover(req);
        return R.ok();
    }

    /**
     * 文件名重复检测
     *
     * @param fileName
     * @return
     */
    @PostMapping("/fileNameDuplicateDetection")
    public R<FileNameDuplicateDetectionResp> fileNameDuplicateDetection(@RequestBody String fileName) {
        return R.ok(recordRelFacade.fileNameDuplicateDetection(fileName));
    }


    /**
     * 文件名重复检测
     * 导出未被文档引用的文档类型和元数据
     *
     * @return
     */
    @GetMapping("/exportNotUsedRecordTypeAndMetadata")
    public void exportNotUsedRecordTypeAndMetadata(HttpServletResponse response) {
        recordFacade.exportNotUsedRecordTypeAndMetadata(response);
    }

    @PostMapping("/confirmImport")
    public R<Void> confirmImport(@RequestBody @Valid List<RecordConfirmImportDTO> dtoList) {
        recordFacade.confirmImport(dtoList);
        return R.ok();
    }


    /**
     * 获取文件名称或标题，标题不为空时返回标题，标题为空时返回文件名称
     *
     * @param recordId 文档id
     * @return
     */
    @PostMapping("/queryTitle")
    public R<String> queryTitle(@RequestParam(name = "id", required = false) Long id,
            @RequestParam("recordId") Long recordId) {
        return R.ok(recordRelFacade.queryTitle(id, recordId));
    }

    /**
     * 旧表迁移数据到sharding
     */
    @PostMapping("/migrateRecord2Sharding")
    public R<Void> migrateRecord2Sharding() {
        recordFacade.migrateRecord2Sharding();
        return R.ok();
    }

    /**
     * 保存文档的排序字段
     *
     * @param recordOrderByReq 文档排序参数
     */
    @PostMapping("/saveOrderBy")
    public R<Void> saveOrderBy(@RequestBody RecordOrderByReq recordOrderByReq) {
        recordFacade.saveOrderBy(recordOrderByReq);
        return R.ok();
    }

    /**
     * 主文件名称重命名
     *
     * @param req
     * @return
     */
    @PostMapping("/rename")
    public R<Void> rename(@RequestBody @Validated RecordRenameReq req) {
        recordFacade.rename(req);
        return R.ok();
    }

    @PostMapping("/internalImportReview")
    public R<Void> internalImportReview(@RequestBody InternalImportReviewReq req) {
        asyncTaskExecutor.submit(() -> {
            dataImportReviewService.importDataReview(PATH_PREFIX, req);
        });
        return R.ok();
    }

    @PostMapping("/recordReviewQueryPage")
    public R<RecordReviewPageResp> recordReviewQueryPage(@RequestBody RecordReviewPageReq req) {
        RecordReviewPageResp resp = dataImportReviewService.recordReviewQueryPage(req);
        return R.ok(resp);
    }

    @PostMapping("/getRecordReviewVersionList")
    public R<RecordReviewVersionListResp> getRecordReviewVersionList(@RequestBody RecordReviewVersionListReq req) {
        RecordReviewVersionListResp resp = dataImportReviewService.getRecordReviewVersionList(req);
        return R.ok(resp);
    }

    @PostMapping("/setImportRecordReviewResult")
    public R<Void> setImportRecordReviewResult(@RequestBody RecordReviewSetResultReq req) {
        dataImportReviewService.setReviewResult(req);
        return R.ok();
    }

    @GetMapping("/getImportRecordReviewFileURL")
    public R<Object> getImportRecordReviewFileURL(@RequestParam String fileinx) {
        String downloadURL = dataImportReviewService.getReviewRecordDownloadURL(fileinx);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", downloadURL);
        return R.ok(jsonObject);
    }

}
