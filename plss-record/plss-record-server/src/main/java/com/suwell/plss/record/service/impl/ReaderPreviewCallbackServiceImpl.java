package com.suwell.plss.record.service.impl;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.record.domain.PermissionDTO;
import com.suwell.plss.record.domain.ReaderInfoDTO;
import com.suwell.plss.record.domain.TuoMinDTO;
import com.suwell.plss.record.domain.WatermarkDTO;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.ReaderPreview;
import com.suwell.plss.record.enums.PreviewFileSourceEnum;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.ReaderCallbackService;
import com.suwell.plss.record.service.ReaderPreviewService;
import com.suwell.plss.record.standard.dto.request.ReaderCallbackReq;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/***
 *
 *<AUTHOR>
 *@date 2024/3/7 20:26
 *@version 1.0.0
 */
@Service("readerPreviewCallback")
public class ReaderPreviewCallbackServiceImpl implements ReaderCallbackService {

    @Resource
    private ReaderPreviewService readerPreviewService;

    @Resource
    private DocumentCallbackServiceImpl documentCallbackServiceImpl;

    @Resource
    private DocumentService documentService;

    @Override
    public ReaderInfoDTO info(HttpServletRequest request, ReaderCallbackReq req) {
        ReaderPreview readerPreview = readerPreviewService.getById(req.getId());
        if (readerPreview != null) {
            LocalDateTime expireDate = readerPreview.getExpireDate();
            //永久有效  或者 未过期
            if (expireDate == null || expireDate.isAfter(LocalDateTime.now())) {
                if (PreviewFileSourceEnum.INNER_FILE.getCode() == readerPreview.getFsource()) {
                    Long recordId = Long.valueOf(readerPreview.getFdata());
                    Document document = documentService.getMasterDocByRecordId(recordId);
                    if (document != null) {
                        req.setId(document.getId());
                        req.setRecordId(recordId);
                    }
                    return documentCallbackServiceImpl.info(request, req);
                }
                ReaderInfoDTO readerInfoDTO = new ReaderInfoDTO();
                ReaderInfoDTO.ReaderInfoDetailDTO readerInfoDetailDTO = new ReaderInfoDTO.ReaderInfoDetailDTO();
                readerInfoDetailDTO.setId(readerPreview.getId().toString());
                readerInfoDetailDTO.setVersion(1);
                readerInfoDetailDTO.setDownload_url(readerPreview.getFdata());
                readerInfoDetailDTO.setName(readerPreview.getFname());
                readerInfoDetailDTO.setSize(readerPreview.getFsize());
                readerInfoDetailDTO.setPermission(JSON.parseObject(readerPreview.getFperm(), PermissionDTO.class));
                readerInfoDetailDTO.setWatermark(JSON.parseObject(readerPreview.getFmark(), WatermarkDTO.class));
                readerInfoDTO.setFile(readerInfoDetailDTO);
                TuoMinDTO tuoMinDTO = new TuoMinDTO();
                tuoMinDTO.setEnable(false);
                readerInfoDTO.setDesensitization(tuoMinDTO);
                return readerInfoDTO;
            }
        }
        return null;
    }
}
