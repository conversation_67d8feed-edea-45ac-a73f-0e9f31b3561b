package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.domain.RcSecurityPolicyAddDto;
import com.suwell.plss.record.entity.RcSecurityPolicy;
import com.suwell.plss.record.entity.RcWatermarkSetting;
import com.suwell.plss.record.service.RcSecurityPolicyService;
import com.suwell.plss.record.service.RcWatermarkSettingService;
import com.suwell.plss.record.standard.dto.request.RcSecurityPolicyQueryReq;
import com.suwell.plss.record.standard.dto.request.SensitiveCategoryQueryReq;
import com.suwell.plss.record.standard.dto.response.SensitiveCategoryResp;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName RcSecurityPolicyController
 * <AUTHOR>
 * @Date 2023-11-2 14:40
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/v1/securityPolicy")
public class RcSecurityPolicyController {
    @Resource
    private RcSecurityPolicyService rcSecurityPolicyService;
    @Resource
    private RcWatermarkSettingService rcWatermarkSettingService;

//    /**
//     * 查询安全策略
//     */
//    @PostMapping("/securityPolicyList")
//    public R<List<RcSecurityPolicy>> securityPolicyList(@RequestBody RcSecurityPolicyQueryReq rcSecurityPolicyQueryReq) {
//        List<RcSecurityPolicy> list = rcSecurityPolicyService.queryRcSecurityPolicys(rcSecurityPolicyQueryReq.getRoleId(),rcSecurityPolicyQueryReq.getDimension(),rcSecurityPolicyQueryReq.getObjectId());
//        return R.ok(list);
//    }
    @PostMapping("/page")
    public R<PageUtils<RcSecurityPolicy>> pageList(@RequestBody RcSecurityPolicyQueryReq req) {
        return R.ok(rcSecurityPolicyService.pageList(req));
    }
    /**
     * 新增安全策略水印
     */
    @PostMapping("/addPolicyWater")
    public R<RcSecurityPolicy> addPolicyWater(@RequestBody RcSecurityPolicyAddDto securityPolicy) {
        RcSecurityPolicy rcSecurityPolicy = rcSecurityPolicyService.addRcSecurityPolicyWatermark(securityPolicy);
        return R.ok(rcSecurityPolicy);
    }

    /**
     * 删除安全策略水印
     */
    @PostMapping("/removePolicyWater")
    public R removePolicyWater(@RequestBody Long securityPolicyId) {
        rcSecurityPolicyService.removeRcSecurityPolicyWatermark(securityPolicyId);
        return R.ok();
    }

    /**
     * 安全策略水印详情
     */
    @PostMapping("/policyWaterInfo")
    public R<RcWatermarkSetting> policyWaterInfo(@RequestBody Long policyWaterId) {
        RcWatermarkSetting rcWatermarkSetting = rcWatermarkSettingService.getById(policyWaterId);
        return R.ok(rcWatermarkSetting);
    }


}
