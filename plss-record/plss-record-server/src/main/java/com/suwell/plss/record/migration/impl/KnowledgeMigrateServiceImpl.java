package com.suwell.plss.record.migration.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.suwell.plss.framework.common.utils.uuid.IdUtils;
import com.suwell.plss.record.entity.RecordKnowledge;
import com.suwell.plss.record.entity.RecordKnowledgeAttribute;
import com.suwell.plss.record.entity.RecordKnowledgeConflict;
import com.suwell.plss.record.entity.RecordKnowledgeRelation;
import com.suwell.plss.record.migration.ExportConstant;
import com.suwell.plss.record.migration.CommonUtil;
import com.suwell.plss.record.migration.KnowledgeMigrateService;
import com.suwell.plss.record.service.RecordKnowledgeAttributeService;
import com.suwell.plss.record.service.RecordKnowledgeConflictService;
import com.suwell.plss.record.service.RecordKnowledgeRelationService;
import com.suwell.plss.record.service.RecordKnowledgeService;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

@Service
public class KnowledgeMigrateServiceImpl implements KnowledgeMigrateService {
    @Resource
    private RecordKnowledgeService recordKnowledgeService;
    @Resource
    private RecordKnowledgeAttributeService recordKnowledgeAttributeService;
    @Resource
    private RecordKnowledgeRelationService recordKnowledgeRelationService;
    @Resource
    private RecordKnowledgeConflictService recordKnowledgeConflictService;

    /*
     * export
     * rc_record_knowledge
     * rc_record_knowledge_relation
     * rc_record_knowledge_attribute
     * rc_record_knowledge_conflict
     */
    @Override
    public void exportKnowledge(List<Long> listRecordId, String savePath) {
        List<RecordKnowledge> listKnowledge = recordKnowledgeService.list(
                new LambdaQueryWrapper<RecordKnowledge>().in(RecordKnowledge::getRecordId, listRecordId));

        if (listKnowledge.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_KNOWLEDGE;
        CommonUtil.seriallizeList(listKnowledge, filepath);

        this.exportKnowledgeRelation(listRecordId, savePath);
    }
    private void exportKnowledgeRelation(List<Long> listRecordId, String savePath) {
        List<RecordKnowledgeRelation> listKnowledgeRelation = recordKnowledgeRelationService
                .list(new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .in(RecordKnowledgeRelation::getRecordId, listRecordId));

        if (listKnowledgeRelation.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_KNOWLEDGE_RELATION;
        CommonUtil.seriallizeList(listKnowledgeRelation, filepath);

        this.exportKnowledgeAttribute(listKnowledgeRelation, savePath);
    }
    private void exportKnowledgeAttribute(List<RecordKnowledgeRelation> listRecordKnowledgeRelation, String savePath) {
        Set<Long> setNodeId = new HashSet<>();

        listRecordKnowledgeRelation.stream()
                .flatMap(record -> Stream.of(record.getFromNodeId(), record.getToNodeId(), record.getRelationNodeId()))
                .forEach(setNodeId::add);

        List<RecordKnowledgeAttribute> listKnowledgeAttribute = recordKnowledgeAttributeService
                .list(new LambdaQueryWrapper<RecordKnowledgeAttribute>().in(!setNodeId.isEmpty(),
                        RecordKnowledgeAttribute::getNodeId, setNodeId));

        if (listKnowledgeAttribute.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_KNOWLEDGE_ATTRIBUTE;
        CommonUtil.seriallizeList(listKnowledgeAttribute, filepath);

        this.exportKnowledgeConflict(setNodeId, savePath);
    }
    private void exportKnowledgeConflict(Set<Long> setNodeId, String savePath) {
        List<RecordKnowledgeConflict> listKnowledgeConflict = recordKnowledgeConflictService
                .list(new LambdaQueryWrapper<RecordKnowledgeConflict>()
                        .in(!setNodeId.isEmpty(), RecordKnowledgeConflict::getSourceId, setNodeId)
                        .or()
                        .in(!setNodeId.isEmpty(), RecordKnowledgeConflict::getTargetId, setNodeId));

        if (listKnowledgeConflict.isEmpty()) {
            return;
        }

        String filepath = savePath + "/" + IdUtils.simpleUUID() + ExportConstant.SUFFIX_RECORD_KNOWLEDGE_CONFLICT;
        CommonUtil.seriallizeList(listKnowledgeConflict, filepath);
    }


    @Override
    public void importList(String extName, String filepath, boolean overwrite, Logger logger) {
        switch (extName) {
            case ExportConstant.SUFFIX_RECORD_KNOWLEDGE:
                importRecordKnowledge(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_KNOWLEDGE_ATTRIBUTE:
                importRecordKnowledgeAttribute(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_KNOWLEDGE_RELATION:
                importRecordKnowledgeRelation(filepath, overwrite, logger);
                break;
            case ExportConstant.SUFFIX_RECORD_KNOWLEDGE_CONFLICT:
                importRecordKnowledgeConflict(filepath, overwrite, logger);
                break;
            default:
                break;
        }
    }
    public void importRecordKnowledge(String filepath, boolean overwrite, Logger logger) {

        List<RecordKnowledge> listRecordKnowledge = CommonUtil.deseriallizeList(filepath, RecordKnowledge.class);
        if (listRecordKnowledge.isEmpty()) {
            return;
        }

        try {
            recordKnowledgeService.saveOrUpdateBatch(listRecordKnowledge, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import RecordKnowledge error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    public void importRecordKnowledgeAttribute(String filepath, boolean overwrite, Logger logger) {

        List<RecordKnowledgeAttribute> listRecordKnowledgeAttribute = CommonUtil.deseriallizeList(filepath,
                RecordKnowledgeAttribute.class);
        if (listRecordKnowledgeAttribute.isEmpty()) {
            return;
        }

        try {
            recordKnowledgeAttributeService.saveOrUpdateBatch(listRecordKnowledgeAttribute, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import RecordKnowledge error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    public void importRecordKnowledgeRelation(String filepath, boolean overwrite, Logger logger) {

        List<RecordKnowledgeRelation> listRecordKnowledgeRelation = CommonUtil.deseriallizeList(filepath,
                RecordKnowledgeRelation.class);
        if (listRecordKnowledgeRelation.isEmpty()) {
            return;
        }

        try {
            recordKnowledgeRelationService.saveOrUpdateBatch(listRecordKnowledgeRelation, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import RecordKnowledge error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
    public void importRecordKnowledgeConflict(String filepath, boolean overwrite, Logger logger) {

        List<RecordKnowledgeConflict> listRecordKnowledgeConflict = CommonUtil.deseriallizeList(filepath,
                RecordKnowledgeConflict.class);
        if (listRecordKnowledgeConflict.isEmpty()) {
            return;
        }

        try {
            recordKnowledgeConflictService.saveOrUpdateBatch(listRecordKnowledgeConflict, overwrite);
            CommonUtil.renameFile(filepath);
        } catch (Exception ex) {
            logger.info("import RecordKnowledge error " + StringEscapeUtils.escapeJavaScript(ex.getMessage()));
        }
    }
}
