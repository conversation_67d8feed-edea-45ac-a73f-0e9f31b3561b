package com.suwell.plss.record.service.impl;

import static com.suwell.plss.framework.common.enums.FeatTypeEnum.ATT_EXTRACT_SWITCH;
import static com.suwell.plss.framework.common.filter.TraceFilter.HEADER_NAME_TRACE_ID;
import static com.suwell.plss.framework.mq.enums.EventType.FILE_2_OFD;
import static com.suwell.plss.framework.mq.enums.EventType.GET_AI_ALL;
import static com.suwell.plss.framework.mq.enums.EventType.GET_TEXT;
import static com.suwell.plss.record.standard.enums.RecordEnum.DocumentAttStatusEnum.ATT_STATUS_FAIL;
import static com.suwell.plss.record.standard.enums.RecordEnum.DocumentAttStatusEnum.ATT_STATUS_OK;
import static com.suwell.plss.record.standard.enums.RecordEnum.DocumentAttStatusEnum.ATT_STATUS_PROCESSED;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordFileTypeEnum.RECORD_FILE_TYPE_DOUBLE_OFD;
import static com.suwell.plss.record.standard.enums.RecordStatusEnum.RECORD_STATUS_PASS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.config.GeneralFeatureProperties;
import com.suwell.plss.framework.common.config.GeneralFeatureSceneConfig;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.mq.dto.base.MqExtendParamDTO;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordConvertProcessEvent;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.conf.AttFormatLimitConfig;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordProcessDetail;
import com.suwell.plss.record.service.AttFlowService;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.OperateAttESService;
import com.suwell.plss.record.service.RecordProcessDetailService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.DocumentUpdateCallbackReq;
import com.suwell.plss.record.standard.enums.EventSkipStateEnum;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
@RefreshScope
@Slf4j
@Service
public class AttFlowServiceImpl implements AttFlowService {
    @Resource
    private ApplicationContext context;
    @Resource
    private RecordService recordService;

    @Resource
    private DocumentService documentService;
    @Resource
    private OperateAttESService operateAttESService;

    @Resource
    private AttFormatLimitConfig attFormatLimitConfig;
    @Resource
    private RecordProcessDetailService recordProcessDetailService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private GeneralFeatureSceneConfig generalFeatureSceneConfig;
    @DSTransactional
    @Override
    public void triggerAttFlow(Long recordId, boolean refreshData, Integer priorityValue) {
        String seqId = StringUtils.isNotBlank(MDC.get(HEADER_NAME_TRACE_ID))
                ? MDC.get(HEADER_NAME_TRACE_ID) : IdWorker.getTimeId();
        CompletableFuture.runAsync(() -> {
            MDC.put(HEADER_NAME_TRACE_ID, seqId);
            try {
                triggerAttFlow(recordId, documentService.listByType(recordId,
                                RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode()), refreshData, false, priorityValue);
            } catch (Exception e){
                log.error("触发附件入库流程异常, recordId:{}", recordId, e);
            }
            MDC.remove(HEADER_NAME_TRACE_ID);
        });
    }

    @Override
    public void triggerAttFlow(Long recordId, Integer priorityValue) {
        triggerAttFlow(recordId, true, priorityValue);
    }

    @DSTransactional
    @Override
    public void attRetry(Long recordId, Integer priorityValue) {
        Record record = recordService.getOneById(recordId);
        if (Objects.isNull(record)) {
            log.error("附件入库流程触发失败, recordId:{} 不存在", recordId);
            return;
        }
        if (!RECORD_STATUS_PASS.getCode().equals(record.getRecordStatus())) {
            log.warn("附件入库流程触发失败, recordId:{} 未入库状态", recordId);
            return;
        }
        if (Objects.nonNull(record.getClassified())) {
            log.warn("附件入库流程触发失败, recordId:{} 密级的文件", recordId);
            return;
        }
        List<Document> documentList = documentService.listByTypeAndDocAttStatus(recordId,
                RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(), Lists.newArrayList(ATT_STATUS_FAIL.getCode()));
        if (CollUtil.isEmpty(documentList)) {
            return;
        }

        List<Long> docIdList = documentList.stream().map(Document::getId).toList();
        recordProcessDetailService.remove(Wrappers.<RecordProcessDetail>query()
                .lambda()
                .eq(RecordProcessDetail::getRecordId, recordId)
                .eq(RecordProcessDetail::getCtype, RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode())
                .in(RecordProcessDetail::getDocId, docIdList));

        TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            triggerAttFlow(recordId, documentList, false, priorityValue);
        });
    }

    @Override
    public void triggerAttFlow(Long recordId, List<Document> documentList, boolean attUpload, Integer priorityValue) {
        triggerAttFlow(recordId, documentList, true, attUpload, priorityValue);
    }

    /**
     * 触发附件入库流程
     * @param recordId
     * @param documentList
     */
    @DSTransactional
    @Override
    public void triggerAttFlow(Long recordId, List<Document> documentList, boolean refreshData,
            boolean attUpload, Integer priorityValue) {
        log.info("触发附件入库流程, recordId:{},docId:{}", recordId,JSON.toJSONString(documentList));

        /**
         * 附件的状态: 1-成功, 2-待处理, 3-处理中, 4-失败
         * 1. 通过recordId查询主件 关联的附件列表，通过文档类型支持的文件格式过滤，重点拿到fileId 列表
         * 2. 循环发附件入库消息
         */
        Record record = recordService.getOneById(recordId);
        if (Objects.isNull(record)) {
            log.error("附件入库流程触发失败, recordId:{} 不存在", recordId);
            return;
        }
        if (!RECORD_STATUS_PASS.getCode().equals(record.getRecordStatus())) {
            log.warn("附件入库流程触发失败, recordId:{} 未入库状态", recordId);
            return;
        }
        if (Objects.nonNull(record.getClassified())) {
            log.warn("附件入库流程触发失败, recordId:{} 密级的文件", recordId);
            return;
        }
        if (CollUtil.isNotEmpty(documentList)) {
            GeneralFeatureProperties generalFeature = generalFeatureSceneConfig.getGeneralFeatureConfig(
                    ATT_EXTRACT_SWITCH.getCode());
            Set<String> attSuffixAll = attFormatLimitConfig.getAttFormatTypeSuffixAll();
            for (Document doc : documentList) {
                if (Objects.nonNull(doc.getClassified())) {
                    log.warn("附件入库流程触发失败, recordId:{} docId:{},密级的文件", recordId,doc.getId());
                    continue;
                }

                // 附件直接上传场景
                if (attUpload) {
                    operateAttESService.putRecordAttEs(doc, priorityValue);
                    continue;
                }

                String suffix = StringUtils.isNotBlank( FileNameUtil.getSuffix(doc.getName()))
                        ? FileNameUtil.getSuffix(doc.getName()) : StringPool.EMPTY;
                if (!attSuffixAll.contains(suffix.toLowerCase())) {
                    // 标记附件成功处理
                    DocumentUpdateCallbackReq req = new DocumentUpdateCallbackReq();
                    req.setRecordId(doc.getRecordId());
                    req.setDocId(doc.getId());
                    req.setCtype(doc.getCtype());
                    documentService.updateDocumentBaseInfo(req, ATT_STATUS_OK.getCode());
                    log.info("附件入库流程触发失败, recordId:{},docId:{} 文件格式不支持", recordId, doc.getId());
                    if (refreshData) {
                        // 需求变更 附件先入库es，后续提取修改补充正文和ofd
                        operateAttESService.putRecordAttEs(doc, priorityValue);
                    }
                } else {
                    // 附件提取总开关，默认关闭
                    if (generalFeature.getFeatEnable()) {
                        if (refreshData) {
                            // 需求变更 附件先入库es，后续提取修改补充正文和ofd
                            operateAttESService.putRecordAttEs(doc, priorityValue);
                        }
                        // 标记附件处理中...
                        DocumentUpdateCallbackReq req = new DocumentUpdateCallbackReq();
                        req.setRecordId(doc.getRecordId());
                        req.setDocId(doc.getId());
                        req.setCtype(doc.getCtype());
                        documentService.updateDocumentBaseInfo(req, ATT_STATUS_PROCESSED.getCode());
                        // 记录附件入库流程的事件
                        recordProcessDetailService.saveBatchRecordProcessDetail(recordId, record.getName(), Lists.newArrayList(
                                        FILE_2_OFD,GET_TEXT,GET_AI_ALL), SecurityUtils.getUserId(), SecurityUtils.getUsername(),
                                doc.getId(), doc.getName(), RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode());

                        //附件提取事件
                        attExtractedEvent(record, doc, suffix, priorityValue);
                    }
                }
            }
        }
    }

    private void attExtractedEvent(Record record, Document doc, String suffix, Integer priorityValue) {
        Long recordId = record.getId();
        MqRecordReqDTO mqRecordReqDTO = new MqRecordReqDTO();
        mqRecordReqDTO.setRecordId(recordId);
        mqRecordReqDTO.setDocId(doc.getId());
        mqRecordReqDTO.setOrigFileId(doc.getFileId());
        mqRecordReqDTO.setCtype(RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode());
        mqRecordReqDTO.setSuffix(StringPool.DOT + suffix);
        mqRecordReqDTO.setFileId(doc.getFileId());
        mqRecordReqDTO.setSkipState(EventSkipStateEnum.ESS_NO.getCode());
        MqExtendParamDTO extendParam = new MqExtendParamDTO();
        extendParam.setClassified(doc.getClassified());
        extendParam.setOrigin(record.getOrigin());
        extendParam.setEventTypeList(Lists.newArrayList(GET_TEXT));
        mqRecordReqDTO.setExtendParam(extendParam);
        if ("ofd".equalsIgnoreCase(suffix)) {
            mqRecordReqDTO.setRecordFileType(RECORD_FILE_TYPE_DOUBLE_OFD.getCode());
        }
        log.info("触发附件入库流程, recordId:{},docId:{} 发送消息:{}", recordId, doc.getId(), JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordConvertProcessEvent<>(mqRecordReqDTO, priorityValue, FILE_2_OFD));
    }


}
