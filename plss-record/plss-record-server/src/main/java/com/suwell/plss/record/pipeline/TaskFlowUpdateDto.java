package com.suwell.plss.record.pipeline;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023/11/6
 * @content
 */
@Data
public class TaskFlowUpdateDto implements Serializable {

    private Long taskId;

    private Long taskDocId;

    private Integer ctype;

    private Long nodeId;

    private Integer status;

    private Integer nodeType;

    private Long recordId;

}
