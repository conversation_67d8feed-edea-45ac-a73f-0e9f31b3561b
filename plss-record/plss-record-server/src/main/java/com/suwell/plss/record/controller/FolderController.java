package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.redis.idempotent.Idempotent;
import com.suwell.plss.record.standard.domain.ResourceDTO;
import com.suwell.plss.record.standard.dto.request.DeepestFolderQueryReq;
import com.suwell.plss.record.standard.dto.request.FolderAddReq;
import com.suwell.plss.record.standard.dto.request.FolderAddTagReq;
import com.suwell.plss.record.standard.dto.request.FolderEditReq;
import com.suwell.plss.record.standard.dto.request.FolderMoveReq;
import com.suwell.plss.record.standard.dto.request.FolderMultiRecordReq;
import com.suwell.plss.record.standard.dto.request.FolderRecordRemoveReq;
import com.suwell.plss.record.standard.dto.request.FolderRecordReq;
import com.suwell.plss.record.standard.dto.request.FolderSortReq;
import com.suwell.plss.record.standard.dto.request.RecordMoveReq;
import com.suwell.plss.record.standard.dto.request.ResourceMoveReq;
import com.suwell.plss.record.standard.dto.request.ResourceReq;
import com.suwell.plss.record.standard.dto.response.FolderListResp;
import com.suwell.plss.record.standard.dto.response.FolderRelResp;
import com.suwell.plss.record.standard.dto.response.FolderResp;
import com.suwell.plss.record.standard.dto.response.FolderTreeResp;
import com.suwell.plss.record.standard.dto.response.RecordRelCountResp;
import com.suwell.plss.record.standard.dto.response.RecordRelFolderResp;
import com.suwell.plss.record.standard.service.StandardFolderFacade;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 目录表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/folder")
public class FolderController {

    public static final String MODE_NAME = "'后台-文库管理'";


    @Resource
    private StandardFolderFacade standardFolderFacade;

    /**
     * 保存
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Void> addFolder(@RequestBody FolderAddReq folder) {
        standardFolderFacade.addFolder(folder);
        return R.ok();
    }

    /**
     * 信息
     */
    @PostMapping("/{id}/query")
    public R<FolderResp> queryById(@PathVariable Long id) {

        FolderResp folder = standardFolderFacade.queryById(id);
        return R.ok(folder);
    }

    /**
     * 删除
     */
    @Log(title = MODE_NAME, businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Void> removeFolders(@RequestBody List<Long> folderIds) {
        standardFolderFacade.removeFolders(folderIds);
        return R.ok();
    }

    /**
     * 强制删除目录，包含目录下的文件
     */
    @Log(title = MODE_NAME, businessType = BusinessType.DELETE)
    @PostMapping("/forceRemove")
    public R<Void> forceRemoveFolders(@RequestBody List<Long> folderIds) {
        standardFolderFacade.forceRemove(folderIds);
        return R.ok();
    }

    /**
     * 目录排序
     * 需要目录在同一个目录下
     *
     * @param req 参数
     */
    @PostMapping("/sortFolder")
    public R<Void> sortFolder(@RequestBody FolderSortReq req) {
        standardFolderFacade.sortFolder(req);
        return R.ok();
    }

    /**
     * 编辑目录信息
     *
     * @param folderEditReq 参数
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Void> editFolder(@RequestBody FolderEditReq folderEditReq) {
        standardFolderFacade.editFolder(folderEditReq);
        return R.ok();
    }

    /**
     * 移动目录
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/move")
    public R<Void> moveFolders(@RequestBody FolderMoveReq moveReq) {
        standardFolderFacade.moveFolders(moveReq);
        return R.ok();
    }

    /**
     * 移动文件
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/moveRecords")
    public R<Void> moveRecords(@RequestBody RecordMoveReq moveReq) {
        standardFolderFacade.moveRecords(moveReq);
        return R.ok();
    }


    /**
     * 移动目录和文件
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/moveFolderAndRecords")
    public R<Void> moveFolderAndRecords(@RequestBody ResourceMoveReq moveReq) {
        standardFolderFacade.moveFolderAndRecords(moveReq);
        return R.ok();
    }

    /**
     * 目录下新增文件
     */
    @PostMapping("/record/add")
    public R<Void> addRecords(@RequestBody FolderRecordReq addRecordDTO) {
        standardFolderFacade.addRecords(addRecordDTO);
        return R.ok();
    }

    /**
     * 将文件批量新增至多个目录下
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/record/addMultiFolders")
    public R<Void> addMultiFolders(@RequestBody FolderMultiRecordReq req) {
        standardFolderFacade.addMultiFolders(req);
        return R.ok();
    }

    /**
     * 查询文件被关联的目录路径
     */
    @PostMapping("/record/rel/{recordId}")
    public R<List<RecordRelFolderResp>> queryRecordRelList(@PathVariable Long recordId) {
        List<RecordRelFolderResp> list = standardFolderFacade.queryRecordRelList(recordId);
        return R.ok(list);
    }

    /**
     * 查询目录路径
     */
    @PostMapping("/queryRelations")
    public R<List<FolderRelResp>> queryRelations(@RequestBody List<Long> folderIds) {
        List<FolderRelResp> list = standardFolderFacade.queryRelations(folderIds);
        return R.ok(list);
    }

    /**
     * 删除目录下的文件
     */
    @Log(title = MODE_NAME, businessType = BusinessType.REMOVE_DOC)
    @PostMapping("/record/remove")
    public R<Void> removeRecords(@RequestBody FolderRecordReq addRecordDTO) {
        standardFolderFacade.removeRecords(addRecordDTO);
        return R.ok();
    }

    /**
     * 删除目录下的文件
     */
    @Log(title = MODE_NAME, businessType = BusinessType.BATCH_REMOVE_DOC)
    @PostMapping("/record/batchRemove")
    public R<Void> batchRemoveRecords(@RequestBody FolderRecordRemoveReq req) {
        standardFolderFacade.batchRemoveRecords(req);
        return R.ok();
    }

    /**
     * 根据目录id查询子目录和文件
     */
    @PostMapping("/queryFolderAndRecord")
    public R<PageUtils<ResourceDTO>> queryFolderAndRecord(@RequestBody ResourceReq req) {
        PageUtils<ResourceDTO> page = standardFolderFacade.queryFolderAndRecord(req);
        return R.ok(page);
    }

    /**
     * 根据目录id查询所有下级文件
     */
    @PostMapping("/queryChildrenRecord")
    public R<PageUtils<ResourceDTO>> queryChildrenRecord(@RequestBody ResourceReq req) {
        PageUtils<ResourceDTO> page = standardFolderFacade.queryChildrenRecord(req);
        return R.ok(page);
    }

    /**
     * 查询目录树
     */
    @PostMapping("/{folderId}/queryFolderTree")
    public R<FolderTreeResp> queryFolderTree(@PathVariable Long folderId) {
        FolderTreeResp tree = standardFolderFacade.queryFolderTree(folderId);
        return R.ok(tree);
    }


    /**
     * 根据目录id查询子目录列表
     */
    @PostMapping("/{folderId}/queryChildrenFolder")
    public R<List<FolderListResp>> queryChildrenFolder(@PathVariable Long folderId) {
        List<FolderListResp> list = standardFolderFacade.queryChildrenFolder(folderId);
        return R.ok(list);
    }

    /**
     * 根据路径查询最低层级的目录id
     */
    @PostMapping("/queryDeepestFolderId")
    public R<Long> queryDeepestFolderId(@RequestBody DeepestFolderQueryReq req) {
        Long folderId = standardFolderFacade.queryDeepestFolderId(req);
        return R.ok(folderId);
    }

    /**
     * 释放文件
     */
    @Log(title = "'后台-文档入库'", businessType = BusinessType.UPDATE)
    @PostMapping("/releaseRecord")
    @Idempotent("#recordIds")
    public R<Void> releaseRecord(@RequestBody List<Long> recordIds) {
        standardFolderFacade.releaseRecord(recordIds);
        return R.ok();
    }


    /**
     * 查询文件关系数量
     */
    @PostMapping("/countRecordRel")
    public R<List<RecordRelCountResp>> countRecordRel(@RequestBody List<Long> recordIds) {
        List<RecordRelCountResp> list = standardFolderFacade.countRecordRel(recordIds);
        return R.ok(list);
    }

    /**
     * 根据文件名获取指定目录下的文件数量
     */
    @PostMapping("/getRecordCountByName")
    public R<Long> getRecordCountByName(@RequestParam("folderId") Long folderId,
            @RequestParam("recordName") String recordName) {
        Long countByName = standardFolderFacade.getRecordCountByName(folderId, recordName);
        return R.ok(countByName);
    }

    /**
     * 批量打标签
     */
    @PostMapping("/batchAddTag")
    public R<Void> batchAddTag(@RequestBody @Valid FolderAddTagReq req) {
        standardFolderFacade.batchAddTag(req);
        return R.ok();
    }

    /**
     * 旧表迁移数据到sharding
     */
    @PostMapping("/migrate2Sharding")
    public R<Void> migrate2Sharding() {
        standardFolderFacade.migrate2Sharding();
        return R.ok();
    }

}
