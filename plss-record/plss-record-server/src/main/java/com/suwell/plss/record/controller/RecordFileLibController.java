package com.suwell.plss.record.controller;

import cn.hutool.core.collection.CollUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.RecordOfflineV3Req;
import com.suwell.plss.record.standard.dto.response.RecordOfflineResp;
import com.suwell.plss.record.standard.dto.response.RecordOfflineResp.FileDto;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 最新文件上传20240716操作
 *
 * <AUTHOR>
 * @date 2024/7/16
 */
@Slf4j
@RestController
@RequestMapping("/v1/lib/file")
public class RecordFileLibController {

    @Resource
    private StandardRecordFacade recordFacade;

    /**
     * 最新版本文档批量上传
     *
     * @param req
     * @return
     */
    @PostMapping("/uploadBatch")
    public R<RecordOfflineResp> uploadBatch(@RequestBody RecordOfflineV3Req req) {
        StringBuilder toast = new StringBuilder("上传文档中与已入库文档存在重复，已自动过滤重复文档");
        final RecordOfflineResp resp = recordFacade.addUploadBatch(req);
        List<FileDto> list = resp.getList();
        if (CollUtil.isNotEmpty(list)) {
            return R.ok(resp, toast.toString());
        }
        return R.ok(resp);
    }

}
