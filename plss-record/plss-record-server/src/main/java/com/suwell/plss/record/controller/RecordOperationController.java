package com.suwell.plss.record.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.record.standard.dto.CorrectMetadataDTO;
import com.suwell.plss.record.enums.DocProcessStatusEnums.ProcessStatusEnum;
import com.suwell.plss.record.standard.dto.request.RecordOperationAttBatchReq;
import com.suwell.plss.record.standard.dto.request.RecordOperationMdBatchReq;
import com.suwell.plss.record.standard.dto.request.RecordOperationReq;
import com.suwell.plss.record.standard.dto.response.RecordOperationResp;
import com.suwell.plss.record.standard.service.StandardRecordOperationFacade;
import java.util.Arrays;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 文件运管操作
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
@RefreshScope
@Slf4j
@RestController
@RequestMapping("/v1/recordOperation")
public class RecordOperationController {

    @Value("${zb.resolveDdName:发文字号}")
    private String resolveDdName;

    @Resource
    private StandardRecordOperationFacade standardRecordOperationFacade;

    /**
     * 删除文件操作-慎重操作
     */
    @PostMapping("/deleteRecord")
    public R<Void> deleteRecord(@RequestBody Long[] ids) {
        standardRecordOperationFacade.removeRecord(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 检查文件是否有附件
     */
    @PostMapping("/recordCheckAtt")
    public R<PageUtils<RecordOperationResp>> recordCheckAtt(@RequestBody @Validated RecordOperationReq req) {
        return R.ok(standardRecordOperationFacade.recordCheckAtt(req));
    }

    /**
     * 批量上传主文件的附件
     */
    @PostMapping("/uploadRecordAtt")
    public R<Void> uploadRecordAtt(@RequestBody @Validated RecordOperationAttBatchReq req) {
        standardRecordOperationFacade.uploadRecordAtt(req);
        return R.ok();
    }

    /**
     * 根据文件id和元数据名称更新文件的元数据值
     */
    @PostMapping("/updateRecordMetadata")
    public R<Void> updateRecordMetadata(@RequestBody @Validated RecordOperationMdBatchReq req) {
        standardRecordOperationFacade.updateRecordMetadata(req);
        return R.ok();
    }


    @PostMapping("/correctMetadataImport")
    public R<Map<String, Object>> correctMetadataImport(MultipartFile file) throws Exception {
        String originalFilename = file.getOriginalFilename();
        String s = FileUtil.extName(originalFilename);
        if (!"xlsx".equals(s)) {
            throw new ServiceException("文件格式不正确，请上传xlsx格式的文件");
        }
        final String mdName = resolveDdName;
        Map<String, Object> map = Maps.newHashMap();
        ExcelUtil<Map<String, String>> excelUtil = new ExcelUtil<>();
        InputStream inputStream = file.getInputStream();
        List<Map<String, String>> excelData = excelUtil.getExcelData(inputStream);
        inputStream.close();
        if (CollUtil.isEmpty(excelData)) {
            throw new ServiceException("文件内容不正确，请按照模板填充");
        }
        for (Map<String, String> excelDatum : excelData) {
            if (StringUtils.isEmpty(excelDatum.get(mdName))) {
                throw new ServiceException(String.format("元数据名称为：%s，不能为空", mdName));
            }
        }
        Set<String> headers = Sets.newLinkedHashSet();
        excelData.get(0).forEach((k, v) -> headers.add(k));
        map.put("headers", headers);

        Set<String> uniqueValueList = Sets.newHashSet();
        for (Map<String, String> excelDatum : excelData) {
            excelDatum.forEach((k, v) -> {
                if (mdName.equals(k)) {
                    uniqueValueList.add(v);
                }
            });
        }

        List<CorrectMetadataDTO> list = standardRecordOperationFacade.correctMetadataImport(mdName, uniqueValueList, excelData);
        map.put("list", list);
        return R.ok(map);
    }

}
