package com.suwell.plss.record.facade;

import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.record.entity.DocumentHistory;
import com.suwell.plss.record.service.DocumentHistoryService;
import com.suwell.plss.record.standard.dto.request.DocumentHistoryAddReq;
import com.suwell.plss.record.standard.dto.request.DocumentHistoryQueryReq;
import com.suwell.plss.record.standard.dto.response.DocumentHistoryResp;
import com.suwell.plss.record.standard.service.StandardDocumentHistoryFacade;
import com.suwell.plss.system.api.enums.SystemBizError;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName DocumentHistoryFacadeImpl
 * <AUTHOR>
 * @Date 2023-8-10 16:47
 * @Version 1.0
 **/
@Service
public class StandardDocumentHistoryFacadeImpl implements StandardDocumentHistoryFacade {

    @Resource
    private DocumentHistoryService documentHistoryService;

    @Override
    public void addDocumentHistory(DocumentHistoryAddReq documentHistory) {
        AssertUtils.notNull(documentHistory, SystemBizError.DOCUMENT_HISTORY_NOT_EXIST);
        documentHistoryService.addDocumentHistory(documentHistory);
    }

    @Override
    public DocumentHistoryResp getById(Long id) {
        AssertUtils.notNull(id, SystemBizError.DOCUMENT_HISTORY_ID_NOT_NULL);
        DocumentHistory byId = documentHistoryService.getById(id);
        if (Objects.isNull(byId)) {
            return null;
        }
//        DocumentHistoryResp documentHistoryResp = new DocumentHistoryResp();
//        BeanUtils.copyBeanProp(byId,documentHistoryResp);
        return  DozerUtils.convertToNew(byId, DocumentHistoryResp.class);
    }

    @Override
    public void removeDocumentHistorys(List<Long> documentIds) {
        documentHistoryService.removeDocumentHistorys(documentIds);
    }

    @Override
    public PageUtils<DocumentHistoryResp> queryDocumentHistorys(DocumentHistoryQueryReq req) {

        PageUtils<DocumentHistory> page = documentHistoryService.queryDocumentHistorys(req);
        List<DocumentHistory> list = page.getList();
//        List<DocumentHistoryResp> documentHistoryRespList = Lists.newArrayList();
//        if (CollUtil.isNotEmpty(list)) {
//            documentHistoryRespList = list.stream().map(x -> {
//                DocumentHistoryResp resp = new DocumentHistoryResp();
//                BeanUtils.copyBeanProp(x, resp);
//                return resp;
//            }).collect(Collectors.toList());
//        }
        List<DocumentHistoryResp> documentHistoryRespList = DozerUtils.convertListToNew(list, DocumentHistoryResp.class);
        return new PageUtils<>(documentHistoryRespList, page.getTotalCount(), page.getPageSize(), page.getPage());
    }
}
