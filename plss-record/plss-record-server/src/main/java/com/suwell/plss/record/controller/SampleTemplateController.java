package com.suwell.plss.record.controller;


import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.entity.SampleTemplateCatalogue;
import com.suwell.plss.record.service.SampleTemplateCatalogueService;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.SampleTemplateFileListResp;
import com.suwell.plss.record.standard.service.SampleTemplateFacade;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;

/**
 * ==========================
 * 开发：maple
 * 创建时间：2023/11/22
 * 版本：1.0
 * 描述：范文的目录层级接口层
 * ==========================
 */
@RestController
@RequestMapping("/v1/sampleTemplate")
public class SampleTemplateController {

    @Resource
    private SampleTemplateFacade sampleTemplateFacade;

    @Resource
    private SampleTemplateCatalogueService sampleTemplateCatalogueService;

    /**
     * 创建目录
     *
     * @param
     * @return
     */
    @PostMapping("/buildCatalogue")
    public R<Void> buildCatalogue(@Valid @RequestBody SampleTemplateBuildCatalogueReq req) {
        this.sampleTemplateCatalogueService.buildCatalogue(req);
        return R.ok();
    }

    /**
     * 修改目录名称
     *
     * @return
     */
    @PutMapping
    public R<Void> rename(@Valid @RequestBody SampleTemplateCatalogueRenameReq req) {
        this.sampleTemplateCatalogueService.rename(req);
        return R.ok();
    }

    /**
     * 目录批量删除
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @PostMapping("/catalogue/batch/del")
    public R<Integer> batchDelCatalogue(@RequestBody List<Long> ids) {
        return R.ok(this.sampleTemplateFacade.batchDelCatalogue(ids));
    }

    /**
     * 根据文件名模糊搜索 公文模板/公文样例 列表
     */
    @PostMapping("/file/list")
    public R<PageUtils<SampleTemplateFileListResp>> page(@Valid @RequestBody DocumentSampleTemplateFileReq req) {
        return R.ok(this.sampleTemplateFacade.pageFile(req));
    }

    /**
     * 目录分页
     */
    @PostMapping("/catagolue/page")
    public R<PageUtils<SampleTemplateCatalogue>> cataloguePage(@RequestBody SampleTemplateCatalogueReq req) {
        return R.ok(this.sampleTemplateCatalogueService.pageList(req));
    }

    /**
     * 获取事务公文模板的同类模板
     */
    @PostMapping("/homogeneous/template")
    public R<PageUtils<SampleTemplateFileListResp>> getHomogeneousTemplate(@Valid @RequestBody HomogeneousTemplateReq req) {
        return R.ok(this.sampleTemplateFacade.getHomogeneousTemplate(req));
    }

    /**
     * 批量上传范文和模板
     *
     * @param multipartFiles 文件
     * @param catalogueId    分类
     * @return
     */
    @PostMapping("/sample/templates/upload")
    public R<Void> sampleTemplatesUpload(@RequestPart("multipartFiles") MultipartFile[] multipartFiles,
                                         @RequestParam Long catalogueId) {
        this.sampleTemplateFacade.sampleTemplatesUpload(multipartFiles, catalogueId);
        return R.ok();
    }

    /**
     * 刷新当前数据数据
     *
     * @return
     */
    @PostMapping("/refreshSampleTemplates")
    public R<Void> refreshSampleTemplates(@RequestBody RefreshSampleTemplatesReq req) {
        this.sampleTemplateFacade.refreshSampleTemplates(req);
        return R.ok();
    }

    /**
     * 下载导入模板
     *
     * @param catalogueId
     * @return
     */
    @PostMapping("/downloadImportTemplate")
    public void downloadImportTemplate(@RequestParam Long catalogueId, HttpServletResponse response) {
        this.sampleTemplateFacade.downloadImportTemplate(catalogueId, response);
    }

    /**
     * 导入文件
     *
     * @param file
     * @param coverage 是否全量覆盖
     */
    @PostMapping("/importTemplateFile")
    public void importTemplateFile(@RequestPart("file") MultipartFile file,
                                   @RequestParam(defaultValue = "true") Boolean coverage) {
        this.sampleTemplateFacade.importTemplateFile(file, coverage);
    }

}
