package com.suwell.plss.record.controller;

import cn.hutool.core.util.StrUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.domain.BasePageCondition;
import com.suwell.plss.record.standard.dto.request.AttachmentOpenReq;
import com.suwell.plss.record.standard.dto.request.DocDeleteReq;
import com.suwell.plss.record.standard.dto.request.RecordDocumentCheckReq;
import com.suwell.plss.record.standard.dto.request.RecordInfoOfflineAttReq;
import com.suwell.plss.record.standard.dto.request.SaveThumbnailReq;
import com.suwell.plss.record.standard.dto.response.DocumentAttachmentsResp;
import com.suwell.plss.record.standard.service.StandardDocumentFacade;
import java.util.List;
import java.util.Locale;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/8/25
 */
@Slf4j
@RestController
@RequestMapping("/v1/document")
public class DocumentHttpController {

    @Resource
    private StandardDocumentFacade standardDocumentFacade;

    /**
     * 上传附件 ok
     */
    @PostMapping("/uploadAttachments")
    public R<Void> uploadAttachments(@ModelAttribute RecordInfoOfflineAttReq recordInfoOfflineAttReq) {
        standardDocumentFacade.addUploadAttachments(recordInfoOfflineAttReq);
        return R.ok();
    }

    /**
     * 查询主件关联的附件 ok
     */
    @PostMapping("/queryAttachments")
    public R<List<DocumentAttachmentsResp>> queryAttachments(@RequestBody Long recordId) {
        return R.ok(standardDocumentFacade.queryAttachments(recordId));
    }

    /**
     * 查询主件缩略图
     */
    @PostMapping("/queryThumbnail")
    public R<DocumentAttachmentsResp> queryThumbnail(@RequestBody Long recordId) {
        DocumentAttachmentsResp resp = standardDocumentFacade.queryThumbnail(recordId);
        return R.ok(resp);
    }

    /**
     * 删除主件的所有附件
     */
    @PostMapping("/deleteAttachments")
    public R<Void> deleteAttachments(@RequestBody List<Long> recordIds) {
        standardDocumentFacade.deleteAttachments(recordIds);
        return R.ok();
    }

    /**
     * 删除附件 OK
     */
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody @Validated DocDeleteReq req) {
        standardDocumentFacade.removeByIds(req);
        return R.ok();
    }

    @PostMapping("/checkUploaded")
    public R<Boolean> checkUploaded(@RequestBody RecordDocumentCheckReq req) {
        // 防止大小第三问题
        if (StrUtil.isNotBlank(req.getFile_md5())) {
            req.setFile_md5(req.getFile_md5().toLowerCase(Locale.ENGLISH));
        }
        return R.ok(standardDocumentFacade.checkedDocumentUploaded(req));
    }

    @PostMapping("/saveThumbnail")
    public R<Void> saveThumbnail(@RequestBody SaveThumbnailReq req) {
        standardDocumentFacade.saveThumbnail(req);
        return R.ok();
    }

    /**
     * 查询主文件不是 docx/doc 的文档
     */
    @PostMapping("/listNonDoc")
    public R<PageUtils<DocumentAttachmentsResp>> listNonDoc(@RequestBody BasePageCondition req) {
        return R.ok(standardDocumentFacade.listNonDoc(req));
    }

    @PostMapping("/webUrl")
    public R<String> webUrl(@RequestBody AttachmentOpenReq req) {
        return R.ok(standardDocumentFacade.webUrl(req));
    }


    @PostMapping("/migrateDoc2Shard")
    public R<Void> migrateDoc2Shard() {
        standardDocumentFacade.migrateDoc2Shard();
        return R.ok();
    }


    @PostMapping("/reHashDoc")
    public R<Void> reHashDoc() {
        standardDocumentFacade.reHashDoc();
        return R.ok();
    }
    @PostMapping("/reHashMd")
    public R<Void> reHashMd() {
        standardDocumentFacade.reHashMd();
        return R.ok();
    }

    @PostMapping("/removeOldShardingHisData")
    public R<Void> removeOldShardingHisData() {
        standardDocumentFacade.removeOldShardingHisData();
        return R.ok();
    }


}
