package com.suwell.plss.record.dataprocesstask.executor;

import com.suwell.plss.record.dataprocesstask.AbstractTaskCondition;
import com.suwell.plss.record.enums.TaskConditionEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/12/30
 * @content
 */
@Component
public class TaskConditionFactory {

    private Map<TaskConditionEnums.ConditionTypeEnum, AbstractTaskCondition> map = new HashMap<>();

    @Autowired
    public void setMap(List<AbstractTaskCondition> list){
        for (AbstractTaskCondition abstractTaskCondition : list) {
            map.put(abstractTaskCondition.getConditionType(), abstractTaskCondition);
        }
    }

    public AbstractTaskCondition getConditionImpl(TaskConditionEnums.ConditionTypeEnum conditionTypeEnum){
        return map.get(conditionTypeEnum);
    }

}
