package com.suwell.plss.record.facade;

import static com.suwell.plss.framework.common.enums.CommonRedisKeyEnum.DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK;
import static com.suwell.plss.framework.mq.enums.EventType.PIPELINE_FILE_MODIFY_REPO_POSITION;
import static com.suwell.plss.record.enums.ProjectFileSizeSceneTypeEnum.UPLOAD_ATTACHMENT;
import static com.suwell.plss.record.standard.enums.EncryptFileTypeEnum.FILE_TYPE_INTERNALLY;
import static com.suwell.plss.record.standard.enums.EncryptFileTypeEnum.FILE_TYPE_PROJECT;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_DOC_PROCESS_NOT_EXIST;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_USER_NOT_PMS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.config.WebReaderConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.DbTypeUtil;
import com.suwell.plss.framework.common.utils.LambdaUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.TOTPUtil;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.mq.dto.base.MqDocProcessBaseDTO;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.conf.NodeConfig;
import com.suwell.plss.record.conf.RecordPersonalConfig;
import com.suwell.plss.record.domain.RecordFrontDto;
import com.suwell.plss.record.entity.*;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.enums.*;
import com.suwell.plss.record.enums.DocProcessStatusEnums.ProcessStatusEnum;
import com.suwell.plss.record.enums.DocProcessStatusEnums.ProcessSubStatusEnum;
import com.suwell.plss.record.permission.factory.PermissionValidatorFactory;
import com.suwell.plss.record.pipeline.StorageInfoV3Dto;
import com.suwell.plss.record.pipeline.handler.AbstractNodeHandler;
import com.suwell.plss.record.pipeline.handler.NodeHandlerFactory;
import com.suwell.plss.record.service.*;
import com.suwell.plss.record.standard.domain.*;
import com.suwell.plss.record.standard.domain.DefinedConfigJsonDTO.CheckBoxButton;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.request.DocProcessEditReq.AiMdValueDTO;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.standard.service.StandardDocProcessFacade;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import com.suwell.plss.record.standard.service.StandardTaskFacade;
import com.suwell.plss.record.util.NodeCacheUtil;
import com.suwell.plss.record.util.RecordCommonUtils;
import com.suwell.plss.system.api.domain.request.AuditMessageReq;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.SysMessageEnum.TypeEnum;
import com.suwell.plss.system.api.service.MessageRpcService;
import com.suwell.plss.system.api.service.UserRpcService;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jaxb.core.v2.TODO;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 * @create 2023/11/15
 * @content
 */
@Slf4j
@RefreshScope
@Service
public class StandardDocProcessFacadeImpl implements StandardDocProcessFacade {

    public static final String DOC_RETRY_KEY = "DOC_RETRY_KEY:%s";
    public static final String RETRY_OPT_RECORD_ID_KEY = "RETRY_OPT_RECORD_ID_KEY:%s";
    @Resource
    private DocProcessService docProcessService;
    @Resource
    private TaskFlowService taskFlowService;
    @Resource
    private StandardTaskFacade standardTaskFacade;
    @Resource
    private TaskBatchService taskBatchService;
    @Resource
    private RedisService redisService;
    @Resource
    private RecordService recordService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private RecordMangerService recordMangerService;
    @Resource
    private DocumentService documentService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private PlanNodeService planNodeService;
    @Resource
    private StandardRecordFacade standardRecordFacade;
    @Resource
    private DocAuditService docAuditService;
    @Resource
    private UserRpcService userRpcService;
    @Resource
    private MessageRpcService messageRpcService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private FileService fileService;
    @Resource
    private TaskDocService taskDocService;
    @Lazy
    @Resource
    private NodeHandlerFactory nodeHandlerFactory;
    @Lazy
    @Resource
    private FolderService folderService;
    @Lazy
    @Resource
    private TaskService taskService;
    @Resource
    private PermissionValidatorFactory validatorFactory;
    @Resource
    private PipelineTraceLogService pipelineTraceLogService;
    @Resource
    private RecordCommonUtils recordCommonUtils;
    @Resource
    private PlanAuditUserService planAuditUserService;
    @Resource
    private NodeCacheUtil nodeCacheUtil;
    @Resource
    private TOTPUtil totpUtil;
    @Resource
    private AiMiddleSegmentService aiMiddleSegmentService;
    @Resource
    private RecordRequestService recordRequestService;
    @Resource
    private DbTypeUtil dbTypeUtil;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;
    @Resource
    protected OperateDocProcessESService docProcessESService;
    @Resource
    private RecordTypeMetadataService recordTypeMetadataService;
    @Resource
    private DataProcessTaskService dataProcessTaskService;

    @Resource
    private WebReaderConfig webReaderConfig;
    @Resource
    private RepoDocService repoDocService;
    @Resource
    private PersonalRepoProcessService personalRepoProcessService;
    @Resource
    private RetryMsgService retryMsgService;
    @Resource
    private RecordPersonalConfig recordPersonalConfig;
    @Resource
    private NodeConfig nodeConfig;

    @Override
    public List<DocProcessNumResp> queryDocProcessNum(Long batchId, Boolean forSecure) {
        String tenantId = SecurityUtils.getTenantId();
        return docProcessService.queryDocProcessNum(tenantId, batchId, forSecure);
    }

    @Override
    public List<DocProcessStatusResp> queryDocProcessStatus() {
        return docProcessService.queryDocProcessStatus();
    }

    @Override
    public List<DocProcessSubStatusResp> queryDocProcessSubStatus(DocProcessStatusReq req) {
        return docProcessService.queryDocProcessSubStatus(req);
    }

    @Override
    public List<DocFrontProcessStatusResp> frontQueryDocProcessSubStatus(DocProcessFrontStatusReq req) {

        Map<Integer, DocFrontProcessStatusResp> map = new LinkedHashMap<>();
        final DocProcessStatusEnums.ProcessSubStatusEnum[] items = DocProcessStatusEnums.ProcessStatusFrontEnum.convert(
                req.getStatus()).getItems();

        for (DocProcessStatusEnums.ProcessSubStatusEnum item : items) {
            final int code = item.getCode();
            int nCode = code;
            String name = item.getDesc();
            int stype = 2;
            if (code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FORMAT_CONVERSION_IN_PROGRESS.getCode() ||
                    code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_IN_PROGRESS.getCode() ||
                    code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_CITATION_EXTRACTION_IN_PROGRESS.getCode() ||
                    code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_QUEUING_UP.getCode()) {
                // 处理中
                nCode = DocProcessStatusEnums.ProcessStatusEnum.PS_SYSTEM_IN_PROCESS.getCode();
                stype = 1;
                name = DocProcessStatusEnums.ProcessStatusEnum.PS_SYSTEM_IN_PROCESS.getDesc();
            } else if (code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FORMAT_CONVERSION_EXCEPTION.getCode() ||
                    code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_EXCEPTION.getCode() ||
                    code == DocProcessStatusEnums.ProcessSubStatusEnum.PSS_ABNORMAL_CITATION_EXTRACTION.getCode()) {
                // 异常
                nCode = DocProcessStatusEnums.ProcessStatusEnum.PS_EXCEPTION_DOCUMENT.getCode();
                stype = 1;
                name = DocProcessStatusEnums.ProcessStatusEnum.PS_EXCEPTION_DOCUMENT.getDesc();
            }
            if (!map.containsKey(nCode)) {
                DocFrontProcessStatusResp resp = new DocFrontProcessStatusResp();
                resp.setStatus(nCode);
                resp.setStatusName(name);
                resp.setStype(stype);
                map.put(nCode, resp);
            }
        }

        return CollUtil.newArrayList(map.values());
    }

    @Override
    public PageUtils<DocProcessResp> queryPage(DocProcessQueryV2Req req) {
        return docProcessService.queryPage(req);
    }

    @Override
    public PageUtils<DocProcessFrontResp> queryPageFront(DocProcessFrontQueryReq req) {
        return docProcessService.queryPageFront(req);
    }

    @Override
    public DocProcessResp viewMainDocProcessInfo(Long recordId) {
        DocProcess docProcess = docProcessService.getByRecordId(recordId);
        DocProcessResp processResp = new DocProcessResp();
        processResp.setRecordId(docProcess.getRecordId());
        processResp.setName(docProcess.getName());
        processResp.setTitle(docProcess.getTitle());
        processResp.setRecordTypeId(docProcess.getRecordTypeId());
        processResp.setProcessStatus(docProcess.getProcessStatus());
        processResp.setProcessSubStatus(docProcess.getProcessSubStatus());
        processResp.setKnowledgeStatus(docProcess.getKnowledgeStatus());
        processResp.setOrigin(docProcess.getOrigin());
        processResp.setCreateBy(docProcess.getCreateBy());
        processResp.setCreateByName(docProcess.getCreateByName());
        processResp.setStoreWay(docProcess.getStoreWay());
        processResp.setClassified(docProcess.getClassified());
        return processResp;
    }

    @Override
    public void retry(DocProcessRetryReq req) {
        innerRetry(req.getRecordId(), req.getStoreWay(), () -> docProcessService.getByRecordId(req.getRecordId()));
    }

    @Override
    public void retry(DocProcessRetryV2Req req) {
        innerRetry(req.getRecordId(), req.getStoreWay(), () -> docProcessService.getByRecordId(req.getRecordId()));
    }

    private void innerRetry(Long id, Integer storeWay, Supplier<DocProcess> supplier) {
        String key = String.format(DOC_RETRY_KEY, id);
        try {
            // 添加幂等性校验，防止接口重复调用
            if (Boolean.TRUE.equals(redisService.hasKey(key))) {
                // 重试中
                log.info("id:{},storeWay:{} ,正在重试中。。。", id, storeWay);
                return;
            }
            redisService.setEx(key, "1", 10, TimeUnit.SECONDS);
            final DocProcess docProcess = supplier.get();
            Long recordId = docProcess.getRecordId();
            Map<Long, PlanNodeDto> planNodeList = nodeCacheUtil.getPlanNodeMap(docProcess.getPlanId());
            LambdaQueryWrapper<TaskFlow> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(TaskFlow::getNodeId, TaskFlow::getNodeType);
            wrapper.eq(TaskFlow::getTaskDocId, docProcess.getTaskDocId());

            List<TaskFlow> flows = taskFlowService.listWithWrapper(List.of(recordId), wrapper);

            Map<Integer, TaskFlow> collect = flows.stream()
                    .collect(Collectors.toMap(o -> planNodeList.get(o.getNodeId()).getIndex(), Function.identity(),
                            (o1, o2) -> o1));
            TaskFlow taskFlow = collect.get(collect.keySet().stream().max(Integer::compareTo).get());

            // 新增重试记录
            RecordRetryLog retryLog = new RecordRetryLog();
            retryLog.setRecordId(recordId);
            retryLog.setNodeType(taskFlow.getNodeType());
            retryLog.setProcessStatus(docProcess.getProcessStatus());
            retryLog.setProcessSubStatus(docProcess.getProcessSubStatus());
            retryLog.setCreateBy(SecurityUtils.getUserId());
            recordRequestService.saveRecordRequest(retryLog);

            AtomicReference<Long> fileIdAtomic = new AtomicReference<>();
            if(Objects.equals(docProcess.getCtype(),DocProcessStatusEnums.DocProcessTypeEnum.DPT_PERSONAL_REPO.getCode())){
                // 个人库入库的人工重试，直接拿重试记录里面的
                Long fileId = personalRepoProcessService.getRetryFileId(recordId);
                if(fileId == null){
                    TaskDoc taskDoc = nodeCacheUtil.getTaskDoc(docProcess.getTaskDocId(), docProcess.getRecordId());
                    String storageInfo = taskDoc.getStorageInfo();
                    StorageInfoV3Dto storageInfoDto = JSON.parseObject(storageInfo, StorageInfoV3Dto.class);
                    fileIdAtomic.set(storageInfoDto.getFileId());
                }

                Record record = recordService.getOneById(recordId);
                record.setStatus(PersonalRecordStatusEnums.RecordStatusEnum.PROCESSING.getCode());
                recordService.modifyById(record);
            }
            // 重发消息
            standardTaskFacade.retry(
                    RecordRetryDto.builder()
                            .taskDocId(docProcess.getTaskDocId())
                            .planId(docProcess.getPlanId())
                            .origin(docProcess.getOrigin())
                            .userId(docProcess.getCreateBy())
                            .tenantId(docProcess.getTenantId())
                            .storeWay(storeWay)
                            .nodeId(taskFlow.getNodeId())
                            .recordId(docProcess.getRecordId())
                            .originalFileId(fileIdAtomic.get())
                            .build()
            );
        } catch (Exception e) {
            log.error("重试异常！", e);
            // 删除key
            redisService.delete(key);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void batchRetry(DocProcessBatchRetryReq req) {
        final List<Long> ids = req.getRecordIds();
        for (Long id : ids) {
            final DocProcessRetryV2Req retryReq = new DocProcessRetryV2Req();
            retryReq.setRecordId(id);
            retryReq.setStoreWay(req.getStoreWay());
            retry(retryReq);
        }
    }

    @Override
    public void batchRetry(DocProcessBatchRetryV2Req req) {
        final List<Long> ids = req.getRecordIdList();
        for (Long id : ids) {
            final DocProcessRetryV2Req retryReq = new DocProcessRetryV2Req();
            retryReq.setRecordId(id);
            retryReq.setStoreWay(req.getStoreWay());
            retry(retryReq);
        }
    }

    @Override
    @DSTransactional
    public void delete(DocProcessDeleteReq req) {
        final Date nowDate = new Date();
        // 软删除文档处理记录
//        docProcessService.lambdaUpdate()
//                .set(DocProcess::getDeleted, CommonDeleteEnum.CD_DELETE.getCode())
//                .set(DocProcess::getModifiedTime, nowDate)
//                .in(DocProcess::getRecordId, req.getRecordIdList())
//                .update();
        // 软删除流转记录
        final List<DocProcess> docProcess = docProcessService.listWithWrapper(req.getRecordIdList(), null);

        if (CollUtil.isNotEmpty(docProcess)) {
            // 修改子文件状态为单独子文件
            List<DocProcess> filterProcess = docProcess.stream()
                    .filter(o -> o.getCtype() == DocProcessStatusEnums.DocProcessTypeEnum.DPT_MASTER.getCode())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(filterProcess)) {
                List<Long> taskDocIdList = filterProcess.stream().map(DocProcess::getTaskDocId).collect(Collectors.toList());
                List<Long> recordIdList = filterProcess.stream().map(DocProcess::getRecordId).collect(Collectors.toList());
                Set<Long> recordIdSet = taskDocService.listWithWrapper(recordIdList, Wrappers.<TaskDoc>lambdaQuery().in(TaskDoc::getId, taskDocIdList))
                        .stream()
                        .flatMap(o -> {
                            String subRecordIds = o.getSubRecordIds();
                            if (StrUtil.isBlank(subRecordIds)) {
                                return Stream.empty();
                            } else {
                                return Arrays.stream(subRecordIds.split(StringPool.COMMA)).map(Long::parseLong);
                            }
                        }).collect(Collectors.toSet());

                if(CollUtil.isNotEmpty(recordIdSet)){
                    LambdaUpdateWrapper<DocProcess> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.set(DocProcess::getCtype, DocProcessStatusEnums.DocProcessTypeEnum.DPT_ONLY_SUB.getCode())
                            .eq(DocProcess::getCtype, DocProcessStatusEnums.DocProcessTypeEnum.DPT_SUB.getCode());
                    docProcessService.updateByWrapper(recordIdSet, wrapper);
                }
            }
            List<Long> recordIds = req.getRecordIdList();
            docProcess.stream().collect(Collectors.toMap(DocProcess::getRecordId, Function.identity(), (o1, o2) -> o1));
            standardRecordFacade.removeByIds(recordIds,l->{
                // 查询元数据
                for (Long recordId : l) {
                    List<Long> list = recordMetadataValueService.queryRecordMetadataRepoPositionList(recordId);
                    Optional.ofNullable(list).ifPresent(ll->{
                        folderService.queryRelations(ll)
                                .stream()
                                .map(f->{
                                    return f.getFolderChain().get(0).getRepoId();
                                }).filter(Objects::nonNull)
                                .forEach(repoId->{
                                    repoDocService.removeRecordFromRepo(recordId, repoId);
                                });
                    });
                }
            });

            // 删除文档审核记录
            docAuditService.remove(Wrappers.<DocAudit>query().lambda().in(DocAudit::getRecordId, recordIds));
            // 物理删除文档处理数据
            deleteDocProcessData(recordIds);
            aiMiddleSegmentService.unbindRecordSegmentRelation(recordIds);
            // 删除相关个人消息
            TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
            });
        }
    }

    @Override
    public DocInfoResp editInfo(DocProcessViewReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        final DocInfoResp resp = new DocInfoResp();
        final DocBaseResp baseResp = new DocBaseResp();
        resp.setDocBaseResp(baseResp);

        final Long recordId = req.getRecordId();
        Record record = recordService.getOneById(recordId);
        baseResp.setRecordId(recordId);
        baseResp.setRecordtypeId(record.getRecordtypeId());
        baseResp.setTextrank(record.getDigest());
        resp.setClassified(record.getClassified());

        // 实体
        final DocEntityResp entityResp = new DocEntityResp();
        resp.setDocEntityResp(entityResp);

        // 元数据
        //分类
        List<RecordMakeTagResp> recordMakeTagRespList = recordMangerService.queryMakeTag(recordId, false);
        resp.setMetaDataValueList(recordMetadataValueService.queryRecordTypeMetaDataValue(recordId,
                record.getRecordtypeId()).stream().filter(o -> {
            if (plssFixedMetadataNameConfig.getFixedKeywordName().equals(o.getMdName())) {
                AiMdValueDTO aiMdValueDTO = new AiMdValueDTO();
                aiMdValueDTO.setDataType(o.getDataType());
                aiMdValueDTO.setDataList(JSON.parseObject(o.getMdValue(), new TypeReference<List<String>>() {
                }));
                aiMdValueDTO.setOptView(o.getOptView());
                aiMdValueDTO.setOptEdit(o.getOptEdit());
                aiMdValueDTO.setAliasName(o.getAliasName());
                aiMdValueDTO.setAliasNameView(o.getAliasNameView());
                baseResp.setKeywords(aiMdValueDTO);
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(o.getMdName())) {
                AiMdValueCategoryDetailsResp aiMdValueCategoryDetailsResp = new AiMdValueCategoryDetailsResp();
                aiMdValueCategoryDetailsResp.setDataList(recordMakeTagRespList);
                aiMdValueCategoryDetailsResp.setDataType(o.getDataType());
                aiMdValueCategoryDetailsResp.setOptView(o.getOptView());
                aiMdValueCategoryDetailsResp.setOptEdit(o.getOptEdit());
                aiMdValueCategoryDetailsResp.setAliasName(o.getAliasName());
                aiMdValueCategoryDetailsResp.setAliasNameView(o.getAliasNameView());
                baseResp.setCategoryList(aiMdValueCategoryDetailsResp);
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedDateName().equals(o.getMdName())) {
                entityResp.setDateList(getAiMdValueDTO(o));
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedLocationName().equals(o.getMdName())) {
                entityResp.setLocationList(getAiMdValueDTO(o));
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedPersonName().equals(o.getMdName())) {
                entityResp.setPersonList(getAiMdValueDTO(o));
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedOrganizationName().equals(o.getMdName())) {
                entityResp.setOrganizationList(getAiMdValueDTO(o));
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedRepoPositionName().equals(o.getMdName())) {
                resp.setRepoIdList(StringUtils.isNotBlank(o.getMdValue()) ?
                        Arrays.stream(o.getMdValue().split(StringPool.COMMA))
                                .map(Long::parseLong).collect(Collectors.toList())
                        : Lists.newArrayList());
                resp.setRepoRequired(o.getRequired());
                resp.setRepoOptView(o.getOptView());
                resp.setOptEdit(o.getOptEdit());
                resp.setAliasName(o.getAliasName());
                resp.setAliasNameView(o.getAliasNameView());
                return false;
            }
            return true;
        }).collect(Collectors.toList()));

        // 附件
        final List<Document> documentList = documentService.listByBatchType(List.of(recordId),
                RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(),
                RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode());

        resp.setAttachmentMap(documentList.stream()
                .filter(o -> RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode()
                        .equals(o.getCtype())).map(o -> {
                    final DocumentInfoResp infoResp = new DocumentInfoResp();
                    infoResp.setId(o.getId());
                    infoResp.setRecordId(recordId);
                    infoResp.setName(o.getName());
                    infoResp.setClassified(o.getClassified());
                    FileTempUrlReq fileTempUrlReq = new FileTempUrlReq();
                    fileTempUrlReq.setFileId(o.getFileId());
                    fileTempUrlReq.setForFront(true);
                    infoResp.setDownUrl(fileService.tempUrl(fileTempUrlReq));
                    String serverEndpointPrefix = serverBasePathConfig.getSysHostPort()
                            + serverBasePathConfig.getSysHostContextPath() + serverBasePathConfig.getSysApiPrefix();
                    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
                            serverEndpointPrefix + "/record/v1/readCallBack/info");
                    builder.queryParam("docId", o.getId())
                            .queryParam("recordId", o.getRecordId())
                            .queryParam("userId", SecurityUtils.getUserId())
                            .queryParam("timestamp", System.nanoTime());
                    if (totpUtil.isCheckSign()) {
                        String sign = totpUtil.generateMyTOTP(o.getId().toString(),
                                SecurityUtils.getUserId().toString());
                        builder.queryParam("suwellsign", sign);
                    }
                    infoResp.setWebUrl(URLEncoder.encode(builder.toUriString(), Charsets.UTF_8));
                    infoResp.setAttachmentType(Optional.ofNullable(o.getAttachmentType())
                            .orElse(DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode()));
                    return infoResp;
                }).collect(Collectors.groupingBy(DocumentInfoResp::getAttachmentType,
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.mapping(Function.identity(),Collectors.toList()),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(DocumentInfoResp::getId))
                                        .collect(Collectors.toList())
                        )
                        )));

        documentList.stream().filter(o -> RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode()
                        .equals(o.getCtype()))
                .map(o -> {
                    Map<String,Object> params = new HashMap<>();
                    params.put("docId", o.getId());
                    params.put("recordId", o.getRecordId());
                    params.put("userId", SecurityUtils.getUserId());
                    params.put("timestamp", System.nanoTime());
                    if (totpUtil.isCheckSign()) {
                        //生成加密签名
                        String sign = totpUtil.generateMyTOTP(o.getId().toString(), SecurityUtils.getUserId().toString());
                        params.put("suwellsign", sign);
                    }
                    if(documentList.size() > 1){
                        return webReaderConfig.buildReaderUrl(params);
                    }
                    return webReaderConfig.buildReaderUrl(params, false);
                })
                .findFirst()
                .ifPresent(resp::setWebUrl);

        return resp;
    }

    private String getWebUrl(Document o) throws UnsupportedEncodingException {
        String serverEndpointPrefix = serverBasePathConfig.buildServerUrl(false);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
                serverEndpointPrefix + "/record/v1/readCallBack/info");
        builder.queryParam("docId", o.getId())
                .queryParam("recordId", o.getRecordId())
                .queryParam("userId", SecurityUtils.getUserId())
                .queryParam("timestamp", System.nanoTime());
        if (totpUtil.isCheckSign()) {
            //生成加密签名
            String sign = totpUtil.generateMyTOTP(o.getId().toString(), SecurityUtils.getUserId().toString());
            builder.queryParam("suwellsign", sign);
        }

        return URLEncoder.encode(builder.toUriString(), Charsets.UTF_8);
    }

    private String getWebUrl(Long docId, Long recordId) throws UnsupportedEncodingException {
        String serverEndpointPrefix = serverBasePathConfig.buildServerUrl(false);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverEndpointPrefix);
        builder.path("/record/v1/readCallBack/info");
        builder.queryParam("docId", docId);
        builder.queryParam("recordId", recordId);
        builder.queryParam("userId", SecurityUtils.getUserId());
        builder.queryParam("timestamp", System.nanoTime());
        if (totpUtil.isCheckSign()) {
            //生成加密签名
            String sign = totpUtil.generateMyTOTP(docId.toString(), SecurityUtils.getUserId().toString());
            builder.queryParam("suwellsign", sign);
        }
        return URLEncoder.encode(builder.toUriString(), Charsets.UTF_8);
    }

    private AiMdValueDTO getAiMdValueDTO(MetaDataValueInfoResp o) {
        if (Objects.isNull(o)) {
            log.error("文档类型没有配置该元数据");
            return null;
        }
        AiMdValueDTO aiMdValueDTO = new AiMdValueDTO();
        aiMdValueDTO.setDataType(o.getDataType());
        aiMdValueDTO.setOptView(o.getOptView());
        aiMdValueDTO.setOptEdit(o.getOptEdit());
        aiMdValueDTO.setAliasName(o.getAliasName());
        aiMdValueDTO.setAliasNameView(o.getAliasNameView());
        aiMdValueDTO.setDataList(StringUtils.isNotBlank(o.getMdValue())
                ? JSON.parseObject(o.getMdValue(), new TypeReference<List<String>>() {
        }) : Lists.newArrayList());
        return aiMdValueDTO;
    }

    @DSTransactional
    @Override
    public void edit(DocProcessEditReq req) {
        log.info("req:{}", JSON.toJSONString(req));
//        if (Integer.valueOf(SW_BACKEND.getCode()).equals(req.getStoreWay()) && Objects.nonNull(req.getPermit())
//                && req.getPermit().equals(Integer.valueOf(StringPool.ONE))) {
//            AssertUtils.isFalse(CollUtil.isEmpty(req.getRepoIdList()), METADATA_RECORD_EXCEPTION,
//                    FIXED_METADATA_REPO_POSITION.getName());
//        }

        String key = String.format(RETRY_OPT_RECORD_ID_KEY, req.getRecordId() + StringPool.UNDERSCORE);
        try {
            // 添加幂等性校验，防止接口重复调用
            if (Boolean.TRUE.equals(redisService.hasKey(key))) {
                log.info("req={} ,正在重试中。。。", req.getRecordId());
                return;
            }
            redisService.setEx(key, StringPool.ONE, 10, TimeUnit.SECONDS);

            final DocProcess docProcess = docProcessService.getByRecordId(req.getRecordId());
            AssertUtils.isFalse(Objects.isNull(docProcess), RECORD_DOC_PROCESS_NOT_EXIST);

            recordMangerService.editBizRecordMetadataList(req, docProcess, true);
        } catch (Exception e) {
            log.error("编辑异常！", e);
            redisService.delete(key);
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            throw new RuntimeException(e);
        }

    }

    @Override
    public Boolean canView(DocProcessCanViewReq req) {
        final DocProcess docProcess = docProcessService.getByRecordId(req.getRecordId());
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        final Long count = documentService.countByBatchType(req.getRecordId(),
                RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode());
        return count != 0;
    }

    @Override
    public DocInfoResp view(DocProcessViewReq req, boolean personalRepoFlag) {
        log.info("req:{}", JSON.toJSONString(req));
        // 文件权限安检
        if (req.getFormType().equals(Integer.valueOf(StringPool.ONE))) {
            AssertUtils.isFalse(Objects.isNull(recordService.getOneById(req.getRecordId())),
                    RECORD_DOC_PROCESS_NOT_EXIST);
            DocProcessQueryV2Req queryV2Req = new DocProcessQueryV2Req();
            queryV2Req.setRecordId(req.getRecordId());
            queryV2Req.setForSecure(req.isForSecure());
            queryV2Req.setPage(1);
            queryV2Req.setPageSize(5);
            queryV2Req.setPersonalRepoFlag(personalRepoFlag);
            PageUtils<DocProcessResp> pageUtils = queryPage(queryV2Req);
            boolean flag = Optional.ofNullable(pageUtils.getList()).map(l -> {
                if (CollUtil.isEmpty(l)) {
                    return false;
                } else {
//                    boolean b = l.stream().anyMatch(o -> o.getProcessSubStatus() == ProcessSubStatusEnum.PSS_PENDING_REVIEW.getCode()
//                            && !o.isUploadUser());
//                    return !b;
                    return true;
                }
            }).orElse(false);
            // 该用户没有权限
            AssertUtils.isFalse(!flag, RECORD_USER_NOT_PMS);
        }
        if (Integer.valueOf(2).equals(req.getFormType())) {
            validatorFactory.getValidator(ResourceType.RECORD).validate(req.getRecordId(),
                    PermissionMask.PERMISSION_SET);
        }
        if (Integer.valueOf(3).equals(req.getFormType())) {
            DocProcessFrontQueryReq frontQueryReq = new DocProcessFrontQueryReq();
            frontQueryReq.setRecordId(req.getRecordId());
            frontQueryReq.setStatus(req.getStatus());
            frontQueryReq.setPage(1);
            frontQueryReq.setPageSize(5);
            queryPageFront(frontQueryReq);
        }
        return editInfo(req);
    }

    @Override
    public DocInfoResp view(DocProcessViewReq req) {
        return view(req, false);
    }

    @Override
    public void uploadAttachment(DocProcessAttachmentFileReq req) {
//        final List<MultipartFile> attFiles = req.getAttFiles();
//        final List<Integer> attFileTypes = req.getAttFileTypes();
//        if (attFiles.size() != attFileTypes.size()) {
//            throw new RuntimeException("附件文件和附件类型数量不一致");
//        }
        BeanValidators.defaultValidate(req);
        final Record one = recordService.getOneById(req.getRecordId());
        if (one == null) {
            throw new RuntimeException("文档不存在");
        }

        List<DocProcessAttachmentFileReq.AttFileDto> attFileDtos = req.getAttFileDtos();
        final List<MultipartFile> attFiles = attFileDtos.stream()
                .map(DocProcessAttachmentFileReq.AttFileDto::getAttFile).collect(Collectors.toList());
        // 检查文件安全
        recordCommonUtils.checkFileSecurity(attFiles, UPLOAD_ATTACHMENT.getCode());
        documentService.saveAttachments(req);
    }

    @Override
    @DSTransactional
    public void check(DocProcessCheckReq req) {
        // 审核通过：入库  审核不通过:
        final DocProcess docProcess = docProcessService.getByRecordId(req.getRecordId());
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }
        final String userId = SecurityUtils.getUserId();
        final R<SysUser> r = userRpcService.queryById(userId);
        if (Boolean.FALSE.equals(r.isSuccess()) || r.getData() == null) {
            throw new ServiceException("获取用户信息失败");
        }
        final String nickName = r.getData().getNickName();

        final Date nowDate = new Date();
        final Integer status = req.getStatus();
        AuditMessageReq messageReq = new AuditMessageReq();

        if (DocProcessStatusEnums.AuditResultStatusEnum.ARS_PASS.getCode() == status) {
            // 审核通过，流转到下一节点
            standardTaskFacade.continueFlow(ContinueFlowDto.builder()
                    .taskDocId(docProcess.getTaskDocId())
                    .planId(docProcess.getPlanId())
                    .origin(docProcess.getOrigin())
                    .flag(false)
                    .storeWay(docProcess.getStoreWay())
                    .recordId(docProcess.getRecordId())
                    .build());
            messageReq.setPass(true);
            messageReq.setTitle(String.format(TypeEnum.MESSAGE_IN_STOCK_PASS.getDec(), docProcess.getName()));
            messageReq.setContent(String.format(TypeEnum.MESSAGE_IN_STOCK_PASS.getContent(), docProcess.getName()));
        } else {
            if (CharSequenceUtil.isBlank(req.getCause())) {
                throw new ServiceException("请填写驳回原因");
            }
            // 审核不通过
            LambdaUpdateWrapper<DocProcess> wrapper = Wrappers.<DocProcess>lambdaUpdate()
                    .set(DocProcess::getProcessStatus,
                            ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode())
                    .set(DocProcess::getProcessSubStatus,
                            ProcessSubStatusEnum.PSS_FAILED_AUDIT.getCode())
                    .set(DocProcess::getModifiedTime, nowDate)
                    .eq(DocProcess::getRecordId, docProcess.getRecordId());
            docProcessService.updateByWrapper(List.of(req.getRecordId()), wrapper);

            MqDocProcessBaseDTO mqDto = new MqDocProcessBaseDTO(
                    docProcess.getRecordId(),
                    DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode(),
                    DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FAILED_AUDIT.getCode(),
                    nowDate.getTime());
            docProcessESService.updateEsDocProcessBase(mqDto,true);

            // 发送不通过消息给文件上传人
            messageReq.setPass(false);
            messageReq.setTitle(String.format(TypeEnum.MESSAGE_IN_STOCK_REJECT.getDec(), docProcess.getName()));
            messageReq.setContent(
                    String.format(TypeEnum.MESSAGE_IN_STOCK_REJECT.getContent(), docProcess.getName(), req.getCause()));
            messageReq.setRejectReason(req.getCause());
        }

        // 记录当前登录人的审批信息
        DocAudit docAudit = new DocAudit();
        docAudit.setId(wfgIdGenerator.next());
        docAudit.setRecordId(docProcess.getRecordId());
        docAudit.setUserId(userId);
        docAudit.setUserNickName(nickName);
        docAudit.setStatus(status);
        docAudit.setCreateTime(nowDate);
        docAudit.setModifiedTime(nowDate);
        docAudit.setRejectReason(req.getCause());
        docAuditService.save(docAudit);
        // 将其他审批人的未读个人消息置为删除；修改当前登录人的信息为已读

        messageReq.setRecordId(docProcess.getRecordId());
        messageReq.setAuditUserId(userId);
        messageReq.setUploadUserId(docProcess.getCreateBy());

        final R<Void> voidR = messageRpcService.auditEnd(messageReq);
        log.info("驳回msg:{}，返回:{}", messageReq, JSON.toJSONString(voidR));
    }


    /**
     * 批量审核
     */

    @Override
    @DSTransactional
    public void batchCheck(DocProcessBatchCheckReq req) {
        // 审核通过：入库  审核不通过:
        // 批量入库
        final List<DocProcess> docProcessList = docProcessService.listWithWrapper(req.getRecordIds(),null);

        if (CollUtil.isEmpty(docProcessList)) {
            throw new RuntimeException("文档处理记录不存在");
        }

        final String userId = SecurityUtils.getUserId();
        final R<SysUser> r = userRpcService.queryById(userId);
        if (Boolean.FALSE.equals(r.isSuccess()) || r.getData() == null) {
            throw new ServiceException("获取用户信息失败");
        }
        final String nickName = r.getData().getNickName();

        final Date nowDate = new Date();
        final Integer status = req.getStatus();

        final ArrayList<DocAudit> docAudits = new ArrayList<>();

        final ArrayList<AuditMessageReq> auditMessageReqs = new ArrayList<>();

        for (DocProcess docProcess : docProcessList) {
            AuditMessageReq messageReq = new AuditMessageReq();

            if (DocProcessStatusEnums.AuditResultStatusEnum.ARS_PASS.getCode() == status) {
                // 审核通过，流转到下一节点
                standardTaskFacade.continueFlow(ContinueFlowDto.builder()
                        .taskDocId(docProcess.getTaskDocId())
                        .planId(docProcess.getPlanId())
                        .origin(docProcess.getOrigin())
                        .flag(false)
                        .storeWay(docProcess.getStoreWay())
                        .recordId(docProcess.getRecordId())
                        .build());
                messageReq.setPass(true);
                messageReq.setTitle(String.format(TypeEnum.MESSAGE_IN_STOCK_PASS.getDec(), docProcess.getName()));
                messageReq.setContent(String.format(TypeEnum.MESSAGE_IN_STOCK_PASS.getContent(), docProcess.getName()));
            } else {
                if (CharSequenceUtil.isBlank(req.getCause())) {
                    throw new ServiceException("请填写驳回原因");
                }
                // 审核不通过

                // 发送不通过消息给文件上传人
                messageReq.setPass(false);
                messageReq.setTitle(String.format(TypeEnum.MESSAGE_IN_STOCK_REJECT.getDec(), docProcess.getName()));
                messageReq.setContent(
                        String.format(TypeEnum.MESSAGE_IN_STOCK_REJECT.getContent(), docProcess.getName(),
                                req.getCause()));
                messageReq.setRejectReason(req.getCause());
            }

            // 记录当前登录人的审批信息
            DocAudit docAudit = new DocAudit();
            docAudit.setId(wfgIdGenerator.next());
            docAudit.setRecordId(docProcess.getRecordId());
            docAudit.setUserId(userId);
            docAudit.setUserNickName(nickName);
            docAudit.setStatus(status);
            docAudit.setCreateTime(nowDate);
            docAudit.setModifiedTime(nowDate);
            docAudit.setRejectReason(req.getCause());
            docAudits.add(docAudit);

            // 将其他审批人的未读个人消息置为删除；修改当前登录人的信息为已读
            messageReq.setRecordId(docProcess.getRecordId());
            messageReq.setAuditUserId(userId);
            messageReq.setUploadUserId(docProcess.getCreateBy());

            auditMessageReqs.add(messageReq);
        }

        // 审核不通过批量更新状态

        if (DocProcessStatusEnums.AuditResultStatusEnum.ARS_PASS.getCode() == status) {

        } else {

            final List<Long> ids = req.getRecordIds().stream().toList();
            // 审核不通过 批量更新
            docProcessService.updateByWrapper(ids, Wrappers.<DocProcess>lambdaUpdate()
                    .set(DocProcess::getProcessStatus,
                            DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode())
                    .set(DocProcess::getProcessSubStatus,
                            DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FAILED_AUDIT.getCode())
                    .set(DocProcess::getModifiedTime, nowDate)
//                    .set(DocProcess::getDeleted, CommonDeleteEnum.CD_NORMAL.getCode())
                    .in(DocProcess::getRecordId, ids));

            for (Long recordId : ids) {
                MqDocProcessBaseDTO mqDto = new MqDocProcessBaseDTO(
                        recordId,
                        DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode(),
                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FAILED_AUDIT.getCode(),
                        nowDate.getTime());
                docProcessESService.updateEsDocProcessBase(mqDto,true);
            }
        }
        final R<Void> voidR = messageRpcService.batchAuditEnd(auditMessageReqs);
        log.info("批量审核返回:{}", JSON.toJSONString(voidR));

        // 批量记录 记录当前登录人的审批信息

        docAuditService.saveBatch(docAudits);


    }

    @Override
    public Boolean findTask(TaskFlowUpdateReq req) {
        LambdaQueryWrapper<TaskDoc> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDoc::getId, req.getTaskDocId());
        TaskDoc one = taskDocService.getOneWithWrapper(req.getRecordId(), wrapper);

        return one != null && (
                personalRepoProcessService.getUpdateFlag(req.getRecordId(),req.getOriginalFileId())
        );
    }

    @Override
    public BaseMetadataInfoResp listBaseMetadataInfo(BaseMetadataInfoReq req) {
        log.info("listBaseMetadataInfo-req:{}", req);
        BaseMetadataInfoResp resp = new BaseMetadataInfoResp();

        List<RecordTypeMetadata> recordTypeMetadataList = recordTypeMetadataService.list(
                Wrappers.<RecordTypeMetadata>query().lambda().eq(RecordTypeMetadata::getRecordtypeId, req.getRecordTypeId()));

        List<MetaDataValueInfoResp> list = recordTypeMetadataList.stream().map(recordTypeMetadata -> {

            MetaDataValueInfoResp metaDataValueInfoResp = new MetaDataValueInfoResp();
            metaDataValueInfoResp.setMdId(recordTypeMetadata.getMdId());
            metaDataValueInfoResp.setMdName(recordTypeMetadata.getMdName());
            metaDataValueInfoResp.setValueType(recordTypeMetadata.getValueType());
            metaDataValueInfoResp.setValueRange(recordTypeMetadata.getValueRange());
            metaDataValueInfoResp.setRequired(recordTypeMetadata.getRequired());
            metaDataValueInfoResp.setOrderby(recordTypeMetadata.getOrderby());
            metaDataValueInfoResp.setOptEdit(recordTypeMetadata.getOptEdit());
            metaDataValueInfoResp.setOptView(recordTypeMetadata.getOptView());
            metaDataValueInfoResp.setDefine(
                    JSON.parseObject(recordTypeMetadata.getValueRule(), MetadataValueDefineReq.class));
            metaDataValueInfoResp.setComment(recordTypeMetadata.getRemark());
            metaDataValueInfoResp.setAliasName(recordTypeMetadata.getAliasName());
            metaDataValueInfoResp.setAliasNameView(recordTypeMetadata.getAliasNameView());

            return metaDataValueInfoResp;
        }).filter(o->{
            if (plssFixedMetadataNameConfig.getFixedKeywordName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedDateName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedLocationName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedPersonName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedOrganizationName().equals(o.getMdName())) {
                return false;
            }
            if (plssFixedMetadataNameConfig.getFixedRepoPositionName().equals(o.getMdName())) {
                return false;
            }
            if(plssFixedMetadataNameConfig.getDocTitleName().equals(o.getMdName())){
                return false;
            }
            return true;
        }).toList();
        resp.setMetaDataValueList(list);
        return resp;
    }

    @Override
    public void batchSetMetadataValue(BatchSetMetadataValReq req) {
        log.info("batchSetMetadataValue-req:{}", req);
        List<Long> recordIdList = req.getRecordIdList();

        List<DocProcess> processList = docProcessService.listWithWrapper(recordIdList, Wrappers.<DocProcess>lambdaQuery()
                .select(DocProcess::getRecordTypeId));
        Set<Long> idSet = processList.stream().map(DocProcess::getRecordTypeId).collect(Collectors.toSet());

        if(idSet.size()!=1){
            throw new ServiceException("批量设置元数据值，文档类型不一致");
        }
        List<DocProcessEditReq.MdValueDTO> metadata = req.getMetadata();
        metadata = metadata.stream().filter(m -> {
            return StrUtil.isNotBlank(m.getMdValue());
        }).toList();

        if(CollUtil.isEmpty(metadata)){
            throw new ServiceException("批量设置元数据值，传入值为空");
        }

        BaseMetadataInfoReq infoReq = new BaseMetadataInfoReq();
        infoReq.setRecordTypeId(processList.get(0).getRecordTypeId());
        BaseMetadataInfoResp resp = listBaseMetadataInfo(infoReq);
        List<MetaDataValueInfoResp> metaDataValueList = resp.getMetaDataValueList();
        Map<Long, MetaDataValueInfoResp> valMap = metaDataValueList.stream().collect(Collectors.toMap(o -> o.getMdId(), o -> o));
        for (DocProcessEditReq.MdValueDTO dto : metadata) {
            if(valMap.containsKey(dto.getMdId())){
                if(!Objects.equals(dto.getMdName(),valMap.get(dto.getMdId()).getMdName())) {
                    throw new ServiceException("批量设置元数据值，传入值不在文档类型中");
                }
            }else{
                throw new ServiceException("批量设置元数据值，传入值不在文档类型中");
            }
        }

        req.setFinishRecordIdSet(null);
        dataProcessTaskService.addTask(
                DataProcessTaskEnum.BusinessType.BATCH_SET_METADATA_VALUE,
                0,
                JSON.toJSONString(req),
                Long.parseLong(recordIdList.size()+""),
                0L
        );
    }

    @Override
    @DSTransactional
    public void batchSetRepoPosition(RecordAppendRepoPositionReq req) {
        log.info("batchSetRepoPosition-req:{}", req);

        List<Long> recordIdList = req.getRecordIdList();
        List<DocProcess> processList = docProcessService.listWithWrapper(recordIdList, null);

        if(CollUtil.isEmpty(processList)){
            return;
        }

        Map<Integer, List<Long>> listMap = processList.stream().collect(Collectors.groupingBy(o -> {
            if (o.getProcessStatus() == ProcessStatusEnum.PS_IN_STOCK.getCode()) {
                return 1;
            } else {
                return 2;
            }
        },Collectors.mapping(DocProcess::getRecordId,Collectors.toList())));

        Optional.ofNullable(listMap.get(1)).ifPresent(l->{
            FolderMultiRecordReq recordReq = new FolderMultiRecordReq();
            recordReq.setRecordIdList(l);
            recordReq.setFolderOrRepoIdList(req.getRepoIdList());
            recordReq.setValidPass(true);
            folderService.addMultiFolders(recordReq);
        });

        Optional.ofNullable(listMap.get(2)).ifPresent(l->{
            req.setRecordIdList(l);
            standardRecordFacade.appendRecordRepoPosition(req);
            // 增加待入库记录
            List<Long> repoIdList = folderService.queryRelations(req.getRepoIdList())
                    .stream()
                    .map(f -> f.getFolderChain().get(0).getRepoId())
                    .distinct().toList();
            Optional.of(repoIdList).ifPresent(o->o.stream().filter(Objects::nonNull).forEach(repoId-> {
                l.forEach(recordId->{
                    repoDocService.appendRecordToRepo(recordId,repoId);
                });
            }));
        });
    }

    @Override
    public boolean hasRecordInRepo(Long folderId) {
        return repoDocService.hasRecordInRepo(folderId);
    }

    @Override
    public Set<Object> getRecordIdsInRepo(Long folderId) {
        return repoDocService.getRecordIdsInRepo(folderId);
    }

    @Override
    public void removeRecordFromRepo(Long folderId, Set<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        repoDocService.removeRecordFromRepo(folderId, recordIds);
    }

    @Override
    public DocRejectReasonResp listRejectReason(DocRejectReasonReq req) {

        DocRejectReasonResp resp = new DocRejectReasonResp();
        final List<DocAudit> list = docAuditService.lambdaQuery()
                .eq(DocAudit::getRecordId, req.getRecordId())
                .eq(DocAudit::getStatus, DocProcessStatusEnums.AuditResultStatusEnum.ARS_REJECTION.getCode())
                .orderByDesc(DocAudit::getCreateTime)
                .list();

        if (CollUtil.isEmpty(list)) {
            return resp;
        }
        final Set<String> userIdSet = list.stream().map(DocAudit::getUserId).collect(Collectors.toSet());
        final R<List<SysUser>> batchInfo = userRpcService.getBatchInfo(CollUtil.newArrayList(userIdSet));
        if (Boolean.TRUE.equals(batchInfo.isSuccess())) {
            Map<String, String> map = Optional.ofNullable(batchInfo.getData()).map(l -> l.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName))).orElseGet(HashMap::new);

            for (DocAudit docAudit : list) {
                Date createTime = docAudit.getCreateTime();
                String format = DateUtil.format(createTime, DatePattern.NORM_DATETIME_PATTERN);
                String[] s = format.split(" ");
                resp.addDto(s[0], DocRejectReasonResp.DocRejectReasonDto.builder()
                        .time(s[1])
                        .nickName(map.getOrDefault(docAudit.getUserId(), docAudit.getUserNickName()))
                        .userId(docAudit.getUserId())
                        .reason("意见：" + docAudit.getRejectReason())
                        .build());
            }
            return resp;
        } else {
            throw new ServiceException("获取用户信息失败");
        }
    }

    @Override
    public DocProcessSplitInfoResp splitBefore(DocProcessSplitInfoReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        DocProcessSplitInfoResp resp = new DocProcessSplitInfoResp();
        resp.setRecordId(recordId);

        // 暂时不需要展示 上一个下一个
//        final List<DocProcess> list = docProcessService.lambdaQuery()
//                .eq(DocProcess::getBatchId, docProcess.getBatchId())
//                .in(DocProcess::getProcessSubStatus,
//                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_TO_BE_SPLIT.getCode(),
//                        DocProcessStatusEnums.ProcessSubStatusEnum.PSS_SPLIT_ANOMALY.getCode(),
//                        docProcess.getProcessSubStatus())
//                .eq(DocProcess::getCtype, DocProcessStatusEnums.DocProcessTypeEnum.DPT_MASTER.getCode())
//                .orderByAsc(DocProcess::getId)
//                .list();

//        Map<Long, Integer> map = new HashMap<>();
//        Map<Integer, Long> indexMap = new HashMap<>();
//        for (int i = 0; i < list.size(); i++) {
//            map.put(list.get(i).getRecordId(), i);
//            indexMap.put(i, list.get(i).getRecordId());
//        }
//
//        final Integer index = map.get(recordId);
//        if (index != 0) {
//            resp.setPreRecordId(indexMap.get(index - 1));
//        }
//        if (index != list.size() - 1) {
//            resp.setNextRecordId(indexMap.get(index + 1));
//        }

        final Document one = documentService.getMasterDocByRecordId(recordId);
        Optional.ofNullable(one).map(o -> {
            try {
                return getWebUrl(o);
            } catch (UnsupportedEncodingException e) {
                log.error("获取webUrl失败", e);
                return null;
            }
        }).ifPresent(resp::setWebUrl);
        return resp;
    }

    @Override
    @DSTransactional
    public void split(DocProcessSplitReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        // 改成拆分中，发送事件
//        Document document = documentService.getOne(Wrappers.<Document>query().lambda()
//                .eq(Document::getCtype, RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode())
//                .eq(Document::getRecordId,req.getRecordId()));

        // 直接调用拆分节点，执行对应逻辑
        final PlanNodeDto planNodeDto = standardTaskFacade.getNodeIdByNodeType(docProcess.getPlanId(),
                NodeTypeEnum.NT_FILE_SPLIT.getCode());
        TaskNodeReq taskReq = new TaskNodeReq();
        taskReq.setCurrentNodeId(planNodeDto.getNodeId());
        taskReq.setBeforeNodeId(planNodeDto.getBeforeNodeId());
        taskReq.setRecordId(docProcess.getRecordId());
        taskReq.setTaskDocId(docProcess.getTaskDocId());
        taskReq.setPlanId(docProcess.getPlanId());
        taskReq.setOrigin(docProcess.getOrigin());
        taskReq.setUserId(docProcess.getCreateBy());
        taskReq.setTenantId(docProcess.getTenantId());
        taskReq.setNickName(docProcess.getCreateByName());
        taskReq.setDirect(true);
        taskReq.setSplitType(req.getSplitType());
        taskReq.setLevel(req.getLevel());
        final AbstractNodeHandler impl = nodeHandlerFactory.getHandlerImpl(NodeTypeEnum.NT_FILE_SPLIT);
        impl.executeLogic(taskReq);
        // 调用api5拆分
//        final TaskDoc one = taskDocService.lambdaQuery().eq(TaskDoc::getId, docProcess.getTaskDocId()).one();
//        saveTaskDocList(one.getTaskId(), document.getOfdFileId(),docProcess.getOrigin(),req.getLevel(),req.getSplitType());
    }

    @Override
    @DSTransactional
    public void saveTaskDocList(DocProcessSplitTaskReq req) {

        final List<DocProcessSplitTaskReq.FileDto> list = req.getList();
        if (CollUtil.isEmpty(list)) {
            throw new RuntimeException("拆分文件数为0，拆分失败");
        }
        final Long taskId = req.getTaskId();
        LambdaQueryWrapper<TaskDoc> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDoc::getTaskId, taskId)
                .eq(TaskDoc::getCtype, TaskDocTypeEnum.TDT_MAIN.getCode());
        TaskDoc mainTaskDoc = taskDocService.getOneWithWrapper(req.getRecordId(), wrapper);

        String subRecordIds = mainTaskDoc.getSubRecordIds();
        if (StringUtils.isNotBlank(subRecordIds)) {
            List<Long> subRecordIdList = Arrays.stream(subRecordIds.split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());
            // 删除所有子文件
            LambdaUpdateWrapper<TaskDoc> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TaskDoc::getTaskId, taskId)
                    .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
                            TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode());
            taskDocService.deleteByWrapper(subRecordIdList, updateWrapper);
            documentService.deleteByRecordIds(subRecordIdList);
            recordService.deleteByIds(subRecordIdList);
        }

//        // 删除所有子文件
//        final List<Long> oldList = taskDocService.lambdaQuery()
//                .select(TaskDoc::getRecordId)
//                .eq(TaskDoc::getTaskId, taskId)
//                .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
//                        TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode())
//                .list().stream().map(TaskDoc::getRecordId).collect(Collectors.toList());
//        taskDocService.remove(Wrappers.<TaskDoc>query().lambda()
//                .eq(TaskDoc::getTaskId, taskId)
//                .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
//                        TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode())
//        );
//        if (CollUtil.isNotEmpty(oldList)) {
//            documentService.deleteByRecordIds(oldList);
//            recordService.deleteByIds(oldList);
//        }
        // 保存文件到 taskDoc表
        final Date nowDate = new Date();
        final DocProcess docProcess = docProcessService.getByRecordId(req.getRecordId());
        TaskNodeReq taskReq = new TaskNodeReq();
        taskReq.setPlanId(docProcess.getPlanId());
        taskReq.setOrigin(docProcess.getOrigin());
        taskReq.setUserId(docProcess.getCreateBy());
        taskReq.setTenantId(docProcess.getTenantId());
        taskReq.setNickName(docProcess.getCreateByName());
        final AbstractNodeHandler impl = nodeHandlerFactory.getHandlerImpl(NodeTypeEnum.NT_UPLOAD);

        List<Long> recordIdList = new ArrayList<>(list.size());
        final List<TaskDoc> docList = list.stream()
                .sorted(
//                        Comparator.comparing(DocProcessSplitTaskReq.FileDto::getFileNo)
                        (o1,o2)->{
                            if(o1.getFileNoInt() != null && o2.getFileNoInt() != null){
                                return o1.getFileNoInt().compareTo(o2.getFileNoInt());
                            }
                            return o1.getFileNo().compareTo(o2.getFileNo());
                        }
                ).map(o -> {
                    final StorageInfoV3Dto dto = new StorageInfoV3Dto();
                    dto.setFileId(o.getFileId());
                    dto.setFileName(o.getFileName());
                    dto.setCtype(TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode());
                    dto.setDocId(wfgIdGenerator.next());

                    final TaskDoc taskDoc = new TaskDoc();
                    taskDoc.setId(wfgIdGenerator.next());
                    taskDoc.setDocId(dto.getDocId());
                    taskDoc.setName(o.getFileName());
                    taskDoc.setTaskId(taskId);
                    taskDoc.setCtype(dto.getCtype());
                    // 子文件recordId重新生成
                    taskDoc.setRecordId(wfgIdGenerator.next());
                    taskDoc.setStorageInfo(JSON.toJSONString(dto));
                    taskDoc.setCreateTime(nowDate);
                    taskDoc.setModifiedTime(nowDate);
                    // 生成document 和 record
                    impl.realExecuteLogic(taskReq, taskDoc);
                    //
                    recordIdList.add(taskDoc.getRecordId());
                    return taskDoc;
                }).collect(Collectors.toList());
        taskDocService.saveBatch(docList);
        taskDocService.updateByWrapper(List.of(mainTaskDoc.getRecordId()), Wrappers.<TaskDoc>lambdaUpdate()
                .set(TaskDoc::getSubRecordIds, CollUtil.join(recordIdList, ","))
                .eq(TaskDoc::getId, mainTaskDoc.getId()));
    }


    @Override
    public DocProcessSplitResultResp splitResult(DocProcessSplitResultReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        DocProcessSplitResultResp resp = new DocProcessSplitResultResp();
        resp.setRecordId(recordId);
        resp.setFileName(docProcess.getName());
        LambdaQueryWrapper<TaskDoc> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDoc::getId, docProcess.getTaskDocId());
        final TaskDoc taskDoc = taskDocService.getOneWithWrapper(recordId, wrapper);

        String subRecordIds = taskDoc.getSubRecordIds();

        final List<TaskDoc> list = Optional.ofNullable(subRecordIds).map(o -> {
            if (StrUtil.isBlank(o)) {
                return null;
            } else {
                List<Long> recordIdList = Arrays.stream(o.split(",")).map(Long::parseLong).collect(Collectors.toList());
                LambdaQueryWrapper<TaskDoc> listW = new LambdaQueryWrapper<>();
                listW.eq(TaskDoc::getTaskId, taskDoc.getTaskId())
                        .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
                                TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode())
                        .orderByAsc(TaskDoc::getId);
                return taskDocService.listWithWrapper(recordIdList, listW);
            }
        }).orElseGet(ArrayList::new);

        resp.addDtos(list.stream().sorted(Comparator.comparing(TaskDoc::getId)).map(o -> {
            DocProcessSplitResultResp.TaskDocDto dto = new DocProcessSplitResultResp.TaskDocDto();
            dto.setRecordId(o.getRecordId());
            dto.setTaskDocId(o.getId());
            dto.setTaskDocName(o.getName());
            try {
                dto.setWebUrl(getWebUrl(o.getDocId(), o.getRecordId()));
            } catch (UnsupportedEncodingException e) {
                log.error("获取webUrl失败", e);
            }
            return dto;
        }).collect(Collectors.toList()));

        final Document one = documentService.getMasterDocByRecordId(recordId);
        Optional.ofNullable(one).map(o -> {
            try {
                return getWebUrl(o);
            } catch (UnsupportedEncodingException e) {
                log.error("获取webUrl失败", e);
                return null;
            }
        }).ifPresent(resp::setWebUrl);

        return resp;
    }

    @Override
    public void deleteSub(DocProcessDeleteSubReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        LambdaQueryWrapper<TaskDoc> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDoc::getCtype, TaskDocTypeEnum.TDT_MAIN.getCode());
        TaskDoc mainTaskDoc = taskDocService.getOneWithWrapper(recordId, wrapper);

        String subRecordIds = mainTaskDoc.getSubRecordIds();
        Set<Long> recordIdSet = Arrays.stream(subRecordIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        recordIdSet.remove(req.getSubRecordId());

        // 删除子文件
//        final List<Long> oldList = taskDocService.lambdaQuery().select(TaskDoc::getRecordId)
//                .eq(TaskDoc::getRecordId, 0L)
//                .eq(TaskDoc::getId, req.getTaskDocId())
//                .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
//                        TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode())
//                .list().stream().map(TaskDoc::getRecordId).collect(Collectors.toList());

        taskDocService.updateByWrapper(List.of(recordId), Wrappers.<TaskDoc>lambdaUpdate()
                .set(TaskDoc::getSubRecordIds, CollUtil.join(recordIdSet, ","))
                .eq(TaskDoc::getId, mainTaskDoc.getId()));
        taskDocService.deleteByWrapper(List.of(req.getSubRecordId()), Wrappers.<TaskDoc>lambdaUpdate()
                .eq(TaskDoc::getId, req.getTaskDocId())
                .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
                        TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode()));
        final List<Long> oldList = CollUtil.newArrayList(req.getSubRecordId());
        if (CollUtil.isNotEmpty(oldList)) {
            documentService.deleteByRecordIds(oldList);
            recordService.deleteByIds(oldList);
        }
    }

    @Override
    @DSTransactional
    public void replaceSub(DocProcessReplaceSubReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }

        TaskDoc mainTaskDoc = taskDocService.getOneWithWrapper(recordId, Wrappers.<TaskDoc>lambdaQuery()
                .eq(TaskDoc::getCtype, TaskDocTypeEnum.TDT_MAIN));

        String subRecordIds = mainTaskDoc.getSubRecordIds();
        Set<Long> recordIdSet = Arrays.stream(subRecordIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        recordIdSet.remove(req.getSubRecordId());

        // 删除子文件
        final TaskDoc one = taskDocService.getOneWithWrapper(req.getSubRecordId(), Wrappers.<TaskDoc>lambdaQuery()
                .eq(TaskDoc::getRecordId, req.getSubRecordId())
                .eq(TaskDoc::getId, req.getTaskDocId())
                .in(TaskDoc::getCtype, TaskDocTypeEnum.TDT_SPLIT_SUB_FILE.getCode(),
                        TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode()));
        taskDocService.deleteByWrapper(List.of(one.getRecordId()), Wrappers.<TaskDoc>lambdaUpdate()
                .eq(TaskDoc::getId, one.getId()));
        documentService.deleteByRecordIds(List.of(one.getRecordId()));
        recordService.deleteByIds(List.of(one.getRecordId()));

        // 上传子文件
        final MultipartFile file = req.getFile();
        final String fileName = file.getOriginalFilename();
        UploadReq uploadReqCnt = new UploadReq();
        uploadReqCnt.setFileData(file);
        uploadReqCnt.setOriginName(fileName);
        uploadReqCnt.setRequireEncrypt(true);
        uploadReqCnt.setFileType(RecordEnum.OriginEnum.ORIGIN_INLAY_DATA.getCode().equals(docProcess.getOrigin())
                ? FILE_TYPE_INTERNALLY.getCode() : FILE_TYPE_PROJECT.getCode());
        UploadResp uploadResp = fileService.generalUpload(uploadReqCnt);
        // 新增子文件
        final StorageInfoV3Dto dto = new StorageInfoV3Dto();
        dto.setFileId(uploadResp.getId());
        dto.setFileName(fileName);
        dto.setCtype(TaskDocTypeEnum.TDT_UPLOAD_SUB_FILE.getCode());
        dto.setDocId(wfgIdGenerator.next());

        final Date nowDate = new Date();
        TaskDoc taskDoc = new TaskDoc();
        taskDoc.setId(req.getTaskDocId());
        taskDoc.setDocId(dto.getDocId());
        taskDoc.setName(fileName);
        taskDoc.setTaskId(one.getTaskId());
        taskDoc.setCtype(dto.getCtype());
        // 子文件recordId重新生成
        taskDoc.setRecordId(wfgIdGenerator.next());
        taskDoc.setStorageInfo(JSON.toJSONString(dto));
        taskDoc.setCreateTime(nowDate);
        taskDoc.setModifiedTime(nowDate);
        taskDocService.saveOne(taskDoc);

        TaskNodeReq taskReq = new TaskNodeReq();
        taskReq.setPlanId(docProcess.getPlanId());
        taskReq.setOrigin(docProcess.getOrigin());
        taskReq.setUserId(docProcess.getCreateBy());
        taskReq.setTenantId(docProcess.getTenantId());
        taskReq.setNickName(docProcess.getCreateByName());
        taskReq.setRecordId(taskDoc.getRecordId());
        final AbstractNodeHandler impl = nodeHandlerFactory.getHandlerImpl(NodeTypeEnum.NT_UPLOAD);
        // 生成document 和 record
        impl.realExecuteLogic(taskReq, taskDoc);

        recordIdSet.add(taskDoc.getRecordId());
        taskDocService.updateByWrapper(List.of(recordId), Wrappers.<TaskDoc>lambdaUpdate()
                .set(TaskDoc::getSubRecordIds, CollUtil.join(recordIdSet, ","))
                .eq(TaskDoc::getId, mainTaskDoc.getId()));
    }

    @Override
    @DSTransactional
    public void splitSubmit(DocProcessSplitSubmitReq req) {

        final Long recordId = req.getRecordId();
        final DocProcess docProcess = docProcessService.getByRecordId(recordId);
        if (docProcess == null) {
            throw new RuntimeException("文档处理记录不存在");
        }
        standardTaskFacade.splitSubmit(req, () -> docProcess);
        if (req.isCtlFlag()) {
            standardTaskFacade.continueFlow(ContinueFlowDto.builder()
                    .taskDocId(docProcess.getTaskDocId())
                    .planId(docProcess.getPlanId())
                    .origin(docProcess.getOrigin())
                    .flag(true)
                    .storeWay(StoreWayEnum.SW_BACKEND.getCode())
                    .recordId(docProcess.getRecordId())
                    .build());
        }

    }

    @Override
    public ResourceFrontResp submitStore(ResourceFrontReq req) {
        ResourceFrontResp resp = new ResourceFrontResp();
        final List<Long> parentIdList = req.getParentIdList();
        final List<Long> recordIds = req.getRecordIds();
        if (CollUtil.isEmpty(parentIdList) && CollUtil.isEmpty(recordIds)) {
            throw new RuntimeException("请选择要提交的文件");
        }

        final String userId = SecurityUtils.getUserId();
        final String tenantId = SecurityUtils.getTenantId();
        if (req.isBlock()) {
            // 推送队列
        } else {
            // 直接执行
            List<Long> recordIdList = new ArrayList<>();
            LambdaUtils.isPresent(recordIds, recordIdList::addAll);
            LambdaUtils.isPresent(parentIdList, o -> {
                List<Long> list = folderService.listAllRecordIds(o);
                recordIdList.addAll(list);
            });
            if (CollUtil.isEmpty(recordIdList)) {
                throw new RuntimeException("请选择要提交的文件");
            }
            LongAdder typeErrorCount = new LongAdder();
            // 需要过滤掉的md5，因为这些md5在 提交入库中已经存在
//            final Set<String> md5Set = documentMapper.queryFileMd5(recordIdList).stream()
//                    .map(RecordFrontDto::getFileMd5).collect(Collectors.toSet());
            // 走入库流程的所有md5
            final Set<String> md5Set = documentService.listMasterMD5ByRecordIds(recordIdList,
                    StoreProcessEnum.SP_WITH_IN_STORE.getCode());
            final List<RecordFrontDto> dtos = documentService.queryDocByRecordIds(recordIdList).stream().filter(
                    o -> {
                        // 不存在 则表示可以提交入库
                        boolean md5Flag = !md5Set.contains(o.getFileMd5());
                        boolean typeFlag = recordCommonUtils.checkFileType(o.getFileName(), req.getRecordTypeId(),
                                false);
                        if (md5Flag && !typeFlag) {
                            typeErrorCount.increment();
                        }
                        return md5Flag && typeFlag;
                    }
            ).collect(Collectors.toList());
            recordCommonUtils.remove();
//            AssertUtils.isTrue(CollUtil.isNotEmpty(dtos), RecordBizError.RECORD_DOC_EXIST,String.valueOf(recordIdList.size()));
            int effectCount = CollectionUtil.size(dtos);
            int totalCount = CollectionUtil.size(recordIdList);
            log.info("recordId size={}, docId size={}, {}", recordIdList.size(), totalCount,
                    recordIdList.size() == effectCount);
            resp.setTotalRecordCount(totalCount);
            resp.setEffectCount(effectCount);
            resp.setTypeErrorCount(typeErrorCount.intValue());
            // TODO 复制record，生成新record
            LambdaUtils.isPresent(dtos, o -> {
                o.forEach(dto -> {
                    RecordOfflineV3Req v3Req = new RecordOfflineV3Req();
                    v3Req.setUserId(userId);
                    v3Req.setTenantId(tenantId);
                    v3Req.setRecordTypeId(req.getRecordTypeId());
                    v3Req.setPlanId(req.getPlanId());
                    v3Req.setOrigin(dto.getOrigin());
                    v3Req.setRealOrigin(dto.getRealOrigin());
                    v3Req.setDataType(req.getDataType());
                    v3Req.setStoreWay(req.getStoreWay());

                    FileInfoOfflineV3Req info = new FileInfoOfflineV3Req();
                    v3Req.setFileInfoReqList(CollUtil.newArrayList(info));
                    info.setRecordId(wfgIdGenerator.next());
                    info.setPreRecordId(dto.getRecordId());
                    info.setRepoIdList(req.getRepoPositionList());

                    FileInfoBaseV3Req baseInfo = new FileInfoBaseV3Req();
                    info.setFileInfoBaseReqList(CollUtil.newArrayList(baseInfo));
                    baseInfo.setFileName(dto.getFileName());
                    baseInfo.setFileId(dto.getFileId());
                    baseInfo.setCtype(RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode());

                    standardTaskFacade.startTaskWithoutTransactional(v3Req);
                });
            });
        }
        return resp;
    }

    @Override
    public void submitStore(ResourceFrontBaseReq req) {
        if(CollUtil.isEmpty(req.getRecordIds())){
            throw new RuntimeException("请选择要提交的文件");
        }

        List<Long> recordIds = req.getRecordIds();
        List<Record> recordList = recordService.getListByIds(recordIds);
        recordList.forEach(r->{
            r.setStatus(PersonalRecordStatusEnums.RecordStatusEnum.PROCESSING.getCode());
        });
        recordService.modifyBatchById(recordList);

        Map<Long, Document> docMap = documentService.listByBatchType(req.getRecordIds(), RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .stream()
                .collect(Collectors.toMap(Document::getRecordId, Function.identity()));

        Map<Long, ResourceFrontBaseReq.DocumentInfo> reqDocMap = req.getDocMap();

        List<RecordFrontDto> dtos = recordService.listWithWrapper(req.getRecordIds(), null)
                .stream()
                .map(o -> {
                    Long recordId = o.getId();
                    RecordFrontDto dto = new RecordFrontDto();
                    Document doc = docMap.get(recordId);
                    if (reqDocMap!=null && reqDocMap.containsKey(recordId)) {
                        ResourceFrontBaseReq.DocumentInfo info = reqDocMap.get(recordId);
                        dto.setFileId(info.getFileId());
                        dto.setFileMd5(info.getFileMd5());
                    }else{
                        dto.setFileId(doc.getFileId());
                        dto.setFileMd5(doc.getFileMd5());
                    }
                    dto.setDocId(doc.getId());
                    dto.setFileName(doc.getName());
                    dto.setRecordId(recordId);
                    dto.setOrigin(o.getOrigin());
                    dto.setRealOrigin(o.getRealOrigin());
                    dto.setTenantId(o.getTenantId());
                    dto.setUserId(o.getCreateBy());
                    return dto;
                }).toList();

        LambdaUtils.isPresent(dtos, o -> {
            o.forEach(dto -> {
                RecordOfflineV3Req v3Req = new RecordOfflineV3Req();
                v3Req.setUserId(dto.getUserId());
                v3Req.setTenantId(dto.getTenantId());
                v3Req.setRecordTypeId(req.getRecordTypeId());
                v3Req.setPlanId(req.getPlanId());
                v3Req.setOrigin(dto.getOrigin());
                v3Req.setRealOrigin(dto.getRealOrigin());
                v3Req.setDataType(req.getDataType());
                v3Req.setStoreWay(req.getStoreWay());

                FileInfoOfflineV3Req info = new FileInfoOfflineV3Req();
                v3Req.setFileInfoReqList(CollUtil.newArrayList(info));
                // 个人库自动入库使用一样的recordId
                info.setRecordId(dto.getRecordId());
                info.setPreRecordId(dto.getRecordId());
                info.setRepoIdList(req.getRepoPositionList());

                FileInfoBaseV3Req baseInfo = new FileInfoBaseV3Req();
                info.setFileInfoBaseReqList(CollUtil.newArrayList(baseInfo));
                baseInfo.setFileName(dto.getFileName());
                baseInfo.setFileId(dto.getFileId());
                baseInfo.setDocId(dto.getDocId());
                baseInfo.setCtype(RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode());

                standardTaskFacade.startTaskWithoutTransactional(v3Req);
            });
        });
    }

    @Override
    public void submitStoreAtOnce(ResourceFrontV2Req req) {

        ResourceFrontBaseReq baseReq = new ResourceFrontBaseReq();
        baseReq.setRecordTypeId(recordPersonalConfig.getRecordTypeId());
        baseReq.setPlanId(nodeConfig.getPersonalDefaultPlanId());
        baseReq.setDataType(1);
        baseReq.setStoreWay(1);

        for (Long recordId : req.getRecordIds()) {

            Record record = recordService.getOneById(recordId);
            if(!Objects.equals(record.getStatus(),PersonalRecordStatusEnums.RecordStatusEnum.UPDATE.getCode())){
                log.warn("recordId:{}, 不能主动提交解析的状态：{}",recordId,record.getStatus());
                continue;
            }
            List<RetryMsg> list = retryMsgService.lambdaQuery()
                    .eq(RetryMsg::getBizType, PersonalRecordStatusEnums.RetryMsgBizTypeEnum.PERSONAL_RECORD_EDIT.getCode())
                    .eq(RetryMsg::getStatus, PersonalRecordStatusEnums.RetryMsgStatusEnum.WAIT.getCode())
                    .eq(RetryMsg::getRecordId,recordId)
                    .orderByDesc(RetryMsg::getCreateTime)
                    .list();

            if(CollUtil.isEmpty(list)){
                log.warn("recordId:{}, 不能主动提交解析,无编辑记录",recordId);
                continue;
            }
            RetryMsg one = list.remove(0);
            submitStore(one.getDocId(),recordId,one.getCreateBy(),baseReq);
            if(CollUtil.isNotEmpty(list)){
                retryMsgService.removeByIds(list.stream().map(RetryMsg::getDocId).toList());
            }
        }

    }

    @Override
    public void submitStore(Long id,Long recordId,String json,ResourceFrontBaseReq baseReq) {
        List<Long> folderOrRepoIdList = recordMetadataValueService.queryRecordMetadataRepoPositionList(recordId);
        baseReq.setRepoPositionList(folderOrRepoIdList);
        baseReq.setRecordIds(List.of(recordId));

        Map<Long, ResourceFrontBaseReq.DocumentInfo> baseDocMap = new HashMap<>();
        ResourceFrontBaseReq.DocumentInfo docInfo = JSON.parseObject(json, ResourceFrontBaseReq.DocumentInfo.class);
        baseDocMap.put(recordId, docInfo);
        baseReq.setDocMap(baseDocMap);

        // 删除之前的入库记录
        deleteDocProcessData(List.of(recordId));
        // 提交入库
        Document document = documentService.getMasterDocByRecordId(recordId);
        if(document == null){
            log.warn("recordId:{}, 不能主动提交解析,无主文档",recordId);
            return;
        }
        recordCommonUtils.sendMarkMqData(document.getId(), DP_AI_INDEXING_EXTRACT_MQ_REPEAT_CHECK.getKey(),false);
        submitStore(baseReq);
        retryMsgService.lambdaUpdate()
                .eq(RetryMsg::getDocId, id)
                .set(RetryMsg::getStatus,PersonalRecordStatusEnums.RetryMsgStatusEnum.IN_PROCESS.getCode())
                .update();
    }

    @Override
    public List<DocProcessCountByDateResp> processCountByDate(DocProcessCountByDateReq req) {
        BeanValidators.defaultValidate(req);
        return docProcessService.processCountByDate(req);
    }

    @Override
    public DocStateResp queryFileState(DocProcessViewReq req) {
        log.info("req:{}", JSON.toJSONString(req));
        DocStateResp resp = new DocStateResp();
        final DocProcess docProcess = docProcessService.getByRecordId(req.getRecordId());
        AssertUtils.notNull(docProcess, RECORD_DOC_PROCESS_NOT_EXIST);

        Record byId = recordService.getOneById(req.getRecordId());
        resp.setClassified(byId.getClassified());
        resp.setRecordId(docProcess.getRecordId());
        resp.setProcessStatus(docProcess.getProcessStatus());
        resp.setProcessSubStatus(docProcess.getProcessSubStatus());
        resp.setStoreWay(docProcess.getStoreWay());
        resp.setKnowledgeStatus(docProcess.getKnowledgeStatus());

        Long planId = docProcess.getPlanId();
//        PlanNode planNode = planNodeService.getOne(Wrappers.<PlanNode>query().lambda()
//                .eq(PlanNode::getPlanId, planId)
//                .eq(PlanNode::getNodeType, NodeTypeEnum.NT_UPLOAD.getCode()));

        List<PlanNode> planNodes = planNodeService.list(Wrappers.<PlanNode>query().lambda()
                .eq(PlanNode::getPlanId, planId)
                .in(PlanNode::getNodeType, Lists.newArrayList(NodeTypeEnum.NT_UPLOAD.getCode(),
                        NodeTypeEnum.NT_MANUAL_REVIEW.getCode())));

        PlanNode planNodeUpload = planNodes.stream()
                .filter(o -> o.getNodeType().equals(NodeTypeEnum.NT_UPLOAD.getCode()))
                .findAny().orElse(null);
        if (Objects.nonNull(planNodeUpload)) {
            DefinedConfigJsonDTO define = JSON.parseObject(planNodeUpload.getDefinedConfigJson(),
                    DefinedConfigJsonDTO.class);
            List<CheckBoxButton> checkBoxButtonList = define.getCheckBoxButtonList();
            CheckBoxButton checkBoxButton = checkBoxButtonList.stream()
                    .filter(o -> PIPELINE_FILE_MODIFY_REPO_POSITION.getCode().equals(o.getCode()))
                    .findAny().orElse(null);
            resp.setPermit(Objects.nonNull(checkBoxButton) ? NormalState.NORMAL.getCode()
                    : NormalState.FORBIDDEN.getCode());
        }
        PlanNode planNodeManualReview = planNodes.stream()
                .filter(o -> o.getNodeType().equals(NodeTypeEnum.NT_MANUAL_REVIEW.getCode()))
                .findAny().orElse(null);
        if (Objects.nonNull(planNodeManualReview)) {
            DefinedConfigJsonDTO define = JSON.parseObject(planNodeManualReview.getDefinedConfigJson(),
                    DefinedConfigJsonDTO.class);
            SelectedPeopleDTO sp = define.getPopupBoxButton().getSp();
            resp.setSp(sp);
        }

        // 审核状态才查询消息表
        if (Objects.equals(docProcess.getProcessSubStatus(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_FAILED_AUDIT.getCode())) {
            log.info("查询驳回原因:record:{}", req.getRecordId());
            List<DocAudit> list = docAuditService.list(Wrappers.<DocAudit>query().lambda()
                    .eq(DocAudit::getRecordId, req.getRecordId())
                    .eq(DocAudit::getStatus, 2)
                    .orderByDesc(DocAudit::getModifiedTime)
                    .last("limit 1 "));
            resp.setRejectReason(CollUtil.isNotEmpty(list) ? list.get(0).getRejectReason() : StringPool.EMPTY);
//            log.info("查询驳回原因:rsp:{}", JSON.toJSONString(list));
//            return R.ok(CollUtil.isNotEmpty(list) ? list.get(0) : null);

//            R<SysMessage> r = messageRpcService.queryRejectReason(req.getRecordId());
//            if (!r.isSuccess()) {
//                log.error("查询审核拒绝原因失败:{}", JSON.toJSONString(r));
//            }
//            SysMessage sysMessage = r.getData();
//            log.info("驳回状态原因:{}", JSON.toJSONString(sysMessage));
//            if (Objects.nonNull(sysMessage) && StringUtils.isNotBlank(sysMessage.getContent())) {
//                String content = sysMessage.getContent();
//                if (content.split(REJECT_REASON_PREFIX).length > 1) {
//                    resp.setRejectReason(content.split(REJECT_REASON_PREFIX)[1]);
//                }
//            }
        }
        if (Objects.equals(docProcess.getProcessSubStatus(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_PENDING_REVIEW.getCode())) {
            // 查询下一个待审批的文档id
            QueryWrapper<PlanAuditUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("distinct plan_id").lambda()
                    .eq(PlanAuditUser::getUserId, SecurityUtils.getUserId())
                    .eq(PlanAuditUser::getTenantId, SecurityUtils.getTenantId());

            final Set<Long> planIdSet = planAuditUserService.list(queryWrapper)
                    .stream()
                    .map(PlanAuditUser::getPlanId)
                    .collect(Collectors.toSet());

            DocProcessQueryV2Req queryV2Req = new DocProcessQueryV2Req();
            queryV2Req.setPage(1);
            queryV2Req.setPageSize(1);
            queryV2Req.setEndRecordId(req.getRecordId());
            queryV2Req.setPlanIds(planIdSet);
            queryV2Req.setProcessStatusList(
                    CollUtil.newArrayList(DocProcessStatusEnums.ProcessStatusEnum.PS_PENDING_REVIEW.getCode()));
            // 搜索条件
            queryV2Req.setName(req.getName());
            queryV2Req.setRecordTypeIdList(req.getRecordTypeIdList());
            queryV2Req.setUploadTimeStart(req.getUploadTimeStart());
            queryV2Req.setUploadTimeEnd(req.getUploadTimeEnd());
            queryV2Req.setOrigin(req.getOrigin());
            queryV2Req.setCreateByName(req.getCreateByName());

            // 先根据搜索条件搜索
            PageUtils<DocProcessResp> pageUtils = queryPage(queryV2Req);
            if (CollUtil.isEmpty(pageUtils.getList()) && (
                    CharSequenceUtil.isNotBlank(req.getName()) ||
                            CollUtil.isNotEmpty(req.getRecordTypeIdList()) ||
                            req.getUploadTimeStart() != null ||
                            req.getUploadTimeEnd() != null ||
                            req.getOrigin() != null ||
                            CharSequenceUtil.isNotBlank(req.getCreateByName())
            )) {
                queryV2Req.setName(null);
                queryV2Req.setRecordTypeIdList(null);
                queryV2Req.setUploadTimeStart(null);
                queryV2Req.setUploadTimeEnd(null);
                queryV2Req.setOrigin(null);
                queryV2Req.setCreateByName(null);
                // 带搜索条件没有，再搜索所有
                pageUtils = queryPage(queryV2Req);
            }
            resp.setNextRecordId(Optional.ofNullable(pageUtils.getList()).map(l -> {
                if (CollUtil.isEmpty(l)) {
                    return null;
                } else {
                    return l.get(0).getRecordId();
                }
            }).orElse(null));
        }

        if(Objects.equals(docProcess.getProcessStatus(),DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode())){
            // 查询下一个 待人工处理的文档
            DocProcessQueryV2Req queryV2Req = new DocProcessQueryV2Req();
            queryV2Req.setPage(1);
            queryV2Req.setPageSize(1);
            queryV2Req.setEndRecordId(req.getRecordId());
            queryV2Req.setProcessStatusList(
                    CollUtil.newArrayList(DocProcessStatusEnums.ProcessStatusEnum.PS_AWAITING_MANUAL_HANDLING.getCode()));
            // 后台查询 只查询后台可编辑文档
            if (Objects.equals(req.getFormType(),1)) {
                queryV2Req.setStoreWay(1);
            }else if(Objects.equals(req.getFormType(),3)){
                queryV2Req.setStoreWay(2);
            }
            // 搜索条件
            queryV2Req.setName(req.getName());
            queryV2Req.setRecordTypeIdList(req.getRecordTypeIdList());
            queryV2Req.setUploadTimeStart(req.getUploadTimeStart());
            queryV2Req.setUploadTimeEnd(req.getUploadTimeEnd());
            queryV2Req.setOrigin(req.getOrigin());
            queryV2Req.setCreateByName(req.getCreateByName());

            // 先根据搜索条件搜索
            PageUtils<DocProcessResp> pageUtils = queryPage(queryV2Req);
            if (CollUtil.isEmpty(pageUtils.getList()) && (
                    CharSequenceUtil.isNotBlank(req.getName()) ||
                            CollUtil.isNotEmpty(req.getRecordTypeIdList()) ||
                            req.getUploadTimeStart() != null ||
                            req.getUploadTimeEnd() != null ||
                            req.getOrigin() != null ||
                            CharSequenceUtil.isNotBlank(req.getCreateByName())
            )) {
                queryV2Req.setName(null);
                queryV2Req.setRecordTypeIdList(null);
                queryV2Req.setUploadTimeStart(null);
                queryV2Req.setUploadTimeEnd(null);
                queryV2Req.setOrigin(null);
                queryV2Req.setCreateByName(null);
                // 带搜索条件没有，再搜索所有
                pageUtils = queryPage(queryV2Req);
            }
            resp.setNextManualRecordId(Optional.ofNullable(pageUtils.getList()).map(l -> {
                if (CollUtil.isEmpty(l)) {
                    return null;
                } else {
                    return l.get(0).getRecordId();
                }
            }).orElse(null));
        }

        // 元数据提取异常
        boolean statusFlag1 = Objects.equals(docProcess.getProcessSubStatus(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_METADATA_EXTRACTION_EXCEPTION.getCode());
        // 标引提取异常
        boolean statusFlag2 = Objects.equals(docProcess.getProcessSubStatus(),
                DocProcessStatusEnums.ProcessSubStatusEnum.PSS_ABNORMAL_CITATION_EXTRACTION.getCode());
        if (statusFlag1 || statusFlag2) {
            Set<Integer> showNodeTypeSwitch = new HashSet<>();
            Map<Long, PlanNodeDto> planNodeMap = nodeCacheUtil.getPlanNodeMap(docProcess.getPlanId());
            Set<Integer> nodeTypeSet = planNodeMap.values().stream().map(PlanNodeDto::getNodeType)
                    .collect(Collectors.toSet());

            if (statusFlag1 && nodeTypeSet.contains(NodeTypeEnum.NT_CITATION_INFO.getCode())) {
                showNodeTypeSwitch.add(NodeTypeEnum.NT_CITATION_INFO.getCode());
            }
            if (nodeTypeSet.contains(NodeTypeEnum.NT_MANUAL_PROCESS.getCode())) {
                showNodeTypeSwitch.add(NodeTypeEnum.NT_MANUAL_PROCESS.getCode());
            }
            resp.setShowNodeTypeSwitch(showNodeTypeSwitch);
        }
        if (statusFlag2) {
            TaskDoc taskDoc = nodeCacheUtil.getTaskDoc(docProcess.getTaskDocId(), req.getRecordId());
            String storageInfo = taskDoc.getStorageInfo();
            StorageInfoV3Dto storageInfoV3Dto = JSON.parseObject(storageInfo, StorageInfoV3Dto.class);
            resp.setSkipNodeTypeMap(storageInfoV3Dto.getSkipNodeTypeMap());
        }
        log.info("入库流程的文件的状态resp:{}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public void libEdit(DocProcessEditReq req) {
        log.info("req:{}", JSON.toJSONString(req));
//        if (Integer.valueOf(SW_BACKEND.getCode()).equals(req.getStoreWay())) {
//            AssertUtils.isFalse(CollUtil.isEmpty(req.getRepoIdList()), METADATA_RECORD_EXCEPTION,
//                    FIXED_METADATA_REPO_POSITION.getName());
//        }

        String key = String.format(RETRY_OPT_RECORD_ID_KEY, req.getRecordId());
        try {
            // 添加幂等性校验，防止接口重复调用
            if (Boolean.TRUE.equals(redisService.hasKey(key))) {
                log.info("req={} ,正在重试中。。。", req.getRecordId());
                return;
            }
            redisService.setEx(key, StringPool.ONE, 10, TimeUnit.SECONDS);

            recordMangerService.editBizRecordMetadataList(req, null, false);
        } catch (Exception e) {
            log.error("已入库编辑异常！", e);
            redisService.delete(key);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void setId(DocInfoResp docInfoResp) {
//        final DocProcess docProcess = docProcessService.getByRecordId(docInfoResp.getDocBaseResp().getRecordId());
//        if (docProcess != null) {
//            docInfoResp.setId(docProcess.getId());
//            docInfoResp.getDocBaseResp().setId(docProcess.getId());
//        }
    }

    @Override
    @DSTransactional
    public void deleteDocProcessData(List<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        log.info("userId:{},删除入库流程数据，recordIds:{}", SecurityUtils.getUserId(), recordIds);
        // 删除查询批次数量与record
        final List<DocProcess> processes = docProcessService.listWithWrapper(recordIds, null);
        if(CollUtil.isNotEmpty(processes)){
            Map<Long, Long> longMap = processes.stream().collect(Collectors.groupingBy(DocProcess::getBatchId, Collectors.counting()));
            Set<Long> batchIds = longMap.keySet();
            Map<Long,Long> map = taskService.countMapByBatchId(batchIds);
            Set<Long> needDelBatchId = longMap.entrySet().stream().filter(e -> {
                return Objects.equals(map.getOrDefault(e.getKey(), 0L),e.getValue());
            }).map(Map.Entry::getKey).collect(Collectors.toSet());

            if(CollUtil.isNotEmpty(needDelBatchId)){
                taskBatchService.deleteByBatchIds(needDelBatchId);
            }
        }
        taskService.deleteByRecordIdList(recordIds);
        taskDocService.deleteByWrapper(recordIds, null);
        taskFlowService.deleteByWrapper(recordIds, null);
        docProcessService.deleteByWrapper(recordIds, null);
        pipelineTraceLogService.lambdaUpdate().in(PipelineTraceLog::getRecordId, recordIds).remove();

        recordIds.forEach(recordId -> {
            // 删除es数据
            if(!docProcessESService.deleteEsDocProcessBase(recordId,true)){
                log.warn("recordId:{},同步删除doc_proces es数据失败，改为异步删除！",recordId);
                docProcessESService.deleteEsDocProcessBase(recordId,false);
            }
        });
    }


}
