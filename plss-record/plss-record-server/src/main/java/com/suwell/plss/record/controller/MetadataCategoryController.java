package com.suwell.plss.record.controller;

import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.MetadataAddReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryAddReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryEditReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryMetadataReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryQueryReq;
import com.suwell.plss.record.standard.dto.request.MetadataCategoryReq;
import com.suwell.plss.record.standard.dto.request.MetadataEditReq;
import com.suwell.plss.record.standard.dto.request.MetadataQueryReq;
import com.suwell.plss.record.standard.dto.request.MetadataRelevanceReq;
import com.suwell.plss.record.standard.dto.request.RecordTypeMetadataAddReq;
import com.suwell.plss.record.standard.dto.response.MetadataCategoryDetailsResp;
import com.suwell.plss.record.standard.dto.response.MetadataCategoryResp;
import com.suwell.plss.record.standard.dto.response.MetadataDetailResp;
import com.suwell.plss.record.standard.dto.response.MetadataResp;
import com.suwell.plss.record.standard.service.StandardMetadataCategoryFacade;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 元数据分类
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@RestController
@RequestMapping("/v1/metadataCategory")
public class MetadataCategoryController {

    @Resource
    private StandardMetadataCategoryFacade standardMetadataCategoryFacade;
    @Resource
    private WFGIdGenerator wfgIdGenerator;

    /**
     * 分页列表 ok
     */
    @PostMapping("/page")
    public R<PageUtils<MetadataCategoryResp>> queryPage(@RequestBody MetadataCategoryQueryReq req) {
        PageUtils<MetadataCategoryResp> page = standardMetadataCategoryFacade.queryPage(req);

        return R.ok(page);
    }

    /**
     * 列表 ok
     */
    @PostMapping("/list")
    public R<List<MetadataCategoryResp>> list(@RequestBody MetadataCategoryQueryReq req) {
        List<MetadataCategoryResp> metadataCategoryRespList = standardMetadataCategoryFacade.queryList(req);
        return R.ok(metadataCategoryRespList);
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    public R<MetadataCategoryDetailsResp> info(@RequestBody Long id) {
        return R.ok(standardMetadataCategoryFacade.queryById(id));
    }

    /**
     * 保存 ok
     */
    @Log(title = "'后台-元数据管理'", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<Void> save(@RequestBody MetadataCategoryAddReq metadataCategoryAddReq) {
        standardMetadataCategoryFacade.addMetadataCategory(metadataCategoryAddReq);
        return R.ok();
    }

    /**
     * 批量导入 添加保存
     * //--
     */
    @PostMapping("/saveBatch")
    public R<Void> saveBatch(@RequestBody List<MetadataCategoryReq> metadataCategoryReqList) {
        return standardMetadataCategoryFacade.addBatchMetadataCategory(metadataCategoryReqList) ? R.ok() : R.error();
    }


    /**
     * 修改 OK
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody MetadataCategoryEditReq metadataCategoryEditReq) {
        standardMetadataCategoryFacade.modifyById(metadataCategoryEditReq);
        return R.ok();
    }

    /**
     * 删除 ok
     */
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody Long[] ids) {
        standardMetadataCategoryFacade.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    // 文件关联-元数据

    /**
     * 元数据分类 文件元数据列表，ok YAPI
     */
    @PostMapping("/metadata")
    public R<List<MetadataResp>> queryMetadata(@RequestBody Long metadataCategoryId) {
        return R.ok(standardMetadataCategoryFacade.queryMetadata(metadataCategoryId));
    }

    /**
     * 元数据项详情 ok
     */
    @PostMapping("/metadata/detail")
    public R<MetadataDetailResp> queryMetadataDetail(@RequestBody Long mdId) {
        return R.ok(standardMetadataCategoryFacade.queryMetadataDetail(mdId));
    }


    /**
     * 元数据分类 添加元数据(创建) YAPI ok
     */
    @Log(title = "'后台-元数据管理'", businessType = BusinessType.INSERT)
    @PostMapping("/saveMetadata")
    public R<Long> saveMetadata(@Valid @RequestBody MetadataAddReq metadataAddReq) {
        return R.ok(standardMetadataCategoryFacade.addMetadata(metadataAddReq));
    }

    /**
     * 元数据分类关联元数据
     */
    @PostMapping("/relevanceMetadata")
    public R<Long> relevanceMetadata( @RequestBody MetadataRelevanceReq metadataRelevanceReq) {
        standardMetadataCategoryFacade.relevanceMetadata(metadataRelevanceReq);
        return R.ok();
    }

    /**
     * 元数据分类 修改元数据 YAPI ok
     */
    @PostMapping("/updateMetadata")
    public R<Long> updateMetadata(@RequestBody MetadataEditReq metadataEditReq) {
        return R.ok(standardMetadataCategoryFacade.modifyMetadata(metadataEditReq));
    }

    /**
     * 元数据分类 删除元数据 ok
     */
    @Log(title = "'后台-元数据管理'",businessType = BusinessType.DELETE)
    @PostMapping("/deleteMetadata")
    public R<Void> deleteMetadata(@RequestBody MetadataCategoryMetadataReq metadataCategoryMetadataReq) {
        standardMetadataCategoryFacade.removeMetadata(metadataCategoryMetadataReq);
        return R.ok();
    }

    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws UnsupportedEncodingException {
        ExcelUtil<RecordTypeMetadataAddReq> util = new ExcelUtil<>(RecordTypeMetadataAddReq.class);
        String fileName = "元数据项导入模板" + wfgIdGenerator.next() + ".xlsx";
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + URLEncoder.encode(fileName, String.valueOf(StandardCharsets.UTF_8)) + "\"");
        util.importTemplateExcel(response, "元数据项导入模板");
    }

    /**
     * 元数据类别以及对应元数据导出
     */
    @Log(title = "'后台-元数据管理'", businessType = BusinessType.EXPORT,isSaveResponseData = false)
    @PostMapping("/exportCategory")
    public void exportCategory(HttpServletResponse response,@RequestBody MetadataQueryReq req) throws IOException {
        standardMetadataCategoryFacade.exportCategory(response,req);
    }


}
