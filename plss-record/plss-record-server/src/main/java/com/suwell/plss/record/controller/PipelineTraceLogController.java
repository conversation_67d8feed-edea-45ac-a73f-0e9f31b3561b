package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.PipelineTraceLogQueryReq;
import com.suwell.plss.record.standard.dto.response.PipelineTraceLogResp;
import com.suwell.plss.record.standard.service.PipelineTraceLogFacade;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 入库流程记录错误日志
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/pipeline/trace/log")
public class PipelineTraceLogController {

    private final PipelineTraceLogFacade pipelineTraceLogFacade;

    /**
     * 分页列表
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    public R<PageUtils<PipelineTraceLogResp>> queryPage(@RequestBody PipelineTraceLogQueryReq req) {
        return R.ok(pipelineTraceLogFacade.queryPage(req));
    }

    /**
     * 错误详情
     *
     * @param id 错误日志id
     */
    @PostMapping("/info")
    public R<PipelineTraceLogResp> info(@RequestBody Long id) {
        return R.ok(pipelineTraceLogFacade.queryPipelineTraceLogInfo(id));
    }

}
