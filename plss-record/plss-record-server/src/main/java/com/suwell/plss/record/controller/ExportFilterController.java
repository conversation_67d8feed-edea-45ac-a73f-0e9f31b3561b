package com.suwell.plss.record.controller;

import java.util.List;

import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.record.domain.ExportFilterDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.entity.ExportFilter;
import com.suwell.plss.record.service.ExportFilterService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据导出过滤器表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Slf4j
@RestController
@RequestMapping("/v1/exportFilter")
public class ExportFilterController {

    @Autowired
    private ExportFilterService exportFilterService;

    @PostMapping(value = "/list")
    public R<List<ExportFilter>> list(@RequestParam(required = true) long taskId) {
        List<ExportFilter> list = exportFilterService.queryList(taskId);
        return R.ok(list);
    }

    @PostMapping(value = "/{id}")
    public R<ExportFilter> getById(@PathVariable("id") String id) {
        ExportFilter exportFilter = exportFilterService.getById(id);
        return R.ok(exportFilter);
    }

    @PostMapping(value = "/add")
    public R<Void> add(@RequestBody ExportFilterDTO exportFilterDTO) {
        ExportFilter exportFilter = ExportFilter.builder().build();
        BeanUtils.copyProperties(exportFilterDTO, exportFilter);
        exportFilterService.addExportFilter(exportFilter);
        return R.ok();
    }

    @PostMapping(value = "/batch")
    public R<Void> add(@RequestBody List<ExportFilter> listExportFilter) {
        // exportFilterService.addExportFilter(listExportFilter);
        return R.ok();
    }

    @PostMapping(value = "/delete/{id}")
    public R<Void> delete(@PathVariable("id") String id) {
        exportFilterService.removeById(id);
        return R.ok();
    }

    @PostMapping(value = "/update")
    public R<Void> update(@RequestBody ExportFilterDTO exportFilterDTO) {
        ExportFilter exportFilter = ExportFilter.builder().build();
        BeanUtils.copyProperties(exportFilterDTO, exportFilter);
        exportFilterService.updateById(exportFilter);
        return R.ok();
    }
}
