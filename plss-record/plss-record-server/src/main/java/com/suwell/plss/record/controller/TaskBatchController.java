package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.service.DocProcessService;
import com.suwell.plss.record.service.TaskBatchService;
import com.suwell.plss.record.service.TaskDocService;
import com.suwell.plss.record.service.TaskFlowService;
import com.suwell.plss.record.service.TaskService;
import com.suwell.plss.record.standard.dto.request.BatchDocProcessQueryV2Req;
import com.suwell.plss.record.standard.dto.request.TaskBatchQueryReq;
import com.suwell.plss.record.standard.dto.response.DocProcessResp;
import com.suwell.plss.record.standard.dto.response.TaskBatchResp;
import com.suwell.plss.record.standard.service.StandardTaskBatchFacade;
import com.suwell.plss.record.standard.service.StandardTaskFacade;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 任务列表
 * <AUTHOR>
 * @create 2023/11/15
 * @content
 */
@RestController
@RequestMapping("/v1/taskBatch")
public class TaskBatchController {

    @Resource
    private StandardTaskBatchFacade standardTaskBatchFacade;
    @Resource
    private TaskService taskService;
    @Resource
    private TaskDocService taskDocService;
    @Resource
    private TaskFlowService taskFlowService;
    @Resource
    private DocProcessService docProcessService;
    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<TaskBatchResp>> queryPage(@RequestBody TaskBatchQueryReq req){
        req.setForSecure(false);
        return R.ok(standardTaskBatchFacade.queryPage(req));
    }

    /**
     * 密级分页列表
     */
    @PostMapping("/pageForSecure")
    public R<PageUtils<TaskBatchResp>> queryPageForSecure(@RequestBody TaskBatchQueryReq req){
        req.setForSecure(true);
        return R.ok(standardTaskBatchFacade.queryPage(req));
    }

    /**
     * 查看-分页列表V2
     * @param req
     * @return
     */
    @PostMapping("/viewPageV2")
    public R<PageUtils<DocProcessResp>> queryPage(@RequestBody BatchDocProcessQueryV2Req req){
        return R.ok(standardTaskBatchFacade.queryPage(req));
    }

    /**
     * task_batch分表数据迁移
     */
    @PostMapping("/migrateTaskBatch")
    public R<Void> migrateTaskBatch() {
        standardTaskBatchFacade.migrateTaskBatch();
        return R.ok();
    }
    /**
     * task分表数据迁移
     */
    @PostMapping("/migrateTask")
    public R<Void> migrateTask() {
        taskService.migrateTask();
        return R.ok();
    }

    /**
     * task_doc分表数据迁移
     */
    @PostMapping("/migrateTaskDoc")
    public R<Void> migrateTaskDoc() {
        taskDocService.migrateTaskDoc();
        return R.ok();
    }


    /**
     * task_flow分表数据迁移
     */
    @PostMapping("/migrateTaskFlow")
    public R<Void> migrateTaskFlow() {
        taskFlowService.migrateTaskFlow();
        return R.ok();
    }

    /**
     * doc_process分表数据迁移
     */
    @PostMapping("/migrateDocProcess")
    public R<Void> migrateDocProcess() {
        docProcessService.migrateDocProcess();
        return R.ok();
    }

}
