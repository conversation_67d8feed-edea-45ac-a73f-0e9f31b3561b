package com.suwell.plss.record.permission.factory;

import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.record.permission.validator.RecordValidator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PermissionValidatorFactory {

    private final Map<ResourceType, RecordValidator> validatorMap;

    public PermissionValidatorFactory(List<RecordValidator> validators) {
        this.validatorMap = validators.stream()
                .collect(Collectors.toMap(RecordValidator::getResourceType, Function.identity()));
    }

    public RecordValidator getValidator(ResourceType resourceType) {
        return validatorMap.get(resourceType);
    }

}
