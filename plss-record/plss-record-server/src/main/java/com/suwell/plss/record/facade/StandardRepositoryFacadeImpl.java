package com.suwell.plss.record.facade;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.entity.Repository;
import com.suwell.plss.record.service.RepositoryService;
import com.suwell.plss.record.standard.dto.request.ContainsCtypeReq;
import com.suwell.plss.record.standard.dto.request.ConvertRepoToFolderReq;
import com.suwell.plss.record.standard.dto.request.QueryOrAutoNewRepoReq;
import com.suwell.plss.record.standard.dto.request.RepoCollectReq;
import com.suwell.plss.record.standard.dto.request.RepoMaterialResp;
import com.suwell.plss.record.standard.dto.request.RepoTagQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMaterialViewReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataAddReq;
import com.suwell.plss.record.standard.dto.request.RepositoryMetadataQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryModifyReq;
import com.suwell.plss.record.standard.dto.request.RepositoryQueryReq;
import com.suwell.plss.record.standard.dto.request.RepositoryRemoveReq;
import com.suwell.plss.record.standard.dto.request.ResourceSearchReq;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.enums.RepositoryEnum;
import com.suwell.plss.record.standard.enums.RepositoryEnum.CType;
import com.suwell.plss.record.standard.enums.RepositoryEnum.SortType;
import com.suwell.plss.record.standard.service.StandardRepositoryFacade;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 库门面实现
 */
@Slf4j
@Service
public class StandardRepositoryFacadeImpl implements StandardRepositoryFacade {

    // 个人库格式正则
    private static final String PERSONAL_LIBRARY_REGEX = "用户【\\d+】个人库";

    // 个人库前端展示别名
    private static final String PERSONAL_LIBRARY_DISPLAY_NAME = "我的文库";

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private RedisService redisService;

    private final Lock visualPageListLock = new ReentrantLock();

    @Override
    public Long addRepository(RepositoryAddReq repository) {
        BeanValidators.defaultValidate(repository);
        return repositoryService.addRepository(repository);
    }

    @Override
    @DSTransactional
    public void removeRepository(Long repositoryId) {
        AssertUtils.notNull(repositoryId, RecordBizError.REPO_INFO_ID_NOT_NULL);
        RepositoryRemoveReq req = new RepositoryRemoveReq();
        req.setRepoId(repositoryId);
        repositoryService.removeRepository(req);
    }

    @Override
    public void modifyRepository(RepositoryModifyReq repository) {
        BeanValidators.defaultValidate(repository);
        repositoryService.modifyRepository(repository);
    }

    @Override
    public RepositoryResp queryById(Long repoId) {
        AssertUtils.notNull(repoId, RecordBizError.REPO_INFO_ID_NOT_NULL);
        return repositoryService.queryById(repoId);
    }

    @Override
    public PageUtils<RepositoryResp> queryPage(RepositoryQueryReq params) {
        BeanValidators.defaultValidate(params);
        return repositoryService.queryPage(params);
    }

    @Override
    public PageUtils<RepositoryResp> queryMaterialRepoPage(RepositoryQueryReq params) {
        BeanValidators.defaultValidate(params);
        return repositoryService.queryMaterialRepoPage(params);
    }

    @Override
    public List<RepositoryResp> queryList(RepositoryQueryReq params) {
        BeanValidators.defaultValidate(params);
        return repositoryService.queryList(params);
    }

    @Override
    public List<RepositoryResp> visualPageList() {
        String redisKey = "visualPageList_repositoryRespList";
        String redisValue = redisService.getStr(redisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            return JSON.parseArray(redisValue, RepositoryResp.class);
        }

        visualPageListLock.lock();
        try {
            redisValue = redisService.getStr(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                return JSON.parseArray(redisValue, RepositoryResp.class);
            }

            List<Repository> repositoryList = repositoryService.list(
                    Wrappers.lambdaQuery(Repository.class).eq(Repository::getStatus, NormalState.NORMAL.getCode())
                            .ne(Repository::getCtype, CType.PERSONAL.getCode()));
            List<RepositoryResp> resultList = DozerUtils.convertListToNew(repositoryList,
                    RepositoryResp.class);
            redisService.setStr(redisKey, JSON.toJSONString(resultList), 1L, TimeUnit.MINUTES);
            return resultList;
        } catch (Exception e) {
            log.error("查询文库列表异常。em={}", e.getMessage(), e);
        } finally {
            visualPageListLock.unlock();
        }
        return new ArrayList<>();
    }

    @Override
    public List<RepoCtypeGroupResp> groupCtype(RepositoryQueryReq req) {
        BeanValidators.defaultValidate(req);
        return repositoryService.groupCtype(req);
    }

    @Override
    public void saveMetadata(RepositoryMetadataAddReq req) {
        BeanValidators.defaultValidate(req);
        repositoryService.saveMetadata(req);
    }

    @Override
    public RepoMetadataResp queryMetadata(RepositoryMetadataQueryReq req) {
        BeanValidators.defaultValidate(req);
        return repositoryService.queryMetadata(req);
    }

    @Override
    public Long queryPersonalRepoId(QueryOrAutoNewRepoReq req) {
        if (Objects.isNull(req)) {
            req = new QueryOrAutoNewRepoReq();
            req.setCtype(CType.PERSONAL.getCode());
        } else {
            BeanValidators.defaultValidate(req);
            if (StringUtils.isBlank(req.getRepoName())) {
                throw new ServiceException("库名不能为空");
            }
        }
        return repositoryService.queryRepoIdOrSave(req);
    }

    @Override
    public void convertRepositoryToFolder(ConvertRepoToFolderReq req) {
        BeanValidators.defaultValidate(req);
        repositoryService.convertRepositoryToFolder(req);
    }

    @Override
    public List<RepoTagResp> listRepoTags(RepoTagQueryReq req) {
        return repositoryService.listRepoTags(req);
    }

    @Override
    public List<RepoTagResp> listRepoRecordTags(RepoTagQueryReq req) {
        return repositoryService.listRepoRecordTags(req);
    }

    @Override
    public List<RepoInfoResp> listRepoInfo(Long recordId) {
        AssertUtils.notNull(recordId, RecordBizError.RECORD_INFO_ID_NOT_NULL);
        return repositoryService.listRepoInfo(recordId);
    }

    @Override
    public RepositoryCountResp statisticRepoCount(Long tenantId) {
        return repositoryService.statisticRepoCount(tenantId);
    }

    @Override
    public R<List<Long>> queryInnerRepoList() {
        List<Repository> repositoryList = repositoryService.list(new LambdaQueryWrapper<Repository>()
                .select(Repository::getId)
                .eq(Repository::getCtype, CType.COMMON.getCode())
                .eq(Repository::getShareType, RepositoryEnum.ShareType.ANY_SHARE.getCode())
                .eq(Repository::getStatus, RepositoryEnum.Status.NORMAL.getCode()));
        List<Long> repoIds = repositoryList.stream().map(Repository::getId).collect(Collectors.toList());
        return R.ok(repoIds);
    }

    @Override
    public R<List<RepoInfoResp>> queryPrivateRepoList(String userId) {
        List<Repository> repositoryList = repositoryService.list(new LambdaQueryWrapper<Repository>()
                .eq(Repository::getCtype, CType.PRIVATE.getCode())
                .eq(Repository::getOwnerId, userId)
                .eq(Repository::getStatus, RepositoryEnum.Status.NORMAL.getCode()));
        List<RepoInfoResp> repoInfoRespList = repositoryList.stream().map(repository -> {
            RepoInfoResp repoInfoResp = new RepoInfoResp();
            repoInfoResp.setId(repository.getId());
            repoInfoResp.setName(repository.getName());
            return repoInfoResp;
        }).collect(Collectors.toList());
        return R.ok(repoInfoRespList);
    }

    @Override
    public List<RepoInfoResp> listRepoBasicInfoByNames(List<String> repoNameList) {
        return repositoryService.listRepoBasicInfoByNames(repoNameList);
    }

    @Override
    public boolean containsByRepoCtype(ContainsCtypeReq req) {
        BeanValidators.defaultValidate(req);
        return repositoryService.containsByRepoCtype(req);
    }

    @Override
    public Long countRepoByOwnerId(String userId) {
        return repositoryService.lambdaQuery().eq(Repository::getOwnerId, userId)
                .ne(Repository::getCtype, CType.PERSONAL.getCode()).count();
    }

    @Override
    public List<RepositoryResp> queryByIds(List<Long> ids) {
        List<Repository> repositoryList = repositoryService.list(
                new LambdaQueryWrapper<Repository>().in(Repository::getId, ids).eq(Repository::getStatus,Integer.valueOf(
                        UserConstants.YSE_STATUS)));
        if (CollectionUtils.isEmpty(repositoryList)) {
            return Collections.emptyList();
        }
        final List<RepositoryResp> repositoryResps = DozerUtils.convertListToNew(repositoryList, RepositoryResp.class);

        if (CollectionUtils.isEmpty(repositoryResps)) {
            return Collections.emptyList();
        }

        repositoryResps.forEach(repositoryResp -> {
            String repoName = repositoryResp.getName();
            // 处理"用户【数字】个人库"格式的仓库名，替换为"我的文库"
            if (repoName != null && repoName.matches(PERSONAL_LIBRARY_REGEX)) {
                repoName = PERSONAL_LIBRARY_DISPLAY_NAME;
            }
            repositoryResp.setName(repoName);
        });
        return repositoryResps;
    }

    @Override
    public void fixMyFolderForRepeat() {
        repositoryService.fixMyFolderForRepeat();
    }


    @Override
    public List<RepositoryResp> listSimpleRepoInfo(RepositoryQueryReq params) {
        List<Repository> list = repositoryService.lambdaQuery()
                .select(Repository::getId, Repository::getName, Repository::getCtype, Repository::getShareType)
                .ne(Objects.nonNull(params.getNonCtype()), Repository::getCtype, params.getNonCtype())
                .ne(Objects.nonNull(params.getNonShareType()), Repository::getShareType, params.getNonShareType())
                .eq(Objects.nonNull(params.getCtype()), Repository::getCtype, params.getCtype())
                .eq(Objects.nonNull(params.getShareType()), Repository::getShareType, params.getShareType())
                .in(CollUtil.isNotEmpty(params.getCtypeList()), Repository::getCtype, params.getCtypeList())
                .in(CollUtil.isNotEmpty(params.getRepoIds()), Repository::getId, params.getRepoIds())
                .in(CollUtil.isNotEmpty(params.getOrgIds()), Repository::getOrgId, params.getOrgIds())
                .like(StringUtils.isNotBlank(params.getRepositoryName()),
                        Repository::getName, params.getRepositoryName())
                .list();
        return DozerUtils.convertListToNew(list, RepositoryResp.class);
    }

    @Override
    public List<ResourceSearchResp> resourceSearch(ResourceSearchReq req) {
        BeanValidators.defaultValidate(req);
        return repositoryService.resourceSearch(req);
    }

    @Override
    public void repoCollect(RepoCollectReq req) {
        BeanValidators.defaultValidate(req);
        repositoryService.repoCollect(req);
    }

    @Override
    public Long countRepoByOrgId(List<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return 0L;
        }
        return repositoryService.lambdaQuery().in(Repository::getOrgId, orgIds).count();
    }

    @Override
    public void repoOrgPreFill(HttpServletResponse response) {
        repositoryService.repoOrgPreFill(response);
    }

    @Override
    public void importRepoOrgBatch(MultipartFile file) {
        repositoryService.importRepoOrgBatch(file);
    }

    @Override
    public List<RepositoryResp> listByIdsWithSort(List<Long> repoIds, SortType sortType) {
        return repositoryService.listByIdsWithSort(repoIds, sortType);
    }

    @Override
    public List<MetadataResp> listRepoRecordMd(Long repoId) {
        if (Objects.isNull(repoId)) {
            return List.of();
        }
        return repositoryService.listRepoRecordMd(repoId);
    }

    @Override
    public List<RepoMaterialResp> listRepoMaterials(String repoName, Integer materialView) {
        List<Repository> repositoryList = repositoryService.list(new LambdaQueryWrapper<Repository>()
                    .eq(Repository::getStatus, Integer.valueOf(UserConstants.YSE_STATUS))
                    .eq(Objects.nonNull(materialView), Repository::getMaterialView, materialView)
                    .like(org.apache.commons.lang3.StringUtils.isNotBlank(repoName), Repository::getName, repoName));
        if (CollUtil.isEmpty(repositoryList)) {
            return Lists.newArrayList();
        }
        return repositoryList.stream().map(x->{
            RepoMaterialResp materialResp = new RepoMaterialResp();
            materialResp.setRepoId(x.getId());
            materialResp.setRepoName(x.getName());
            return materialResp;
        }).toList();
    }

    @Override
    public void modifyMaterialViewRepository(RepositoryMaterialViewReq req) {
        BeanValidators.defaultValidate(req);
        repositoryService.modifyMaterialViewRepository(req);
    }
}
