package com.suwell.plss.record.knowledge;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4
 **/
@Data
public class AiKnowledgeReq implements Serializable {

    private static final long serialVersionUID = 8840006764744114853L;
    /**
     * 文件id列表
     */
    private List<Long> recordIds;

    /**
     * 文档类型id列表
     */
    private List<Long> recordTypeIds;

    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
