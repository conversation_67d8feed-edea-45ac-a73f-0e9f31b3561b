package com.suwell.plss.record.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 写文档接口的注释
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@RestController
@RequestMapping("/v1/demo")
public class DemoController {

    //    /**
//     * 在线上传入库
//     *
//     * @param recordOfflineReq 文件及元数据信息
//     */
//    @PostMapping("/addOnlineUploadV3")
//    R<Void> addOnlineUploadV3(@RequestBody RecordOfflineV3Req recordOfflineReq) {
//        return R.ok();
//    }
}
