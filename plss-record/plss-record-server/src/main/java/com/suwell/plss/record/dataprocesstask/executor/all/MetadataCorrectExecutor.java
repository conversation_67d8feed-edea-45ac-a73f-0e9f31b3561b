package com.suwell.plss.record.dataprocesstask.executor.all;

import static com.suwell.plss.record.standard.enums.RecordEnum.MetadataValueDataTypeEnum.USER_EDIT;

import com.alibaba.fastjson2.JSON;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.utils.CollectionUtil;
import com.suwell.plss.framework.common.utils.SleepUtils;
import com.suwell.plss.record.conf.DataProcessTaskConfig;
import com.suwell.plss.record.conf.DataProcessTaskConfig.DataProcessTaskProp;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordMetadataValue;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RepairMetadataService;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.domain.RecordMetadataValueDTO;
import com.suwell.plss.record.standard.dto.MetadataCorrectData;
import com.suwell.plss.record.standard.dto.response.TypeMetadataRuleResp;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/1 19:16
 */
@Service
@Slf4j
public class MetadataCorrectExecutor implements DataProcessTaskExecutor {

    @Resource
    private DataProcessTaskService dataProcessTaskService;

    @Resource
    private RecordMetadataValueService recordMetadataValueService;

    @Resource
    private RepairMetadataService repairMetadataService;

    @Resource
    private RecordService recordService;

    @Resource
    private StandardRecordTypeFacade recordTypeFacade;

    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;

    @Resource
    private DataProcessTaskConfig dataProcessTaskConfig;

    @Override
    public BusinessType getBusinessType() {
        return BusinessType.METADATA_CORRECT;
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-任务开始,taskId={}", logPrefix, taskId);

        DataProcessTaskProp config = dataProcessTaskConfig.getConfig();
        Integer batchSleepSecond = config.getBatchSleepSecond();
        Integer batchSize = config.getAllBatchSize();

        DataProcessTask task = dataProcessTaskService.getById(taskId);

        List<MetadataCorrectData> correctDataList = JSON.parseArray(task.getBusinessData(), MetadataCorrectData.class);
        List<List<MetadataCorrectData>> lists = CollectionUtil.splitBySize(correctDataList, batchSize);

        for (int i = task.getOffsetPosition().intValue(), len = lists.size(); i < len; i++) {
            SleepUtils.sleepSecond(batchSleepSecond);
            task = dataProcessTaskService.getById(taskId);

            List<MetadataCorrectData> subList = lists.get(i);
            log.info("{}任务-批次开始,taskId={},subList={}", logPrefix, taskId, JSON.toJSONString(subList));
            try {
                List<Long> recordIdList = subList.stream().map(MetadataCorrectData::getRecordId).toList();
                Map<Long, Map<String, String>> recordId2MetadataMapMap = subList.stream().collect(
                        Collectors.toMap(MetadataCorrectData::getRecordId, MetadataCorrectData::getMetadataMap,
                                (v1, v2) -> v1));

                List<Record> recordList = recordService.getListByIds(recordIdList);
                Map<Long, Record> recordId2RecordMap = recordList.stream()
                        .collect(Collectors.toMap(Record::getId, Function.identity()));

                List<TypeMetadataRuleResp> typeMetadataRuleList = recordTypeFacade.batchQueryTypeMetadataRule(
                        recordList.stream().map(Record::getRecordtypeId).collect(Collectors.toList()));
                Map<String, TypeMetadataRuleResp> typeMetadataRuleMap = typeMetadataRuleList.stream()
                        .collect(Collectors.toMap(v -> v.getRecordtypeId() + "_" + v.getMdName(), Function.identity(),
                                (v1, v2) -> v1));

                List<RecordMetadataValueDTO> recordValueDtoList = recordMetadataValueService.queryByRecordIdList(
                        recordIdList);

                List<Record> needUpdateRecordList = new ArrayList<>();
                List<RecordMetadataValue> metadataValueList = recordValueDtoList.stream().map(recordValueDto -> {
                    //文档id
                    Long recordId = recordValueDto.getRecordId();
                    Record record = recordId2RecordMap.get(recordId);

                    Map<String, String> metadataMap = recordId2MetadataMapMap.get(recordId);
                    Map<String, MetadataValueDTO> currentMetadataMap = recordValueDto.getMetadataValueList().stream()
                            .collect(
                                    Collectors.toMap(MetadataValueDTO::getMdName, Function.identity(), (v1, v2) -> v1));

                    for (String metadataKey : metadataMap.keySet()) {
                        if (plssFixedMetadataNameConfig.getDocTitleName().equals(metadataKey)) {
                            record.setTitle(metadataMap.get(metadataKey));
                            needUpdateRecordList.add(record);
                        }
                        MetadataValueDTO valueDto = currentMetadataMap.get(metadataKey);
                        if (valueDto != null) {
                            valueDto.setMdValue(metadataMap.get(metadataKey));
                            valueDto.setModifiedTime(new Date());
                        } else if (typeMetadataRuleMap.get(record.getRecordtypeId() + "_" + metadataKey) != null) {
                            TypeMetadataRuleResp typeMetadataRuleResp = typeMetadataRuleMap.get(
                                    record.getRecordtypeId() + "_" + metadataKey);
                            valueDto = new MetadataValueDTO();
                            valueDto.setId(wfgIdGenerator.next());
                            valueDto.setRecordId(recordId);
                            valueDto.setMdId(typeMetadataRuleResp.getMdId());
                            valueDto.setMdName(typeMetadataRuleResp.getMdName());
                            valueDto.setMdValue(metadataMap.get(metadataKey));
                            valueDto.setCreateTime(new Date());
                            valueDto.setModifiedTime(new Date());
                            valueDto.setDataType(USER_EDIT.getCode());

                            recordValueDto.getMetadataValueList().add(valueDto);
                        }
                    }

                    RecordMetadataValue metadataValue = new RecordMetadataValue();
                    metadataValue.setRecordId(recordValueDto.getRecordId());
                    metadataValue.setMetadataValueJson(JSON.toJSONString(recordValueDto.getMetadataValueList()));
                    metadataValue.setCreateTime(recordValueDto.getCreateTime());
                    metadataValue.setModifiedTime(new Date());
                    return metadataValue;
                }).collect(Collectors.toList());

                repairMetadataService.updateDbMetadata(metadataValueList, needUpdateRecordList);
                repairMetadataService.updateEsMetadata(metadataValueList);

                Long offsetNew = task.getOffsetPosition() + 1;
                if (i == len - 1) {
                    dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetNew);
                    log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,
                            task.getTotalCount(), offsetNew);
                } else {
                    dataProcessTaskService.finishBatch(taskId, task.getExecutedCount() + subList.size(), offsetNew);
                    log.info("{}任务-批次完成,taskId={},executedCount={},offsetNew={}", logPrefix, taskId,
                            task.getExecutedCount() + subList.size(), offsetNew);
                }
            } catch (Exception e) {
                dataProcessTaskService.breakTask(taskId);
                log.error("{}任务-任务中断,taskId={},em={}", logPrefix, taskId, e.getMessage(), e);
            }
        }
    }
}
