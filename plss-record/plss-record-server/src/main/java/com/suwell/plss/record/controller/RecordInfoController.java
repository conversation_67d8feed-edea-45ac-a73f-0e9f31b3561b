package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.CardDTO;
import com.suwell.plss.record.standard.dto.request.RecordDetailReq;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.service.StandardRecordInfoFacade;
import com.suwell.plss.search.standard.dto.request.RelatedRecordReq;
import com.suwell.plss.search.standard.dto.response.RelatedRecordResp;
import com.suwell.plss.search.standard.dto.response.SearchRecordResp;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 文档详细页卡片相关接口
 *<AUTHOR>
 *@date 2023/11/30 20:31
 *@version 1.0.0
 */
@RestController
@RequestMapping("/v1/record")
public class RecordInfoController {

    @Resource
    private StandardRecordInfoFacade standardRecordInfoFacade;

    /**
     * 主题卡片
     *
     * @param recordId
     * @return
     */
    @PostMapping("record_type/get")
    public R<RecordCardResp> get(@RequestBody RecordDetailReq recordDetailReq) {
        return R.ok(standardRecordInfoFacade.cardData(recordDetailReq));
    }

    /**
     * 点击关键词，返回相关文档
     *
     * @param recordId
     * @param kw
     * @param page
     * @param size
     * @return
     */
    @GetMapping("record/{recordId}")
    public R<RelatedRecordResp> relatedRecord(@PathVariable("recordId") Long recordId, @RequestParam("kw") String kw, @RequestParam(value = "page",required = false) Integer page, @RequestParam(value = "size",required = false) Integer size) {
        return R.ok(standardRecordInfoFacade.relatedRecord(recordId, Arrays.asList(kw), page, size));
    }


    /**
     * 查询当前文档的知识提取的结果
     * @param req
     */
    @PostMapping("/detail/knowledge")
    public R<List<RecordKnowledgeResp>> recordKnowledge(@RequestBody @Valid RecordDetailReq req){
        return R.ok(standardRecordInfoFacade.recordKnowledge(req.getRecordId()));
    }

    /**
     * 文档基本信息
     * @param recordDetailReq
     * @return
     */
    @PostMapping("/card/base")
    public R<PmdRecordDocumentResp> baseInfo(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.baseInfo(recordDetailReq));
    }

    /**
     * 同期文档
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/same_journal")
    public R<List<SameJournalResp>> sameJournal(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.sameJournal(recordDetailReq.getRecordId()));
    }

    /**
     * 相关文档
     * @param req
     * @return
     */
    @PostMapping("card/related")
    public R<RelatedRecordResp> relatedRecord1(@RequestBody RecordDetailReq req) {
        return R.ok(standardRecordInfoFacade.relatedRecord(req.getRecordId(), Arrays.asList(req.getKw()), req.getPage(), req.getSize()));
    }

    /**
     * 知识提取
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/knowledge")
    public R<List<KnowledgeResp>> knowledge(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.knowledge(recordDetailReq.getRecordId()));
    }


    /**
     * 系列文章
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/series")
    public R<List<SearchRecordResp>> series(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.seriesRecord(recordDetailReq.getRecordId()));
    }

    /**
     * 解读文档
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/interpretation")
    public R<List<RecordRelQueryResp>> interpretation(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.interpretation(recordDetailReq.getRecordId()));
    }

    /**
     * 知识链路
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/knowledge_link")
    public R<List<RecordLinkRelQueryResp>> knowledgeLink(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.knowledgeLink(recordDetailReq.getRecordId()));
    }

    /**
     * 知识图谱
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/atlas")
    public R<AtlasResp> atlas(@RequestBody RecordDetailReq recordDetailReq){
        return R.ok(standardRecordInfoFacade.atlas(recordDetailReq.getRecordId()));
    }

    /**
     * 卡片配置
     * @param recordDetailReq
     * @return
     */
    @PostMapping("card/config")
    public R<Map<String,List<CardDTO>>> config(){
        return R.ok(standardRecordInfoFacade.cardConfig());
    }

    /**
     * 文档卡片配置
     * @return
     */
    @PostMapping("card/record_config")
    public R<List<CardDTO>> recordConfig(@RequestBody RecordDetailReq req){
        return R.ok(standardRecordInfoFacade.recordConfig(req.getRecordId()));
    }

    @PostMapping("meta/key_word")
    public R<List<String>> recordKeyword(@RequestBody RecordDetailReq req){
        return R.ok(standardRecordInfoFacade.recordKeyWord(req.getRecordId()));
    }

    @PostMapping("detail/related_record")
    public R<DetailRelatedRecordResp> relatedRecord(@RequestBody RecordDetailReq req) {
        return R.ok(standardRecordInfoFacade.relatedRecordV2(req));
    }

}
