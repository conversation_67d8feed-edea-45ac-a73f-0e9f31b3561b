package com.suwell.plss.record.rpcimpl;


import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.service.RecordShareRpcService;
import com.suwell.plss.record.service.RecordShareService;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(RecordShareRpcService.INNER_PREFIX)
public class RecordShareRpcServiceImpl implements RecordShareRpcService {

    @Resource
    private RecordShareService recordShareService;
    @Override
    public R updateInvalidStatus(List<Long> recordIds) {
        recordShareService.updateInvalidStatus(recordIds);
        return R.ok();
    }
}
