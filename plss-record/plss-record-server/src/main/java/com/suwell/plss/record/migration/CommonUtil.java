package com.suwell.plss.record.migration;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.suwell.plss.framework.common.utils.EncryptUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import io.jsonwebtoken.impl.crypto.MacProvider;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommonUtil {
    public static void seriallizeList(List<?> list, String filepath) {

        if (list.isEmpty()) {
            return;
        }

        try {
            List<?> listOutput = list.stream().distinct().collect(Collectors.toList());

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(Include.NON_NULL);
            String json = objectMapper.writeValueAsString(listOutput);

            String jsonCrypt = EncryptUtils.encryptWithAES(json, ExportConstant.AES_KEY);
            if (StringUtils.isEmpty(jsonCrypt)) {
                return;
            }

            FileOutputStream fileOutputStream = FileUtils.openOutputStream(FileUtils.getFile(filepath));
            fileOutputStream.write(jsonCrypt.getBytes(StandardCharsets.UTF_8));
            fileOutputStream.flush();
            fileOutputStream.close();

        } catch (Exception ex) {
            log.info("seriallizeList error: {}", ex.getMessage());
        }
    }

    public static <T> List<T> deseriallizeList(String filepath, Class<T> clazz) {

        try {
            if (StringUtils.isEmpty(filepath)) {
                return new ArrayList<>();
            }

            if (!FileUtil.exist(filepath)) {
                throw new RuntimeException("File does not exist: " + filepath);
            }
            String jsonCrypt = Files.readString(Paths.get(filepath));

            return deseriallizeContent(jsonCrypt, clazz);

        } catch (Exception ex) {
            log.info("deseriallizeList error: {}", ex.getMessage());
        }

        return new ArrayList<>();
    }

    public static List<Map<String, Object>> deserializeContentToMap(String filepath) {
        try {

            if (StringUtils.isEmpty(filepath)) {
                return new ArrayList<>();
            }
            if (!FileUtil.exist(filepath)) {
                throw new RuntimeException("File does not exist: " + filepath);
            }
            String jsonCrypt = Files.readString(Paths.get(filepath));
            String json = EncryptUtils.decryptWithAES(jsonCrypt, ExportConstant.AES_KEY);

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return objectMapper.readValue(json,
                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class));
        } catch (Exception ex) {
            log.info("deseriallizeList error: {}", ex.getMessage());
        }

        return new ArrayList<>();
    }

    public static <T> List<T> deseriallizeContent(String content, Class<T> clazz) {

        try {

            if (StringUtils.isEmpty(content)) {
                return new ArrayList<>();
            }

            String json = EncryptUtils.decryptWithAES(content, ExportConstant.AES_KEY);

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            List<T> list = objectMapper.readValue(json,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));

            list = list.stream().distinct().collect(Collectors.toList());

            return list;

        } catch (Exception ex) {
            log.info("deseriallizeList error: {}", ex.getMessage());
        }

        return new ArrayList<>();
    }

    public static void renameFile(String filepath) {
        renameFile(filepath, ".done");
    }

    public static void renameFile(String filepath, String suffix) {
        try {
            Path srcPath = Paths.get(filepath);
            Path destPath = Paths.get(filepath + suffix);
            Files.move(srcPath, destPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException ex) {
            log.info("renameFile error: {}", ex.getMessage());
        }
    }

}
