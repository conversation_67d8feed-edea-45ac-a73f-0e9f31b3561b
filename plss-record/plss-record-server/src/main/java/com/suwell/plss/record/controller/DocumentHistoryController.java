package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.DocumentHistoryQueryReq;
import com.suwell.plss.record.standard.dto.response.DocumentHistoryResp;
import com.suwell.plss.record.standard.service.StandardDocumentHistoryFacade;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;


/**
 * @ClassName DocumentHistoryController
 * <AUTHOR>
 * @Date 2023-8-10 17:47
 * @Version 1.0
 **/
@RestController
@RequestMapping("/v1/document/history")
public class DocumentHistoryController {

    @Resource
    private StandardDocumentHistoryFacade documentHistoryFacade;

    /**
     * 列表
     */
    @PostMapping("/list")
    //@RequiresPermissions("record:documentHistory:list")
    public R<PageUtils<DocumentHistoryResp>> list(@RequestBody DocumentHistoryQueryReq params) {
        PageUtils<DocumentHistoryResp> page = documentHistoryFacade.queryDocumentHistorys(params);
        return R.ok(page);
    }


    /**
     * 信息
     */
    @GetMapping("/info/{id}")
    //@RequiresPermissions("record:documentHistory:info")
    public R<DocumentHistoryResp> info(@PathVariable("id") Long id) {
        DocumentHistoryResp docHis = documentHistoryFacade.getById(id);
        return R.ok(docHis);
    }

}
