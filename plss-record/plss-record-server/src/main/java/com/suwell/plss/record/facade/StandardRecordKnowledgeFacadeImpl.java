package com.suwell.plss.record.facade;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.domain.RecordKnowledgeModelAttributeDto;
import com.suwell.plss.record.domain.RecordKnowledgeModelConceptDto;
import com.suwell.plss.record.domain.RecordKnowledgeModelDto;
import com.suwell.plss.record.domain.RecordKnowledgeModelRelationDto;
import com.suwell.plss.record.entity.KnowledgeAttribute;
import com.suwell.plss.record.entity.KnowledgeConceptAttribute;
import com.suwell.plss.record.entity.KnowledgeModel;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.entity.RecordKnowledge;
import com.suwell.plss.record.entity.RecordKnowledgeAttribute;
import com.suwell.plss.record.entity.RecordKnowledgeConflict;
import com.suwell.plss.record.entity.RecordKnowledgeRelation;
import com.suwell.plss.record.enums.KnowledgeEnum;
import com.suwell.plss.record.service.KnowledgeAttributeService;
import com.suwell.plss.record.service.KnowledgeConceptAttributeService;
import com.suwell.plss.record.service.KnowledgeModelService;
import com.suwell.plss.record.service.RecordKnowledgeAttributeService;
import com.suwell.plss.record.service.RecordKnowledgeConflictService;
import com.suwell.plss.record.service.RecordKnowledgeRelationService;
import com.suwell.plss.record.service.RecordKnowledgeService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.service.RepositoryService;
import com.suwell.plss.record.standard.domain.MetadataValueDTO;
import com.suwell.plss.record.standard.dto.ModelSettingAttributeDTO;
import com.suwell.plss.record.standard.dto.ModelSettingDTO;
import com.suwell.plss.record.standard.dto.ModelSettingEdgDTO;
import com.suwell.plss.record.standard.dto.ModelSettingNodeDTO;
import com.suwell.plss.record.standard.dto.request.KnowledgeModelScopeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeAddConceptValueReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeAddRelationReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeAiExtractItem;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeAttributeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeConflictValueReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeDeleteConceptValueReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeDeleteRelationReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeEditConceptValueReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeEditRelationReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeMergeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeModelAiExtractReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgePredicate;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeReq;
import com.suwell.plss.record.standard.dto.request.RecordKnowledgeSubject;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeAttributeResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictCountResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictDetailResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictValueCountResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictValueRelationResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeQueryItemResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeQueryResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.service.StandardKnowledgeFacade;
import com.suwell.plss.record.standard.service.StandardRecordKnowledgeFacade;
import com.suwell.plss.search.api.http.RecordGeneralSearchRpcEntrance;
import com.suwell.plss.search.api.http.RecordGraphRpcEntrance;
import com.suwell.plss.search.standard.dto.graph.LabelValueAttribute;
import com.suwell.plss.search.standard.dto.graph.LabelValueRelation;
import com.suwell.plss.search.standard.dto.graph.RecordKnowledgeGraph;
import com.suwell.plss.search.standard.dto.graph.RecordKnowledgeGraphObject;
import com.suwell.plss.search.standard.dto.graph.RecordKnowledgeGraphPredicate;
import com.suwell.plss.search.standard.dto.graph.RecordKnowledgeGraphSubject;
import com.suwell.plss.search.standard.dto.graph.request.ConflictQueryReq;
import com.suwell.plss.search.standard.dto.graph.request.LabelPropertyAddReq;
import com.suwell.plss.search.standard.dto.graph.request.LabelRelationAddReq;
import com.suwell.plss.search.standard.dto.graph.request.LabelRelationPropertyReq;
import com.suwell.plss.search.standard.dto.graph.request.LabelValueAddReq;
import com.suwell.plss.search.standard.dto.graph.request.LabelValueReq;
import com.suwell.plss.search.standard.dto.graph.request.MergeNodeReq;
import com.suwell.plss.search.standard.dto.graph.response.LabelValueResp;
import com.suwell.plss.search.standard.dto.request.newsSearch.wrapper.EsSearchWrapper;
import com.suwell.plss.search.standard.dto.request.newsSearch.wrapper.RecordEsIndexEntity;
import com.suwell.plss.search.standard.dto.response.KnowledgeInfoSourceResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/05/15
 */
@Slf4j
@RefreshScope
@Service
@DSTransactional
public class StandardRecordKnowledgeFacadeImpl implements StandardRecordKnowledgeFacade {

    @Resource
    private RecordKnowledgeService recordKnowledgeService;

    @Resource
    private KnowledgeModelService knowledgeModelService;

    @Resource
    private RecordKnowledgeRelationService recordKnowledgeRelationService;

    @Resource
    private RecordKnowledgeAttributeService recordKnowledgeAttributeService;

    @Resource
    private RecordKnowledgeConflictService recordKnowledgeConflictService;

    @Resource
    private RecordService recordService;

    @Resource
    private WFGIdGenerator wfgIdGenerator;

    @Resource
    private RecordGraphRpcEntrance recordGraphRpcEntrance;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private RecordMetadataValueService recordMetadataValueService;

    @Resource
    private StandardKnowledgeFacade standardKnowledgeFacade;

    @Resource
    private KnowledgeConceptAttributeService knowledgeConceptAttributeService;

    @Resource
    private KnowledgeAttributeService knowledgeAttributeService;
    @Resource
    private RecordGeneralSearchRpcEntrance recordSearchRpcEntrance;


    @Override
    public void saveRecordKnowledge(RecordKnowledgeReq recordKnowledgeReq) {
        if (recordKnowledgeReq == null) {
            return;
        }
        Long recordId = recordKnowledgeReq.getRecordId();
        Record record = recordService.getOneById(recordId);
        if (record == null) {
            log.error("ai知识提取响应文档id:{}不存在", recordId);
            return;
        }
        recordKnowledgeReq.setTitle(record.getTitle());
        RecordKnowledge existRecordKnowledge = recordKnowledgeService.getOne(new LambdaQueryWrapper<RecordKnowledge>()
                .eq(RecordKnowledge::getRecordId, recordId));
        if (existRecordKnowledge == null || StringUtils.isEmpty(existRecordKnowledge.getModelSetting())) {
            log.warn("文档id:{}知识提取模型配置为空", recordId);
            return;
        }
        //解析知识提取结果 主要是客体实例关联系统文件
        resolveRecordKnowledge(recordKnowledgeReq.getKnowledgeItemList());
        RecordKnowledgeGraph recordKnowledgeGraph = resolveAiKnowledge2Graph(recordKnowledgeReq,
                existRecordKnowledge.getModelSetting());
        RecordKnowledgeModelDto recordKnowledgeModelDto = JSON.parseObject(existRecordKnowledge.getModelSetting(),
                RecordKnowledgeModelDto.class);

        if (recordKnowledgeGraph != null) {
            recordKnowledgeGraph.setAutoMerge(KnowledgeEnum.AlignWay.AUTO.getCode().equals(recordKnowledgeReq.getAlignWay()));
            recordKnowledgeGraph.setConceptAutoMegerAttrMap(recordKnowledgeModelDto.getConceptAutoMegerAttrMap());
            log.info("文档id:{}知识提取结果转换为图数据:{}", recordId, JSON.toJSONString(recordKnowledgeGraph));
            //保存到图数据库
            saveRecordKnowledge2Db(recordKnowledgeReq, false);
            saveRecordKnowledgeItem2Db(recordKnowledgeGraph);
            recordGraphRpcEntrance.save2Graph(Collections.singletonList(recordKnowledgeGraph));
        }
    }



    @Override
    public String modelSetting4Ai(RecordKnowledgeModelAiExtractReq aiExtractReq) {
        Long recordId = aiExtractReq.getRecordId();
        Long modelId = aiExtractReq.getModelId();
        log.info("文档id:{},模型id:{}开始获取知识提取模型配置", recordId, modelId);
        KnowledgeModel knowledgeModel = knowledgeModelService.getOne(new LambdaQueryWrapper<KnowledgeModel>()
                .eq(KnowledgeModel::getId, modelId).eq(KnowledgeModel::getStatus, 1));
        log.info("文档id:{},模型id:{}查询模型信息结果:{}", recordId, modelId, JSON.toJSONString(knowledgeModel));
        if (knowledgeModel == null) {
            return null;
        }
        //保存模型入参到数据库
        RecordKnowledgeModelDto recordKnowledgeModel = resolveRecordModelSetting(knowledgeModel);
        log.info("文档id:{}根据模型id:{}解析模型配置结果:{}", recordId, modelId,
                JSON.toJSONString(recordKnowledgeModel));
        List<Map<String, Set<String>>> modelSetting4AiMap = getModelSetting4Ai(recordKnowledgeModel);
        log.info("文档id:{}根据模型id:{}解析AI提取模型入参:{}", recordId, modelId,
                JSON.toJSONString(modelSetting4AiMap));

        if (CollUtil.isEmpty(modelSetting4AiMap)) {
            //有模型配置 才保存知识提取数据
            return null;
        }

        RecordKnowledgeReq recordKnowledgeReq = new RecordKnowledgeReq();
        recordKnowledgeReq.setRecordId(recordId);
        recordKnowledgeReq.setModelSetting(JSON.toJSONString(recordKnowledgeModel));
        recordKnowledgeReq.setAlignWay(aiExtractReq.getAlignWay());
        saveRecordKnowledge2Db(recordKnowledgeReq, true);
        return JSON.toJSONString(modelSetting4AiMap);
    }


    @Override
    public void reloadNeo4j(Long recordId, Integer alignWay, Long modelId) {
        RecordKnowledge recordKnowledge = recordKnowledgeService.getOne(new LambdaQueryWrapper<RecordKnowledge>()
                .eq(RecordKnowledge::getRecordId, recordId));
        String contentJson = recordKnowledge.getContentJson();
        if(StringUtils.isEmpty(contentJson)){
            return;
        }
        Record record = recordService.getOneById(recordId);
        if(record == null){
            log.error("ai知识提取响应文档id:{}不存在", recordId);
            return;
        }
        String modelSetting = recordKnowledge.getModelSetting();
        if(modelId != null){
            KnowledgeModel knowledgeModel = knowledgeModelService.getOne(new LambdaQueryWrapper<KnowledgeModel>()
                    .eq(KnowledgeModel::getId, modelId).eq(KnowledgeModel::getStatus, 1));
            if(knowledgeModel == null){
                log.error("文档id:{} 模型id:{}不存在", recordId, modelId);
                return;
            }
            RecordKnowledgeModelDto recordKnowledgeModel = resolveRecordModelSetting(knowledgeModel);
            modelSetting = JSON.toJSONString(recordKnowledgeModel);
        }

        List<RecordKnowledgeAiExtractItem> recordKnowledgeAiExtractItems = JSON.parseArray(contentJson, RecordKnowledgeAiExtractItem.class);
        RecordKnowledgeReq recordKnowledgeReq = new RecordKnowledgeReq();
        recordKnowledgeReq.setTitle(record.getTitle());
        recordKnowledgeReq.setRecordId(recordId);
        recordKnowledgeReq.setModelSetting(modelSetting);
        recordKnowledgeReq.setAlignWay(alignWay);
        if(alignWay == null){
            recordKnowledgeReq.setAlignWay(KnowledgeEnum.AlignWay.AUTO.getCode());
        }
        recordKnowledgeReq.setKnowledgeItemList(recordKnowledgeAiExtractItems);

        resolveRecordKnowledge(recordKnowledgeReq.getKnowledgeItemList());
        RecordKnowledgeGraph recordKnowledgeGraph = resolveAiKnowledge2Graph(recordKnowledgeReq,
                modelSetting);
        RecordKnowledgeModelDto recordKnowledgeModelDto = JSON.parseObject(modelSetting,
                RecordKnowledgeModelDto.class);
        if (recordKnowledgeGraph != null) {
            recordKnowledgeGraph.setAutoMerge(
                    KnowledgeEnum.AlignWay.AUTO.getCode().equals(recordKnowledgeReq.getAlignWay()));
            recordKnowledgeGraph.setConceptAutoMegerAttrMap(recordKnowledgeModelDto.getConceptAutoMegerAttrMap());

            //保存到图数据库
            saveRecordKnowledgeItem2Db(recordKnowledgeGraph);
            recordGraphRpcEntrance.save2Graph(Collections.singletonList(recordKnowledgeGraph));
        }
    }

    /**
     * @param recordIds
     * @return
     * @see com.suwell.plss.record.service.impl.RepositoryServiceImpl#listRepoInfo(Long recordId)
     */
    @Override
    public List<KnowledgeInfoSourceResp> queryKnowledgeSourceRecord(List<Long> recordIds) {
        List<KnowledgeInfoSourceResp> respList = new ArrayList<>();

        //查询文件
        List<Record> recordList = recordService.getListByIds(recordIds);
        Map<Long, String> recordMap = recordList.stream().collect(Collectors.toMap(Record::getId, Record::getTitle));

        //查询文档的发布日期
        Map<Long, List<MetadataValueDTO>> metadataMap = recordMetadataValueService.queryByBatchRecordId(recordIds);
        //查询文档所在库
        List<MqRecordPathDTO> recordRel = recordService.getRecordRel(recordIds);
        Map<Long, MqRecordPathDTO> pathMap = recordRel.stream()
                .collect(Collectors.toMap(MqRecordPathDTO::getRecordId, Function.identity()));

        for (Long recordId : recordIds) {
            KnowledgeInfoSourceResp knowledgeInfoSourceResp = new KnowledgeInfoSourceResp();
            respList.add(knowledgeInfoSourceResp);

            knowledgeInfoSourceResp.setRecordId(recordId);
            //设置文档标题
            knowledgeInfoSourceResp.setTitle(recordMap.get(recordId));

            //设置发布日期
            List<MetadataValueDTO> metadataList = metadataMap.get(recordId);
            if (CollUtil.isNotEmpty(metadataList)) {
                Map<String, String> metadataNameMap = metadataList.stream().collect(
                        Collectors.toMap(MetadataValueDTO::getMdName, MetadataValueDTO::getMdValue));
                knowledgeInfoSourceResp.setDate(metadataNameMap.get("发布日期"));
            }

            MqRecordPathDTO mqRecordPathDTO = pathMap.get(recordId);
            if (mqRecordPathDTO != null) {
                Set<String> repos = new HashSet<>();
                mqRecordPathDTO.getRelList().stream().forEach(rel -> {
                    RepositoryResp repositoryResp = repositoryService.queryById(rel.getRepoId());
                    if (repositoryResp != null) {
                        repos.add(repositoryResp.getName());
                    }
                });
                //设置来源库
                knowledgeInfoSourceResp.setRepos(Joiner.on(",").join(repos));
            }
        }
        return respList;
    }


    private void saveRecordKnowledge2Db(RecordKnowledgeReq recordKnowledgeReq, boolean init) {
        Long recordId = recordKnowledgeReq.getRecordId();
        String title = recordKnowledgeReq.getTitle();
        Integer alignWay = recordKnowledgeReq.getAlignWay();
        String modelSetting = recordKnowledgeReq.getModelSetting();
        List<RecordKnowledgeAiExtractItem> knowledgeItemList = recordKnowledgeReq.getKnowledgeItemList();
        RecordKnowledge recordKnowledge = recordKnowledgeService.getOne(new LambdaQueryWrapper<RecordKnowledge>()
                .eq(RecordKnowledge::getRecordId, recordId));
        if (init) {
            //初始化
            if (recordKnowledge != null) {
                recordKnowledge.setModelSetting(modelSetting);
                recordKnowledge.setAlignWay(alignWay);
                recordKnowledge.setUpdateTime(new Date());
            } else {
                recordKnowledge = new RecordKnowledge();
                recordKnowledge.setId(wfgIdGenerator.next());
                recordKnowledge.setTitle(title);
                recordKnowledge.setRecordId(recordId);
                recordKnowledge.setModelSetting(modelSetting);
                recordKnowledge.setAlignWay(alignWay);
                recordKnowledge.setCreateTime(new Date());
                recordKnowledge.setUpdateTime(new Date());
            }
        } else {
            //ai回调后更新
            Assert.notNull(recordKnowledge, "文档id:" + recordId + "知识提取记录不存在");
            String contentJson = null;
            if (!CollectionUtils.isEmpty(knowledgeItemList)) {
                contentJson = JSON.toJSONString(knowledgeItemList);
            }
            recordKnowledge.setUpdateTime(new Date());
            recordKnowledge.setContentJson(contentJson);
        }
        //保存db
        recordKnowledgeService.saveOrUpdate(recordKnowledge);
    }

    private void saveRecordKnowledgeItem2Db(RecordKnowledgeGraph recordKnowledgeGraph) {
        Long recordId = recordKnowledgeGraph.getRecordId();
        //先删除原数据
        recordKnowledgeRelationService.remove(
                new LambdaQueryWrapper<RecordKnowledgeRelation>().eq(RecordKnowledgeRelation::getRecordId, recordId));
        List<RecordKnowledgeAttribute> recordKnowledgeAttributes = new ArrayList<>();
        List<RecordKnowledgeGraphSubject> subjectList = recordKnowledgeGraph.getSubjectList();
        List<RecordKnowledgeRelation> recordKnowledgeRelations = new ArrayList<>();
        for (RecordKnowledgeGraphSubject graphSubject : subjectList) {
            List<RecordKnowledgeGraphPredicate> predicateList = graphSubject.getPredicateList();
            if(!graphSubject.isExitsInGraph()){
                addKnowledgeAttribute(recordKnowledgeAttributes, graphSubject.getUniqueId(),
                        graphSubject.getAttributeMap(), 2);
            }
            for (RecordKnowledgeGraphPredicate graphPredicate : predicateList) {
                RecordKnowledgeGraphObject graphObject = graphPredicate.getGraphObject();
                if (graphObject == null) {
                    continue;
                }
                RecordKnowledgeRelation recordKnowledgeRelation = new RecordKnowledgeRelation();
                recordKnowledgeRelation.setId(wfgIdGenerator.next());
                recordKnowledgeRelation.setRecordId(recordId);
                recordKnowledgeRelation.setFromConceptName(graphSubject.getLabel());
                recordKnowledgeRelation.setFromConceptValue(graphSubject.getValue());
                recordKnowledgeRelation.setFromNodeId(graphSubject.getUniqueId());
                recordKnowledgeRelation.setToConceptName(graphObject.getLabel());
                recordKnowledgeRelation.setToConceptValue(graphObject.getValue());
                recordKnowledgeRelation.setToNodeId(graphObject.getUniqueId());
                recordKnowledgeRelation.setRelationName(graphPredicate.getRelName());
                recordKnowledgeRelation.setRelationNodeId(graphPredicate.getUniqueId());
                recordKnowledgeRelation.setCreateTime(new Date());
                recordKnowledgeRelation.setUpdateTime(new Date());
                recordKnowledgeRelations.add(recordKnowledgeRelation);
                //如果节点存在就不再添加
                if(!graphObject.isExitsInGraph()){
                    addKnowledgeAttribute(recordKnowledgeAttributes, graphObject.getUniqueId(),
                            graphObject.getAttributeMap(), 2);
                }
                addKnowledgeAttribute(recordKnowledgeAttributes, graphPredicate.getUniqueId(),
                        graphPredicate.getAttributeMap(), 4);
            }
        }
        if (!CollectionUtils.isEmpty(recordKnowledgeRelations)) {
            recordKnowledgeRelationService.saveBatch(recordKnowledgeRelations);
        }
        if (!CollectionUtils.isEmpty(recordKnowledgeAttributes)) {
            recordKnowledgeAttributeService.saveBatch(recordKnowledgeAttributes);
        }
    }

    private void addKnowledgeAttribute(List<RecordKnowledgeAttribute> recordKnowledgeAttributes, Long nodeId
            , Map<String, Object> attributeMap, int type) {
        if (!CollectionUtils.isEmpty(attributeMap)) {
            RecordKnowledgeAttribute attribute = new RecordKnowledgeAttribute();
            attribute.setNodeId(nodeId);
            attribute.setId(wfgIdGenerator.next());
            attribute.setType(type);
            attribute.setAttribute(JSON.toJSONString(attributeMap));
            attribute.setCreateTime(new Date());
            attribute.setUpdateTime(new Date());
            recordKnowledgeAttributes.add(attribute);
        }
    }


    public RecordKnowledgeGraph resolveAiKnowledge2Graph(RecordKnowledgeReq recordKnowledgeReq,
            String recordModelSetting) {
        Long recordId = recordKnowledgeReq.getRecordId();
        String title = recordKnowledgeReq.getTitle();
        List<RecordKnowledgeAiExtractItem> knowledgeItemList = recordKnowledgeReq.getKnowledgeItemList();
        if (StringUtils.isEmpty(recordModelSetting)) {
            log.warn("文档id:{}ai提取知识模型配置为空", recordId);
            return null;
        }
        RecordKnowledgeGraph recordKnowledgeGraph = new RecordKnowledgeGraph();
        recordKnowledgeGraph.setRecordId(recordId);
        recordKnowledgeGraph.setTitle(title);
        List<RecordKnowledgeGraphSubject> subjectList = new ArrayList<>();
        recordKnowledgeGraph.setSubjectList(subjectList);
        RecordKnowledgeModelDto recordKnowledgeModelDto = JSON.parseObject(recordModelSetting,
                RecordKnowledgeModelDto.class);
        List<RecordKnowledgeModelConceptDto> subjectConceptList = recordKnowledgeModelDto.getSubjectConceptList();
        if (CollectionUtils.isEmpty(subjectConceptList)) {
            log.warn("文档id:{}文档知识模型配置主体为空", recordId);
            return null;
        }
        //主体属性
        Map<String, List<String>> conceptAttributeMap = new HashMap<>();
        //主体关系
        Map<String, Map<String, String>> subjectRelationMap = new HashMap<>();
        for (RecordKnowledgeModelConceptDto conceptDto : subjectConceptList) {
            String conceptName = conceptDto.getConceptName();
            if (StringUtils.isEmpty(conceptName)) {
                continue;
            }
            List<RecordKnowledgeModelAttributeDto> attributeList = conceptDto.getAttributeList();
            if (!CollectionUtils.isEmpty(attributeList)) {
                conceptAttributeMap.put(conceptName, attributeList.stream()
                        .map(RecordKnowledgeModelAttributeDto::getAttributeName).collect(Collectors.toList()));
            }
            List<RecordKnowledgeModelRelationDto> relationList = conceptDto.getRelationList();
            if (!CollectionUtils.isEmpty(relationList)) {
                Map<String, String> relationMap = new HashMap<>();
                subjectRelationMap.put(conceptName, relationMap);
                for (RecordKnowledgeModelRelationDto relationDto : relationList) {
                    String relationName = relationDto.getRelationName();
                    RecordKnowledgeModelConceptDto toConcept = relationDto.getToConcept();
                    if (toConcept == null) {
                        continue;
                    }
                    relationMap.put(relationName, toConcept.getConceptName());
                }
            }
        }
        for (RecordKnowledgeAiExtractItem recordKnowledgeAiExtractItem : knowledgeItemList) {
            RecordKnowledgeSubject subject = recordKnowledgeAiExtractItem.getSubject();
            if (subject == null) {
                continue;
            }
            String type = subject.getType();
            String value = subject.getValue();
            //过滤不在模型中的主体（不受控）数据 和值为 “无”的数据
            if (StringUtils.isEmpty(type) || StringUtils.isEmpty(value) ||
                    subjectRelationMap.get(type) == null || "无".equals(value)) {
                continue;
            }
            RecordKnowledgeGraphSubject graphSubject = new RecordKnowledgeGraphSubject();
            graphSubject.setLabel(type);
            graphSubject.setValue(value);
            graphSubject.setUniqueId(wfgIdGenerator.next());

            subjectList.add(graphSubject);
            //按照模型对Ai提取的知识进行解析
            List<RecordKnowledgePredicate> predicates = recordKnowledgeAiExtractItem.getPredicates();
            if (CollectionUtils.isEmpty(predicates)) {
                continue;
            }
            Map<String, Object> attributeMap = new HashMap<>();
            graphSubject.setAttributeMap(attributeMap);
            List<RecordKnowledgeGraphPredicate> predicateList = new ArrayList<>();
            graphSubject.setPredicateList(predicateList);
            List<String> subjectAttributes = conceptAttributeMap.getOrDefault(type, new ArrayList<>());
            for (RecordKnowledgePredicate knowledgePredicate : predicates) {
                String predicate = knowledgePredicate.getPredicate();
                String objectValue = knowledgePredicate.getObject();
                Long objectRelRecordId = knowledgePredicate.getObjectRelRecordId();
                if (StringUtils.isEmpty(predicate) || StringUtils.isEmpty(objectValue)
                        || "无".equals(objectValue)) {
                    continue;
                }
                if (subjectAttributes.contains(predicate)) {
                    //提取出了属性
                    attributeMap.put(predicate, objectValue);
                }
                Map<String, String> relationMap = subjectRelationMap.get(type);
                if (CollectionUtils.isEmpty(relationMap)) {
                    continue;
                }
                String relationObject = relationMap.get(predicate);
                if (StringUtils.isNotEmpty(relationObject)) {
                    //提取出了关系
                    RecordKnowledgeGraphPredicate graphPredicate = new RecordKnowledgeGraphPredicate();
                    graphPredicate.setRelName(predicate);
                    graphPredicate.setUniqueId(wfgIdGenerator.next());
                    RecordKnowledgeGraphObject graphObject = new RecordKnowledgeGraphObject();
                    graphObject.setLabel(relationObject);
                    graphObject.setValue(objectValue);
                    graphObject.setUniqueId(wfgIdGenerator.next());
                    graphObject.setRecordId(objectRelRecordId);
                    graphPredicate.setGraphObject(graphObject);
                    predicateList.add(graphPredicate);
                }
            }
        }
        return recordKnowledgeGraph;
    }

    private static List<Map<String, Set<String>>> getModelSetting4Ai(RecordKnowledgeModelDto recordKnowledgeModelDto) {
        if (recordKnowledgeModelDto == null) {
            return null;
        }
        List<RecordKnowledgeModelConceptDto> subjectConceptList = recordKnowledgeModelDto.getSubjectConceptList();
        if (CollectionUtils.isEmpty(subjectConceptList)) {
            return null;
        }
        List<Map<String, Set<String>>> modelSetting4AiMap = Lists.newArrayList();
        for (RecordKnowledgeModelConceptDto conceptDto : subjectConceptList) {
            Map<String, Set<String>> predicates = new HashMap<>();
            String conceptName = conceptDto.getConceptName();
            Set<String> relationOrAttributes = new HashSet<>();
            List<RecordKnowledgeModelRelationDto> relationList = conceptDto.getRelationList();
            if (!CollectionUtils.isEmpty(relationList)) {
                for (RecordKnowledgeModelRelationDto relationDto : relationList) {
                    relationOrAttributes.add(relationDto.getRelationName());
                }
            }
            List<RecordKnowledgeModelAttributeDto> attributeList = conceptDto.getAttributeList();
            if (!CollectionUtils.isEmpty(attributeList)) {
                for (RecordKnowledgeModelAttributeDto attributeDto : attributeList) {
                    relationOrAttributes.add(attributeDto.getAttributeName());
                }
            }
            if (!CollectionUtils.isEmpty(relationOrAttributes)) {
                predicates.put(conceptName, relationOrAttributes);
                modelSetting4AiMap.add(predicates);
            }
        }
        return modelSetting4AiMap;
    }


    private RecordKnowledgeModelDto resolveRecordModelSetting(KnowledgeModel knowledgeModel) {
        String modelSetting = knowledgeModel.getModelSetting();
        if (StringUtils.isEmpty(modelSetting)) {
            return null;
        }
        ModelSettingDTO modelSettingDTO = JSON.parseObject(modelSetting, ModelSettingDTO.class);
        if (modelSettingDTO == null) {
            return null;
        }
        List<ModelSettingNodeDTO> nodes = modelSettingDTO.getNodes();
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        RecordKnowledgeModelDto recordKnowledgeModelDto = new RecordKnowledgeModelDto();
        recordKnowledgeModelDto.setConceptAutoMegerAttrMap(buildConceptAutoMergeAttrMap(nodes));
        recordKnowledgeModelDto.setId(knowledgeModel.getId());
        recordKnowledgeModelDto.setName(knowledgeModel.getName());
        List<RecordKnowledgeModelConceptDto> subjectConceptList = new ArrayList<>();
        recordKnowledgeModelDto.setSubjectConceptList(subjectConceptList);
        List<ModelSettingEdgDTO> edges = modelSettingDTO.getEdges();

        //处理双向对称的 因为双向对称前端只生成一条关系，所以后台处理要生成俩条关系
        List<ModelSettingEdgDTO> forwardEdges = new ArrayList<>();
        for (ModelSettingEdgDTO edge : edges) {
            if ("1".equals(edge.getSymmetry())) {
                ModelSettingEdgDTO forwardEdge = new ModelSettingEdgDTO();
                forwardEdge.setSource(edge.getTarget());
                forwardEdge.setTarget(edge.getSource());
                forwardEdge.setLabel(edge.getLabel());
                forwardEdge.setSymmetry("2");
                forwardEdge.setAttributes(edge.getAttributes());
                forwardEdges.add(forwardEdge);
            }
        }
        edges.addAll(forwardEdges);
        Map<Long, ModelSettingNodeDTO> modelNodeMap = nodes.stream()
                .collect(Collectors.toMap(ModelSettingNodeDTO::getId, Function.identity(), (v1, v2) -> v2));
        Map<Long, List<ModelSettingEdgDTO>> sourceNodeMap = edges.stream()
                .collect(Collectors.groupingBy(ModelSettingEdgDTO::getSource));
        for (ModelSettingNodeDTO node : nodes) {
            Long nodeId = node.getId();
            RecordKnowledgeModelConceptDto fromConcept = convertModelSettingNode2RecordConcept(node);
            List<ModelSettingEdgDTO> sourceNodeEdges = sourceNodeMap.get(nodeId);
            if (!CollectionUtils.isEmpty(sourceNodeEdges)) {
                List<RecordKnowledgeModelRelationDto> recordModelRelationList = new ArrayList<>();
                fromConcept.setRelationList(recordModelRelationList);
                for (ModelSettingEdgDTO sourceNodeEdge : sourceNodeEdges) {
                    Long targetNodeId = sourceNodeEdge.getTarget();
                    String label = sourceNodeEdge.getLabel();
                    if (targetNodeId == null || modelNodeMap.get(targetNodeId) == null || StringUtils.isEmpty(label)) {
                        continue;
                    }
                    RecordKnowledgeModelRelationDto relationDto = new RecordKnowledgeModelRelationDto();
                    relationDto.setRelationName(sourceNodeEdge.getLabel());
                    List<ModelSettingAttributeDTO> relationAttributes = sourceNodeEdge.getAttributes();
                    if (!CollectionUtils.isEmpty(relationAttributes)) {
                        relationDto.setAttributeList(relationAttributes.stream().map(attribute -> {
                            RecordKnowledgeModelAttributeDto attributeDto = new RecordKnowledgeModelAttributeDto();
                            attributeDto.setAttributeName(attribute.getName());
                            return attributeDto;
                        }).collect(Collectors.toList()));
                    }
                    RecordKnowledgeModelConceptDto targetConcept = convertModelSettingNode2RecordConcept(
                            modelNodeMap.get(targetNodeId));
                    relationDto.setToConcept(targetConcept);
                    recordModelRelationList.add(relationDto);
                }
            }
            subjectConceptList.add(fromConcept);
        }
        return recordKnowledgeModelDto;
    }

    /**
     *
     * 构建概念的自动合并属性
     *
     * @param nodes 概念列表
     * @return 概念 属性列表映射
     */
    private Map<String,List<String>> buildConceptAutoMergeAttrMap(List<ModelSettingNodeDTO> nodes){
        Map<String,List<String>> conceptAutoMergeAttrMap = new HashMap<>(nodes.size());
        List<Long> conceptIds = nodes.stream().map(ModelSettingNodeDTO::getId).toList();
        List<KnowledgeConceptAttribute> attributeList = knowledgeConceptAttributeService.list(
                Wrappers.<KnowledgeConceptAttribute>lambdaQuery()
                        .select(KnowledgeConceptAttribute::getConceptId,
                                KnowledgeConceptAttribute::getAttributeId,
                                KnowledgeConceptAttribute::getAutoAlign)
                        .in(KnowledgeConceptAttribute::getConceptId, conceptIds));
        Map<Long,String> conceptIdNameMap = nodes.stream().collect(Collectors.toMap(ModelSettingNodeDTO::getId,ModelSettingNodeDTO::getLabel));
        if(CollUtil.isNotEmpty(attributeList)){
            List<Long> attrIds = attributeList.stream().map(KnowledgeConceptAttribute::getAttributeId).distinct().toList();
            List<KnowledgeAttribute> attributes = knowledgeAttributeService.listByIds(attrIds);
            Map<Long, String> attributeMap = attributes.stream().collect(Collectors.toMap(KnowledgeAttribute::getId, KnowledgeAttribute::getName));
            for (KnowledgeConceptAttribute conceptAttribute : attributeList) {
                String conceptName = conceptIdNameMap.get(conceptAttribute.getConceptId());
                String attributeName = attributeMap.get(conceptAttribute.getAttributeId());
                if(conceptAttribute.getAutoAlign() == 1 && StringUtils.isNotEmpty(conceptName)){
                    List<String> attrList = conceptAutoMergeAttrMap.computeIfAbsent(conceptName, k -> new ArrayList<>());
                    attrList.add(attributeName);
                }
            }
        }
        return conceptAutoMergeAttrMap;
    }


    /**
     * 解析知识三元组
     *
     * @param knowledgeItemList 知识三元组
     */
    private void resolveRecordKnowledge(List<RecordKnowledgeAiExtractItem> knowledgeItemList) {
        if (CollectionUtils.isEmpty(knowledgeItemList)) {
            return;
        }
        //正则匹配被《》包裹的内容
        List<String> titleList = new ArrayList<>();
        for (int i = 0; i < knowledgeItemList.size(); i++) {
            RecordKnowledgeAiExtractItem knowledgeItem = knowledgeItemList.get(i);
            RecordKnowledgeSubject subject = knowledgeItem.getSubject();
            if (subject == null) {
                continue;
            }
            List<RecordKnowledgePredicate> predicates = knowledgeItem.getPredicates();
            if (CollectionUtils.isEmpty(predicates)) {
                continue;
            }
            for (RecordKnowledgePredicate predicate : predicates) {
                String object = predicate.getObject();
                if (StringUtils.isNotEmpty(object) && object.startsWith("《") && object.endsWith("》")) {
                    String recordTitle = object.substring(1, object.length() - 1);
                    predicate.setObjectRelRecordTitle(object);
                    titleList.add(recordTitle);
                }
            }
        }
        if (!CollectionUtils.isEmpty(titleList)) {
            EsSearchWrapper<RecordEsIndexEntity> searchWrapper = new EsSearchWrapper<>();
            searchWrapper.like("title", titleList).page(1).pageSize(1000);
            R<PageUtils<RecordEsIndexEntity>> pageUtilsR = recordSearchRpcEntrance.pageList(searchWrapper);
            if(pageUtilsR.isError()){
                log.error("根据标题查询es数据失败");
                return;
            }
            List<RecordEsIndexEntity> recordEsIndexEntityList = pageUtilsR.getData().getList();
            List<Record> relRecordList = recordEsIndexEntityList.stream().map(recordEsIndexEntity -> {
                Record record = new Record();
                record.setId(recordEsIndexEntity.getRecordId());
                record.setTitle(recordEsIndexEntity.getTitle());
                return record;
            }).toList();
            Map<String, List<Record>> recordTitleMap = relRecordList.stream()
                    .collect(Collectors.groupingBy(Record::getTitle));
            for (RecordKnowledgeAiExtractItem knowledgeItem : knowledgeItemList) {
                RecordKnowledgeSubject subject = knowledgeItem.getSubject();
                if (subject == null) {
                    continue;
                }
                List<RecordKnowledgePredicate> predicates = knowledgeItem.getPredicates();
                if (CollectionUtils.isEmpty(predicates)) {
                    continue;
                }
                for (RecordKnowledgePredicate predicate : predicates) {
                    String object = predicate.getObject();
                    List<Record> records = recordTitleMap.get(object);
                    if (!CollectionUtils.isEmpty(records)) {
                        Record record = records.get(0);
                        predicate.setObjectRelRecordId(record.getId());
                    }
                }
            }
        }
    }

    private static RecordKnowledgeModelConceptDto convertModelSettingNode2RecordConcept(ModelSettingNodeDTO node) {
        String nodeLabel = node.getLabel();
        RecordKnowledgeModelConceptDto recordKnowledgeModelConceptDto = new RecordKnowledgeModelConceptDto();
        recordKnowledgeModelConceptDto.setConceptName(nodeLabel);
        List<ModelSettingAttributeDTO> attributes = node.getAttributeList();
        if (!CollectionUtils.isEmpty(attributes)) {
            recordKnowledgeModelConceptDto.setAttributeList(attributes.stream().map(attribute -> {
                RecordKnowledgeModelAttributeDto attributeDto = new RecordKnowledgeModelAttributeDto();
                attributeDto.setAttributeName(attribute.getName());
                return attributeDto;
            }).collect(Collectors.toList()));
        }
        return recordKnowledgeModelConceptDto;
    }

    @Override
    public RecordKnowledgeQueryResp recordDetail(Long recordId) {
        List<RecordKnowledgeRelation> recordKnowledgeRelations = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .orderByDesc(RecordKnowledgeRelation::getCreateTime));

        RecordKnowledgeQueryResp resp = new RecordKnowledgeQueryResp();
        resp.setRecordId(recordId);
        if (!CollectionUtils.isEmpty(recordKnowledgeRelations)) {
            List<Long> fromNodeIdList = recordKnowledgeRelations.stream().map(RecordKnowledgeRelation::getFromNodeId)
                    .collect(Collectors.toList());
            List<Long> toNodeIdList = recordKnowledgeRelations.stream().map(RecordKnowledgeRelation::getToNodeId)
                    .collect(Collectors.toList());
            List<Long> relationNodeIdList = recordKnowledgeRelations.stream()
                    .map(RecordKnowledgeRelation::getRelationNodeId).collect(Collectors.toList());
            List<Long> attributeNodes = new ArrayList<>();
            attributeNodes.addAll(fromNodeIdList);
            attributeNodes.addAll(toNodeIdList);
            attributeNodes.addAll(relationNodeIdList);
            attributeNodes = attributeNodes.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<RecordKnowledgeQueryItemResp> queryItemRespList = new ArrayList<>();
            resp.setList(queryItemRespList);
            Map<String, List<RecordKnowledgeRelation>> relationMap = recordKnowledgeRelations.stream()
                    .collect(Collectors.groupingBy(RecordKnowledgeRelation::getFromConceptName));
            List<RecordKnowledgeAttribute> attributeList = recordKnowledgeAttributeService.list(
                    new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                            .in(RecordKnowledgeAttribute::getNodeId, attributeNodes));
            Map<Long, RecordKnowledgeAttribute> attributeMap = attributeList.stream()
                    .collect(Collectors.toMap(RecordKnowledgeAttribute::getNodeId, Function.identity()));
            //嵌套循环多次 每层级数据可控 时间不会太长
            for (Map.Entry<String, List<RecordKnowledgeRelation>> entry : relationMap.entrySet()) {
                String key = entry.getKey();
                List<RecordKnowledgeRelation> items = entry.getValue();
                RecordKnowledgeQueryItemResp queryItemResp = new RecordKnowledgeQueryItemResp();
                queryItemRespList.add(queryItemResp);
                queryItemResp.setType(1);
                queryItemResp.setLabel(key);
                Map<Long, List<RecordKnowledgeRelation>> fromNodeMap = items.stream()
                        .collect(Collectors.groupingBy(RecordKnowledgeRelation::getFromNodeId));
                for (Map.Entry<Long, List<RecordKnowledgeRelation>> fromNodeEntry : fromNodeMap.entrySet()) {
                    Long fromNodeId = fromNodeEntry.getKey();
                    List<RecordKnowledgeRelation> fromNodeList = fromNodeEntry.getValue();
                    RecordKnowledgeQueryItemResp valueChild = new RecordKnowledgeQueryItemResp();
                    valueChild.setType(2);
                    //实例顺序按照创建时间排序
                    valueChild.setSort(
                            fromNodeList.stream().max(Comparator.comparing(RecordKnowledgeRelation::getCreateTime))
                                    .map(a -> -a.getCreateTime().getTime()).orElse(0L));
                    //来自同一个节点的概念和实例一定相同
                    RecordKnowledgeRelation fromNode = fromNodeList.get(0);
                    valueChild.setLabel(fromNode.getFromConceptValue());
                    valueChild.setNodeId(fromNodeId);
                    queryItemResp.getChildren().add(valueChild);
                    //解析属性和关系
                    RecordKnowledgeAttribute recordAttribute = attributeMap.get(fromNodeId);
                    if (recordAttribute != null && StringUtils.isNotEmpty(recordAttribute.getAttribute())) {
                        JSONObject attributeJson = JSON.parseObject(recordAttribute.getAttribute());
                        for (Map.Entry<String, Object> attributeEntry : attributeJson.entrySet()) {
                            RecordKnowledgeQueryItemResp attributeChild = new RecordKnowledgeQueryItemResp();
                            attributeChild.setType(3);
                            attributeChild.setLabel(attributeEntry.getKey());
                            attributeChild.setValue(attributeEntry.getValue().toString());
                            attributeChild.setSort(1L);
                            valueChild.getChildren().add(attributeChild);
                        }
                    }
                    for (RecordKnowledgeRelation recordKnowledgeRelation : fromNodeList) {
                        Long relationNodeId = recordKnowledgeRelation.getRelationNodeId();
                        if (relationNodeId != null) {
                            //存在关系
                            RecordKnowledgeQueryItemResp relationChild = new RecordKnowledgeQueryItemResp();
                            relationChild.setType(4);
                            relationChild.setLabel(recordKnowledgeRelation.getRelationName());
                            relationChild.setNodeId(relationNodeId);
                            relationChild.setValue(recordKnowledgeRelation.getToConceptValue());
                            relationChild.setTargetId(recordKnowledgeRelation.getToNodeId());
                            relationChild.setTargetLabel(recordKnowledgeRelation.getToConceptName());
                            relationChild.setSort(-recordKnowledgeRelation.getCreateTime().getTime());
                            valueChild.getChildren().add(relationChild);
                            //客体属性
                            //关系的属性
                            RecordKnowledgeAttribute objectAttribute = attributeMap.get(
                                    recordKnowledgeRelation.getToNodeId());
                            if (objectAttribute != null && StringUtils.isNotEmpty(objectAttribute.getAttribute())) {
                                JSONObject objectAttributeJson = JSON.parseObject(objectAttribute.getAttribute());
                                //客体为系统存在的文件
                                if (objectAttributeJson.containsKey("objectRecordId")) {
                                    relationChild.setRecordId(objectAttributeJson.getLong("objectRecordId"));
                                }
                            }
                            //关系的属性
                            RecordKnowledgeAttribute relationAttribute = attributeMap.get(
                                    recordKnowledgeRelation.getRelationNodeId());
                            if (relationAttribute != null && StringUtils.isNotEmpty(relationAttribute.getAttribute())) {
                                JSONObject attributeJson = JSON.parseObject(relationAttribute.getAttribute());
                                for (Map.Entry<String, Object> attributeEntry : attributeJson.entrySet()) {
                                    RecordKnowledgeQueryItemResp attributeChild = new RecordKnowledgeQueryItemResp();
                                    String attributeKey = attributeEntry.getKey();
                                    String value = attributeEntry.getValue().toString();
                                    attributeChild.setType(3);
                                    attributeChild.setLabel(attributeKey);
                                    attributeChild.setValue(value);
                                    attributeChild.setSort(1L);
                                    relationChild.getChildren().add(attributeChild);
                                }
                            }
                        }
                    }
                    valueChild.getChildren().sort(Comparator.comparing(RecordKnowledgeQueryItemResp::getSort));
                }
                queryItemResp.getChildren().sort(Comparator.comparing(RecordKnowledgeQueryItemResp::getSort));
            }
        }
        KnowledgeModelScopeReq knowledgeModelScopeReq = new KnowledgeModelScopeReq();
        knowledgeModelScopeReq.setRecordId(recordId);
        //概念范围
        List<String> conceptScope = standardKnowledgeFacade.modelScopeConcept(knowledgeModelScopeReq);
        List<RecordKnowledgeQueryItemResp> conceptList = resp.getList();
        List<String> recordConceptList = conceptList.stream().map(RecordKnowledgeQueryItemResp::getLabel)
                .collect(Collectors.toList());
        conceptScope.removeAll(recordConceptList);
        if (CollUtil.isNotEmpty(conceptScope)) {
            for (String concept : conceptScope) {
                RecordKnowledgeQueryItemResp queryItemResp = new RecordKnowledgeQueryItemResp();
                queryItemResp.setLabel(concept);
                queryItemResp.setType(1);
                resp.getList().add(queryItemResp);
            }
        }
        return resp;
    }


    @Override
    public RecordKnowledgeInfoResp queryRecordInfo(Long recordId) {
        RecordKnowledge recordKnowledge = recordKnowledgeService.getOne(new LambdaQueryWrapper<RecordKnowledge>()
                .eq(RecordKnowledge::getRecordId, recordId));
        RecordKnowledgeInfoResp resp = new RecordKnowledgeInfoResp();
        if (recordKnowledge == null) {
            resp.setKnowledgeSwitch(false);
            resp.setManualAlign(false);
        } else {
            resp.setKnowledgeSwitch(true);
            resp.setManualAlign(Objects.equals(recordKnowledge.getAlignWay(), 2));
        }
        return resp;
    }

    @Override
    public List<RecordKnowledgeConflictCountResp> queryConflictList(Long recordId) {
        List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRecordId, recordId));
        Map<String, List<RecordKnowledgeRelation>> fromConceptMap = relationList.stream()
                .collect(Collectors.groupingBy(RecordKnowledgeRelation::getFromConceptName));
        List<LabelValueReq> labelValueReqList = new ArrayList<>();
        for (Map.Entry<String, List<RecordKnowledgeRelation>> entry : fromConceptMap.entrySet()) {
            LabelValueReq labelValueReq = new LabelValueReq();
            labelValueReq.setLabel(entry.getKey());
            labelValueReq.setValues(entry.getValue().stream().map(RecordKnowledgeRelation::getFromConceptValue)
                    .collect(Collectors.toSet()));
            labelValueReqList.add(labelValueReq);
        }
        ConflictQueryReq conflictQueryReq = new ConflictQueryReq();
        conflictQueryReq.setLabelValueReqList(labelValueReqList);
        conflictQueryReq.setRecordId(recordId);
        log.info("根据recordId:{} 查询实例列表参数:{}", recordId, JSON.toJSONString(conflictQueryReq));
        R<List<LabelValueResp>> listR = recordGraphRpcEntrance.queryConflictByRecordId(conflictQueryReq);
        if (listR.isError()) {
            throw new IllegalArgumentException("查询冲突数据失败" + listR.getMsg());
        }
        List<LabelValueResp> labelValueRespList = listR.getData();
        if (CollectionUtils.isEmpty(labelValueRespList)) {
            return Collections.emptyList();
        }
        List<RecordKnowledgeConflictCountResp> conflictList = new ArrayList<>();
        Map<String, List<LabelValueResp>> labelValueRespMap = labelValueRespList.stream()
                .collect(Collectors.groupingBy(LabelValueResp::getLabel));
        List<Long> sourceNodeIds = relationList.stream().map(RecordKnowledgeRelation::getFromNodeId)
                .collect(Collectors.toList());
        //不予合并冲突列表
        List<RecordKnowledgeConflict> refuseMergeConflictList = recordKnowledgeConflictService.list(
                new LambdaQueryWrapper<RecordKnowledgeConflict>().in(RecordKnowledgeConflict::getSourceId,
                        sourceNodeIds));
        List<Long> refuseMergeSourceIds = refuseMergeConflictList.stream().map(RecordKnowledgeConflict::getSourceId)
                .collect(Collectors.toList());
        List<Long> refuseMergeTargetIds = refuseMergeConflictList.stream().map(RecordKnowledgeConflict::getTargetId)
                .collect(Collectors.toList());
        List<Long> refuseMergeIds = new ArrayList<>();
        refuseMergeIds.addAll(refuseMergeSourceIds);
        refuseMergeIds.addAll(refuseMergeTargetIds);
        for (Map.Entry<String, List<LabelValueResp>> entry : labelValueRespMap.entrySet()) {
            String key = entry.getKey();
            List<LabelValueResp> labelValues = entry.getValue();
            if (!CollectionUtils.isEmpty(labelValues)) {
                RecordKnowledgeConflictCountResp conflictResp = new RecordKnowledgeConflictCountResp();
                conflictList.add(conflictResp);
                conflictResp.setConflictLabel(key);
                Map<String, List<LabelValueResp>> valueListMap = labelValues.stream()
                        .collect(Collectors.groupingBy(LabelValueResp::getValue));
                for (Map.Entry<String, List<LabelValueResp>> valueEntry : valueListMap.entrySet()) {
                    RecordKnowledgeConflictValueCountResp conflictValueResp = new RecordKnowledgeConflictValueCountResp();
                    //排除不予合并项
                    List<LabelValueResp> valueRespList = valueEntry.getValue();
                    valueRespList.removeIf(next -> refuseMergeIds.contains(next.getUniqueId()));
                    if (!CollectionUtils.isEmpty(valueRespList)) {
                        conflictValueResp.setValue(valueEntry.getKey());
                        conflictValueResp.setCount(valueRespList.size());
                        conflictResp.getConflictValues().add(conflictValueResp);
                    }
                }
            }
        }
        conflictList.removeIf(conflictCountResp -> CollectionUtils.isEmpty(conflictCountResp.getConflictValues()));
        return conflictList;
    }


    @Override
    public List<RecordKnowledgeConflictDetailResp> queryConflictConceptList(
            RecordKnowledgeConflictValueReq conflictValueReq) {
        String conflictValue = conflictValueReq.getConflictValue();
        Long recordId = conflictValueReq.getRecordId();
        //查当前节点
        List<RecordKnowledgeRelation> fromNodeList = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .eq(RecordKnowledgeRelation::getFromConceptName, conflictValueReq.getConflictLabel())
                        .eq(RecordKnowledgeRelation::getFromConceptValue, conflictValue));
        if (CollectionUtils.isEmpty(fromNodeList)) {
            throw new RuntimeException("根据概念实例未查询到数据");
        }
        ConflictQueryReq conflictQueryReq = new ConflictQueryReq();
        List<LabelValueReq> labelValueReqList = new ArrayList<>();
        LabelValueReq labelValueReq = new LabelValueReq();
        labelValueReq.setLabel(conflictValueReq.getConflictLabel());
        labelValueReq.setValues(Collections.singleton(conflictValue));
        labelValueReqList.add(labelValueReq);
        conflictQueryReq.setLabelValueReqList(labelValueReqList);
        conflictQueryReq.setRecordId(recordId);
        log.info("根据recordId:{} 查询实例列表参数:{}", recordId, JSON.toJSONString(conflictQueryReq));
        R<List<LabelValueResp>> listR = recordGraphRpcEntrance.queryConflictByRecordId(conflictQueryReq);
        if (listR.isError()) {
            throw new IllegalArgumentException("查询冲突数据失败" + listR.getMsg());
        }
        List<LabelValueResp> labelValueRespList = listR.getData();
        if (CollectionUtils.isEmpty(labelValueRespList)) {
            return Collections.emptyList();
        }
        List<RecordKnowledgeConflictDetailResp> conflictDetailList = new ArrayList<>();
        //查询当前节点的属性 不考虑同概念同实例的情况 取第一个
        RecordKnowledgeRelation currentRelation = fromNodeList.get(0);
        RecordKnowledgeConflictDetailResp currentConflictDetail = new RecordKnowledgeConflictDetailResp();
        conflictDetailList.add(currentConflictDetail);
        Long fromNodeId = currentRelation.getFromNodeId();
        currentConflictDetail.setId(fromNodeId);
        currentConflictDetail.setValue(conflictValue);
        currentConflictDetail.setCurrent(true);
        //查询当前节点的关系和属性
        List<RecordKnowledgeRelation> currentNodeRelations = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>().eq(RecordKnowledgeRelation::getFromNodeId,
                        fromNodeId));
        currentConflictDetail.setRelations(currentNodeRelations.stream().map(relation -> {
            RecordKnowledgeConflictValueRelationResp relationResp = new RecordKnowledgeConflictValueRelationResp();
            relationResp.setName(relation.getRelationName());
            relationResp.setValue(relation.getToConceptValue());
            return relationResp;
        }).collect(Collectors.toList()));
        RecordKnowledgeAttribute currentAttribute = recordKnowledgeAttributeService.getOne(
                new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                        .eq(RecordKnowledgeAttribute::getNodeId, fromNodeId));
        if (currentAttribute != null && StringUtils.isNotEmpty(currentAttribute.getAttribute())) {
            JSONObject attributeJson = JSON.parseObject(currentAttribute.getAttribute());
            attributeJson.forEach((key, value) -> {
                RecordKnowledgeAttributeResp attributeResp = new RecordKnowledgeAttributeResp();
                attributeResp.setName(key);
                attributeResp.setValue(value);
                currentConflictDetail.getAttributes().add(attributeResp);
            });
            //属性排序
            currentConflictDetail.getAttributes().sort(Comparator.comparing(RecordKnowledgeAttributeResp::getName));
        }
        //不予合并冲突列表
        List<RecordKnowledgeConflict> refuseMergeConflictList = recordKnowledgeConflictService.list(
                new LambdaQueryWrapper<RecordKnowledgeConflict>().eq(RecordKnowledgeConflict::getSourceId, fromNodeId));
        List<Long> refuseConflictTargetIds = refuseMergeConflictList.stream().map(RecordKnowledgeConflict::getTargetId)
                .collect(Collectors.toList());
        for (LabelValueResp labelValueResp : labelValueRespList) {
            if (refuseConflictTargetIds.contains(labelValueResp.getUniqueId())) {
                continue;
            }
            RecordKnowledgeConflictDetailResp conflictDetailResp = new RecordKnowledgeConflictDetailResp();
            conflictDetailResp.setId(labelValueResp.getUniqueId());
            conflictDetailResp.setValue(conflictValue);
            List<LabelValueAttribute> attributes = labelValueResp.getAttributes();
            for (LabelValueAttribute attribute : attributes) {
                RecordKnowledgeAttributeResp attributeResp = new RecordKnowledgeAttributeResp();
                attributeResp.setName(attribute.getName());
                attributeResp.setValue(attribute.getValue());
                conflictDetailResp.getAttributes().add(attributeResp);
            }
            //属性排序
            conflictDetailResp.getAttributes().sort(Comparator.comparing(RecordKnowledgeAttributeResp::getName));
            List<LabelValueRelation> relations = labelValueResp.getRelations();
            for (LabelValueRelation relation : relations) {
                RecordKnowledgeConflictValueRelationResp relationResp = new RecordKnowledgeConflictValueRelationResp();
                relationResp.setName(relation.getName());
                relationResp.setValue(relation.getValue());
                conflictDetailResp.getRelations().add(relationResp);
            }
            conflictDetailList.add(conflictDetailResp);
        }
        return conflictDetailList;
    }

    @Override
    public void refuseMerge(RecordKnowledgeMergeReq req) {
        Long sourceId = req.getSourceId();
        Long targetId = req.getTargetId();
        RecordKnowledgeConflict conflict = recordKnowledgeConflictService.
                getOne(new LambdaQueryWrapper<RecordKnowledgeConflict>().eq(RecordKnowledgeConflict::getSourceId,
                                sourceId)
                        .eq(RecordKnowledgeConflict::getTargetId, targetId).eq(RecordKnowledgeConflict::getOperate, 1));
        if (conflict != null) {
            return;
        }
        conflict = new RecordKnowledgeConflict();
        conflict.setId(wfgIdGenerator.next());
        conflict.setSourceId(sourceId);
        conflict.setTargetId(targetId);
        conflict.setOperate(1);
        String userId = SecurityUtils.getUserId();
        conflict.setCreateTime(new Date());
        conflict.setUpdateTime(new Date());
        conflict.setCreateBy(userId);
        conflict.setUpdateBy(userId);
        recordKnowledgeConflictService.save(conflict);
    }

    @Override
    public void mergeConflict(RecordKnowledgeMergeReq req) {
        Long sourceId = req.getSourceId();
        Long targetId = req.getTargetId();
        List<RecordKnowledgeAttributeResp> attributes = req.getAttributes();
        RecordKnowledgeConflict conflict = recordKnowledgeConflictService.
                getOne(new LambdaQueryWrapper<RecordKnowledgeConflict>().eq(RecordKnowledgeConflict::getSourceId,
                                sourceId)
                        .eq(RecordKnowledgeConflict::getTargetId, targetId));
        if (conflict != null) {
            return;
        }
        conflict = new RecordKnowledgeConflict();
        conflict.setId(wfgIdGenerator.next());
        conflict.setSourceId(sourceId);
        conflict.setTargetId(targetId);
        JSONObject attributeJson = new JSONObject();
        for (RecordKnowledgeAttributeResp attribute : attributes) {
            attributeJson.put(attribute.getName(), attribute.getValue());
        }
        conflict.setMergeAttribute(attributeJson.toJSONString());
        conflict.setOperate(2);
        String userId = SecurityUtils.getUserId();
        conflict.setCreateTime(new Date());
        conflict.setUpdateTime(new Date());
        conflict.setCreateBy(userId);
        conflict.setUpdateBy(userId);
        recordKnowledgeConflictService.save(conflict);
        //将目标节点合并到当前节点
        RecordKnowledgeRelation recordKnowledgeRelation = new RecordKnowledgeRelation();
        recordKnowledgeRelation.setFromNodeId(sourceId);
        recordKnowledgeRelation.setUpdateBy(userId);
        recordKnowledgeRelation.setUpdateTime(new Date());
        recordKnowledgeRelationService.update(recordKnowledgeRelation, new LambdaQueryWrapper<RecordKnowledgeRelation>()
                .eq(RecordKnowledgeRelation::getFromNodeId, targetId));
        //更新源属性
        RecordKnowledgeAttribute recordKnowledgeAttribute = new RecordKnowledgeAttribute();
        recordKnowledgeAttribute.setAttribute(attributeJson.toJSONString());
        recordKnowledgeAttribute.setUpdateBy(userId);
        recordKnowledgeAttribute.setUpdateTime(new Date());
        recordKnowledgeAttributeService.update(recordKnowledgeAttribute,
                new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                        .eq(RecordKnowledgeAttribute::getNodeId, sourceId));
        //删除目标属性
        recordKnowledgeAttributeService.remove(new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                .eq(RecordKnowledgeAttribute::getNodeId, targetId));
        //更新到图数据库
        MergeNodeReq mergeNodeReq = new MergeNodeReq();
        //接口中sourceNodeId为主动要去合并的节点 targetNodeId为之后要保留的目标节点
        mergeNodeReq.setSourceNodeId(targetId);
        mergeNodeReq.setTargetNodeId(sourceId);
        mergeNodeReq.setProperties(attributeJson);
        recordGraphRpcEntrance.mergeNode(mergeNodeReq);
    }

    @Override
    public ModelSettingDTO queryRecordGraph(Long recordId) {
        Record record = recordService.getOneById(recordId);
        Assert.notNull(record, "文档不存在");
        List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRecordId, recordId));
        ModelSettingDTO modelSettingDTO = new ModelSettingDTO();
        Set<ModelSettingNodeDTO> nodes = new HashSet<>();
        List<ModelSettingEdgDTO> edges = new ArrayList<>();
        for (RecordKnowledgeRelation recordKnowledgeRelation : relationList) {
            Long fromNodeId = recordKnowledgeRelation.getFromNodeId();
            Long toNodeId = recordKnowledgeRelation.getToNodeId();
            ModelSettingNodeDTO fromNode = new ModelSettingNodeDTO();
            fromNode.setId(fromNodeId);
            fromNode.setLabel(recordKnowledgeRelation.getFromConceptValue());
            ModelSettingNodeDTO toNode = new ModelSettingNodeDTO();
            toNode.setId(toNodeId);
            toNode.setLabel(recordKnowledgeRelation.getToConceptValue());
            ModelSettingEdgDTO edgDTO = new ModelSettingEdgDTO();
            edgDTO.setSource(fromNodeId);
            edgDTO.setTarget(toNodeId);
            edgDTO.setLabel(recordKnowledgeRelation.getRelationName());
            nodes.add(fromNode);
            nodes.add(toNode);
            edges.add(edgDTO);
        }
        //添加当前文档节点
        ModelSettingNodeDTO recordNode = new ModelSettingNodeDTO();
        recordNode.setId(recordId);
        recordNode.setLabel(record.getTitle());
        for (ModelSettingNodeDTO node : nodes) {
            ModelSettingEdgDTO edgDTO = new ModelSettingEdgDTO();
            edgDTO.setSource(recordId);
            edgDTO.setTarget(node.getId());
            edgDTO.setLabel("包含");
            edges.add(edgDTO);
        }
        nodes.add(recordNode);
        //添加当前文档与主体的关系
        modelSettingDTO.setNodes(new ArrayList<>(nodes));
        modelSettingDTO.setEdges(edges);
        return modelSettingDTO;
    }

    @Override
    public Long addConceptValue(RecordKnowledgeAddConceptValueReq recordKnowledgeQueryResp) {
        Long recordId = recordKnowledgeQueryResp.getRecordId();
        String conceptName = recordKnowledgeQueryResp.getConceptName();
        String conceptValue = recordKnowledgeQueryResp.getConceptValue();
        log.info("新增实例值,recordId:{},conceptName:{},conceptValue:{}", recordId, conceptName, conceptValue);
        if (StringUtils.isEmpty(conceptName) || StringUtils.isEmpty(conceptValue)) {
            throw new RuntimeException("概念名称和实例值不能为空");
        }
        recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>().eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .eq(RecordKnowledgeRelation::getFromConceptName, conceptName)
                        .eq(RecordKnowledgeRelation::getFromConceptValue, conceptValue)).forEach(relation -> {
            throw new RuntimeException("实例值已存在");
        });
        RecordKnowledgeRelation recordKnowledgeRelation = new RecordKnowledgeRelation();
        recordKnowledgeRelation.setId(wfgIdGenerator.next());
        recordKnowledgeRelation.setRecordId(recordId);
        recordKnowledgeRelation.setFromConceptName(conceptName);
        recordKnowledgeRelation.setFromConceptValue(conceptValue);
        long fromNodeId = wfgIdGenerator.next();
        recordKnowledgeRelation.setFromNodeId(fromNodeId);
        recordKnowledgeRelation.setCreateTime(new Date());
        recordKnowledgeRelation.setUpdateTime(new Date());
        recordKnowledgeRelation.setUpdateBy(SecurityUtils.getUserId());
        recordKnowledgeRelationService.save(recordKnowledgeRelation);
        LabelValueAddReq labelValueAddReq = new LabelValueAddReq();
        labelValueAddReq.setRecordId(recordId);
        labelValueAddReq.setLabel(conceptName);
        labelValueAddReq.setValue(conceptValue);
        labelValueAddReq.setNewNodeId(fromNodeId);
        recordGraphRpcEntrance.addLabelValue(labelValueAddReq);
        return fromNodeId;
    }


    @Override
    public void deleteConceptValue(RecordKnowledgeDeleteConceptValueReq deleteConceptValueReq) {
        Long recordId = deleteConceptValueReq.getRecordId();
        Long nodeId = deleteConceptValueReq.getNodeId();
        log.info("删除实例值,recordId:{},nodeId:{}", recordId, nodeId);
        recordKnowledgeRelationService.remove(new LambdaQueryWrapper<RecordKnowledgeRelation>()
                .eq(RecordKnowledgeRelation::getRecordId, recordId)
                .eq(RecordKnowledgeRelation::getFromNodeId, nodeId));
        recordKnowledgeAttributeService.remove(new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                .eq(RecordKnowledgeAttribute::getNodeId, nodeId));
        //同步到图数据库
        LabelValueAddReq labelValueAddReq = new LabelValueAddReq();
        labelValueAddReq.setNodeId(nodeId);
        labelValueAddReq.setRecordId(recordId);
        recordGraphRpcEntrance.deleteLabelValue(labelValueAddReq);
    }

    @Override
    public Long editConceptValue(RecordKnowledgeEditConceptValueReq editConceptValueReq) {
        Long recordId = editConceptValueReq.getRecordId();
        Long nodeId = editConceptValueReq.getNodeId();
        String conceptValue = editConceptValueReq.getConceptValue();
        log.info("编辑实例值,recordId:{},nodeId:{},conceptValue:{}", recordId, nodeId, conceptValue);
        List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<>(RecordKnowledgeRelation.class)
                        .eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .eq(RecordKnowledgeRelation::getFromNodeId, nodeId));
        if (CollectionUtils.isEmpty(relationList)) {
            throw new RuntimeException("实例节点不存在");
        }
        RecordKnowledgeRelation relation = relationList.get(0);
        recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>().eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .eq(RecordKnowledgeRelation::getFromConceptName, relation.getFromConceptName())
                        .eq(RecordKnowledgeRelation::getFromConceptValue, conceptValue)
                        .ne(RecordKnowledgeRelation::getFromNodeId, nodeId)).forEach(r -> {
            throw new RuntimeException("实例值已存在");
        });
        String userId = SecurityUtils.getUserId();
        Date now = new Date();
        //编辑节点需要新建节点
        long newNodeId = wfgIdGenerator.next();
        String fromConceptName = relationList.get(0).getFromConceptName();
        for (RecordKnowledgeRelation recordKnowledgeRelation : relationList) {
            recordKnowledgeRelation.setFromConceptValue(conceptValue);
            recordKnowledgeRelation.setUpdateTime(now);
            recordKnowledgeRelation.setUpdateBy(userId);
            recordKnowledgeRelation.setFromNodeId(newNodeId);
        }
        recordKnowledgeRelationService.saveOrUpdateBatch(relationList);
        //同步到图数据库
        LabelValueAddReq labelValueAddReq = new LabelValueAddReq();
        labelValueAddReq.setNodeId(nodeId);
        labelValueAddReq.setRecordId(recordId);
        labelValueAddReq.setValue(conceptValue);
        labelValueAddReq.setLabel(fromConceptName);
        labelValueAddReq.setNewNodeId(newNodeId);
        recordGraphRpcEntrance.editLabelValue(labelValueAddReq);
        return newNodeId;
    }


    @Override
    public Long addAttribute(RecordKnowledgeAttributeReq attributeReq) {
        Long recordId = attributeReq.getRecordId();
        Long nodeId = attributeReq.getNodeId();
        Integer type = attributeReq.getType();
        String attributeName = attributeReq.getAttributeName();
        String attributeValue = attributeReq.getAttributeValue();
        if (StringUtils.isEmpty(attributeValue)) {
            throw new RuntimeException("属性值不能为空");
        }
        if (type == null) {
            throw new RuntimeException("属性类型不能为空");
        }
        log.info("新增属性,recordId:{},nodeId:{},attributeName:{},attributeValue:{}", recordId, nodeId, attributeName,
                attributeValue);
        RecordKnowledgeAttribute existAttribute = recordKnowledgeAttributeService.getOne(
                new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                        .eq(RecordKnowledgeAttribute::getNodeId, nodeId));
        //编辑节点需要新建节点
        long newNodeId = wfgIdGenerator.next();
        if (existAttribute == null) {
            existAttribute = new RecordKnowledgeAttribute();
            existAttribute.setId(wfgIdGenerator.next());
            existAttribute.setNodeId(newNodeId);
            existAttribute.setType(type);
            existAttribute.setCreateTime(new Date());
            existAttribute.setUpdateTime(new Date());
            JSONObject attributeJson = new JSONObject();
            attributeJson.put(attributeName, attributeValue);
            existAttribute.setAttribute(attributeJson.toJSONString());
        } else {
            existAttribute.setUpdateTime(new Date());
            String attributeStr = existAttribute.getAttribute();
            JSONObject jsonObject = JSON.parseObject(attributeStr);
            if (jsonObject.containsKey(attributeName)) {
                throw new RuntimeException("属性不能重复");
            }
            Map<String, Object> attributeMap = new LinkedHashMap<>();
            attributeMap.put(attributeName, attributeValue);
            attributeMap.putAll(jsonObject);
            existAttribute.setNodeId(newNodeId);
            existAttribute.setAttribute(JSON.toJSONString(attributeMap));
        }
        recordKnowledgeAttributeService.saveOrUpdate(existAttribute);
        //更新到图数据库
        String userId = SecurityUtils.getUserId();
        Date now = new Date();
        if (type == 2) {
            //修改实例属性
            List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                    new LambdaQueryWrapper<>(RecordKnowledgeRelation.class)
                            .eq(RecordKnowledgeRelation::getRecordId, recordId)
                            .eq(RecordKnowledgeRelation::getFromNodeId, nodeId));
            if (CollectionUtils.isEmpty(relationList)) {
                throw new RuntimeException("实例节点不存在");
            }
            for (RecordKnowledgeRelation recordKnowledgeRelation : relationList) {
                recordKnowledgeRelation.setFromNodeId(newNodeId);
                recordKnowledgeRelation.setUpdateBy(userId);
                recordKnowledgeRelation.setUpdateTime(now);
            }
            recordKnowledgeRelationService.saveOrUpdateBatch(relationList);
            //同步到图数据库
            LabelPropertyAddReq labelPropertyAddReq = new LabelPropertyAddReq();
            labelPropertyAddReq.setNewNodeId(newNodeId);
            labelPropertyAddReq.setRecordId(recordId);
            labelPropertyAddReq.setNodeId(nodeId);
            labelPropertyAddReq.setKey(attributeName);
            labelPropertyAddReq.setValue(attributeValue);
            recordGraphRpcEntrance.addLabelProperty(labelPropertyAddReq);
            return newNodeId;
        } else if (type == 4) {
            LabelRelationPropertyReq labelRelationPropertyReq = new LabelRelationPropertyReq();
            labelRelationPropertyReq.setRelationNodeId(nodeId);
            labelRelationPropertyReq.setKey(attributeName);
            labelRelationPropertyReq.setValue(attributeValue);
            recordGraphRpcEntrance.addLabelRelationProperty(labelRelationPropertyReq);
        }
        return null;
    }

    @Override
    public Long deleteAttribute(RecordKnowledgeAttributeReq attributeReq) {
        Long recordId = attributeReq.getRecordId();
        Long nodeId = attributeReq.getNodeId();
        String attributeName = attributeReq.getAttributeName();
        log.info("删除属性,recordId:{},nodeId:{},attributeName:{}", recordId, nodeId, attributeName);
        RecordKnowledgeAttribute existAttribute = recordKnowledgeAttributeService.getOne(
                new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                        .eq(RecordKnowledgeAttribute::getNodeId, nodeId));
        if (existAttribute == null || StringUtils.isEmpty(existAttribute.getAttribute())) {
            throw new RuntimeException("属性不存在");
        }
        //编辑节点需要新建节点
        long newNodeId = wfgIdGenerator.next();
        JSONObject attributeJson = JSON.parseObject(existAttribute.getAttribute());
        attributeJson.remove(attributeName);
        existAttribute.setAttribute(attributeJson.toJSONString());
        existAttribute.setNodeId(newNodeId);
        recordKnowledgeAttributeService.updateById(existAttribute);
        //更新到图数据库
        Integer type = existAttribute.getType();
        String userId = SecurityUtils.getUserId();
        Date now = new Date();
        if (type == 2) {
            //修改实例属性
            List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                    new LambdaQueryWrapper<>(RecordKnowledgeRelation.class)
                            .eq(RecordKnowledgeRelation::getRecordId, recordId)
                            .eq(RecordKnowledgeRelation::getFromNodeId, nodeId));
            if (CollectionUtils.isEmpty(relationList)) {
                throw new RuntimeException("实例节点不存在");
            }
            for (RecordKnowledgeRelation recordKnowledgeRelation : relationList) {
                recordKnowledgeRelation.setFromNodeId(newNodeId);
                recordKnowledgeRelation.setUpdateBy(userId);
                recordKnowledgeRelation.setUpdateTime(now);
            }
            recordKnowledgeRelationService.saveOrUpdateBatch(relationList);
            LabelPropertyAddReq labelPropertyAddReq = new LabelPropertyAddReq();
            labelPropertyAddReq.setNewNodeId(newNodeId);
            labelPropertyAddReq.setRecordId(recordId);
            labelPropertyAddReq.setNodeId(nodeId);
            labelPropertyAddReq.setKey(attributeName);
            recordGraphRpcEntrance.deleteLabelProperty(labelPropertyAddReq);
            return newNodeId;
        } else if (type == 4) {
            LabelRelationPropertyReq labelRelationPropertyReq = new LabelRelationPropertyReq();
            labelRelationPropertyReq.setRelationNodeId(nodeId);
            labelRelationPropertyReq.setKey(attributeName);
            recordGraphRpcEntrance.deleteLabelRelationProperty(labelRelationPropertyReq);
        }
        return null;
    }

    @Override
    public Long editAttribute(RecordKnowledgeAttributeReq attributeReq) {
        Long recordId = attributeReq.getRecordId();
        Long nodeId = attributeReq.getNodeId();
        String attributeName = attributeReq.getAttributeName();
        String attributeValue = attributeReq.getAttributeValue();
        log.info("编辑属性,recordId:{},nodeId:{},attributeName:{},attributeValue:{}", recordId, nodeId, attributeName,
                attributeValue);
        RecordKnowledgeAttribute existAttribute = recordKnowledgeAttributeService.getOne(
                new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                        .eq(RecordKnowledgeAttribute::getNodeId, nodeId));
        if (existAttribute == null) {
            throw new RuntimeException("属性不存在");
        }
        //编辑节点需要新建节点
        long newNodeId = wfgIdGenerator.next();
        existAttribute.setUpdateTime(new Date());
        JSONObject attributeJson = JSON.parseObject(existAttribute.getAttribute());
        attributeJson.put(attributeName, attributeValue);
        existAttribute.setAttribute(attributeJson.toJSONString());
        existAttribute.setNodeId(newNodeId);
        recordKnowledgeAttributeService.saveOrUpdate(existAttribute);
        //更新到图数据库
        Integer type = existAttribute.getType();
        String userId = SecurityUtils.getUserId();
        Date now = new Date();
        if (type == 2) {
            //修改实例属性
            List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                    new LambdaQueryWrapper<>(RecordKnowledgeRelation.class)
                            .eq(RecordKnowledgeRelation::getRecordId, recordId)
                            .eq(RecordKnowledgeRelation::getFromNodeId, nodeId));
            if (CollectionUtils.isEmpty(relationList)) {
                throw new RuntimeException("实例节点不存在");
            }
            for (RecordKnowledgeRelation recordKnowledgeRelation : relationList) {
                recordKnowledgeRelation.setFromNodeId(newNodeId);
                recordKnowledgeRelation.setUpdateBy(userId);
                recordKnowledgeRelation.setUpdateTime(now);
            }
            recordKnowledgeRelationService.saveOrUpdateBatch(relationList);
            LabelPropertyAddReq labelPropertyAddReq = new LabelPropertyAddReq();
            labelPropertyAddReq.setNewNodeId(newNodeId);
            labelPropertyAddReq.setRecordId(recordId);
            labelPropertyAddReq.setNodeId(nodeId);
            labelPropertyAddReq.setKey(attributeName);
            labelPropertyAddReq.setValue(attributeValue);
            recordGraphRpcEntrance.addLabelProperty(labelPropertyAddReq);
            return newNodeId;
        } else if (type == 4) {
            LabelRelationPropertyReq labelRelationPropertyReq = new LabelRelationPropertyReq();
            labelRelationPropertyReq.setRelationNodeId(nodeId);
            labelRelationPropertyReq.setKey(attributeName);
            labelRelationPropertyReq.setValue(attributeValue);
            recordGraphRpcEntrance.addLabelRelationProperty(labelRelationPropertyReq);
        }
        return null;
    }

    @Override
    public JSONObject addRelation(RecordKnowledgeAddRelationReq addRelationReq) {
        Long recordId = addRelationReq.getRecordId();
        Long fromNodeId = addRelationReq.getFromNodeId();
        String toConceptName = addRelationReq.getToConceptName();
        String toConceptValue = addRelationReq.getToConceptValue();
        String relationName = addRelationReq.getRelationName();
        log.info("新增关系,recordId:{},fromNodeId:{},toConceptName:{},toConceptValue:{},relationName:{}",
                recordId, fromNodeId, toConceptName, toConceptValue, relationName);
        List<RecordKnowledgeRelation> relationList = recordKnowledgeRelationService.list(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRecordId, recordId)
                        .eq(RecordKnowledgeRelation::getFromNodeId, fromNodeId));
        if (CollectionUtils.isEmpty(relationList)) {
            throw new RuntimeException("实例不存在");
        }
        RecordKnowledgeRelation fromNode = relationList.get(0);
        RecordKnowledgeRelation recordKnowledgeRelation = new RecordKnowledgeRelation();
        recordKnowledgeRelation.setId(wfgIdGenerator.next());
        recordKnowledgeRelation.setRecordId(recordId);
        recordKnowledgeRelation.setFromNodeId(fromNodeId);
        recordKnowledgeRelation.setFromConceptName(fromNode.getFromConceptName());
        recordKnowledgeRelation.setFromConceptValue(fromNode.getFromConceptValue());
        recordKnowledgeRelation.setToConceptName(toConceptName);
        recordKnowledgeRelation.setToConceptValue(toConceptValue);
        long toNodeId = wfgIdGenerator.next();
        recordKnowledgeRelation.setToNodeId(toNodeId);
        recordKnowledgeRelation.setRelationName(relationName);
        long relationNodeId = wfgIdGenerator.next();
        recordKnowledgeRelation.setRelationNodeId(relationNodeId);
        recordKnowledgeRelation.setCreateTime(new Date());
        recordKnowledgeRelation.setUpdateTime(new Date());
        recordKnowledgeRelationService.save(recordKnowledgeRelation);
        //更新到图数据库
        LabelRelationAddReq labelRelationAddReq = new LabelRelationAddReq();
        labelRelationAddReq.setRecordId(recordId);
        labelRelationAddReq.setNodeId(fromNodeId);
        labelRelationAddReq.setRelId(relationNodeId);
        labelRelationAddReq.setRelName(relationName);
        labelRelationAddReq.setNewObjectId(toNodeId);
        labelRelationAddReq.setObjectLabelName(toConceptName);
        labelRelationAddReq.setObjectValue(toConceptValue);
        recordGraphRpcEntrance.addLabelRelation(labelRelationAddReq);
        JSONObject resp = new JSONObject();
        resp.put("toNodeId", toNodeId);
        resp.put("relationNodeId", relationNodeId);
        return resp;
    }

    @Override
    public void deleteRelation(RecordKnowledgeDeleteRelationReq deleteRelationReq) {
        Long recordId = deleteRelationReq.getRecordId();
        Long relationNodeId = deleteRelationReq.getRelationNodeId();
        log.info("删除关系,recordId:{},relationNodeId:{}", recordId, relationNodeId);
        recordKnowledgeRelationService.remove(new LambdaQueryWrapper<RecordKnowledgeRelation>()
                .eq(RecordKnowledgeRelation::getRecordId, recordId)
                .eq(RecordKnowledgeRelation::getRelationNodeId, relationNodeId));
        recordKnowledgeAttributeService.remove(new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                .eq(RecordKnowledgeAttribute::getNodeId, relationNodeId));
        LabelRelationAddReq labelRelationAddReq = new LabelRelationAddReq();
        labelRelationAddReq.setRelId(relationNodeId);
        recordGraphRpcEntrance.deleteLabelRelation(labelRelationAddReq);
    }

    @Override
    public JSONObject editRelation(RecordKnowledgeEditRelationReq editRelationReq) {
        Long relationNodeId = editRelationReq.getRelationNodeId();
        Long recordId = editRelationReq.getRecordId();
        String toConceptName = editRelationReq.getToConceptName();
        String toConceptValue = editRelationReq.getToConceptValue();
        String relationName = editRelationReq.getRelationName();
        log.info("编辑关系,recordId:{},relationNodeId:{},toConceptName:{},toConceptValue:{},relationName:{}",
                recordId, relationNodeId, toConceptName, toConceptValue, relationName);
        RecordKnowledgeRelation relation = recordKnowledgeRelationService.getOne(
                new LambdaQueryWrapper<RecordKnowledgeRelation>()
                        .eq(RecordKnowledgeRelation::getRelationNodeId, relationNodeId));
        if (relation == null) {
            throw new RuntimeException("关系不存在");
        }
        String oldToConceptName = relation.getToConceptName();
        String oldToConceptValue = relation.getToConceptValue();
        String oldRelName = relation.getRelationName();
        JSONObject resp = new JSONObject();
        if (Objects.equals(oldToConceptName, toConceptName) && Objects.equals(oldToConceptValue, toConceptValue)
                && Objects.equals(oldRelName, relationName)) {
            //没有修改关系
            resp.put("toNodeId", relation.getToNodeId());
            resp.put("relationNodeId", relationNodeId);
            return resp;
        }
        relation.setToConceptName(toConceptName);
        relation.setToConceptValue(toConceptValue);
        long toNodeId = wfgIdGenerator.next();
        relation.setToNodeId(toNodeId);
        relation.setRelationName(relationName);
        relation.setUpdateTime(new Date());
        relation.setUpdateBy(SecurityUtils.getUserId());
        recordKnowledgeRelationService.updateById(relation);
        //修改了关系 删除关系下的实例
        recordKnowledgeAttributeService.remove(new LambdaQueryWrapper<RecordKnowledgeAttribute>()
                .eq(RecordKnowledgeAttribute::getNodeId, relationNodeId));
        //更新到图数据库
        LabelRelationAddReq labelRelationAddReq = new LabelRelationAddReq();
        labelRelationAddReq.setRecordId(recordId);
        labelRelationAddReq.setNodeId(relation.getFromNodeId());
        labelRelationAddReq.setRelId(relationNodeId);
        labelRelationAddReq.setRelName(relationName);
        labelRelationAddReq.setNewObjectId(toNodeId);
        labelRelationAddReq.setObjectLabelName(toConceptName);
        labelRelationAddReq.setObjectValue(toConceptValue);
        recordGraphRpcEntrance.editLabelRelation(labelRelationAddReq);
        resp.put("toNodeId", toNodeId);
        resp.put("relationNodeId", relationNodeId);
        return resp;
    }

}
