package com.suwell.plss.record.rpcimpl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.record.entity.Material;
import com.suwell.plss.record.entity.MaterialCategory;
import com.suwell.plss.record.mapper.MaterialCategoryMapper;
import com.suwell.plss.record.mapper.MaterialMapper;
import com.suwell.plss.record.service.MaterialCategoryService;
import com.suwell.plss.record.service.MaterialRpcService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;

import com.suwell.plss.record.service.MaterialService;
import com.suwell.plss.record.standard.dto.request.MaterialPageReq;
import com.suwell.plss.record.standard.dto.request.MaterialQueryReq;
import com.suwell.plss.record.standard.dto.response.MaterialCategoryInfoResp;
import com.suwell.plss.record.standard.dto.response.MaterialResp;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.service.CategoryRpcService;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(MaterialRpcService.INNER_PREFIX)
public class MaterialRpcServiceImpl implements MaterialRpcService {

    @Resource
    private MaterialCategoryService materialCategoryService;

    @Resource
    private MaterialService materialService;

    @Resource
    private CategoryRpcService categoryRpcService;
    @Override
    public R<Boolean> deleteMaterialCategory(List<Long> categoryIds) {
        materialCategoryService.remove(Wrappers.<MaterialCategory>lambdaQuery().in(MaterialCategory::getCategoryId,categoryIds));
        return R.ok();
    }


    @Override
    public R<Boolean> updateMaterialCategory(Map<String,Object> categoryMap){
        LambdaUpdateWrapper<MaterialCategory> materialCategoryLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        materialCategoryLambdaUpdateWrapper.eq(MaterialCategory::getCategoryId,categoryMap.get("id"));
        materialCategoryLambdaUpdateWrapper.set(MaterialCategory::getCategoryName,categoryMap.get("name"));
        materialCategoryService.update(materialCategoryLambdaUpdateWrapper);
        return R.ok();
    }

    @Override
    public R<PageUtils<SysCategory>> pageQueryMaterialCategory(MaterialQueryReq materialQueryReq){
        IPage<Long> categoryPage = ((MaterialCategoryMapper) materialCategoryService.getBaseMapper())
                .selectCategoryId(new Page<>(materialQueryReq.getPage(), materialQueryReq.getPageSize()), materialQueryReq.getRepId());
        if(CollUtil.isNotEmpty(categoryPage.getRecords())){
            List<Long> categoryIdList = categoryPage.getRecords();
            R<List<SysCategory>> categoryListR = categoryRpcService.getByIdList(categoryIdList);
            if(categoryListR.isSuccess() && CollUtil.isNotEmpty(categoryListR.getData())){
                return R.ok(new PageUtils<SysCategory>(categoryListR.getData(),categoryPage.getTotal(),categoryPage.getSize(),categoryPage.getCurrent()));
            }
        }

        return R.ok(new PageUtils<SysCategory>(null,0,materialQueryReq.getPageSize(),materialQueryReq.getPage()));
    }

    @Override
    public R<PageUtils<MaterialResp>> pageQueryMaterial(@RequestBody MaterialPageReq materialPageReq){
        PageUtils<Material> materialPageUtils = materialService.selectMaterialPageByCategoryId(materialPageReq);
        List<MaterialResp> materialRespList = materialPageUtils.getList().stream().map(o -> {
            MaterialResp materialResp = new MaterialResp();
            BeanUtils.copyProperties(o, materialResp);
            return materialResp;
        }).collect(Collectors.toList());
        return R.ok(new PageUtils<>(materialRespList, materialPageUtils.getTotalCount(), materialPageUtils.getPageSize(), materialPageUtils.getPage()));
    }

}
