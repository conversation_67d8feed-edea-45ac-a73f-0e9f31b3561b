package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.PermissionGroupAddReq;
import com.suwell.plss.record.standard.dto.request.PermissionGroupModifyReq;
import com.suwell.plss.record.standard.dto.request.PermissionOperateReq;
import com.suwell.plss.record.standard.dto.request.PermissionQueryReq;
import com.suwell.plss.record.standard.dto.request.PermissionRecordsQueryReq;
import com.suwell.plss.record.standard.dto.request.ResourceManagerAddReq;
import com.suwell.plss.record.standard.dto.request.ResourceManagerQueryReq;
import com.suwell.plss.record.standard.dto.request.ResourceNatureChangeReq;
import com.suwell.plss.record.standard.dto.request.ResourcePermissionAddReq;
import com.suwell.plss.record.standard.dto.request.ResourcePermissionRemoveReq;
import com.suwell.plss.record.standard.dto.request.UserPermConditionReq;
import com.suwell.plss.record.standard.dto.response.PermissionBatchOperateResp;
import com.suwell.plss.record.standard.dto.response.PermissionDicResp;
import com.suwell.plss.record.standard.dto.response.PermissionGroupResp;
import com.suwell.plss.record.standard.dto.response.PermissionOperateResp;
import com.suwell.plss.record.standard.dto.response.PermissionRecordResp;
import com.suwell.plss.record.standard.dto.response.PermissionResp;
import com.suwell.plss.record.standard.dto.response.ResourceManagerResp;
import com.suwell.plss.record.standard.dto.response.ResourcePermissionResp;
import com.suwell.plss.record.standard.dto.response.UserPermissionResp;
import com.suwell.plss.record.standard.service.StandardPermissionFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 权限
 * 1：权限字典查询、修改
 * 2：权限组的查询、新增、删除
 * 3：资源权限的查询、新增、删除
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/permission")
public class PermissionController {

    @Resource
    private StandardPermissionFacade standardPermissionFacade;

    /**
     * 查询权限字典列表
     */
    @PostMapping("/permissionDicList")
    public R<List<PermissionDicResp>> permissionDicList() {
        List<PermissionDicResp> list = standardPermissionFacade.listPermissionDic();
        return R.ok(list);
    }

    /**
     * 保存用户的权限组
     */
    @PostMapping("/addPermissionGroup")
    public R<Void> addPermissionGroup(@RequestBody List<PermissionGroupAddReq> list) {
        standardPermissionFacade.addPermissionGroup(list);
        return R.ok();
    }

    /**
     * 修改权限组
     */
    @PostMapping("/modifyPermissionGroup")
    public R<Void> modifyPermissionGroup(@RequestBody PermissionGroupModifyReq req) {
        standardPermissionFacade.modifyPermissionGroup(req);
        return R.ok();
    }

    /**
     * 删除权限组
     */
    @PostMapping("/removePermissionGroup")
    public R<Void> removePermissionGroup(@RequestBody List<Long> groupIdList) {
        standardPermissionFacade.removePermissionGroup(groupIdList);
        return R.ok();
    }

    /**
     * 获取权限组列表
     */
    @PostMapping("/listPermissionGroup")
    public R<List<PermissionGroupResp>> listPermissionGroup() {

        List<PermissionGroupResp> list = standardPermissionFacade.listPermissionGroup();
        return R.ok(list);
    }

    /**
     * 新增资源权限
     */
    @Log(title = "'后台-文库管理'", businessType = BusinessType.UPDATE)
    @PostMapping("/addResourcePermission")
    public R<Void> addResourcePermission(@RequestBody ResourcePermissionAddReq addReq) {
        standardPermissionFacade.addResourcePermission(addReq);
        return R.ok();
    }

    /**
     * 新增资源管理员
     */
    @Log(title = "'后台-文库管理'", businessType = BusinessType.UPDATE)
    @PostMapping("/addResourceManagers")
    public R<Void> addResourceManagers(@RequestBody ResourceManagerAddReq addReq) {
        standardPermissionFacade.addResourceManagers(addReq);
        return R.ok();
    }

    /**
     * 获取资源权限列表
     */
    @Deprecated
    @PostMapping("/listResourcePermission")
    public R<List<PermissionResp>> listResourcePermission(@RequestBody PermissionQueryReq req) {

        List<PermissionResp> list = standardPermissionFacade.listResourcePermission(req);
        return R.ok(list);
    }

    /**
     * 获取资源权限列表
     */
    @PostMapping("/getResourcePermission")
    public R<ResourcePermissionResp> getResourcePermission(@RequestBody PermissionQueryReq req) {

        ResourcePermissionResp resp = standardPermissionFacade.getResourcePermission(req);
        return R.ok(resp);
    }


    /**
     * 获取资源管理员列表
     */
    @PostMapping("/listResourceManagers")
    public R<List<ResourceManagerResp>> listResourceManagers(@RequestBody ResourceManagerQueryReq req) {

        List<ResourceManagerResp> list = standardPermissionFacade.listResourceManagers(req);
        return R.ok(list);
    }

    /**
     * 资源继承性质转换，或继承、或阻断
     */
    @PostMapping("/resourceNatureChange")
    public R<Void> resourceNatureChange(@RequestBody ResourceNatureChangeReq req) {
        standardPermissionFacade.resourceNatureChange(req);
        return R.ok();
    }

    /**
     * 获取资源操作权限列表
     */
    @PostMapping("/listResourceOperate")
    public R<List<PermissionOperateResp>> listResourceOperate(@RequestBody PermissionOperateReq req) {

        List<PermissionOperateResp> list = standardPermissionFacade.listResourceOperate(req);
        return R.ok(list);
    }

    /**
     * 批量获取文件操作权限列表
     */
    @PostMapping("/batchListResourceOperate")
    R<List<PermissionBatchOperateResp>> batchListResourceOperate(@RequestBody List<PermissionOperateReq> req) {
        List<PermissionBatchOperateResp> list = standardPermissionFacade.batchListResourceOperate(req);
        return R.ok(list);
    }

    /**
     * 获取用户的可见资源
     */
    @PostMapping("/listUserResources")
    public R<UserPermissionResp> listUserResources() {

        UserPermissionResp resp = standardPermissionFacade.listUserResources();
        return R.ok(resp);
    }

    /**
     * 有条件的查询用户权限
     */
    @PostMapping("/listUserResourcesWithCondition")
    public R<UserPermissionResp> listUserResourcesWithCondition(@RequestBody UserPermConditionReq req) {
        UserPermissionResp resp = standardPermissionFacade.listUserResourcesWithCondition(req);
        return R.ok(resp);
    }


    /**
     * 资源授权记录
     */
    @PostMapping("/listPermRecord")
    public R<PermissionRecordResp> listPermRecord(@RequestBody PermissionRecordsQueryReq req) {
        PermissionRecordResp list = standardPermissionFacade.listPermRecord(req);
        return R.ok(list);
    }

}
