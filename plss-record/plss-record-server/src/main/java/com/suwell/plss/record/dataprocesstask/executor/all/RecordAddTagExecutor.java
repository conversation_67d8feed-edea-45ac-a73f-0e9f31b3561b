package com.suwell.plss.record.dataprocesstask.executor.all;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.utils.CollectionUtil;
import com.suwell.plss.framework.common.utils.SleepUtils;
import com.suwell.plss.record.conf.DataProcessTaskConfig;
import com.suwell.plss.record.conf.DataProcessTaskConfig.DataProcessTaskProp;
import com.suwell.plss.record.dataprocesstask.DataProcessTaskExecutor;
import com.suwell.plss.record.entity.DataProcessTask;
import com.suwell.plss.record.enums.DataProcessTaskEnum.BusinessType;
import com.suwell.plss.record.service.DataProcessTaskService;
import com.suwell.plss.record.standard.dto.request.FolderAddTagReq;
import com.suwell.plss.record.standard.dto.request.RecordMakeBachTagReq;
import com.suwell.plss.record.standard.dto.response.RecordMakeBachTagResp;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/1 14:55
 */
@Service
@Slf4j
public class RecordAddTagExecutor implements DataProcessTaskExecutor {

    @Resource
    private DataProcessTaskService dataProcessTaskService;

    @Resource
    private StandardRecordFacade standardRecordFacade;

    @Resource
    private DataProcessTaskConfig dataProcessTaskConfig;

    @Override
    public BusinessType getBusinessType() {
        return BusinessType.RECORD_ADD_TAG;
    }

    @Override
    public void before(Long taskId) {

    }

    @Override
    public void execute(Long taskId) {
        String logPrefix = getBusinessType().getName();

        log.info("{}任务-任务开始,taskId={}",logPrefix, taskId);

        DataProcessTaskProp config = dataProcessTaskConfig.getConfig();
        Integer batchSleepSecond = config.getBatchSleepSecond();
        Integer batchSize = config.getAllBatchSize();

        DataProcessTask task = dataProcessTaskService.getById(taskId);

        FolderAddTagReq req = JSON.parseObject(task.getBusinessData(), FolderAddTagReq.class);
        List<List<Long>> lists = CollectionUtil.splitBySize(req.getRecordIdList(), batchSize);

        for (int i = task.getOffsetPosition().intValue(), len = lists.size(); i < len; i++) {
            SleepUtils.sleepSecond(batchSleepSecond);
            task = dataProcessTaskService.getById(taskId);

            List<Long> subList = lists.get(i);
            log.info("{}任务-批次开始,taskId={},subList={}", logPrefix,taskId, JSON.toJSONString(subList));
            try{
                RecordMakeBachTagReq makeBachTagReq = new RecordMakeBachTagReq();
                makeBachTagReq.setRecordIdList(subList);
                makeBachTagReq.setLastTagIdList(req.getTagIdList());
                RecordMakeBachTagResp resp = standardRecordFacade.modifyRecordBatchMakeTag(makeBachTagReq);

                Long offsetNew = task.getOffsetPosition() + 1;
                if (i == len - 1) {
                    dataProcessTaskService.finishTask(taskId, task.getTotalCount(), offsetNew);
                    log.info("{}任务-任务完成,taskId={},executedCount={},offsetNew={}",logPrefix, taskId,
                            task.getTotalCount(), offsetNew);
                } else {
                    dataProcessTaskService.finishBatch(taskId, task.getExecutedCount() + subList.size(), offsetNew);
                    log.info("{}任务-批次完成,taskId={},executedCount={},offsetNew={}",logPrefix, taskId,
                            task.getExecutedCount() + subList.size(), offsetNew);
                }
            }catch (Exception e){
                dataProcessTaskService.breakTask(taskId);
                log.error("{}任务-任务中断,taskId={},em={}",logPrefix, taskId, e.getMessage(), e);
            }
        }
    }
}
