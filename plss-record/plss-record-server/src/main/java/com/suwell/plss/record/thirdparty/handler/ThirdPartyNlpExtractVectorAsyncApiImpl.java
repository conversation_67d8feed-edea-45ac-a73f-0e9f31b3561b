package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_VECTOR;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordAiVectorProcessEvent;
import com.suwell.plss.record.service.RecordProcessDetailService;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractVectorApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyNlpExtractVectorAsyncApiImpl implements ThirdPartyNlpExtractVectorApi {

    @Resource
    private ApplicationContext context;
    @Resource
    private RecordProcessDetailService recordProcessDetailService;

    @DSTransactional
    @Override
    public void getVector(MqRecordReqDTO mqRecordReqDTO) {
        log.info("async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordAiVectorProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_VECTOR));
//        recordProcessDetailService.saveBatchRecordProcessDetail(mqRecordReqDTO.getRecordId(),
//                FileUtil.getName(mqRecordReqDTO.getRelativePath()), Lists.newArrayList(GET_VECTOR));
//        //监听记录消息事务提交后回调发送消息
//        TransactionSynchronizationManagerUtils.executeAfterCommit(() ->
//                context.publishEvent(new RecordAiVectorProcessEvent<>(mqRecordReqDTO, GET_VECTOR)));
    }
}
