package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.conf.NodeFeatureConfig;
import com.suwell.plss.record.conf.NodeFeatureProperties;
import com.suwell.plss.record.standard.dto.response.NodeResp;
import com.suwell.plss.record.standard.service.StandardNodeFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
@RestController
@RequestMapping("/v1/node")
public class NodeController {

    @Resource
    private StandardNodeFacade standardNodeFacade;
    @Resource
    private NodeFeatureConfig nodeFeatureConfig;

    /**
     * 列表
     */
    @PostMapping("/list")
    public R<List<NodeResp>> list() {
        return R.ok(standardNodeFacade.queryList());
    }


    /**
     * 节点下具体功能按钮细力度控制配置
     */
    @PostMapping("/featureButtonConfig")
    public R<List<NodeFeatureProperties>> listNodeFeatureProperties() {
        return R.ok(nodeFeatureConfig.getNodeFeatureConfig());
    }
}
