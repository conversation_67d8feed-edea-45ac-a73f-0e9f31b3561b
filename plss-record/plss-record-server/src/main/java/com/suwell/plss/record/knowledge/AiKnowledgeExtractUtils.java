package com.suwell.plss.record.knowledge;

import static com.suwell.plss.record.enums.StoreWayEnum.SW_BACKEND;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.RecordMetadataValueReq;
import com.suwell.plss.record.standard.dto.request.RecordQueryReq;
import com.suwell.plss.record.standard.dto.response.MetaDataValueInfoResp;
import com.suwell.plss.search.api.http.SupSearchRpcService;
import com.suwell.plss.search.standard.dto.request.SupRefrestMedataReq;
import feign.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/28
 * @content
 */
@Slf4j
@Component
public class AiKnowledgeExtractUtils {

    @Resource
    DocumentService documentService;
    @Resource
    SupSearchRpcService supSearchRpcService;
    @Resource
    private FileManageRpcService fileManageRpcService;

    @Resource
    private RecordService recordService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;

    public Map<String, Set<String>> aiKnowledgeExtract(FileItem file) {
//        String url = "http://************:8086/openApi/lyricsProcess";
        int timeout = 45000;
        try {
            String url = serverBasePathConfig.getAiMiddleHostPort() + "/openApi/lyricsProcess";
            HttpRequest httpRequest = HttpRequest.post(url)
                    .form("file", new InputStreamResource(file.getInputStream()));
            HttpResponse httpResponse = httpRequest.timeout(timeout).execute();
            AiKnowledgeDTO aiKnowledgeDTO = JSON.parseObject(httpResponse.body(), AiKnowledgeDTO.class);
            if (httpResponse.isOk() && aiKnowledgeDTO.getSuccess()) {
                List<String> data = aiKnowledgeDTO.getData();
                List<AiKnowledgeDTO.KnowledgeDTO> list = data.stream()
                        .map(json -> JSONObject.parseObject(json, AiKnowledgeDTO.KnowledgeDTO.class))
                        .collect(Collectors.toList());

                List<Map<String, Set<String>>> mapList = new ArrayList<>();
                for (AiKnowledgeDTO.KnowledgeDTO knowledgeDTO : list) {
                    Map<String, Set<String>> resultMap = knowledgeDTO.getLabel().stream()
                            .flatMap(map -> map.entrySet().stream())
                            .collect(Collectors.groupingBy(Map.Entry::getKey,
                                    Collectors.mapping(Map.Entry::getValue, Collectors.toList())))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey,
                                    entry -> entry.getValue().stream().flatMap(List::stream)
                                            .collect(Collectors.toSet())));
                    mapList.add(resultMap);
                }
                Map<String, Set<String>> result = mapList.stream()
                        .flatMap(map -> map.entrySet().stream())
                        .collect(Collectors.groupingBy(Map.Entry::getKey,
                                Collectors.mapping(Map.Entry::getValue, Collectors.toList())))
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                entry -> entry.getValue().stream().flatMap(Set::stream).collect(Collectors.toSet())));
                log.info("ai知识提取结果，{}", JSON.toJSONString(result));
                return result;
            } else {
                log.warn("ai知识提取调用失败，响应结果-->{}", httpResponse);
                throw new RuntimeException("ai知识提取调用失败");
            }
        } catch (Exception e) {
            log.error("ai知识提取调用失败", e);
            if (e instanceof java.util.concurrent.TimeoutException) {
                // 请求超时处理逻辑
                throw new RuntimeException("ai知识提取调用超时，-->{}", e);
            } else {
                // 其他异常处理逻辑
                throw new RuntimeException("ai知识提取调用失败，-->{}", e);
            }
        }
    }


    public List<Long> FileTask(AiKnowledgeReq aiKnowledgeReq) {
        log.info("开始进行知识提取");
        List<Long> ids = new ArrayList<>();
        try {
            RecordQueryReq req = new RecordQueryReq();
            req.setRecordIdList(aiKnowledgeReq.getRecordIds());
            req.setRecordTypeIds(aiKnowledgeReq.getRecordTypeIds());
            req.setPutLibTimeSt(aiKnowledgeReq.getStartTime());
            req.setPutLibTimeEd(aiKnowledgeReq.getEndTime());
            req.setPageSize(1000);
            long pages;
            int i = 1;
            do {
                req.setPage(i);
                PageUtils<Record> page = recordService.pageFromES(req);
                pages = page.getPage();
                List<Record> records = page.getList();
                if (CollUtil.isNotEmpty(records)) {
                    for (Record dto : records) {
                        long totalStartTime = System.currentTimeMillis();
                        Long recordId = dto.getId();
                        long fileSize = 0;
                        List<MetaDataValueInfoResp> metaDataValueInfoResps = recordMetadataValueService.queryRecordTypeMetaDataValue(
                                recordId, dto.getRecordtypeId());

                        MetaDataValueInfoResp resp = metaDataValueInfoResps.stream()
                                .filter(O -> "知识提取".equals(O.getMdName())).findFirst().get();
                        if (StringUtils.isEmpty(resp.getMdValue())) {
                            try {
                                Document document = documentService.getMasterDocByRecordId(recordId);
                                if (document == null) {
                                    log.warn("文档id={}，没有找到对应的ofd文件", recordId);
                                    continue;
                                }
                                long aiKnowledgeExtractStartTime = System.currentTimeMillis();
                                FileItem file = getAiKnowledgeFile(document.getOfdFileId());
                                fileSize = file.getSize();
                                Map<String, Set<String>> result = aiKnowledgeExtract(file);
                                log.info("knowledgeExtract文档id={}，文档大小={}, ai知识提取时间，{}", recordId, fileSize,
                                        System.currentTimeMillis() - aiKnowledgeExtractStartTime);
                                long metadataStartTime = System.currentTimeMillis();
                                Map<String, String> metadata = new HashMap<>();
                                String jsonString = com.alibaba.fastjson.JSON.toJSONString(result);
                                metadata.put("知识提取", jsonString);
                                RecordMetadataValueReq recordMetadataValueReq = new RecordMetadataValueReq();
                                recordMetadataValueReq.setRecordId(document.getRecordId());
                                recordMetadataValueReq.setMetadata(metadata);
                                recordMetadataValueReq.setStoreWay(SW_BACKEND.getCode());
                                log.info("知识提取结束，开始回调写入元数据提取结果，回调参数：{}",
                                        com.alibaba.fastjson.JSON.toJSONString(recordMetadataValueReq));
                                recordMetadataValueService.updateRecordMetadata(recordMetadataValueReq);
                                SupRefrestMedataReq medataReq = new SupRefrestMedataReq();
                                medataReq.setRecordId(document.getRecordId());
                                medataReq.setMedataValue(jsonString);
                                medataReq.setMetadataId(resp.getMdId());
                                supSearchRpcService.updateMedataValueKnowledge(medataReq);
                                log.info("knowledgeExtract文档id={}，文档大小={}, 知识提取更新元数据和es数据时间，{}",
                                        recordId, fileSize, System.currentTimeMillis() - metadataStartTime);
                            } catch (Exception e) {
                                log.error("req={},知识提取异常!", e);
                            }
                        }
                        log.info("knowledgeExtract文档id={}，文档大小={},知识提取整个流程时间={}", recordId, fileSize,
                                System.currentTimeMillis() - totalStartTime);
                    }
                }
                i++;
            } while (i <= pages);

        } catch (Exception e) {
            log.error("知识提取发生异常", e);
        }
        log.info("结束进行知识提取");
        return ids;
    }

    private FileItem getAiKnowledgeFile(Long ofdFileId) {
        Response response = fileManageRpcService.downloadFile(ofdFileId);
        return FileUtils.getFileItem(response);
    }
}
