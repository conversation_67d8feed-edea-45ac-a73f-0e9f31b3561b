package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.PublishDiffDataReq;
import com.suwell.plss.record.standard.dto.request.UploadPartReq;
import com.suwell.plss.record.standard.service.StandardDataDeliveryFacade;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据分发
 */
@RestController
@RequestMapping("/v1/delivery")
public class DataDeliveryController {


    @Resource
    private StandardDataDeliveryFacade standardDataDeliveryFacade;

    /**
     * 分片上传文件，离线上传压缩包
     */
    @PostMapping("/uploadPart")
    R<Void> uploadPart(@ModelAttribute UploadPartReq req) {
        standardDataDeliveryFacade.uploadPart(req);
        return R.ok();
    }

    /**
     * 上传差异数据
     */
    @PostMapping("/publishDiffData")
    R<Void> publishDiffData(@RequestBody PublishDiffDataReq req) {
        standardDataDeliveryFacade.publishDiffData(req);
        return R.ok();
    }

}
