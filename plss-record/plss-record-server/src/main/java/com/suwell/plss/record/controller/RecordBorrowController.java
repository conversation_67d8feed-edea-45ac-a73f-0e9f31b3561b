package com.suwell.plss.record.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.RecordBorrowAddReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowEditReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowQueryReq;
import com.suwell.plss.record.standard.dto.request.RecordBorrowStatusReq;
import com.suwell.plss.record.standard.dto.response.RecordBorrowInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordBorrowStatusResp;
import com.suwell.plss.record.standard.enums.RecordBorrowEnum.BorrowStatusEnum;
import com.suwell.plss.record.standard.service.StandardRecordBorrowFacade;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/10/26
 */
@RestController
@RequestMapping("/v1/borrow")
public class RecordBorrowController {
    public static final String MODE_NAME = "'后台-借阅审批'";

    @Resource
    private StandardRecordBorrowFacade recordBorrowFacade;


    /**
     * 审批分页列表
     */
    @PostMapping("/page")
    public R<Page<RecordBorrowInfoResp>> queryPage(@RequestBody RecordBorrowQueryReq request) {
        Page<RecordBorrowInfoResp> page = recordBorrowFacade.queryPage(request);
        return R.ok(page);
    }

    /**
     * 借阅状态
     */
    @PostMapping("/status")
    public R<List<RecordBorrowStatusResp>> status(@RequestBody RecordBorrowStatusReq request) {
        List<RecordBorrowStatusResp> list = recordBorrowFacade.status(request);
        return R.ok(list);
    }

    /**
     * 添加借阅信息
     */
    @PostMapping("/add")
    public R add(@RequestBody RecordBorrowAddReq request) {
        recordBorrowFacade.add(request);
        return R.ok();
    }

    /**
     * 修改借阅信息
     */
    @PostMapping("/update")
    public R update(@RequestBody RecordBorrowAddReq request) {
        recordBorrowFacade.update(request);
        return R.ok();
    }

    /**
     * 借阅审批
     */
    @Log(title = MODE_NAME, businessType = BusinessType.CHECK)
    @PostMapping("/audit")
    public R audit(@RequestBody RecordBorrowEditReq request) {
        recordBorrowFacade.audit(request);
        return R.ok();
    }

    /**
     * 借阅状态下拉框
     */
    @PostMapping("/statusDropDownBox")
    public R<BorrowStatusEnum[]> statusDropDownBox() {
        return R.ok(recordBorrowFacade.statusDropDownBox());
    }

    /**
     * 文件再次借阅判断
     */
    @PostMapping("/borrowAgainToJudge")
    public R<Boolean> borrowAgainToJudge(@RequestBody RecordBorrowStatusReq request) {
        return R.ok(recordBorrowFacade.borrowAgainToJudge(request));
    }

    /**
     * 借阅收回
     *
     * @param ids
     * @return
     */
    @PostMapping("/withdraw")
    public R<Boolean> withdraw(@RequestBody List<Long> ids) {
        recordBorrowFacade.withdraw(ids);
        return R.ok();
    }

    /**
     * 审批详情
     *
     * @param id
     * @return
     */
    @PostMapping("/auditInfo")
    public R<RecordBorrowInfoResp> auditInfo(@RequestBody Long id) {
        return R.ok(recordBorrowFacade.auditInfo(id));
    }



    /**
     * 撤回借阅申请
     *
     * @param id
     * @return
     */
    @PostMapping("/revoke")
    public R<Void> revoke(@RequestBody Long id) {
        recordBorrowFacade.revoke(id);
        return R.ok();
    }


}
