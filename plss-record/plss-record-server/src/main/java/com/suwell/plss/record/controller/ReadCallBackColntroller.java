package com.suwell.plss.record.controller;

import static com.suwell.plss.record.standard.enums.RecordBizError.REQUEST_INVALID;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Charsets;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.TOTPUtil;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.record.domain.ReaderAttachmentDTO;
import com.suwell.plss.record.domain.ReaderInfoDTO;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.service.ReaderCallbackService;
import com.suwell.plss.record.standard.dto.request.ReaderAttachmentCallbackReq;
import com.suwell.plss.record.standard.dto.request.ReaderCallbackReq;
import com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.CallbackBusinessType;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 * @date : 2023/11/1
 */
@Slf4j
@RestController
@RequestMapping("/v1/readCallBack")
public class ReadCallBackColntroller {

    @Resource(name = "sampleCallback")
    private ReaderCallbackService samplateFileCallbackService;

    @Resource(name = "documentCallbackService")
    private ReaderCallbackService documentCallbackService;
    @Resource(name = "personalLibraryCallback")
    private ReaderCallbackService personalLibraryCallbackService;

    @Resource(name = "readerPreviewCallback")
    private ReaderCallbackService readerPreviewCallbackService;
    @Resource(name = "readerFileIdPreviewCallback")
    private ReaderCallbackService readerFileIdPreviewCallback;
    @Resource
    private DocumentService documentService;
    @Resource
    private RedisService redisService;
    @Resource
    private TOTPUtil totpUtil;

    @Resource
    private FileService fileService;

    @Resource
    private ServerBasePathConfig serverBasePathConfig;

    @GetMapping("/info")
    public ReaderInfoDTO getDesensitizeRule(HttpServletRequest request,
            @RequestParam(value = "fileId", required = false) Long fileId,
            @RequestParam(value = "docId", required = false) Long docId,
            @RequestParam(value = "businessType", required = false) Integer businessType,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(required = false) Long timestamp,
            @RequestParam(value = "recordId", required = false) Long recordId,
            @RequestParam(value = "suwellsign", required = false) String sign,
            @RequestParam(value = "forFront", required = false, defaultValue = "false") boolean forFront) {
        log.info("轻阅读回调入参,docId:{},businessType:{},userId:{},timestamp:{},recordId:{},sign:{},forFront:{}",
                docId,businessType,userId,timestamp,recordId,sign,forFront);
        ReaderCallbackReq req = new ReaderCallbackReq();
        //传了recordId 默认用recordId去反查
        req.setId(docId);
        if (Objects.nonNull(recordId)) {
            Document document = documentService.getMasterDocByRecordId(recordId);
            if (Objects.nonNull(document) && Objects.isNull(docId)) {
                req.setId(document.getId());
            }
        }
        if (Objects.nonNull(fileId)) {
            req.setId(fileId);
        }
        if (totpUtil.isCheckSign()) {
            if (StringUtils.isNotEmpty(sign) && userId != null && Convert.toLong(userId) > 0) {
                boolean checkSignResult = totpUtil.verifyTOTPFlexibility(req.getId().toString(), userId, sign);
                log.info("验签结果:{}", checkSignResult);
                if (!checkSignResult) {
                    throw new BizException(REQUEST_INVALID);
                }
            }
        }
        req.setRecordId(recordId);
        req.setUserId(userId);
        req.setBusinessType(businessType);
        req.setForFront(forFront);
        ReaderInfoDTO readerInfoDTO = null;
        if (Objects.isNull(req.getBusinessType())) {
            readerInfoDTO = documentCallbackService.info(request, req);
        }
        if (CallbackBusinessType.SAMPLE_EXAMPLES.getCode().equals(req.getBusinessType())) {
            readerInfoDTO = samplateFileCallbackService.info(request, req);
        }
        if (CallbackBusinessType.PERSONAL_LIBRARY.getCode().equals(req.getBusinessType())) {
            readerInfoDTO = personalLibraryCallbackService.info(request, req);
        }
        if (CallbackBusinessType.PREVIEW_FILE.getCode().equals(req.getBusinessType())) {
            readerInfoDTO = readerPreviewCallbackService.info(request, req);
        }
        if (CallbackBusinessType.FILE_ID_PREVIEW_FILE.getCode().equals(req.getBusinessType())) {
            readerInfoDTO = readerFileIdPreviewCallback.info(request, req);
        }
        if (StringUtils.isNotBlank(sign)) {
            redisService.delete(sign);
        }
        return readerInfoDTO;
    }

    /**
     * 连续阅读轻阅读回调获取附件接口  （包含）主件放第一条
     * @param req
     *
     * @return
     */
    @PostMapping("/attachments")
    public  List<ReaderAttachmentDTO> attachment( @RequestBody ReaderAttachmentCallbackReq req){
        Long recordId = req.getRecordId();
        Long docId = req.getDocId();
        Long userId = req.getUserId();
        String sign = req.getSuwellsign();
        if (totpUtil.isCheckSign()) {
            if (StringUtils.isNotEmpty(sign) && userId != null && userId > 0) {
                boolean checkSignResult = totpUtil.verifyTOTPFlexibility(docId.toString(), userId.toString(), sign);
                log.info("验签结果:{}", checkSignResult);
                if (!checkSignResult) {
                    throw new BizException(REQUEST_INVALID);
                }
            }
        }
        List<Document> documentList = documentService.listByRecordIds(List.of(recordId));
        List<ReaderAttachmentDTO> result = new ArrayList<>();
        if(CollUtil.isNotEmpty(documentList)){
            documentList.stream().filter(d -> RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode().equals(d.getCtype())||
                    RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode().equals(d.getCtype()))
                    .peek(a->{
                        if(Objects.equals(a.getCtype(), RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode())
                                && a.getAttachmentType() == null) {
                            a.setAttachmentType(DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode());
                        }
                    })
                    .sorted(Comparator.comparing(Document::getAttachmentType,Comparator.nullsFirst(Integer::compareTo))
                            .thenComparing(Document::getId)
                    ).forEach(attachment -> {
                ReaderAttachmentDTO readerAttachmentDTO = new ReaderAttachmentDTO();
                readerAttachmentDTO.setPid(recordId);
                readerAttachmentDTO.setId(attachment.getId());
                readerAttachmentDTO.setName(attachment.getName());
                readerAttachmentDTO.setUrl(getWebUrl(req, attachment.getId()));
                result.add(readerAttachmentDTO);
            });
        }
        return result;
    }

    private String getWebUrl(ReaderAttachmentCallbackReq req, Long documentId){
        //生成回调地址
        String serverEndpointPrefix = serverBasePathConfig.buildServerUrl(false);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
                serverEndpointPrefix + "/record/v1/readCallBack/info");
        String sign = totpUtil.generateMyTOTP(documentId.toString(), req.getUserId().toString());
        builder.queryParam("docId", documentId)
                .queryParam("recordId", req.getRecordId())
                .queryParam("userId", req.getUserId())
                .queryParam("businessType", req.getBusinessType())
                .queryParam("forFront", req.getForFront())
                .queryParam("suwellsign", sign)
                .queryParam("timestamp", req.getTimestamp());
        return URLEncoder.encode(builder.toUriString(), Charsets.UTF_8);
    }

}
