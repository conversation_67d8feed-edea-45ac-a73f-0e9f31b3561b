package com.suwell.plss.record.mapstruct;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.entity.LogRecordView;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/25
 */

@Mapper
public interface RecordLogConvertor {
    RecordLogConvertor INSTANCE = Mappers.getMapper(RecordLogConvertor.class);

    @Mapping(source = "id",target = "id")
    RecordLogResp toResp(LogRecordView logRecordView);


    Page<RecordLogResp> toRespList(IPage<LogRecordView> logRecordViewIPage);
}
