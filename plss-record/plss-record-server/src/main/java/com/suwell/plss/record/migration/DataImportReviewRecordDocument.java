package com.suwell.plss.record.migration;

import lombok.Data;

@Data
public class DataImportReviewRecordDocument {
    private Long id;
    private Long fileId;
    private String content;
    /**
     * 文档类型(1:主文档, 2:附件)
     */
    private Integer ctype;
    /**
     * 附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件<br/>
     *
     * @see com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum
     */
    private Integer attachmentType;
    /**
     * 文件扩展名
     */
    private String fileExt;
    private Long fileSize;
    private String fileName;
    private String md5;
}
