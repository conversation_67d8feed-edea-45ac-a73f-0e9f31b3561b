package com.suwell.plss.record.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.ProofreadRecordReq;
import com.suwell.plss.record.standard.dto.response.ProofreadRecordResp;
import com.suwell.plss.record.standard.service.DocumentProofreadRecordFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/proofread/record")
public class ProofreadRecordController {
    @Resource
    private DocumentProofreadRecordFacade documentProofreadRecordFacade;

    @PostMapping("/page")
    public R<Page<ProofreadRecordResp>> queryProofreadRecordPage(@Valid @RequestBody ProofreadRecordReq req) {
        if (req.getStm() != null && req.getEtm() != null) {
            // 计算两个日期之间的天数差
            long daysBetween = DateUtil.between(req.getStm(), req.getEtm(), DateUnit.DAY);

            // 将天数转换为年份（365天为一年）
            double yearsBetween = (double)daysBetween / 365;
            if(yearsBetween > 1){
                return R.error("筛查时间区间不可超过1年");
            }
        }

        return R.ok(documentProofreadRecordFacade.queryProofreadRecordPage(req));
    }

    @PostMapping("/detail")
    public R<ProofreadRecordResp> queryDetail(@RequestBody String fileName) {
        return R.ok(documentProofreadRecordFacade.queryDetail(fileName));
    }

    /**
     * 导出纪录
     */
    @PostMapping("/exportRecord")
    public void exportRecord(HttpServletResponse response, @RequestBody(required = false) ProofreadRecordReq req) {
        documentProofreadRecordFacade.exportRecord(response,req);
    }

    /**
     * 聚合发文机关
     */
    @GetMapping("/queryOrgan")
    public R<List<String>> queryOrgan() {
        return R.ok(documentProofreadRecordFacade.queryOrgan());
    }
}
