package com.suwell.plss.record.pipeline.handler;

import com.suwell.plss.framework.mq.dto.base.MqDocProcessBaseDTO;
import com.suwell.plss.record.standard.domain.MqNodeProcessDto;
import com.suwell.plss.framework.mq.events.NodeFileDesensitizeProcessEvent;
import com.suwell.plss.record.standard.dto.request.TaskDocStatusReq;
import com.suwell.plss.record.standard.dto.request.TaskNodeReq;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.enums.TaskDocStatusEnum;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/7
 * @content
 */
@Component
public class FileDesensitizeNodeHandler extends AbstractNodeHandler{
    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.NT_FILE_DESENSITIZE;
    }

    @Override
    public void sendMq(MqNodeProcessDto dto) {
        context.publishEvent(new NodeFileDesensitizeProcessEvent<>(dto, dto.getPriorityValue()));
    }

    @Override
    public boolean needBreak(TaskNodeReq req) {
        return false;
    }

    @Override
    public MqDocProcessBaseDTO prePending(Long taskDocId, Long recordId) {
        return null;
    }


    @Override
    public void preFail(Long taskDocId,Long recordId) {

    }

    @Override
    protected Integer nodeExecuteLogic(TaskNodeReq req) {
        return TaskDocStatusEnum.TDS_SUCCESS.getCode();
    }

    @Override
    public Integer findTaskStatus(TaskDocStatusReq req) {
        return TaskDocStatusEnum.TDS_SUCCESS.getCode();
    }
}
