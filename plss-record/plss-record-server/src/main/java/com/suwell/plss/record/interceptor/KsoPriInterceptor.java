package com.suwell.plss.record.interceptor;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.utils.ForestDataType;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.domain.KsoHeaderIn;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.security.service.KsoAccessTokenService;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 14:32 <br/>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KsoPriInterceptor<T> implements Interceptor<T> {

    private final KsoAccessTokenService ksoAccessTokenService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Override
    public boolean beforeExecute(ForestRequest req) {

        final String accessToken = ksoAccessTokenService.getAppAccessToken(SecurityUtils.getTenantId(),ksoAuthConfig.getTokenUrlForInternal());
        req.addHeader("Authorization", accessToken);
        req.addHeader("Content-Type",req.getContentType());
        return true;
    }

    private static String kso1Sign(String method, String uri, String contentType, String ksoDate, byte[] requestBody,
                                   String accessKey, String secretKey) {
        try {
            String ksoSignature = getKso1Signature(method, uri, contentType, ksoDate, requestBody, secretKey);

            return String.format("KSO-1 %s:%s", accessKey, ksoSignature);
        } catch (Exception e) {
            throw new ServiceException("KSO-1 签名失败", e);
        }
    }

    private static String getKso1Signature(String method, String uri, String contentType, String ksoDate,
                                           byte[] requestBody, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        String sha256Hex = "";
        if (requestBody != null && requestBody.length > 0) {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(requestBody);
            sha256Hex = bytesToHex(hash);
        }
        String dataToSign = "KSO-1" + method + uri + contentType + ksoDate + sha256Hex;
        Mac mac = Mac.getInstance("HmacSHA256");

        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8),
                "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] macBytes = mac.doFinal(dataToSign.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(macBytes);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }


}
