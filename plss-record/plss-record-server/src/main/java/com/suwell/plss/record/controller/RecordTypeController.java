package com.suwell.plss.record.controller;

import static com.suwell.plss.framework.common.constant.AkaliConst.AKALI_COUNT;
import static com.suwell.plss.framework.common.constant.AkaliConst.AKALI_DURATION;

import com.alibaba.fastjson2.JSON;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import com.suwell.plss.system.api.enums.SystemBizError;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.akali.annotation.AkaliHot;
import org.dromara.akali.enums.FlowGradeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * 文件类型 管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-08 18:47:30
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/v1/recordType")
public class RecordTypeController {

    @Resource
    private StandardRecordTypeFacade recordTypeFacade;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Value("${record.server.personal.recordTypeId:2679033248005}")
    private Long recordTypeId;

    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<RecordTypeResp>> queryPage(@RequestBody RecordTypeQueryReq req) {
        PageUtils<RecordTypeResp> page = recordTypeFacade.queryPage(req);
        return R.ok(page);
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    public R<List<RecordTypeResp>> list(@RequestBody RecordTypeQueryReq req) {
        List<RecordTypeResp> recordTypeRespList = recordTypeFacade.queryList(req);
        return R.ok(recordTypeRespList);
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    public R<RecordTypeMetadataResp> info(@RequestBody Long id) {
        return R.ok(recordTypeFacade.queryById(id));
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody RecordTypeReq recordTypeReq) {
        return recordTypeFacade.addRecordType(recordTypeReq) ? R.ok() : R.error();
    }

    /**
     * 批量导入 添加保存
     * //--
     */
    @PostMapping("/saveBatch")
    public R<Void> saveBatch(@RequestBody List<RecordTypeReq> recordTypeReqList) {
        return recordTypeFacade.addBatchRecordType(recordTypeReqList) ? R.ok() : R.error();
    }


    /**
     * 文件上传请求
     */
    @PostMapping("/importMetadata")
    public void upload(@RequestParam("file") MultipartFile file) {
        String regex = ".*\\.(xlsx|xls|et)";
        AssertUtils.isTrue(file.getOriginalFilename().matches(regex), SystemBizError.CATEGORY_TEMPLATE_ERROR);

        log.info("文件类型:{}", file.getOriginalFilename());
        ExcelUtil<RecordTypeMetadataAddReq> excelUtil = new ExcelUtil<>(RecordTypeMetadataAddReq.class);
        try (InputStream inputStream = file.getInputStream()) {
            List<RecordTypeMetadataAddReq> recordTypeMetadataAddReqList = excelUtil.importExcel(inputStream);
            log.info("excl rsp:{}", JSON.toJSONString(recordTypeMetadataAddReqList));
            //recordTypeFacade.importExcel(categoryImportReqList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws UnsupportedEncodingException {
        ExcelUtil<RecordTypeMetadataAddReq> util = new ExcelUtil<>(RecordTypeMetadataAddReq.class);
        String fileName = "元数据项导入模板" + wfgIdGenerator.next() + ".xlsx";
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + URLEncoder.encode(fileName, String.valueOf(StandardCharsets.UTF_8)) + "\"");
        util.importTemplateExcel(response, "元数据项导入模板");
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RecordTypeReq recordTypeReq) {
        recordTypeFacade.modifyById(recordTypeReq);
        return R.ok();
    }

    /**
     * 删除
     */
    @Log(title = "'后台-文档类型管理'",businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody Long[] ids) {
        recordTypeFacade.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    // 文件关联-元数据

    /**
     * 文件元数据列表
     */
    @PostMapping("/metadata")
    public R<List<RecordTypeMetadataRuleResp>> queryMetadata(@RequestBody Long recordTypeId) {
        // 迭代升级导致，避免改前端接口
        List<TypeMetadataRuleResp> typeMetadataRule = recordTypeFacade.queryTypeMetadataRule(recordTypeId);
        List<RecordTypeMetadataRuleResp> collect = typeMetadataRule.stream().map(x -> {
            RecordTypeMetadataRuleResp ruleResp = new RecordTypeMetadataRuleResp();
            ruleResp.setId(x.getMdId());
            ruleResp.setName(x.getMdName());
            ruleResp.setValueType(x.getValueType());
            ruleResp.setValueRange(x.getValueRange());
            ruleResp.setComment(x.getRemark());
            ruleResp.setSearchFlag(x.getSearchFlag());
            ruleResp.setOrderby(x.getOrderby());
            ruleResp.setRequired(x.getRequired());
            ruleResp.setOptEdit(x.getOptEdit());
            ruleResp.setOptView(x.getOptView());
            ruleResp.setPinyin(x.getPinyin());
            ruleResp.setMnemonic(x.getMnemonic());
            ruleResp.setBorrowView(x.getBorrowView());
            ruleResp.setDetailsView(x.getDetailsView());
            ruleResp.setSearchResultView(x.getSearchResultView());
            ruleResp.setSearchFieldWay(x.getSearchFieldWay());
            ruleResp.setRecordtypeId(x.getRecordtypeId());
            ruleResp.setMdcategoryId(x.getMdcategoryId());
            ruleResp.setDefine(x.getDefine());
            ruleResp.setOptSortStandardItem(x.getOptSortStandardItem());
            ruleResp.setAliasName(x.getAliasName());
            ruleResp.setAliasNameView(x.getAliasNameView());
            return ruleResp;
        }).collect(Collectors.toList());
        return R.ok(collect);
    }

    /**
     * 有条件的查询文件元数据
     */
    @PostMapping("/conditionQueryMetadata")
    public R<List<RecordTypeMetadataRuleResp>> conditionQueryMetadata(@RequestBody RecordTypeQueryReq req) {
        List<RecordTypeMetadataRuleResp> list = recordTypeFacade.conditionQueryMetadata(req);
        return R.ok(list);
    }


    /**
     * 文件元数据列表
     */
    @PostMapping("/personalRecordTypeMetadata")
    public R<PersonalRecordTypeMetadataResp> queryPersonalRecordTypeMetadata() {
        return R.ok(recordTypeFacade.queryPersonalRecordTypeMetadata(recordTypeId));
    }


    /**
     * 文件类型 删除分组
     */
    @Log(title = "'后台-文档类型管理'", businessType = BusinessType.DELETE)
    @PostMapping("/deleteTypeGroup/{groupId}")
    public R<Void> deleteTypeGroup(@PathVariable("groupId") Long groupId) {
        recordTypeFacade.removeRecordTypeGroup(groupId);
        return R.ok();
    }

    /**
     * 文件类型 移动文件类型到指定分组
     */
    @Log(title = "'文档类型管理'", businessType = BusinessType.UPDATE)
    @PostMapping("/moveRecordTypeGroup")
    public R<Void> moveRecordTypeGroup(@RequestBody MoveRecordTypeGroupReq req) {
        recordTypeFacade.updateRecordTypeGroup(req);
        return R.ok();
    }


    /**
     * 文件类型 添加元数据 YAPI
     */
    @Log(title = "'后台-文档类型管理'", businessType = BusinessType.INSERT)
    @PostMapping("/saveMetadata")
    public R<Void> saveMetadata(@RequestBody RecordTypeMetadataReq recordTypeMetadataReq) {
        recordTypeFacade.addMetadata(recordTypeMetadataReq);
        return R.ok();
    }

    /**
     * 文件类型 修改元数据
     */
    @Log(title = "'文档类型管理'", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMetadata")
    public R<Void> updateMetadata(@RequestBody RecordTypeMetadataEditReq recordTypeMetadataEditReq) {
        recordTypeFacade.modifyMetadata(recordTypeMetadataEditReq);
        return R.ok();
    }

    /**
     * 文件类型 删除元数据
     */
    @PostMapping("/deleteMetadata")
    public R<Void> deleteMetadata(@RequestBody Long[] id) {
        recordTypeFacade.removeMetadata(id[0]);
        return R.ok();
    }

    /**
     * 查询文件类型前端显示的元数据
     */
    @PostMapping("/recordMetadata")
    @AkaliHot(grade = FlowGradeEnum.FLOW_GRADE_QPS, count = AKALI_COUNT, duration = AKALI_DURATION)
    public R<Map<String, List<RecordTypeMetadataFrontResp>>> queryRecordMetadata() {
        return R.ok(recordTypeFacade.queryRecordMetadata());
    }

    /**
     * 启用/禁用文档类型：禁用时同步禁用相关联的入库方案，启用不需要同步启用
     */
    @Log(title = "'文档类型管理'", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public R updateStatus(@RequestParam("recordTypeId") Long recordTypeId, @RequestParam("status") Integer status) {
        recordTypeFacade.updateStatus(recordTypeId, status);
        return R.ok();
    }


    /**
     * 可被搜索元数据列表查询
     */
    @RequestMapping("/querySearchableMetadata")
    public R<List<SearchableMetadataResp>> querySearchableMetadata(@RequestBody SearchableMetadataReq searchableMetadataReq) {
        List<SearchableMetadataResp> searchableMetadataList = recordTypeFacade.querySearchableMetadata(searchableMetadataReq);
        return R.ok(searchableMetadataList);
    }
}
