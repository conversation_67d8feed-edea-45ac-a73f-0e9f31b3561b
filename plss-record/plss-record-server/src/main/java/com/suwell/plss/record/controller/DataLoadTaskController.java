package com.suwell.plss.record.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.service.DataLakeService;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskAddReq;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskHeatHeartReq;
import com.suwell.plss.record.standard.dto.request.DataLoadTaskReq;
import com.suwell.plss.record.standard.dto.response.DataLoadTaskResp;
import com.suwell.plss.record.standard.service.DataLoadTaskFacade;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据装载导入任务管理
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
@RestController
@RequestMapping("/v1/dataLoadTask")
public class DataLoadTaskController {

    @Resource
    private DataLoadTaskFacade dataLoadTaskFacade;

    /**
     * 分页列表
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    public R<PageUtils<DataLoadTaskResp>> queryPage(@RequestBody DataLoadTaskReq req) {
        return R.ok(dataLoadTaskFacade.queryPage(req));
    }

    /**
     * 详情 任务详情
     *
     * @param taskId 任务id
     */
    @PostMapping("/info")
    public R<DataLoadTaskResp> info(@RequestBody Long taskId) {
        return R.ok(dataLoadTaskFacade.queryDataLoadTaskInfo(taskId));
    }

    /**
     * 数据导入任务添加
     *
     * @param req
     * @return
     */
    @PostMapping("/add")
    public R<Long> add(@RequestBody @Validated DataLoadTaskAddReq req) {
        long taskId = dataLoadTaskFacade.addDataLoadTaskWithDataDelivery(req);
        return R.ok(taskId);
    }

    /**
     * 停止
     *
     * @param taskId 任务id
     */
    @PostMapping("/stop")
    public R<Void> stop(@RequestBody Long taskId) {
        dataLoadTaskFacade.modifiedStop(taskId);
        return R.ok();
    }

    /**
     * 导入中的任务健康检查【废弃】
     *
     * @param req 任务id
     */
    @PostMapping("/health")
    public R<Void> health(@RequestBody DataLoadTaskHeatHeartReq req) {
        dataLoadTaskFacade.health(req);
        return R.ok();
    }

    /**
     * 所有导入中的任务健康检查【废弃】
     */
    @PostMapping("/healthAll")
    public R<Void> healthAll() {
        dataLoadTaskFacade.healthAll();
        return R.ok();
    }


}
