package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Tuple;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.constant.HttpStatus;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.YesOrNoFlag;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.*;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.framework.datasource.sharding.DataSourceConfiguration;
import com.suwell.plss.framework.redis.service.RedisService;
import com.suwell.plss.framework.security.auth.AuthUtil;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.domain.FileDownloadDTO;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.FileRecord;
import com.suwell.plss.record.entity.FileRecordMd5Rel;
import com.suwell.plss.record.file.conf.CryptoConfig;
import com.suwell.plss.record.file.crypto.Crypto;
import com.suwell.plss.record.file.crypto.ProjectCertificate;
import com.suwell.plss.record.file.enums.Credentials;
import com.suwell.plss.record.file.enums.FileType;
import com.suwell.plss.record.file.enums.StoragePlatform;
import com.suwell.plss.record.mapper.FileRecordMapper;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileRecordMd5RelService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.request.FileTransferReq;
import com.suwell.plss.record.standard.dto.request.ReplaceRecordReq;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.TempUrlResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.enums.RecordBizError;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordDownloadType;
import com.suwell.plss.system.api.domain.LoginUser;
import com.suwell.plss.system.api.domain.request.SysAppearanceConfigReq;
import com.suwell.plss.system.api.entity.SysDictData;
import com.suwell.plss.system.api.service.ConfigRpcService;
import com.suwell.plss.system.api.service.DictDataRpcService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.dromara.x.file.storage.core.Downloader;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.file.FileWrapper;
import org.dromara.x.file.storage.core.platform.FileStorage;
import org.dromara.x.file.storage.core.platform.WPSStoreFileStorage;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import org.yaml.snakeyaml.util.UriEncoder;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
@DS(DataSourceConfiguration.SHARDING_DATA_SOURCE_NAME)
public class FileServiceImpl extends ServiceImpl<FileRecordMapper, FileRecord> implements FileService,
        InitializingBean {

    private static final String OUT_ACCESS_URI = "/record/v1/file/outAccessFile";

    private static final String DOCUMENT_CLASSIFIED_DICT_TYPE = "rc_file_classified";
    private static final String WPS_APP_STORE_DRIVE_ID = "wps.app.store.drive.id";

    @Resource
    private FileRecordMapper fileRecordMapper;
    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private Crypto commonCrypto;
    @Resource
    private CryptoConfig cryptoConfig;
    @Resource
    private RedisService redisService;
    @Lazy
    @Resource
    private RedissonClient redissonClient;
    @Lazy
    @Resource
    private DocumentService documentService;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private FileRecordMd5RelService fileRecordMd5RelService;

    @Resource
    private DictDataRpcService dictDataRpcService;
    @Resource
    private ConfigRpcService configRpcService;
    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Override
    @DSTransactional
    public UploadResp generalUpload(UploadReq req) {
        log.info("req={}, type={}", req, req.getFileData().getClass().getSimpleName());
        FileWrapper fileWrapper = fileStorageService.of(req.getFileData()).getFileWrapper();
        String md5Hex;
        try {
            md5Hex = DigestUtils.md5Hex(fileWrapper.getInputStream());
        } catch (IOException e) {
            throw new ServiceException("获取文件摘要失败", e);
        }
        String originName = StringUtils.isEmpty(req.getOriginName()) ? fileWrapper.getName() : req.getOriginName();
        // 以md5做唯一标识，不重复上传
        FileRecordMd5Rel exist = fileRecordMd5RelService.findByMd5Hex(md5Hex);
        if (Objects.nonNull(exist)) {
            FileRecord fileRecord = getById(exist.getFileId());
            if (Objects.isNull(fileRecord)) {
                // 关系存在，文件不存在，删除关系，重新上传
                fileRecordMd5RelService.remove(md5Hex, exist.getFileId());
            } else {
                // 关系存在，文件存在，保存新的关系，然后返回
                fileRecord.setId(req.getAimId());
                fileRecord.setOriginName(originName);
                return saveBaseInfo(fileRecord, true);
            }
        }
        try {
            FileRecord fileRecord = uploadFile(req.isRequireEncrypt(), req.getFileType(),
                    md5Hex, originName, fileWrapper.getInputStream(), fileWrapper.getSize());
            fileRecord.setCreateBy(SecurityUtils.getUserId());
            fileRecord.setId(req.getAimId());
            return saveBaseInfo(fileRecord, false);
        } catch (IOException e) {
            throw new ServiceException("上传文件失败", e);
        }
    }

    private UploadResp saveBaseInfo(FileRecord fileRecord, boolean exist) {
        saveOrUpdateOne(fileRecord);
        UploadResp resp = convertResp(fileRecord);
        resp.setExist(exist);
        return resp;
    }

    @Override
    public String typeFileExt(Long recordId, Integer type) {
        List<Document> list = documentService.listByType(recordId, type);

        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        Document document = list.get(0);
        FileRecord fileRecord = getById(document.getFileId());
        if (Objects.isNull(fileRecord)) {
            return null;
        }

        return fileRecord.getFileExt();
    }

    @Override
    public void downloadFile(FileDownloadDTO dto, HttpServletResponse response) {
        FileInfo fileInfo = findFileInfo(dto.getTargetId());
        if (Objects.isNull(fileInfo)) {
            try {
                response.sendError(HttpStatus.NOT_FOUND, "The file does not exist or has expired");
                return;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        String originName = StringUtils.isEmpty(dto.getTargetName()) ?
                fileInfo.getOriginalFilename() : dto.getTargetName();
        if (ObjectUtils.isNotEmpty(dto.getClassified())) {
            //获取文件密级信息
            R<List<SysDictData>> r = dictDataRpcService.getDictDataList(DOCUMENT_CLASSIFIED_DICT_TYPE);
            if (r.isError()) {
                throw new ServiceException(r.getMsg());
            }
            originName = "[" + r.getData().stream()
                    .filter(sysDictData -> sysDictData.getDictValue().equals(dto.getClassified().toString()))
                    .findFirst().get().getDictLabel() + "]" + originName;
        }
        try {
            originName = URLEncoder.encode(originName, StandardCharsets.UTF_8);
            UploadResp info = getInfo(dto.getTargetId());
            if (Objects.equals(dto.getPreview(), 1)) {
                response.setHeader("Content-Disposition", "inline;filename=" + originName);
            } else {
                response.setHeader("Content-Disposition", "attachment;filename=" + originName);
            }
            response.setContentType(info.getContentType());
            response.setHeader("Plss-File-Name", originName);
            innerDownload(dto.getTargetId(), response.getOutputStream());
        } catch (IOException e) {
            log.error("下载文件失败", e);
            try {
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                response.sendError(HttpStatus.NOT_FOUND, "download file error");
            } catch (IOException ignore) {
                // ignore
            }
        }
    }

    @Override
    public void downloadRecordFile(Long recordId, Integer type, HttpServletResponse response) {
        downloadRecordFile(recordId, type, null, response);
    }

    @Override
    public void downloadRecordFile(Long recordId, Integer type, Long attachmentId, HttpServletResponse response) {
        List<Document> list = documentService.listByRecordIds(List.of(recordId));
        if (CollectionUtil.isEmpty(list)) {
            log.warn("可能是非法的recordId:{},未找到任何文件", recordId);
            response.setStatus(HttpStatus.NOT_FOUND);
            return;
        }
        Map<Integer, List<FileDownloadDTO>> collect = list.stream().map(FileDownloadDTO::convertOfdFirst)
                .collect(Collectors.groupingBy(FileDownloadDTO::getCtype));
        FileDownloadDTO master = collect.get(RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER.getCode()).get(0);
        List<FileDownloadDTO> attachments = collect.getOrDefault(
                RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(), Lists.newArrayList());
        String zipName = null;
        List<FileDownloadDTO> needDownload = Lists.newArrayList();
        switch (RecordDownloadType.getEnum(type)) {
            case MASTER_OFD:
                // 下载单个主件ofd
                // 优先下载ofd文件
                needDownload.add(master);
                break;
            case ATTACH_LIST:
                // 下载多个附件
                zipName = "附件.zip";
                needDownload.addAll(attachments);
                break;
            case MASTER_ATTACH_LIST:
                // 下载主件和附件
                // 优先下载ofd文件
                needDownload.add(master);
                needDownload.addAll(attachments);
                zipName = FilenameUtils.getBaseName(master.getTargetName()) + ".zip";
                break;
            case MASTER_ORIGIN:
                // 下载单个主件原文
                needDownload.add(master.originFirst());
                break;
            case MASTER_TXT:
                // 下载单个主件txt
                needDownload.add(master.txtFirst());
                break;
            case MASTER_OFD_AND_ORIGIN:
                if(!checkOfd(master)){
                    FileDownloadDTO masterOfd = new FileDownloadDTO();
                    BeanUtils.copyProperties(master, masterOfd);
                    needDownload.add(masterOfd);
                }
                zipName = FilenameUtils.getBaseName(master.getTargetName()) + ".zip";
                needDownload.add(master.originFirst());
                break;
            case SINGLE_ATTACH:
                //因为连续阅读页面改版，轻阅读callback接口只能传递docId, 单个附件下载这里就不能走fileId，需要用docId判断
                if (Objects.nonNull(attachmentId)) {
                    list.stream().filter(d -> d.getId().equals(attachmentId))
                            .map(FileDownloadDTO::convertOfdFirst)
                            .map(FileDownloadDTO::originFirst).forEach(needDownload::add);
                } else {
                    log.warn("下载单个附件，但是没有传递附件id");
                }
                break;
            case ALL:
                //如果是OFD则不需要重复下载
                if(!checkOfd(master)){
                    FileDownloadDTO ofdMaster = new FileDownloadDTO();
                    BeanUtils.copyProperties(master, ofdMaster);
                    needDownload.add(ofdMaster);
                }
                zipName = FilenameUtils.getBaseName(master.getTargetName()) + ".zip";
                needDownload.add(master.originFirst());
                needDownload.addAll(attachments);
                break;
        }
        if (CollectionUtil.isEmpty(needDownload)) {
            response.setStatus(HttpStatus.NOT_FOUND);
            log.warn("下载文件失败，没有找到需要下载的文件");
            return;
        }
        // 下载文件
        downloadZipFile(needDownload, response, zipName);
    }

    private boolean checkOfd(FileDownloadDTO dto){
        if("ofd".equalsIgnoreCase(FileNameUtil.getSuffix(dto.getOriginFileName()))){
            return true;
        }
        return false;
    }

    @Override
    public void downloadRecordFileToAIPlatform(Long recordId, Long templateId, Integer type,
            HttpServletResponse response) {
        Long aPlatformFileId = recordId;
        List<Document> list = documentService.listByRecordIds(List.of(aPlatformFileId));
        if (CollectionUtil.isEmpty(list)) {
            log.warn("可能是非法的aiPlatformFileId:{},未找到任何文件", aPlatformFileId);
            aPlatformFileId = templateId;
        }
        downloadRecordFile(aPlatformFileId, type, response);
    }

    @Override
    @DSTransactional
    public void replaceRecordFile(ReplaceRecordReq req) {
        // 校验原文件是否存在
        Document document = documentService.getMasterDocByRecordId(req.getRecordId());
        AssertUtils.notNull(document, RecordBizError.FILE_RESOURCE_NOT_EXIST);
        Long oldFileId = document.getOfdFileId();
        FileRecord oldFile = getById(oldFileId);
        AssertUtils.notNull(oldFile, RecordBizError.FILE_RESOURCE_NOT_EXIST);
        // 删除原文件并更新record
        removeById(oldFileId);
        fileRecordMd5RelService.remove(oldFile.getMd5Hex(), oldFileId);
        // 上传新文件
        UploadReq uploadReq = new UploadReq();
        uploadReq.setAimId(oldFileId);
        uploadReq.setFileData(req.getFileData());
        uploadReq.setFileType(oldFile.getFileType());
        uploadReq.setOriginName(oldFile.getOriginName());
        uploadReq.setRequireEncrypt(Objects.equals(oldFile.getEncryption(), YesOrNoFlag.YNF_YES.getCode()));
        generalUpload(uploadReq);
    }

    @Override
    public void innerDownload(Long fileId, OutputStream outputStream) throws IOException {
        FileRecord fileRecord = getById(fileId);
        if (Objects.isNull(fileRecord)) {
            return;
        }
        FileInfo fileInfo = convertFileInfo(fileRecord);
        if (!fileStorageService.exists(fileInfo)) {
            log.error("文件不存在:{}", fileRecord);
            throw new IOException("文件不存在");
        }
        try {
            Downloader download = fileStorageService.download(fileInfo);
            if (Objects.equals(fileRecord.getEncryption(), YesOrNoFlag.YNF_NO.getCode())) {
                download.outputStream(outputStream);
                return;
            }
            download.inputStream(is -> {
                Integer version = fileRecord.getEncryptVersion();
                String password = fileRecord.getEncryptPassword();
                String magicNumber = fileRecord.getEncryptMagicNumber();
                String salt = Credentials.getSalt(magicNumber, version);
                boolean coreData = Objects.equals(FileType.CORE_FILE.getCode(), fileRecord.getFileType());
                ProjectCertificate certificate = ProjectCertificate.within(coreData, magicNumber, version, salt,
                        password);
                long start = System.currentTimeMillis();
                commonCrypto.decrypt(certificate, new BufferedInputStream(is, 1024 * 1024 * 10), outputStream);
                log.info("下载文件:{}, 整个解密过程执行耗时: {}ms", fileRecord.getMd5Hex(),
                        System.currentTimeMillis() - start);
            });
        } catch (Exception e) {
            throw new IOException("下载文件失败", e);
        }
    }

    @Override
    public UploadResp getInfo(Long fileId) {
        FileRecord fileRecord = getById(fileId);
        AssertUtils.notNull(fileRecord, RecordBizError.FILE_RESOURCE_NOT_EXIST);
        return convertResp(fileRecord);
    }

    @Override
    public List<FileRecord> batchList(Collection<Long> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return List.of();
        }
        int batchSize = 100; // 每批次的大小
        List<FileRecord> result = new ArrayList<>();
        List<List<Long>> partitions = Lists.partition(new ArrayList<>(fileIds), batchSize);
        for (List<Long> partition : partitions) {
            result.addAll(listByIds(partition));
        }
        return result;
    }

    @Override
    public List<UploadResp> batchInfo(List<Long> fileIds) {
        List<FileRecord> fileRecords = Lists.newArrayList();
        for (Long fileId : fileIds) {
            FileRecord fileRecord = getById(fileId);
            if (Objects.nonNull(fileRecord)) {
                fileRecords.add(fileRecord);
            }
        }
        return fileRecords.stream().map(this::convertResp).toList();
    }

    private void delCache(Long fileId) {
        //redis中删除对应file的缓存
        String encryptStr = EncryptUtils.encryptWithMD5(fileId.toString());
        String str = redisService.getStr(encryptStr);
        if (StringUtils.isNotBlank(str)) {
            redisService.delete(str);
        }
    }

    @Override
    public String tempUrl(FileTempUrlReq req) {
        Long fileId = req.getFileId();
        String encryptStr = EncryptUtils.encryptWithMD5(fileId.toString());
        String str = redisService.getStr(encryptStr);
        if (StringUtils.isBlank(str)) {
            redisService.setStr(encryptStr, fileId.toString());
            int duration = req.getDuration();
            if (duration > 0) {
                redisService.setExpire(encryptStr, duration, TimeUnit.MINUTES);
            }
        }
        String exportUrl = serverBasePathConfig.buildServerUrl(req.isForFront());
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(exportUrl)
                .path(OUT_ACCESS_URI)
                .queryParam("sk", encryptStr);
        if (req.isPreview()) {
            builder.queryParam("preview", 1);
        }
        return builder.toUriString();
    }

    @Override
    public List<TempUrlResp> batchTempUrl(List<FileTempUrlReq> reqList) {
        List<Tuple> tuples = reqList.stream().map(req -> {
            String fileId = req.getFileId().toString();
            long seconds = req.getDuration() * 60L;
            return new Tuple(EncryptUtils.encryptWithMD5(fileId), fileId, seconds);
        }).toList();
        redisService.batchSetStr(tuples);

        return reqList.stream().map(req -> {
            TempUrlResp resp = new TempUrlResp();
            resp.setFileId(req.getFileId());
            String exportUrl = serverBasePathConfig.buildServerUrl(req.isForFront());
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(exportUrl)
                    .path(OUT_ACCESS_URI)
                    .queryParam("sk", EncryptUtils.encryptWithMD5(req.getFileId().toString()));
            if (req.isPreview()) {
                builder.queryParam("preview", 1);
            }
            resp.setTempUrl(builder.toUriString());
            return resp;
        }).toList();
    }

    @Override
    public void outAccessFile(String sk, Integer preview, HttpServletResponse response) {
        if (StringUtils.isBlank(sk)) {
            try {
                response.sendError(HttpStatus.NOT_FOUND, "The file does not exist or has expired");
                return;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        String str = redisService.getStr(sk);
        if (StringUtils.isBlank(str)) {
            try {
                response.sendError(HttpStatus.NOT_FOUND, "The file does not exist or has expired");
                return;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        FileDownloadDTO dto = new FileDownloadDTO(Long.parseLong(str));
        dto.setPreview(preview);
        downloadFile(dto, response);
    }

    @Override
    public void transferIn(FileTransferReq req) {
        String fileName = req.getFileName();
        fileStorageService.of(req.getFileData()).setSaveFilename(fileName).setPath(req.getRelativePath())
                .setOriginalFilename(req.getOriginName()).upload();
    }

    @Override
    public void transferOut(Long fileId, HttpServletResponse response) {
        FileInfo fileInfo = findFileInfo(fileId);
        if (Objects.isNull(fileInfo)) {
            return;
        }
        try {
            String originName = URLEncoder.encode(fileInfo.getOriginalFilename(), StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + originName);
            Downloader download = fileStorageService.download(fileInfo);
            download.outputStream(response.getOutputStream());
        } catch (IOException e) {
            throw new ServiceException("下载文件失败", e);
        }
    }

    @Override
    public void transferOut(Long fileId, OutputStream outputStream) {
        FileInfo fileInfo = findFileInfo(fileId);
        if (Objects.isNull(fileInfo)) {
            return;
        }
        Downloader download = fileStorageService.download(fileInfo);
        try {
            download.outputStream(outputStream);
        } catch (Exception e) {
            throw new ServiceException("下载文件失败", e);
        }
    }

    @Override
    public void convertProject2Core(String projectCode, int batchSize, int batchNum) {
        Long count = lambdaQuery().eq(FileRecord::getEncryptMagicNumber, projectCode).count();
        if (count == 0) {
            log.info("项目：{}，没有需要转换的文件", projectCode);
            return;
        }
        long pageSize = count % batchSize == 0 ? count / batchSize : count / batchSize + 1;
        pageSize = Math.min(pageSize, batchNum);
        for (int i = 1; i <= pageSize; i++) {
            Page<Object> page = PageHelperUtils.startPage(i, batchSize);
            List<FileRecord> list = lambdaQuery().eq(FileRecord::getEncryptMagicNumber, projectCode).list();
            page.close();
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            list.parallelStream().forEach(fileRecord -> {
                try {
                    if (Objects.equals(fileRecord.getEncryption(), YesOrNoFlag.YNF_NO.getCode())) {
                        return;
                    }
                    FileInfo fileInfo = convertFileInfo(fileRecord);
                    if (!fileStorageService.exists(fileInfo)) {
                        log.warn("文件：{},fileName:{}，不存在", fileRecord, LogUtil.name(fileRecord.getFileName()));
                        return;
                    }
                    FileItem fileItem = FileUtils.createEmptyFileItem(fileRecord.getOriginName());
                    innerDownload(fileRecord.getId(), fileItem.getOutputStream());
                    FileRecord newFileRecord = uploadFile(true, FileType.CORE_FILE.getCode(), fileRecord.getMd5Hex(),
                            fileRecord.getOriginName(), fileItem.getInputStream(), fileItem.getSize());
                    newFileRecord.setId(fileRecord.getId());
                    newFileRecord.setCreateBy(fileRecord.getCreateBy());
                    fileStorageService.delete(fileInfo);
                    lambdaUpdate().eq(FileRecord::getId, newFileRecord.getId()).update(newFileRecord);
                } catch (Exception e) {
                    log.error("文件id：{}，转换失败", fileRecord.getId(), e);
                }
            });
        }
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void migrateFile() {
        Long count = lambdaQuery().count();
        if (count == 0) {
            log.info("没有需要迁移的文件");
            return;
        }
        FileServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        long page = count % 2000 == 0 ? count / 2000 : count / 2000 + 1;
        ExecutorService executors = Executors.newFixedThreadPool(32);
        ExecutorService finalExecutors = TtlExecutors.getTtlExecutorService(executors);
        CompletableFuture<?>[] array = LongStream.range(0, page + 1).mapToObj(i ->
                CompletableFuture.runAsync(() -> {
                    List<FileRecord> originlist = lambdaQuery().orderByAsc(FileRecord::getId)
                            .last("limit 2000 offset " + i * 2000).list();
                    aopProxy.saveOrUpdateBatch(originlist);
                }, finalExecutors)).toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(array).join();
        log.info("迁移file数据至分表完成，总数：{}", count);
    }


    @Override
    @DSTransactional
    public void saveOrUpdateOne(FileRecord fileRecord) {
        if (Objects.isNull(fileRecord)) {
            return;
        }
        saveOrUpdateBatch(List.of(fileRecord));
    }

    @Override
    public void saveOrUpdateBatch(List<FileRecord> files) {
        if (CollUtil.isEmpty(files)) {
            return;
        }
        saveOrUpdateBatch(files, Math.min(files.size(), 2000));
        fileRecordMd5RelService.saveOrUpdateBatch(files);
    }

    @Override
    public void saveOrUpdateBatchWithTransaction(List<FileRecord> listFileRecord) {
        if (CollectionUtil.isEmpty(listFileRecord)) {
            return;
        }
        List<Long> list = listFileRecord.stream().map(FileRecord::getId).toList();
        lambdaUpdate().in(FileRecord::getId, list).remove();
        super.saveBatch(listFileRecord);
        fileRecordMd5RelService.replaceBatchWithoutTransaction(listFileRecord);
    }

    @Override
    @DSTransactional
    public void deleteBatch(Collection<Long> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return;
        }
        List<FileRecord> fileRecords = listByIds(fileIds);
        if (CollectionUtil.isEmpty(fileRecords)) {
            return;
        }
        //删除缓存
        fileRecords.stream().map(FileRecord::getId).forEach(this::delCache);
        Map<String, List<FileRecord>> recordMap = fileRecords.stream()
                .collect(Collectors.groupingBy(FileRecord::getMd5Hex));
        // 查关系
        List<FileRecordMd5Rel> relList = fileRecordMd5RelService.listByMd5Hex(recordMap.keySet());
        // 删关系
        fileRecordMd5RelService.removeBatch(recordMap.keySet(), fileIds);
        // 删文件
        removeBatchByIds(fileIds);
        // 删存储
        Map<String, Set<Long>> collect = relList.stream().collect(Collectors.groupingBy(FileRecordMd5Rel::getMd5Hex,
                Collectors.mapping(FileRecordMd5Rel::getFileId, Collectors.toSet())));
        collect.forEach((md5hex, fileIdSet) -> {
            List<FileRecord> list = recordMap.getOrDefault(md5hex, List.of());
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            Set<Long> set = list.stream().map(FileRecord::getId).collect(Collectors.toSet());
            fileIdSet.removeAll(set);
            if (CollectionUtil.isEmpty(fileIdSet)) {
                list.forEach(fileRecord -> {
                    FileInfo fileInfo = convertFileInfo(fileRecord);
                    if (fileStorageService.exists(fileInfo)) {
                        log.info("删除存储平台的文件：{}", fileRecord);
                        fileStorageService.delete(fileInfo);
                    }
                });
            }
        });
    }

    @Override
    @DSTransactional
    public Long removeRepeatFile(Long fileId) {
        FileRecord fileRecord = getById(fileId);
        if (Objects.isNull(fileRecord)) {
            return 0L;
        }
        List<FileRecordMd5Rel> relList = fileRecordMd5RelService.listByMd5Hex(List.of(fileRecord.getMd5Hex()));
        if (CollectionUtil.isEmpty(relList)) {
            removeById(fileId);
            return 1L;
        }
        List<Long> list = relList.stream().map(FileRecordMd5Rel::getFileId).toList();
        deleteBatch(list);
        return (long) list.size();
    }

    @SneakyThrows
    private FileRecord uploadFile(boolean requireEncrypt, Integer fileType, String md5Hex, String originName,
            InputStream fileStream, Long fileSize) {
        FileRecord fileRecord = new FileRecord();
        fileRecord.setFileType(fileType);
        fileRecord.setMd5Hex(md5Hex);
        InputStream fileInputStream = fileStream;
        boolean needEncrypt =
                cryptoConfig.isEnabled() && (requireEncrypt || Objects.equals(FileType.CORE_FILE.getCode(), fileType));
        if (needEncrypt) {
            ProjectCertificate certificate = ProjectCertificate.getInstance(
                    Objects.equals(FileType.CORE_FILE.getCode(), fileType), cryptoConfig.getProjectCertificate());
            fileRecord.setEncryptPassword(certificate.getBasePassword());
            fileRecord.setEncryptMagicNumber(certificate.getMagicNumber());
            fileRecord.setEncryptVersion(certificate.getVersion());
            FileItem fileItem = FileUtils.createEmptyFileItem(originName);
            long start = System.currentTimeMillis();
            commonCrypto.encrypt(certificate, fileInputStream, fileItem.getOutputStream());
            log.info("文件：{}, 总加密执行耗时:{}ms", md5Hex, System.currentTimeMillis() - start);
            fileInputStream = fileItem.getInputStream();
            // 加密后获取实时文件大小
            fileSize = fileItem.getSize();
        }
        String path = LocalDate.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd/"));
        FileInfo upload = fileStorageService.of(fileInputStream, originName, null, fileSize)
                .setPlatform(!needEncrypt, StoragePlatform.MINIO_PUBLIC.getPlatFormName())
                .setPath(path).upload();
        fileRecord.setPlatform(upload.getPlatform());
        fileRecord.setFileName(upload.getFilename());
        fileRecord.setOriginName(originName);
        fileRecord.setFileSize(upload.getSize());
        fileRecord.setFileExt(upload.getExt());
        fileRecord.setRelativePath(upload.getPath());
        fileRecord.setEncryption(needEncrypt ? YesOrNoFlag.YNF_YES.getCode() : YesOrNoFlag.YNF_NO.getCode());
        fileRecord.setCreateTime(new Date());
        return fileRecord;
    }

    private UploadResp convertResp(FileRecord fileRecord) {
        UploadResp resp = new UploadResp();
        resp.setId(fileRecord.getId());
        resp.setFileName(fileRecord.getOriginName());
        resp.setFileSize(fileRecord.getFileSize());
        resp.setFileExt(fileRecord.getFileExt());
        resp.setCreateBy(fileRecord.getCreateBy());
        resp.setCreateTime(fileRecord.getCreateTime());
        resp.setMd5Hex(fileRecord.getMd5Hex());
        resp.setEncryption(fileRecord.getEncryption());
        String contentType = MediaTypeFactory.getMediaType(fileRecord.getFileName()).map(MediaType::toString)
                .orElse(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        resp.setContentType(contentType);
        resp.setPublicAccessUrl(getPublicAccessUrl(fileRecord));
        return resp;
    }

    private FileInfo convertFileInfo(FileRecord fileRecord) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBasePath("");
        fileInfo.setFilename(fileRecord.getFileName());
        fileInfo.setPlatform(fileRecord.getPlatform());
        fileInfo.setPath(fileRecord.getRelativePath());
        fileInfo.setOriginalFilename(fileRecord.getOriginName());
        return fileInfo;
    }

    private String getPublicAccessUrl(FileRecord fileRecord) {
        if (!Objects.equals(fileRecord.getPlatform(), StoragePlatform.MINIO_PUBLIC.getPlatFormName())) {
            return null;
        }
        return tempUrl(new FileTempUrlReq(fileRecord.getId(), 60 * 24 * 7, true, true));
    }

    private FileInfo findFileInfo(Long fileId) {
        FileRecord fileRecord = getById(fileId);
        if (Objects.isNull(fileRecord)) {
            return null;
        }
        return convertFileInfo(fileRecord);
    }

    private void downloadZipFile(List<FileDownloadDTO> downloadDTOS, HttpServletResponse response, String zipName) {
        if (CollectionUtil.isEmpty(downloadDTOS)) {
            log.warn("下载文件失败，没有找到对应的文件");
            response.setStatus(HttpStatus.NOT_FOUND);
            return;
        }
        if (downloadDTOS.size() == 1) {
            FileDownloadDTO downloadDTO = downloadDTOS.get(0);
            downloadFile(downloadDTO, response);
            return;
        }
        Map<String, Integer> filenameMap = Maps.newHashMap();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            String originName = UriEncoder.encode(zipName);
            response.setHeader("Content-Disposition", "attachment;filename=" + originName);
            for (FileDownloadDTO downloadDTO : downloadDTOS) {
                Long fileId = downloadDTO.getTargetId();
                FileInfo fileInfo = findFileInfo(fileId);
                if (Objects.isNull(fileInfo)) {
                    log.warn("下载fileId:{}的文件失败，没有找到对应的文件", fileId);
                    response.setStatus(HttpStatus.NOT_FOUND);
                    return;
                }
                String filename = StringUtils.isBlank(downloadDTO.getTargetName()) ? fileInfo.getOriginalFilename()
                        : downloadDTO.getTargetName();
                if (filenameMap.containsKey(filename)) {
                    Integer i = filenameMap.get(filename);
                    String str = FilenameUtils.getBaseName(filename) + "(" + ++i + ")." + FilenameUtils.getExtension(
                            filename);
                    filenameMap.put(filename, i);
                    filenameMap.put(str, 0);
                    filename = str;
                } else {
                    filenameMap.put(filename, 0);
                }
                ZipEntry zipEntry = new ZipEntry(filename);
                zipOutputStream.putNextEntry(zipEntry);
                innerDownload(fileId, zipOutputStream);
                zipOutputStream.closeEntry();
            }
        } catch (IOException e) {
            log.error("下载文件失败", e);
            response.setStatus(HttpStatus.NOT_FOUND);
        }
    }

    @DSTransactional
    @Override
    public boolean saveOrUpdateBatch(List<FileRecord> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }
        entityList.forEach(entity -> {
            QueryWrapper<FileRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", entity.getId());

            long count = fileRecordMapper.selectCount(queryWrapper);
            if (count > 0) {
                if (!isUpdate) {
                    return;
                }
                update(entity, queryWrapper);
            } else {
                save(entity);
            }
        });
        return true;
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void reHashFile(List<Long> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return;
        }
        List<FileRecord> records = fileIds.stream().map(fileId ->
                        fileRecordMapper.getOldSharding(fileId % 30, fileId))
                .filter(Objects::nonNull)
                .toList();
        SpringUtils.getAopProxy(this).saveOrUpdateBatchWithTransaction(records);
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void removeOldShardingHisData() {
        for (int i = 0; i < 30; i++) {
            Long lastId = Long.MAX_VALUE;
            while (true) {
                List<FileRecord> list = fileRecordMapper.pageList(i, 2000, lastId);
                if (CollectionUtil.isEmpty(list)) {
                    break;
                }
                lastId = list.get(list.size() - 1).getId();
                int finalI = i;
                List<Long> ids = list.stream().map(FileRecord::getId)
                        .filter(id -> Math.abs(id.hashCode()) % 100 != finalI).toList();
                if (CollectionUtil.isNotEmpty(ids)) {
                    fileRecordMapper.delBatch(i, ids);
                }
            }
        }
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void reHashFile() {
        int pageSize = 3000;
        FileServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        for (int i = 0; i < 20; i++) {
            fileRecordMapper.clearMd5Rel(i);
        }
        int allCount = 0;
        for (int i = 0; i < 30; i++) {
            int totalCount = fileRecordMapper.totalCount(i);
            if (totalCount == 0) {
                continue;
            }
            int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
            Long lastId = Long.MAX_VALUE;
            for (int j = 0; j < totalPage; j++) {
                List<FileRecord> list = fileRecordMapper.pageList(i, pageSize, lastId);
                lastId = list.get(list.size() - 1).getId();
                List<Long> ids = list.stream().map(FileRecord::getId).toList();
                fileRecordMapper.delBatch(i, ids);
                aopProxy.saveOrUpdateBatchWithTransaction(list);
            }
            allCount += totalCount;
        }
        log.info("rc_file表数据迁移完成，总数:{}", allCount);
    }

    @Override
    public void afterPropertiesSet() {
        if (!ksoAuthConfig.isEnable()) {
            return;
        }
        log.info("金山环境开始获取应用存储使用的driveId");
        FileStorage fileStorage = fileStorageService.getFileStorage();
        if (!(fileStorage instanceof WPSStoreFileStorage wpsStoreFileStorage)) {
            log.info("kso环境开关打开，但是record服务的wps应用存储尚未开启，目前使用的默认存储类型是:{}", fileStorage.getClass());
            return;
        }
        String driveId = wpsStoreFileStorage.getDriveId();
        if (StringUtils.isNotEmpty(driveId)) {
            log.info("wps应用存储的驱动盘id已经设置，请确保在同一环境中不变！！！！！！！！");
            return;
        }
        R<String> configValue = configRpcService.getConfigValue(WPS_APP_STORE_DRIVE_ID);
        if (configValue.isError()) {
            throw new ServiceException("获取db中的驱动盘id请求失败，resp=" + JSONObject.toJSONString(configValue));
        }
        driveId = configValue.getData();
        if (StringUtils.isNotEmpty(driveId)) {
            log.info("从db获取wps应用存储的driveId:{}", driveId);
            wpsStoreFileStorage.setDriveId(driveId);
            return;
        }
        log.info("当前金山环境，开始首次初始化driveId逻辑");
        RLock lock = redissonClient.getLock("lock:" + WPS_APP_STORE_DRIVE_ID);
        // 这里死等
        lock.lock();
        log.info("获取到driveId分布式锁，开始尝试二次db获取数据");
        driveId = wpsStoreFileStorage.requestDriveId();
        log.info("从wps文档中心获取到驱动盘id：{}", driveId);
        wpsStoreFileStorage.setDriveId(driveId);
        SysAppearanceConfigReq configReq = new SysAppearanceConfigReq();
        configReq.setConfigName("文档中心驱动盘id");
        configReq.setConfigKey(WPS_APP_STORE_DRIVE_ID);
        configReq.setConfigValue(driveId);
        R<Void> voidR = configRpcService.saveOrUpdateOne(configReq);
        if (voidR.isSuccess()) {
            log.info("保存新的驱动盘id成功，driveId：{}", driveId);
        }
    }
}