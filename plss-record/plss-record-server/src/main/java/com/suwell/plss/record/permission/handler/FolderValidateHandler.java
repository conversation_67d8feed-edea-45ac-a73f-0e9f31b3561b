package com.suwell.plss.record.permission.handler;

import com.suwell.plss.framework.common.enums.VisitType;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.record.entity.Folder;
import com.suwell.plss.record.entity.Repository;
import com.suwell.plss.record.permission.validator.DataPermissionValidator;
import com.suwell.plss.record.permission.validator.ValidateContext;
import com.suwell.plss.record.standard.enums.PermissionMask;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FolderValidateHandler extends DataPermissionValidator {

    @Override
    public void postProcessBeforeHandle(ValidateContext context, Long folderId, PermissionMask... basePermission) {
        log.info("目录业务自定义前置校验");
        if (!context.isOnlyManagerValid()) {
            return;
        }
        // 校验目录所在的库是否有权限
        Folder folder = folderService.lambdaQuery()
                .select(Folder::getCreateBy)
                .eq(Folder::getId, folderId).one();
        Repository repository = null;
        if (Objects.isNull(folder)) {
            repository = repositoryService.lambdaQuery()
                    .select(Repository::getOwnerId)
                    .eq(Repository::getId, folderId).one();
        }
        if (Objects.isNull(repository) && Objects.isNull(folder)) {
            context.hasError("目录不存在");
            return;
        }
        String ownerId = Objects.isNull(repository) ? folder.getCreateBy() : repository.getOwnerId();
        if (super.listResourceVisitorIds().contains(ownerId)) {
            context.pass();
        }
    }

    @Override
    public void postProcessAfterHandle(ValidateContext context, Long folderId, PermissionMask... basePermission) {
        log.info("目录直接权限校验未通过，继承校验开启");
    }

    @NotNull
    @Override
    public ResourceType getResourceType() {
        return ResourceType.FOLDER;
    }

    @Override
    public boolean isPrivateResource(Long resourceId) {
        Folder byId = folderService.lambdaQuery()
                .select(Folder::getVisitType)
                .eq(Folder::getId, resourceId).one();
        return Objects.nonNull(byId) && Objects.equals(byId.getVisitType(), VisitType.PRIVATE.getCode());
    }

    @Override
    public void makePrivate(Long resourceId) {
        Folder folder = new Folder();
        folder.setId(resourceId);
        folder.setVisitType(VisitType.PRIVATE.getCode());
        folderService.updateById(folder);
    }

    @Override
    public void cancelPrivate(Long resourceId) {
        Folder folder = new Folder();
        folder.setId(resourceId);
        folder.setVisitType(VisitType.PUBLIC.getCode());
        folderService.updateById(folder);
    }

    @Override
    public Integer getClassified(Long resourceId) {
        if (!super.classifiedSwitchConfig.isSafetySwitch()) {
            return null;
        }
        Folder byId = folderService.lambdaQuery()
                .select(Folder::getClassified)
                .eq(Folder::getId, resourceId).one();
        return Objects.nonNull(byId) ? byId.getClassified() : null;
    }
}
