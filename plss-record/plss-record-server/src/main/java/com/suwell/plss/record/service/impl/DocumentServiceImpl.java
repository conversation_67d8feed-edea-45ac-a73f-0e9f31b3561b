package com.suwell.plss.record.service.impl;

import static com.suwell.plss.record.standard.enums.RecordBizError.FILEID_NOT_EXIST;
import static com.suwell.plss.record.standard.enums.RecordBizError.FILE_NOT_EXIST;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_DOCUMENT_MASTER_NOT_EXIST;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_DOCUMENT_UPLOAD_NOT_EXIST;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_UPLOAD_OFFLINE_ERROR;
import static com.suwell.plss.record.standard.enums.RecordBizError.RECORD_UPLOAD_OSS_ERROR;
import static com.suwell.plss.record.standard.enums.RecordEnum.DocumentAttStatusEnum.ATT_STATUS_AWAIT;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL;
import static com.suwell.plss.record.standard.enums.RecordStatusEnum.RECORD_STATUS_PASS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.config.ServerBaseConfig;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.constant.SymbolPool;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.UserClassifiedEnum;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.SpringUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.TOTPUtil;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.datasource.sharding.DataSourceConfiguration;
import com.suwell.plss.framework.datasource.spring.TransactionSynchronizationManagerUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.convert.DocumentConvertUtils;
import com.suwell.plss.record.domain.PermissionDTO;
import com.suwell.plss.record.domain.ReaderInfoDTO;
import com.suwell.plss.record.domain.RecordFrontDto;
import com.suwell.plss.record.domain.TuoMinDTO;
import com.suwell.plss.record.domain.TuoMinRuleDTO;
import com.suwell.plss.record.domain.WatermarkDTO;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.DocumentMd5Rel;
import com.suwell.plss.record.entity.FileRecord;
import com.suwell.plss.record.entity.FolderRecord;
import com.suwell.plss.record.entity.RcWatermarkSetting;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.enums.StoreProcessEnum;
import com.suwell.plss.record.mapper.DocumentMapper;
import com.suwell.plss.record.pipeline.StorageInfoV3Dto;
import com.suwell.plss.record.service.AttFlowService;
import com.suwell.plss.record.service.DocumentHistoryService;
import com.suwell.plss.record.service.DocumentMd5RelService;
import com.suwell.plss.record.service.DocumentRpcService;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.service.FolderRecordService;
import com.suwell.plss.record.service.FolderService;
import com.suwell.plss.record.service.OperateAttESService;
import com.suwell.plss.record.service.PermissionService;
import com.suwell.plss.record.service.RcMaskingConfigService;
import com.suwell.plss.record.service.RcWatermarkSettingService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.AttachmentOpenReq;
import com.suwell.plss.record.standard.dto.request.DocProcessAttachmentFileReq;
import com.suwell.plss.record.standard.dto.request.DocumentUpdateCallbackReq;
import com.suwell.plss.record.standard.dto.request.DocumentUpdateTxtReq;
import com.suwell.plss.record.standard.dto.request.FileInfoBaseReq;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.request.PermissionOperateReq;
import com.suwell.plss.record.standard.dto.request.ReaderCallbackReq;
import com.suwell.plss.record.standard.dto.request.RecordDocumentCheckReq;
import com.suwell.plss.record.standard.dto.request.RecordInfoOfflineAttReq;
import com.suwell.plss.record.standard.dto.request.RecordOperationAttBatchReq;
import com.suwell.plss.record.standard.dto.request.RecordOperationRelationAttReq;
import com.suwell.plss.record.standard.dto.request.RecordOperationReq;
import com.suwell.plss.record.standard.dto.request.ReplaceMasterRecordReq;
import com.suwell.plss.record.standard.dto.request.SaveThumbnailReq;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.DocumentAttachmentsResp;
import com.suwell.plss.record.standard.dto.response.MaskingConfigResp;
import com.suwell.plss.record.standard.dto.response.PermissionOperateResp;
import com.suwell.plss.record.standard.dto.response.RecordOperationResp;
import com.suwell.plss.record.standard.dto.response.RecordRelFolderResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum;
import com.suwell.plss.record.standard.enums.PermissionMask;
import com.suwell.plss.record.standard.enums.RecordEnum.MaskingProcessModeEnum;
import com.suwell.plss.record.standard.enums.WatermarkTypeEnum;
import com.suwell.plss.record.standard.service.StandardTaskFacade;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.service.UserRpcService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import lombok.extern.slf4j.Slf4j;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Trie;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@RefreshScope
@DS(DataSourceConfiguration.SHARDING_DATA_SOURCE_NAME)
@Service
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements DocumentService {

    private static final String OUT_UPLOAD_URI = "/record/v1/file/replaceRecordFile";
    @Resource
    private DocumentMapper documentMapper;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Lazy
    @Resource
    private RecordService recordService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RcMaskingConfigService maskingConfigService;
    @Resource
    private RcWatermarkSettingService watermarkSettingService;
    @Value("${readCallBack.Version:1}")
    private String readCallBackVersion;
    @Resource
    private DocumentHistoryService documentHistoryService;
    @Lazy
    @Resource
    private FolderService folderService;
    @Lazy
    @Resource
    private FolderRecordService folderRecordService;
    @Resource
    private UserRpcService userRpcService;
    @Lazy
    @Resource
    private StandardTaskFacade standardTaskFacade;
    @Resource
    private FileService fileService;
    @Resource
    private TOTPUtil totpUtil;
    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private DocumentMd5RelService documentMd5RelService;
    @Resource
    private OperateAttESService operateAttESService;
    @Resource
    private AttFlowService attFlowService;
    @Resource
    private ServerBaseConfig serverBaseConfig;

    private DocumentRpcService documentRpcService;

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 保存主件的缩略图
     */
    @DSTransactional
    @Override
    public void replaceRecordMasterFile(ReplaceMasterRecordReq req) {
        // 检查换件操作
        Document doc = getOne(Wrappers.<Document>query().lambda()
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .eq(Document::getRecordId, req.getRecordId()));

        // 产生历史记录
        if (Objects.nonNull(doc)) {
            // 产生历史记录
            documentHistoryService.save(DocumentConvertUtils.documentReplace2DocumentHistory(doc,
                    wfgIdGenerator.next()));

            // 拿到新件的fileId
            MultipartFile file = req.getFile();
            UploadReq uploadReq = new UploadReq();
            uploadReq.setFileData(file);
            uploadReq.setOriginName(doc.getName());
            uploadReq.setRequireEncrypt(true);
            UploadResp uploadResp = fileService.generalUpload(uploadReq);

            // 更新
            Document document = new Document();
            document.setId(doc.getId());
            document.setFileId(uploadResp.getId());
            document.setModifiedTime(new Date());
            lambdaUpdate()
                    .eq(Document::getId, doc.getId())
                    .eq(Document::getRecordId, doc.getRecordId())
                    .update(document);
        }
    }

    @DSTransactional
    @Override
    public void saveAttachments(RecordInfoOfflineAttReq recordInfoOfflineAttReq) {
        //附件上传
        MultipartFile[] attFileArray = recordInfoOfflineAttReq.getAttFile();
        AssertUtils.isTrue(attFileArray != null && attFileArray.length > 0, RECORD_DOCUMENT_UPLOAD_NOT_EXIST);
        try {
            //附件上传
            List<Document> documentList = Lists.newArrayList();
            final List<Integer> attFileTypes = recordInfoOfflineAttReq.getAttFileTypes();
            if (CollUtil.isNotEmpty(attFileTypes)) {
                for (int i = 0; i < attFileArray.length; i++) {
                    MultipartFile attFile = attFileArray[i];
                    UploadReq uploadReqAtt = new UploadReq();
                    uploadReqAtt.setFileData(attFile);
                    uploadReqAtt.setOriginName(attFile.getOriginalFilename());
                    uploadReqAtt.setRequireEncrypt(true);
                    UploadResp uploadResp = fileService.generalUpload(uploadReqAtt);
                    Document documentDTOAtt = buildDocument(uploadResp.getId(),
                            attFile.getOriginalFilename(), RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(),
                            recordInfoOfflineAttReq.getRecordId(), uploadResp.getMd5Hex());
                    documentDTOAtt.setAttachmentType(attFileTypes.get(i));
                    documentList.add(documentDTOAtt);
                }
            } else {
                for (MultipartFile attFile : attFileArray) {
                    UploadReq uploadReqAtt = new UploadReq();
                    uploadReqAtt.setFileData(attFile);
                    uploadReqAtt.setOriginName(attFile.getOriginalFilename());
                    uploadReqAtt.setRequireEncrypt(true);
                    UploadResp uploadResp = fileService.generalUpload(uploadReqAtt);
                    Document documentDTOAtt = buildDocument(uploadResp.getId(),
                            attFile.getOriginalFilename(), RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(),
                            recordInfoOfflineAttReq.getRecordId(), uploadResp.getMd5Hex());
                    documentDTOAtt.setAttachmentType(DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode());
                    documentList.add(documentDTOAtt);
                }
            }
            saveBatch(documentList);
        } catch (Exception e) {
            log.error("主件的附件上传失败", e);
            throw new BizException(RECORD_UPLOAD_OFFLINE_ERROR);
        }
    }

    @DSTransactional
    @Override
    public void saveAttachments(DocProcessAttachmentFileReq req) {
        //附件上传
        List<DocProcessAttachmentFileReq.AttFileDto> attFileDtos = req.getAttFileDtos();
        AssertUtils.isTrue(CollUtil.isNotEmpty(attFileDtos), RECORD_DOCUMENT_UPLOAD_NOT_EXIST);
        try {
            //附件上传
            List<Document> documentList = Lists.newArrayList();
            for (DocProcessAttachmentFileReq.AttFileDto attFileDto : attFileDtos) {
                MultipartFile attFile = attFileDto.getAttFile();
                UploadReq uploadReqAtt = new UploadReq();
                uploadReqAtt.setFileData(attFile);
                uploadReqAtt.setOriginName(attFile.getOriginalFilename());
                uploadReqAtt.setRequireEncrypt(true);
                UploadResp uploadResp = fileService.generalUpload(uploadReqAtt);
                Document documentDTOAtt = buildDocument(uploadResp.getId(),
                        attFile.getOriginalFilename(), RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode(),
                        req.getRecordId(), uploadResp.getMd5Hex());
                documentDTOAtt.setAttachmentType(ObjectUtil.defaultIfNull(attFileDto.getAttFileType(),
                        DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode()));
                documentDTOAtt.setClassified(attFileDto.getClassified());
                documentDTOAtt.setDocAttStatus(ATT_STATUS_AWAIT.getCode());
                documentList.add(documentDTOAtt);
            }
            saveBatch(documentList);

            TransactionSynchronizationManagerUtils.executeAfterCommit(() -> {
                if (CollUtil.isNotEmpty(documentList)) {
                    attFlowService.triggerAttFlow(req.getRecordId(), documentList, true, req.getPriorityValue());
                }
            });
        } catch (Exception e) {
            log.error("主件的附件上传失败", e);
            throw new BizException(RECORD_UPLOAD_OFFLINE_ERROR);
        }
    }

    @Override
    public Document buildDocument(Long fileId, String fileName, Integer type, Long id, Long recordId, String md5hex) {
        Document document = new Document();
        document.setId(id);
        document.setRecordId(recordId);
        document.setName(fileName);
        document.setCtype(type);
        document.setFileId(fileId);
        document.setCreateBy(SecurityUtils.getUserId());
        document.setCreateTime(new Date());
        document.setModifiedTime(new Date());
        document.setModifiedBy(SecurityUtils.getUserId());
        document.setFileMd5(md5hex);
        return document;
    }

    @Override
    public Document buildDocument(StorageInfoV3Dto storageInfoDto, UploadResp info, Integer type, Long recordId,
            String userId) {
        Document document = new Document();
        document.setId(Optional.ofNullable(storageInfoDto.getDocId()).orElse(storageInfoDto.getFileId()));
        document.setRecordId(recordId);
        document.setName(storageInfoDto.getFileName());
        document.setCtype(type);
        document.setFileId(storageInfoDto.getFileId());
        document.setFileSize(info.getFileSize());
        document.setFileMd5(info.getMd5Hex());
        document.setCreateBy(userId);
        document.setCreateTime(new Date());
        document.setModifiedTime(new Date());
        document.setModifiedBy(userId);
        return document;
    }

    @Override
    public Document buildDocument(Long fileId, String fileName, Integer type, Long recordId, String md5hex) {
        return buildDocument(fileId, fileName, type, wfgIdGenerator.next(), recordId, md5hex);
    }

    @DSTransactional
    @Override
    public void updateByIdTextDocument(DocumentUpdateTxtReq documentUpdateReq) {
        log.info("req:{}", JSON.toJSONString(documentUpdateReq));
        // txt文件 临时桶 拷贝 正式桶
        if (Objects.isNull(documentUpdateReq.getRecordId())) {
            throw new ServiceException("recordId不能为空");
        }
        //更新 txt 文件信息
        Document document = getOne(Wrappers.<Document>query().lambda()
                .eq(Document::getRecordId, documentUpdateReq.getRecordId())
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode()));
        if (Objects.nonNull(document)) {
            lambdaUpdate().set(Document::getTxtFileId, documentUpdateReq.getTxtFileId())
                    .eq(Document::getRecordId, documentUpdateReq.getRecordId())
                    .eq(Document::getId, document.getId())
                    .update();

        }
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public ReaderInfoDTO getDesensitizeRule(HttpServletRequest request, ReaderCallbackReq req) {
        //1,文档信息
        Document doc = SpringUtils.getAopProxy(this).getByIdAndRecordId(req.getId(), req.getRecordId());
        log.info("rsp document:{}", JSON.toJSONString(doc));
        AssertUtils.notNull(doc, RECORD_DOCUMENT_MASTER_NOT_EXIST);
        Long recordId = doc.getRecordId();
        ReaderInfoDTO readerInfoDTO = new ReaderInfoDTO();
        ReaderInfoDTO.ReaderInfoDetailDTO file = new ReaderInfoDTO.ReaderInfoDetailDTO();
        //文件详情-对接轻阅读
        Record record = recordService.getOneById(recordId);
        if (Objects.nonNull(record)) {
            file.setId(record.getId().toString());
            String name = FileUtil.getPrefix(doc.getName());
            // 新版存储方式
            try {
                FileTempUrlReq urlReq = new FileTempUrlReq();
                if (doc.getCtype().equals(RECORD_DOCUMENT_TYPE_MASTER.getCode())) {
                    String exportUrl = serverBasePathConfig.getSysHostPort()
                            + serverBasePathConfig.getSysHostContextPath()
                            + serverBasePathConfig.getSysApiPrefix();
                    String uploadUrl = UriComponentsBuilder.fromHttpUrl(exportUrl)
                            .path(OUT_UPLOAD_URI)
                            .queryParam("recordId", recordId).build().toUriString();
                    file.setUpload_url(uploadUrl);
                    //主件
                    if (doc.getOfdFileId() == null) {
                        file.setName(doc.getName());
                        urlReq.setFileId(doc.getFileId());
                        file.setDownload_url(fileService.tempUrl(urlReq));
                    } else {
                        file.setName(name + SymbolPool.DOT + "ofd");
                        urlReq.setFileId(doc.getOfdFileId());
                        file.setDownload_url(fileService.tempUrl(urlReq));
                    }
                } else {
                    //附件
                    file.setName(doc.getName());
                    urlReq.setFileId(doc.getFileId());
                    file.setDownload_url(fileService.tempUrl(urlReq));
                }
            } catch (Exception e) {
                log.error("获取文件地址异常！", e);
            }
            if (Objects.isNull(doc.getFileId())) {
                throw new BizException(FILEID_NOT_EXIST);
            }
            UploadResp info = fileService.getInfo(doc.getFileId());
            if (Objects.isNull(info)) {
                throw new BizException(FILE_NOT_EXIST);
            }
            file.setSize(info.getFileSize());
            file.setVersion(Integer.parseInt(readCallBackVersion));
        }
        //文件权限
        List<String> relationIds = Lists.newArrayList();
        String userId = req.getUserId();
        R<List<String>> r = userRpcService.listRelateIds(userId);
        if (!r.isSuccess()) {
            log.warn("获取用户关联id失败！userId:{}", userId);
            relationIds.add(userId);
        } else {
            relationIds.addAll(r.getData());
        }
        R<SysUser> userR = userRpcService.queryById(userId);
        Integer userClassify = UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL.getCode();
        if (userR.isSuccess()) {
            SysUser data = userR.getData();
            if (Objects.nonNull(data)) {
                userClassify = data.getClassified();
            }
        }
        file.setPermission(getPermission(record, relationIds, req.isForFront(), userClassify));
        //水印
        file.setWatermark(getWatermark(request, recordId, Objects.nonNull(req.getUserId()) ? req.getUserId() : "0"));
        //脱敏数据
        String fileTxtUrl = "";
        if (doc.getCtype().equals(RECORD_DOCUMENT_TYPE_MASTER.getCode()) && Objects.nonNull(doc.getTxtFileId())) {
            FileTempUrlReq urlReq = new FileTempUrlReq();
            urlReq.setFileId(doc.getTxtFileId());
            fileTxtUrl = fileService.tempUrl(urlReq);
        }

        readerInfoDTO.setDesensitization(
                getMaskingConfig(recordId, Objects.nonNull(req.getUserId()) ? req.getUserId() : "0",
                        fileTxtUrl, doc.getCtype().equals(RECORD_DOCUMENT_TYPE_MASTER.getCode())));

        readerInfoDTO.setFile(file);
        log.info("readerInfoDTO: {}", readerInfoDTO);

        return readerInfoDTO;
    }

    public WatermarkDTO getWatermark(HttpServletRequest request, Long recordId, String userId) {
        WatermarkDTO watermarkDTO = new WatermarkDTO();
        //todo 从文档所属的所有库中拿水印最新的那条
        LambdaQueryWrapper<RcWatermarkSetting> watermarkLqw = new LambdaQueryWrapper<>();
        watermarkLqw.orderByDesc(RcWatermarkSetting::getUpdateTime);
        List<RecordRelFolderResp> respList = folderService.queryRecordRelList(recordId);
        List<List<Long>> allFolderIdList = respList.stream().map(RecordRelFolderResp::getFolderIdList)
                .toList();
        final R<SysUser> r = userRpcService.queryById(userId);
        if (Boolean.FALSE.equals(r.isSuccess()) || r.getData() == null) {
            throw new ServiceException("获取用户信息失败");
        }
        SysUser sysUser = r.getData();
        if (CollUtil.isNotEmpty(respList)) {
            List<Long> relIdList = new ArrayList<>();
            for (List<Long> folderIdList : allFolderIdList) {
                if (CollUtil.isNotEmpty(folderIdList)) {
                    relIdList.add(folderIdList.get(0));
                }
            }
            watermarkLqw.in(RcWatermarkSetting::getRepoId, relIdList);
            watermarkLqw.eq(RcWatermarkSetting::getDelFlag, UserConstants.YSE_STATUS);
            watermarkLqw.eq(RcWatermarkSetting::getEnableStatus, UserConstants.YSE_STATUS);
            List<RcWatermarkSetting> watermarkSettings = watermarkSettingService.list(watermarkLqw);
            if (CollUtil.isNotEmpty(watermarkSettings)) {
                RcWatermarkSetting rcWatermarkSetting = watermarkSettings.get(0);
                watermarkDTO = JSONObject.parseObject(rcWatermarkSetting.getWatermarkJson(), WatermarkDTO.class);
                watermarkDTO.setValue(
                        watermarkSettingService.parseWatermarkContent(request, rcWatermarkSetting.getWatermarkType(),
                                sysUser.getNickName(), sysUser.getUserId(), rcWatermarkSetting.getValue()));
                watermarkDTO.setType(WatermarkTypeEnum.TFS_FAIL.getCode());
                return watermarkDTO;
            }
        }
        watermarkLqw.clear();
        //全局水印
        watermarkLqw.orderByDesc(RcWatermarkSetting::getUpdateTime);
        watermarkLqw.eq(RcWatermarkSetting::getRepoId, 0L);
        watermarkLqw.eq(RcWatermarkSetting::getDelFlag, UserConstants.YSE_STATUS);
        watermarkLqw.eq(RcWatermarkSetting::getEnableStatus, UserConstants.YSE_STATUS);
        List<RcWatermarkSetting> watermarkSettings = watermarkSettingService.list(watermarkLqw);
        if (CollUtil.isNotEmpty(watermarkSettings)) {
            RcWatermarkSetting rcWatermarkSetting = watermarkSettings.get(0);
            WatermarkDTO globalWatermark = JSONObject.parseObject(rcWatermarkSetting.getWatermarkJson(),
                    WatermarkDTO.class);
            if (Objects.nonNull(globalWatermark.getValue())) {
                globalWatermark.setType(WatermarkTypeEnum.TFS_FAIL.getCode());
            }
            globalWatermark.setValue(
                    watermarkSettingService.parseWatermarkContent(request, rcWatermarkSetting.getWatermarkType(),
                            sysUser.getNickName(), sysUser.getUserId(), rcWatermarkSetting.getValue()));
            return globalWatermark;
        }
        if (Objects.nonNull(watermarkDTO.getValue())) {
            watermarkDTO.setType(WatermarkTypeEnum.TFS_FAIL.getCode());
        }
        log.info("watermarkDTO: {}", watermarkDTO);
        return watermarkDTO;
    }

    @Override
    public boolean checkedDocumentUploaded(RecordDocumentCheckReq req) {
        if (serverBaseConfig.getMd5Switch()) {
            List<DocumentMd5Rel> md5RelList = documentMd5RelService.listByMd5AndCtype(req.getFile_md5(),
                    RECORD_DOCUMENT_TYPE_MASTER.getCode());
            if (CollUtil.isEmpty(md5RelList)) {
                return false;
            }
            Set<Long> recordIds = md5RelList.stream().map(DocumentMd5Rel::getRecordId).collect(Collectors.toSet());
            LambdaQueryWrapper<Record> wrapper = Wrappers.<Record>lambdaQuery()
                    .eq(Record::getStoreProcess, StoreProcessEnum.SP_WITH_IN_STORE.getCode());
            Long count = recordService.countWithWrapper(recordIds, wrapper);
            return count > 0;
        }
        return false;
    }

    @Override
    public boolean saveBatchDocumentList(List<FileInfoBaseReq> fileInfoBaseReqList, Long recordId) {
        return saveBatchDocumentList(fileInfoBaseReqList, recordId, doc->{});
    }

    @DSTransactional
    @Override
    public boolean saveBatchDocumentList(List<FileInfoBaseReq> fileInfoBaseReqList, Long recordId, Consumer<Document> docConsumer) {
        log.info("保存文件的文档信息 recordId:{}", recordId);
        AssertUtils.isTrue(CollUtil.isNotEmpty(fileInfoBaseReqList), RECORD_DOCUMENT_MASTER_NOT_EXIST);

        try {
            boolean flag = true;
            List<Document> documentList = Lists.newArrayList();
            for (FileInfoBaseReq fileInfoBaseReq : fileInfoBaseReqList) {
                //上传对象存储
                UploadResp uploadResp = Optional.ofNullable(fileInfoBaseReq.getFileId())
                        .map(o -> fileService.getInfo(o))
                        .orElseGet(() -> {
                            MultipartFile file = fileInfoBaseReq.getFile();

                            UploadReq uploadReq = new UploadReq();
                            uploadReq.setFileData(file);
                            uploadReq.setOriginName(file.getOriginalFilename());
                            uploadReq.setRequireEncrypt(true);
                            return fileService.generalUpload(uploadReq);
                        });

                documentList.add(DocumentConvertUtils.fileInfoBaseReq2Document(wfgIdGenerator.next(), recordId,
                        fileInfoBaseReq, uploadResp, SecurityUtils.getUserId()));
            }
            // 检查换件操作
            LambdaQueryWrapper<Document> wrapper = new LambdaQueryWrapper<>();
            wrapper = wrapper.eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                    .eq(Document::getRecordId, recordId);
            Document doc = getOne(wrapper);
            Document documentNew = documentList.stream()
                    .filter(o -> o.getCtype().equals(RECORD_DOCUMENT_TYPE_MASTER.getCode()))
                    .findAny().orElse(null);
            // 换件操作
            if (Objects.nonNull(doc) && Objects.nonNull(documentNew)) {
                // 产生历史记录
//                documentHistoryService.save(DocumentConvertUtils.document2DocumentHistory(doc,
//                        documentNew.getId()));
//                remove(wrapper);
                // 不更改docId
                doc.setFileId(documentNew.getFileId());
                doc.setFileSize(documentNew.getFileSize());
                doc.setFileMd5(documentNew.getFileMd5());
                doc.setName(documentNew.getName());
                doc.setModifiedTime(new Date());
                doc.setTxtFileId(null);
                doc.setOfdFileId(null);

                documentList.remove(documentNew);
                documentList.add(doc);
                // 编辑
                flag = false;
                docConsumer.accept(doc);
            }else if(Objects.isNull(doc) && Objects.nonNull(documentNew)){
                docConsumer.accept(documentNew);
            }else{
                throw new BizException(RECORD_DOCUMENT_MASTER_NOT_EXIST);
            }

            log.info("保存文档信息:{}", JSON.toJSONString(documentList));
            saveOrUpdateBatch(documentList);
            return flag;
        } catch (Exception e) {
            log.error("upload oss fail", e);
            throw new BizException(RECORD_UPLOAD_OSS_ERROR);
        }
    }

    private TuoMinDTO getMaskingConfig(Long recordId, String userId, String fileUrl, boolean isMaster) {
        TuoMinDTO tuomin = new TuoMinDTO();

        List<MaskingConfigResp> maskingConfigResps = maskingConfigService.recordInfo(recordId, userId);
        if (CollectionUtils.isEmpty(maskingConfigResps)) {
            tuomin.setEnable(false);
            return tuomin;
        }
        List<TuoMinRuleDTO> tuoMinRuleDTOS = Lists.newArrayList();
        for (MaskingConfigResp maskingConfigResp : maskingConfigResps) {
            if (CollectionUtils.isNotEmpty(maskingConfigResp.getWordList()) && maskingConfigResp.getWordStatus()
                    .equals(1)) {
                tuoMinRuleDTOS.addAll(maskingConfigResp.getWordList()
                        .stream().map(m -> {
                            TuoMinRuleDTO tuoMinRuleDTO = new TuoMinRuleDTO();
                            tuoMinRuleDTO.setRule(m.getSensitiveWordName());
                            tuoMinRuleDTO.setType(1);
                            String desc = MaskingProcessModeEnum.getEnum(maskingConfigResp.getProcessMode()).getDesc();
                            tuoMinRuleDTO.setReplace(desc);
                            return tuoMinRuleDTO;
                        }).toList());
            }
            if (CollectionUtils.isNotEmpty(maskingConfigResp.getMetaDataList()) && maskingConfigResp.getMetadataStatus()
                    .equals(1)) {
                maskingConfigResp.getMetaDataList().forEach(metaData -> {
                    if (Objects.nonNull(metaData.getMdValue())) {
                        //todo 日期格式特殊处理
                        TuoMinRuleDTO tuoMinRuleDTO = new TuoMinRuleDTO();
                        tuoMinRuleDTO.setRule(metaData.getMdValue());
                        tuoMinRuleDTO.setType(1);
                        String desc = MaskingProcessModeEnum.getEnum(maskingConfigResp.getProcessMode()).getDesc();
                        tuoMinRuleDTO.setReplace(desc);
                        tuoMinRuleDTOS.add(tuoMinRuleDTO);
                    }

                });
            }
        }
        tuomin.setEnable(true);
        List<TuoMinRuleDTO> dist = tuoMinRuleDTOS.stream().filter(distinctByKey(TuoMinRuleDTO::getRule))
                .collect(Collectors.toList());
        List<String> collect = dist.stream().map(TuoMinRuleDTO::getRule).collect(Collectors.toList());
        log.info("敏感词AC过滤前: {}", collect);
        //敏感词使用AC自动机(Aho-Corasick算法)过滤筛选，仅过滤主件
        if (isMaster && !"".equals(fileUrl)) {
            long start = System.currentTimeMillis();
            String text = getText(fileUrl);
            Trie trie = Trie.builder()
                    .addKeywords(dist.stream().map(TuoMinRuleDTO::getRule).collect(Collectors.toList())).build();
            Set<String> keywordSet = new HashSet<>();
            Collection<Emit> emits = trie.parseText(text);
            for (Emit emit : emits) {
                keywordSet.add(emit.getKeyword());
            }
            log.info("AC自动机过滤筛选耗时：{}ms", System.currentTimeMillis() - start);
            if (CollectionUtils.isEmpty(keywordSet)) {
                dist = Lists.newArrayList();
            }
            dist = dist.stream().filter(o -> keywordSet.contains(o.getRule())).toList();
            log.info("敏感词AC过滤后：{}", keywordSet);
        }

        //根据rule排序
        Comparator<TuoMinRuleDTO> compByLength = Comparator.comparingInt(o -> o.getRule().length());
        List<TuoMinRuleDTO> result = dist.stream().sorted(compByLength.reversed())
                .collect(Collectors.toList());
        tuomin.setRules(result);
        log.info("tuomin: {}", tuomin);
        return tuomin;
    }

    private String getText(String fileUrl) {
        StringBuilder text = new StringBuilder();
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            InputStream inputStream = conn.getInputStream();
            InputStreamReader read = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader bufferedReader = new BufferedReader(read);
            String oneLine;
            while ((oneLine = bufferedReader.readLine()) != null) {
                text.append(oneLine);
            }
            read.close();
            inputStream.close();
        } catch (Exception e) {
            throw new ServiceException("获取文本内容失败", e);
        }
        return text.toString();
    }


    private PermissionDTO getPermission(Record record, List<String> visitorIds, boolean forFront, Integer userClassify) {
        //文件权限
        PermissionDTO permission = PermissionDTO.builder()
                .read(1).copy(0).print(0).download(0).write(0).seal(0).build();
        // 需要拆分时给写权限
        if (standardTaskFacade.withSplitNode(record.getId())) {
            permission.setWrite(1);
        }
        // 未入库的文件或者已入库但是没有位置的给所有常规权限
        if (!Objects.equals(record.getRecordStatus(), RECORD_STATUS_PASS.getCode())
                || recordNoLocation(record.getId())) {
            permission.setCopy(1);
            permission.setDownload(1);
            permission.setPrint(1);
            permission.setRead(1);
            return permission;
        }
        PermissionOperateReq permissionOperateReq = new PermissionOperateReq();
        permissionOperateReq.setResourceId(record.getId());
        permissionOperateReq.setVisitorIds(visitorIds);
        permissionOperateReq.setSpecifyUserClassified(userClassify);
        permissionOperateReq.setResourceType(ResourceType.RECORD.getCode());
        permissionOperateReq.setForFront(forFront);
        List<PermissionOperateResp> permissionOperateResps = permissionService.listResourceOperate(
                permissionOperateReq);
        Set<PermissionMask> permissionMaskSet = permissionOperateResps.stream()
                .map(resp -> PermissionMask.splitMask(resp.getPvalue()))
                .flatMap(Collection::stream).collect(Collectors.toSet());
        if (permissionMaskSet.contains(PermissionMask.COPY)) {
            permission.setCopy(1);
        }
        if (permissionMaskSet.contains(PermissionMask.VIEW)) {
            permission.setRead(1);
        }
        if (permissionMaskSet.contains(PermissionMask.PRINT)) {
            permission.setPrint(1);
        }
        if (permissionMaskSet.contains(PermissionMask.DOWNLOAD_SAVE)) {
            permission.setDownload(1);
        }
//        if (permissionMaskSet.contains(PermissionMask.EDIT)) {
//            permission.setWrite(1);
//        }
        log.info("permission:{}", permission);
        return permission;
    }

    private boolean recordNoLocation(Long recordId) {
        return folderRecordService.countWithWrapper(new LambdaQueryWrapper<FolderRecord>()
                .eq(FolderRecord::getRecordId, recordId)) == 0;
    }

    @Override
    public boolean rename(Long recordId, String userId, String name) {
        Document entity = new Document();
        entity.setModifiedBy(userId);
        entity.setName(name);
        return lambdaUpdate()
                .eq(Document::getRecordId, recordId)
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .update(entity);
    }

    @Override
    public void saveThumbnail(SaveThumbnailReq req) {
        Document document = lambdaQuery()
                .eq(Document::getRecordId, req.getRecordId())
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode())
                .one();
        UploadResp info = fileService.getInfo(req.getThumbnailId());
        if (Objects.nonNull(document)) {
            document.setFileId(req.getThumbnailId());
            document.setFileMd5(info.getMd5Hex());
            document.setModifiedTime(new Date());
            lambdaUpdate()
                    .eq(Document::getRecordId, document.getRecordId())
                    .eq(Document::getId, document.getId())
                    .update(document);
        } else {
            Document documentNew = buildDocument(req.getThumbnailId(), "",
                    RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode(), wfgIdGenerator.next(), req.getRecordId(),
                    info.getMd5Hex());
            save(documentNew);
        }
    }

    @Override
    public DocumentAttachmentsResp queryThumbnail(Long recordId) {
        List<Document> documentList = list(Wrappers.<Document>query().lambda()
                .eq(Document::getRecordId, recordId)
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER_THUMBNAIL.getCode())
                .orderByDesc(Document::getCreateTime));
        if (CollUtil.isEmpty(documentList)) {
            return null;
        }
        Document document = documentList.get(0);
        DocumentAttachmentsResp resp = new DocumentAttachmentsResp();
        resp.setId(document.getId());
        resp.setName(document.getName());
        FileTempUrlReq urlReq = new FileTempUrlReq();
        urlReq.setFileId(document.getFileId());
        urlReq.setForFront(true);
        resp.setWebUrl(fileService.tempUrl(urlReq));
        resp.setRecordId(document.getRecordId());
        return resp;
    }

    @Override
    public PageUtils<RecordOperationResp> checkRecordAtt(RecordOperationReq req) {
        log.info("检查主件文件是否有附件:req:{}", JSON.toJSONString(req));
        List<Long> recordIds = req.getRecordIds();
        List<Record> recordList = recordService.getListByIds(recordIds);
        AssertUtils.isFalse(CollUtil.isEmpty(recordList), RECORD_DOCUMENT_MASTER_NOT_EXIST);
        AssertUtils.isFalse(recordList.size() != recordIds.size(), RECORD_DOCUMENT_MASTER_NOT_EXIST);

        LambdaQueryWrapper<Document> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Document::getCtype, RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode());
        wrapper.in(Document::getRecordId, recordIds);
        IPage<Document> page = documentMapper.selectPage(req.toPage(Document.class), wrapper);
        List<Document> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            List<RecordOperationResp> collect = recordIds.stream().map(x -> {
                RecordOperationResp resp = new RecordOperationResp();
                resp.setRecordId(x);
                resp.setAttFlag(false);
                return resp;
            }).collect(Collectors.toList());
            return new PageUtils<>(collect, page.getTotal(), page.getSize(), page.getCurrent());
        }

        Set<Long> recordIdList = records.stream()
                .map(Document::getRecordId).collect(Collectors.toSet());
        List<RecordOperationResp> collect = recordIds.stream().map(x -> {
            RecordOperationResp resp = new RecordOperationResp();
            resp.setRecordId(x);
            resp.setAttFlag(recordIdList.contains(x));
            return resp;
        }).collect(Collectors.toList());
        log.info("检查主件文件是否有附件结果:{}", JSON.toJSONString(collect));
        return new PageUtils<>(collect, page.getTotal(), page.getSize(), page.getCurrent());
    }

    @DSTransactional
    @Override
    public void uploadRecordAtt(RecordOperationAttBatchReq req) {
        log.info("上传主件与附件绑定关系:req:{}", JSON.toJSONString(req));
        // 数据安检
        List<Long> recordIds = req.getReqList().stream().map(RecordOperationRelationAttReq::getRecordId)
                .collect(Collectors.toList());
        List<Document> records = list(Wrappers.<Document>query().lambda()
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .in(Document::getRecordId, recordIds));
        AssertUtils.isFalse(CollUtil.isEmpty(records), RECORD_DOCUMENT_MASTER_NOT_EXIST);

        Set<Long> recordIdList = records.stream()
                .map(Document::getRecordId).collect(Collectors.toSet());
        for (Long recordId : recordIds) {
            AssertUtils.isFalse(!recordIdList.contains(recordId), RECORD_DOCUMENT_MASTER_NOT_EXIST,
                    "文件id:" + recordId);
        }

        List<RecordOperationRelationAttReq> reqList = req.getReqList();
        List<Document> documentAllList = Lists.newArrayList();
        for (RecordOperationRelationAttReq relationAttReq : reqList) {
            documentAllList.addAll(DocumentConvertUtils.recordOperationAttReq2Document(
                    relationAttReq.getAttList(), relationAttReq.getRecordId(), SecurityUtils.getUserId()));
        }
        log.info("上传主件与附件绑定关系:{}", JSON.toJSONString(documentAllList));
        saveBatch(documentAllList);
    }

    @Override
    public String webUrl(AttachmentOpenReq req) {
        Document doc = getByIdAndRecordId(req.getDocId(), req.getRecordId());

        if (Objects.isNull(doc)) {
            throw new BizException(RECORD_DOCUMENT_MASTER_NOT_EXIST);
        }
        String serverUrl = serverBasePathConfig.buildServerUrl(false);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverUrl)
                .path("/record/v1/readCallBack/info")
                .queryParam("docId", doc.getId())
                .queryParam("recordId", doc.getRecordId())
                .queryParam("userId", SecurityUtils.getUserId())
                .queryParam("timestamp", System.currentTimeMillis());
        if (totpUtil.isCheckSign()) {
            //生成加密签名
            String sign = totpUtil.generateMyTOTP(doc.getId().toString(), SecurityUtils.getUserId().toString());
            builder.queryParam("suwellsign", sign);
        }
        return builder.toUriString();
    }

    @Override
    public List<Document> listByRecordIds(Collection<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return List.of();
        }
        List<Document> list = Lists.newArrayList();
        for (Long recordId : recordIds) {
            list.addAll(lambdaQuery().eq(Document::getRecordId, recordId).list());
        }
        return list;
    }

    @Override
    public List<Document> listRepeatDocument(List<Document> listDocument) {
        if (CollUtil.isEmpty(listDocument)) {
            return List.of();
        }

        return listDocument.stream()
                .filter(document -> countByIdAndRecordId(document.getId(), document.getRecordId()) > 0)
                .toList();
    }

    @Override
    @DSTransactional
    public void saveBatch(List<Document> listDocument) {
        if (CollUtil.isEmpty(listDocument)) {
            return;
        }
        saveBatch(listDocument, listDocument.size());
        documentMd5RelService.saveBatchDocument(listDocument);
    }

    @Override
    public void saveOrUpdateBatch(List<Document> listDocument) {
        if (CollUtil.isEmpty(listDocument)) {
            return;
        }
        for (Document document : listDocument) {
            saveOrUpdateOne(document);
        }
    }

    @Override
    public void saveOrUpdateBatchWithoutTransaction(List<Document> listDocument) {
        if (CollectionUtil.isEmpty(listDocument)) {
            return;
        }
        Set<Long> ids = listDocument.stream().map(Document::getId).collect(Collectors.toSet());
        Set<Long> recordIds = listDocument.stream().map(Document::getRecordId).collect(Collectors.toSet());
        lambdaUpdate()
                .in(Document::getId, ids)
                .in(Document::getRecordId, recordIds)
                .remove();
        super.saveBatch(listDocument);
        documentMd5RelService.saveOrUpdateBatch(listDocument);
    }

    @Override
    public Document getMasterDocByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return null;
        }
        return lambdaQuery()
                .eq(Document::getRecordId, recordId)
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .one();
    }

    @Override
    public Document getByIdAndRecordId(Long id, Long recordId) {
        if (Objects.isNull(id) || Objects.isNull(recordId)) {
            return null;
        }
        return lambdaQuery()
                .eq(Document::getId, id)
                .eq(Document::getRecordId, recordId)
                .one();
    }

    @Override
    public Long countByIdAndRecordId(Long id, Long recordId) {
        if (Objects.isNull(id) || Objects.isNull(recordId)) {
            return 0L;
        }
        return lambdaQuery()
                .eq(Document::getId, id)
                .eq(Document::getRecordId, recordId)
                .count();
    }

    @Override
    @DSTransactional
    public void deleteByRecordIds(Collection<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        List<Document> documents = listByRecordIds(recordIds);
        if (CollUtil.isEmpty(documents)) {
            return;
        }
        documentMd5RelService.deleteRelByDocIds(documents);
        lambdaUpdate()
                .in(Document::getRecordId, recordIds)
                .remove();
    }

    @Override
    public void deleteByRecordIdsWithoutTransaction(Collection<Long> recordIds) {
        this.deleteByRecordIds(recordIds);
    }

    @Override
    @DSTransactional
    public void deleteByRecordIdsAndCtype(Collection<Long> recordIds, Integer ctype) {
        if (CollUtil.isEmpty(recordIds) || Objects.isNull(ctype)) {
            return;
        }
        List<Document> documents = listByBatchType(recordIds, ctype);
        if (CollUtil.isEmpty(documents)) {
            return;
        }
        documentMd5RelService.deleteRelByDocIds(documents);
        lambdaUpdate()
                .in(Document::getRecordId, recordIds)
                .eq(Document::getCtype, ctype)
                .remove();
    }

    @Override
    @DSTransactional
    public void deleteByRecordIdsAndDocIds(Long recordId, List<Long> docIdList) {
        if (Objects.isNull(recordId) || CollUtil.isEmpty(docIdList)) {
            return;
        }
        List<Document> documents = lambdaQuery()
                .eq(Document::getRecordId, recordId)
                .in(Document::getId, docIdList)
                .list();
        if (CollUtil.isEmpty(documents)) {
            return;
        }
        documentMd5RelService.deleteRelByDocIds(documents);
        lambdaUpdate()
                .eq(Document::getRecordId, recordId)
                .in(Document::getId, docIdList)
                .remove();
        Record byId = recordService.getOneById(recordId);
        if (RECORD_STATUS_PASS.getCode().equals(byId.getRecordStatus())) {
            operateAttESService.deleteRecordAtt(recordId, docIdList, null);
        }
    }

    @Override
    public List<Document> listByType(Long recordId, Integer type) {
        if (Objects.isNull(recordId) || Objects.isNull(type)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(Document::getRecordId, recordId)
                .eq(Document::getCtype, type)
                .list();
    }

    @Override
    public List<Document> listByTypeAndDocAttStatus(Long recordId, Integer type, List<Integer> docAttStatusList) {
        if (Objects.isNull(recordId) || Objects.isNull(type) || CollUtil.isEmpty(docAttStatusList)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(Document::getRecordId, recordId)
                .eq(Document::getCtype, type)
                .in(Document::getDocAttStatus, docAttStatusList)
                .list();
    }

    @Override
    public List<Long> listRecordIdByMd5Hex(String md5Hex, Integer type) {
        if (StringUtils.isEmpty(md5Hex) || Objects.isNull(type)) {
            return List.of();
        }
        List<DocumentMd5Rel> relList = documentMd5RelService.listByMd5AndCtype(md5Hex, type);
        return relList.stream().map(DocumentMd5Rel::getRecordId).collect(Collectors.toList());
    }

    @Override
    public List<Document> listByBatchType(Collection<Long> recordIds, Integer... type) {
        if (CollUtil.isEmpty(recordIds) || type.length == 0) {
            return List.of();
        }
        return lambdaQuery()
                .in(Document::getRecordId, recordIds)
                .in(Document::getCtype, Arrays.asList(type))
                .list();
    }

    @Override
    public Long countByBatchType(Long recordId, Integer... type) {
        if (Objects.isNull(recordId) || type.length == 0) {
            return 0L;
        }
        return lambdaQuery()
                .eq(Document::getRecordId, recordId)
                .in(Document::getCtype, Arrays.asList(type))
                .count();
    }

    @Override
    @DSTransactional
    public void saveOrUpdateOne(Document document) {
        if (Objects.isNull(document)) {
            return;
        }
        if (Objects.nonNull(document.getId())) {
            lambdaUpdate()
                    .eq(Document::getId, document.getId())
                    .eq(Document::getRecordId, document.getRecordId())
                    .remove();
        }
        save(document);
        documentMd5RelService.replaceOneDocument(document);
    }

    @Override
    public Set<String> listMasterMD5ByRecordIds(Collection<Long> recordIds, Integer storeProcess) {
        if (CollUtil.isEmpty(recordIds)) {
            return Set.of();
        }
        List<Document> documents = lambdaQuery()
                .select(Document::getFileMd5)
                .in(Document::getRecordId, recordIds)
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .list();
        Set<String> fileMd5s = documents.stream().map(Document::getFileMd5).collect(Collectors.toSet());
        List<DocumentMd5Rel> relList = documentMd5RelService.listByMd5sAndCtype(fileMd5s,
                RECORD_DOCUMENT_TYPE_MASTER.getCode());
        if (CollUtil.isEmpty(relList)) {
            return Set.of();
        }
        Set<Long> existsRecordIds = relList.stream().map(DocumentMd5Rel::getRecordId).collect(Collectors.toSet());
        LambdaQueryWrapper<Record> wrapper = Wrappers.<Record>lambdaQuery()
                .select(Record::getId)
                .eq(Record::getStoreProcess, storeProcess);
        List<Long> recordIdList = recordService.listWithWrapper(existsRecordIds, wrapper)
                .stream().map(Record::getId).toList();
        if (CollUtil.isEmpty(recordIdList)) {
            return Set.of();
        }
        List<Document> targetDocuments = lambdaQuery()
                .select(Document::getFileMd5)
                .in(Document::getRecordId, recordIdList)
                .eq(Document::getCtype, RECORD_DOCUMENT_TYPE_MASTER.getCode())
                .list();
        return targetDocuments.stream().map(Document::getFileMd5).collect(Collectors.toSet());
    }

    @Override
    public List<RecordFrontDto> queryDocByRecordIds(Collection<Long> recordIds) {
        if (CollUtil.isEmpty(recordIds)) {
            return List.of();
        }
        return documentMapper.queryDocByRecordIds(recordIds);
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void migrateDoc2Shard() {
        Long count = lambdaQuery().count();
        if (count == 0) {
            return;
        }
        log.info("开始迁移document数据至分表");
        int pageSize = 2000;
        long pages = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
        ExecutorService executors = Executors.newFixedThreadPool(32);
        ExecutorService finalExecutors = TtlExecutors.getTtlExecutorService(executors);
        DocumentServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        CompletableFuture[] array = LongStream.range(0, pages + 1).mapToObj(i -> CompletableFuture.runAsync(() -> {
            List<Document> documents = lambdaQuery()
                    .orderByAsc(Document::getId)
                    .last("limit " + pageSize + " offset " + pageSize * i)
                    .list();
            if (CollUtil.isEmpty(documents)) {
                return;
            }
            // 填充文件md5
            fillMd5Hex(documents);
            List<Long> docIds = documents.stream().map(Document::getId).toList();
            Set<Long> recordIds = documents.stream().map(Document::getRecordId).collect(Collectors.toSet());
            aopProxy.remove(Wrappers.<Document>query().lambda()
                    .in(Document::getId, docIds)
                    .in(Document::getRecordId, recordIds));
            documentMd5RelService.deleteRelByDocIds(documents);
            aopProxy.saveBatch(documents, documents.size());
            documentMd5RelService.saveBatchDocument(documents);
        }, finalExecutors)).toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(array).join();
        log.info("迁移document数据至分表完成，总数：{}", count);
    }

    @Override
    public List<Document> batchDocument(Collection<Long> recordIds, Collection<Long> docIds) {
        if (CollUtil.isEmpty(recordIds) || CollUtil.isEmpty(docIds)) {
            return List.of();
        }
        return lambdaQuery()
                .in(Document::getRecordId, recordIds)
                .in(Document::getId, docIds)
                .list();
    }

    private void fillMd5Hex(List<Document> documents) {
        Set<Long> fileIds = documents.stream().filter(doc -> StringUtils.isEmpty(doc.getFileMd5()))
                .map(Document::getFileId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(fileIds)) {
            return;
        }
        log.info("迁移document数据至分表，找到md5为空的文件有：{} 个", fileIds.size());
        List<FileRecord> fileRecords = fileService.batchList(fileIds);
        Map<Long, String> fileRecordMap = fileRecords.stream()
                .collect(Collectors.toMap(FileRecord::getId, FileRecord::getMd5Hex));
        documents.forEach(doc -> {
            if (StringUtils.isEmpty(doc.getFileMd5())) {
                doc.setFileMd5(fileRecordMap.get(doc.getFileId()));
            }
        });
    }

    @Override
    public boolean saveOrUpdateBatch(List<Document> entityList, boolean isUpdate) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }
        entityList.forEach(entity -> {
            if (Objects.isNull(entity.getFileMd5())) {
                entity.setFileMd5(entity.getId().toString());
            }
            if (Objects.equals(entity.getCtype(), RECORD_DOCUMENT_TYPE_MASTER.getCode())) {
                deleteByRecordIdsAndCtype(List.of(entity.getRecordId()), RECORD_DOCUMENT_TYPE_MASTER.getCode());
            }
            if (isUpdate) {
                saveOrUpdateOne(entity);
                return;
            }
            Document document = getByIdAndRecordId(entity.getId(), entity.getRecordId());
            if (Objects.nonNull(document)) {
                return;
            }
            saveOrUpdateOne(entity);
        });

        return true;
    }

    @Override
    public void updateDocumentBaseInfo(DocumentUpdateCallbackReq req, Integer docAttStatus) {
        lambdaUpdate().set(Objects.nonNull(req.getOfdFileId()), Document::getOfdFileId, req.getOfdFileId())
                .set(Objects.nonNull(req.getTxtFileId()), Document::getTxtFileId, req.getTxtFileId())
                .set(Objects.nonNull(docAttStatus), Document::getDocAttStatus, docAttStatus)
                .set(Document::getModifiedTime, new Date())
                .eq(Document::getRecordId, req.getRecordId())
                .eq(Document::getId, req.getDocId())
                .eq(Document::getCtype, req.getCtype())
                .update();
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void reHashDoc() {
        int pageSize = 2000;
        DocumentServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        for (int i = 0; i < 20; i++) {
            documentMapper.clearMd5Rel(i);
        }
        for (int i = 0; i < 40; i++) {
            int totalCount = documentMapper.totalCount(i);
            if (totalCount == 0) {
                continue;
            }
            int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
            Long lastId = Long.MAX_VALUE;
            for (int j = 0; j < totalPage; j++) {
                List<Document> list = documentMapper.pageList(i, pageSize, lastId);
                lastId = list.get(list.size() - 1).getId();
                List<Long> ids = list.stream().map(Document::getId).toList();
                documentMapper.delBatch(i, ids);
                aopProxy.saveOrUpdateBatchWithoutTransaction(list);
            }
        }
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void removeOldShardingHisData() {
        for (int i = 0; i < 40; i++) {
            Long lastId = Long.MAX_VALUE;
            while (true) {
                List<Document> list = documentMapper.pageList(i, 2000, lastId);
                if (CollectionUtil.isEmpty(list)) {
                    break;
                }
                lastId = list.get(list.size() - 1).getId();
                int finalI = i;
                List<Long> ids = list.stream()
                        .filter(id -> Math.abs(id.getRecordId().hashCode()) % 40 != finalI)
                        .map(Document::getId).toList();
                if (CollectionUtil.isNotEmpty(ids)) {
                    documentMapper.delBatch(i, ids);
                }
            }
        }
    }

}