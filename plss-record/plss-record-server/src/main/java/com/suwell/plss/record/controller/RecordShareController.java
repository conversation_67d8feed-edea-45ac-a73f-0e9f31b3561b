package com.suwell.plss.record.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.RecordShareAddReq;
import com.suwell.plss.record.standard.dto.request.RecordShareReq;
import com.suwell.plss.record.standard.dto.response.RecordShareResp;
import com.suwell.plss.record.standard.service.StandardRecordShareFacade;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 文件分享
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-06 10:26:27
 */
@RestController
@RequestMapping("v1/recordshare")
public class RecordShareController {
    public static final String MODE_NAME = "'前台-文档操作'";

    @Resource
    private StandardRecordShareFacade recordShareFacade;

    /**
     * 保存
     */
    @Log(title = MODE_NAME, businessType = BusinessType.DOCUMENT_SHARE,remark = "#recordShareReq.getLogRemark()")
    @PostMapping("/add")
    public R<Void> add(@RequestBody RecordShareAddReq recordShareReq) {
        recordShareFacade.add(recordShareReq);
        return R.ok();
    }

    /**
     * 我的分享
     */
    @PostMapping("/myShare")
    public R<Page<RecordShareResp>> myShare(@RequestBody RecordShareReq recordShareReq) {
        return R.ok(recordShareFacade.myShare(recordShareReq));
    }

    /**
     * 分享给我
     */
    @PostMapping("/shareWithMe")
    public R<Page<RecordShareResp>> shareWithMe(@RequestBody RecordShareReq recordShareReq) {
        return R.ok(recordShareFacade.shareWithMe(recordShareReq));
    }


}
