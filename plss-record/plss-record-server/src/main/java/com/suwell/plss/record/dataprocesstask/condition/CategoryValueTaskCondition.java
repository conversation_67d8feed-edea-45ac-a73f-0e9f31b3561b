package com.suwell.plss.record.dataprocesstask.condition;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.suwell.plss.framework.common.config.PlssFixedMetadataNameConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.dataprocesstask.AbstractTaskCondition;
import com.suwell.plss.record.entity.Metadata;
import com.suwell.plss.record.entity.Record;
import com.suwell.plss.record.enums.TaskConditionEnums;
import com.suwell.plss.record.service.MetadataService;
import com.suwell.plss.record.service.RecordMetadataValueService;
import com.suwell.plss.record.service.RecordService;
import com.suwell.plss.record.standard.dto.request.RecordEditMetadataReq;
import com.suwell.plss.record.standard.dto.response.MetaDataValueInfoResp;
import com.suwell.plss.record.standard.service.StandardRecordFacade;
import com.suwell.plss.search.api.http.SearchRpcEntrance;
import com.suwell.plss.search.standard.dto.request.newsSearch.BackendSearchReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.MetadataListQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchSortOption;
import com.suwell.plss.system.api.domain.SysCategoryDTO;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.service.CategoryRpcService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/12/25
 * @content
 */
@Slf4j
@Service
public class CategoryValueTaskCondition extends AbstractTaskCondition {

    @Resource
    private SearchRpcEntrance searchRpcEntrance;
    @Resource
    private MetadataService metadataService;
    @Resource
    private StandardRecordFacade recordFacade;
    @Resource
    private RecordService recordService;
    @Resource
    private CategoryRpcService categoryRpcService;
    @Resource
    private RecordMetadataValueService recordMetadataValueService;
    @Resource
    private PlssFixedMetadataNameConfig plssFixedMetadataNameConfig;

    private static final String TIMELINESS_METADATA_NAME = "时效性";
    private static final String IMPLEMENTATION_DATE_METADATA_NAME = "施行日期";
    public static final String SWSX = "尚未生效";
    private static final TransmittableThreadLocal<BackendSearchReq> SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL = new TransmittableThreadLocal<>();
    private static final TransmittableThreadLocal<List<Long>> RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL = new TransmittableThreadLocal<>();
    private static final TransmittableThreadLocal<Long[]> CATEGORY_ID_TRANSMITTABLE_THREAD_LOCAL = new TransmittableThreadLocal<>();

    @Override
    public TaskConditionEnums.ConditionTypeEnum getConditionType() {
        return TaskConditionEnums.ConditionTypeEnum.CT_CATEGORY_VALUE_RATIONALITY;
    }

    private void setSortOptions(BackendSearchReq searchReq){
        SearchSortOption sortOption = new SearchSortOption();
        sortOption.setMetadataSort(false);
        sortOption.setSortField("recordId");
        sortOption.setSortType("asc");
        searchReq.setSortOptions(CollUtil.newArrayList(sortOption));
    }
    private void setMetadataList(BackendSearchReq searchReq,Map<String, Long> metadataMap){
        List<MetadataListQueryItem> list = new ArrayList<>();
        MetadataListQueryItem one = new MetadataListQueryItem();
        one.setMetadataId(metadataMap.get(IMPLEMENTATION_DATE_METADATA_NAME));
        one.setMetadataValue(CollUtil.newArrayList("~"+DateUtil.format(new Date(), "yyyy-MM-dd")));
        list.add(one);
        searchReq.setMetadataList(list);
    }
    private BackendSearchReq createSearchReq(Map<String, Long> metadataMap){
        BackendSearchReq searchReq = new BackendSearchReq();
        setSortOptions(searchReq);
        setMetadataList(searchReq, metadataMap);
        searchReq.setRecordIdStart(0L).setPage(1).setPageSize(100);
        return searchReq;
    }

    @Override
    public Long getTotalCount(String paramJson) {
        return queryData(paramJson, (metadataMap, flag) -> {
            if(!flag){
                return 0L;
            }
            R<PageUtils<Long>> pageR = searchRpcEntrance.backendSearch(createSearchReq(metadataMap));
            return Optional.ofNullable(pageR.getData()).map(PageUtils::getTotalCount).orElse(0L);
        });
    }

    @Override
    public void init(String paramJson,Integer batchSize) {
        BackendSearchReq searchReq = queryData(paramJson,(metadataMap,flag)->{
            if(!flag){
                return null;
            }
            return createSearchReq(metadataMap);
        });
        if (searchReq != null){
            searchReq.setPageSize(batchSize);
            // 查询分类id
            boolean flag = true;
            SysCategoryDTO categoryDto = new SysCategoryDTO();
            R<List<SysCategory>> listR = categoryRpcService.queryChildrenById(categoryDto);
            if (listR.isSuccess()) {
                List<SysCategory> data = listR.getData();
                if(CollUtil.isEmpty(data)){
                    flag = false;
                }else{
                    Long id = data.stream().filter(o -> Objects.equals(o.getName(), TIMELINESS_METADATA_NAME)).map(SysCategory::getId).findFirst().orElseThrow();
                    categoryDto = new SysCategoryDTO();
                    categoryDto.setId(id);
                    listR = categoryRpcService.queryChildrenById(categoryDto);
                    Long sourceId = listR.getData().stream().filter(o -> Objects.equals(o.getName(), SWSX)).map(SysCategory::getId).findFirst().orElseThrow();
                    Long targetId = listR.getData().stream().filter(o -> Objects.equals(o.getName(), "有效")).map(SysCategory::getId).findFirst().orElseThrow();
                    CATEGORY_ID_TRANSMITTABLE_THREAD_LOCAL.set(new Long[]{sourceId,targetId});
                    log.info("分类id查询成功,sourceId={},targetId={}", sourceId, targetId);
                    searchReq.setCategoryIds(CollUtil.newArrayList(sourceId));
                }
            }else{
                flag = false;
                log.error("查询分类失败");
            }

            if(!flag){
                throw new RuntimeException("定时更新元数据值，查询分类失败");
            }
            SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.set(searchReq);
        }
    }

    @Override
    public void finish() {
        SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.remove();
        RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.remove();
        CATEGORY_ID_TRANSMITTABLE_THREAD_LOCAL.remove();
    }

    @Override
    public void startLog(Consumer<List<Long>> consumer) {
        consumer.accept(RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.get());
    }

    @Override
    public void finishLog(Function<List<Long>,Boolean> func) {
        if(func.apply(RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.get())){
            SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.remove();
        }
    }

    @Override
    public void breakLog(Consumer<List<Long>> consumer) {
        consumer.accept(RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.get());
        SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.remove();
    }

    private <K> K queryData(String paramJson, BiFunction<Map<String, Long>,Boolean,K> function){
        List<Metadata> metadataList = metadataService.lambdaQuery()
                .select(Metadata::getId, Metadata::getName)
                .in(Metadata::getName, CollUtil.newArrayList(TIMELINESS_METADATA_NAME, IMPLEMENTATION_DATE_METADATA_NAME))
                .list();
        if(CollUtil.isEmpty(metadataList)){
            log.info("元数据不存在");
            return function.apply(null,false);
        }
        if (metadataList.size()!=2){
            log.info("元数据不完整");
            return function.apply(null,false);
        }
        return function.apply(metadataList.stream().collect(Collectors.toMap(Metadata::getName, Metadata::getId)),true);
    }

    @Override
    public List<Long> queryData(String paramJson) {
        return RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.get();
    }

    @Override
    public boolean hasNext() {
        if(SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.get() == null){
            return false;
        }

        BackendSearchReq searchReq = SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.get();
        R<PageUtils<Long>> backendSearch = searchRpcEntrance.backendSearch(searchReq);
        PageUtils<Long> data = backendSearch.getData();
        List<Long> dataList = Optional.ofNullable(data).map(PageUtils::getList).orElseGet(ArrayList::new);
        if(CollUtil.isEmpty(dataList)){
            return false;
        }else{
            RECORD_ID_LIST_TRANSMITTABLE_THREAD_LOCAL.set(dataList);
            return true;
        }
    }

    @Override
    public void updateData(List<Long> recordIdList) {
        Map<String,String> map = new HashMap<>();
        map.put(TIMELINESS_METADATA_NAME, "有效");

        for (Long recordId : recordIdList) {
            Record record = recordService.getOneById(recordId);
            if(record == null){
                log.info("文件不存在");
                continue;
            }
            map.remove(plssFixedMetadataNameConfig.getFixedSysCategoryName());
            RecordEditMetadataReq req = new RecordEditMetadataReq();
            req.setRecordId(recordId);
            req.setRecordTypeId(record.getRecordtypeId());

            // 修改分类标签
            Long[] idArr = CATEGORY_ID_TRANSMITTABLE_THREAD_LOCAL.get();
            List<MetaDataValueInfoResp> infoResps = recordMetadataValueService.queryRecordTypeMetaDataValue(recordId, record.getRecordtypeId());
            if(CollUtil.isNotEmpty(infoResps)){
                infoResps.stream().filter(o->plssFixedMetadataNameConfig.getFixedSysCategoryName().equals(o.getMdName()))
                        .findFirst().ifPresent(o->{
                            String mdValue = o.getMdValue();
                            if(StrUtil.isNotBlank(mdValue)){
                                StringJoiner leafList = new StringJoiner(",");
                                String[] leafArr = mdValue.split(",");
                                for (String leaf : leafArr) {
                                    leafList.add(replaceLeaf(leaf, idArr[0], idArr[1]));
                                }
                                map.put(plssFixedMetadataNameConfig.getFixedSysCategoryName(), leafList.toString());
                            }
                        });
            }
            req.setMetadata(map);
            recordFacade.modifyRecordMetadataValue(req);
        }

        BackendSearchReq searchReq = SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.get();
        searchReq.setPage(searchReq.getPage()+1);
        searchReq.setRecordIdStart(recordIdList.get(recordIdList.size()-1));
        SEARCH_REQ_TRANSMITTABLE_THREAD_LOCAL.set(searchReq);
    }

    private String replaceLeaf(String sourceId1,Long sourceId2,Long target){
        if(Long.parseLong(sourceId1) == sourceId2){
            return target.toString();
        }
        return sourceId1;
    }
}
