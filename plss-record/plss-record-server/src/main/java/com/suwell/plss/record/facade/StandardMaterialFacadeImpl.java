package com.suwell.plss.record.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.convert.MaterialCategoryConvertUtils;
import com.suwell.plss.record.convert.MaterialChangeConvertUtils;
import com.suwell.plss.record.convert.MaterialConvertUtils;
import com.suwell.plss.record.convert.MaterialQuoteConverUtils;
import com.suwell.plss.record.domain.ResourceChainDTO;
import com.suwell.plss.record.entity.Material;
import com.suwell.plss.record.entity.MaterialCategory;
import com.suwell.plss.record.entity.MaterialQuote;
import com.suwell.plss.record.permission.factory.PermissionValidatorFactory;
import com.suwell.plss.record.service.MaterialCategoryService;
import com.suwell.plss.record.service.MaterialChangeService;
import com.suwell.plss.record.service.MaterialQuoteService;
import com.suwell.plss.record.service.MaterialService;
import com.suwell.plss.record.service.RepositoryService;
import com.suwell.plss.record.standard.dto.request.MaterialCategoryAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialCategoryMaterialAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeCategoryEditReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeListReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeRepAnalyzeReq;
import com.suwell.plss.record.standard.dto.request.MaterialQueryReq;
import com.suwell.plss.record.standard.dto.request.MaterialQuoteAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialQuoteListReq;
import com.suwell.plss.record.standard.dto.request.QueryOrAutoNewRepoReq;
import com.suwell.plss.record.standard.dto.response.MaterialCategoryInfoResp;
import com.suwell.plss.record.standard.dto.response.MaterialCategoryResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeCategoryResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeInfoResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeRepAnalyzeResp;
import com.suwell.plss.record.standard.dto.response.MaterialQuoteInfoResp;
import com.suwell.plss.record.standard.dto.response.RepositoryResp;
import com.suwell.plss.record.standard.enums.MaterialCategoryEnum.CategoryTypeEnum;
import com.suwell.plss.record.standard.enums.MaterialChangeEnum.ReasonTypeEnum;
import com.suwell.plss.record.standard.enums.MaterialEnum.FromTypeEnum;
import com.suwell.plss.record.standard.enums.MaterialEnum.MaterialTypeEnum;
import com.suwell.plss.record.standard.enums.RepositoryEnum.CType;
import com.suwell.plss.record.standard.service.StandardMaterialFacade;
import com.suwell.plss.record.standard.service.StandardRepositoryFacade;
import com.suwell.plss.system.api.domain.SysCategoryDTO;
import com.suwell.plss.system.api.entity.SysCategory;
import com.suwell.plss.system.api.entity.SysMessage;
import com.suwell.plss.system.api.service.CategoryRpcService;
import com.suwell.plss.system.api.service.ConfigRpcService;
import com.suwell.plss.system.api.service.MessageRpcService;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/11/25
 */
@Slf4j
@Service
public class StandardMaterialFacadeImpl implements StandardMaterialFacade {

    //素材分类
    private final String material_classify_categoryId = "material.classify.categoryId";
    //素材标签
    private final String material_tag_categoryId = "material.tag.categoryId";

    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialCategoryService materialCategoryService;
    @Resource
    private MaterialChangeService materialChangeService;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private PermissionValidatorFactory validatorFactory;
    @Resource
    private StandardRepositoryFacade standardRepositoryFacade;
    @Resource
    private MaterialQuoteService materialQuoteService;
    @Resource
    private MessageRpcService messageRpcService;
    @Resource
    private RepositoryService repositoryService;

    @Resource
    private CategoryRpcService categoryRpcService;

    @Resource
    private ConfigRpcService configRpcService;


    @Override
    public PageUtils<MaterialCategoryInfoResp> queryPage(MaterialQueryReq req) {
        log.info("user account:{},req:{}", SecurityUtils.getUsername(), JSON.toJSONString(req));
        BeanValidators.defaultValidate(req);

        PageUtils<MaterialCategoryInfoResp> pageUtils = materialService.queryPage(req);
        log.info("rsp size:{}", pageUtils.getList().size());
        return pageUtils;
    }

    @Override
    public MaterialChangeCategoryResp queryMaterialById(Long materialId) {
        log.info("查询变更详情,素材id req:{}", materialId);
        Material material = materialService.getById(materialId);
        MaterialChangeCategoryResp materialChangeCategoryResp = new MaterialChangeCategoryResp();
        materialChangeCategoryResp.setMaterialId(material.getId());
        materialChangeCategoryResp.setMaterialType(material.getMaterialType());
        materialChangeCategoryResp.setContent(material.getContent());
        materialChangeCategoryResp.setReason(material.getReason());
        materialChangeCategoryResp.setReasonType(material.getReasonType());

        List<MaterialCategory> list = materialCategoryService.list(Wrappers.<MaterialCategory>query().lambda()
                .eq(MaterialCategory::getMaterialId, materialId));
        if (CollUtil.isNotEmpty(list)) {
            //返回父id
            List<Long> categoryId = list.stream().map(MaterialCategory::getCategoryId).collect(Collectors.toList());
            Map<Long, Long> AncestorIdByDescendantIds = categoryRpcService.queryAncestorIdByDescendantIds(categoryId);

            List<MaterialCategoryResp> collect = list.stream().map(x -> {
                MaterialCategoryResp materialCategoryResp = new MaterialCategoryResp();
                materialCategoryResp.setCategoryId(x.getCategoryId());
                materialCategoryResp.setCategoryName(x.getCategoryName());
                materialCategoryResp.setCategoryType(x.getCategoryType());
                materialCategoryResp.setParentId(AncestorIdByDescendantIds.get(x.getCategoryId()));
                return materialCategoryResp;
            }).collect(Collectors.toList());
            materialChangeCategoryResp.setMaterialCategoryRespList(collect);
        }
        /*// 变更原因
        MaterialChange materialChange = materialChangeService.getOne(Wrappers.<MaterialChange>query().lambda()
                .eq(MaterialChange::getMaterialId, materialId).orderByDesc(MaterialChange::getModifiedTime)
                .last(" LIMIT 1"));
        if (Objects.nonNull(materialChange)) {
            materialChangeCategoryResp.setReasonType(materialChange.getReasonType());
            materialChangeCategoryResp.setReason(materialChange.getReason());
        }*/
        log.info("rsp :{}", JSON.toJSONString(materialChangeCategoryResp));
        return materialChangeCategoryResp;
    }

    @DSTransactional
    @Override
    public Map<Integer, Long> addMaterial(MaterialCategoryMaterialAddReq req) {
        log.info("添加素材 req:{}", JSON.toJSONString(req));
        List<Integer> materialType = req.getMaterialAddReq().getMaterialType();
        Integer fromType = req.getMaterialAddReq().getFromType();
        HashMap<Integer, Long> returnMap = new HashMap<>();
        for (Integer type : materialType) {
            Long materialId = wfgIdGenerator.next();
            // fromType=2时，设置所属库信息
            if (fromType.equals(FromTypeEnum.FILE_ADD.getCode())) {
                //如果我的素材，则查询当前人得个人库信息
                Long repoId = 0L;
                if (type.equals(MaterialTypeEnum.ONESELF_MATERIAL.getCode())) {
                    QueryOrAutoNewRepoReq repoReq = new QueryOrAutoNewRepoReq();
                    repoReq.setCtype(CType.PERSONAL.getCode());
                    repoId = repositoryService.queryRepoIdOrSave(repoReq);
                } else {
                    //非我的素材则根据文件信息反查库得信息
                    Long recordId = req.getMaterialAddReq().getRelationId();
                    List<ResourceChainDTO> resourceChainDTOS = validatorFactory.getValidator(ResourceType.RECORD)
                            .listResourceChains(recordId);
                    if (CollectionUtil.isNotEmpty(resourceChainDTOS)) {
                        ResourceChainDTO resourceChainDTO = resourceChainDTOS.get(0);
                        repoId = resourceChainDTO.getRepoId();
                    }
                }
                RepositoryResp repositoryResp = standardRepositoryFacade.queryById(repoId);
                if (Objects.nonNull(repositoryResp)) {
                    req.getMaterialAddReq().setRepId(repositoryResp.getId());
                    req.getMaterialAddReq().setRepName(repositoryResp.getName());
                }

            } else {
                if (req.getMaterialAddReq().getRepId() != null && StringUtils.isEmpty(
                        req.getMaterialAddReq().getRepName())) {
                    RepositoryResp repositoryResp = standardRepositoryFacade.queryById(
                            req.getMaterialAddReq().getRepId());
                    if (Objects.nonNull(repositoryResp)) {
                        req.getMaterialAddReq().setRepId(repositoryResp.getId());
                        req.getMaterialAddReq().setRepName(repositoryResp.getName());
                    }
                }

            }

            Material material = MaterialConvertUtils.materialAddReq2Material(req.getMaterialAddReq(),
                    materialId, SecurityUtils.getLoginUser(), type);

            materialService.save(material);

            // 素材分类
            List<MaterialCategoryAddReq> materialCategoryAddReqs = analyCategory(req.getCategoryAddReqList());
            materialCategoryService.saveBatch(
                    MaterialCategoryConvertUtils.materialCategoryAddReqList2MaterialCategoryList(
                            materialCategoryAddReqs, materialId));

            returnMap.put(type, materialId);
        }

        return returnMap;

    }

    /**
     * 筛选出id是空的标签，进行新增
     */
    private List<MaterialCategoryAddReq> analyCategory(List<MaterialCategoryAddReq> categoryAddReqList) {
        ArrayList<MaterialCategoryAddReq> categoryList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(categoryAddReqList)) {

            Long tagId = null;
            R<String> configValue = configRpcService.getConfigValue(this.material_tag_categoryId);
            if (configValue.isSuccess() && StringUtils.isNotBlank(configValue.getData())) {
                tagId = Long.parseLong(configValue.getData());
                for (MaterialCategoryAddReq mca : categoryAddReqList) {
                    if (CategoryTypeEnum.CLASSIFY.getCode().intValue() == mca.getCategoryType()
                            .intValue()) {//排除掉分类，只留标签
                        categoryList.add(mca);
                        continue;
                    }
                    //如果传了父id，就用传的，没传就用默认素材标签的根id
                    if (mca.getParentId() != null) {
                        tagId = mca.getParentId();
                    }

                    R<Long> byName = categoryRpcService.getByName(tagId, mca.getCategoryName());
                    if (byName != null && byName.getData() != null) {
                        mca.setCategoryId(byName.getData());
                        categoryList.add(mca);
                    } else {
                        R<Long> add = categoryRpcService.addPersonal(tagId, mca.getCategoryName());
                        MaterialCategoryAddReq materialCategoryAddReq = new MaterialCategoryAddReq();
                        materialCategoryAddReq.setCategoryId(add.getData());
                        materialCategoryAddReq.setCategoryName(mca.getCategoryName());
                        materialCategoryAddReq.setCategoryType(CategoryTypeEnum.LABEL.getCode());
                        categoryList.add(materialCategoryAddReq);
                    }
                }
            } else {
                log.error("系统配置缺少素材标签配置参数：mmaterial.tag.categoryId");
                throw new RuntimeException("系统配置缺少素材标签配置参数：mmaterial.tag.categoryId");
            }
        }
        return categoryList;
    }

    @DSTransactional
    @Override
    public void modifyMaterial(MaterialChangeCategoryEditReq req) {
        String userId = SecurityUtils.getUserId();

        MaterialChangeAddReq materialChangeAddReq = req.getMaterialChangeAddReq();
        Material material = new Material();
        material.setId(req.getMaterialId());
        material.setContent(materialChangeAddReq.getContentAfter());
        material.setModifiedTime(new Date());
        material.setReasonType(ObjectUtils.isNotEmpty(materialChangeAddReq.getReasonType())?materialChangeAddReq.getReasonType():ReasonTypeEnum.OTHER.getCode());
        material.setName(req.getName());
        if (ReasonTypeEnum.OTHER.getCode().equals(materialChangeAddReq.getReasonType())) {
            material.setReason(materialChangeAddReq.getReason());
        } else {
            if(ObjectUtils.isNotEmpty(materialChangeAddReq.getReasonType())){
                ReasonTypeEnum anEnum = ReasonTypeEnum.getEnum(materialChangeAddReq.getReasonType());
                material.setReason(anEnum.getDesc());
            }
        }
        materialService.updateById(material);

        // 素材变更记录:和前端约定好了，‘素材内容’有变更的时候contentAfter才传值，否则传null。只有内容发生变更的时候才插入变更记录
        if (StringUtils.isNotEmpty(req.getMaterialChangeAddReq().getContentAfter())) {
            materialChangeService.save(
                    MaterialChangeConvertUtils.materialChangeAddReq2MaterialChange(materialChangeAddReq,
                            req.getMaterialId(), SecurityUtils.getLoginUser()));
            //如果是项目素材，发送变更通知消息
            Material byId = materialService.getById(req.getMaterialId());
            if (byId.getMaterialType().equals(MaterialTypeEnum.PROJECT_MATERIAL.getCode())) {
                //查询出该素材被引用的信息，所有引用的人都要发送消息
                List<MaterialQuote> materialQuoteList = materialQuoteService.list(
                        Wrappers.<MaterialQuote>query().lambda().eq(MaterialQuote::getMaterialId, byId.getId()));
                if (CollectionUtil.isNotEmpty(materialQuoteList)) {
                    List<SysMessage> sysmessageList = materialQuoteList.stream().map(materialQuote -> {
                        SysMessage sysMessage = new SysMessage();
                        sysMessage.setType(1);//1变更提醒；2协作提醒
                        String baseName = FilenameUtils.getBaseName(materialQuote.getRecordName());
                        sysMessage.setTitle(byId.getRepName() + "《" + baseName + "》");
                        sysMessage.setSendPersonId(userId);
                        sysMessage.setAcceptPersonId(materialQuote.getCreateBy());
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("contentBefore", materialChangeAddReq.getContentBefore());
                        jsonObject.put("contentAfter", materialChangeAddReq.getContentAfter());
                        sysMessage.setContent(jsonObject.toJSONString());
                        return sysMessage;
                    }).collect(Collectors.toList());
                    messageRpcService.batchSaveMessage(sysmessageList);
                }

            }
        }

        // 先干掉素材分类关系,重新添加
        materialCategoryService.remove(Wrappers.<MaterialCategory>query().lambda()
                .eq(MaterialCategory::getMaterialId, req.getMaterialId()));
        List<MaterialCategoryAddReq> materialCategoryAddReqs = analyCategory(req.getCategoryAddReqList());
        materialCategoryService.saveBatch(MaterialCategoryConvertUtils.materialCategoryAddReqList2MaterialCategoryList(
                materialCategoryAddReqs, req.getMaterialId()));

    }

    @DSTransactional
    @Override
    public void removeMaterialById(Long materialId) {
        Material material = new Material();
        material.setId(materialId);
        material.setDelFlag(NormalState.FORBIDDEN.getCode());
        materialService.updateById(material);
    }

    @Override
    public Long addMaterialQuote(MaterialQuoteAddReq materialQuoteAddReq) {

        Long id = wfgIdGenerator.next();

        MaterialQuote materialQuote = MaterialQuoteConverUtils.materialQuoteAddReq2MaterialQuote(materialQuoteAddReq,
                id, SecurityUtils.getLoginUser());
        materialQuoteService.save(materialQuote);
        return id;

    }

    @Override
    public PageUtils<MaterialQuoteInfoResp> queryMaterialQuotePage(MaterialQuoteListReq req) {
        PageUtils<MaterialQuoteInfoResp> materialQuoteInfoRespPageUtils = materialQuoteService.queryPage(req);
        return materialQuoteInfoRespPageUtils;
    }

    @Override
    public PageUtils<MaterialChangeInfoResp> queryMaterialChangePage(MaterialChangeListReq req) {
        PageUtils<MaterialChangeInfoResp> materialChangeInfoRespPageUtils = materialChangeService.queryPage(req);
        return materialChangeInfoRespPageUtils;
    }

    @Override
    public List<MaterialChangeRepAnalyzeResp> materialChangeRepAnalyze(
            MaterialChangeRepAnalyzeReq materialChangeRepAnalyzeReq) {
        List<MaterialChangeRepAnalyzeResp> result = materialChangeService.materialChangeRepAnalyze(
                materialChangeRepAnalyzeReq);

        return result;
    }

    @Override
    public R queryLazyClassify(Long parentId) {
        String configValue = parentId + "";
        if (parentId == 0L) {//与前端约定好,0代表是分类标签的根
            R<String> r_configValue = configRpcService.getConfigValue(this.material_classify_categoryId);
            if (r_configValue.isSuccess() && StringUtils.isNotBlank(r_configValue.getData())) {
                configValue = r_configValue.getData();
            } else {
                return R.error("系统配置缺少素材分类配置参数：material.classify.categoryId");
            }
        }
        SysCategoryDTO sysCategory = new SysCategoryDTO();
        sysCategory.setId(Long.parseLong(configValue));
        R<List<SysCategory>> listR = categoryRpcService.queryChildrenById(sysCategory);
        return listR;
    }

    @Override
    public R queryMaterialTag() {
        R<String> configValue = configRpcService.getConfigValue(this.material_tag_categoryId);
        if (configValue.isSuccess() && StringUtils.isNotBlank(configValue.getData())) {
            SysCategoryDTO sysCategory = new SysCategoryDTO();
            sysCategory.setId(Long.parseLong(configValue.getData()));
            R<List<SysCategory>> listR = categoryRpcService.queryChildrenById(sysCategory);
            //过滤出当前人的标签
            List<SysCategory> data = listR.getData();
            if (data != null && data.size() > 0) {
                List<SysCategory> myTag = data.stream()
                        .filter(o -> SecurityUtils.getUsername().equals(o.getCreateBy())).collect(Collectors.toList());
                return R.ok(myTag);
            }
            return listR;
        } else {
            return R.error("系统配置缺少素材标签配置参数：material.tag.categoryId");
        }
    }

    @Override
    public void materialRelationCategory(MaterialChangeCategoryEditReq req) {
        List<MaterialCategoryAddReq> materialCategoryAddReqs = analyCategory(req.getCategoryAddReqList());
        // 先干掉素材分类关系,重新添加
        materialCategoryService.remove(Wrappers.<MaterialCategory>query().lambda()
                .eq(MaterialCategory::getMaterialId, req.getMaterialId()));
        materialCategoryService.saveBatch(MaterialCategoryConvertUtils.materialCategoryAddReqList2MaterialCategoryList(
                materialCategoryAddReqs, req.getMaterialId()));
    }


    @DSTransactional
    @Override
    public Map<Integer, Long> addYingjiMaterial(MaterialCategoryMaterialAddReq req) {
        log.info("添加素材 req:{}", JSON.toJSONString(req));
        List<Integer> materialType = req.getMaterialAddReq().getMaterialType();
        Integer fromType = req.getMaterialAddReq().getFromType();
        HashMap<Integer, Long> returnMap = new HashMap<>();
        for (Integer type : materialType) {
            Long materialId = wfgIdGenerator.next();
            // fromType=2时，设置所属库信息
            if (fromType.equals(FromTypeEnum.FILE_ADD.getCode())) {
                //如果我的素材，则查询当前人得个人库信息
                Long repoId = 0L;
                if (type.equals(MaterialTypeEnum.ONESELF_MATERIAL.getCode())) {
                    QueryOrAutoNewRepoReq repoReq = new QueryOrAutoNewRepoReq();
                    repoReq.setCtype(CType.PERSONAL.getCode());
                    repoId = repositoryService.queryRepoIdOrSave(repoReq);
                } else {
                    //非我的素材则根据文件信息反查库得信息
                    Long recordId = req.getMaterialAddReq().getRelationId();
                    List<ResourceChainDTO> resourceChainDTOS = validatorFactory.getValidator(ResourceType.RECORD)
                            .listResourceChains(recordId);
                    if (CollectionUtil.isNotEmpty(resourceChainDTOS)) {
                        ResourceChainDTO resourceChainDTO = resourceChainDTOS.get(0);
                        repoId = resourceChainDTO.getRepoId();
                    }
                }
                RepositoryResp repositoryResp = standardRepositoryFacade.queryById(repoId);
                if (Objects.nonNull(repositoryResp)) {
                    req.getMaterialAddReq().setRepId(repositoryResp.getId());
                    req.getMaterialAddReq().setRepName(repositoryResp.getName());
                }

            } else {
                if (req.getMaterialAddReq().getRepId() != null && StringUtils.isEmpty(
                        req.getMaterialAddReq().getRepName())) {
                    RepositoryResp repositoryResp = standardRepositoryFacade.queryById(
                            req.getMaterialAddReq().getRepId());
                    if (Objects.nonNull(repositoryResp)) {
                        req.getMaterialAddReq().setRepId(repositoryResp.getId());
                        req.getMaterialAddReq().setRepName(repositoryResp.getName());
                    }
                }

            }

            Material material = MaterialConvertUtils.materialAddReq2Material(req.getMaterialAddReq(),
                    materialId, SecurityUtils.getLoginUser(), type);

            materialService.save(material);

            // 素材分类
            materialCategoryService.saveBatch(
                    MaterialCategoryConvertUtils.materialCategoryAddReqList2MaterialCategoryList(
                            req.getCategoryAddReqList(), materialId));

            returnMap.put(type, materialId);
        }

        return returnMap;

    }



    @DSTransactional
    @Override
    public void modifyYingjiMaterial(MaterialChangeCategoryEditReq req) {
        String userId = SecurityUtils.getUserId();

        MaterialChangeAddReq materialChangeAddReq = req.getMaterialChangeAddReq();
        Material material = new Material();
        material.setId(req.getMaterialId());
        material.setContent(materialChangeAddReq.getContentAfter());
        material.setModifiedTime(new Date());
        material.setReasonType(ObjectUtils.isNotEmpty(materialChangeAddReq.getReasonType())?materialChangeAddReq.getReasonType():ReasonTypeEnum.OTHER.getCode());
        material.setName(req.getName());
        if (ReasonTypeEnum.OTHER.getCode().equals(materialChangeAddReq.getReasonType())) {
            material.setReason(materialChangeAddReq.getReason());
        } else {
            if(ObjectUtils.isNotEmpty(materialChangeAddReq.getReasonType())){
                ReasonTypeEnum anEnum = ReasonTypeEnum.getEnum(materialChangeAddReq.getReasonType());
                material.setReason(anEnum.getDesc());
            }
        }
        materialService.updateById(material);

        // 素材变更记录:和前端约定好了，‘素材内容’有变更的时候contentAfter才传值，否则传null。只有内容发生变更的时候才插入变更记录
        if (StringUtils.isNotEmpty(req.getMaterialChangeAddReq().getContentAfter())) {
            materialChangeService.save(
                    MaterialChangeConvertUtils.materialChangeAddReq2MaterialChange(materialChangeAddReq,
                            req.getMaterialId(), SecurityUtils.getLoginUser()));
            //如果是项目素材，发送变更通知消息
            Material byId = materialService.getById(req.getMaterialId());
            if (byId.getMaterialType().equals(MaterialTypeEnum.PROJECT_MATERIAL.getCode())) {
                //查询出该素材被引用的信息，所有引用的人都要发送消息
                List<MaterialQuote> materialQuoteList = materialQuoteService.list(
                        Wrappers.<MaterialQuote>query().lambda().eq(MaterialQuote::getMaterialId, byId.getId()));
                if (CollectionUtil.isNotEmpty(materialQuoteList)) {
                    List<SysMessage> sysmessageList = materialQuoteList.stream().map(materialQuote -> {
                        SysMessage sysMessage = new SysMessage();
                        sysMessage.setType(1);//1变更提醒；2协作提醒
                        String baseName = FilenameUtils.getBaseName(materialQuote.getRecordName());
                        sysMessage.setTitle(byId.getRepName() + "《" + baseName + "》");
                        sysMessage.setSendPersonId(userId);
                        sysMessage.setAcceptPersonId(materialQuote.getCreateBy());
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("contentBefore", materialChangeAddReq.getContentBefore());
                        jsonObject.put("contentAfter", materialChangeAddReq.getContentAfter());
                        sysMessage.setContent(jsonObject.toJSONString());
                        return sysMessage;
                    }).collect(Collectors.toList());
                    messageRpcService.batchSaveMessage(sysmessageList);
                }

            }
        }

        // 先干掉素材分类关系,重新添加
        materialCategoryService.remove(Wrappers.<MaterialCategory>query().lambda()
                .eq(MaterialCategory::getMaterialId, req.getMaterialId()));
//        List<MaterialCategoryAddReq> materialCategoryAddReqs = analyCategory();
        materialCategoryService.saveBatch(MaterialCategoryConvertUtils.materialCategoryAddReqList2MaterialCategoryList(
                req.getCategoryAddReqList(), req.getMaterialId()));

    }
}
