package com.suwell.plss.record.controller;

import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.ServletUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.ip.IpUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessStatus;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.log.enums.OperatorType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.entity.LogRecordView;
import com.suwell.plss.record.standard.dto.request.MaterialCategoryMaterialAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeCategoryEditReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeListReq;
import com.suwell.plss.record.standard.dto.request.MaterialChangeRepAnalyzeReq;
import com.suwell.plss.record.standard.dto.request.MaterialQueryReq;
import com.suwell.plss.record.standard.dto.request.MaterialQuoteAddReq;
import com.suwell.plss.record.standard.dto.request.MaterialQuoteListReq;
import com.suwell.plss.record.standard.dto.response.MaterialCategoryInfoResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeCategoryResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeInfoResp;
import com.suwell.plss.record.standard.dto.response.MaterialChangeRepAnalyzeResp;
import com.suwell.plss.record.standard.dto.response.MaterialQuoteInfoResp;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.standard.service.StandardMaterialFacade;
import java.util.List;
import java.util.Map;
import jakarta.annotation.Resource;

import com.suwell.plss.system.api.entity.SysOperLog;
import com.suwell.plss.system.api.service.LogRpcService;
import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/25
 */
@RestController
@RequestMapping("/v1/material")
public class MaterialController {

    public static final String MODE_NAME = "'前台-文档操作'";
    @Resource
    private StandardMaterialFacade standardMaterialFacade;
    @Resource
    private LogRpcService logRpcService;

    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<MaterialCategoryInfoResp>> queryPage(@RequestBody MaterialQueryReq req) {
        return R.ok(standardMaterialFacade.queryPage(req));
    }


    /**
     * 素材详情
     */
    @PostMapping("/queryMaterialInfo/{materialId}")
    public R<MaterialChangeCategoryResp> queryMaterialById(@PathVariable("materialId") Long materialId) {
        return R.ok(standardMaterialFacade.queryMaterialById(materialId));
    }

    /**
     * 素材添加
     */
    @Log(title = MODE_NAME,businessType = BusinessType.KNOWLEDGE_MATERIALS_ADD,remark = "'添加了文档'+#req.getMaterialAddReq().getName()+'的内容为知识素材'")
    @PostMapping("/addMaterial")
    public R<Map<Integer, Long>> addMaterial(@RequestBody MaterialCategoryMaterialAddReq req) {
        Map<Integer, Long> integerLongMap = standardMaterialFacade.addMaterial(req);
        return R.ok(integerLongMap);
    }

    /**
     * 素材修改
     */
    @PostMapping("/modifyMaterial")
    public R<Long> modifyMaterial(@RequestBody MaterialChangeCategoryEditReq req) {
        standardMaterialFacade.modifyMaterial(req);
        return R.ok();
    }


    /**
     * 素材删除
     */
    @PostMapping("/del/{materialId}")
    public R<MaterialChangeCategoryResp> del(@PathVariable("materialId") Long materialId) {
        standardMaterialFacade.removeMaterialById(materialId);
        return R.ok();
    }

    /**
     * 新增素材引用记录
     */
    @PostMapping("/quote/add")
    public R<Long> addMaterialQuote(@RequestBody MaterialQuoteAddReq materialQuoteAddReq) {
        Long aLong = standardMaterialFacade.addMaterialQuote(materialQuoteAddReq);
        return R.ok(aLong);
    }

    /**
     * 素材引用记录列表-分页
     */
    @PostMapping("/quote/list")
    public R<PageUtils<MaterialQuoteInfoResp>> queryMaterialQuotePage(
            @RequestBody MaterialQuoteListReq materialQuoteListReq) {
        PageUtils<MaterialQuoteInfoResp> materialQuoteInfoRespPageUtils = standardMaterialFacade.queryMaterialQuotePage(
                materialQuoteListReq);
        return R.ok(materialQuoteInfoRespPageUtils);
    }


    /**
     * 素材变更记录列表-分页
     */
    @PostMapping("/change/list")
    public R<PageUtils<MaterialChangeInfoResp>> queryMaterialChangePage(
            @RequestBody MaterialChangeListReq materialChangeListReq) {
        PageUtils<MaterialChangeInfoResp> materialChangeInfoRespPageUtils = standardMaterialFacade.queryMaterialChangePage(
                materialChangeListReq);
        return R.ok(materialChangeInfoRespPageUtils);
    }

    /**
     * 查询项目对应的素材的变更统计：top10
     */
    @PostMapping("/change/repAnalyze")
    public R<List<MaterialChangeRepAnalyzeResp>> materialChangeRepAnalyze(
            @RequestBody MaterialChangeRepAnalyzeReq materialChangeRepAnalyzeReq) {
        List<MaterialChangeRepAnalyzeResp> materialChangeRepAnalyzeResps = standardMaterialFacade.materialChangeRepAnalyze(
                materialChangeRepAnalyzeReq);

        return R.ok(materialChangeRepAnalyzeResps);

    }

    /**
     * 查询素材分类
     */
    @PostMapping("/category/clssify/{parentId}")
    public R queryLazyClassify(@PathVariable("parentId") Long parentId) {
        R r = standardMaterialFacade.queryLazyClassify(parentId);
        return r;
    }

    /**
     * 查询我的素材标签
     */
    @PostMapping("/category/tag")
    public R queryMaterialTag() {
        R r = standardMaterialFacade.queryMaterialTag();
        return r;
    }

    /**
     * 素材关联分类或者标签
     */
    @PostMapping("/relationCategory")
    public R materialRelationCategory(@RequestBody MaterialChangeCategoryEditReq materialChangeCategoryEditReq){
        standardMaterialFacade.materialRelationCategory(materialChangeCategoryEditReq);
        return R.ok();

    }



    /**
     * 素材添加
     */
    @PostMapping("/yingji/addMaterial")
    public R<Map<Integer, Long>> addYingjiMaterial(@RequestBody MaterialCategoryMaterialAddReq req) {
        Map<Integer, Long> integerLongMap = standardMaterialFacade.addYingjiMaterial(req);
        return R.ok(integerLongMap);
    }

    /**
     * 素材修改
     */
    @PostMapping("/yingji/modifyMaterial")
    public R<Long> modifyYingjiMaterial(@RequestBody MaterialChangeCategoryEditReq req) {
        standardMaterialFacade.modifyYingjiMaterial(req);
        return R.ok();
    }



}
