package com.suwell.plss.record.controller;

import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.ModelSettingDTO;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictCountResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeConflictDetailResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeInfoResp;
import com.suwell.plss.record.standard.dto.response.RecordKnowledgeQueryResp;
import com.suwell.plss.record.standard.service.StandardRecordKnowledgeFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 知识工程
 *
 * <AUTHOR>
 * @create 2024/06/12
 * @content
 */
@RestController
@RequestMapping("/v1/knowledge/record")
@Slf4j
public class KnowledgeRecordController {

    @Resource
    private StandardRecordKnowledgeFacade recordKnowledgeFacade;


    /**
     * 文档知识提取详情
     */
    @PostMapping("/detail")
    public R<RecordKnowledgeQueryResp> modelDetail(@RequestBody RecordKnowledgeQueryReq req){
        Long recordId = req.getRecordId();
        if(recordId == null){
            return R.error("recordId不能为空");
        }
        return R.ok(recordKnowledgeFacade.recordDetail(recordId));
    }

    /**
     * 新增概念实例
     */
    @PostMapping("/conceptValue/add")
    public R<Long> addConceptValue(@RequestBody RecordKnowledgeAddConceptValueReq addConceptValueReq){
        try {
            return R.ok(recordKnowledgeFacade.addConceptValue(addConceptValueReq));
        } catch (Exception e){
            log.error("新增概念实例失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }

    }

    /**
     * 删除概念实例
     */
    @PostMapping("/conceptValue/delete")
    public R<Void> deleteConceptValue(@RequestBody RecordKnowledgeDeleteConceptValueReq deleteConceptValueReq){
        try {
            recordKnowledgeFacade.deleteConceptValue(deleteConceptValueReq);
            return R.ok();
        }catch (Exception e){
            log.error("删除概念实例失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 编辑概念实例
     */
    @PostMapping("/conceptValue/edit")
    public R<Long> deleteConceptValue(@RequestBody RecordKnowledgeEditConceptValueReq editConceptValueReq){
        try {
            return R.ok(recordKnowledgeFacade.editConceptValue(editConceptValueReq));
        } catch (Exception e){
            log.error("编辑概念实例失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 添加属性
     */
    @PostMapping("/attribute/add")
    public R<Long> addAttribute(@RequestBody RecordKnowledgeAttributeReq attributeReq){
        try {
            return R.ok(recordKnowledgeFacade.addAttribute(attributeReq));
        } catch (Exception e){
            log.error("添加属性失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除属性
     */
    @PostMapping("/attribute/delete")
    public R<Long> deleteAttribute(@RequestBody RecordKnowledgeAttributeReq attributeReq){
        try {
            return R.ok(recordKnowledgeFacade.deleteAttribute(attributeReq));
        } catch (Exception e){
            log.error("删除属性失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 编辑属性
     */
    @PostMapping("/attribute/edit")
    public R<Long> editAttribute(@RequestBody RecordKnowledgeAttributeReq attributeReq){
        try {
            return R.ok(recordKnowledgeFacade.editAttribute(attributeReq));
        } catch (Exception e){
            log.error("编辑属性失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 添加关系
     */
    @PostMapping("/relation/add")
    public R<JSONObject> addRelation(@RequestBody RecordKnowledgeAddRelationReq addRelationReq){
        try {
            return R.ok(recordKnowledgeFacade.addRelation(addRelationReq));
        } catch (Exception e){
            log.error("添加关系失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 删除关系
     */
    @PostMapping("/relation/delete")
    public R<Void> deleteRelation(@RequestBody RecordKnowledgeDeleteRelationReq deleteRelationReq){
        try {
            recordKnowledgeFacade.deleteRelation(deleteRelationReq);
            return R.ok();
        } catch (Exception e){
            log.error("删除关系失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 编辑关系
     */
    @PostMapping("/relation/edit")
    public R<Void> editRelation(@RequestBody RecordKnowledgeEditRelationReq editRelationReq){
        try {
            recordKnowledgeFacade.editRelation(editRelationReq);
            return R.ok();
        } catch (Exception e){
            log.error("编辑关系失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询文档知识提取信息
     */
    @PostMapping("/info")
    public R<RecordKnowledgeInfoResp> queryRecordInfo(@RequestBody RecordKnowledgeReq req) {
        Long recordId = req.getRecordId();
        if(recordId == null){
            return R.error("recordId不能为空");
        }
        try {
            return R.ok(recordKnowledgeFacade.queryRecordInfo(recordId));
        } catch (Exception e){
            log.error("查询文档知识提取信息失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询文档冲突列表
     */
    @PostMapping("/conflict/list")
    public R<List<RecordKnowledgeConflictCountResp>> queryConflictList(@RequestBody RecordKnowledgeReq req) {
        Long recordId = req.getRecordId();
        if(recordId == null){
            return R.error("recordId不能为空");
        }
        try {
            return R.ok(recordKnowledgeFacade.queryConflictList(recordId));
        } catch (Exception e){
            log.error("查询文档冲突列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 根据概念实例查询冲突列表
     */
    @PostMapping("/conflict/concept")
    public R<List<RecordKnowledgeConflictDetailResp>> queryConflictConceptList(@RequestBody RecordKnowledgeConflictValueReq req) {
        Long recordId = req.getRecordId();
        if(recordId == null){
            return R.error("recordId不能为空");
        }
        try {
            return R.ok(recordKnowledgeFacade.queryConflictConceptList(req));
        } catch (Exception e){
            log.error("根据概念实例查询冲突列表失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 冲突不予合并
     */
    @PostMapping("/conflict/nonMerge")
    public R<Void> refuseMerge(@RequestBody RecordKnowledgeMergeReq req) {
        try {
            recordKnowledgeFacade.refuseMerge(req);
            return R.ok();
        } catch (Exception e){
            log.error("冲突不予合并失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }

    /**
     * 冲突合并
     */
    @PostMapping("/conflict/merge")
    public R<Void> mergeConflict(@RequestBody RecordKnowledgeMergeReq req) {
        try {
            recordKnowledgeFacade.mergeConflict(req);
            return R.ok();
        } catch (Exception e) {
           log.error("冲突合并失败:{}", e.getMessage());
           return R.error(e.getMessage());
       }
    }

    /**
     * 查询文档图谱
     */
    @PostMapping("/graph")
    public R<ModelSettingDTO> queryRecordGraph(@RequestBody RecordKnowledgeReq req) {
        Long recordId = req.getRecordId();
        if(recordId == null){
            return R.error("recordId不能为空");
        }
        try {
            ModelSettingDTO recordGraph = recordKnowledgeFacade.queryRecordGraph(req.getRecordId());
            return R.ok(recordGraph);
        } catch (Exception e){
            log.error("查询文档图谱失败:{}",e.getMessage());
            return R.error(e.getMessage());
        }
    }
}
