package com.suwell.plss.record;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.purgeteam.dynamic.config.starter.annotation.EnableDynamicConfigEvent;
import com.suwell.plss.framework.security.annotation.EnableCustomConfig;
import com.suwell.plss.framework.security.annotation.EnableCustomFeignClients;
import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2023/6/27
 */
@EnableDynamicConfigEvent
@EnableScheduling
@EnableFileStorage
@EnableCustomConfig
@EnableCustomFeignClients
@SpringBootApplication
@EnableMethodCache(basePackages = "com.suwell.plss.record")
@EnableCreateCacheAnnotation
public class PlssRecordApplication {

    public static void main(String[] args) {
        SpringApplication.run(PlssRecordApplication.class, args);
        System.out.println("================> 文件基础服务启动成功 <================");
    }
}
