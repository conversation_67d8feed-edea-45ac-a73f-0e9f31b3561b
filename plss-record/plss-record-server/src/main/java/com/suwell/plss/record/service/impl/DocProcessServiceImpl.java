package com.suwell.plss.record.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.suwell.plss.framework.common.config.KsoAuthConfig;
import com.suwell.plss.framework.common.constant.KsoGroupConstant;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.ResouceClassifiedEnum;
import com.suwell.plss.framework.common.enums.UserClassifiedEnum;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.text.Convert;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.SpringUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.SuwellStopWatch;
import com.suwell.plss.framework.datasource.sharding.DataSourceConfiguration;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.conf.RecordOriginConfig;
import com.suwell.plss.record.conf.StatisticConfig;
import com.suwell.plss.record.entity.DocProcess;
import com.suwell.plss.record.entity.Document;
import com.suwell.plss.record.entity.FolderRecord;
import com.suwell.plss.record.entity.Node;
import com.suwell.plss.record.entity.RecordType;
import com.suwell.plss.record.enums.DocProcessStatusEnums;
import com.suwell.plss.record.mapper.DocProcessMapper;
import com.suwell.plss.record.service.DocProcessService;
import com.suwell.plss.record.service.DocumentService;
import com.suwell.plss.record.service.FolderRecordService;
import com.suwell.plss.record.service.NodeService;
import com.suwell.plss.record.service.OperateDocProcessESService;
import com.suwell.plss.record.service.RecordTypeService;
import com.suwell.plss.record.standard.dto.request.DocProcessCountByDateReq;
import com.suwell.plss.record.standard.dto.request.DocProcessFrontQueryReq;
import com.suwell.plss.record.standard.dto.request.DocProcessQueryBaseReq;
import com.suwell.plss.record.standard.dto.request.DocProcessQueryV2Req;
import com.suwell.plss.record.standard.dto.request.DocProcessStatusReq;
import com.suwell.plss.record.standard.dto.request.DocStoreCountReq;
import com.suwell.plss.record.standard.dto.request.DocTotalReq;
import com.suwell.plss.record.standard.dto.request.ThirdPartyCountReq;
import com.suwell.plss.record.standard.dto.response.DocProcessCountByDateResp;
import com.suwell.plss.record.standard.dto.response.DocProcessFrontResp;
import com.suwell.plss.record.standard.dto.response.DocProcessNumResp;
import com.suwell.plss.record.standard.dto.response.DocProcessResp;
import com.suwell.plss.record.standard.dto.response.DocProcessStatusResp;
import com.suwell.plss.record.standard.dto.response.DocProcessSubStatusResp;
import com.suwell.plss.record.standard.dto.response.DocStoreCountResp;
import com.suwell.plss.record.standard.dto.response.DocTotalResp;
import com.suwell.plss.record.standard.dto.response.DocumentSimpleResp;
import com.suwell.plss.record.standard.dto.response.ThirdPartyCountResp;
import com.suwell.plss.record.standard.enums.DocAttachmentTypeEnum;
import com.suwell.plss.record.standard.enums.NodeTypeEnum;
import com.suwell.plss.record.standard.enums.RecordEnum;
import com.suwell.plss.record.util.DataPmsUtil;
import com.suwell.plss.record.util.DateUtil;
import com.suwell.plss.record.util.DocProcessEsUtil;
import com.suwell.plss.record.util.NodeCacheUtil;
import com.suwell.plss.search.api.http.SearchRpcEntrance;
import com.suwell.plss.search.standard.dto.request.newsSearch.AggRecordCountQueryReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.BackendSearchReq;
import com.suwell.plss.search.standard.dto.request.newsSearch.MetadataListQueryItem;
import com.suwell.plss.search.standard.dto.request.newsSearch.SearchSortOption;
import com.suwell.plss.search.standard.dto.response.SearchAggCountResp;
import com.suwell.plss.system.api.enums.DataPmsEnum;
import com.suwell.plss.system.api.service.UserRpcService;
import jakarta.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023/11/14
 * @content
 */
@Slf4j
@Service
@DS(DataSourceConfiguration.SHARDING_DATA_SOURCE_NAME)
public class DocProcessServiceImpl extends ServiceImpl<DocProcessMapper, DocProcess> implements DocProcessService {

    @Resource
    private RecordTypeService recordTypeService;
    @Resource
    private UserRpcService userRpcService;
    @Resource
    private NodeService nodeService;
    @Resource
    private NodeCacheUtil nodeCacheUtil;
    @Resource
    private FolderRecordService folderRecordService;
    @Resource
    private RecordOriginConfig recordOriginConfig;
    @Resource
    private DataPmsUtil dataPmsUtil;
    @Resource
    private StatisticConfig statisticConfig;
    @Resource
    private SearchRpcEntrance searchRpcEntrance;
    @Resource
    private DocProcessEsUtil docProcessEsUtil;
    @Resource
    private OperateDocProcessESService operateDocProcessESService;
    @Resource
    private DocumentService documentService;

    @Resource
    private KsoAuthConfig ksoAuthConfig;

    @Override
    public List<DocProcessNumResp> queryDocProcessNum(String tenantId, Long batchId, Boolean forSecure) {

        Map<Integer, DocProcessNumResp> map = new HashMap<>();
        final UserClassifiedEnum userClassified = SecurityUtils.getUserClassified();
        List<Integer> classifiedList = null;
        // 用户密级校验
        if (Boolean.TRUE.equals(forSecure)) {
            if (userClassified == UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL) {
                // 非密用户不能查看密级数据  return
            } else {
                classifiedList = userClassified.getResourcePerms().stream().map(ResouceClassifiedEnum::getCode)
                        .collect(Collectors.toList());
            }
        } else {
            classifiedList = new ArrayList<>();
        }

        if (classifiedList != null) {
            final List<String> userIdList = queryAllUserId();
            if (CollUtil.isNotEmpty(userIdList)) {
                final String userId = SecurityUtils.getUserId();
                Integer moduleType = Boolean.TRUE.equals(forSecure)
                        ? DataPmsEnum.BizModuleType.BIZ_MODULE_TYPE_SECURE_DOC_PUT.getCode()
                        : DataPmsEnum.BizModuleType.BIZ_MODULE_TYPE_DOC_PUT.getCode();
                List<String> newUserIdList = dataPmsUtil.getUserIdList(tenantId, userIdList, userId, moduleType);

                if (CollUtil.isEmpty(newUserIdList)) {
                    map = new HashMap<>();
                } else {
                    // 开启用户组
                    if (CollUtil.contains(newUserIdList, KsoGroupConstant.EXPIRED_ID)) {
                        newUserIdList = null;
                    }
                    final List<DocProcessNumResp> resps = docProcessEsUtil.queryDocProcessNum(classifiedList,
                            Boolean.TRUE.equals(forSecure), tenantId, batchId,
                            queryDocProcessStatus().stream().map(DocProcessStatusResp::getProcessStatus)
                                    .collect(Collectors.toList()),
                            newUserIdList, recordOriginConfig.getOriginMap(true).values().stream().toList());
                    map = resps.stream().collect(Collectors.toMap(DocProcessNumResp::getProcessStatus, o -> o));
                }
            }
        }

        final Map<Integer, DocProcessNumResp> finalMap = map;
        return Arrays.stream(DocProcessStatusEnums.ProcessStatusEnum.values())
                .filter(p -> !Boolean.TRUE.equals(forSecure)
                        || p != DocProcessStatusEnums.ProcessStatusEnum.PS_PENDING_REVIEW)
                .map(o -> {
                    if (finalMap.containsKey(o.getCode())) {
                        final DocProcessNumResp resp = finalMap.get(o.getCode());
                        resp.setProcessStatusName(o.getDesc());
                        return resp;
                    } else {
                        DocProcessNumResp r = new DocProcessNumResp();
                        r.setNum(0L);
                        r.setProcessStatus(o.getCode());
                        r.setProcessStatusName(o.getDesc());
                        return r;
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public List<DocProcessStatusResp> queryDocProcessStatus() {
        return Arrays.stream(DocProcessStatusEnums.ProcessStatusEnum.values()).map(o -> {
            final DocProcessStatusResp resp = new DocProcessStatusResp();
            resp.setProcessStatus(o.getCode());
            resp.setProcessStatusName(o.getDesc());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DocProcessSubStatusResp> queryDocProcessSubStatus(DocProcessStatusReq req) {
        return Arrays.stream(DocProcessStatusEnums.ProcessStatusEnum.convert(req.getProcessStatus()).getItems())
                .map(o -> {
                    final DocProcessSubStatusResp resp = new DocProcessSubStatusResp();
                    resp.setProcessSubStatus(o.getCode());
                    resp.setProcessSubStatusName(o.getDesc());
                    return resp;
                }).collect(Collectors.toList());
    }

    public List<String> getUserIdListOld(String tenantId, List<String> userIdList, String userId) {
        List<String> newUserIdList = new ArrayList<>();
        if (isManager(userId, tenantId)) {
            log.info("管理员或代管理员,userId={},tenantId={}", userId, tenantId);
        } else {
            final Set<String> userIdSet = CollUtil.newHashSet(userIdList);
            userIdSet.add(userId);

            final R<Map<String, String>> r = userRpcService.getUserDeptId(CollUtil.newArrayList(userIdSet));
            final Map<String, String> data = r.getData();

            final String currentDpId = data.get(userId);
            final List<String> currentUserIdList = data.entrySet().stream()
                    .filter(e -> currentDpId.equals(e.getValue()))
                    .map(Map.Entry::getKey).collect(Collectors.toList());
            log.info("currentUserIdList={}", currentUserIdList);
            newUserIdList = currentUserIdList;
        }
        return newUserIdList;
    }

    @Override
    public PageUtils<DocProcessResp> queryPage(DocProcessQueryV2Req req) {
        return queryPage(req, null);
    }

    @Override
    public PageUtils<DocProcessResp> queryPage(DocProcessQueryV2Req req, Long batchId) {
        log.info("入参:{},batchId:{}", req, batchId);
        List<Integer> processStatusList = req.getProcessStatusList();
        List<Long> recordTypeIdList = req.getRecordTypeIdList();

        final UserClassifiedEnum userClassified = SecurityUtils.getUserClassified();
        // 用户密级校验
        if (req.isForSecure()) {
            if (userClassified == UserClassifiedEnum.NON_CONFIDENTIAL_PERSONNEL) {
                // 非密用户不能查看密级数据
                return new PageUtils<>(new ArrayList<>(), 0, req.getPageSize(), req.getPage());
            } else {
                final List<Integer> resourcePermList = userClassified.getResourcePerms().stream()
                        .map(ResouceClassifiedEnum::getCode).collect(Collectors.toList());
                if (req.getClassified() == null) {
                    // 未传密级，查看用户密级范围内的数据
                    req.setClassifiedList(resourcePermList);
                } else {
                    if (!CollUtil.newHashSet(resourcePermList).contains(req.getClassified())) {
                        // 传入密级不在用户密级范围内
                        return new PageUtils<>(new ArrayList<>(), 0, req.getPageSize(), req.getPage());
                    } else {
                        req.setClassifiedList(CollUtil.newArrayList(req.getClassified()));
                    }
                }
            }
        }

        final String tenantId = SecurityUtils.getTenantId();
        final String userId = SecurityUtils.getUserId();

        SuwellStopWatch ssw = new SuwellStopWatch("文档入库");
        ssw.start("查询用户列表");

        final List<String> userIdList = docProcessEsUtil.queryAllUserId();
        ssw.stop();
        if (CollUtil.isEmpty(userIdList)) {
            return new PageUtils<>(new ArrayList<>(), 0, req.getPageSize(), req.getPage());
        }

        final boolean loadSubRecord = Optional.ofNullable(processStatusList).map(List::size).orElse(0) == 0 &&
                Optional.ofNullable(recordTypeIdList).map(List::size).orElse(0) == 0 &&
                CharSequenceUtil.isBlank(req.getName()) &&
                req.getUploadTimeEnd() == null &&
                req.getUploadTimeStart() == null &&
                req.getOrigin() == null &&
                req.getRecordId() == null &&
                req.getClassified() == null &&
                CharSequenceUtil.isBlank(req.getCreateByName());

        ssw.start("查询用户权限");
        Integer moduleType = req.isForSecure() ? DataPmsEnum.BizModuleType.BIZ_MODULE_TYPE_SECURE_DOC_PUT.getCode()
                : DataPmsEnum.BizModuleType.BIZ_MODULE_TYPE_DOC_PUT.getCode();
        List<String> newUserIdList = dataPmsUtil.getUserIdList(tenantId, userIdList, userId, moduleType);
        ssw.stop();

        // 开启用户组 查询es要设为空
        if (CollUtil.isEmpty(newUserIdList)) {
            return new PageUtils<>(new ArrayList<>(), 0, req.getPageSize(), req.getPage());
        }

        if (CollUtil.contains(newUserIdList, KsoGroupConstant.EXPIRED_ID)) {
            newUserIdList = null;
        }

        if (loadSubRecord) {
            req.setCtype(DocProcessStatusEnums.DocProcessTypeEnum.DPT_MASTER.getCode());
        } else {
            req.setCtype(null);
        }

//        ssw.start("查询recordType");
//        final Map<Long, String> recordTypeMap = recordTypeService.lambdaQuery()
//                .select(RecordType::getId, RecordType::getName)
//                .gt(RecordType::getId, 0L)
//                .list().stream().collect(Collectors.toMap(RecordType::getId, RecordType::getName));
//        if (CollUtil.isEmpty(req.getRecordTypeIdList())) {
//            req.setRecordTypeIdList(new ArrayList<>(recordTypeMap.keySet()));
//        }
//        ssw.stop();
        // 处理origin
        if (Objects.isNull(req.getOrigin())) {
            List<Long> originList = recordOriginConfig.getOriginMap(true).values().stream()
                    .map(Long::valueOf).toList();
            req.setOriginList(originList);
        } else {
            req.setOriginList(List.of(Long.valueOf(req.getOrigin())));
        }
        // 元数据去es查询
        if (req.mdQuery()) {
            return forMdQuery(req, ssw, newUserIdList, userId);
        }
        if (CollUtil.isEmpty(req.getProcessStatusList())) {
            req.setProcessStatusList(queryDocProcessStatus().stream().map(DocProcessStatusResp::getProcessStatus)
                    .collect(Collectors.toList()));
            req.setProcessSubStatusList(req.getProcessStatusList().stream().flatMap(o -> {
                DocProcessStatusReq docProcessStatusReq = new DocProcessStatusReq();
                docProcessStatusReq.setProcessStatus(o);
                return queryDocProcessSubStatus(docProcessStatusReq).stream()
                        .map(DocProcessSubStatusResp::getProcessSubStatus);
            }).collect(Collectors.toList()));
        } else {
            if (CollUtil.isEmpty(req.getProcessSubStatusList())) {
                req.setProcessSubStatusList(req.getProcessStatusList().stream().flatMap(o -> {
                    DocProcessStatusReq docProcessStatusReq = new DocProcessStatusReq();
                    docProcessStatusReq.setProcessStatus(o);
                    return queryDocProcessSubStatus(docProcessStatusReq).stream()
                            .map(DocProcessSubStatusResp::getProcessSubStatus);
                }).collect(Collectors.toList()));
            }
        }

        ssw.start("查询入库记录");
        final IPage<Long> recordIdPage = docProcessEsUtil.selectDocProcessPage(tenantId, req, batchId, newUserIdList);
        ssw.stop();
        final List<Long> recordIdList = recordIdPage.getRecords();
        if (CollUtil.isEmpty(recordIdList)) {
            return new PageUtils<>(new ArrayList<>(), recordIdPage.getTotal(), req.getPageSize(), req.getPage());
        }
        List<DocProcess> records = this.list(
                Wrappers.<DocProcess>lambdaQuery().in(DocProcess::getRecordId, recordIdList)
                        .orderByDesc(DocProcess::getRecordId));

        List<Long> subRecordIds = null;
        Map<Long, List<DocProcess>> subDocMap = null;
        // 判断是否加载子文件
        if (loadSubRecord) {
            // 查询全部文档，则加载子文件
            List<Long> taskIds = records.stream().map(DocProcess::getTaskId).collect(Collectors.toList());
            ssw.start("查询入库子记录");
            subRecordIds = docProcessEsUtil.getSubDoc(taskIds);
            final List<DocProcess> subDocList = CollUtil.isEmpty(subRecordIds) ? new ArrayList<>() : this.list(
                    Wrappers.<DocProcess>lambdaQuery().in(DocProcess::getRecordId, subRecordIds));
            ssw.stop();
            subDocMap = subDocList.stream().collect(Collectors.groupingBy(DocProcess::getTaskId));
        }

        List<Long> mainRecordIds = records.stream().map(DocProcess::getRecordId).collect(Collectors.toList());
        List<Long> recordIds = Optional.ofNullable(subRecordIds).map(o -> {
            mainRecordIds.addAll(o);
            return mainRecordIds;
        }).orElse(mainRecordIds);

        Map<Long, Long> recordRelCountMap;
        if (Objects.isNull(req.getRecordRelStatus())) {
            ssw.start("查询folderRecord");
            recordRelCountMap = folderRecordService.listWithWrapper(new LambdaQueryWrapper<FolderRecord>()
                            .in(FolderRecord::getRecordId, recordIds))
                    .stream().collect(Collectors.groupingBy(FolderRecord::getRecordId, Collectors.counting()));
            ssw.stop();
        } else {
            recordRelCountMap = null;
        }

        Map<Long, List<DocProcess>> finalSubDocMap = subDocMap;
        // 审批人集合
        final Table<Long, Long, Set<String>> planIdSetMap = HashBasedTable.create();
        final Node node = nodeService.lambdaQuery().eq(Node::getNodeType, NodeTypeEnum.NT_MANUAL_REVIEW.getCode())
                .one();
        ssw.start("查询附件");
        // 查询附件
        final List<Document> documentList = documentService.listByBatchType(recordIds,
                RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode());
        Map<Long, List<Document>> attachmentMap = documentList.stream()
                .collect(Collectors.groupingBy(Document::getRecordId));
        ssw.stop();
        ssw.start("查询recordType");
        final Map<Long, String> recordTypeMap = recordTypeService.lambdaQuery()
                .select(RecordType::getId, RecordType::getName)
                .in(RecordType::getId, records.stream().map(DocProcess::getRecordTypeId).collect(Collectors.toList()))
                .list().stream().collect(Collectors.toMap(RecordType::getId, RecordType::getName));
        ssw.stop();
        ssw.start("整理数据");
        final List<DocProcessResp> list = records.stream().map(o -> {
            DocProcessResp resp = getResp(req, userId, recordRelCountMap, planIdSetMap, recordTypeMap, o, node);
            if (Objects.nonNull(finalSubDocMap) && finalSubDocMap.containsKey(o.getTaskId())) {
                resp.setChildList(
                        finalSubDocMap.get(o.getTaskId()).stream().sorted(Comparator.comparing(DocProcess::getRecordId))
                                .map(sub -> {
                                    DocProcessResp subResp = getResp(req, userId, recordRelCountMap, planIdSetMap,
                                            recordTypeMap, sub, node);
                                    if (attachmentMap.containsKey(sub.getRecordId())) {
                                        subResp.setAttachmentList(attachmentMap.get(sub.getRecordId()).stream()
                                                .peek(a -> {
                                                    if (a.getAttachmentType() == null) {
                                                        a.setAttachmentType(
                                                                DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode());
                                                    }
                                                })
                                                .sorted(Comparator.comparing(Document::getAttachmentType,
                                                                Comparator.nullsFirst(Comparator.naturalOrder()))
                                                        .thenComparing(Document::getId)
                                                )
                                                .map(d -> {
                                                    DocumentSimpleResp simpleResp = new DocumentSimpleResp();
                                                    simpleResp.setRecordId(d.getRecordId());
                                                    simpleResp.setDocId(d.getId());
                                                    simpleResp.setName(d.getName());
                                                    simpleResp.setDocAttStatus(d.getDocAttStatus());
                                                    return simpleResp;
                                                }).toList()
                                        );
                                    }
                                    return subResp;
                                }).collect(Collectors.toList()));
            }
            if (attachmentMap.containsKey(o.getRecordId())) {
                resp.setAttachmentList(attachmentMap.get(o.getRecordId()).stream()
                        .peek(a -> {
                            if (a.getAttachmentType() == null) {
                                a.setAttachmentType(DocAttachmentTypeEnum.DAT_ATTACHMENT.getCode());
                            }
                        })
                        .sorted(Comparator.comparing(Document::getAttachmentType)
                                .thenComparing(Document::getId)
                        )
                        .map(d -> {
                            DocumentSimpleResp simpleResp = new DocumentSimpleResp();
                            simpleResp.setRecordId(d.getRecordId());
                            simpleResp.setDocId(d.getId());
                            simpleResp.setName(d.getName());
                            simpleResp.setDocAttStatus(d.getDocAttStatus());
                            return simpleResp;
                        }).toList()
                );
            }
            return resp;
        }).sorted(Comparator.comparing(DocProcessResp::getRecordId).reversed()).collect(Collectors.toList());
        ssw.stop();
        log.info("入库查询耗时:{}", ssw);
        return new PageUtils<>(list, recordIdPage.getTotal(), req.getPageSize(), req.getPage());
    }

    @Override
    @DS(DataSourceConfiguration.MASTER_DATA_SOURCE_NAME)
    public void migrateDocProcess() {
        Long count = lambdaQuery().count();
        if (count == 0) {
            return;
        }
        long pageSize = 1000;
        long page = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
        DocProcessServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        Long maxId = 0L;
        for (int i = 0; i < page; i++) {
            QueryWrapper<DocProcess> wrapper = new QueryWrapper<>();
            wrapper.lambda().select(DocProcess::getRecordId, DocProcess::getPlanId, DocProcess::getBatchId,
                    DocProcess::getTaskId, DocProcess::getTaskDocId, DocProcess::getName,
                    DocProcess::getRecordTypeId, DocProcess::getOrigin, DocProcess::getRealOrigin,
                    DocProcess::getProcessStatus, DocProcess::getProcessSubStatus,
                    DocProcess::getAuditStatus, DocProcess::getCtype, DocProcess::getTenantId,
                    DocProcess::getTitle, DocProcess::getPreRecordId, DocProcess::getStoreWay,
                    DocProcess::getKnowledgeStatus, DocProcess::getClassified, DocProcess::getCreateBy,
                    DocProcess::getCreateByName, DocProcess::getCreateTime, DocProcess::getModifiedTime);
            wrapper.eq("deleted", 1)
                    .gt("record_id", maxId)
                    .orderByAsc("record_id")
                    .last("limit " + pageSize);
            List<DocProcess> list = list(wrapper);
            if (CollUtil.isEmpty(list)) {
                return;
            }
            maxId = list.get(list.size() - 1).getRecordId();
            Set<String> collect = list.stream().map(DocProcess::getCreateBy).collect(Collectors.toSet());
            R<Map<String, String>> userDeptId = userRpcService.getUserDeptId(Lists.newArrayList(collect));
            Map<String, String> map = userDeptId.getData();
            list.forEach(record -> record.setOrgId(map.getOrDefault(record.getCreateBy(), "0")));
            aopProxy.removeBatchByIds(list);
            aopProxy.saveBatch(list, list.size());
            // 写入es
            for (DocProcess docProcess : list) {
                operateDocProcessESService.saveOrUpdateEsDocProcess(docProcess);
            }
        }
        log.info("迁移doc_process数据至分表完成，总数：{}", count);
    }

    /**
     * 包含元数据查询时，去es搜索。忽略批次号
     */
    private PageUtils<DocProcessResp> forMdQuery(DocProcessQueryV2Req req, SuwellStopWatch ssw,
            List<String> userPermRange, String userId) {
        // 去es查询recordId
        PageUtils<Long> recordIdPage = queryRecordIdOnES(req, ssw, userPermRange);
        List<Long> recordIdList = recordIdPage.getList();
        if (CollUtil.isEmpty(recordIdList)) {
            return new PageUtils<>();
        }
        // 这里都是有位置的
        req.setRecordRelStatus(1);
        List<DocProcess> docProcesses = lambdaQuery()
                .in(DocProcess::getRecordId, recordIdList).list();

        final Map<Long, String> recordTypeMap = Optional.ofNullable(docProcesses).map(l -> {
            if (CollUtil.isEmpty(l)) {
                return null;
            } else {
                return recordTypeService.lambdaQuery()
                        .select(RecordType::getId, RecordType::getName)
                        .in(RecordType::getId, l.stream().map(DocProcess::getRecordTypeId).collect(Collectors.toList()))
                        .list().stream().collect(Collectors.toMap(RecordType::getId, RecordType::getName));
            }
        }).orElseGet(HashMap::new);

        List<DocProcessResp> list = docProcesses.stream()
                .map(doc -> getResp(req, userId, null, null, recordTypeMap, doc, null))
                .toList();
        return new PageUtils<>(list, recordIdPage.getTotalCount(), recordIdPage.getPageSize(), recordIdPage.getPage());
    }

    private PageUtils<Long> queryRecordIdOnES(DocProcessQueryV2Req req, SuwellStopWatch ssw,
            List<String> userPermRange) {
        ssw.start("查询es-record");
        BackendSearchReq searchReq = new BackendSearchReq();
        searchReq.setTitle(req.getName());
        searchReq.setOriginList(req.getOriginList());
        searchReq.setUserIds(userPermRange);
        searchReq.setRecordTypeIds(req.getRecordTypeIdList());
        searchReq.setUploadTimeStart(req.getUploadTimeStart());
        searchReq.setUploadTimeEnd(req.getUploadTimeEnd());
        SearchSortOption sortOption = new SearchSortOption();
        sortOption.setSortField("recordId");
        sortOption.setSortType("desc");
        searchReq.setSortOptions(List.of(sortOption));
        searchReq.setPage(req.getPage());
        searchReq.setPageSize(req.getPageSize());
        List<MetadataListQueryItem> mdQueryList = req.getMetadataList().stream().map(m -> {
            MetadataListQueryItem queryItem = new MetadataListQueryItem();
            queryItem.setMetadataId(m.getMetadataId());
            queryItem.setMetadataValue(List.of(m.getMetadataValue()));
            return queryItem;
        }).toList();
        searchReq.setMetadataList(mdQueryList);
        R<PageUtils<Long>> esR = searchRpcEntrance.backendSearch(searchReq);
        ssw.stop();
        if (esR.isError()) {
            log.error("es查询失败:{}", esR);
            throw new ServiceException("搜索文件id失败");
        }
        return esR.getData();
    }

    @Override
    public PageUtils<DocProcessFrontResp> queryPageFront(DocProcessFrontQueryReq req) {
        // 只查本人
        final String userId = SecurityUtils.getUserId();
        final Integer status = req.getStatus();
        if (CollUtil.isEmpty(req.getProcessSubStatusList())) {
            req.setProcessSubStatusList(
                    Arrays.stream(DocProcessStatusEnums.ProcessStatusFrontEnum.convert(status).getItems())
                            .map(DocProcessStatusEnums.ProcessSubStatusEnum::getCode)
                            .collect(Collectors.toList())
            );
        }

        final Map<Long, String> recordTypeMap = recordTypeService.lambdaQuery()
                .select(RecordType::getId, RecordType::getName)
                .gt(RecordType::getId, 0L)
                .list().stream().collect(Collectors.toMap(RecordType::getId, RecordType::getName));

        if (CollUtil.isEmpty(req.getRecordTypeIdList())) {
            req.setRecordTypeIdList(new ArrayList<>(recordTypeMap.keySet()));
        }

        IPage<Long> recordIdPage = docProcessEsUtil.selectFrontDocProcessPage(userId, req);
        List<Long> recordsIdList = recordIdPage.getRecords();
        if (CollUtil.isEmpty(recordsIdList)) {
            return new PageUtils<>(new ArrayList<>(), recordIdPage.getTotal(), req.getPageSize(), req.getPage());
        }
        final List<DocProcess> records = this.list(
                Wrappers.<DocProcess>lambdaQuery().in(DocProcess::getRecordId, recordsIdList)
                        .orderByDesc(DocProcess::getRecordId));

        final List<DocProcessFrontResp> list = records.stream().map(o -> {
            final DocProcessFrontResp resp = new DocProcessFrontResp();
            resp.setRecordId(o.getRecordId());
            resp.setName(o.getName());
            resp.setTitle(o.getTitle());
            resp.setRecordTypeName(recordTypeMap.get(o.getRecordTypeId()));
            resp.setProcessSubStatus(o.getProcessSubStatus());
            resp.setProcessSubStatusName(DocProcessStatusEnums.ProcessSubStatusEnum.getDesc(o.getProcessSubStatus()));
            resp.setProcessStatus(o.getProcessStatus());
            resp.setProcessStatusName(DocProcessStatusEnums.ProcessStatusEnum.getDesc(o.getProcessStatus()));
            resp.setUploadTime(o.getCreateTime());
            resp.setStoreWay(o.getStoreWay());
            return resp;
        }).collect(Collectors.toList());
        return new PageUtils<>(list, recordIdPage.getTotal(), req.getPageSize(), req.getPage());
    }

    @Override
    public List<DocProcessCountByDateResp> processCountByDate(DocProcessCountByDateReq req) {
        String dateType = req.getDateType();
        String tenantId = req.getTenantId();
        List<DocProcessCountByDateResp> list = Lists.newArrayList();
        if ("1".equals(dateType)) {
            Calendar calendar = Calendar.getInstance();
            // 将日期调整到本周第一天（星期日）
            int firstDayOfWeek = calendar.getFirstDayOfWeek();
            while (calendar.get(Calendar.DAY_OF_WEEK) != firstDayOfWeek) {
                calendar.add(Calendar.DATE, -1);
            }
            // 遍历输出本周的每一天
            Map<String, Date[]> dateMap = new HashMap<>(7);
            for (int i = 0; i < 7; i++) {
                // 将日期加上一天
                calendar.add(Calendar.DATE, 1);
                dateMap.put(cn.hutool.core.date.DateUtil.format(calendar.getTime(), "yyyy-MM-dd"),
                        new Date[]{
                                DateUtil.getStartTimeOfCurrentDay(calendar.getTime()),
                                DateUtil.getEndTimeOfCurrentDay(calendar.getTime())
                        }
                );
            }
            Map<String, Long> longMap = docProcessEsUtil.processCountByDate(tenantId, dateMap);
            List<DocProcessCountByDateResp> collect = longMap.entrySet().stream().map(o -> {
                        DocProcessCountByDateResp docProcessCountByDateResp = new DocProcessCountByDateResp();
                        docProcessCountByDateResp.setDateStr(o.getKey());
                        docProcessCountByDateResp.setNum(o.getValue());
                        return docProcessCountByDateResp;
                    })
                    .sorted(Comparator.comparing(DocProcessCountByDateResp::getDateStr))
                    .toList();
            list.addAll(collect);
        }
        if ("2".equals(dateType)) {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

            Map<String, Date[]> dateMap = new HashMap<>(daysInMonth);
            for (int day = 1; day <= daysInMonth; day++) {
                calendar.set(year, month - 1, day);
                dateMap.put(cn.hutool.core.date.DateUtil.format(calendar.getTime(), "yyyy-MM-dd"),
                        new Date[]{
                                DateUtil.getStartTimeOfCurrentDay(calendar.getTime()),
                                DateUtil.getEndTimeOfCurrentDay(calendar.getTime())
                        }
                );
            }
            Map<String, Long> longMap = docProcessEsUtil.processCountByDate(tenantId, dateMap);
            List<DocProcessCountByDateResp> collect = longMap.entrySet().stream().map(o -> {
                        DocProcessCountByDateResp docProcessCountByDateResp = new DocProcessCountByDateResp();
                        docProcessCountByDateResp.setDateStr(o.getKey());
                        docProcessCountByDateResp.setNum(o.getValue());
                        return docProcessCountByDateResp;
                    })
                    .sorted(Comparator.comparing(DocProcessCountByDateResp::getDateStr))
                    .toList();
            list.addAll(collect);
        }
        if ("3".equals(dateType)) {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            Map<String, Date[]> dateMap = new HashMap<>(12);
            for (int month = 0; month < 12; month++) {
                DocProcessCountByDateResp docProcessCountByDateResp = new DocProcessCountByDateResp();
                // 将 Calendar 对象的月份设为指定值（从0开始）
                calendar.set(year, month, 1);

                // 获取该月第一天的起始时间
                Date startTime = DateUtil.getStartTimeOfCurrentDay(calendar.getTime());
                // 将 Calendar 对象的月份加上1，表示下个月
                calendar.add(Calendar.MONTH, 1);
                calendar.add(Calendar.DAY_OF_MONTH, -1);

                // 获取该月最后一天的结束时间
                Date endTime = DateUtil.getEndTimeOfCurrentDay(calendar.getTime());
                dateMap.put((month + 1) + "月",
                        new Date[]{
                                startTime,
                                endTime
                        }
                );
            }
            Map<String, Long> longMap = docProcessEsUtil.processCountByDate(tenantId, dateMap);
            List<DocProcessCountByDateResp> collect = longMap.entrySet().stream().map(o -> {
                        DocProcessCountByDateResp docProcessCountByDateResp = new DocProcessCountByDateResp();
                        docProcessCountByDateResp.setDateStr(o.getKey());
                        docProcessCountByDateResp.setNum(o.getValue());
                        return docProcessCountByDateResp;
                    })
                    .sorted(Comparator.comparing(DocProcessCountByDateResp::getDateStr))
                    .toList();
            list.addAll(collect);
        }
        return list;
    }

    @Override
    public DocTotalResp queryDocTotal(DocTotalReq req) {
        // 有现场直接导入record表的数据，doc_process表没数据，所以统计record表数据
//        LambdaQueryWrapper<Record> lqw = Wrappers.<Record>query().lambda()
//                .eq(Record::getRecordStatus, RECORD_PROCESS_STATUS_FINISH.getRecordStatusEnum().getCode())
//                .eq(Record::getStoreProcess, StoreProcessEnum.SP_WITH_IN_STORE.getCode());

        AggRecordCountQueryReq queryReq = new AggRecordCountQueryReq();
        queryReq.setAggField("origin");
        // 使用传入的租户id 或者 登录的租户id
        final String tenantId = Optional.ofNullable(req.getTenantId())
                .orElseGet(() -> (statisticConfig.isTenantIdEnable() && statisticConfig.openTotal())
                        ? SecurityUtils.getTenantId() : null);
        if (StringUtils.isNotEmpty(tenantId) && Convert.toLong(tenantId, 0L) > 0) {
//            lqw.eq(Record::getTenantId, tenantId);
            queryReq.setTenantIds(Lists.newArrayList(tenantId));
        }
        if (statisticConfig.isUseOriginEnable() && statisticConfig.openTotal()) {
            List<Integer> originList = new ArrayList<>();
            originList.addAll(Arrays.stream(statisticConfig.getManualOrigins().split(",")).map(Integer::parseInt)
                    .toList());
            originList.addAll(Arrays.stream(statisticConfig.getAutomaticOrigins().split(",")).map(Integer::parseInt)
                    .toList());
//            lqw.in(Record::getOrigin, originList);
            queryReq.setOriginList(originList.stream().map(Integer::longValue).collect(Collectors.toList()));
        } else {
            final Map<String, Integer> originMap = recordOriginConfig.getOriginMap(true);
//            lqw.in(Record::getOrigin, originMap.values());
            queryReq.setOriginList(originMap.values().stream().map(Integer::longValue).collect(Collectors.toList()));
        }

        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRecordCount(queryReq);
        List<SearchAggCountResp> data = listR.getData();

        AtomicReference<Long> recordCount = new AtomicReference<>(0L);
        Optional.ofNullable(data).ifPresent(l -> {
            recordCount.set(l.stream().map(SearchAggCountResp::getCount).mapToLong(Long::longValue).sum());
        });

//        final Long recordCount = recordService.count(lqw);
        DocTotalResp resp = new DocTotalResp();
        resp.setTotal(recordCount.get());
        return resp;
    }

    @Override
    public List<DocStoreCountResp> queryDocStoreCount(DocStoreCountReq req) {

        // 使用传入的租户id 或者 登录的租户id
        final String loginTenantId = SecurityUtils.getTenantId();
        final String lastTenantId = ObjectUtil.equals(loginTenantId, "0") ? null : loginTenantId;
        final String tenantId = Optional.ofNullable(req.getTenantId())
                .orElseGet(() -> (statisticConfig.isTenantIdEnable() && statisticConfig.openStoreCount()) ? lastTenantId
                        : null);

        AggRecordCountQueryReq queryReq = new AggRecordCountQueryReq();
        queryReq.setAggField("origin");

        Collection<Integer> originList = null;
        Set<Integer> automaticOrigins = new HashSet<>();
        if (statisticConfig.isUseOriginEnable() && statisticConfig.openStoreCount()) {
            originList = new ArrayList<>();
            automaticOrigins.addAll(
                    Arrays.stream(statisticConfig.getAutomaticOrigins().split(",")).map(Integer::parseInt)
                            .toList());
            originList.addAll(automaticOrigins);
            originList.addAll(Arrays.stream(statisticConfig.getManualOrigins().split(",")).map(Integer::parseInt)
                    .toList());
        } else {
            final Map<String, Integer> originMap = recordOriginConfig.getOriginMap(true);
            originList = originMap.values();
            automaticOrigins.add(1);
        }

        queryReq.setOriginList(originList.stream().map(Integer::longValue).collect(Collectors.toList()));
        if (StringUtils.isNotEmpty(tenantId) && Convert.toLong(tenantId, 0L) > 0) {
//            lqw.eq(Record::getTenantId, tenantId);
            queryReq.setTenantIds(Lists.newArrayList(tenantId));
        }
        queryReq.setRecordCreateTimeStart(
                cn.hutool.core.date.DateUtil.parse(req.getStartTime(), "yyyy-MM-dd").getTime());
        queryReq.setRecordCreateTimeEnd(cn.hutool.core.date.DateUtil.parse(req.getEndTime(), "yyyy-MM-dd").getTime());
        R<List<SearchAggCountResp>> listR = searchRpcEntrance.aggRecordCount(queryReq);
        List<SearchAggCountResp> data = listR.getData();
        List<DocStoreCountResp> countResps = Optional.ofNullable(data).map(l -> l.stream().map(o -> {
            DocStoreCountResp resp = new DocStoreCountResp();
            resp.setStoreType(o.getId().intValue());
            resp.setCount(o.getCount());
            return resp;
        }).toList()).orElseGet(ArrayList::new);
//        final List<DocStoreCountResp> countResps = recordMapper.queryDocStoreCount(
//                tenantId,
//                originList,
//                cn.hutool.core.date.DateUtil.parse(req.getStartTime(), "yyyy-MM-dd"),
//                cn.hutool.core.date.DateUtil.parse(req.getEndTime(), "yyyy-MM-dd")
//        );

        final Map<Integer, List<Long>> listMap = countResps.stream().collect(Collectors.groupingBy(o -> {
            // 入库方式：1-手动入库，2-自动入库; origin:1-OA
            return automaticOrigins.contains(o.getStoreType()) ? 2 : 1;
        }, Collectors.mapping(DocStoreCountResp::getCount, Collectors.toList())));

        List<DocStoreCountResp> list = new ArrayList<>();
        final DocStoreCountResp resp1 = Optional.ofNullable(listMap.get(1)).map(o -> {
            DocStoreCountResp resp = new DocStoreCountResp();
            resp.setStoreType(1);
            resp.setCount(o.stream().mapToLong(Long::longValue).sum());
            return resp;
        }).orElseGet(() -> {
            DocStoreCountResp resp = new DocStoreCountResp();
            resp.setStoreType(1);
            resp.setCount(0L);
            return resp;
        });
        list.add(resp1);

        final DocStoreCountResp resp2 = Optional.ofNullable(listMap.get(2)).map(o -> {
            DocStoreCountResp resp = new DocStoreCountResp();
            resp.setStoreType(2);
            resp.setCount(o.stream().mapToLong(Long::longValue).sum());
            return resp;
        }).orElseGet(() -> {
            DocStoreCountResp resp = new DocStoreCountResp();
            resp.setStoreType(2);
            resp.setCount(0L);
            return resp;
        });
        list.add(resp2);
        return list;
    }

    @Override
    public List<ThirdPartyCountResp> queryThirdPartyCount(ThirdPartyCountReq req) {
        List<ThirdPartyCountResp> list = new ArrayList<>();
        ThirdPartyCountResp resp = new ThirdPartyCountResp();
        list.add(resp);

        resp.setCount(0L);
        resp.setThirdPartyType(1);
        return list;
    }

    private Long processCountByDate(Long tenantId, String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //更新 txt 文件信息
        try {
            return getBaseMapper().selectCount(Wrappers.<DocProcess>query().lambda()
                    .between(DocProcess::getCreateTime, sdf.parse(startTime), sdf.parse(endTime))
                    .eq(tenantId != null, DocProcess::getTenantId, tenantId)
//                    .eq(DocProcess::getDeleted, 1)
                    .eq(DocProcess::getProcessStatus, 5).notIn(DocProcess::getOrigin, 5, 6));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private DocProcessResp getResp(DocProcessQueryBaseReq req, String userId, Map<Long, Long> recordRelCountMap,
            Table<Long, Long, Set<String>> planIdSetMap, Map<Long, String> finalRecordTypeMap, DocProcess o,
            Node node) {
        final DocProcessResp resp = new DocProcessResp();
        resp.setRecordId(o.getRecordId());
        resp.setName(o.getName());
        resp.setTitle(o.getTitle());
        resp.setRecordTypeName(finalRecordTypeMap.get(o.getRecordTypeId()));
        resp.setProcessStatus(o.getProcessStatus());
        resp.setProcessStatusName(DocProcessStatusEnums.ProcessStatusEnum.getDesc(o.getProcessStatus()));
        resp.setProcessSubStatus(o.getProcessSubStatus());
        resp.setProcessSubStatusName(DocProcessStatusEnums.ProcessSubStatusEnum.getDesc(o.getProcessSubStatus()));
        resp.setOrigin(o.getOrigin());
        resp.setCreateByName(o.getCreateByName());
        resp.setUploadTime(o.getCreateTime());
        resp.setStoreWay(o.getStoreWay());
        resp.setKnowledgeStatus(o.getKnowledgeStatus());
        resp.setClassified(o.getClassified());
        resp.setRecordTypeId(o.getRecordTypeId());
        resp.setCreateBy(o.getCreateBy());

        if (DocProcessStatusEnums.ProcessStatusEnum.PS_PENDING_REVIEW.getCode() == o.getProcessStatus()) {
            if (planIdSetMap.contains(o.getBatchId(), o.getPlanId())) {
                final Set<String> idSet = planIdSetMap.get(o.getBatchId(), o.getPlanId());
                resp.setAuditUser(idSet.contains(userId));
            } else {
                final Set<String> idSet = nodeCacheUtil.getUserIdListByBatchId(o.getBatchId(), o.getPlanId(),
                        node.getId(), o.getTenantId());
                resp.setAuditUser(idSet.contains(userId));
                planIdSetMap.put(o.getBatchId(), o.getPlanId(), idSet);
            }
        }
        resp.setUploadUser(o.getCreateBy().equals(userId));

        if (Objects.isNull(recordRelCountMap)) {
            resp.setRecordRelStatus(req.getRecordRelStatus());
        } else {
            Long aLong = recordRelCountMap.get(resp.getRecordId());
            resp.setRecordRelStatus(Objects.nonNull(aLong) ? 1 : 2);
        }
        return resp;
    }

    @Override
    public boolean isManager(String userId, String tenantId) {
        final R<Boolean> tenantManager = userRpcService.isTenantManager(userId, tenantId);
        return tenantManager.getData();
    }

    @Override
    public List<String> queryAllUserId() {
        return docProcessEsUtil.queryAllUserId();
    }

    @Override
    public void saveOne(DocProcess docProcess) {
        save(docProcess);
    }

    @Override
    public DocProcess getByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return null;
        }
        return lambdaQuery().eq(DocProcess::getRecordId, recordId).one();
    }

    @Override
    public List<DocProcess> listWithWrapper(Collection<Long> recordIds, LambdaQueryWrapper<DocProcess> wrapper) {
        if (CollUtil.isEmpty(recordIds)) {
            return List.of();
        }
        if (Objects.isNull(wrapper)) {
            wrapper = Wrappers.lambdaQuery();
        }
        return list(wrapper.in(DocProcess::getRecordId, recordIds));
    }

    @Override
    public List<DocProcess> listFromDBWithCondition(LambdaQueryWrapper<DocProcess> wrapper) {
        if (Objects.isNull(wrapper) || wrapper.isEmptyOfWhere()) {
            return List.of();
        }
        return list(wrapper);
    }

    @Override
    public void updateByWrapper(Collection<Long> recordIds, LambdaUpdateWrapper<DocProcess> wrapper) {
        if (CollUtil.isEmpty(recordIds) || Objects.isNull(wrapper) || wrapper.isEmptyOfWhere()) {
            return;
        }
        update(wrapper.in(DocProcess::getRecordId, recordIds));
    }

    @Override
    public void deleteByWrapper(Collection<Long> recordIds, LambdaUpdateWrapper<DocProcess> wrapper) {
        if (CollUtil.isEmpty(recordIds)) {
            return;
        }
        if (Objects.isNull(wrapper)) {
            wrapper = Wrappers.lambdaUpdate();
        }
        remove(wrapper.in(DocProcess::getRecordId, recordIds));
    }
}
