package com.suwell.plss.record.permission.validator;

import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class ValidateContext implements Serializable {

    @Serial
    private static final long serialVersionUID = -4425861986710096220L;

    /**
     * 默认使用基础通用校验
     */
    private boolean useBaseValid = true;

    /**
     * 强行pass
     */
    private boolean passAll;

    /**
     * 有错误
     */
    private boolean hasError;

    /**
     * 是否仅验证管理员权限
     */
    private boolean onlyManagerValid;

    /**
     * 错误信息
     */
    private String errorMsg;

    public void hasError(String errorMsg) {
        this.hasError = true;
        this.errorMsg = errorMsg;
        this.passAll = false;
    }

    public void pass() {
        this.passAll = true;
        this.useBaseValid = false;
        this.hasError = false;
    }

    public void managerValid(boolean onlyManagerValid) {
        this.onlyManagerValid = onlyManagerValid;
    }

}
