package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.common.web.controller.BaseController;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.domain.RcWatermarkSettingDTO;
import com.suwell.plss.record.domain.RepoWatermarkSettingDTO;
import com.suwell.plss.record.entity.RcWatermarkSetting;
import com.suwell.plss.record.service.RcWatermarkSettingService;
import com.suwell.plss.record.standard.dto.request.MaskingConfigEnableReq;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import com.suwell.plss.record.standard.dto.response.WatermarkDefaultResp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 水印
 *
 * <AUTHOR>
 * @date 2023年12月20日
 */
@RestController
@RequestMapping("/v1/watermark")
public class WatermarkSettingController extends BaseController {

    @Resource
    private RcWatermarkSettingService watermarkSettingService;

    /**
     * 新增活修改水印
     */
    @Log(title = "'后台-文库管理'" ,businessType = BusinessType.REPO_SECURITY_SETTING)
    @PostMapping("/add")
    public R add(@Validated @RequestBody RepoWatermarkSettingDTO watermarkSetting) {
        return R.ok(watermarkSettingService.add(watermarkSetting));
    }

    /**
     * 根据库id查询水印
     */
//    @Log(title = "查询水印", businessType = BusinessType.)
    @PostMapping("/get")
    public R<RepoWatermarkSettingDTO> get(HttpServletRequest request,@Validated @RequestBody RcWatermarkSettingDTO watermarkSetting) {
        return R.ok(watermarkSettingService.get(request,watermarkSetting.getRepoId()));
    }

    /**
     * 根据库id查询水印开关状态
     */
//    @Log(title = "查询水印", businessType = BusinessType.)
    @PostMapping("/getEnableStatus")
    public R<String> getEnableStatus(@Validated @RequestBody RcWatermarkSetting watermarkSetting) {
        return R.ok(watermarkSettingService.getEnableStatus(watermarkSetting.getRepoId()));
    }

    /**
     * 根据库id查询水印开关状态(新)
     */
//    @Log(title = "查询水印", businessType = BusinessType.)
    @PostMapping("/getEnableStatus/{ctype}")
    public R<String> getEnableStatusNew(@PathVariable("ctype") Integer ctype,@Validated @RequestBody RcWatermarkSettingDTO watermarkSettingDTO) {
        RcWatermarkSetting watermarkSetting = new RcWatermarkSetting();
        BeanUtils.copyProperties(watermarkSettingDTO,watermarkSetting);
        return R.ok(watermarkSettingService.getEnableStatusNew(ctype,watermarkSetting.getRepoId()));
    }


    /**
     * 获取水印的预览内容
     *
     * @param dto
     * @return
     */
    @PostMapping("/preview/content")
    public R<String> previewContent(HttpServletRequest request, @Valid @RequestBody RepoWatermarkSettingDTO dto) {
        return R.ok(watermarkSettingService.previewContent(request,dto));
    }



    /**
     * 配置启用/禁用
     */
    @Log(title = "'后台-文库管理'" ,businessType = BusinessType.REPO_SECURITY_SETTING)
    @PostMapping("/enable")
    public R enable(@RequestBody MaskingConfigEnableReq req) {
        watermarkSettingService.enable(req);
        return R.ok();
    }

    /**
     * 获取默认水印数据配置
     * @return
     */
    @PostMapping("/getDefaultWatermark")
    public R<WatermarkDefaultResp> getDefaultWatermark() {
        return R.ok(watermarkSettingService.getDefaultWatermark());
    }

}
