package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.QueryMetadataViewReq;
import com.suwell.plss.record.standard.dto.response.MetadataSearchResultViewResp;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对接ai中台
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@RestController
@RequestMapping("/v1/butt/joint")
public class RecordButtJointController {

    @Resource
    private StandardRecordTypeFacade standardRecordTypeFacade;

    /**
     * 提供ai中台调用,查询文档类型关联搜索结果展示的元数据
     */
    @PostMapping("/queryMetadataViewByRecordTypes")
    public R<MetadataSearchResultViewResp> queryMetadataViewByRecordTypes(@RequestBody @Validated QueryMetadataViewReq req) {
        return R.ok(standardRecordTypeFacade.queryMetadataViewByRecordTypes(req));
    }
}
