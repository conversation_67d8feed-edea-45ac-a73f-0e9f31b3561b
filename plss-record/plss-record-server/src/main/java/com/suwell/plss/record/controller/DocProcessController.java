package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.*;
import com.suwell.plss.record.standard.service.StandardDocProcessFacade;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文档列表
 *
 * <AUTHOR>
 * @create 2023/11/15
 * @content
 */
@RestController
@RequestMapping("/v1/docProcess")
public class DocProcessController {

    @Resource
    private StandardDocProcessFacade standardDocProcessFacade;

    public static final String MODE_NAME = "'后台-文档入库'";


    /**
     * 文档数量<br/>
     * forSecure: true or false; true:密级  false:非密级
     */
    @PostMapping(value = {"/num/{forSecure}","/num"})
    public R<List<DocProcessNumResp>> queryDocProcessNum(@PathVariable(name = "forSecure",required = false) Boolean forSecure, @RequestParam(required = false) Long batchId) {
        return R.ok(standardDocProcessFacade.queryDocProcessNum(batchId,forSecure));
    }

    /**
     * 处理状态列表
     *
     * @return
     */
    @PostMapping("/status")
    public R<List<DocProcessStatusResp>> queryDocProcessStatus() {
        return R.ok(standardDocProcessFacade.queryDocProcessStatus());
    }

    /**
     * 处理子状态列表
     *
     * @return
     */
    @PostMapping("/subStatus")
    public R<List<DocProcessSubStatusResp>> queryDocProcessSubStatus(@RequestBody @Validated DocProcessStatusReq req) {
        return R.ok(standardDocProcessFacade.queryDocProcessSubStatus(req));
    }

    /**
     * 前台文档状态
     *
     * @return
     */
    @PostMapping("/frontSubStatus")
    public R<List<DocFrontProcessStatusResp>> frontQueryDocProcessSubStatus(@RequestBody @Validated DocProcessFrontStatusReq req) {
        return R.ok(standardDocProcessFacade.frontQueryDocProcessSubStatus(req));
    }

    /**
     * 分页列表V2
     */
    @PostMapping("/pageV2")
    public R<PageUtils<DocProcessResp>> queryPage(@RequestBody DocProcessQueryV2Req req) {
        req.setForSecure(false);
        return R.ok(standardDocProcessFacade.queryPage(req));
    }

    /**
     * 密级分页列表
     */
    @PostMapping("/pageForSecure")
    public R<PageUtils<DocProcessResp>> queryPageForSecure(@RequestBody DocProcessQueryV2Req req) {
        req.setForSecure(true);
        return R.ok(standardDocProcessFacade.queryPage(req));
    }

    /**
     * 前台分页列表
     */
    @PostMapping("/pageFront")
    public R<PageUtils<DocProcessFrontResp>> queryPageFront(@RequestBody @Validated DocProcessFrontQueryReq req) {
        return R.ok(standardDocProcessFacade.queryPageFront(req));
    }

    /**
     * 重试
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/retry")
    public R<Void> retry(@RequestBody @Validated DocProcessRetryReq req) {
        standardDocProcessFacade.retry(req);
        return R.ok();
    }

    /**
     * 批量重试
     */
    @Deprecated
    @PostMapping("/batchRetry")
    public R<Void> batchRetry(@RequestBody @Validated DocProcessBatchRetryReq req) {
        standardDocProcessFacade.batchRetry(req);
        return R.ok();
    }

    /**
     * 批量重试 by recordId
     */
    @PostMapping("/batchRetryByRecordId")
    public R<Void> batchRetry(@RequestBody @Validated DocProcessBatchRetryV2Req req) {
        standardDocProcessFacade.batchRetry(req);
        return R.ok();
    }

    /**
     * 删除
     *
     * @param req
     * @return
     */
    @Log(title = MODE_NAME,businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> delete(@RequestBody @Validated DocProcessDeleteReq req) {
        standardDocProcessFacade.delete(req);
        return R.ok();
    }

    /**
     * 查询入库流程文件状态
     *
     * @param req
     * @return
     */
    @PostMapping("/queryFileState")
    public R<DocStateResp> queryFileState(@RequestBody @Validated DocProcessViewReq req) {
        return R.ok(standardDocProcessFacade.queryFileState(req));
    }

    /**
     * 编辑详情
     *
     * @param req
     * @return
     */
    @PostMapping("/editInfo")
    public R<DocInfoResp> editInfo(@RequestBody @Validated DocProcessViewReq req) {
        return R.ok(standardDocProcessFacade.view(req));
    }

    /**
     * 个人库编辑详情
     *
     * @param req
     * @return
     */
    @PostMapping("/editPersonalInfo")
    public R<DocInfoResp> editPersonalInfo(@RequestBody @Validated DocProcessViewReq req) {
        return R.ok(standardDocProcessFacade.view(req,true));
    }

    /**
     * 编辑
     *
     * @param req
     * @return
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Void> edit(@RequestBody @Validated DocProcessEditReq req) {
        standardDocProcessFacade.edit(req);
        return R.ok();
    }

    /**
     * 文库编辑文件
     *
     * @param req
     * @return
     */
    @PostMapping("/libEdit")
    public R<Void> libEdit(@RequestBody @Validated DocProcessEditReq req) {
        standardDocProcessFacade.libEdit(req);
        return R.ok();
    }

    /**
     * 能否查看
     *
     * @param req
     * @return
     */
    @PostMapping("/canView")
    public R<Boolean> canView(@RequestBody @Validated DocProcessCanViewReq req) {
        return R.ok(standardDocProcessFacade.canView(req));
    }

    /**
     * 查看
     *
     * @param req
     * @return
     */
    @PostMapping("/view")
    public R<DocInfoResp> view(@RequestBody @Validated DocProcessViewReq req) {
        return R.ok(standardDocProcessFacade.view(req));
    }

    /**
     * 上传附件
     *
     * @param req
     * @return
     */
    @PostMapping("/uploadAttachment")
    public R<Void> uploadAttachment(@ModelAttribute @Validated DocProcessAttachmentFileReq req) {
        standardDocProcessFacade.uploadAttachment(req);
        return R.ok();
    }

    /**
     * 审批
     *
     * @param req
     * @return
     */
    @Log(title = MODE_NAME,businessType = BusinessType.CHECK)
    @PostMapping("/check")
    public R<Void> check(@RequestBody @Validated DocProcessCheckReq req) {
        standardDocProcessFacade.check(req);
        return R.ok();
    }


    /**
     * 批量审批
     *
     * @param req
     * @return
     */
    @Log(title = MODE_NAME,businessType = BusinessType.CHECK)
    @PostMapping("/batchCheck")
    public R<Void> batchCheck(@RequestBody @Validated DocProcessBatchCheckReq req) {


        req.setCause("未填写");
        standardDocProcessFacade.batchCheck(req);
        return R.ok();
    }


    /**
     * 驳回原因列表
     * @param req
     * @return
     */
    @PostMapping("/listRejectReason")
    public R<DocRejectReasonResp> listRejectReason(@RequestBody @Validated DocRejectReasonReq req){
        return R.ok(standardDocProcessFacade.listRejectReason(req));
    }

    /**
     * 待拆分查看
     *
     * @param req
     * @return
     */
    @PostMapping("/preSplit")
    public R<DocProcessSplitInfoResp> splitBefore(@RequestBody @Validated DocProcessSplitInfoReq req) {
        return R.ok(standardDocProcessFacade.splitBefore(req));
    }

    /**
     * 拆分
     *
     * @param req
     * @return
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/split")
    public R<Void> split(@RequestBody @Validated DocProcessSplitReq req) {
        standardDocProcessFacade.split(req);
        return R.ok();
    }

    /**
     * 查看拆分结果
     *
     * @param req
     * @return
     */
    @PostMapping("/splitResult")
    public R<DocProcessSplitResultResp> splitResult(@RequestBody @Validated DocProcessSplitResultReq req) {
        return R.ok(standardDocProcessFacade.splitResult(req));
    }

    /**
     * 删除子文件（拆分结果页）
     *
     * @param req
     * @return
     */
    @PostMapping("/deleteSub")
    public R<Void> deleteSub(@RequestBody @Validated DocProcessDeleteSubReq req) {
        standardDocProcessFacade.deleteSub(req);
        return R.ok();
    }

    /**
     * 替换子文件（拆分结果页）
     *
     * @param req
     * @return
     */
    @PostMapping("/replaceSub")
    public R<Void> replaceSub(@ModelAttribute @Validated DocProcessReplaceSubReq req) {
        standardDocProcessFacade.replaceSub(req);
        return R.ok();
    }

    /**
     * 拆分提交结果
     *
     * @param req
     * @return
     */
    @PostMapping("/splitSubmit")
    public R<Void> splitSubmit(@RequestBody @Validated DocProcessSplitSubmitReq req) {
        req.setCtlFlag(true);
        standardDocProcessFacade.splitSubmit(req);
        return R.ok();
    }

    /**
     * 个人库提交入库
     *
     * @param req
     * @return
     */
    @PostMapping("/submitStore")
    public R<ResourceFrontResp> submitStore(@RequestBody @Validated ResourceFrontReq req) {
        ResourceFrontResp resp = standardDocProcessFacade.submitStore(req);
        return R.ok(resp);
    }

    /**
     * 个人库立即解析
     * @param req
     * @return
     */
    @PostMapping("/submitStoreAtOnce")
    public R<Void> submitStoreAtOnce(@RequestBody @Validated ResourceFrontV2Req req){
        standardDocProcessFacade.submitStoreAtOnce(req);
        return R.ok();
    }

    /**
     * 入库趋势：入库趋势+数量
     *
     * @param req
     * @return
     */
    @PostMapping("/processCountByDate")
    public R<List<DocProcessCountByDateResp>> processCountByDate(@RequestBody @Validated DocProcessCountByDateReq req) {
        return R.ok(standardDocProcessFacade.processCountByDate(req));
    }

    /**
     * 查询基本元数据列表
     * @param req
     * @return
     */
    @PostMapping("/listBaseMetadataInfo")
    public R<BaseMetadataInfoResp> listBaseMetadataInfo(@RequestBody @Validated BaseMetadataInfoReq req){
        return R.ok(standardDocProcessFacade.listBaseMetadataInfo(req));
    }

    /**
     * 批量设置元数据值
     *
     * @param req
     * @return
     */
    @PostMapping("/batchSetMetadataValue")
    public R<Void> batchSetMetadataValue(@RequestBody @Validated BatchSetMetadataValReq req) {
        standardDocProcessFacade.batchSetMetadataValue(req);
        return R.ok();
    }

    /**
     * 批量添加入库位置
     * @param req
     * @return
     */
    @PostMapping("/batchSetRepoPosition")
    public R<Void> batchSetRepoPosition(@Validated @RequestBody RecordAppendRepoPositionReq req){
        standardDocProcessFacade.batchSetRepoPosition(req);
        return R.ok();
    }

    /**
     * 判断指定库是否有待入库文档
     * @param folderId
     * @return
     */
    @PostMapping("/existRecordInRepo/{folderId}")
    public R<Boolean> existRecordInRepo(@PathVariable(name = "folderId") Long folderId) {
        return R.ok(standardDocProcessFacade.hasRecordInRepo(folderId));
    }

    @PostMapping("/getRecordIdsInRepo/{folderId}")
    public R<Set<Object>> getRecordIdsInRepo(@PathVariable(name = "folderId") Long folderId) {
        return R.ok(standardDocProcessFacade.getRecordIdsInRepo(folderId));
    }

    @PostMapping("/removeRecordFromRepo/{folderId}")
    public R<Boolean> removeRecordFromRepo(@PathVariable(name = "folderId") Long folderId, @RequestBody Set<Long> recordIds) {
        standardDocProcessFacade.removeRecordFromRepo(folderId, recordIds);
        return R.ok();
    }
}
