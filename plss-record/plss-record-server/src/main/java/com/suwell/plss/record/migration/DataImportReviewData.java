package com.suwell.plss.record.migration;

import com.suwell.plss.record.entity.*;
import com.suwell.plss.record.entity.Record;
import lombok.Data;

import java.util.List;

@Data
public class DataImportReviewData {
    private List<Record> records;
    private List<Document> documents;
    private List<FileRecord> fileRecords;
    private List<FolderRecord> folderRecords;
    private List<FolderRel> folderRels;
    private List<Repository> repositories;
}
