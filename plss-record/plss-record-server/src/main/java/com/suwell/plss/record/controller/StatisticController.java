package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.standard.dto.request.*;
import com.suwell.plss.record.standard.dto.response.DocStoreCountByOrgResp;
import com.suwell.plss.record.standard.dto.response.DocStoreCountResp;
import com.suwell.plss.record.standard.dto.response.DocTotalResp;
import com.suwell.plss.record.standard.dto.response.HotRecordStatisticResp;
import com.suwell.plss.record.standard.dto.response.HotTagResp;
import com.suwell.plss.record.standard.dto.response.RecordTypeNumResp;
import com.suwell.plss.record.standard.dto.response.RepoCountResp;
import com.suwell.plss.record.standard.dto.response.RepoRecordBulkingResp;
import com.suwell.plss.record.standard.dto.response.RepoRecordCountResp;
import com.suwell.plss.record.standard.dto.response.ThirdPartyCountResp;
import com.suwell.plss.record.standard.service.StandardRecordTypeFacade;
import com.suwell.plss.record.standard.service.StandardStatisticFacade;
import java.util.List;
import java.util.Map;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 统计
 */
@RestController
@RequestMapping("/v1/statistic")
public class StatisticController {

    @Resource
    private StandardStatisticFacade standardStatisticFacade;

    @Resource
    private StandardRecordTypeFacade recordTypeFacade;

    /**
     * 根据库分类统计各库数量
     */
    @PostMapping("/repoCountForCategory")
    public R<List<RepoCountResp>> repoCountForCategory(@RequestBody RepoCountReq req) {
        List<RepoCountResp> list = standardStatisticFacade.repoCountForCategory(req);
        return R.ok(list);
    }


    /**
     * 统计库文档增量趋势
     */
    @PostMapping("/repoRecordBulking")
    public R<List<RepoRecordBulkingResp>> repoRecordBulking(@RequestBody RepoRecordBulkingReq req) {
        List<RepoRecordBulkingResp> list = standardStatisticFacade.repoRecordBulking(req);
        return R.ok(list);
    }


    /**
     * 统计库内文件数量
     */
    @PostMapping("/repoRecordCount")
    public R<List<RepoRecordCountResp>> repoRecordCount(@RequestBody RepoRecordCountReq req) {
        List<RepoRecordCountResp> list = standardStatisticFacade.repoRecordCount(req);
        return R.ok(list);
    }

    /**
     * 热文浏览统计
     */
    @PostMapping("/hotRecords")
    public R<List<HotRecordStatisticResp>> hotRecords(@RequestBody HotRecordStatisticReq req) {
        return R.ok(standardStatisticFacade.hotRecordList(req));
    }

    /**
     * 文档标签总量
     */
    @PostMapping("/recordTags")
    public R<Long> recordCountTags() {
        Long tags = standardStatisticFacade.countRecordTags();
        return R.ok(tags);
    }

    /**
     * 热门标签
     */
    @PostMapping("/hotTags")
    public R<List<HotTagResp>> hotTags(@RequestBody HotTagReq req) {
        return R.ok(standardStatisticFacade.countHotTags(req));
    }

    /**
     * 热门标签下的文档
     */
    @PostMapping("/hotTagRecords")
    public R<List<HotRecordStatisticResp>> hotTags(@RequestBody HotRecordStatisticReq req) {
        Long categoryId = req.getCategoryId();
        if(categoryId == null){
            return R.error("分类标签不能为空");
        }
        return R.ok(standardStatisticFacade.hotTagRecords(req));
    }

//    /**
//     * 热门文档
//     */
//    @PostMapping("/hotTags")
//    public R<Long> hotRecord(@RequestBody HotRecordStatisticReq req) {
//        standardStatisticFacade.hotRecord();
//        return R.ok();
//    }


    /**
     * 文档总数
     */
    @PostMapping("/docTotal")
    public R<DocTotalResp> docTotal(@RequestBody DocTotalReq req) {
        return R.ok(standardStatisticFacade.docTotal(req));
    }

    /**
     * 按照入库方式统计文档数量
     *
     * @param req
     * @return
     */
    @PostMapping("/docStoreCount")
    public R<List<DocStoreCountResp>> docStoreCount(@RequestBody DocStoreCountReq req) {
        return R.ok(standardStatisticFacade.docStoreCount(req));
    }

    /**
     * 按照第三方对接调用次数统计
     *
     * @param req
     * @return
     */
    @PostMapping("/thirdPartyCount")
    public R<List<ThirdPartyCountResp>> thirdPartyCount(@RequestBody ThirdPartyCountReq req) {
        return R.ok(standardStatisticFacade.thirdPartyCount(req));
    }

    /**
     * 数据大屏获取各文档类型的文件数量占总量的百分比
     */
    @PostMapping("/recordPercent")
    public R<List<RecordTypeNumResp>> getRecordPercent(@RequestBody(required = false) Map<String,String> param) {
        String tenantId = param.getOrDefault("tenantId","");
        return R.ok(recordTypeFacade.getRecordPercent(tenantId));
    }


    /**
     * 数据大屏获取日访问量统计信息
     */
    @PostMapping("/visitInfo")
    public R<List<Map<String,Object>>> getVisitInfo(@RequestBody VisitInfoCountReq req) {
        return R.ok(standardStatisticFacade.getVisitInfo(req));
    }


    /**
     * 按内蒙组织架构统计入库数量
     */
    @PostMapping("/storeCountByNmgOrg")
    public R<List<DocStoreCountByOrgResp>> getStoreCountByOrg(@RequestBody StoreCountByOrgReq req) {
        return R.ok(standardStatisticFacade.getStoreCountByOrg(req));
    }


    /**
     * 每个单位/部门下的入库文档量:统计单位/部门的文档入库量的排名
     */
    @PostMapping("/storeRankStatisticByOrg")
    public R<List<StoreRankStatisticByOrgResp>> storeRankStatisticByOrg(@RequestBody StoreRankStatisticByOrgReq req) {
        return R.ok(standardStatisticFacade.storeRankStatisticByOrg(req));
    }
}
