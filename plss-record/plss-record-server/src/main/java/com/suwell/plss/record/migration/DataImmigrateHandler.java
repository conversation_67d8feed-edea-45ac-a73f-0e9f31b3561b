package com.suwell.plss.record.migration;
/**
 * 数据分发导入导出抽象处理器  能力: 启动、停止、健康检查
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
public interface DataImmigrateHandler {

    String PATH_PREFIX = "/swdata/builtin/";
//    String PATH_PREFIX = "/Users/<USER>/app/builtin/";

    /**
     * 启动任务
     *
     * @param taskId 任务id
     * @param dataDir 数据目录
     */
    void start(Long taskId, String prefixPath, String dataDir, boolean overwrite, boolean needDelFile);
    /**
     * 停止任务
     *
     * @param taskId 任务id
     * @param dataDir 数据目录
     */
    void stop(Long taskId, String dataDir);

    /**
     * 健康检查
     *
     * @param taskId 任务id
     * @param dataDir 数据目录
     * @return 是否健康运行 true 健康 false 不健康
     */
    boolean health(Long taskId, String dataDir);
}

