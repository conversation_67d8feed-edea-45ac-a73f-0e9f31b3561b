package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.domain.DataRepoInfo;
import com.suwell.plss.record.standard.domain.DataSubscribeInfo;
import com.suwell.plss.record.standard.dto.request.DataLakePageReq;
import com.suwell.plss.record.standard.dto.request.DataLakeSubscribeReq;
import com.suwell.plss.record.standard.dto.response.DataLakeResp;
import com.suwell.plss.record.standard.service.DataSubscribeFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/subscribe")
public class DataSubscribeController {

    @Resource
    private DataSubscribeFacade dataSubscribeFacade;

    /**
     * 获取项目订阅信息
     */
    @GetMapping("/info")
    R<DataSubscribeInfo> info() {
        return R.ok(dataSubscribeFacade.info());
    }

    /**
     * 更新项目订阅信息
     */
    @PostMapping("/updateInfo")
    R<Void> updateInfo(@RequestBody DataSubscribeInfo info) {
        dataSubscribeFacade.updateInfo(info);
        return R.ok();
    }

    /**
     * 检查数据分发服务地址是否可用
     */
    @PostMapping("/check")
    R<Void> checkSubscribeInfo(@RequestBody DataSubscribeInfo info) {
        dataSubscribeFacade.checkSubscribeInfo(info.getSubscribeAddress());
        return R.ok();
    }

    /**
     * 列出项目可用的仓库
     */
    @PostMapping("/listAvailableRepos")
    R<List<DataRepoInfo>> listAvailableRepos(@RequestBody DataSubscribeInfo info) {
        List<DataRepoInfo> list = dataSubscribeFacade.listAvailableRepos(info.getSubscribeAddress(),
                info.getAccessKey());
        return R.ok(list);
    }

    /**
     * 订阅指定日期的数据
     */
    @PostMapping("/subscribeDiffData")
    R<Void> subscribeDiffData(@RequestBody DataLakeSubscribeReq req) {
        dataSubscribeFacade.subscribeDiffData(req);
        return R.ok();
    }

    /**
     * 分页查询文件湖数据
     */
    @PostMapping("/queryPage")
    R<PageUtils<DataLakeResp>> queryPage(@RequestBody DataLakePageReq req) {
        PageUtils<DataLakeResp> page = dataSubscribeFacade.queryPage(req);
        return R.ok(page);
    }

    /**
     * 手动触发同步数据湖数据
     */
    @PostMapping("/syncRemoteDataLake")
    R<Void> queryPage(@RequestBody Integer pageSize) {
        dataSubscribeFacade.syncRemoteDataLake(pageSize);
        return R.ok();
    }


}
