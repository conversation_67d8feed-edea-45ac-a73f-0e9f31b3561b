package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.service.MqRetryMsgService;
import com.suwell.plss.record.standard.dto.request.MetadataRepairManualRetryReq;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * mq重试消息
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@RestController
@RequestMapping("/v1/mq/retry/msg")
public class MqRetryMsgController {

    @Resource
    private MqRetryMsgService mqRetryMsgService;

    /**
     * 元数据修复手动重试
     */
    @PostMapping("/metadataRepair/manualRetry")
    public R<Void> manualRetry(@RequestBody @Validated MetadataRepairManualRetryReq req) {
        mqRetryMsgService.metadataRepairManualRetry(req);
        return R.ok();
    }

}
