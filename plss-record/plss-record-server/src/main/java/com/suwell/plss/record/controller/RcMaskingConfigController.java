package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.domain.BasePageCondition;
import com.suwell.plss.record.standard.dto.request.MaskingConfigAddReq;
import com.suwell.plss.record.standard.dto.request.MaskingConfigEnableReq;
import com.suwell.plss.record.standard.dto.request.RepoMaskingConfigAddReq;
import com.suwell.plss.record.standard.dto.response.MaskingConfigResp;
import com.suwell.plss.record.standard.service.StandardMaskingConfigFacade;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 脱敏配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-21 18:34:47
 */
@RestController
@RequestMapping("v1/maskingConfig")
public class RcMaskingConfigController {

    public static final String MODE_NAME = "'后台-全局敏感词配置'";
    @Resource
    private StandardMaskingConfigFacade maskingConfigFacade;

    /**
     * 列表
     */
    @PostMapping("/page")
    public R<PageUtils<MaskingConfigResp>> list(@RequestBody BasePageCondition req) {
        return R.ok(maskingConfigFacade.page(req));
    }


    /**
     * 信息
     */
    @PostMapping("/info")
    public R<MaskingConfigResp> info(@RequestBody Long id) {
        return R.ok(maskingConfigFacade.info(id));
    }

    /**
     * 库脱敏信息
     */
    @PostMapping("/repoInfo")
    public R<List<MaskingConfigResp>> repoInfo(@RequestBody Long repoId) {
        return R.ok(maskingConfigFacade.repoInfo(repoId));
    }

    /**
     * 保存
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R save(@RequestBody MaskingConfigAddReq req) {
        maskingConfigFacade.save(req);
        return R.ok();
    }

    /**
     * 库保存
     */
    @Log(title = "'后台-文库管理'", businessType = BusinessType.REPO_SECURITY_SETTING)
    @PostMapping("/repoSave")
    public R repoSave(@RequestBody RepoMaskingConfigAddReq req) {
        maskingConfigFacade.repoSave(req);
        return R.ok();
    }

    /**
     * 修改
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody MaskingConfigAddReq req) {
        maskingConfigFacade.update(req);
        return R.ok();
    }

    /**
     * 库修改
     */
    @Log(title = "'后台-文库管理'", businessType = BusinessType.REPO_SECURITY_SETTING)
    @PostMapping("/repoUpdate")
    public R repoUpdate(@RequestBody RepoMaskingConfigAddReq req) {
        maskingConfigFacade.repoUpdate(req);
        return R.ok();
    }


    /**
     * 删除
     */
    @Log(title = MODE_NAME, businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R delete(@RequestBody Long id) {
        maskingConfigFacade.delete(id);
        return R.ok();
    }

    /**
     * 配置启用/禁用
     */
    @Log(title = "'后台-文库管理'", businessType = BusinessType.REPO_SECURITY_SETTING)
    @PostMapping("/enable")
    public R enable(@RequestBody MaskingConfigEnableReq req) {
        maskingConfigFacade.enable(req);
        return R.ok();
    }

}
