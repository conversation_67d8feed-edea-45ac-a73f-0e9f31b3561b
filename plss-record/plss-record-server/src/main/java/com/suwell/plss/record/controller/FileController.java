package com.suwell.plss.record.controller;

import static com.suwell.plss.record.standard.enums.RecordBizError.FILE_DOWNLOAD_COUNT_LIMIT;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.framework.log.enums.OperatorType;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.dto.request.FileTransferReq;
import com.suwell.plss.record.standard.dto.request.ReplaceRecordReq;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.TempUrlResp;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import com.suwell.plss.record.standard.enums.RecordEnum.RecordDownloadType;
import com.suwell.plss.record.standard.service.StandardFileFacade;
import com.suwell.plss.record.util.RecordCommonUtils;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 上传文件
 */
@RestController
@RequestMapping("/v1/file")
public class FileController {

    @Resource
    private StandardFileFacade standardFileFacade;
    @Resource
    private RecordCommonUtils recordCommonUtils;

    /**
     * 通用文件上传
     */
    @PostMapping("/generalUpload")
    public R<UploadResp> generalUpload(@ModelAttribute UploadReq req) {
        UploadResp resp = standardFileFacade.generalUpload(req);
        return R.ok(resp);
    }

    /**
     * 通用文件下载
     */
    @PostMapping("/downloadFile/{fileId}")
    public void downloadFile(@PathVariable("fileId") Long fileId, HttpServletResponse response) {
        standardFileFacade.downloadFile(fileId, response);
    }

    /**
     * 下载record主件文件下载 type 1:主件，2:附件
     */
    @Log(title = "#backend ? #isRepository ? '后台-文库管理' : '后台 - 文件详情' : '前台-文档操作'", businessType = BusinessType.DOCUMENT_DOWNLOAD, operatorType = OperatorType.MANAGE,
            remark = "'下载了'+T(com.suwell.plss.record.standard.enums.RecordEnum.RecordDownloadType).getEnum(#type).getDesc()+':'+#recordName")
    @PostMapping("/downloadRecordFile/{recordId}")
    public void downloadRecordFile(@PathVariable Long recordId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "backend", required = false, defaultValue = "false") Boolean backend,
            @RequestParam(value = "isRepository", required = false, defaultValue = "false") Boolean isRepository,
            @RequestParam(value = "recordName", required = false, defaultValue = "") String recordName,
            @RequestParam(value = "attachmentId", required = false) Long attachmentId,
            HttpServletResponse response) {
//        AssertUtils.isTrue(recordCommonUtils.checkFileDownloadLimit(SecurityUtils.getUserId()),
//                FILE_DOWNLOAD_COUNT_LIMIT);
        standardFileFacade.downloadRecordFile(recordId, type, attachmentId, response);
    }

    /**
     * 下载record主件txt文件下载
     */
    @PostMapping("/downloadTxtFile/{recordId}")
    public void downloadTxtFile(@PathVariable Long recordId, HttpServletResponse response) {
//        AssertUtils.isTrue(recordCommonUtils.checkFileDownloadLimit(SecurityUtils.getUserId()),
//                FILE_DOWNLOAD_COUNT_LIMIT);
        standardFileFacade.downloadRecordFile(recordId, RecordDownloadType.MASTER_TXT.getCode(), response);
    }

    /**
     * 下载record主件doc文件下载
     */
    @PostMapping("/downloadDocFile/{recordId}")
    public void downloadDocFile(@PathVariable Long recordId, HttpServletResponse response) {
        String fileExt = standardFileFacade.typeFileExt(recordId, 1);
        if (StringUtils.isEmpty(fileExt) ||
                (!"doc".equalsIgnoreCase(fileExt) && !"docx".equalsIgnoreCase(fileExt))) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }

        standardFileFacade.downloadRecordFile(recordId, RecordDownloadType.MASTER_ORIGIN.getCode(), response);
    }

    /**
     * 替换record主件文件
     */
    @PostMapping("/replaceRecordFile")
    public R<Void> replaceRecordFile(@RequestParam("file") MultipartFile file,
            @RequestParam("recordId") Long recordId) {
        ReplaceRecordReq req = new ReplaceRecordReq();
        req.setFileData(file);
        req.setRecordId(recordId);
        standardFileFacade.replaceRecordFile(req);
        // 轻阅读要求返回code为1是成功
        R<Void> r = R.ok();
        r.setCode(1);
        return r;
    }

    /**
     * 获取文件基本信息
     */
    @PostMapping("/info/{fileId}")
    public R<UploadResp> getInfo(@PathVariable Long fileId) {
        UploadResp resp = standardFileFacade.getInfo(fileId);
        return R.ok(resp);
    }

    /**
     * 批量获取文件基本信息
     */
    @PostMapping("/batchInfo")
    public R<List<UploadResp>> batchInfo(@RequestBody List<Long> fileIds) {
        List<UploadResp> list = standardFileFacade.batchInfo(fileIds);
        return R.ok(list);
    }

    /**
     * 文件的临时url
     */
    @PostMapping("/tempUrl")
    public R<String> tempUrl(@RequestBody FileTempUrlReq req) {
        String tempUrl = standardFileFacade.tempUrl(req);
        return R.ok(tempUrl);
    }

    /**
     * 批量获取文件的临时url
     */
    @PostMapping("/batchTempUrl")
    public R<List<TempUrlResp>> batchTempUrl(@RequestBody List<FileTempUrlReq> req) {
        List<TempUrlResp> list = standardFileFacade.batchTempUrl(req);
        return R.ok(list);
    }

    /**
     * 根据sk临时访问文件
     */
    @GetMapping("/outAccessFile")
    public void outAccessFile(@RequestParam(value = "sk", required = false) String sk,
            @RequestParam(value = "preview", required = false, defaultValue = "0") Integer preview,
            HttpServletResponse response) {
        standardFileFacade.outAccessFile(sk, preview, response);
    }

    /**
     * 内部文件迁入
     */
    @PostMapping("/transferIn")
    public R<Void> transferIn(@ModelAttribute FileTransferReq req) {
        standardFileFacade.transferIn(req);
        return R.ok();
    }

    /**
     * 内部文件迁出
     */
    @PostMapping("/transferOut/{fileId}")
    public R<Void> transferOut(@PathVariable Long fileId, HttpServletResponse response) {
        standardFileFacade.transferOut(fileId, response);
        return R.ok();
    }

    /**
     * file分表迁移
     */
    @PostMapping("/migrateFile")
    public R<Void> migrateFile() {
        standardFileFacade.migrateFile();
        return R.ok();
    }

    /**
     * file分表迁移
     */
    @PostMapping("/remove/{fileId}")
    public R<Void> removeFile(@PathVariable Long fileId) {
        standardFileFacade.removeFile(fileId);
        return R.ok();
    }

    /**
     * file表分表
     */
    @PostMapping("/reHashFile")
    public R<Void> reHashFile() {
        standardFileFacade.reHashFile();
        return R.ok();
    }

    /**
     * 公文写作-下载record主件文件下载 type 3：原件
     */
    @Log(title = "'前台-文档操作'", businessType = BusinessType.DOCUMENT_DOWNLOAD, operatorType = OperatorType.MANAGE,
            remark = "'公文写作下载了主件原件'+#fileId")
    @GetMapping("/downloadOriginFile")
    public void downloadRecordFile(@RequestParam("_w_fileId") Long fileId,
            @RequestParam("_w_templateId") Long templateId, HttpServletResponse response) {
        standardFileFacade.downloadRecordFileToAIPlatform(fileId, templateId, 3, response);
    }

    /**
     * 公文写作-创建未命名文档
     * @return
     */
    @GetMapping("/createBlankFile/{driveId}")
    public R<String> createBlankFile(@PathVariable String driveId){
        String linkUrl = standardFileFacade.ksoCloudCreateBlankFile(driveId);
        return R.ok(linkUrl);
    }
}
