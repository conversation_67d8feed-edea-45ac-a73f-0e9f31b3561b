package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_METADATA;
import static com.suwell.plss.framework.mq.enums.EventType.GET_TEXT;
import static com.suwell.plss.framework.mq.enums.EventType.GET_TEXT_DOX_DOCX;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.RecordOcrMetadataProcessEvent;
import com.suwell.plss.framework.mq.events.RecordOcrTextDocProcessEvent;
import com.suwell.plss.framework.mq.events.RecordOcrTextProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyOcrTextApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 异步调用 获取正文
 *
 * <AUTHOR>
 * @date 2023/9/18
 */
@Slf4j
@Service
public class ThirdPartyOcrTextAsyncApiImpl implements ThirdPartyOcrTextApi {

    @Resource
    private ApplicationContext context;

    @DSTransactional
    @Override
    public void getOcrText(MqRecordReqDTO mqRecordReqDTO) {
        log.info("异步发送提取正文消息 async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordOcrTextProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_TEXT));
    }

    @Override
    public void getOcrTextDoc(MqRecordReqDTO mqRecordReqDTO) {
        log.info("异步发送提取纯文字的world文档 正文消息 async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordOcrTextDocProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_TEXT_DOX_DOCX));
    }

    @DSTransactional
    @Override
    public void getOcrMetadata(MqRecordReqDTO mqRecordReqDTO) {
        log.info("异步发送提取元数据消息 async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new RecordOcrMetadataProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_METADATA));
    }
}
