package com.suwell.plss.record.controller;

import com.alibaba.fastjson2.JSON;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.AssertUtils;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.poi.ExcelUtil;
import com.suwell.plss.framework.log.annotation.Log;
import com.suwell.plss.framework.log.enums.BusinessType;
import com.suwell.plss.record.standard.dto.request.SensitiveCategoryAddReq;
import com.suwell.plss.record.standard.dto.request.SensitiveCategoryImportReq;
import com.suwell.plss.record.standard.dto.request.SensitiveCategoryQueryReq;
import com.suwell.plss.record.standard.dto.response.SensitiveCategoryResp;
import com.suwell.plss.record.standard.service.StandardSensitiveCategoryFacade;
import com.suwell.plss.system.api.enums.SystemBizError;
import java.io.InputStream;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-21 18:34:36
 */
@RestController
@RequestMapping("v1/sensitiveCategory")
@Slf4j
public class RcSensitiveCategoryController {

    @Resource
    private StandardSensitiveCategoryFacade sensitiveCategoryFacade;

    public static final String MODE_NAME = "'后台-敏感词库'";



    /**
     * 分页列表
     */
    @PostMapping("/page")
    public R<PageUtils<SensitiveCategoryResp>> pageList(@RequestBody SensitiveCategoryQueryReq req) {
        return R.ok(sensitiveCategoryFacade.page(req));
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    public R<List<SensitiveCategoryResp>> getList() {
        return R.ok(sensitiveCategoryFacade.getList());
    }

    /**
     * 创建分类
     */
    @Log(title = MODE_NAME, businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R creat(@RequestBody SensitiveCategoryAddReq req) {
        sensitiveCategoryFacade.save(req);
        return R.ok();
    }

    /**
     * 修改分类
     */
    @Log(title = MODE_NAME, businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody SensitiveCategoryAddReq req) {
        sensitiveCategoryFacade.update(req);
        return R.ok();
    }

    /**
     * 详情
     */
    @PostMapping("/info")
    public R<SensitiveCategoryResp> info(@RequestBody Long id) {
        return R.ok(sensitiveCategoryFacade.info(id));
    }

    /**
     * 删除
     */
    @Log(title = MODE_NAME,businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R delete(@RequestBody Long id) {
        sensitiveCategoryFacade.delete(id);
        return R.ok();
    }

    /**
     * 敏感词导入
     */
    @Log(title = MODE_NAME, businessType = BusinessType.IMPORT, isSaveResponseData = false)
    @PostMapping("/importSensitiveWord")
    public R importSensitiveWord(@RequestParam("file") MultipartFile file) {
        String regex = ".*\\.(xlsx|xls|et)";
        AssertUtils.isTrue(file.getOriginalFilename().matches(regex), SystemBizError.CATEGORY_TEMPLATE_ERROR);

        log.info("文件类型:{}", file.getOriginalFilename());
        ExcelUtil<SensitiveCategoryImportReq> util = new ExcelUtil<>(SensitiveCategoryImportReq.class);
        List<SensitiveCategoryImportReq> recordTypeMetadataAddReqList;
        try (InputStream inputStream = file.getInputStream()) {
            recordTypeMetadataAddReqList = util.importExcel(inputStream);
            log.info("excl rsp:{}", JSON.toJSONString(recordTypeMetadataAddReqList));
        } catch (Exception e) {
            throw new RuntimeException();
        }
        sensitiveCategoryFacade.importSensitiveWord(recordTypeMetadataAddReqList);
        return R.ok();

    }

    /**
     * 导出模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SensitiveCategoryImportReq> util = new ExcelUtil<SensitiveCategoryImportReq>(
                SensitiveCategoryImportReq.class);
        util.exportExcel(response, null, "敏感词导入模板");
    }

}
