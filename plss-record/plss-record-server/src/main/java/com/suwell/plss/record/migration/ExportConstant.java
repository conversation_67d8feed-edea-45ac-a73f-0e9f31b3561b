package com.suwell.plss.record.migration;

public class ExportConstant {

    public static final String SUFFIX_RECORD = ".rc";
    public static final String SUFFIX_DOCUMENT = ".rcd";
    public static final String SUFFIX_DOCUMENT_MD5REL = ".rcd5";
    public static final String SUFFIX_METADATA = ".rcm";
    public static final String SUFFIX_METADATA_CATEGORY = ".rcmc";
    public static final String SUFFIX_METADATA_CATEGORY_METADATA = ".rcmcm";
    public static final String SUFFIX_METADATA_VALUE = ".rcmv";
    public static final String SUFFIX_RECORD_METADATA_VALUE = ".rmv";
    public static final String SUFFIX_FILE = ".rcf";
    public static final String SUFFIX_FILE_MD5REL = ".rcf5";

    public static final String SUFFIX_RECORD_KNOWLEDGE = ".rk";
    public static final String SUFFIX_RECORD_KNOWLEDGE_ATTRIBUTE = ".rka";
    public static final String SUFFIX_RECORD_KNOWLEDGE_RELATION = ".rkr";
    public static final String SUFFIX_RECORD_KNOWLEDGE_CONFLICT = ".rkc";

    public static final String SUFFIX_REPOSITORY = ".rep";
    public static final String SUFFIX_FOLDER = ".fd";
    public static final String SUFFIX_RESOURCE_MANAGER = ".rm";
    public static final String SUFFIX_FOLDER_RECORD = ".fdrc";
    public static final String SUFFIX_FOLDER_REL = ".fdr";
    public static final String SUFFIX_REPO_RECORD = ".repr";

    public static final String SUFFIX_RECORD_TYPE = ".rt";
    public static final String SUFFIX_RECORD_TYPE_METADATA = ".rtm";

    public static final String SUFFIX_CATEGORY = ".cat";
    public static final String SUFFIX_CATEGORY_REL = ".catr";
    public static final String SUFFIX_RECORD_REL = ".rcr";
    public static final String SUFFIX_ES_RECORD = ".es";
    public static final String SUFFIX_ES_VECTOR_RECORD = ".esx";

    public static final String NLP_RECORD = "nlp_record";
    public static final String NLP_RECORD_VECTOR = "nlp_record_vector";

    public static final String AES_KEY = "suwellplss123456";

    public static final Integer MULTIPLE_NUMBER = 20;
    public static final Double MULTIPLE_SECOND = 0.12;
    public static final Double MULTIPLE_SIZE = 1.35;
}
