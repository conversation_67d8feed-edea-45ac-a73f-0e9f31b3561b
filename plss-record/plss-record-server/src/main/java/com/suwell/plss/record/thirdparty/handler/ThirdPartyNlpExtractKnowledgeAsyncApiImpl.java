package com.suwell.plss.record.thirdparty.handler;

import static com.suwell.plss.framework.mq.enums.EventType.GET_KNOWLEDGE;

import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.suwell.plss.framework.mq.dto.process.MqRecordReqDTO;
import com.suwell.plss.framework.mq.events.NodeKnowledgeExtractProcessEvent;
import com.suwell.plss.record.thirdparty.ThirdPartyNlpExtractKnowledgeApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ThirdPartyNlpExtractKnowledgeAsyncApiImpl implements ThirdPartyNlpExtractKnowledgeApi {

    @Resource
    private ApplicationContext context;

    /**
     * 知识提取
     *
     * @param mqRecordReqDTO 文件消息体
     */
    @DSTransactional
    @Override
    public void getKnowledge(MqRecordReqDTO mqRecordReqDTO) {
        log.info("async req:{}", JSON.toJSONString(mqRecordReqDTO));
        context.publishEvent(new NodeKnowledgeExtractProcessEvent<>(mqRecordReqDTO, mqRecordReqDTO.getPriorityValue(), GET_KNOWLEDGE));
    }

}
