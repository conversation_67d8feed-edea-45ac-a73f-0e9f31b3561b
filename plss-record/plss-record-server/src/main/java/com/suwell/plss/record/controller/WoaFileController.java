package com.suwell.plss.record.controller;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.record.entity.WoaUploadFileOut;
import com.suwell.plss.record.service.FileService;
import com.suwell.plss.record.service.WoaFileService;
import com.suwell.plss.record.standard.dto.request.UploadReq;
import com.suwell.plss.record.standard.dto.response.UploadResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */

@RestController
@RequestMapping("/v1/ai")
public class WoaFileController {

    @Resource
    private WoaFileService woaFileService;

    @Resource
    private FileService fileService;

    /**
     * woa上传文件并打开
     *
     * @param multipartFile
     * @param parentId
     * @return
     */
    @PostMapping("/file/woa/upload")
    public R<WoaUploadFileOut> woaUploadFile(MultipartFile multipartFile) {
        if(multipartFile == null){
            return R.error("请上传文件");
        }
        UploadReq uploadReq = new UploadReq();
        uploadReq.setFileData(multipartFile);
        uploadReq.setOriginName(multipartFile.getOriginalFilename());
        UploadResp uploadResp = fileService.generalUpload(uploadReq);
        String onlineUrl = woaFileService.getOnlineUrl(uploadResp);
        WoaUploadFileOut out = new WoaUploadFileOut();
        out.setId(uploadResp.getId());
        out.setOnlineUrl(onlineUrl);
        return R.ok(out);
    }
}
