package com.suwell.plss.record.permission.handler;

import com.suwell.plss.framework.datapermission.enums.ResourceType;
import com.suwell.plss.record.entity.Repository;
import com.suwell.plss.record.permission.validator.DataPermissionValidator;
import com.suwell.plss.record.permission.validator.ValidateContext;
import com.suwell.plss.record.standard.enums.PermissionMask;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RepositoryValidateHandler extends DataPermissionValidator {

    @Override
    public void postProcessBeforeHandle(ValidateContext context, Long repoId, PermissionMask... basePermission) {
        Repository repository = repositoryService.lambdaQuery()
                .select(Repository::getOwnerId)
                .eq(Repository::getId, repoId).one();
        if (Objects.isNull(repository)) {
            context.hasError("库为空，刷新后重试");
            return;
        }
        // 如果当前登录人是owner对库有控制权
        if (context.isOnlyManagerValid() && super.listResourceVisitorIds().contains(repository.getOwnerId())) {
            context.pass();
        }
        log.info("库管理业务自定义前置处理");
    }

    @Override
    public void postProcessAfterHandle(ValidateContext context, Long repoId, PermissionMask... basePermission) {
    }

    @NotNull
    @Override
    public ResourceType getResourceType() {
        return ResourceType.REPOSITORY;
    }

    @Override
    public boolean isPrivateResource(Long resourceId) {
        return false;
    }

    @Override
    public void makePrivate(Long resourceId) {
        // 库没有私有状态。。。
    }

    @Override
    public void cancelPrivate(Long resourceId) {
        // 库没有私有状态。。。
    }

    @Override
    public Integer getClassified(Long resourceId) {
        if (!super.classifiedSwitchConfig.isSafetySwitch()) {
            return null;
        }
        Repository byId = repositoryService.lambdaQuery()
                .select(Repository::getClassified)
                .eq(Repository::getId, resourceId).one();
        return Objects.nonNull(byId) ? byId.getClassified() : null;
    }

}
