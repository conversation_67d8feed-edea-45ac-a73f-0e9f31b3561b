<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordShareObjectMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordShareObject" id="rcRecordShareObjectMap">
        <result property="shareId" column="share_id"/>
        <result property="shareObjectId" column="share_object_id"/>
        <result property="shareObjectType" column="share_object_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>