<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RepositoryMapper">

    <select id="pageQuery" resultType="com.suwell.plss.record.entity.Repository">
        SELECT
            a.*, b.create_time collectTime
        FROM rc_repository a
        LEFT JOIN rc_repo_collect b ON a.id = b.repo_id
        <if test="userId != null">AND b.user_id = #{userId}</if>
        <where>
            AND a.ctype != 3
            <if test="repoId != null">AND a.id = #{repoId}</if>
            <if test="classified != null">AND a.classified &gt;= #{classified}</if>
            <if test="tenantId != null">AND a.tenant_id = #{tenantId}</if>
            <if test="status != null">AND a.status = #{status}</if>
            <if test="ownerIds != null and ownerIds.size() > 0">AND a.owner_id IN
                <foreach collection="ownerIds" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="repositoryName != null and repositoryName != ''">AND a.name LIKE CONCAT('%',#{repositoryName},'%') </if>
            <if test="shareType != null">AND a.share_type = #{shareType}</if>
            <if test="ctype != null">AND a.ctype = #{ctype}</if>
            <if test="ctypeList != null and ctypeList.size() > 0">AND a.ctype IN
                <foreach collection="ctypeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="tagIds != null and tagIds.size() > 0">AND a.id IN
                (SELECT repo_id FROM rc_repo_metadata WHERE type = 3 AND JSON_VALUE IN
                <foreach collection="tagIds" open="(" close=")" item="item" separator=",">#{item}</foreach>
                )
            </if>
            <choose>
                <when test="queryType == 2 || queryType == 4">AND a.id IN
                    (SELECT resource_id FROM rc_resource_manager WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    )
                </when>
                <when test="queryType == 3">AND a.id IN
                    (SELECT resource_id FROM rc_resource_permission WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    UNION
                    SELECT resource_id FROM rc_resource_manager WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    )
                </when>
            </choose>
        </where>
        ORDER BY CASE WHEN b.create_time IS NULL THEN 0 ELSE 1 END DESC, b.create_time DESC, a.id DESC
    </select>

    <select id="groupCtype" resultType="com.suwell.plss.record.standard.dto.response.RepoCtypeGroupResp">
        SELECT
        ctype, COUNT(*) AS count
        FROM rc_repository
        <where>
            AND ctype != 3
            <if test="ownerIds != null and ownerIds.size() > 0">AND owner_id IN
                <foreach collection="ownerIds" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
            <if test="ctypeList != null and ctypeList.size() > 0">AND ctype IN
                <foreach collection="ctypeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <choose>
                <when test="queryType == 2 || queryType == 4">AND id IN
                    (SELECT resource_id FROM rc_resource_manager WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    )
                </when>
                <when test="queryType == 3">AND id IN
                    (SELECT resource_id FROM rc_resource_permission WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    UNION
                    SELECT resource_id FROM rc_resource_manager WHERE rtype = 1 AND visitor_id IN
                    <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    )
                </when>
            </choose>
        </where>
        GROUP BY ctype
    </select>

    <select id="listRepoTags" resultType="com.suwell.plss.record.standard.dto.response.RepoTagResp">
        SELECT DISTINCT
            a.json_value id
        FROM rc_repo_metadata a
        <if test="ctype != null">
            JOIN rc_repository b ON a.repo_id = b.id
        </if>
        <where>
            AND a.type = 3
            <if test="ctype != null">AND b.ctype = #{ctype}</if>
        </where>
        GROUP BY a.json_value
        ORDER BY a.json_value DESC
    </select>

    <select id="statisticRepoCount" resultType="com.suwell.plss.record.standard.dto.response.RepositoryCountResp">
        SELECT
        COUNT(CASE WHEN share_type = 1 THEN 1 END) AS sharedCount,
        COUNT(CASE WHEN share_type = 2 THEN 1 END) AS nonSharedCount
        FROM rc_repository
        <where>
            AND ctype != 3
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
        </where>
    </select>

    <select id="repoSearchManage" resultType="com.suwell.plss.record.standard.dto.response.ResourceSearchResp">
        SELECT DISTINCT
        a.id resourceId, a.name resourceName,
        a.ctype, a.share_type,
        a.owner_id, a.id repoId, 1 resourceType,
        c.create_time collectTime
        FROM rc_repository a
        JOIN rc_resource_manager b ON a.id = b.resource_id
        LEFT JOIN rc_repo_collect c ON a.id = c.repo_id
        <if test="visitorIds != null and visitorIds.size() > 0">AND c.user_id IN
            <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <where>
            a.ctype !=3 AND b.rtype = 1
            <if test="resourceName != null and resourceName != ''">AND a.name LIKE CONCAT('%', #{resourceName}, '%')</if>
            <if test="visitorIds != null and visitorIds.size() > 0">AND b.visitor_id IN
                <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="ctypeList != null and ctypeList.size() > 0">AND a.ctype IN
                <foreach collection="ctypeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
        </where>
        order by a.id
    </select>

    <select id="repoSearchFolder" resultType="com.suwell.plss.record.standard.dto.response.ResourceSearchResp">
        SELECT
        a.id resourceId, a.name resourceName,
        a.repo_id, a.visit_type, 2 resourceType,
        d.create_time collectTime
        FROM rc_folder a
        JOIN rc_repository c ON a.repo_id = c.id
        LEFT JOIN rc_repo_collect d ON c.id = d.repo_id
        <if test="visitorIds != null and visitorIds.size() > 0">AND d.user_id IN
            <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <where>
            c.ctype !=3
            <if test="ctypeList != null and ctypeList.size() > 0">AND c.ctype IN
                <foreach collection="ctypeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="resourceName != null and resourceName != ''">AND a.name LIKE CONCAT('%', #{resourceName}, '%')</if>
            <if test="visitorIds != null and visitorIds.size() > 0">AND a.repo_id IN (
                SELECT resource_id FROM rc_resource_manager
                WHERE rtype = 1 AND visitor_id IN
                <foreach collection="visitorIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                )
            </if>
        </where>
        order by a.id
    </select>


    <select id="storeStatisticByOrg" resultType="com.suwell.plss.record.standard.dto.request.StoreRankStatisticByOrgResp">
        SELECT
            c.org_id as orgId, COUNT(b.record_id) as num
        FROM
            rc_folder_rel a
                JOIN rc_folder_record b ON a.descendant_id = b.folder_id
                JOIN rc_repository c ON c.id = a.ancestor_id
        WHERE c.org_id IS NOT NULL and c.ctype in(1,2)
        <if test="tenantId != null">
            and c.tenant_id = #{tenantId}
        </if>
        GROUP BY c.org_id
    </select>

    <select id="listByIdsSortByCollect"
            resultType="com.suwell.plss.record.standard.dto.response.RepositoryResp">
        SELECT
        a.*
        FROM rc_repository a
        LEFT JOIN rc_repo_collect b ON a.id = b.repo_id
        <if test="userId != null">AND b.user_id = #{userId}</if>
        <where>
            AND a.ctype != 3
            <if test="repoIds != null and repoIds.size() > 0">AND a.id IN
                <foreach collection="repoIds" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
        </where>
        ORDER BY CASE WHEN b.create_time IS NULL THEN 0 ELSE 1 END DESC,a.share_type DESC, a.id DESC
    </select>

</mapper>