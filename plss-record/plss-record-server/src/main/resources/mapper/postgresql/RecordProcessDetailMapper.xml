<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordProcessDetailMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordProcessDetail" id="recordProcessDetailMap">
        <result property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="recordName" column="record_name"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="recordProcessType" column="record_process_type"/>
        <result property="recordProcessStatus" column="record_process_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
    </resultMap>

</mapper>