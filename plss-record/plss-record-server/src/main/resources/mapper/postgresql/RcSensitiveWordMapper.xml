<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RcSensitiveWordMapper">

    <select id="querySensitiveWordList" resultType="string">
        select distinct w.name from rc_sensitive_category_word cw
        inner join rc_sensitive_word w on cw.word_id = w.id
        where cw.del_flag = '1' and w.status = 1 and w.del_flag = '1' and w.name in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


</mapper>