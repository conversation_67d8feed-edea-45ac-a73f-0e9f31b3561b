<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.TaskBatchMapper">

<!--    <delete id="deleteByRecordList">-->
<!--        delete from rc_task_batch-->
<!--        where id in (-->
<!--        select batch_id from rc_doc_process-->
<!--        where record_id in-->
<!--        <foreach collection="recordIds" open="(" close=")" separator="," item="item">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        )-->
<!--    </delete>-->
<!--    <select id="selectTaskBatchPage" resultType="com.suwell.plss.record.entity.TaskBatch">-->
<!--        select b.*-->
<!--        from rc_task_batch b-->
<!--        <where>-->
<!--            and EXISTS (select * from rc_doc_process c where c.tenant_id = #{tenantId} and c.deleted = 1-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(userIds)">-->
<!--                and c.create_by in <foreach collection="userIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(processStatusList)">-->
<!--                and c.process_status in <foreach collection="processStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            and c.batch_id = b.id-->
<!--            <if test="req.forSecure == true">-->
<!--                <choose>-->
<!--                    <when test="@cn.hutool.core.collection.CollUtil@isEmpty(req.classifiedList)">-->
<!--                        and c.classified is not null-->
<!--                    </when>-->
<!--                    <otherwise>-->
<!--                        and c.classified in <foreach collection="req.classifiedList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--                    </otherwise>-->
<!--                </choose>-->
<!--            </if>-->
<!--            <if test="req.forSecure == false">-->
<!--                and c.classified is null-->
<!--            </if>-->
<!--            )-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.planIdList)">-->
<!--                and b.plan_id in <foreach collection="req.planIdList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="req.origin != null">-->
<!--                and b.origin= #{req.origin}-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(req.taskName)">-->
<!--                and b.name like CONCAT('%', #{req.taskName}, '%')-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(req.name)">-->
<!--                and b.id in (-->
<!--                select batch_id from rc_doc_process-->
<!--                <where>-->
<!--                    and deleted = 1-->
<!--                    <if test="req.origin != null">-->
<!--                        and origin = #{req.origin}-->
<!--                    </if>-->
<!--                    and title like CONCAT('%', #{req.name}, '%')-->
<!--                </where>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="req.createTimeStart != null">-->
<!--                and b.create_time &gt;= #{req.createTimeStart}-->
<!--            </if>-->
<!--            <if test="req.createTimeEnd != null">-->
<!--                and b.create_time &lt;= #{req.createTimeEnd}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by b.create_time desc-->
<!--    </select>-->

</mapper>