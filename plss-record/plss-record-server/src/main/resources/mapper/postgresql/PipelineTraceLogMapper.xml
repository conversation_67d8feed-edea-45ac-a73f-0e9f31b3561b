<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.PipelineTraceLogMapper">

    <select id="queryIds" resultType="java.lang.Long">
        select a.id from rc_pipeline_trace_log a left join rc_doc_process b on a.record_id = b.record_id
        <where>
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(pStatusList)">
                and b.process_status in <foreach collection="pStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            and b.deleted = 1
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(eCodeList)">
                and a.event_type not in <foreach collection="eCodeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="eTime != null">
                and b.modified_time &lt;= #{eTime}
            </if>
        </where>
        order by a.id asc
        limit #{limitSize}
    </select>
    <select id="queryIdsFromRecord" resultType="java.lang.Long">
        select a.id from rc_pipeline_trace_log a left join rc_record b on a.record_id = b.id
        <where>
            and b.recordtype_id is not null and b.record_status = 400 and b.process_status = 999 and b.store_process = 2
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(eCodeList)">
                and a.event_type not in <foreach collection="eCodeList" open="(" close=")" separator="," item="item">#{item}</foreach>
            </if>
            <if test="eTime != null">
                and a.create_time &lt;= #{eTime}
            </if>
        </where>
        order by a.id asc
        limit #{limitSize}
    </select>
</mapper>