<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.TaskMapper">

    <delete id="deleteByRecordIdList">
        delete from rc_task
        where id in (
        select task_id from rc_doc_process
        where record_id in
        <foreach collection="recordIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        )
    </delete>

</mapper>