<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.Record" id="recordMap">
        <result property="id" column="id"/>
        <result property="recordtypeId" column="recordtype_id"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="digest" column="digest"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="origin" column="origin"/>
        <result property="recordStatus" column="record_status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createByName" column="create_by_name"/>
        <result property="modifiedByName" column="modified_by_name"/>
        <result property="putLibTime" column="put_lib_time"/>
    </resultMap>

    <select id="selectUpUnder" resultType="java.lang.Long">
        (SELECT RA.id
         FROM rc_record RA
         WHERE RA.tenant_id = #{tenantId}
           AND RA.recordtype_id = #{recordTypeId}
           AND RA.id &gt; #{recordId}
           AND RA.create_time &gt;=
               (SELECT RB.create_time
                FROM rc_record RB
                WHERE RB.id = #{recordId}
                  AND RB.tenant_id = #{tenantId}
                  AND RB.recordtype_id = #{recordTypeId})
         ORDER BY RA.create_time ASC, RA.id ASC LIMIT 1)
        UNION ALL
        (SELECT RA.id
         FROM rc_record RA
         WHERE RA.id = #{recordId}
           AND RA.tenant_id = #{tenantId}
           AND RA.recordtype_id = #{recordTypeId})
        UNION ALL
        (SELECT RA.id
         FROM rc_record RA
         WHERE RA.tenant_id = #{tenantId}
           AND RA.recordtype_id = #{recordTypeId}
           AND RA.id &lt; #{recordId}
           AND RA.create_time &lt;=
               (SELECT RB.create_time
                FROM rc_record RB
                WHERE RB.id = #{recordId}
                  AND RB.tenant_id = #{tenantId}
                  AND RB.recordtype_id = #{recordTypeId})
         ORDER BY RA.create_time DESC, RA."id" DESC LIMIT 1)
    </select>


<!--    <select id="queryRecordData"-->
<!--            resultType="com.suwell.plss.record.standard.dto.response.RecordDataResp">-->
<!--        SELECT r.id recordId,r.name recordName,r.recordtype_id recordTypeId,rt.name-->
<!--        recordTypeName,r.put_lib_time uploadTime,r.title-->
<!--        FROM rc_record r-->
<!--        left join rc_record_type rt ON rt.id = r.recordtype_id-->
<!--        left join rc_folder_record fr ON fr.record_id = r.id-->
<!--        left join rc_folder f ON f.id = fr.folder_id-->
<!--        <where>-->
<!--            <if test="req.recordName != null and req.recordName != ''">-->
<!--                AND r.name like concat('%',#{req.recordName},'%')-->
<!--            </if>-->
<!--            <if test="req.uploadTimeStart != null ">-->
<!--                AND r.put_lib_time >= #{req.uploadTimeStart}-->
<!--            </if>-->
<!--            <if test="req.uploadTimeEnd != null">-->
<!--                <![CDATA[   AND r.put_lib_time <= #{req.uploadTimeEnd}  ]]>-->
<!--            </if>-->
<!--            <if test="req.recordTypeId != null">-->
<!--                AND r.recordtype_id = #{req.recordTypeId}-->
<!--            </if>-->
<!--            <if test="req.repoIds != null and req.repoIds.size() > 0">-->
<!--                AND f.repo_id in-->
<!--                <foreach collection="req.repoIds" item="repoId" open="(" separator="," close=")">-->
<!--                    #{repoId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        order by r.create_time desc-->
<!--    </select>-->

<!--    <select id="queryRecordTypeCount"-->
<!--            resultType="com.suwell.plss.record.standard.dto.response.RecordTypeNumResp">-->
<!--        select r.recordtype_id as id, count(*) as num-->
<!--        FROM rc_record r-->
<!--        where record_status = 400-->
<!--        and store_process = 2-->
<!--        <if test="tenantId != null">-->
<!--            AND tenant_id = #{tenantId}-->
<!--        </if>-->
<!--        and recordtype_id in-->
<!--        <foreach collection="recordTypeIdList" open="(" close=")" separator="," item="item">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        group by r.recordtype_id-->
<!--    </select>-->

<!--    <select id="exportForRepo" resultType="com.suwell.plss.record.entity.Record">-->
<!--        SELECT-->
<!--        DISTINCT b.*-->
<!--        FROM rc_folder_record a-->
<!--        JOIN rc_record b ON a.record_id = b.id AND b.record_status = 400 AND b.origin = 999-->
<!--        <if test="req.putLibTimeMin != null">-->
<!--            <![CDATA[ AND b.put_lib_time >= #{req.putLibTimeMin} ]]>-->
<!--        </if>-->
<!--        <if test="req.putLibTimeMax != null">-->
<!--            <![CDATA[ AND b.put_lib_time < #{req.putLibTimeMax} ]]>-->
<!--        </if>-->
<!--        <if test="req.repoId != null">-->
<!--            JOIN (SELECT DISTINCT descendant_id FROM rc_folder_rel WHERE ancestor_id =-->
<!--            #{req.repoId}) c ON c.descendant_id = a.folder_id-->
<!--        </if>-->
<!--        ORDER BY b.put_lib_time DESC-->
<!--    </select>-->

<!--    <select id="statisticDocStoreByUserId"-->
<!--            resultType="com.suwell.plss.record.domain.StatisticDocStoreCountDTO">-->
<!--        select create_by as userId, count(1) as count-->
<!--        from rc_record-->
<!--        where record_status = 400-->
<!--          and store_process = 2-->
<!--        group by create_by-->
<!--    </select>-->

<!--    <select id="exportForFolder" resultType="com.suwell.plss.record.entity.Record">-->
<!--        SELECT b.*-->
<!--        FROM rc_folder_record a-->
<!--        JOIN rc_record b ON a.record_id = b.id AND b.record_status = 400 AND b.origin = 999-->
<!--        <if test="req.folderId != null">-->
<!--            AND a.folder_id = #{req.folderId}-->
<!--        </if>-->
<!--        <if test="req.putLibTimeMin != null">-->
<!--            <![CDATA[ AND b.put_lib_time >= #{req.putLibTimeMin} ]]>-->
<!--        </if>-->
<!--        <if test="req.putLibTimeMax != null">-->
<!--            <![CDATA[ AND b.put_lib_time < #{req.putLibTimeMax} ]]>-->
<!--        </if>-->
<!--        <where>-->
<!--            <if test="req.metadataList != null and req.metadataList.size() > 0">AND b.id IN-->
<!--                <foreach collection="req.metadataList" item="item" open="(" separator="UNION"-->
<!--                        close=")">-->
<!--                    SELECT record_id FROM rc_metadata_value-->
<!--                    WHERE md_id=#{item.metadataId} AND md_value-->
<!--                    LIKE CONCAT('%', #{item.metadataValue}, '%')-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY b.put_lib_time DESC-->
<!--    </select>-->

<!--    <select id="countRecordIdRepairMetadata" resultType="java.lang.Long">-->
<!--        SELECT COUNT(1)-->
<!--        FROM rc_record t1-->
<!--        WHERE t1.recordtype_id IN-->
<!--              (SELECT DISTINCT t2.recordtype_id-->
<!--               FROM rc_record_type_metadata t2-->
<!--               WHERE t2.md_id = #{mdId})-->
<!--          AND t1.create_time &lt;= #{stm}-->
<!--    </select>-->

<!--    <select id="queryRecordIdRepairMetadata" resultType="java.lang.Long">-->
<!--        SELECT t1.id-->
<!--        FROM rc_record t1-->
<!--        WHERE t1.recordtype_id IN-->
<!--              (SELECT DISTINCT t2.recordtype_id-->
<!--               FROM rc_record_type_metadata t2-->
<!--               WHERE t2.md_id = #{mdId})-->
<!--          AND t1.create_time &lt;= #{stm}-->
<!--          AND t1.id &lt; #{maxRecordId}-->
<!--        ORDER BY t1.id DESC LIMIT #{pageSize}-->
<!--    </select>-->
<!--    <select id="countResidualRecordIdRepairMetadata" resultType="java.lang.Long">-->
<!--        SELECT COUNT(1)-->
<!--        FROM rc_record t1-->
<!--        WHERE t1.recordtype_id IN-->
<!--              (SELECT DISTINCT t2.recordtype_id-->
<!--               FROM rc_record_type_metadata t2-->
<!--               WHERE t2.md_id = #{mdId})-->
<!--          AND t1.create_time &lt;= #{stm}-->
<!--          AND t1.id &lt; #{maxRecordId}-->
<!--    </select>-->
</mapper>