<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MetadataCategoryMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.MetadataCategory" id="metadataCategoryMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
    </resultMap>

    <select id="selectMetadataCategoryPage"
            resultType="com.suwell.plss.record.domain.MetadataCategoryDTO">
        SELECT mc.id,
        mc.name,
        mc.create_time,
        (SELECT COUNT(1)
        FROM rc_metadata_category_metadata mcm
        WHERE mcm.category_id = mc.id) metadataCount
        FROM rc_metadata_category mc
        <where>
            AND mc.status = 1
            <if test="name != null">
                AND mc.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY mc.create_time ASC
    </select>


</mapper>