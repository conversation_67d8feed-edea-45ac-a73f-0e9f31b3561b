<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.FolderRelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.FolderRel" id="folderRelMap">
        <result property="ancestorId" column="ancestor_id"/>
        <result property="descendantId" column="descendant_id"/>
        <result property="distance" column="distance"/>
        <result property="orderby" column="orderby"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        ancestor_id, descendant_id, distance, orderby, tenant_id, create_by, create_by_name, create_time
    </sql>

    <sql id="Base_Column_List_A">
        a.ancestor_id, a.descendant_id, a.distance, a.orderby, a.tenant_id, a.create_by, a.create_by_name, a.create_time
    </sql>

    <insert id="saveRelation">
        INSERT INTO rc_folder_rel ( ancestor_id, descendant_id, distance, orderby,
                                   tenant_id, create_by, create_by_name, create_time)
        SELECT A.ancestor_id, #{descendantId}, A.distance + 1, #{orderby},
               #{tenantId}, #{createBy}, #{createByName}, to_timestamp(#{createTime},'yyyy-mm-dd hh24:mi:ss')
        FROM
            rc_folder_rel A
        WHERE
            A.descendant_id = #{ancestorId}
        UNION ALL SELECT #{descendantId}, #{descendantId} , 0, #{orderby},
                         #{tenantId}, #{createBy}, #{createByName}, to_timestamp(#{createTime},'yyyy-mm-dd hh24:mi:ss')
    </insert>

    <insert id="graftChildrenFolder">
        INSERT INTO rc_folder_rel(ancestor_id, descendant_id, distance, orderby, tenant_id, create_by, create_by_name, create_time)
        SELECT
            super.ancestor_id,
            sub.descendant_id,
            super.distance + sub.distance + 1,
            sub.orderby,
            sub.tenant_id,
            #{createBy}, #{createByName}, #{createTime}
        FROM
            rc_folder_rel super
            CROSS JOIN rc_folder_rel sub
        WHERE
            super.descendant_id = #{targetFolderId} AND sub.ancestor_id IN
                <foreach collection="fromFolderIdList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </insert>

    <delete id="removeFolderRel">
        DELETE FROM rc_folder_rel
        WHERE descendant_id IN <foreach collection="folderIdList" item="item" open="("  close=")" separator=",">#{item}</foreach>
        OR ancestor_id IN <foreach collection="folderIdList" item="item" open="("  close=")" separator=",">#{item}</foreach>
    </delete>

    <delete id="releaseParentFolder">
        DELETE FROM rc_folder_rel
        WHERE (descendant_id IN <foreach collection="folderIdList" item="item" open="("  close=")" separator=",">#{item}</foreach>
        OR ancestor_id IN <foreach collection="folderIdList" item="item" open="("  close=")" separator=",">#{item}</foreach>)
        AND distance &lt;&gt; 0
    </delete>

    <select id="listDescendantId" resultType="java.lang.Long">
        SELECT
            descendant_id
        FROM rc_folder_rel
        <where>
            <if test="ancestorIds != null and ancestorIds.size() > 0">
                AND ancestor_id IN
                <foreach collection="ancestorIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listAncestorId" resultType="java.lang.Long">
        SELECT
            ancestor_id
        FROM rc_folder_rel
        <where>
            <if test="descendantIds != null and descendantIds.size() > 0">
                AND descendant_id IN
                <foreach collection="descendantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY distance DESC
    </select>

    <select id="queryInFolderRel" resultType="com.suwell.plss.record.entity.FolderRel">
        SELECT
            <include refid="Base_Column_List_A"/>
        FROM rc_folder_rel a
        WHERE (a.ancestor_id, a.descendant_id) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.ancestorId}, #{item.descendantId})
        </foreach>
    </select>

</mapper>