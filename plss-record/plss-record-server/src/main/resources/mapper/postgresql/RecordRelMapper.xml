<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordRelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordRel" id="recordRelMap">
        <result property="referId" column="refer_id"/>
        <result property="recordId" column="record_id"/>
        <result property="ctype" column="ctype"/>
        <result property="distance" column="distance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="referRelName" column="refer_rel_name"/>
        <result property="recordRelName" column="record_rel_name"/>

    </resultMap>

    <select id="selectRecordRelByIds" resultType="java.lang.Long">
        SELECT record_id FROM rc_record_rel
            WHERE refer_id IN
         <foreach collection="recordIdList" open="(" close=")" separator="," item="item">#{item}</foreach>
        UNION
            SELECT refer_id FROM rc_record_rel
            WHERE record_id IN
         <foreach collection="recordIdList" open="(" close=")" separator="," item="item">#{item}</foreach>
    </select>


</mapper>