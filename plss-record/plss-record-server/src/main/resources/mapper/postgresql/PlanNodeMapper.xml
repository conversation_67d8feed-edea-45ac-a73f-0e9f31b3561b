<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.PlanNodeMapper">

    <select id="findPlanNodes" resultType="com.suwell.plss.record.standard.domain.PlanNodeDto">
        select node_id,node_type,defined_config_json as conf_json,order_num as node_order_num
        from rc_plan_node
        where plan_id = #{planId} and status = 1
    </select>
</mapper>