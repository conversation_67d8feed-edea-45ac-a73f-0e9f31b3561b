<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.ResourceManagerMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.ResourceManager">
        <id column="id" property="id" />
        <result column="visitor_id" property="visitorId" />
        <result column="vtype" property="vtype" />
        <result column="resource_id" property="resourceId" />
        <result column="rtype" property="rtype" />
        <result column="manager_type" property="managerType" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, visitor_id, vtype, resource_id, rtype, manager_type, create_by, create_by_name, create_time
    </sql>

    <select id="queryInResourceManager" resultType="com.suwell.plss.record.entity.ResourceManager">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rc_resource_manager
        WHERE (visitor_id, resource_id) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.visitorId}, #{item.resourceId})
        </foreach>
    </select>
</mapper>