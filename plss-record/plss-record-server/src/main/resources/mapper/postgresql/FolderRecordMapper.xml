<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.FolderRecordMapper">
    <resultMap type="com.suwell.plss.record.entity.FolderRecord" id="folderRecordMap">
        <result property="folderId" column="folder_id"/>
        <result property="recordId" column="record_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        folder_id, record_id, create_by, create_by_name, create_time
    </sql>

    <sql id="Base_Column_List_A">
        a.folder_id, a.record_id, a.create_by, a.create_by_name, a.create_time
    </sql>


    <select id="queryFolderIdByRecordId" resultType="java.lang.Long">
        SELECT
            folder_id
        FROM  rc_folder_record
        WHERE record_id IN
        <foreach collection="recordIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="queryRecordIdByFolderId" resultType="java.lang.Long">
        SELECT
            record_id
        FROM  rc_folder_record
        WHERE folder_id IN
        <foreach collection="folderIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="countRepoRecord" resultType="com.suwell.plss.record.entity.FolderRecord">
        SELECT
        a.*, b.ancestor_id repoId
        FROM rc_folder_record AS a
        JOIN rc_folder_rel AS b ON a.folder_id = b.descendant_id
        <where>
            <if test="startTime != null and endTime != null">AND a.create_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="repoIds != null and repoIds.size() > 0">AND b.ancestor_id IN
                <foreach collection="repoIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <select id="queryInFolderRecord" resultType="com.suwell.plss.record.entity.FolderRecord">
        SELECT
            <include refid="Base_Column_List"/>
        FROM rc_folder_record t
        WHERE (t.folder_id, t.record_id) IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                (#{item.folderId}, #{item.recordId})
            </foreach>
    </select>

</mapper>