<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.DocumentMapper">

    <select id="queryDocByRecordIds" resultType="com.suwell.plss.record.domain.RecordFrontDto">
        SELECT
            a.record_id,a.id as docId,
            a.file_id,a.name as fileName,
            b.origin,b.real_origin,a.file_md5
        FROM rc_document a
        JOIN rc_record b ON a.record_id = b.id
        <where>
            a.ctype = 1 AND a.record_id in
            <foreach collection="recordIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND b.id IN
            <foreach collection="recordIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>