<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MaterialMapper">

    <select id="selectMaterialPage" resultType="com.suwell.plss.record.entity.Material">
        SELECT *
        FROM rc_material r
        <where>
            AND r.del_flag = 1
            <if test="req.repId != null and req.repId != ''">
                AND r.rep_id = #{req.repId}
            </if>

            <if test="req.projectRepIds != null and req.projectRepIds.size > 0">
                AND r.rep_id in
                <foreach collection="req.projectRepIds" item="item" open="(" separator=","
                        close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.classifyIds != null and req.classifyIds.size > 0">
                AND EXISTS ( SELECT 1 FROM rc_material_category mc
                WHERE
                mc.material_id = r.id
                AND mc.category_id in
                <foreach collection="req.classifyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.tagIds != null and req.tagIds.size > 0">
                AND EXISTS ( SELECT 1 FROM rc_material_category mc2
                WHERE
                mc2.material_id = r.id
                AND mc2.category_id in
                <foreach collection="req.tagIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.content != null and req.content != '' ">
                AND r.content LIKE CONCAT('%', #{req.content}, '%')
            </if>
            <if test="req.materialType != null and req.materialType != ''">
                AND r.material_type = #{req.materialType}
            </if>
            <if test="req.name != null and req.name != '' ">
                AND r.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
        </where>
        ORDER BY r.modified_time DESC
    </select>

    <select id="selectMaterialPageByCategoryId" resultType="com.suwell.plss.record.entity.Material">
        SELECT *
        FROM rc_material r
        <where>
            AND r.del_flag = 1
            <if test="req.repoId != null and req.repoId != ''">
                AND r.rep_id = #{req.repoId}
            </if>
            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.categoryIdList)">
                and r.id in ( select material_id from rc_material_category where category_id in
                <foreach collection="req.categoryIdList"   item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.searchStr != null and req.searchStr != '' ">
                AND (r.content LIKE CONCAT('%', #{req.searchStr}, '%') or r.name LIKE CONCAT('%', #{req.searchStr}, '%'))
            </if>
            ORDER BY r.modified_time DESC
        </where>
    </select>

    <select id="queryMaterialCountByRepoId" resultType="com.suwell.plss.record.domain.RepoMaterialCountDTO">
        SELECT
            rep_id as repoId, count(1) as count
        FROM rc_material
        WHERE del_flag = 1
        <if test="repoIdList != null and repoIdList.size() > 0">
            AND rep_id in
            <foreach collection="repoIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY rep_id
    </select>
</mapper>