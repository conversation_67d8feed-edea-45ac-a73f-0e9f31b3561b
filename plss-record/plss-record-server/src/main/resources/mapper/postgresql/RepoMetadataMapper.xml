<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RepoMetadataMapper">

    <select id="findExist" resultType="com.suwell.plss.record.entity.RepoMetadata">
        SELECT
            *
        FROM rc_repo_metadata
        <where>
            <if test="repoId != null">AND repo_id = #{repoId}</if>
            <if test="type != null">AND type = #{type}</if>
            <if test="jsonValue != null and jsonValue != ''">AND json_value = #{jsonValue}</if>
        </where>
    </select>

    <select id="listConditionRepoMD" resultType="com.suwell.plss.record.entity.RepoMetadata">
        SELECT
        a.*
        FROM rc_repo_metadata a
        <if test="ctype != null">
            JOIN rc_repository b ON a.repo_id = b.id
        </if>
        <where>
            AND a.type = 3
            <if test="ctype != null">AND b.ctype = #{ctype}</if>
            <if test="categoryId != null">AND a.json_value = '${categoryId}'</if>
        </where>
    </select>


</mapper>