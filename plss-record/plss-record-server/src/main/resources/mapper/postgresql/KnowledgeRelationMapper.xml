<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.KnowledgeRelationMapper">

    <select id="queryRelationList" resultType="com.suwell.plss.record.standard.dto.response.KnowledgeRelationResp">
        select r.id,r.from_concept_id as fromConceptId,c1.name as fromConceptName,r.to_concept_id as toConceptId,
               c2.name as toConceptName,r.name as relationName, r.backward_name as backwardName, r.symmetry AS symmetry
        from rc_knowledge_relation r
        inner join rc_knowledge_concept c1 on r.from_concept_id = c1.id
        inner join rc_knowledge_concept c2 on r.to_concept_id = c2.id
        <where>
            <if test="name != null and name != ''">
                and (r.name like concat('%',#{name},'%') or c1.name like concat('%',#{name},'%') or c2.name like concat('%',#{name},'%'))
            </if>
            <if test="conceptId != null">
                and (r.from_concept_id = #{conceptId} or r.to_concept_id = #{conceptId})
            </if>
        </where>
        order by r.create_time desc
    </select>
</mapper>