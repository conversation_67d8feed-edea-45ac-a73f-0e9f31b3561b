<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.LogRecordViewMapper">

    <select id="queryHotRecords" resultType="com.suwell.plss.record.entity.LogRecordViewCount">
        select record_id as recordId,count(*) as count from rc_log_recordview
        <where>
            <if test="start != null">
                and create_time >= #{start}
            </if>
            <if test="end != null">
                <![CDATA[   and create_time <= #{end} ]]>
            </if>
        </where>
        group by record_id order by count desc limit #{size}
    </select>

</mapper>