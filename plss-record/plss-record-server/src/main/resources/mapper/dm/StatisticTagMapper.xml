<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suwell.plss.record.mapper.StatisticTagMapper">

    <update id="truncateStatisticTag">
        truncate table rc_statistic_tag
    </update>

    <select id="selectCountTagNumber"
            resultType="com.suwell.plss.record.entity.StatisticTag">
        SELECT tag_id as tagId, SUM(tag_num) as tagNum
        FROM rc_statistic_tag
        WHERE tag_modified_time BETWEEN #{beginDate} AND #{endDate}
        GROUP BY tag_id
        ORDER BY tagNum DESC LIMIT 10
    </select>
</mapper>