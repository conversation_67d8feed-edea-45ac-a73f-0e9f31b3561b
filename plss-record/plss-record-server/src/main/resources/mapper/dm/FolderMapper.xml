<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.FolderMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.Folder" id="folderMap">
        <result property="id" column="id"/>
        <result property="repoId" column="repo_id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
    </resultMap>

    <select id="queryAllChildrenFolder" resultType="com.suwell.plss.record.entity.Folder">
        SELECT
            B.*
        FROM rc_folder_rel A
        JOIN rc_folder B ON A.descendant_id = b.id
        WHERE A.ancestor_id IN
        <foreach collection="folderIdList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="countByParentId" resultType="Integer">
        SELECT
        count(b.id)
        <include refid="ByParentId"/>
    </select>

    <select id="listByParentId" resultType="com.suwell.plss.record.entity.Folder">
        SELECT
            b.*
            <include refid="ByParentId"/>
            ORDER BY a.orderby,b.modified_time desc
    </select>

    <sql id="ByParentId">
        FROM rc_folder_rel a JOIN rc_folder b ON a.descendant_id = b.id
        <where>
            a.distance = 1
            <if test="parentId != null"> AND a.ancestor_id = #{parentId}</if>
            <if test="tenantId != null"> AND b.tenant_id = #{tenantId}</if>
            <if test="folderName != null and folderName != ''"> AND b.name = #{folderName}</if>
            <if test='neFolderName != null and neFolderName != ""'> AND b.name != #{neFolderName}</if>
        </where>
    </sql>

    <select id="listByRepoIdAndName" resultType="com.suwell.plss.record.entity.Folder">
        select
            f.repo_id, f.id, f.name
        from rc_folder f
        join rc_folder_rel fr on f.repo_id = fr.ancestor_id and f.id = fr.descendant_id
        <where>
            and f.status = 1
            <if test="folderBasicList != null and folderBasicList.size() > 0">
                and (
                <foreach collection="folderBasicList" item="item" separator=" or ">
                    (f.repo_id = #{item.repoId} and f.name = #{item.name} and fr.distance = #{item.distance})
                </foreach>
                (f.repo_id = 0 and f.name = '*******' and fr.distance = -1)
                )
            </if>
        </where>
    </select>
    <select id="listMyFolderByRepoIds" resultType="com.suwell.plss.record.entity.Folder">
        SELECT
            a.*
        FROM rc_folder a
        JOIN rc_folder_rel b ON a.id = b.descendant_id
        <where>
            a.name = '我的文档'
            AND b.distance = 1
            AND b.ancestor_id IN
            <foreach collection="repoIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </select>

    <select id="listPrivateFolderIds" resultType="java.lang.Long">
        SELECT
            a.id
        FROM rc_folder a
        <where>
            a.visit_type = 2
                AND EXISTS (
                SELECT 1 FROM rc_folder_rel WHERE descendant_id = a.id AND ancestor_id IN
                <foreach collection="folderIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                )
        </where>
    </select>

</mapper>