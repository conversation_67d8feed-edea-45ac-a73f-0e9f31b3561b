<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordShareMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordShare" id="rcRecordShareMap">
        <result property="id" column="id"/>
        <result property="shareRecordId" column="share_record_id"/>
        <result property="shareRecordName" column="share_record_name"/>
        <result property="shareRecordTenantId" column="share_record_tenant_id"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
        <result property="invalidStatus" column="invalid_status"/>
    </resultMap>


    <select id="pageList"
            resultType="com.suwell.plss.record.standard.dto.response.RecordShareResp">
        SELECT
        rrs.id, rrs.share_record_id, rrs.share_record_name,rrs.share_record_tenant_id,rrs.status,
        rrs.del_flag, rrs.create_by,
        rrs.create_by_name, rrs.create_time, rrs.remark,
        rrs.invalid_status
        FROM rc_record_share_object rrso
        left join rc_record_share rrs on rrso.share_id = rrs.id
        <where>
            AND rrs.status = 1 and rrs.del_flag = '1'
            <if test="req.shareObjectId != null and req.shareObjectId > 0">
                AND rrso.share_object_id= #{req.shareObjectId}
            </if>
            <if test="req.shareObjectType != null and req.shareObjectType  > 0">
                AND rrso.share_object_type= #{req.shareObjectType}
            </if>
            <if test="req.shareRecordName != null">
                AND rrs.share_record_name LIKE CONCAT('%', #{req.shareRecordName}, '%')
            </if>
            <if test="req.startTime != null ">
                AND rrs.create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null ">
                AND rrs.create_time &lt;= #{req.endTime}
            </if>

        </where>
        ORDER BY rrs.create_time DESC
    </select>


</mapper>