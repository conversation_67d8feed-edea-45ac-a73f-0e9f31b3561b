<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.ForbiddenRecordRelMapper">

    <select id="getRecordIdList" resultType="java.lang.Long">
        select record_id from rc_record_del_status where record_id in (
        select record_id from rc_forbidden_record_rel where forbidden_word_id = #{forbiddenWordId}
        )
        <if test = "status!=null">
            and status = #{status}
        </if>
    </select>

</mapper>