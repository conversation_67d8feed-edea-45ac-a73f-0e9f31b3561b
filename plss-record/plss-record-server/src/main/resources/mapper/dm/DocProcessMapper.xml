<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.DocProcessMapper">

<!--    <select id="queryDocProcessNum" resultType="com.suwell.plss.record.standard.dto.response.DocProcessNumResp">-->
<!--        select process_status,count(1) as num-->
<!--        from rc_doc_process-->
<!--        where record_id > 0 and tenant_id = #{tenantId}-->
<!--        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(userIds)">-->
<!--            and create_by in <foreach collection="userIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(processStatusList)">-->
<!--            and process_status in <foreach collection="processStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        <if test="batchId != null">-->
<!--            and batch_id = #{batchId}-->
<!--        </if>-->
<!--        <if test="originList != null and originList.size() > 0">-->
<!--            and origin in <foreach collection="originList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        <choose>-->
<!--            <when test="forSecure == true and @cn.hutool.core.collection.CollUtil@isEmpty(classifiedList)">-->
<!--                and classified is not null-->
<!--            </when>-->
<!--            <when test="forSecure == true and @cn.hutool.core.collection.CollUtil@isNotEmpty(classifiedList)">-->
<!--                and classified in <foreach collection="classifiedList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                and classified is null-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        group by process_status-->
<!--    </select>-->

<!--    <select id="queryBatchDocProcessNum" resultType="com.suwell.plss.record.domain.BatchDocProcessNumDto">-->
<!--        select process_status,batch_id,count(1) as num-->
<!--        from rc_doc_process-->
<!--        where record_id > 0 and tenant_id = #{tenantId}-->
<!--        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(userIds)">-->
<!--            and create_by in <foreach collection="userIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(processStatusList)">-->
<!--            and process_status in <foreach collection="processStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        and batch_id in <foreach collection="batchIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        group by process_status,batch_id-->
<!--    </select>-->

<!--    <select id="selectDocProcessPage" resultType="com.suwell.plss.record.entity.DocProcess">-->
<!--        select a.*-->
<!--        from rc_doc_process a-->
<!--        <where>-->
<!--            a.tenant_id = #{tenantId}-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.processStatusList)">-->
<!--                and a.process_status in <foreach collection="req.processStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.processSubStatusList)">-->
<!--                and a.process_sub_status in <foreach collection="req.processSubStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.recordTypeIdList)">-->
<!--                and a.record_type_id in <foreach collection="req.recordTypeIdList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(userIds)">-->
<!--                and a.create_by in <foreach collection="userIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="req.recordId != null">-->
<!--                and a.record_id = #{req.recordId}-->
<!--            </if>-->
<!--            <if test = "req.endRecordId != null">-->
<!--                and a.record_id &lt; #{req.endRecordId}-->
<!--            </if>-->
<!--            <if test="req.recordId == null and req.endRecordId == null">-->
<!--                and a.record_id > 0-->
<!--            </if>-->
<!--            <if test="req.origin != null">-->
<!--                and a.origin = #{req.origin}-->
<!--            </if>-->
<!--            <if test="req.originList != null and req.originList.size() > 0">-->
<!--                and a.origin in <foreach collection="req.originList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.knowledgeStatusList)">-->
<!--                and a.knowledge_status in <foreach collection="req.knowledgeStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="req.uploadTimeStart != null">-->
<!--                and a.create_time &gt;= #{req.uploadTimeStart}-->
<!--            </if>-->
<!--            <if test="req.uploadTimeEnd != null">-->
<!--                and a.create_time &lt;= #{req.uploadTimeEnd}-->
<!--            </if>-->
<!--            <if test="batchId != null">-->
<!--                and a.batch_id = #{batchId}-->
<!--            </if>-->
<!--            <if test = "req.ctype!=null and req.ctype == 1">-->
<!--                and a.ctype in (1,3)-->
<!--            </if>-->
<!--            <if test = "@cn.hutool.core.collection.CollUtil@isNotEmpty(req.planIds)">-->
<!--                and a.plan_id in <foreach collection="req.planIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(req.name)">-->
<!--                and a.title like CONCAT('%', #{req.name}, '%')-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(req.createByName)">-->
<!--                and a.create_by_name like CONCAT('%', #{req.createByName}, '%')-->
<!--            </if>-->
<!--            <if test="req.recordRelStatus != null">AND-->
<!--                <choose>-->
<!--                    <when test="req.recordRelStatus == 2">NOT</when>-->
<!--                </choose>-->
<!--                EXISTS (select * from rc_folder_record b where a.record_id = b.record_id)-->
<!--            </if>-->
<!--            <if test="batchId != null and req.classified != null">-->
<!--                and a.classified = #{req.classified}-->
<!--            </if>-->
<!--            <if test="batchId == null">-->
<!--                <choose>-->
<!--                    <when test="req.forSecure == true and @cn.hutool.core.collection.CollUtil@isEmpty(req.classifiedList)">-->
<!--                        and a.classified is not null-->
<!--                    </when>-->
<!--                    <when test="req.forSecure == true and @cn.hutool.core.collection.CollUtil@isNotEmpty(req.classifiedList)">-->
<!--                        and a.classified in <foreach collection="req.classifiedList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--                    </when>-->
<!--                    <when test="req.forSecure == false">-->
<!--                        and a.classified is null-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
<!--        </where>-->
<!--        order by a.id desc-->
<!--    </select>-->

<!--    <select id="queryAllUserId" resultType="java.lang.Long">-->
<!--        select distinct create_by from rc_doc_process where create_by > 0 and record_id > 0-->
<!--    </select>-->
<!--    <select id="getSubDoc" resultType="com.suwell.plss.record.entity.DocProcess">-->
<!--        select *-->
<!--        from rc_doc_process-->
<!--        where ctype = 2-->
<!--        and task_id in (-->
<!--            select task_id from rc_doc_process where record_id in <foreach collection="recordIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        )-->
<!--    </select>-->

<!--    <select id="processCountGroupByOrg" resultType="com.suwell.plss.record.standard.dto.response.DocProcessCountResp">-->
<!--        SELECT-->
<!--            count(*) as num, so.org_name as orgName-->
<!--        FROM "plss-record".rc_doc_process rd-->
<!--        left join "plss-system".sys_user_org su on rd.create_by=su.user_id-->
<!--        left join "plss-system".sys_org so on so.org_id=su.org_id-->
<!--        where so.org_type= #{orgType}-->
<!--        <if test="startTime != null">-->
<!--            and rd.create_time &gt;= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime != null">-->
<!--            and rd.create_time &lt;= #{endTime}-->
<!--        </if>-->
<!--        <if test="tenantId != null">-->
<!--            and rd.tenant_id= #{tenantId}-->
<!--            and su.tenant_id= #{tenantId}-->
<!--            and so.tenant_id= #{tenantId}-->
<!--        </if>-->
<!--        and rd.process_status=5-->
<!--        and rd.origin not in (5,6,999)-->
<!--        group by so.org_name order by num desc-->
<!--    </select>-->


<!--    <select id="processCountGroupByOrgs" resultType="java.lang.Long">-->
<!--        SELECT-->
<!--        count(*) as num-->
<!--        FROM "plss-record".rc_doc_process rd-->
<!--        left join "plss-system".sys_user_org su on rd.create_by=su.user_id-->
<!--        left join "plss-system".sys_org so on so.org_id=su.org_id-->
<!--        where  1=1-->
<!--        <if test="startTime != null">-->
<!--            and rd.create_time &gt;= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime != null">-->
<!--            and rd.create_time &lt;= #{endTime}-->
<!--        </if>-->
<!--        <if test="tenantId != null">-->
<!--            and rd.tenant_id= #{tenantId}-->
<!--            and su.tenant_id= #{tenantId}-->
<!--            and so.tenant_id= #{tenantId}-->
<!--        </if>-->
<!--        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(orgs)">-->
<!--            and su.org_id in <foreach collection="orgs" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        </if>-->
<!--        and rd.process_status=5-->
<!--        and rd.origin not in (5,6,999)-->
<!--    </select>-->
    <sql id = "process_sub_status_condition">
        a.process_sub_status in <foreach collection="req.processSubStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>
    </sql>

<!--    <select id="selectFrontDocProcessPage" resultType="com.suwell.plss.record.entity.DocProcess">-->
<!--        select * from rc_doc_process-->
<!--        <where>-->
<!--            and store_way = 2-->
<!--            <if test="userId != null">-->
<!--                and create_by = #{userId}-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.processSubStatusList)">-->
<!--                and process_sub_status in <foreach collection="req.processSubStatusList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(req.recordTypeIdList)">-->
<!--                and record_type_id in <foreach collection="req.recordTypeIdList" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--            </if>-->
<!--            <if test="req.recordId != null">-->
<!--                and record_id = #{req.recordId}-->
<!--            </if>-->
<!--            <if test="req.recordId == null">-->
<!--                and record_id > 0-->
<!--            </if>-->
<!--            <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(req.title)">-->
<!--                and title like CONCAT('%', #{req.title}, '%')-->
<!--            </if>-->
<!--            <if test="req.uploadTimeStart != null">-->
<!--                and create_time &gt;= #{req.uploadTimeStart}-->
<!--            </if>-->
<!--            <if test="req.uploadTimeEnd != null">-->
<!--                and create_time &lt;= #{req.uploadTimeEnd}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by id desc-->
<!--    </select>-->
<!--    <select id="countByRecordIds" resultType="java.lang.Long">-->
<!--        select count(1) as num from rc_doc_process-->
<!--        where batch_id in (-->
<!--            select distinct batch_id-->
<!--            from rc_doc_process-->
<!--            where record_id in <foreach collection="recordIds" open="(" close=")" separator="," item="item">#{item}</foreach>-->
<!--        )-->
<!--    </select>-->
    <!--    <select id="processCountByDate" resultType="java.lang.Long">-->

<!--        select count(*) as num from "plss-record".rc_doc_process where 1=1-->
<!--        <if test="startTime != null">-->
<!--            and create_time >= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime != null">-->
<!--            and create_time  &lt;= #{endTime}-->
<!--        </if>-->
<!--        <if test="tenantId != null">-->
<!--            and tenant_id= #{tenantId}-->
<!--        </if>-->
<!--    </select>-->
</mapper>