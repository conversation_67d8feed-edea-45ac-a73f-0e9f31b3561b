<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.DataLakeCustomMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.DataLakeCustom">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="custom_key" property="customKey" />
        <result column="data_name" property="dataName" />
        <result column="file_date" property="fileDate" />
        <result column="file_size" property="fileSize" />
        <result column="file_md5" property="fileMd5" />
        <result column="file_origin_name" property="fileOriginName" />
        <result column="status" property="status" />
        <result column="task_key" property="taskKey" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, custom_key, data_name, file_date, file_size, file_md5, file_origin_name, status, task_key
    </sql>

</mapper>
