<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.MqRetryMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.MqRetryMsg">
        <id column="id" property="id" />
        <result column="business_type" property="businessType" />
        <result column="msg" property="msg" />
        <result column="fail_times" property="failTimes" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_type, msg, fail_times, status, create_time, create_by, update_time, update_by
    </sql>

</mapper>
