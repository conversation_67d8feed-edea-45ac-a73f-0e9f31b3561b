<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RcSensitiveCategoryMapper">

    <select id="pageQuery"
            resultType="com.suwell.plss.record.standard.dto.response.SensitiveCategoryResp">
        SELECT category.id,category.name FROM rc_sensitive_category category
        left join rc_sensitive_category_word cw on category.id = cw.category_id
        left join rc_sensitive_word word on cw.word_id= word.id
        <where>
            category.del_flag = cast(1 as char)
            and cw.del_flag = cast(1 as char)
            and word.del_flag = cast(1 as char)
            <if test="categoryId != null and categoryId > 0 ">
                and category.id = #{categoryId}
            </if>
            <if test="wordName != null and wordName != ''">
                and word.name like concat('%', #{wordName}, '%')
            </if>
        </where>
        GROUP BY category.id,category.name
    </select>

    <select id="pageAssetSensitiveCategory"
            resultType="com.suwell.plss.record.standard.dto.response.SensitiveCategoryResp">
        SELECT c.id, c.name FROM rc_sensitive_category c
        inner join rc_sensitive_category_word cw on c.id = cw.category_id
        inner join rc_sensitive_word word on cw.word_id= word.id
        <where>
            c.del_flag = cast(1 as char)
            and cw.del_flag = cast(1 as char)
            and word.del_flag = cast(1 as char)
            <if test="req.categoryId != null and req.categoryId > 0 ">
                and c.id = #{req.categoryId}
            </if>
            <if test="req.wordName != null and req.wordName != ''">
                and word.name like concat('%', #{req.wordName}, '%')
            </if>
            <if test="req.updateTimeStart != null">
                and (c.update_time &gt;= #{req.updateTimeStart} or word.update_time &gt;= #{req.updateTimeStart})
            </if>
            <if test="req.updateTimeEnd != null">
                and (c.update_time &lt;= #{req.updateTimeEnd} or word.update_time &lt;= #{req.updateTimeEnd})
            </if>
        </where>
        GROUP BY c.id, c.name
    </select>

    <select id="selectSensitiveCategoryWord" resultType="com.suwell.plss.record.standard.dto.response.SensitiveCategoryWordResp">
        SELECT cw.category_id, cw.word_id, word.name FROM rc_sensitive_category_word cw
        inner join rc_sensitive_word word on cw.word_id= word.id
        <where>
            cw.del_flag = cast(1 as char)
            and word.del_flag = cast(1 as char)
            <if test="categoryIds != null and categoryIds.size() > 0">
                and cw.category_id in
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>