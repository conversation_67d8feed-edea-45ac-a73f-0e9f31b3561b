<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RcProofreadRecordMapper">

    <select id="selectOrganList"  resultType="string">
        SELECT
            DISTINCT issued_organ
        FROM rc_proofread_record
        WHERE file_create_user_id = #{userId}
        AND issued_organ is not null
    </select>

</mapper>