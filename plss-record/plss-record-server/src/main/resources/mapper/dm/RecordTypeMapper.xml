<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordTypeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordType" id="recordTypeMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
    </resultMap>


    <select id="queryUsedRecordTypeIds" resultType="java.lang.Long">
        select distinct recordtype_id from rc_record;
    </select>

</mapper>