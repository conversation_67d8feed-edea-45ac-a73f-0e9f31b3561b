<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.RcWatermarkSettingMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.RcWatermarkSetting">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="watermarkJson" column="watermark_json" jdbcType="VARCHAR"/>
            <result property="watermarkType" column="watermark_type" jdbcType="VARCHAR"/>
            <result property="exampleUrl" column="example_url" jdbcType="VARCHAR"/>
            <result property="value" column="value" jdbcType="VARCHAR"/>
            <result property="enableStatus" column="enable_status" jdbcType="CHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,watermark_json,watermark_type,
        example_url,value,enable_status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
</mapper>
