<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.DocumentHistoryMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.DocumentHistory" id="documentHistoryMap">
        <result property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="name" column="name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="objectName" column="object_name"/>
        <result property="ofdFileSize" column="ofd_file_size"/>
        <result property="ofdFileMd5" column="ofd_file_md5"/>
        <result property="ofdBucketName" column="ofd_bucket_name"/>
        <result property="ofdObjectName" column="ofd_object_name"/>
        <result property="txtFileSize" column="txt_file_size"/>
        <result property="txtFileMd5" column="txt_file_md5"/>
        <result property="txtBucketName" column="txt_bucket_name"/>
        <result property="txtObjectName" column="txt_object_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="modifiedTime" column="modified_time"/>
        <result property="modifiedBy" column="modified_by"/>
    </resultMap>

</mapper>