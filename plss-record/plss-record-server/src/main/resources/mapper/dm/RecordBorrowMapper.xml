<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordBorrowMapper">

    <select id="queryBorrowPage" resultType="com.suwell.plss.record.entity.RecordBorrow">
        SELECT distinct
        a.id,a.borrow_id as borrowId, a.record_id as recordId,
        a.record_name as recordName,a.record_tenant_id as recordTenantId,
        a.borrow_by as borrowBy,a.borrow_by_name as borrowByName,
        a.borrow_date as borrowDate,a.org_id as orgId,
        a.org_name as orgName,a.borrow_days as borrowDays,
        a.borrow_purpose as borrowPurpose,a.borrow_for as borrowFor,
        a.usage,a.audit_by as auditBy,a.audit_by_name as auditByName,
        a.audit_date as auditDate,a.status,a.reject_reason as rejectedReason,
        a.del_flag as delFlag,a.create_time as createTime,a.create_by as createBy,
        a.update_time as updateTime,a.update_by as updateBy,a.remark as remark,
        a.invalid_status as invalidStatus,a.withdraw_date as withdrawDate
        FROM rc_record_borrow a
        left JOIN rc_record_repo b ON a.record_id = b.record_id
        <where>
            a.del_flag = '1'
            <if test="req.tenantId != null">
                and a.record_tenant_id = #{req.tenantId}
            </if>
            <if test="req.borrowBy != null">
                and a.borrow_by = #{req.borrowBy}
            </if>
            <if test="req.borrowByName != null and req.borrowByName != ''">
                and a.borrow_by_name like CONCAT('%', #{req.borrowByName}, '%')
            </if>
            <if test="req.recordName != null and req.recordName != ''">
                and a.record_name like CONCAT('%', #{req.recordName}, '%')
            </if>
            <if test="req.orgId != null and req.orgId != ''">
                and a.org_id = #{req.orgId}
            </if>
            <if test="req.statusList != null and req.statusList.size() > 0">
                and a.status in
                <foreach collection="req.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.borrowDateBefore != null">
                <![CDATA[  and a.borrow_date >= #{req.borrowDateBefore} ]]>
            </if>
            <if test="req.borrowDateAfter != null">
                <![CDATA[  and a.borrow_date <= #{req.borrowDateAfter} ]]>
            </if>
            <if test="req.recordIds != null and req.recordIds.size() > 0">
                and a.record_id in
                <foreach collection="req.recordIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.repoIds != null and req.repoIds.size() > 0">
                and b.repo_id in
                <foreach collection="req.repoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.borrow_date desc
    </select>
</mapper>