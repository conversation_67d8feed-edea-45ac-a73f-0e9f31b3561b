<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.ExportFilterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.ExportFilter">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="repo_id" property="repoId"/>
        <result column="repo_name" property="repoName"/>
        <result column="folder_ids" property="folderIds"/>
        <result column="category_ids" property="categoryIds"/>
        <result column="put_lib_min" property="putLibMin"/>
        <result column="put_lib_max" property="putLibMax"/>
        <result column="md_value_json" property="mdValueJson"/>
        <result column="limit_record" property="limitRecord"/>
        <result column="total_record" property="totalRecord"/>
        <result column="file_type" property="fileType"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , task_id, repo_id, repo_name, folder_ids, category_ids, put_lib_min, put_lib_max, md_value_json, limit_record, total_record, file_type
    </sql>

    <select id="queryList" resultType="com.suwell.plss.record.entity.ExportFilter">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rc_export_filter
        WHERE task_id = #{taskId}
    </select>
</mapper>
