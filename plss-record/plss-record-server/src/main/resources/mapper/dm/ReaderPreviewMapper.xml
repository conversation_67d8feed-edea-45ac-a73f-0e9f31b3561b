<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.ReaderPreviewMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.ReaderPreview" id="readerPreviewMap">
        <result property="id" column="id"/>
        <result property="fname" column="fname"/>
        <result property="fdata" column="fdata"/>
        <result property="fsource" column="fsource"/>
        <result property="fperm" column="fperm"/>
        <result property="fmark" column="fmark"/>
        <result property="expireDate" column="expire_date"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
    </resultMap>


</mapper>