<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.RcSecurityPolicyMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.RcSecurityPolicy">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="policyType" column="policy_type" jdbcType="VARCHAR"/>
            <result property="policyId" column="policy_id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="dimension" column="dimension" jdbcType="VARCHAR"/>
            <result property="objectId" column="object_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,policy_type,policy_id,
        role_id,dimension,object_id
    </sql>
</mapper>
