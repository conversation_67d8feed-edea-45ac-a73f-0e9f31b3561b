<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordTypeMetadataMapper">

    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.RecordTypeMetadata">
        <result column="recordtype_id" property="recordtypeId"/>
        <result column="md_id" property="mdId"/>
        <result column="value_type" property="valueType"/>
        <result column="value_range" property="valueRange"/>
        <result column="search_flag" property="searchFlag"/>
        <result column="mdcategory_id" property="mdcategoryId"/>
        <result column="orderby" property="orderby"/>
        <result column="required" property="required"/>
        <result column="opt_edit" property="optEdit"/>
        <result column="opt_view" property="optView"/>
        <result column="value_type_range" property="valueTypeRange"/>
        <result column="value_rule" property="valueRule"/>
        <result column="md_name" property="mdName"/>
        <result column="pinyin" property="pinyin"/>
        <result column="mnemonic" property="mnemonic"/>
        <result column="borrow_view" property="borrowView"/>
        <result column="details_view" property="detailsView"/>
        <result column="search_result_view" property="searchResultView"/>
        <result column="search_field_way" property="searchFieldWay"/>
        <result column="remark" property="remark"/>
        <result column="opt_sort_standard_item" property="optSortStandardItem"/>
    </resultMap>

    <sql id="Base_Column_List">
        recordtype_id, md_id, value_type, value_range, search_flag, mdcategory_id, orderby, required, opt_edit, opt_view, value_type_range, value_rule, md_name, pinyin, mnemonic, borrow_view, details_view, search_result_view, search_field_way, remark,opt_sort_standard_item
    </sql>

    <!-- 通过文件类型的id 查询 文件的元数据-->
    <select id="selectMetadata" resultType="com.suwell.plss.record.domain.RecordTypeMetadataDTO">
        SELECT
        rtm.recordtype_id ,
        rtm.md_id ,
        rtm.value_type ,
        rtm.value_range ,
        rtm.search_flag ,
        rtm.mdcategory_id ,
        rtm.orderby ,
        rtm.required ,
        rtm.opt_edit ,
        rtm.opt_view ,
        rtm.value_type_range ,
        rtm.value_rule ,
        rtm.md_name ,
        rtm.pinyin ,
        rtm.mnemonic ,
        rtm.borrow_view ,
        rtm.details_view ,
        rtm.search_result_view,
        rtm.search_field_way ,
        rtm.remark,
        rtm.alias_name
        FROM rc_record_type_metadata rtm
        <where>
            <if test="recordTypeIds != null and recordTypeIds.size() > 0">AND rtm.recordtype_id IN
                <foreach collection="recordTypeIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="idList != null and idList.size() > 0">AND rtm.md_id IN
                <foreach collection="idList" open="(" close=")" separator="," item="item">#{item}
                </foreach>
            </if>
            <if test="mdId != null">AND rtm.md_id = #{mdId}</if>
            <if test="querySearchMd != null">
                <choose>
                    <when test="querySearchMd">AND rtm.search_flag = 1</when>
                    <otherwise>AND rtm.search_flag = 2</otherwise>
                </choose>
            </if>
            <if test="queryListMd != null">
                <choose>
                    <when test="queryListMd">AND rtm.value_type &gt;= 6</when>
                    <otherwise>AND rtm.value_type &lt; 6</otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="queryRecordMetadata"
            resultType="com.suwell.plss.record.domain.RecordTypeMetadataDTO">
        SELECT
        rtm.recordtype_id ,
        rtm.md_id ,
        rtm.value_type ,
        rtm.value_range ,
        rtm.search_flag ,
        rtm.mdcategory_id ,
        rtm.orderby ,
        rtm.required ,
        rtm.opt_edit ,
        rtm.opt_view ,
        rtm.value_type_range ,
        rtm.value_rule ,
        rtm.md_name ,
        rtm.pinyin ,
        rtm.mnemonic ,
        rtm.borrow_view ,
        rtm.details_view ,
        rtm.search_result_view,
        rtm.search_field_way ,
        rtm.remark
        FROM rc_record_type_metadata rtm
        where
        rtm.details_view = 1
        or rtm.search_result_view = 1
        <if test="recordTypeIds != null and recordTypeIds.size() > 0">
            AND rtm.recordtype_id IN
            <foreach collection="recordTypeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mdId != null">
            AND rtm.md_id = #{mdId}
        </if>
        <if test="querySearchMd != null">
            <choose>
                <when test="querySearchMd">
                    AND rtm.search_flag = 1
                </when>
                <otherwise>
                    AND rtm.search_flag = 2
                </otherwise>
            </choose>
        </if>
        <if test="queryListMd != null">
            <choose>
                <when test="queryListMd">
                    AND rtm.value_type &gt;= 6
                </when>
                <otherwise>
                    AND rtm.value_type &lt; 6
                </otherwise>
            </choose>
        </if>
        ORDER BY rtm.recordtype_id ASC, rtm.orderby ASC
    </select>

    <select id="queryInRecordTypeMetadata" resultType="com.suwell.plss.record.entity.RecordTypeMetadata">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rc_record_type_metadata
        WHERE (recordtype_id, md_id) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.recordtypeId}, #{item.mdId})
        </foreach>
    </select>
    <select id="listMdByRecordTypeIds"
            resultType="com.suwell.plss.record.domain.RecordTypeMetadataDTO">
        SELECT DISTINCT md_id, md_name, value_type
        FROM rc_record_type_metadata
        WHERE recordtype_id IN
        <foreach collection="recordTypeIds" item="recordTypeId" open="(" close=")" separator=",">
            #{recordTypeId}
        </foreach>
        AND search_result_view = 1
        ORDER BY md_id
    </select>
</mapper>