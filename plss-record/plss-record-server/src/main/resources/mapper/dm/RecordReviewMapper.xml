<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordReviewMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordReview" id="recordReviewMap">
        <result property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="version" column="version"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="documentId" column="document_id"/>
        <result property="fileId" column="file_id"/>
        <result property="repoId" column="repo_id"/>
        <result property="content" column="content"/>
        <result property="recordtypeId" column="record_type_id"/>
        <result property="recordType" column="record_type"/>
        <result property="ctype" column="ctype"/>
        <result property="attachmentType" column="attachment_type"/>
        <result property="md5" column="md5"/>
        <result property="fileSize" column="fileSize"/>
        <result property="reviewTime" column="review_time"/>
        <result property="reviewResult" column="review_result"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="modifiedTime" column="modified_time"/>
    </resultMap>
</mapper>