<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MaterialCategoryMapper">

    <select id="selectCategoryId" resultType="long">
        select distinct category_id from rc_material_category rmc
        where material_id in
        (select  id from rc_material rm where rep_id  = #{repoId} and del_flag = 1 and status = 1)
    </select>
</mapper>