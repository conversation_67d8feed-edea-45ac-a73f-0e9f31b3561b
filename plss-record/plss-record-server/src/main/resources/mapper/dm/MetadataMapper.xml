<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MetadataMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <select id="queryList"
            resultType="com.suwell.plss.record.standard.dto.response.MetadataResp">
        SELECT
        a.*
        FROM rc_metadata a
        <if test="metadataCategoryId != null">
            JOIN rc_metadata_category_metadata b ON a.id = b.md_id
        </if>
        <if test="docTypeIdList != null and docTypeIdList.size() > 0">
            JOIN rc_record_type_metadata c ON a.id = c.md_id
        </if>
        <where>
            AND a.status = 1
            <if test="name != null and name != ''">AND a.name LIKE CONCAT('%', #{name}, '%')</if>
            <if test="nameEn != null and nameEn != ''">AND a.name_en LIKE CONCAT('%', #{nameEn},
                '%')
            </if>
            <if test="metadataCategoryId != null">AND b.category_id = #{metadataCategoryId}</if>
            <if test="docTypeIdList != null and docTypeIdList.size() > 0">AND c.recordtype_id IN
                <foreach collection="docTypeIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.id DESC
    </select>
    <select id="queryRecordTypeMetadataByRecordIds" resultType="java.lang.String">
        SELECT t1.md_name
        FROM  rc_record_type_metadata t1
        WHERE t1.recordtype_id IN (
        SELECT recordtype_id
        FROM  rc_record
        WHERE id IN
        <foreach item="item" collection="recordIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY recordtype_id
        )
        AND t1.md_name NOT IN ('入库位置', '系统分类','知识提取', '实体日期','实体地点', '实体人物','实体机构', '关键词')
        GROUP BY t1.md_name
    </select>


</mapper>