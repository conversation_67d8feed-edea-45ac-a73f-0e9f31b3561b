<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.RecordMetadataValueMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.RecordMetadataValue" id="recordMetadataValueMap">
        <result property="recordId" column="record_id"/>
        <result property="metadataValueJson" column="metadata_value_json"/>
        <result property="createTime" column="create_time"/>
        <result property="modifiedTime" column="modified_time"/>
    </resultMap>

    <update id="batchUpdate">
        update rc_record_metadata_value
        <set>
            <trim prefix="metadata_value_json = case record_id" suffix="end,">
                <foreach collection="list" item="item">
                    when #{item.recordId} then #{item.metadataValueJson}
                </foreach>
            </trim>
            <trim prefix="modified_time = case record_id" suffix="end,">
                <foreach collection="list" item="item">
                    when #{item.recordId} then to_timestamp(#{item.modifiedTime}, 'yyyy-mm-dd hh24:mi:ss.ms')
                </foreach>
            </trim>
        </set>
        <where>
            record_id in (
            <foreach collection="list" item="item" separator=",">
                #{item.recordId}
            </foreach>
            )
        </where>
    </update>


    <update id="truncateRecordMetadataValueAll">
        truncate table rc_record_metadata_value
    </update>

    <!-- sharding 迁移数据 使用-->
    <select id="selectMetadataValueRecordsById" resultType="java.lang.Long">
        SELECT id
        FROM rc_record
        WHERE id &lt; #{maxId}
          AND id IN (SELECT record_id
                     FROM rc_metadata_value)
        ORDER BY id DESC
            LIMIT #{pageSize}
    </select>

    <!-- sharding 迁移数据 使用-->
    <select id="selectMetadataValueRecordsByIdList"
            resultType="com.suwell.plss.record.standard.domain.MetadataValueDTO">
        SELECT *
        FROM rc_metadata_value
        WHERE record_id IN
        <foreach collection="recordIdList" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
        ORDER BY record_id DESC
    </select>

    <!-- sharding 迁移数据 使用-->
    <select id="listQueryPageByRecordId"
            resultType="com.suwell.plss.record.standard.domain.MetadataValueDTO">
        SELECT *
        FROM rc_metadata_value
        WHERE record_id &lt; #{maxId}
        ORDER BY record_id DESC LIMIT #{pageSize}
    </select>

</mapper>