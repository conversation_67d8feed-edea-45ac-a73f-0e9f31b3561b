<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suwell.plss.record.mapper.ExportTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.suwell.plss.record.entity.ExportTask">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="dir_path" property="dirPath"/>
        <result column="gmt_create" property="gmtCreate"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , name, status, start_time, finish_time, dir_path, gmt_create
    </sql>

</mapper>
