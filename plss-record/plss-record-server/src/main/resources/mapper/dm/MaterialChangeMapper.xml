<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MaterialChangeMapper">

	<select id="selectMaterialChangeRepCount" resultType="com.suwell.plss.record.domain.MaterialChangeReposityDTO">
        select count(1) as materialChangeNum, rm.rep_id
        from rc_material_change rmc
        left join rc_material rm on rmc.material_id = rm.id

        <where>
        <if test="repIdList != null and repIdList.size() > 0">
            and rm.rep_id in
            <foreach collection="repIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>
        group by rm.rep_id
        order by materialChangeNum desc limit 10
    </select>


</mapper>