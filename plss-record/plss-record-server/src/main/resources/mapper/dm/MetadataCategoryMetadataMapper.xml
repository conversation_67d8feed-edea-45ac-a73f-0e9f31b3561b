<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suwell.plss.record.mapper.MetadataCategoryMetadataMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.suwell.plss.record.entity.MetadataCategoryMetadata" id="metadataCategoryMetadataMap">
        <result property="categoryId" column="category_id"/>
        <result property="mdId" column="md_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        category_id, md_id
    </sql>

    <sql id="Base_Column_List_A">
        a.category_id, a.md_id
    </sql>

    <select id="selectMetadata" resultType="com.suwell.plss.record.entity.Metadata">
        SELECT id,
               name,
               name_en,
               definition,
               value_type,
               value_range,
               short_name,
               remark,
               status,
               orderby,
               create_time,
               create_by,
               modified_time,
               modified_by,
               search_flag,
               required,
               opt_edit,
               opt_view,
               value_rule,
               pinyin,
               mnemonic,
               borrow_view,
               details_view,
               search_result_view,
               search_field_way,
               mt.opt_sort_standard_item,
               mt.alias_name,
               mt.alias_name_view
        FROM rc_metadata mt
                 LEFT JOIN rc_metadata_category_metadata mcm
                           ON mt.id = mcm.md_id
        WHERE mcm.category_id = #{metadataCategoryId}
        ORDER BY mt.create_time DESC
    </select>

    <select id="queryInMetadataCategoryMetadata" resultType="com.suwell.plss.record.entity.MetadataCategoryMetadata">
        SELECT
        <include refid="Base_Column_List_A"/>
        FROM rc_metadata_category_metadata a
        WHERE (a.category_id, a.md_id) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.categoryId}, #{item.mdId})
        </foreach>
    </select>

</mapper>