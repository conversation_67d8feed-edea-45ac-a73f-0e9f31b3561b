
CREATE TABLE rc_file_md5_rel_0 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_0 ON rc_file_md5_rel_0(md5_hex);

CREATE TABLE rc_file_md5_rel_1 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_1 ON rc_file_md5_rel_1(md5_hex);

CREATE TABLE rc_file_md5_rel_2 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_2 ON rc_file_md5_rel_2(md5_hex);

CREATE TABLE rc_file_md5_rel_3 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_3 ON rc_file_md5_rel_3(md5_hex);

CREATE TABLE rc_file_md5_rel_4 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_4 ON rc_file_md5_rel_4(md5_hex);

CREATE TABLE rc_file_md5_rel_5 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_5 ON rc_file_md5_rel_5(md5_hex);

CREATE TABLE rc_file_md5_rel_6 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_6 ON rc_file_md5_rel_6(md5_hex);

CREATE TABLE rc_file_md5_rel_7 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_7 ON rc_file_md5_rel_7(md5_hex);

CREATE TABLE rc_file_md5_rel_8 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_8 ON rc_file_md5_rel_8(md5_hex);

CREATE TABLE rc_file_md5_rel_9 (
                                   id bigint NOT NULL PRIMARY KEY,
                                   md5_hex varchar(255) NOT NULL,
                                   file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_9 ON rc_file_md5_rel_9(md5_hex);

CREATE TABLE rc_file_md5_rel_10 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_10 ON rc_file_md5_rel_10(md5_hex);

CREATE TABLE rc_file_md5_rel_11 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_11 ON rc_file_md5_rel_11(md5_hex);

CREATE TABLE rc_file_md5_rel_12 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_12 ON rc_file_md5_rel_12(md5_hex);

CREATE TABLE rc_file_md5_rel_13 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_13 ON rc_file_md5_rel_13(md5_hex);

CREATE TABLE rc_file_md5_rel_14 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_14 ON rc_file_md5_rel_14(md5_hex);

CREATE TABLE rc_file_md5_rel_15 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_15 ON rc_file_md5_rel_15(md5_hex);

CREATE TABLE rc_file_md5_rel_16 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_16 ON rc_file_md5_rel_16(md5_hex);

CREATE TABLE rc_file_md5_rel_17 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_17 ON rc_file_md5_rel_17(md5_hex);

CREATE TABLE rc_file_md5_rel_18 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_18 ON rc_file_md5_rel_18(md5_hex);

CREATE TABLE rc_file_md5_rel_19 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_19 ON rc_file_md5_rel_19(md5_hex);

CREATE TABLE rc_file_0 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_1 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_2 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_3 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_4 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_5 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_6 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_7 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_8 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_9 (
                           id bigint NOT NULL PRIMARY KEY,
                           platform varchar(30) NOT NULL COMMENT '存储平台代码',
                           file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                           origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                           file_name varchar(255) NOT NULL COMMENT '文件名',
                           md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                           file_size int NOT NULL COMMENT '文件大小',
                           file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                           relative_path varchar(255) NOT NULL COMMENT '相对路径',
                           encryption int NOT NULL COMMENT '是否加密',
                           encrypt_version int COMMENT '加密版本号',
                           encrypt_password varchar(255) COMMENT '基础密码',
                           encrypt_magic_number varchar(32) COMMENT '魔数',
                           create_by bigint NOT NULL COMMENT '创建人id',
                           create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_10 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_11 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_12 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_13 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_14 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_15 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_16 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_17 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_18 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_19 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_20 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_21 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_22 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_23 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_24 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_25 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_26 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_27 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_28 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_29 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_document_0 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_0_record_id_idx ON rc_document_0(record_id);

CREATE TABLE rc_document_1 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_1_record_id_idx ON rc_document_1(record_id);

CREATE TABLE rc_document_2 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_2_record_id_idx ON rc_document_2(record_id);

CREATE TABLE rc_document_3 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_3_record_id_idx ON rc_document_3(record_id);

CREATE TABLE rc_document_4 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_4_record_id_idx ON rc_document_4(record_id);

CREATE TABLE rc_document_5 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_5_record_id_idx ON rc_document_5(record_id);

CREATE TABLE rc_document_6 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_6_record_id_idx ON rc_document_6(record_id);

CREATE TABLE rc_document_7 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_7_record_id_idx ON rc_document_7(record_id);

CREATE TABLE rc_document_8 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_8_record_id_idx ON rc_document_8(record_id);

CREATE TABLE rc_document_9 (
                               id BIGINT NOT NULL PRIMARY KEY,
                               record_id BIGINT NOT NULL COMMENT '文件id',
                               name varchar(256) NOT NULL COMMENT '文档名称',
                               ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                               attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                               classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                               file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                               file_size BIGINT COMMENT '文档大小',
                               file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                               ofd_file_id BIGINT,
                               txt_file_id BIGINT,
                               status INT,
                               create_time timestamp NOT NULL COMMENT '创建时间',
                               create_by BIGINT NOT NULL COMMENT '创建人',
                               modified_time timestamp NOT NULL COMMENT '修改时间',
                               modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_9_record_id_idx ON rc_document_9(record_id);

CREATE TABLE rc_document_10 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_10_record_id_idx ON rc_document_10(record_id);

CREATE TABLE rc_document_11 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_11_record_id_idx ON rc_document_11(record_id);

CREATE TABLE rc_document_12 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_12_record_id_idx ON rc_document_12(record_id);

CREATE TABLE rc_document_13 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_13_record_id_idx ON rc_document_13(record_id);

CREATE TABLE rc_document_14 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_14_record_id_idx ON rc_document_14(record_id);

CREATE TABLE rc_document_15 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_15_record_id_idx ON rc_document_15(record_id);

CREATE TABLE rc_document_16 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_16_record_id_idx ON rc_document_16(record_id);

CREATE TABLE rc_document_17 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_17_record_id_idx ON rc_document_17(record_id);

CREATE TABLE rc_document_18 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_18_record_id_idx ON rc_document_18(record_id);

CREATE TABLE rc_document_19 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_19_record_id_idx ON rc_document_19(record_id);

CREATE TABLE rc_document_20 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_20_record_id_idx ON rc_document_20(record_id);

CREATE TABLE rc_document_21 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_21_record_id_idx ON rc_document_21(record_id);

CREATE TABLE rc_document_22 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_22_record_id_idx ON rc_document_22(record_id);

CREATE TABLE rc_document_23 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_23_record_id_idx ON rc_document_23(record_id);

CREATE TABLE rc_document_24 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_24_record_id_idx ON rc_document_24(record_id);

CREATE TABLE rc_document_25 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_25_record_id_idx ON rc_document_25(record_id);

CREATE TABLE rc_document_26 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_26_record_id_idx ON rc_document_26(record_id);

CREATE TABLE rc_document_27 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_27_record_id_idx ON rc_document_27(record_id);

CREATE TABLE rc_document_28 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_28_record_id_idx ON rc_document_28(record_id);

CREATE TABLE rc_document_29 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_29_record_id_idx ON rc_document_29(record_id);

CREATE TABLE rc_document_30 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_30_record_id_idx ON rc_document_30(record_id);

CREATE TABLE rc_document_31 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_31_record_id_idx ON rc_document_31(record_id);

CREATE TABLE rc_document_32 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_32_record_id_idx ON rc_document_32(record_id);

CREATE TABLE rc_document_33 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_33_record_id_idx ON rc_document_33(record_id);

CREATE TABLE rc_document_34 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_34_record_id_idx ON rc_document_34(record_id);

CREATE TABLE rc_document_35 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_35_record_id_idx ON rc_document_35(record_id);

CREATE TABLE rc_document_36 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_36_record_id_idx ON rc_document_36(record_id);

CREATE TABLE rc_document_37 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_37_record_id_idx ON rc_document_37(record_id);

CREATE TABLE rc_document_38 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_38_record_id_idx ON rc_document_38(record_id);

CREATE TABLE rc_document_39 (
                                id BIGINT NOT NULL PRIMARY KEY,
                                record_id BIGINT NOT NULL COMMENT '文件id',
                                name varchar(256) NOT NULL COMMENT '文档名称',
                                ctype INT NOT NULL COMMENT '文档类型(1:主文档, 2:附件)',
                                attachment_type INT COMMENT '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件',
                                classified INT COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                                file_id BIGINT NOT NULL COMMENT '原件在对象存储中文件id',
                                file_size BIGINT COMMENT '文档大小',
                                file_md5 varchar(256) NOT NULL COMMENT '文档MD5值',
                                ofd_file_id BIGINT,
                                txt_file_id BIGINT,
                                status INT,
                                create_time timestamp NOT NULL COMMENT '创建时间',
                                create_by BIGINT NOT NULL COMMENT '创建人',
                                modified_time timestamp NOT NULL COMMENT '修改时间',
                                modified_by BIGINT NOT NULL COMMENT '修改人'
) COMMENT='文档表';
CREATE INDEX rc_document_39_record_id_idx ON rc_document_39(record_id);

CREATE TABLE rc_document_md5_rel_0 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_0_md5_hex_idx ON rc_document_md5_rel_0(md5_hex);

CREATE TABLE rc_document_md5_rel_1 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_1_md5_hex_idx ON rc_document_md5_rel_1(md5_hex);

CREATE TABLE rc_document_md5_rel_2 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_2_md5_hex_idx ON rc_document_md5_rel_2(md5_hex);

CREATE TABLE rc_document_md5_rel_3 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_3_md5_hex_idx ON rc_document_md5_rel_3(md5_hex);

CREATE TABLE rc_document_md5_rel_4 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_4_md5_hex_idx ON rc_document_md5_rel_4(md5_hex);

CREATE TABLE rc_document_md5_rel_5 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_5_md5_hex_idx ON rc_document_md5_rel_5(md5_hex);

CREATE TABLE rc_document_md5_rel_6 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_6_md5_hex_idx ON rc_document_md5_rel_6(md5_hex);

CREATE TABLE rc_document_md5_rel_7 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_7_md5_hex_idx ON rc_document_md5_rel_7(md5_hex);

CREATE TABLE rc_document_md5_rel_8 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_8_md5_hex_idx ON rc_document_md5_rel_8(md5_hex);

CREATE TABLE rc_document_md5_rel_9 (
                                       id BIGINT NOT NULL PRIMARY KEY,
                                       record_id BIGINT NOT NULL,
                                       doc_id BIGINT NOT NULL,
                                       ctype INT NOT NULL COMMENT '文档类型',
                                       md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_9_md5_hex_idx ON rc_document_md5_rel_9(md5_hex);

CREATE TABLE rc_document_md5_rel_10 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_10_md5_hex_idx ON rc_document_md5_rel_10(md5_hex);

CREATE TABLE rc_document_md5_rel_11 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_11_md5_hex_idx ON rc_document_md5_rel_11(md5_hex);

CREATE TABLE rc_document_md5_rel_12 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_12_md5_hex_idx ON rc_document_md5_rel_12(md5_hex);

CREATE TABLE rc_document_md5_rel_13 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_13_md5_hex_idx ON rc_document_md5_rel_13(md5_hex);

CREATE TABLE rc_document_md5_rel_14 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_14_md5_hex_idx ON rc_document_md5_rel_14(md5_hex);

CREATE TABLE rc_document_md5_rel_15 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_15_md5_hex_idx ON rc_document_md5_rel_15(md5_hex);

CREATE TABLE rc_document_md5_rel_16 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_16_md5_hex_idx ON rc_document_md5_rel_16(md5_hex);

CREATE TABLE rc_document_md5_rel_17 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_17_md5_hex_idx ON rc_document_md5_rel_17(md5_hex);

CREATE TABLE rc_document_md5_rel_18 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_18_md5_hex_idx ON rc_document_md5_rel_18(md5_hex);

CREATE TABLE rc_document_md5_rel_19 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_19_md5_hex_idx ON rc_document_md5_rel_19(md5_hex);