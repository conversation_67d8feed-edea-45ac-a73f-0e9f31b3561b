
CREATE TABLE "plss-record".rc_file_md5_rel_0 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_0" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_0" ON "plss-record".rc_file_md5_rel_0(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_0.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_0.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_0.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_1 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_1" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_1" ON "plss-record".rc_file_md5_rel_1(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_1.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_1.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_1.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_2 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_2" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_2" ON "plss-record".rc_file_md5_rel_2(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_2.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_2.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_2.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_3 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_3" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_3" ON "plss-record".rc_file_md5_rel_3(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_3.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_3.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_3.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_4 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_4" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_4" ON "plss-record".rc_file_md5_rel_4(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_4.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_4.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_4.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_5 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_5" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_5" ON "plss-record".rc_file_md5_rel_5(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_5.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_5.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_5.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_6 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_6" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_6" ON "plss-record".rc_file_md5_rel_6(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_6.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_6.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_6.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_7 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_7" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_7" ON "plss-record".rc_file_md5_rel_7(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_7.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_7.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_7.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_8 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_8" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_8" ON "plss-record".rc_file_md5_rel_8(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_8.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_8.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_8.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_9 (
                                                 id bigint NOT NULL,
                                                 md5_hex varchar(255) NOT NULL,
                                                 file_id bigint NOT NULL,
                                                 CONSTRAINT "rc_file_md5_rel_pkey_9" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_9" ON "plss-record".rc_file_md5_rel_9(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_9.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_9.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_9.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_10 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_10" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_10" ON "plss-record".rc_file_md5_rel_10(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_10.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_10.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_10.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_11 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_11" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_11" ON "plss-record".rc_file_md5_rel_11(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_11.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_11.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_11.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_12 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_12" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_12" ON "plss-record".rc_file_md5_rel_12(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_12.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_12.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_12.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_13 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_13" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_13" ON "plss-record".rc_file_md5_rel_13(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_13.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_13.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_13.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_14 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_14" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_14" ON "plss-record".rc_file_md5_rel_14(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_14.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_14.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_14.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_15 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_15" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_15" ON "plss-record".rc_file_md5_rel_15(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_15.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_15.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_15.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_16 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_16" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_16" ON "plss-record".rc_file_md5_rel_16(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_16.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_16.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_16.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_17 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_17" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_17" ON "plss-record".rc_file_md5_rel_17(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_17.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_17.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_17.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_18 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_18" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_18" ON "plss-record".rc_file_md5_rel_18(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_18.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_18.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_18.file_id IS '文件id';
CREATE TABLE "plss-record".rc_file_md5_rel_19 (
                                                  id bigint NOT NULL,
                                                  md5_hex varchar(255) NOT NULL,
                                                  file_id bigint NOT NULL,
                                                  CONSTRAINT "rc_file_md5_rel_pkey_19" PRIMARY KEY (id)
);

CREATE INDEX "rc_file_md5_rel_md5_hex_idx_19" ON "plss-record".rc_file_md5_rel_19(md5_hex);

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_19.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_19.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_md5_rel_19.file_id IS '文件id';


CREATE TABLE "plss-record".rc_file_0 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_0" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_0.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_0.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_0.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_0.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_0.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_0.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_0.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_0.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_0.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_0.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_0.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_0.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_0.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_0.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_0.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_0 IS '文件存储';

CREATE TABLE "plss-record".rc_file_1 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_1" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_1.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_1.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_1.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_1.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_1.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_1.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_1.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_1.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_1.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_1.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_1.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_1.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_1.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_1.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_1.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_1 IS '文件存储';

CREATE TABLE "plss-record".rc_file_2 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_2" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_2.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_2.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_2.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_2.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_2.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_2.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_2.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_2.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_2.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_2.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_2.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_2.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_2.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_2.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_2.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_2 IS '文件存储';

CREATE TABLE "plss-record".rc_file_3 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_3" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_3.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_3.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_3.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_3.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_3.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_3.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_3.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_3.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_3.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_3.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_3.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_3.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_3.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_3.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_3.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_3 IS '文件存储';

CREATE TABLE "plss-record".rc_file_4 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_4" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_4.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_4.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_4.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_4.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_4.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_4.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_4.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_4.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_4.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_4.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_4.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_4.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_4.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_4.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_4.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_4 IS '文件存储';

CREATE TABLE "plss-record".rc_file_5 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_5" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_5.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_5.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_5.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_5.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_5.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_5.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_5.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_5.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_5.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_5.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_5.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_5.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_5.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_5.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_5.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_5 IS '文件存储';

CREATE TABLE "plss-record".rc_file_6 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_6" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_6.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_6.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_6.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_6.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_6.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_6.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_6.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_6.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_6.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_6.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_6.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_6.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_6.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_6.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_6.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_6 IS '文件存储';

CREATE TABLE "plss-record".rc_file_7 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_7" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_7.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_7.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_7.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_7.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_7.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_7.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_7.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_7.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_7.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_7.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_7.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_7.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_7.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_7.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_7.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_7 IS '文件存储';

CREATE TABLE "plss-record".rc_file_8 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_8" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_8.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_8.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_8.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_8.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_8.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_8.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_8.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_8.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_8.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_8.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_8.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_8.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_8.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_8.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_8.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_8 IS '文件存储';

CREATE TABLE "plss-record".rc_file_9 (
                                         id bigint NOT NULL,
                                         platform varchar(30) NOT NULL,
                                         file_type int NOT NULL,
                                         origin_name varchar(255) NOT NULL,
                                         file_name varchar(255) NOT NULL,
                                         md5_hex varchar(64) NOT NULL,
                                         file_size int NOT NULL,
                                         file_ext varchar(255) NOT NULL,
                                         relative_path varchar(255) NOT NULL,
                                         encryption int NOT NULL,
                                         encrypt_version int,
                                         encrypt_password varchar(255),
                                         encrypt_magic_number varchar(32),
                                         create_by bigint NOT NULL,
                                         create_time timestamp NOT NULL,
                                         CONSTRAINT "rc_file_pkey_9" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_9.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_9.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_9.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_9.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_9.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_9.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_9.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_9.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_9.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_9.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_9.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_9.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_9.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_9.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_9.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_9 IS '文件存储';

CREATE TABLE "plss-record".rc_file_10 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_10" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_10.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_10.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_10.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_10.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_10.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_10.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_10.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_10.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_10.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_10.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_10.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_10.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_10.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_10.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_10.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_10 IS '文件存储';

CREATE TABLE "plss-record".rc_file_11 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_11" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_11.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_11.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_11.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_11.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_11.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_11.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_11.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_11.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_11.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_11.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_11.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_11.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_11.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_11.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_11.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_11 IS '文件存储';

CREATE TABLE "plss-record".rc_file_12 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_12" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_12.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_12.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_12.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_12.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_12.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_12.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_12.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_12.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_12.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_12.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_12.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_12.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_12.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_12.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_12.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_12 IS '文件存储';

CREATE TABLE "plss-record".rc_file_13 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_13" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_13.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_13.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_13.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_13.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_13.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_13.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_13.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_13.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_13.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_13.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_13.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_13.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_13.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_13.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_13.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_13 IS '文件存储';

CREATE TABLE "plss-record".rc_file_14 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_14" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_14.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_14.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_14.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_14.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_14.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_14.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_14.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_14.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_14.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_14.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_14.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_14.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_14.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_14.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_14.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_14 IS '文件存储';

CREATE TABLE "plss-record".rc_file_15 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_15" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_15.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_15.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_15.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_15.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_15.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_15.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_15.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_15.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_15.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_15.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_15.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_15.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_15.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_15.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_15.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_15 IS '文件存储';

CREATE TABLE "plss-record".rc_file_16 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_16" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_16.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_16.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_16.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_16.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_16.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_16.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_16.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_16.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_16.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_16.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_16.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_16.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_16.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_16.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_16.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_16 IS '文件存储';

CREATE TABLE "plss-record".rc_file_17 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_17" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_17.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_17.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_17.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_17.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_17.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_17.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_17.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_17.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_17.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_17.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_17.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_17.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_17.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_17.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_17.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_17 IS '文件存储';

CREATE TABLE "plss-record".rc_file_18 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_18" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_18.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_18.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_18.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_18.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_18.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_18.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_18.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_18.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_18.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_18.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_18.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_18.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_18.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_18.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_18.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_18 IS '文件存储';

CREATE TABLE "plss-record".rc_file_19 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_19" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_19.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_19.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_19.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_19.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_19.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_19.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_19.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_19.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_19.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_19.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_19.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_19.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_19.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_19.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_19.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_19 IS '文件存储';

CREATE TABLE "plss-record".rc_file_20 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_20" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_20.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_20.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_20.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_20.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_20.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_20.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_20.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_20.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_20.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_20.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_20.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_20.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_20.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_20.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_20.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_20 IS '文件存储';

CREATE TABLE "plss-record".rc_file_21 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_21" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_21.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_21.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_21.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_21.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_21.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_21.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_21.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_21.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_21.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_21.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_21.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_21.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_21.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_21.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_21.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_21 IS '文件存储';

CREATE TABLE "plss-record".rc_file_22 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_22" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_22.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_22.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_22.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_22.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_22.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_22.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_22.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_22.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_22.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_22.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_22.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_22.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_22.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_22.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_22.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_22 IS '文件存储';

CREATE TABLE "plss-record".rc_file_23 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_23" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_23.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_23.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_23.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_23.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_23.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_23.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_23.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_23.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_23.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_23.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_23.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_23.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_23.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_23.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_23.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_23 IS '文件存储';

CREATE TABLE "plss-record".rc_file_24 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_24" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_24.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_24.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_24.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_24.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_24.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_24.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_24.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_24.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_24.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_24.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_24.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_24.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_24.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_24.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_24.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_24 IS '文件存储';

CREATE TABLE "plss-record".rc_file_25 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_25" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_25.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_25.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_25.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_25.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_25.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_25.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_25.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_25.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_25.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_25.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_25.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_25.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_25.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_25.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_25.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_25 IS '文件存储';

CREATE TABLE "plss-record".rc_file_26 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_26" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_26.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_26.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_26.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_26.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_26.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_26.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_26.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_26.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_26.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_26.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_26.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_26.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_26.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_26.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_26.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_26 IS '文件存储';

CREATE TABLE "plss-record".rc_file_27 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_27" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_27.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_27.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_27.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_27.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_27.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_27.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_27.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_27.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_27.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_27.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_27.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_27.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_27.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_27.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_27.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_27 IS '文件存储';

CREATE TABLE "plss-record".rc_file_28 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_28" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_28.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_28.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_28.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_28.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_28.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_28.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_28.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_28.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_28.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_28.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_28.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_28.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_28.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_28.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_28.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_28 IS '文件存储';

CREATE TABLE "plss-record".rc_file_29 (
                                          id bigint NOT NULL,
                                          platform varchar(30) NOT NULL,
                                          file_type int NOT NULL,
                                          origin_name varchar(255) NOT NULL,
                                          file_name varchar(255) NOT NULL,
                                          md5_hex varchar(64) NOT NULL,
                                          file_size int NOT NULL,
                                          file_ext varchar(255) NOT NULL,
                                          relative_path varchar(255) NOT NULL,
                                          encryption int NOT NULL,
                                          encrypt_version int,
                                          encrypt_password varchar(255),
                                          encrypt_magic_number varchar(32),
                                          create_by bigint NOT NULL,
                                          create_time timestamp NOT NULL,
                                          CONSTRAINT "rc_file_pkey_29" PRIMARY KEY (id)
);

COMMENT ON COLUMN "plss-record".rc_file_29.id IS 'id';

COMMENT ON COLUMN "plss-record".rc_file_29.platform IS '存储平台代码';

COMMENT ON COLUMN "plss-record".rc_file_29.file_type IS '文件类型，1项目数据，2内置数据';

COMMENT ON COLUMN "plss-record".rc_file_29.origin_name IS '文件原始名';

COMMENT ON COLUMN "plss-record".rc_file_29.file_name IS '文件名';

COMMENT ON COLUMN "plss-record".rc_file_29.md5_hex IS '文件md5';

COMMENT ON COLUMN "plss-record".rc_file_29.file_size IS '文件大小';

COMMENT ON COLUMN "plss-record".rc_file_29.file_ext IS '文件扩展名';

COMMENT ON COLUMN "plss-record".rc_file_29.relative_path IS '相对路径';

COMMENT ON COLUMN "plss-record".rc_file_29.encryption IS '是否加密';

COMMENT ON COLUMN "plss-record".rc_file_29.encrypt_version IS '加密版本号';

COMMENT ON COLUMN "plss-record".rc_file_29.encrypt_password IS '基础密码';

COMMENT ON COLUMN "plss-record".rc_file_29.encrypt_magic_number IS '魔数';

COMMENT ON COLUMN "plss-record".rc_file_29.create_by IS '创建人id';

COMMENT ON COLUMN "plss-record".rc_file_29.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_file_29 IS '文件存储';

CREATE TABLE "plss-record".rc_document_0 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_0_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_0_record_id_idx" ON "plss-record".rc_document_0(record_id);
COMMENT ON COLUMN "plss-record".rc_document_0.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_0.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_0.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_0.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_0.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_0.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_0.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_0.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_0.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_0.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_0.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_0.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_0.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_0 IS '文档表';

CREATE TABLE "plss-record".rc_document_1 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_1_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_1_record_id_idx" ON "plss-record".rc_document_1(record_id);
COMMENT ON COLUMN "plss-record".rc_document_1.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_1.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_1.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_1.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_1.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_1.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_1.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_1.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_1.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_1.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_1.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_1.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_1.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_1 IS '文档表';

CREATE TABLE "plss-record".rc_document_2 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_2_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_2_record_id_idx" ON "plss-record".rc_document_2(record_id);
COMMENT ON COLUMN "plss-record".rc_document_2.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_2.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_2.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_2.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_2.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_2.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_2.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_2.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_2.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_2.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_2.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_2.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_2.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_2 IS '文档表';

CREATE TABLE "plss-record".rc_document_3 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_3_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_3_record_id_idx" ON "plss-record".rc_document_3(record_id);
COMMENT ON COLUMN "plss-record".rc_document_3.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_3.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_3.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_3.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_3.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_3.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_3.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_3.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_3.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_3.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_3.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_3.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_3.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_3 IS '文档表';

CREATE TABLE "plss-record".rc_document_4 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_4_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_4_record_id_idx" ON "plss-record".rc_document_4(record_id);
COMMENT ON COLUMN "plss-record".rc_document_4.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_4.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_4.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_4.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_4.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_4.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_4.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_4.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_4.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_4.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_4.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_4.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_4.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_4 IS '文档表';

CREATE TABLE "plss-record".rc_document_5 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_5_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_5_record_id_idx" ON "plss-record".rc_document_5(record_id);
COMMENT ON COLUMN "plss-record".rc_document_5.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_5.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_5.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_5.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_5.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_5.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_5.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_5.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_5.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_5.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_5.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_5.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_5.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_5 IS '文档表';

CREATE TABLE "plss-record".rc_document_6 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_6_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_6_record_id_idx" ON "plss-record".rc_document_6(record_id);
COMMENT ON COLUMN "plss-record".rc_document_6.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_6.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_6.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_6.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_6.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_6.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_6.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_6.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_6.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_6.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_6.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_6.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_6.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_6 IS '文档表';

CREATE TABLE "plss-record".rc_document_7 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_7_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_7_record_id_idx" ON "plss-record".rc_document_7(record_id);
COMMENT ON COLUMN "plss-record".rc_document_7.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_7.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_7.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_7.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_7.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_7.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_7.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_7.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_7.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_7.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_7.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_7.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_7.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_7 IS '文档表';

CREATE TABLE "plss-record".rc_document_8 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_8_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_8_record_id_idx" ON "plss-record".rc_document_8(record_id);
COMMENT ON COLUMN "plss-record".rc_document_8.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_8.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_8.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_8.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_8.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_8.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_8.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_8.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_8.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_8.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_8.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_8.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_8.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_8 IS '文档表';

CREATE TABLE "plss-record".rc_document_9 (
                                             id BIGINT NOT NULL,
                                             record_id BIGINT NOT NULL,
                                             name varchar(256) NOT NULL,
                                             ctype INT NOT NULL,
                                             attachment_type INT,
                                             classified INT,
                                             file_id BIGINT NOT NULL,
                                             file_size BIGINT,
                                             file_md5 varchar(256) NOT NULL,
                                             ofd_file_id BIGINT,
                                             txt_file_id BIGINT,
                                             create_time timestamp NOT NULL,
                                             create_by BIGINT NOT NULL,
                                             modified_time timestamp NOT NULL,
                                             modified_by BIGINT NOT NULL,
                                             status INT,
                                             CONSTRAINT "rc_document_9_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_9_record_id_idx" ON "plss-record".rc_document_9(record_id);
COMMENT ON COLUMN "plss-record".rc_document_9.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_9.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_9.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_9.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_9.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_9.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_9.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_9.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_9.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_9.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_9.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_9.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_9.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_9 IS '文档表';

CREATE TABLE "plss-record".rc_document_10 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_10_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_10_record_id_idx" ON "plss-record".rc_document_10(record_id);
COMMENT ON COLUMN "plss-record".rc_document_10.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_10.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_10.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_10.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_10.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_10.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_10.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_10.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_10.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_10.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_10.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_10.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_10.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_10 IS '文档表';

CREATE TABLE "plss-record".rc_document_11 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_11_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_11_record_id_idx" ON "plss-record".rc_document_11(record_id);
COMMENT ON COLUMN "plss-record".rc_document_11.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_11.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_11.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_11.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_11.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_11.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_11.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_11.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_11.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_11.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_11.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_11.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_11.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_11 IS '文档表';

CREATE TABLE "plss-record".rc_document_12 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_12_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_12_record_id_idx" ON "plss-record".rc_document_12(record_id);
COMMENT ON COLUMN "plss-record".rc_document_12.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_12.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_12.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_12.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_12.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_12.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_12.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_12.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_12.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_12.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_12.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_12.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_12.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_12 IS '文档表';

CREATE TABLE "plss-record".rc_document_13 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_13_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_13_record_id_idx" ON "plss-record".rc_document_13(record_id);
COMMENT ON COLUMN "plss-record".rc_document_13.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_13.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_13.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_13.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_13.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_13.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_13.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_13.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_13.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_13.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_13.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_13.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_13.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_13 IS '文档表';

CREATE TABLE "plss-record".rc_document_14 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_14_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_14_record_id_idx" ON "plss-record".rc_document_14(record_id);
COMMENT ON COLUMN "plss-record".rc_document_14.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_14.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_14.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_14.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_14.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_14.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_14.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_14.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_14.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_14.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_14.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_14.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_14.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_14 IS '文档表';

CREATE TABLE "plss-record".rc_document_15 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_15_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_15_record_id_idx" ON "plss-record".rc_document_15(record_id);
COMMENT ON COLUMN "plss-record".rc_document_15.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_15.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_15.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_15.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_15.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_15.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_15.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_15.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_15.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_15.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_15.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_15.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_15.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_15 IS '文档表';

CREATE TABLE "plss-record".rc_document_16 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_16_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_16_record_id_idx" ON "plss-record".rc_document_16(record_id);
COMMENT ON COLUMN "plss-record".rc_document_16.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_16.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_16.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_16.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_16.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_16.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_16.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_16.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_16.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_16.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_16.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_16.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_16.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_16 IS '文档表';

CREATE TABLE "plss-record".rc_document_17 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_17_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_17_record_id_idx" ON "plss-record".rc_document_17(record_id);
COMMENT ON COLUMN "plss-record".rc_document_17.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_17.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_17.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_17.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_17.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_17.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_17.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_17.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_17.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_17.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_17.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_17.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_17.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_17 IS '文档表';

CREATE TABLE "plss-record".rc_document_18 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_18_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_18_record_id_idx" ON "plss-record".rc_document_18(record_id);
COMMENT ON COLUMN "plss-record".rc_document_18.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_18.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_18.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_18.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_18.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_18.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_18.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_18.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_18.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_18.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_18.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_18.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_18.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_18 IS '文档表';

CREATE TABLE "plss-record".rc_document_19 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_19_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_19_record_id_idx" ON "plss-record".rc_document_19(record_id);
COMMENT ON COLUMN "plss-record".rc_document_19.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_19.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_19.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_19.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_19.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_19.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_19.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_19.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_19.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_19.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_19.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_19.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_19.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_19 IS '文档表';

CREATE TABLE "plss-record".rc_document_20 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_20_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_20_record_id_idx" ON "plss-record".rc_document_20(record_id);
COMMENT ON COLUMN "plss-record".rc_document_20.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_20.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_20.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_20.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_20.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_20.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_20.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_20.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_20.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_20.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_20.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_20.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_20.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_20 IS '文档表';

CREATE TABLE "plss-record".rc_document_21 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_21_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_21_record_id_idx" ON "plss-record".rc_document_21(record_id);
COMMENT ON COLUMN "plss-record".rc_document_21.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_21.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_21.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_21.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_21.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_21.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_21.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_21.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_21.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_21.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_21.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_21.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_21.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_21 IS '文档表';

CREATE TABLE "plss-record".rc_document_22 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_22_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_22_record_id_idx" ON "plss-record".rc_document_22(record_id);
COMMENT ON COLUMN "plss-record".rc_document_22.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_22.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_22.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_22.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_22.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_22.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_22.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_22.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_22.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_22.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_22.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_22.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_22.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_22 IS '文档表';

CREATE TABLE "plss-record".rc_document_23 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_23_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_23_record_id_idx" ON "plss-record".rc_document_23(record_id);
COMMENT ON COLUMN "plss-record".rc_document_23.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_23.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_23.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_23.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_23.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_23.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_23.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_23.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_23.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_23.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_23.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_23.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_23.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_23 IS '文档表';

CREATE TABLE "plss-record".rc_document_24 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_24_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_24_record_id_idx" ON "plss-record".rc_document_24(record_id);
COMMENT ON COLUMN "plss-record".rc_document_24.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_24.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_24.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_24.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_24.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_24.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_24.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_24.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_24.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_24.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_24.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_24.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_24.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_24 IS '文档表';

CREATE TABLE "plss-record".rc_document_25 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_25_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_25_record_id_idx" ON "plss-record".rc_document_25(record_id);
COMMENT ON COLUMN "plss-record".rc_document_25.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_25.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_25.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_25.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_25.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_25.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_25.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_25.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_25.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_25.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_25.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_25.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_25.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_25 IS '文档表';

CREATE TABLE "plss-record".rc_document_26 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_26_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_26_record_id_idx" ON "plss-record".rc_document_26(record_id);
COMMENT ON COLUMN "plss-record".rc_document_26.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_26.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_26.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_26.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_26.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_26.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_26.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_26.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_26.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_26.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_26.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_26.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_26.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_26 IS '文档表';

CREATE TABLE "plss-record".rc_document_27 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_27_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_27_record_id_idx" ON "plss-record".rc_document_27(record_id);
COMMENT ON COLUMN "plss-record".rc_document_27.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_27.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_27.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_27.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_27.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_27.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_27.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_27.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_27.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_27.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_27.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_27.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_27.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_27 IS '文档表';

CREATE TABLE "plss-record".rc_document_28 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_28_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_28_record_id_idx" ON "plss-record".rc_document_28(record_id);
COMMENT ON COLUMN "plss-record".rc_document_28.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_28.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_28.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_28.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_28.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_28.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_28.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_28.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_28.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_28.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_28.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_28.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_28.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_28 IS '文档表';

CREATE TABLE "plss-record".rc_document_29 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_29_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_29_record_id_idx" ON "plss-record".rc_document_29(record_id);
COMMENT ON COLUMN "plss-record".rc_document_29.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_29.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_29.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_29.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_29.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_29.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_29.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_29.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_29.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_29.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_29.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_29.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_29.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_29 IS '文档表';

CREATE TABLE "plss-record".rc_document_30 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_30_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_30_record_id_idx" ON "plss-record".rc_document_30(record_id);
COMMENT ON COLUMN "plss-record".rc_document_30.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_30.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_30.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_30.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_30.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_30.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_30.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_30.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_30.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_30.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_30.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_30.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_30.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_30 IS '文档表';

CREATE TABLE "plss-record".rc_document_31 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_31_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_31_record_id_idx" ON "plss-record".rc_document_31(record_id);
COMMENT ON COLUMN "plss-record".rc_document_31.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_31.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_31.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_31.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_31.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_31.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_31.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_31.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_31.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_31.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_31.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_31.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_31.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_31 IS '文档表';

CREATE TABLE "plss-record".rc_document_32 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_32_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_32_record_id_idx" ON "plss-record".rc_document_32(record_id);
COMMENT ON COLUMN "plss-record".rc_document_32.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_32.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_32.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_32.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_32.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_32.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_32.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_32.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_32.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_32.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_32.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_32.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_32.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_32 IS '文档表';

CREATE TABLE "plss-record".rc_document_33 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_33_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_33_record_id_idx" ON "plss-record".rc_document_33(record_id);
COMMENT ON COLUMN "plss-record".rc_document_33.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_33.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_33.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_33.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_33.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_33.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_33.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_33.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_33.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_33.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_33.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_33.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_33.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_33 IS '文档表';

CREATE TABLE "plss-record".rc_document_34 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_34_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_34_record_id_idx" ON "plss-record".rc_document_34(record_id);
COMMENT ON COLUMN "plss-record".rc_document_34.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_34.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_34.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_34.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_34.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_34.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_34.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_34.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_34.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_34.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_34.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_34.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_34.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_34 IS '文档表';

CREATE TABLE "plss-record".rc_document_35 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_35_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_35_record_id_idx" ON "plss-record".rc_document_35(record_id);
COMMENT ON COLUMN "plss-record".rc_document_35.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_35.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_35.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_35.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_35.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_35.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_35.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_35.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_35.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_35.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_35.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_35.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_35.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_35 IS '文档表';

CREATE TABLE "plss-record".rc_document_36 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_36_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_36_record_id_idx" ON "plss-record".rc_document_36(record_id);
COMMENT ON COLUMN "plss-record".rc_document_36.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_36.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_36.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_36.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_36.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_36.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_36.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_36.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_36.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_36.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_36.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_36.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_36.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_36 IS '文档表';

CREATE TABLE "plss-record".rc_document_37 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_37_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_37_record_id_idx" ON "plss-record".rc_document_37(record_id);
COMMENT ON COLUMN "plss-record".rc_document_37.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_37.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_37.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_37.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_37.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_37.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_37.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_37.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_37.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_37.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_37.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_37.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_37.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_37 IS '文档表';

CREATE TABLE "plss-record".rc_document_38 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_38_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_38_record_id_idx" ON "plss-record".rc_document_38(record_id);
COMMENT ON COLUMN "plss-record".rc_document_38.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_38.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_38.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_38.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_38.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_38.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_38.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_38.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_38.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_38.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_38.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_38.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_38.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_38 IS '文档表';

CREATE TABLE "plss-record".rc_document_39 (
                                              id BIGINT NOT NULL,
                                              record_id BIGINT NOT NULL,
                                              name varchar(256) NOT NULL,
                                              ctype INT NOT NULL,
                                              attachment_type INT,
                                              classified INT,
                                              file_id BIGINT NOT NULL,
                                              file_size BIGINT,
                                              file_md5 varchar(256) NOT NULL,
                                              ofd_file_id BIGINT,
                                              txt_file_id BIGINT,
                                              create_time timestamp NOT NULL,
                                              create_by BIGINT NOT NULL,
                                              modified_time timestamp NOT NULL,
                                              modified_by BIGINT NOT NULL,
                                              status INT,
                                              CONSTRAINT "rc_document_39_pkey" PRIMARY KEY (id)
);
CREATE INDEX "rc_document_39_record_id_idx" ON "plss-record".rc_document_39(record_id);
COMMENT ON COLUMN "plss-record".rc_document_39.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_document_39.record_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_document_39.name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_document_39.ctype IS '文档类型(1:主文档, 2:附件)';
COMMENT ON COLUMN "plss-record".rc_document_39.file_size IS '文档大小';
COMMENT ON COLUMN "plss-record".rc_document_39.file_md5 IS '文档MD5值';
COMMENT ON COLUMN "plss-record".rc_document_39.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_document_39.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_document_39.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_document_39.modified_by IS '修改人';
COMMENT ON COLUMN "plss-record".rc_document_39.file_id IS '原件在对象存储中文件id';
COMMENT ON COLUMN "plss-record".rc_document_39.attachment_type IS '附件文件类型: 1-附件 2-签批单 3-草稿件 4-关联文件';
COMMENT ON COLUMN "plss-record".rc_document_39.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';
COMMENT ON TABLE "plss-record".rc_document_39 IS '文档表';

CREATE TABLE "plss-record".rc_document_md5_rel_0 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_0_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_0_md5_hex_idx" ON "plss-record".rc_document_md5_rel_0(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_0.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_0.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_0 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_1 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_1_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_1_md5_hex_idx" ON "plss-record".rc_document_md5_rel_1(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_1.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_1.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_1 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_2 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_2_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_2_md5_hex_idx" ON "plss-record".rc_document_md5_rel_2(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_2.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_2.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_2 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_3 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_3_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_3_md5_hex_idx" ON "plss-record".rc_document_md5_rel_3(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_3.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_3.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_3 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_4 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_4_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_4_md5_hex_idx" ON "plss-record".rc_document_md5_rel_4(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_4.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_4.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_4 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_5 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_5_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_5_md5_hex_idx" ON "plss-record".rc_document_md5_rel_5(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_5.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_5.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_5 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_6 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_6_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_6_md5_hex_idx" ON "plss-record".rc_document_md5_rel_6(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_6.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_6.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_6 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_7 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_7_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_7_md5_hex_idx" ON "plss-record".rc_document_md5_rel_7(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_7.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_7.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_7 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_8 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_8_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_8_md5_hex_idx" ON "plss-record".rc_document_md5_rel_8(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_8.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_8.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_8 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_9 (
                                                     id BIGINT NOT NULL,
                                                     record_id BIGINT NOT NULL,
                                                     doc_id BIGINT NOT NULL,
                                                     ctype INT NOT NULL,
                                                     md5_hex varchar(255) NOT NULL,
                                                     CONSTRAINT rc_document_md5_rel_9_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_9_md5_hex_idx" ON "plss-record".rc_document_md5_rel_9(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_9.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_9.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_9 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_10 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_10_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_10_md5_hex_idx" ON "plss-record".rc_document_md5_rel_10(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_10.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_10.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_10 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_11 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_11_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_11_md5_hex_idx" ON "plss-record".rc_document_md5_rel_11(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_11.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_11.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_11 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_12 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_12_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_12_md5_hex_idx" ON "plss-record".rc_document_md5_rel_12(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_12.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_12.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_12 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_13 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_13_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_13_md5_hex_idx" ON "plss-record".rc_document_md5_rel_13(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_13.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_13.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_13 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_14 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_14_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_14_md5_hex_idx" ON "plss-record".rc_document_md5_rel_14(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_14.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_14.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_14 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_15 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_15_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_15_md5_hex_idx" ON "plss-record".rc_document_md5_rel_15(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_15.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_15.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_15 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_16 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_16_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_16_md5_hex_idx" ON "plss-record".rc_document_md5_rel_16(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_16.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_16.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_16 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_17 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_17_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_17_md5_hex_idx" ON "plss-record".rc_document_md5_rel_17(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_17.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_17.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_17 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_18 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_18_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_18_md5_hex_idx" ON "plss-record".rc_document_md5_rel_18(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_18.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_18.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_18 IS 'document-md5关系表';

CREATE TABLE "plss-record".rc_document_md5_rel_19 (
                                                      id BIGINT NOT NULL,
                                                      record_id BIGINT NOT NULL,
                                                      doc_id BIGINT NOT NULL,
                                                      ctype INT NOT NULL,
                                                      md5_hex varchar(255) NOT NULL,
                                                      CONSTRAINT rc_document_md5_rel_19_pkey PRIMARY KEY (id)
);
CREATE INDEX "rc_document_md5_rel_19_md5_hex_idx" ON "plss-record".rc_document_md5_rel_19(md5_hex);
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_19.ctype IS '文档类型';
COMMENT ON COLUMN "plss-record".rc_document_md5_rel_19.md5_hex IS 'md5';
COMMENT ON TABLE "plss-record".rc_document_md5_rel_19 IS 'document-md5关系表';

