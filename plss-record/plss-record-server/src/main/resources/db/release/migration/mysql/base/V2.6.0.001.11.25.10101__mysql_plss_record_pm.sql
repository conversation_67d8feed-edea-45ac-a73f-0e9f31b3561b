-- 元数据新增别名字段
ALTER TABLE `plss_record`.`rc_metadata`
    ADD COLUMN `alias_name` varchar(128) NULL COMMENT '元数据项名称的别名' AFTER `fixed_data`,
ADD COLUMN `alias_name_view` int NOT NULL DEFAULT 2 COMMENT '元数据项名称的别名,是否使用别名展示,1-是，2-否' AFTER `alias_name`;

ALTER TABLE `plss_record`.`rc_record_type_metadata`
    ADD COLUMN `alias_name` varchar(128) NULL COMMENT '元数据项名称的别名' AFTER `remark`,
ADD COLUMN `alias_name_view` int NOT NULL DEFAULT 2 COMMENT '元数据项名称的别名,是否使用别名展示,1-是，2-否' AFTER `alias_name`;

alter table "plss-record"."rc_record_share" ADD invalid_status int(2) DEFAULT 1 COMMENT '效用状态(1有效2无效)';

ALTER TABLE plss_record.rc_metadata ADD opt_sort_standard_item SMALLINT DEFAULT 2 NOT NULL COMMENT '是否排序基准项。1-是，2-否';
ALTER TABLE plss_record.rc_record_type_metadata ADD opt_sort_standard_item SMALLINT DEFAULT 2 NOT NULL COMMENT '是否排序基准项。1-是，2-否';

