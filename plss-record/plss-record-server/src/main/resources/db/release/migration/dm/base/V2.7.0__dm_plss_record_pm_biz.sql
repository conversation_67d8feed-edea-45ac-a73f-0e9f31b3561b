CREATE TABLE "plss-record".rc_data_lake (
                                            id bigint NOT NULL,
                                            repo_id bigint NOT NULL,
                                            data_name varchar(64) NOT NULL,
                                            file_date timestamp(6) NOT NULL,
                                            file_size bigint NOT NULL,
                                            file_md5 varchar(64) NOT NULL,
                                            file_origin_name varchar(64) NOT NULL,
                                            status int NOT NULL,
                                            task_key varchar(64),
                                            task_id int8,
                                            CONSTRAINT rc_data_lake_pk PRIMARY KEY (id)
);
COMMENT ON COLUMN "plss-record".rc_data_lake.repo_id IS '文库id';
COMMENT ON COLUMN "plss-record".rc_data_lake.data_name IS '数据包名称';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_date IS '数据包日期';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_size IS '文件大小';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_md5 IS '文件md5';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_origin_name IS '文件原始名';
COMMENT ON COLUMN "plss-record".rc_data_lake.status IS '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功';
COMMENT ON COLUMN "plss-record".rc_data_lake.task_key IS '任务标识，数据分发服务返回';
COMMENT ON COLUMN "plss-record".rc_data_lake.task_id IS '任务id';

UPDATE "plss-record".rc_dataload_task SET task_type = 999 WHERE task_type IS NULL;
ALTER TABLE "plss-record".rc_export_filter ADD file_type INT DEFAULT 0;
COMMENT ON COLUMN "plss-record".rc_export_filter.file_type IS '文件类型';

ALTER TABLE "plss-record".rc_record_process_detail ADD COLUMN(ctype INT DEFAULT (1) NOT NULL );

COMMENT ON COLUMN "plss-record".rc_record_process_detail.ctype IS '文件类型1-主件2-附件';

-- 本地消息重试表
CREATE TABLE "plss-record".rc_retry_msg
(
    doc_id BIGINT NOT NULL,
    record_id BIGINT NOT NULL,
    biz_type INT NOT NULL,
    status INT,
    create_by BIGINT,
    create_time TIMESTAMP(6) NOT NULL,
    modified_time TIMESTAMP(6) NOT NULL,
     PRIMARY KEY(doc_id));

COMMENT ON TABLE "plss-record".rc_retry_msg IS '本地消息重试表';
COMMENT ON COLUMN "plss-record".rc_retry_msg.biz_type IS '业务类型:1-附件提取';
COMMENT ON COLUMN "plss-record".rc_retry_msg.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_retry_msg.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_retry_msg.doc_id IS '文档id';
COMMENT ON COLUMN "plss-record".rc_retry_msg.modified_time IS '修改时间';
COMMENT ON COLUMN "plss-record".rc_retry_msg.record_id IS '文件记录id';
COMMENT ON COLUMN "plss-record".rc_retry_msg.status IS '1-成功2-失败';

CREATE OR REPLACE  INDEX "idx_bizType_status" ON "plss-record".rc_retry_msg(biz_type ASC,status ASC);
CREATE OR REPLACE  INDEX "idx_recordId" ON "plss-record".rc_retry_msg(record_id ASC);
--添加文档与库的关联表
create table "plss-record".rc_record_repo
(
    id BIGINT not null ,
    record_id BIGINT not null ,
    repo_id BIGINT not null ,
    create_time TIMESTAMP(6) not null ,
    primary key(id)
);

comment on table "plss-record".rc_record_repo is '文档关联的文库';

comment on column "plss-record".rc_record_repo.record_id is '文档id';

comment on column "plss-record".rc_record_repo.repo_id is '文库id';

comment on column "plss-record".rc_record_repo.create_time is '创建时间';

create index "plss-record".idx_record_repo_record_id on "plss-record".rc_record_repo(record_id);


alter table "plss-record".rc_document_0 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_0.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_1 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_1.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_2 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_2.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_3 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_3.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_4 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_4.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_5 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_5.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_6 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_6.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_7 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_7.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_8 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_8.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_9 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_9.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_10 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_10.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_11 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_11.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_12 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_12.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_13 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_13.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_14 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_14.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_15 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_15.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_16 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_16.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_17 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_17.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_18 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_18.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_19 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_19.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_20 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_20.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_21 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_21.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_22 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_22.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_23 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_23.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_24 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_24.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_25 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_25.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_26 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_26.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_27 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_27.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_28 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_28.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_29 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_29.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_30 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_30.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_31 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_31.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_32 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_32.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_33 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_33.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_34 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_34.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_35 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_35.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_36 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_36.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_37 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_37.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_38 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_38.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

alter table "plss-record".rc_document_39 add column(doc_att_status INT default (2) not null );

comment on column "plss-record".rc_document_39.doc_att_status is 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

        -- 修改字段长度
ALTER TABLE "plss-record"."rc_record_borrow"
    MODIFY COLUMN org_name varchar(1024);