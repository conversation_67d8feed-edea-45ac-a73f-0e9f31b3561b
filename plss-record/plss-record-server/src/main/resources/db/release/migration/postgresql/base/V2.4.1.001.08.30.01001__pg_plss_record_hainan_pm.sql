-- "plss-record".rc_mq_retry_msg definition

-- Drop table

-- DROP TABLE "plss-record".rc_mq_retry_msg;

CREATE TABLE "plss-record".rc_mq_retry_msg (
                                               id int8 NOT NULL, -- 主键
                                               business_type int2 DEFAULT 0 NOT NULL, -- 业务类型:1-元数据修复
                                               msg varchar(5000) DEFAULT ''::character varying NOT NULL, -- 消息
                                               fail_times int4 DEFAULT 0 NOT NULL, -- 失败次数
                                               status int2 DEFAULT 0 NOT NULL, -- 1:待处理,2：已处理，3：待人工处理，4：人工确认，5：人工删除
                                               create_time timestamp NULL, -- 创建时间
                                               create_by int8 NULL, -- 创建人
                                               update_time timestamp NULL, -- 更新时间
                                               update_by int8 NULL, -- 更新人
                                               CONSTRAINT rc_mq_msg_retry_pk PRIMARY KEY (id)
);
COMMENT ON TABLE "plss-record".rc_mq_retry_msg IS 'mq重试消息';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.id IS '主键';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.business_type IS '业务类型:1-元数据修复';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.msg IS '消息';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.fail_times IS '失败次数';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.status IS '1:待处理,2：已处理，3：待人工处理，4：人工确认，5：人工删除';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.update_time IS '更新时间';
COMMENT ON COLUMN "plss-record".rc_mq_retry_msg.update_by IS '更新人';