
CREATE TABLE `plss_record`.`rc_data_lake_custom` (
                                                     `id` bigint NOT NULL,
                                                     `task_id` bigint DEFAULT NULL COMMENT '导入任务id',
                                                     `custom_key` varchar(64) NOT NULL COMMENT '定制key',
                                                     `data_name` varchar(64) NOT NULL COMMENT '数据包名称',
                                                     `file_date` datetime(6) NOT NULL COMMENT '数据包日期',
                                                     `file_size` bigint NOT NULL COMMENT '文件大小',
                                                     `file_md5` varchar(64) NOT NULL COMMENT '文件md5',
                                                     `file_origin_name` varchar(64) NOT NULL COMMENT '文件原始名',
                                                     `status` smallint NOT NULL COMMENT '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功，5:数据准备中',
                                                     `task_key` varchar(255) DEFAULT NULL COMMENT '任务标识，数据分发服务返回',
                                                     `data_dir` varchar(255) DEFAULT NULL COMMENT '目录',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `plss_record`.`rc_data_lake` ADD `data_dir` varchar(255) DEFAULT NULL COMMENT '目录';