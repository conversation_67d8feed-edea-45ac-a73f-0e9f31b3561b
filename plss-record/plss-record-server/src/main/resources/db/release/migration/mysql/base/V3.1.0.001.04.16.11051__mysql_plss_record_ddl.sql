ALTER TABLE `plss_record`.rc_repository
MODIFY COLUMN owner_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repository
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repository
MODIFY COLUMN modified_by VA<PERSON><PERSON>R(256) ;
ALTER TABLE `plss_record`.rc_repository
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repository
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_mq_retry_msg
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_mq_retry_msg
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_sample_template_file
MODIFY COLUMN update_by <PERSON><PERSON><PERSON><PERSON>(256) ;
ALTER TABLE `plss_record`.rc_sample_template_file
MODIFY COLUMN create_by VA<PERSON><PERSON><PERSON>(256) ;
ALTER TABLE `plss_record`.rc_template_generate
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_template_generate
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_metadata
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_metadata
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_resource_permission_log
MODIFY COLUMN visitor_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_resource_permission_log
MODIFY COLUMN operator_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_resource_permission
MODIFY COLUMN visitor_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_resource_manager
MODIFY COLUMN visitor_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_resource_manager
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repo_metadata
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repo_metadata
MODIFY COLUMN modify_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_repo_collect
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_material_quote
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_plan
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_plan
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_material_change
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_concept
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_concept
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_model
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_model
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_model
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_attribute
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_attribute
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_concept_attribute
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_concept_attribute
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_relation
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_relation
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_relation_attribute
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_knowledge_relation_attribute
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_rel
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_rel
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_history
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_history
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_retry_log
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_audit
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_process_detail
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge_attribute
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge_attribute
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge_relation
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_dataload_task
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_dataload_task
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_share
MODIFY COLUMN share_record_tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_share
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_share_object
MODIFY COLUMN share_object_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_sample_template_catalogue
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_sample_template_catalogue
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_data_process_task
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_data_process_task
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_data_process_task
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_reference_record
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_type
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_type
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_material
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_metadata_category
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_metadata_category
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_borrow
MODIFY COLUMN record_tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_borrow
MODIFY COLUMN borrow_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_borrow
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_borrow
MODIFY COLUMN audit_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_red_header_templates
MODIFY COLUMN owner_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_red_header_templates
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge_conflict
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_knowledge_conflict
MODIFY COLUMN update_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_plan_audit_user
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_plan_audit_user
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_rel
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_rel
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_proofread_record
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_proofread_record
MODIFY COLUMN file_create_user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_retry_msg
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_7
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_8
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_9
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_10
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_11
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_12
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_13
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_14
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_15
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_16
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_17
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_18
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_19
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_20
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_21
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_22
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_23
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_24
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_25
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_26
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_27
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_28
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_29
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_30
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_31
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_32
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_33
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_34
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_35
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_36
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_37
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_38
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_39
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_40
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_41
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_42
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_43
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_44
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_45
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_46
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_47
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_48
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_49
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_50
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_51
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_52
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_53
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_54
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_55
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_56
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_57
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_58
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_59
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_60
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_61
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_62
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_63
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_64
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_65
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_66
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_67
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_68
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_69
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_70
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_71
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_72
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_73
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_74
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_75
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_76
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_77
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_78
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_79
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_80
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_81
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_82
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_83
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_84
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_85
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_86
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_87
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_88
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_89
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_90
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_91
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_92
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_93
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_94
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_95
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_96
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_97
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_98
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_file_99
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_0
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_1
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_2
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_3
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_4
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_5
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_6
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_7
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_7
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_8
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_8
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_9
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_9
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_10
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_10
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_11
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_11
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_12
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_12
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_13
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_13
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_14
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_14
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_15
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_15
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_16
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_16
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_17
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_17
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_18
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_18
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_19
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_19
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_20
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_20
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_21
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_21
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_22
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_22
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_23
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_23
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_24
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_24
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_25
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_25
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_26
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_26
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_27
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_27
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_28
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_28
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_29
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_29
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_30
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_30
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_31
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_31
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_32
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_32
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_33
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_33
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_34
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_34
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_35
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_35
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_36
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_36
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_37
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_37
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_38
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_38
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_39
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_document_39
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_0
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_0
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_1
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_1
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_2
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_2
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_3
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_3
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_4
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_4
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_5
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_5
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_6
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_6
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_7
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_7
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_7
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_8
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_8
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_8
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_9
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_9
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_9
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_10
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_10
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_10
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_11
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_11
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_11
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_12
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_12
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_12
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_13
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_13
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_13
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_14
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_14
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_14
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_15
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_15
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_15
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_16
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_16
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_16
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_17
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_17
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_17
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_18
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_18
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_18
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_19
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_19
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_19
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_20
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_20
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_20
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_21
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_21
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_21
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_22
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_22
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_22
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_23
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_23
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_23
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_24
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_24
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_24
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_25
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_25
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_25
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_26
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_26
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_26
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_27
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_27
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_27
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_28
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_28
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_28
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_29
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_29
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_29
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_30
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_30
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_30
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_31
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_31
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_31
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_32
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_32
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_32
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_33
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_33
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_33
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_34
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_34
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_34
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_35
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_35
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_35
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_36
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_36
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_36
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_37
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_37
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_37
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_38
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_38
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_38
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_39
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_39
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_doc_process_39
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_0
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_0
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_1
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_1
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_2
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_2
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_3
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_3
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_4
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_4
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_5
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_5
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_6
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_6
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_7
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_7
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_7
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_8
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_8
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_8
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_9
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_9
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_9
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_10
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_10
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_10
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_11
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_11
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_11
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_12
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_12
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_12
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_13
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_13
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_13
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_14
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_14
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_14
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_15
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_15
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_15
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_16
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_16
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_16
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_17
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_17
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_17
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_18
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_18
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_18
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_19
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_19
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_19
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_20
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_20
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_20
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_21
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_21
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_21
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_22
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_22
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_22
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_23
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_23
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_23
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_24
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_24
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_24
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_25
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_25
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_25
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_26
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_26
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_26
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_27
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_27
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_27
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_28
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_28
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_28
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_29
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_29
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_29
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_30
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_30
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_30
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_31
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_31
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_31
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_32
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_32
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_32
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_33
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_33
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_33
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_34
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_34
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_34
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_35
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_35
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_35
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_36
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_36
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_36
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_37
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_37
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_37
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_38
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_38
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_38
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_39
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_39
MODIFY COLUMN tenant_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_record_39
MODIFY COLUMN modified_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2024_q1
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2024_q2
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2024_q3
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2024_q4
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2025_q1
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2025_q2
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2025_q3
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2025_q4
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2026_q1
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2026_q2
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2026_q3
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2026_q4
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2027_q1
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2027_q2
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2027_q3
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_log_recordview_2027_q4
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_0_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_1_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_2_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_3_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_4_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_5_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_0
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_1
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_2
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_3
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_4
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_5
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_folder_record_6_6
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_record`.rc_masking_range
MODIFY COLUMN range_id VARCHAR(256);