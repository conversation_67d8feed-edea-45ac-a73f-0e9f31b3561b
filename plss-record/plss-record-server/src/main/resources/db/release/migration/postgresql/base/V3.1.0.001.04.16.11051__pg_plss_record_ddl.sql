ALTER TABLE "plss-record".rc_repository ALTER COLUMN  owner_id  TYPE varchar(255) USING owner_id::varchar(255);
ALTER TABLE "plss-record".rc_repository ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_repository ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_repository ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_repository ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_mq_retry_msg ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_mq_retry_msg ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_sample_template_file ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_sample_template_file ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_template_generate ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_template_generate ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_metadata ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_metadata ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_resource_permission_log ALTER COLUMN  visitor_id  TYPE varchar(255) USING visitor_id::varchar(255);
ALTER TABLE "plss-record".rc_resource_permission_log ALTER COLUMN  operator_id  TYPE varchar(255) USING operator_id::varchar(255);
ALTER TABLE "plss-record".rc_resource_permission ALTER COLUMN  visitor_id  TYPE varchar(255) USING visitor_id::varchar(255);
ALTER TABLE "plss-record".rc_resource_manager ALTER COLUMN  visitor_id  TYPE varchar(255) USING visitor_id::varchar(255);
ALTER TABLE "plss-record".rc_resource_manager ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_repo_metadata ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_repo_metadata ALTER COLUMN  modify_by  TYPE varchar(255) USING modify_by::varchar(255);
ALTER TABLE "plss-record".rc_repo_collect ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_material_quote ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_plan ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_plan ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_material_change ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_concept ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_concept ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_model ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_model ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_model ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_attribute ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_attribute ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_concept_attribute ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_concept_attribute ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_relation ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_relation ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_relation_attribute ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_knowledge_relation_attribute ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_record_rel ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_rel ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_history ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_history ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_retry_log ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_audit ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_folder ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_folder ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_folder ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_process_detail ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge_attribute ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge_attribute ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge_relation ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_dataload_task ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_dataload_task ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_share ALTER COLUMN  share_record_tenant_id  TYPE varchar(255) USING share_record_tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_share ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_share_object ALTER COLUMN  share_object_id  TYPE varchar(255) USING share_object_id::varchar(255);
ALTER TABLE "plss-record".rc_sample_template_catalogue ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_sample_template_catalogue ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_data_process_task ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_data_process_task ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_data_process_task ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_reference_record ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_type ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_type ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_material ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_metadata_category ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_metadata_category ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_borrow ALTER COLUMN  record_tenant_id  TYPE varchar(255) USING record_tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_borrow ALTER COLUMN  borrow_by  TYPE varchar(255) USING borrow_by::varchar(255);
ALTER TABLE "plss-record".rc_record_borrow ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_record_borrow ALTER COLUMN  audit_by  TYPE varchar(255) USING audit_by::varchar(255);
ALTER TABLE "plss-record".rc_red_header_templates ALTER COLUMN  owner_id  TYPE varchar(255) USING owner_id::varchar(255);
ALTER TABLE "plss-record".rc_red_header_templates ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge_conflict ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_knowledge_conflict ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-record".rc_plan_audit_user ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_plan_audit_user ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_folder_rel ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_folder_rel ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_proofread_record ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_proofread_record ALTER COLUMN  file_create_user_id  TYPE varchar(255) USING file_create_user_id::varchar(255);
ALTER TABLE "plss-record".rc_retry_msg ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_7 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_8 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_9 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_10 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_11 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_12 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_13 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_14 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_15 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_16 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_17 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_18 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_19 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_20 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_21 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_22 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_23 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_24 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_25 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_26 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_27 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_28 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_29 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_30 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_31 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_32 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_33 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_34 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_35 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_36 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_37 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_38 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_39 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_40 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_41 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_42 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_43 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_44 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_45 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_46 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_47 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_48 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_49 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_50 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_51 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_52 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_53 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_54 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_55 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_56 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_57 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_58 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_59 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_60 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_61 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_62 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_63 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_64 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_65 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_66 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_67 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_68 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_69 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_70 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_71 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_72 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_73 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_74 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_75 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_76 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_77 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_78 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_79 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_80 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_81 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_82 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_83 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_84 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_85 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_86 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_87 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_88 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_89 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_90 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_91 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_92 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_93 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_94 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_95 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_96 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_97 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_98 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_file_99 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_0 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_1 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_2 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_3 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_4 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_5 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_6 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_7 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_7 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_8 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_8 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_9 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_9 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_10 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_10 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_11 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_11 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_12 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_12 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_13 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_13 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_14 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_14 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_15 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_15 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_16 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_16 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_17 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_17 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_18 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_18 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_19 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_19 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_20 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_20 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_21 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_21 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_22 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_22 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_23 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_23 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_24 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_24 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_25 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_25 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_26 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_26 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_27 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_27 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_28 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_28 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_29 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_29 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_30 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_30 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_31 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_31 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_32 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_32 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_33 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_33 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_34 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_34 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_35 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_35 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_36 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_36 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_37 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_37 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_38 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_38 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_document_39 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_document_39 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_0 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_0 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_1 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_1 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_2 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_2 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_3 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_3 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_4 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_4 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_5 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_5 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_6 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_6 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_7 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_7 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_7 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_8 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_8 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_8 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_9 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_9 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_9 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_10 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_10 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_10 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_11 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_11 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_11 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_12 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_12 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_12 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_13 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_13 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_13 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_14 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_14 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_14 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_15 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_15 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_15 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_16 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_16 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_16 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_17 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_17 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_17 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_18 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_18 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_18 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_19 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_19 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_19 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_20 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_20 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_20 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_21 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_21 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_21 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_22 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_22 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_22 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_23 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_23 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_23 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_24 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_24 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_24 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_25 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_25 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_25 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_26 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_26 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_26 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_27 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_27 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_27 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_28 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_28 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_28 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_29 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_29 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_29 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_30 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_30 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_30 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_31 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_31 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_31 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_32 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_32 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_32 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_33 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_33 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_33 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_34 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_34 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_34 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_35 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_35 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_35 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_36 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_36 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_36 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_37 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_37 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_37 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_38 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_38 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_38 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_39 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_39 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_doc_process_39 ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-record".rc_record_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_0 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_0 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_1 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_1 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_2 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_2 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_3 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_3 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_4 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_4 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_5 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_5 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_6 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_6 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_7 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_7 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_7 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_8 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_8 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_8 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_9 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_9 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_9 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_10 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_10 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_10 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_11 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_11 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_11 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_12 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_12 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_12 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_13 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_13 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_13 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_14 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_14 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_14 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_15 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_15 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_15 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_16 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_16 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_16 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_17 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_17 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_17 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_18 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_18 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_18 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_19 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_19 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_19 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_20 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_20 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_20 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_21 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_21 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_21 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_22 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_22 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_22 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_23 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_23 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_23 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_24 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_24 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_24 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_25 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_25 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_25 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_26 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_26 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_26 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_27 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_27 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_27 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_28 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_28 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_28 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_29 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_29 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_29 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_30 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_30 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_30 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_31 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_31 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_31 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_32 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_32 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_32 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_33 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_33 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_33 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_34 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_34 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_34 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_35 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_35 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_35 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_36 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_36 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_36 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_37 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_37 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_37 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_38 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_38 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_38 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_record_39 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_record_39 ALTER COLUMN  tenant_id  TYPE varchar(255) USING tenant_id::varchar(255);
ALTER TABLE "plss-record".rc_record_39 ALTER COLUMN  modified_by  TYPE varchar(255) USING modified_by::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2024_q1 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2024_q2 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2024_q3 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2024_q4 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2025_q1 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2025_q2 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2025_q3 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2025_q4 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2026_q1 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2026_q2 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2026_q3 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2026_q4 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2027_q1 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2027_q2 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2027_q3 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_log_recordview_2027_q4 ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_0_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_1_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_2_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_3_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_4_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_5_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_0 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_1 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_2 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_3 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_4 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_5 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_folder_record_6_6 ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-record".rc_masking_range ALTER COLUMN  range_id  TYPE varchar(255) USING range_id::varchar(255);
