-- record表增加oa_permission_json字段 用于OA推送文件权限
ALTER TABLE "plss-record"."rc_record"
    ADD COLUMN "oa_permission_json" text;

COMMENT ON COLUMN "plss-record"."rc_record"."oa_permission_json" IS 'OA推送文件权限json：[{"permissionGroupId":2,"visitorId":1,"vtype":1}]';

CREATE TABLE "plss-record".rc_red_header_templates (
                                                       id bigint NOT NULL,
                                                       template_name varchar(255) NULL,
                                                       thumbnail_id bigint NULL,
                                                       template_file_id bigint NULL,
                                                       owner_id bigint NULL,
                                                       exception_msg varchar(255) NULL,
                                                       tenant_id bigint NULL,
                                                       update_time timestamp(6) NULL,
                                                       create_time timestamp(6) NULL,
                                                       upload_time timestamp(6) NULL,
                                                       thumbnail_status int NULL,
                                                       CONSTRAINT rc_red_header_templates_pk PRIMARY KEY (id)
);

comment on table "plss-record".rc_red_header_templates is '套红模版';

comment on column "plss-record".rc_red_header_templates.id is '唯一id';

comment on column "plss-record".rc_red_header_templates.template_name is '模版名称';

comment on column "plss-record".rc_red_header_templates.exception_msg is '失败异常信息';

comment on column "plss-record".rc_red_header_templates.tenant_id is '租户id';

comment on column "plss-record".rc_red_header_templates.upload_time is '上传时间';

comment on column "plss-record".rc_red_header_templates.thumbnail_status is '获取缩略图是否成功 （1：成功，0：失败)';

-- 新建筛查表
CREATE TABLE "plss-record".rc_proofread_record
(
    id bigint NOT NULL, -- 唯一标识
    file_id bigint, -- 文件ID
    screening_time timestamp(6), -- 筛选时间
    issued_number varchar(255) NOT NULL, -- 发文单位
    issued_organ varchar(255) NOT NULL, -- 发文机关
    result_json text, -- 校对结果Json {key:value}，key：错误类型，value：数量
    create_time timestamp(6) NOT NULL, -- 创建时间
    create_by bigint NOT NULL, -- 创建人
    file_name varchar(255) NOT NULL, -- 文档名称
    file_create_user_id bigint NOT NULL, -- 文档创建人
    screening_status varchar(255) NOT NULL, -- 校对状态
    CONSTRAINT rc_proofread_record_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE "plss-record".rc_proofread_record IS '文档校对记录表';

COMMENT ON COLUMN "plss-record".rc_proofread_record.id IS '唯一标识';
COMMENT ON COLUMN "plss-record".rc_proofread_record.file_id IS '文件ID';
COMMENT ON COLUMN "plss-record".rc_proofread_record.screening_time IS '筛选时间';
COMMENT ON COLUMN "plss-record".rc_proofread_record.issued_number IS '发文单位';
COMMENT ON COLUMN "plss-record".rc_proofread_record.issued_organ IS '发文机关';
COMMENT ON COLUMN "plss-record".rc_proofread_record.result_json IS '校对结果Json {key:value}，key：错误类型，value：数量';
COMMENT ON COLUMN "plss-record".rc_proofread_record.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_proofread_record.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_proofread_record.file_name IS '文档名称';
COMMENT ON COLUMN "plss-record".rc_proofread_record.file_create_user_id IS '文档创建人';
COMMENT ON COLUMN "plss-record".rc_proofread_record.screening_status IS '校对状态';
