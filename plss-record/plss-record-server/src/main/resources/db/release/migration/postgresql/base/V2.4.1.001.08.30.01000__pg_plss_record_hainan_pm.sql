-- 标品海南需求

-- rc_doc_process新增”文件密级”字段
ALTER TABLE "plss-record".rc_doc_process
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_doc_process.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- rc_document新增”文件密级”字段
ALTER TABLE "plss-record".rc_document
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_document.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

ALTER TABLE "plss-record".rc_plan
    ADD store_type int DEFAULT 1 NOT NULL;
COMMENT
ON COLUMN "plss-record".rc_plan.store_type IS '入库类型：1-标准入库  2-安全入库';

-- rc_record新增”文件密级”字段
ALTER TABLE "plss-record".rc_record
    ADD classified int4 NULL;
COMMENT
ON COLUMN "plss-record".rc_record.classified IS '文件密级：100-秘密  200-机密,字典表：rc_file_classified';

-- 增加索引
CREATE INDEX rc_task_batch_create_time_idx ON "plss-record".rc_task_batch (create_time DESC);

ALTER TABLE "plss-record".rc_export_filter
    ADD COLUMN not_record_status character varying(64);
COMMENT
ON COLUMN "plss-record".rc_export_filter.not_record_status
    IS '排除文档状态';

-- 文件分享记录表
CREATE TABLE "plss-record".rc_record_share
(
    id                     int8         NOT NULL,
    share_record_id        int8         NOT NULL,
    share_record_name      varchar(512),
    share_record_tenant_id int8         NOT NULL,
    status                 int2         NOT NULL DEFAULT 1,
    del_flag               char(1)      NOT NULL DEFAULT 1,
    create_by              int8         NOT NULL,
    create_by_name         varchar(64)  NOT NULL,
    create_time            timestamp(6) NOT NULL DEFAULT now(),
    remark                 varchar(512),
    CONSTRAINT rc_record_share_pkey PRIMARY KEY ("id")
)
;

CREATE INDEX "idx_create_by" ON "plss-record".rc_record_share USING btree (create_by);

COMMENT
ON COLUMN "plss-record".rc_record_share.share_record_id IS '分享文件id';

COMMENT
ON COLUMN "plss-record".rc_record_share.share_record_name IS '分享文件名称';

COMMENT
ON COLUMN "plss-record"."rc_record_share"."share_record_tenant_id" IS '分享文件租户id';

COMMENT
ON COLUMN "plss-record".rc_record_share.status IS '状态';

COMMENT
ON COLUMN "plss-record".rc_record_share.del_flag IS '删除标识';

COMMENT
ON COLUMN "plss-record".rc_record_share.create_by IS '创建人（分享人）';

COMMENT
ON COLUMN "plss-record".rc_record_share.create_by_name IS '创建人名称（分享人名称）';

COMMENT
ON COLUMN "plss-record".rc_record_share.create_time IS '创建时间';

COMMENT
ON COLUMN "plss-record".rc_record_share.remark IS '备注（说明）';

COMMENT
ON TABLE "plss-record".rc_record_share IS '文件分享记录表';

-- 文件分享对象表
CREATE TABLE "plss-record".rc_record_share_object
(
    share_id          int8         NOT NULL,
    share_object_id   int8         NOT NULL,
    share_object_type int2         NOT NULL DEFAULT 1,
    create_time       timestamp(6) NOT NULL DEFAULT now(),
    CONSTRAINT rc_record_share_object_pkey PRIMARY KEY (share_id, share_object_id)
)
;

CREATE INDEX "idx_share_id" ON "plss-record".rc_record_share_object USING btree (share_id);

COMMENT
ON COLUMN "plss-record".rc_record_share_object.share_id IS '分享id';

COMMENT
ON COLUMN "plss-record".rc_record_share_object.share_object_id IS '分享对象id';

COMMENT
ON COLUMN "plss-record".rc_record_share_object.share_object_type IS '分享对象类型（1用户2机构）';

COMMENT
ON COLUMN "plss-record".rc_record_share_object.create_time IS '创建时间';

COMMENT
ON TABLE "plss-record".rc_record_share_object IS '文件分享对象表';

-- rc_log_recordview分表
CREATE TABLE "plss-record".rc_log_recordview_2024_q1
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2024_q1_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2024_q1" ON "plss-record".rc_log_recordview_2024_q1 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2024_q2
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2024_q2_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2024_q2" ON "plss-record".rc_log_recordview_2024_q2 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2024_q3
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2024_q3_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2024_q3" ON "plss-record".rc_log_recordview_2024_q3 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2024_q4
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2024_q4_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2024_q4" ON "plss-record".rc_log_recordview_2024_q4 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2025_q1
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2025_q1_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2025_q1" ON "plss-record".rc_log_recordview_2025_q1 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2025_q2
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2025_q2_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2025_q2" ON "plss-record".rc_log_recordview_2025_q2 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2025_q3
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2025_q3_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2025_q3" ON "plss-record".rc_log_recordview_2025_q3 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2025_q4
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2025_q4_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2025_q4" ON "plss-record".rc_log_recordview_2025_q4 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2026_q1
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2026_q1_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2026_q1" ON "plss-record".rc_log_recordview_2026_q1 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2026_q2
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2026_q2_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2026_q2" ON "plss-record".rc_log_recordview_2026_q2 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2026_q3
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2026_q3_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2026_q3" ON "plss-record".rc_log_recordview_2026_q3 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2026_q4
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2026_q4_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2026_q4" ON "plss-record".rc_log_recordview_2026_q4 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2027_q1
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2027_q1_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2027_q1" ON "plss-record".rc_log_recordview_2027_q1 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2027_q2
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2027_q2_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2027_q2" ON "plss-record".rc_log_recordview_2027_q2 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2027_q3
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2027_q3_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2027_q3" ON "plss-record".rc_log_recordview_2027_q3 USING btree ( record_id );
CREATE TABLE "plss-record".rc_log_recordview_2027_q4
(
    id             INT8         NOT NULL,
    record_id      INT8         NOT NULL,
    record_name    VARCHAR(256),
    user_id        INT8         NOT NULL,
    user_name      VARCHAR(256),
    view_ip        VARCHAR(256),
    view_useragent VARCHAR(256),
    view_os        VARCHAR(256),
    view_browser   VARCHAR(256),
    create_time    TIMESTAMP(6) NOT NULL DEFAULT now(),
    invalid_status INT2         NOT NULL DEFAULT 1,
    CONSTRAINT "rc_log_recordview_2027_q4_pkey" PRIMARY KEY (ID)
);
CREATE INDEX "idx_record_id_2027_q4" ON "plss-record".rc_log_recordview_2027_q4 USING btree ( record_id );

ALTER TABLE "plss-record".rc_repository
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_repository.classified IS '密级';
ALTER TABLE "plss-record".rc_folder
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_folder.classified IS '密级';
ALTER TABLE "plss-record".rc_resource_permission
    ADD classified int4;
COMMENT
ON COLUMN "plss-record".rc_resource_permission.classified IS '密级';

CREATE TABLE "plss-record".rc_repo_collect
(
    user_id     int8         NOT NULL,
    repo_id     int8         NOT NULL,
    create_time timestamp(6) NOT NULL,
    CONSTRAINT "rc_repo_collect_pkey" PRIMARY KEY (user_id, repo_id)
);
COMMENT
ON COLUMN "plss-record".rc_repo_collect.user_id IS '用户id';
COMMENT
ON COLUMN "plss-record".rc_repo_collect.repo_id IS '库id';
COMMENT
ON COLUMN "plss-record".rc_repo_collect.create_time IS '创建时间';
COMMENT
ON TABLE "plss-record".rc_repo_collect IS '用户收藏文库表';
-- "plss-record".rc_data_process_task definition
CREATE TABLE "plss-record".rc_data_process_task
(
    task_id          int8           NOT NULL, -- 主键
    business_type    int2           NOT NULL, -- 业务类型:1-元数据修复
    task_type        int2           NOT NULL, -- 任务类型:1-(1-修改名称,2-修改值类型,3-修改名称和值类型)
    business_data    text NULL,               -- 业务相关数据
    execute_status   int2           NOT NULL, -- 任务执行状态 100执行中  200重试待执行 300待执行 400中断  500已完成
    total_count      int8 DEFAULT 0 NOT NULL, -- 文件总数
    executed_count   int8 DEFAULT 0 NOT NULL, -- 执行完成数量
    finish_status    int2 DEFAULT 2 NOT NULL, -- 是否已完成 1-已完成-2未完成
    execute_time     timestamp NULL,          -- 执行时间
    task_start_time  timestamp NULL,          -- 任务开始时间
    task_end_time    timestamp NULL,          -- 任务结束时间
    process_way      int2 NULL,               -- 数据处理方式1-清楚2-保留
    offset_position  int8           NOT NULL, -- 执行的偏移量位置
    create_time      timestamp      NOT NULL, -- 创建时间
    modified_time    timestamp      NOT NULL, -- 修改时间
    modified_by      int8           NOT NULL, -- 修改人
    modified_by_name varchar(255) NULL,       -- 修改人姓名
    CONSTRAINT rc_data_process_task_pkey PRIMARY KEY (task_id)
);
COMMENT
ON TABLE "plss-record".rc_data_process_task IS '数据处理任务表';

-- Column comments
COMMENT
ON COLUMN "plss-record".rc_data_process_task.task_id IS '主键';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.business_type IS '业务类型:1-元数据修复';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.task_type IS '任务类型:1-(1-修改名称,2-修改值类型,3-修改名称和值类型)';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.business_data IS '业务相关数据';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.execute_status IS '任务执行状态 100执行中  200重试待执行 300待执行 400中断  500已完成';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.total_count IS '文件总数';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.executed_count IS '执行完成数量';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.finish_status IS '是否已完成 1-已完成-2未完成 ';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.execute_time IS '执行时间';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.task_start_time IS '任务开始时间';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.task_end_time IS '任务结束时间';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.process_way IS '数据处理方式1-清楚2-保留';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.offset_position IS '执行的偏移量位置';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.create_time IS '创建时间';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.modified_time IS '修改时间';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.modified_by IS '修改人';
COMMENT
ON COLUMN "plss-record".rc_data_process_task.modified_by_name IS '修改人姓名';

ALTER TABLE "plss-record".rc_data_process_task
    ADD create_by bigint NULL;
COMMENT
ON COLUMN "plss-record".rc_data_process_task.create_by IS '创建人';
ALTER TABLE "plss-record".rc_data_process_task
    ADD tenant_id bigint NULL;
COMMENT
ON COLUMN "plss-record".rc_data_process_task.tenant_id IS '租户id';

-- DML

--830工作报告库分类调整
delete
from "plss-record"."rc_repo_metadata"
where id = 250425069104134;
INSERT INTO "plss-record"."rc_repo_metadata" ("id", "repo_id", "type", "retrieve_type",
                                              "json_value", "create_by", "create_time", "modify_by",
                                              "modify_time")
VALUES (250425069104134, 2689875149573, 2, 2,
        '{"children":[{"children":[{"children":[{"fixed":false,"levelSwitch":false,"nodeId":4725903193349,"nodeName":"全国党代会工作报告","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804697164037,"nodeName":"两会政府工作报告","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804697311237,"nodeName":"全国人大工作报告","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804697464837,"nodeName":"最高人民法院工作报告","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804697614085,"nodeName":"最高人民检察院工作报告","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":4681559855621,"nodeName":"全国政协常委会工作报告","retrieveType":2}],"fixed":false,"levelSwitch":false,"nodeId":2804679708421,"nodeName":"国家级","retrieveType":2},{"children":[{"children":[{"fixed":false,"levelSwitch":false,"nodeId":218450809678277,"nodeName":"海淀区","retrieveType":2}],"fixed":false,"levelSwitch":false,"nodeId":2804707087109,"nodeName":"北京市","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804707252229,"nodeName":"天津市","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804707401989,"nodeName":"河北省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804707549957,"nodeName":"山西省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804707711237,"nodeName":"内蒙古自治区","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804707861765,"nodeName":"辽宁省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708009477,"nodeName":"吉林省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708154885,"nodeName":"黑龙江省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708304645,"nodeName":"上海市","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708451333,"nodeName":"江苏省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708598021,"nodeName":"浙江省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708747269,"nodeName":"安徽省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804708899333,"nodeName":"福建省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709049861,"nodeName":"江西省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709203461,"nodeName":"山东省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709357317,"nodeName":"河南省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709507589,"nodeName":"湖北省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709662981,"nodeName":"湖南省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709812229,"nodeName":"广东省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804709971461,"nodeName":"广西壮族自治区","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710130693,"nodeName":"海南省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710282245,"nodeName":"重庆市","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710429445,"nodeName":"四川省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710577157,"nodeName":"贵州省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710727685,"nodeName":"云南省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804710874373,"nodeName":"西藏自治区","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804711022085,"nodeName":"陕西省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804711174149,"nodeName":"甘肃省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804711326981,"nodeName":"青海省","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804711624197,"nodeName":"新疆维吾尔自治区","retrieveType":2},{"fixed":false,"levelSwitch":false,"nodeId":2804711475461,"nodeName":"宁夏回族自治区","retrieveType":2}],"fixed":false,"levelSwitch":false,"nodeId":2804681447941,"nodeName":"省市级","retrieveType":2}],"fixed":false,"levelSwitch":false,"nodeId":2804669962245,"nodeName":"发文机关","retrieveType":2}],"fixed":false,"levelSwitch":false,"nodeId":0,"retrieveType":2}',
        1612860642053, '2024-07-19 19:31:06.442', 1612860642053, '2024-07-19 19:31:06.442');

--830法律法规新增元数据
--元数据
delete
from "plss-record"."rc_metadata"
where id in (269267211693957, 269267070701445);
INSERT INTO "plss-record"."rc_metadata" ("id", "name", "name_en", "definition", "value_type",
                                         "value_range", "short_name", "search_flag", "status",
                                         "orderby", "create_time", "create_by", "modified_time",
                                         "modified_by", "opt_edit", "opt_view", "value_type_range",
                                         "required", "value_rule", "pinyin", "mnemonic",
                                         "borrow_view", "details_view", "search_result_view",
                                         "search_field_way", "remark", "fixed_data")
VALUES (269267211693957, '批准日期', NULL, NULL, 2, '^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$',
        NULL, 1, 1, 0, '2024-08-15 10:25:32.677', 1612860642053, '2024-08-15 10:25:32.677',
        1612860642053, 1, 1, NULL, 1,
        '{"formatType":"1","len":512,"selectItems":[],"unit":"","valueType":2}', 'pizhunriqi',
        'pzrq', 1, 1, 1, 1, '', 2);
INSERT INTO "plss-record"."rc_metadata" ("id", "name", "name_en", "definition", "value_type",
                                         "value_range", "short_name", "search_flag", "status",
                                         "orderby", "create_time", "create_by", "modified_time",
                                         "modified_by", "opt_edit", "opt_view", "value_type_range",
                                         "required", "value_rule", "pinyin", "mnemonic",
                                         "borrow_view", "details_view", "search_result_view",
                                         "search_field_way", "remark", "fixed_data")
VALUES (269267070701445, '通过日期', NULL, NULL, 2, '^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$',
        NULL, 1, 1, 0, '2024-08-15 10:25:15.504', 1612860642053, '2024-08-15 10:25:15.504',
        1612860642053, 1, 1, NULL, 1,
        '{"formatType":"1","len":512,"selectItems":[],"unit":"","valueType":2}', 'tongguoriqi',
        'tgrq', 1, 1, 1, 1, '', 2);

--元数据分组关联的元数据
delete
from "plss-record"."rc_metadata_category_metadata"
where md_id in (269267211693957, 269267070701445);
INSERT INTO "plss-record"."rc_metadata_category_metadata" ("category_id", "md_id")
VALUES (199472245712389, 269267070701445);
INSERT INTO "plss-record"."rc_metadata_category_metadata" ("category_id", "md_id")
VALUES (199472245712389, 269267211693957);

--文档类型关联的元数据
delete
from "plss-record"."rc_record_type_metadata"
where md_id in (269267211693957, 269267070701445);
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (2221158194437, 269267211693957, 2, '^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$', 1,
        199472245712389, 18, 2, 1, 1, NULL,
        '{"formatType":"1","len":512,"selectItems":[],"unit":"","valueType":2}', '批准日期',
        'pizhunriqi', 'pzrq', 1, 1, 1, 1, '');
INSERT INTO "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type",
                                                     "value_range", "search_flag", "mdcategory_id",
                                                     "orderby", "required", "opt_edit", "opt_view",
                                                     "value_type_range", "value_rule", "md_name",
                                                     "pinyin", "mnemonic", "borrow_view",
                                                     "details_view", "search_result_view",
                                                     "search_field_way", "remark")
VALUES (2221158194437, 269267070701445, 2, '^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$', 1,
        199472245712389, 19, 2, 1, 1, NULL,
        '{"formatType":"1","len":512,"selectItems":[],"unit":"","valueType":2}', '通过日期',
        'tongguoriqi', 'tgrq', 1, 1, 1, 1, '');
