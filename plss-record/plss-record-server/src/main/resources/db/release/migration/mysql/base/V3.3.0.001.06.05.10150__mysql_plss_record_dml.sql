UPDATE `plss_record`.rc_node
SET defined_config_json='{"checkBoxButtonList":[{"candidateCode":[700,-1],"code":700,"desc":"摘要提取"},{"candidateCode":[701,-1],"code":701,"desc":"主题分类"},{"candidateCode":[703,-1],"code":703,"desc":"主题词提取"},{"candidateCode":[704,-1],"code":704,"desc":"实体识别"},{"candidateCode":[705,-1],"code":705,"desc":"向量"},{"candidateCode":[708,-1],"code":708,"desc":"段落评分"}],"nodeType":7,"switchBoxButton":{"candidateCode":[706,-1],"code":706,"desc":"标引信息自动提取"}}'
WHERE id=2490549920517;

UPDATE `plss_record`.rc_plan_node set defined_config_json = replace(defined_config_json,'实体识别','实体识别"},{"candidateCode":[705,-1],"code": 705,"desc": "向量"},{"candidateCode":[708,-1],"code": 708,"desc": "段落评分')
where node_type = 7 and defined_config_json not like '%段落评分%';


update `plss_record`.rc_record_type set name = '我的文库-我的文档',remark = '我的文库-我的文档',fixed_data = 1 where id = 3465734962949;


INSERT INTO `plss_record`.rc_plan
(id, name, record_type_id, remark, status, create_time, modified_time, del_flag, last_flag, tenant_id, common_flag, show_front, create_by, store_type, fixed_data)
VALUES(476793031887301, '我的文库专有入库方案', 476789470439877, '', 1, '2025-06-04 15:17:54', '2025-06-04 15:17:54', 1, 1, 0, 2, 2, 1, 1, 1);

INSERT INTO `plss_record`.rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(476793031887301, 2490549604613, '{"checkBoxButtonList":[{"candidateCode":[1105,-1],"code":1105,"desc":"允许入库人员修改入库位置"}],"nodeType":1,"repoPositionButton":{"candidateCode":[1101,-1],"code":1101,"desc":"设置默认入库位置：","repoIdList":[]},"switchBoxButton":{"candidateCode":[1100,-1],"code":1100,"desc":"文件上传"}}', 0, 1, '2025-06-04 15:17:54', '2025-06-04 15:17:54', 1);
INSERT INTO `plss_record`.rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(476793031887301, 2490549730565, '{"checkBoxButtonList":[{"candidateCode":[501,-1],"code":501,"desc":"转换成OFD格式"},{"candidateCode":[500,-1],"code":500,"desc":"扫描件双层OFD处理"},{"candidateCode":[600,-1],"code":600,"desc":"提取正文"}],"nodeType":4,"switchBoxButton":{"candidateCode":[500,-1],"code":500,"desc":"文件转换"}}', 1, 1, '2025-06-04 15:17:54', '2025-06-04 15:17:54', 4);
INSERT INTO `plss_record`.rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(476793031887301, 2490549920517, '{"checkBoxButtonList":[{"candidateCode":[700,-1],"code":-1,"desc":"摘要提取"},{"candidateCode":[701,-1],"code":-1,"desc":"主题分类"},{"candidateCode":[703,-1],"code":-1,"desc":"主题词提取"},{"candidateCode":[704,-1],"code":-1,"desc":"实体识别"},{"candidateCode":[705,-1],"code":705,"desc":"向量"},{"candidateCode":[708,-1],"code":708,"desc":"段落评分"}],"nodeType":7,"switchBoxButton":{"candidateCode":[706,-1],"code":706,"desc":"标引信息自动提取"}}', 4, 1, '2025-06-04 15:17:54', '2025-06-04 15:17:54', 7);
INSERT INTO `plss_record`.rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(476793031887301, 2490550124037, '{"checkBoxButtonList":[{"candidateCode":[1501,-1],"code":-1,"desc":"系统自动生成封面"}],"nodeType":9,"switchBoxButton":{"candidateCode":[1500,-1],"code":1500,"desc":"入库"}}', 8, 1, '2025-06-04 15:17:54', '2025-06-04 15:17:54', 9);

DELETE FROM `plss_record`.rc_retry_msg WHERE biz_type = 1;

DELETE FROM `plss_record`.rc_record_process_detail WHERE ctype = 2;

UPDATE `plss_record`.rc_document_0  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_1  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_2  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_3  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_4  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_5  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_6  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_7  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_8  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_9  SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_10 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_11 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_12 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_13 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_14 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_15 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_16 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_17 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_18 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_19 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_20 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_21 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_22 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_23 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_24 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_25 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_26 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_27 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_28 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_29 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_30 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_31 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_32 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_33 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_34 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_35 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_36 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_37 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_38 SET doc_att_status = 2;
UPDATE `plss_record`.rc_document_39 SET doc_att_status = 2;


update `plss_record`.rc_plan_front_range set range_id_str = range_id where range_id_str is null;

update `plss_record`.rc_folder_record_0_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_0_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_1_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_2_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_3_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_4_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_5_6 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_0 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_1 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_2 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_3 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_4 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_5 set modified_time = create_time where modified_time is null;
update `plss_record`.rc_folder_record_6_6 set modified_time = create_time where modified_time is null;