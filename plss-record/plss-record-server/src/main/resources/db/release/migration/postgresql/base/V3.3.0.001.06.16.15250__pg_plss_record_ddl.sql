CREATE TABLE "plss-record".rc_data_lake_custom (
       id int8 NOT NULL,
       task_id int8 NULL, -- 导入任务id
       custom_key varchar(64) NOT NULL, -- 定制key
       data_name varchar(64) NOT NULL, -- 数据包名称
       file_date timestamp(6) NOT NULL, -- 数据包日期
       file_size int8 NOT NULL, -- 文件大小
       file_md5 varchar(64) NOT NULL, -- 文件md5
       file_origin_name varchar(64) NOT NULL, -- 文件原始名
       status int2 NOT NULL, -- 数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功，5:数据准备中
       task_key varchar(255) NULL, -- 任务标识，数据分发服务返回
       data_dir varchar(255) NULL, -- 目录
       CONSTRAINT rc_data_lake_custom_pk PRIMARY KEY (id)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN "plss-record".rc_data_lake_custom.task_id IS '导入任务id';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.custom_key IS '定制key';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.data_name IS '数据包名称';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_date IS '数据包日期';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_size IS '文件大小';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_md5 IS '文件md5';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_origin_name IS '文件原始名';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.status IS '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功，5:数据准备中';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.task_key IS '任务标识，数据分发服务返回';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.data_dir IS '目录';

ALTER TABLE "plss-record".rc_data_lake ADD data_dir varchar(255) NULL;
COMMENT ON COLUMN "plss-record".rc_data_lake.data_dir IS '目录';