CREATE TABLE rc_data_lake (
                              id BIGINT PRIMARY KEY COMMENT '数据包id',
                              repo_id BIGINT NOT NULL COMMENT '文库id',
                              data_name VARCHAR(64) NOT NULL COMMENT '数据包名称',
                              file_date TIMESTAMP(6) NOT NULL COMMENT '数据包日期',
                              file_size BIGINT NOT NULL COMMENT '文件大小',
                              file_md5 VARCHAR(64) NOT NULL COMMENT '文件md5',
                              file_origin_name VARCHAR(64) NOT NULL COMMENT '文件原始名',
                              status INT NOT NULL COMMENT '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功',
                              task_key VARCHAR(64) COMMENT '任务标识，数据分发服务返回',
                              task_id BIGINT COMMENT '任务id'
);

UPDATE rc_dataload_task SET task_type = 999 WHERE task_type IS NULL;

ALTER TABLE rc_record_process_detail
    ADD COLUMN ctype int NOT NULL DEFAULT 1 COMMENT '文件类型1-主件2-附件' AFTER doc_id;

-- 本地消息重试表
CREATE TABLE rc_retry_msg (
                                doc_id BIGINT NOT NULL COMMENT '文档id',
                                record_id BIGINT NOT NULL COMMENT '文件记录id',
                                biz_type INT NOT NULL COMMENT '业务类型:1-附件提取',
                                status INT DEFAULT NULL COMMENT '1-成功2-失败',
                                create_by BIGINT DEFAULT NULL COMMENT '创建人',
                                create_time DATETIME NOT NULL COMMENT '创建时间',
                                modified_time DATETIME NOT NULL COMMENT '修改时间',
                                PRIMARY KEY (doc_id),
    KEY idx_bizType_status (biz_type,status),
    KEY idx_recordId (record_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
--添加文档与库的关联表
CREATE TABLE rc_record_repo  (
                                 id bigint NOT NULL,
                                 record_id bigint NOT NULL COMMENT '文档id',
                                 repo_id bigint NOT NULL COMMENT '文库id',
                                 create_time datetime NULL COMMENT '创建时间',
                                                 PRIMARY KEY (id),
    INDEX idx_record_repo_record_id(record_id)
    ) COMMENT = '文档关联文库';

ALTER TABLE rc_export_filter ADD file_type INT DEFAULT 0 COMMENT '文件类型';

ALTER TABLE `plss_record`.`rc_document_0`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_1`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_2`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_3`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_4`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_5`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_6`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_7`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_8`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_9`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_10`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_11`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_12`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_13`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_14`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_15`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_16`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_17`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_18`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_19`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_20`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_21`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_22`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_23`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_24`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_25`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_26`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_27`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_28`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_29`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_30`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_31`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_32`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_33`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_34`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_35`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_36`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_37`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_38`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_39`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

-- 修改字段长度
ALTER TABLE rc_record_borrow MODIFY COLUMN org_name varchar(1024);