ALTER TABLE `plss_record`.`rc_record_process_detail`
    ADD COLUMN `ctype` int NOT NULL DEFAULT 1 COMMENT '文件类型1-主件2-附件' AFTER `doc_id`;

-- 本地消息重试表
CREATE TABLE `rc_retry_msg` (
                                `doc_id` bigint NOT NULL COMMENT '文档id',
                                `record_id` bigint NOT NULL COMMENT '文件记录id',
                                `biz_type` int NOT NULL COMMENT '业务类型:1-附件提取',
                                `status` int DEFAULT NULL COMMENT '1-成功2-失败',
                                `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                `modified_time` datetime NOT NULL COMMENT '修改时间',
                                PRIMARY KEY (`doc_id`),
    KEY `idx_bizType_status` (`biz_type`,`status`),
    KEY `idx_recordId` (`record_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
--添加文档与库的关联表
CREATE TABLE `plss_record`.`rc_record_repo`  (
                                                 `id` bigint NOT NULL,
                                                 `record_id` bigint NOT NULL COMMENT '文档id',
                                                 `repo_id` bigint NOT NULL COMMENT '文库id',
                                                 `create_time` datetime NULL COMMENT '创建时间',
                                                 PRIMARY KEY (`id`),
    INDEX `idx_record_repo_record_id`(`record_id`)
    ) COMMENT = '文档关联文库';

CREATE TABLE rc_data_lake (
                              id bigint PRIMARY KEY COMMENT '数据包id',
                              repo_id bigint NOT NULL COMMENT '文库id',
                              data_name varchar(64) NOT NULL COMMENT '数据包名称',
                              file_date timestamp(6) NOT NULL COMMENT '数据包日期',
                              file_size bigint NOT NULL COMMENT '文件大小',
                              file_md5 varchar(64) NOT NULL COMMENT '文件md5',
                              file_origin_name varchar(64) NOT NULL COMMENT '文件原始名',
                              status int NOT NULL COMMENT '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功',
                              task_key varchar(64) COMMENT '任务标识，数据分发服务返回',
                              task_id bigint COMMENT '任务id'
);
update rc_dataload_task set task_type =999 where task_type is null;
CREATE TABLE rc_doc_process_0 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_1 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_2 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_3 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_4 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_5 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_6 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_7 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_8 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_9 (
                                  record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                  plan_id bigint NOT NULL COMMENT '方案id',
                                  batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                  task_id bigint COMMENT '任务id',
                                  task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                  name varchar(256) COMMENT '文件名称',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                  process_status int NOT NULL COMMENT '处理状态',
                                  process_sub_status int NOT NULL COMMENT '处理子状态',
                                  audit_status int COMMENT '审核状态',
                                  ctype int COMMENT '1-主文件 2-子文件',
                                  tenant_id bigint COMMENT '租户id',
                                  title varchar(512) COMMENT '文档标题',
                                  pre_record_id bigint COMMENT '对应的个人库record_id',
                                  store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                  knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                  classified int COMMENT '密级',
                                  org_id bigint COMMENT '单位id',
                                  create_by bigint NOT NULL COMMENT '创建人',
                                  create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_10 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_11 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_12 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_13 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_14 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_15 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_16 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_17 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_18 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_19 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_20 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_21 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_22 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_23 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_24 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_25 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_26 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_27 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_28 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_29 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_30 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_31 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_32 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_33 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_34 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_35 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_36 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_37 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_38 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';

CREATE TABLE rc_doc_process_39 (
                                   record_id bigint NOT NULL PRIMARY KEY COMMENT '文档id',
                                   plan_id bigint NOT NULL COMMENT '方案id',
                                   batch_id bigint NOT NULL COMMENT '批次id，rc_task_batch表主键',
                                   task_id bigint COMMENT '任务id',
                                   task_doc_id bigint NOT NULL COMMENT '任务文件id，rc_task_doc表主键',
                                   name varchar(256) COMMENT '文件名称',
                                   record_type_id bigint NOT NULL COMMENT '文件类型id',
                                   origin int NOT NULL COMMENT '文档来源',
                                   real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                                   process_status int NOT NULL COMMENT '处理状态',
                                   process_sub_status int NOT NULL COMMENT '处理子状态',
                                   audit_status int COMMENT '审核状态',
                                   ctype int COMMENT '1-主文件 2-子文件',
                                   tenant_id bigint COMMENT '租户id',
                                   title varchar(512) COMMENT '文档标题',
                                   pre_record_id bigint COMMENT '对应的个人库record_id',
                                   store_way int DEFAULT 1 COMMENT '入库方式：1-后台  2-前台',
                                   knowledge_status int COMMENT '知识提取状态：1-知识提取中 2-知识提取异常 3-知识已提取',
                                   classified int COMMENT '密级',
                                   org_id bigint COMMENT '单位id',
                                   create_by bigint NOT NULL COMMENT '创建人',
                                   create_by_name varchar(256) NOT NULL COMMENT '创建人名称',
                                   create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                   modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '文档处理表';
CREATE TABLE rc_document_md5_rel_20 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_20_md5_hex_idx ON rc_document_md5_rel_20(md5_hex);


CREATE TABLE rc_document_md5_rel_21 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_21_md5_hex_idx ON rc_document_md5_rel_21(md5_hex);


CREATE TABLE rc_document_md5_rel_22 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_22_md5_hex_idx ON rc_document_md5_rel_22(md5_hex);


CREATE TABLE rc_document_md5_rel_23 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_23_md5_hex_idx ON rc_document_md5_rel_23(md5_hex);


CREATE TABLE rc_document_md5_rel_24 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_24_md5_hex_idx ON rc_document_md5_rel_24(md5_hex);


CREATE TABLE rc_document_md5_rel_25 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_25_md5_hex_idx ON rc_document_md5_rel_25(md5_hex);


CREATE TABLE rc_document_md5_rel_26 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_26_md5_hex_idx ON rc_document_md5_rel_26(md5_hex);


CREATE TABLE rc_document_md5_rel_27 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_27_md5_hex_idx ON rc_document_md5_rel_27(md5_hex);


CREATE TABLE rc_document_md5_rel_28 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_28_md5_hex_idx ON rc_document_md5_rel_28(md5_hex);


CREATE TABLE rc_document_md5_rel_29 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_29_md5_hex_idx ON rc_document_md5_rel_29(md5_hex);


CREATE TABLE rc_document_md5_rel_30 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_30_md5_hex_idx ON rc_document_md5_rel_30(md5_hex);


CREATE TABLE rc_document_md5_rel_31 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_31_md5_hex_idx ON rc_document_md5_rel_31(md5_hex);


CREATE TABLE rc_document_md5_rel_32 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_32_md5_hex_idx ON rc_document_md5_rel_32(md5_hex);


CREATE TABLE rc_document_md5_rel_33 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_33_md5_hex_idx ON rc_document_md5_rel_33(md5_hex);


CREATE TABLE rc_document_md5_rel_34 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_34_md5_hex_idx ON rc_document_md5_rel_34(md5_hex);


CREATE TABLE rc_document_md5_rel_35 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_35_md5_hex_idx ON rc_document_md5_rel_35(md5_hex);


CREATE TABLE rc_document_md5_rel_36 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_36_md5_hex_idx ON rc_document_md5_rel_36(md5_hex);


CREATE TABLE rc_document_md5_rel_37 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_37_md5_hex_idx ON rc_document_md5_rel_37(md5_hex);


CREATE TABLE rc_document_md5_rel_38 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_38_md5_hex_idx ON rc_document_md5_rel_38(md5_hex);


CREATE TABLE rc_document_md5_rel_39 (
                                        id BIGINT NOT NULL PRIMARY KEY,
                                        record_id BIGINT NOT NULL,
                                        doc_id BIGINT NOT NULL,
                                        ctype INT NOT NULL COMMENT '文档类型',
                                        md5_hex varchar(255) NOT NULL COMMENT 'md5'
);
CREATE INDEX rc_document_md5_rel_39_md5_hex_idx ON rc_document_md5_rel_39(md5_hex);

CREATE TABLE rc_file_md5_rel_20 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_20 ON rc_file_md5_rel_20(md5_hex);

CREATE TABLE rc_file_md5_rel_21 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_21 ON rc_file_md5_rel_21(md5_hex);

CREATE TABLE rc_file_md5_rel_22 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_22 ON rc_file_md5_rel_22(md5_hex);

CREATE TABLE rc_file_md5_rel_23 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_23 ON rc_file_md5_rel_23(md5_hex);

CREATE TABLE rc_file_md5_rel_24 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_24 ON rc_file_md5_rel_24(md5_hex);

CREATE TABLE rc_file_md5_rel_25 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_25 ON rc_file_md5_rel_25(md5_hex);

CREATE TABLE rc_file_md5_rel_26 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_26 ON rc_file_md5_rel_26(md5_hex);

CREATE TABLE rc_file_md5_rel_27 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_27 ON rc_file_md5_rel_27(md5_hex);

CREATE TABLE rc_file_md5_rel_28 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_28 ON rc_file_md5_rel_28(md5_hex);

CREATE TABLE rc_file_md5_rel_29 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_29 ON rc_file_md5_rel_29(md5_hex);

CREATE TABLE rc_file_md5_rel_30 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_30 ON rc_file_md5_rel_30(md5_hex);

CREATE TABLE rc_file_md5_rel_31 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_31 ON rc_file_md5_rel_31(md5_hex);

CREATE TABLE rc_file_md5_rel_32 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_32 ON rc_file_md5_rel_32(md5_hex);

CREATE TABLE rc_file_md5_rel_33 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_33 ON rc_file_md5_rel_33(md5_hex);

CREATE TABLE rc_file_md5_rel_34 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_34 ON rc_file_md5_rel_34(md5_hex);

CREATE TABLE rc_file_md5_rel_35 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_35 ON rc_file_md5_rel_35(md5_hex);

CREATE TABLE rc_file_md5_rel_36 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_36 ON rc_file_md5_rel_36(md5_hex);

CREATE TABLE rc_file_md5_rel_37 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_37 ON rc_file_md5_rel_37(md5_hex);

CREATE TABLE rc_file_md5_rel_38 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_38 ON rc_file_md5_rel_38(md5_hex);

CREATE TABLE rc_file_md5_rel_39 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_39 ON rc_file_md5_rel_39(md5_hex);

CREATE TABLE rc_file_md5_rel_40 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_40 ON rc_file_md5_rel_40(md5_hex);

CREATE TABLE rc_file_md5_rel_41 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_41 ON rc_file_md5_rel_41(md5_hex);

CREATE TABLE rc_file_md5_rel_42 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_42 ON rc_file_md5_rel_42(md5_hex);

CREATE TABLE rc_file_md5_rel_43 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_43 ON rc_file_md5_rel_43(md5_hex);

CREATE TABLE rc_file_md5_rel_44 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_44 ON rc_file_md5_rel_44(md5_hex);

CREATE TABLE rc_file_md5_rel_45 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_45 ON rc_file_md5_rel_45(md5_hex);

CREATE TABLE rc_file_md5_rel_46 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_46 ON rc_file_md5_rel_46(md5_hex);

CREATE TABLE rc_file_md5_rel_47 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_47 ON rc_file_md5_rel_47(md5_hex);

CREATE TABLE rc_file_md5_rel_48 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_48 ON rc_file_md5_rel_48(md5_hex);

CREATE TABLE rc_file_md5_rel_49 (
                                    id bigint NOT NULL PRIMARY KEY,
                                    md5_hex varchar(255) NOT NULL,
                                    file_id bigint NOT NULL
) COMMENT='文件md5关系表';
CREATE INDEX rc_file_md5_rel_md5_hex_idx_49 ON rc_file_md5_rel_49(md5_hex);

CREATE TABLE rc_file_30 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_31 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_32 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_33 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_34 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_35 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_36 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_37 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_38 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_39 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_40 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_41 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_42 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_43 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_44 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_45 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_46 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_47 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_48 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_49 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_50 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_51 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_52 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_53 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_54 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_55 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_56 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_57 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_58 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_59 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_60 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_61 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_62 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_63 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_64 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_65 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_66 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_67 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_68 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_69 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_70 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_71 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_72 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_73 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_74 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_75 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_76 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_77 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_78 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_79 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_80 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_81 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_82 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_83 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_84 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_85 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_86 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_87 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_88 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_89 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_90 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_91 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_92 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_93 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_94 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_95 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_96 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_97 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_98 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';

CREATE TABLE rc_file_99 (
                            id bigint NOT NULL PRIMARY KEY,
                            platform varchar(30) NOT NULL COMMENT '存储平台代码',
                            file_type int NOT NULL COMMENT '文件类型，1项目数据，2内置数据',
                            origin_name varchar(255) NOT NULL COMMENT '文件原始名',
                            file_name varchar(255) NOT NULL COMMENT '文件名',
                            md5_hex varchar(64) NOT NULL COMMENT '文件md5',
                            file_size int NOT NULL COMMENT '文件大小',
                            file_ext varchar(255) NOT NULL COMMENT '文件扩展名',
                            relative_path varchar(255) NOT NULL COMMENT '相对路径',
                            encryption int NOT NULL COMMENT '是否加密',
                            encrypt_version int COMMENT '加密版本号',
                            encrypt_password varchar(255) COMMENT '基础密码',
                            encrypt_magic_number varchar(32) COMMENT '魔数',
                            create_by bigint NOT NULL COMMENT '创建人id',
                            create_time timestamp NOT NULL COMMENT '创建时间'
) COMMENT='文件表';
CREATE TABLE rc_folder_record_0_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_0_idx ON rc_folder_record_0_0(record_id);

CREATE TABLE rc_folder_record_0_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_1_idx ON rc_folder_record_0_1(record_id);

CREATE TABLE rc_folder_record_0_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_2_idx ON rc_folder_record_0_2(record_id);

CREATE TABLE rc_folder_record_0_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_3_idx ON rc_folder_record_0_3(record_id);

CREATE TABLE rc_folder_record_0_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_4_idx ON rc_folder_record_0_4(record_id);

CREATE TABLE rc_folder_record_0_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_5_idx ON rc_folder_record_0_5(record_id);

CREATE TABLE rc_folder_record_0_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_0_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_0_6_idx ON rc_folder_record_0_6(record_id);

CREATE TABLE rc_folder_record_1_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_0_idx ON rc_folder_record_1_0(record_id);

CREATE TABLE rc_folder_record_1_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_1_idx ON rc_folder_record_1_1(record_id);

CREATE TABLE rc_folder_record_1_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_2_idx ON rc_folder_record_1_2(record_id);

CREATE TABLE rc_folder_record_1_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_3_idx ON rc_folder_record_1_3(record_id);

CREATE TABLE rc_folder_record_1_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_4_idx ON rc_folder_record_1_4(record_id);

CREATE TABLE rc_folder_record_1_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_5_idx ON rc_folder_record_1_5(record_id);

CREATE TABLE rc_folder_record_1_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_1_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_1_6_idx ON rc_folder_record_1_6(record_id);

CREATE TABLE rc_folder_record_2_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_0_idx ON rc_folder_record_2_0(record_id);

CREATE TABLE rc_folder_record_2_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_1_idx ON rc_folder_record_2_1(record_id);

CREATE TABLE rc_folder_record_2_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_2_idx ON rc_folder_record_2_2(record_id);

CREATE TABLE rc_folder_record_2_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_3_idx ON rc_folder_record_2_3(record_id);

CREATE TABLE rc_folder_record_2_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_4_idx ON rc_folder_record_2_4(record_id);

CREATE TABLE rc_folder_record_2_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_5_idx ON rc_folder_record_2_5(record_id);

CREATE TABLE rc_folder_record_2_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_2_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_2_6_idx ON rc_folder_record_2_6(record_id);

CREATE TABLE rc_folder_record_3_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_0_idx ON rc_folder_record_3_0(record_id);

CREATE TABLE rc_folder_record_3_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_1_idx ON rc_folder_record_3_1(record_id);

CREATE TABLE rc_folder_record_3_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_2_idx ON rc_folder_record_3_2(record_id);

CREATE TABLE rc_folder_record_3_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_3_idx ON rc_folder_record_3_3(record_id);

CREATE TABLE rc_folder_record_3_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_4_idx ON rc_folder_record_3_4(record_id);

CREATE TABLE rc_folder_record_3_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_5_idx ON rc_folder_record_3_5(record_id);

CREATE TABLE rc_folder_record_3_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_3_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_3_6_idx ON rc_folder_record_3_6(record_id);

CREATE TABLE rc_folder_record_4_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_0_idx ON rc_folder_record_4_0(record_id);

CREATE TABLE rc_folder_record_4_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_1_idx ON rc_folder_record_4_1(record_id);

CREATE TABLE rc_folder_record_4_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_2_idx ON rc_folder_record_4_2(record_id);

CREATE TABLE rc_folder_record_4_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_3_idx ON rc_folder_record_4_3(record_id);

CREATE TABLE rc_folder_record_4_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_4_idx ON rc_folder_record_4_4(record_id);

CREATE TABLE rc_folder_record_4_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_5_idx ON rc_folder_record_4_5(record_id);

CREATE TABLE rc_folder_record_4_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_4_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_4_6_idx ON rc_folder_record_4_6(record_id);

CREATE TABLE rc_folder_record_5_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_0_idx ON rc_folder_record_5_0(record_id);

CREATE TABLE rc_folder_record_5_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_1_idx ON rc_folder_record_5_1(record_id);

CREATE TABLE rc_folder_record_5_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_2_idx ON rc_folder_record_5_2(record_id);

CREATE TABLE rc_folder_record_5_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_3_idx ON rc_folder_record_5_3(record_id);

CREATE TABLE rc_folder_record_5_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_4_idx ON rc_folder_record_5_4(record_id);

CREATE TABLE rc_folder_record_5_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_5_idx ON rc_folder_record_5_5(record_id);

CREATE TABLE rc_folder_record_5_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_5_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_5_6_idx ON rc_folder_record_5_6(record_id);

CREATE TABLE rc_folder_record_6_0 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_0_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_0_idx ON rc_folder_record_6_0(record_id);

CREATE TABLE rc_folder_record_6_1 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_1_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_1_idx ON rc_folder_record_6_1(record_id);

CREATE TABLE rc_folder_record_6_2 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_2_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_2_idx ON rc_folder_record_6_2(record_id);

CREATE TABLE rc_folder_record_6_3 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_3_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_3_idx ON rc_folder_record_6_3(record_id);

CREATE TABLE rc_folder_record_6_4 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_4_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_4_idx ON rc_folder_record_6_4(record_id);

CREATE TABLE rc_folder_record_6_5 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_5_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_5_idx ON rc_folder_record_6_5(record_id);

CREATE TABLE rc_folder_record_6_6 (
                                      folder_id bigint NOT NULL COMMENT '目录id',
                                      record_id bigint NOT NULL COMMENT '文件id',
                                      create_by bigint COMMENT '创建人id',
                                      create_by_name varchar(255) COMMENT '创建人姓名',
                                      create_time timestamp(6) COMMENT '创建时间',
                                      CONSTRAINT rc_folder_record_6_6_pkey PRIMARY KEY (folder_id, record_id)
) COMMENT '目录-文件关联表';
CREATE INDEX record_id_6_6_idx ON rc_folder_record_6_6(record_id);

CREATE TABLE rc_record_0 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_0_put_lib_time_idx ON rc_record_0(put_lib_time);
CREATE INDEX rc_record_0_store_process_idx ON rc_record_0(store_process);

CREATE TABLE rc_record_1 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_1_put_lib_time_idx ON rc_record_1(put_lib_time);
CREATE INDEX rc_record_1_store_process_idx ON rc_record_1(store_process);

CREATE TABLE rc_record_2 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_2_put_lib_time_idx ON rc_record_2(put_lib_time);
CREATE INDEX rc_record_2_store_process_idx ON rc_record_2(store_process);

CREATE TABLE rc_record_3 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_3_put_lib_time_idx ON rc_record_3(put_lib_time);
CREATE INDEX rc_record_3_store_process_idx ON rc_record_3(store_process);

CREATE TABLE rc_record_4 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_4_put_lib_time_idx ON rc_record_4(put_lib_time);
CREATE INDEX rc_record_4_store_process_idx ON rc_record_4(store_process);

CREATE TABLE rc_record_5 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_5_put_lib_time_idx ON rc_record_5(put_lib_time);
CREATE INDEX rc_record_5_store_process_idx ON rc_record_5(store_process);

CREATE TABLE rc_record_6 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_6_put_lib_time_idx ON rc_record_6(put_lib_time);
CREATE INDEX rc_record_6_store_process_idx ON rc_record_6(store_process);

CREATE TABLE rc_record_7 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_7_put_lib_time_idx ON rc_record_7(put_lib_time);
CREATE INDEX rc_record_7_store_process_idx ON rc_record_7(store_process);

CREATE TABLE rc_record_8 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_8_put_lib_time_idx ON rc_record_8(put_lib_time);
CREATE INDEX rc_record_8_store_process_idx ON rc_record_8(store_process);

CREATE TABLE rc_record_9 (
                             id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                             recordtype_id bigint COMMENT '文件类型id',
                             name varchar(256) NOT NULL COMMENT '文件名称',
                             title varchar(512) COMMENT '标题',
                             digest varchar(4096) COMMENT '摘要',
                             origin int NOT NULL COMMENT '来源:接入方标识',
                             record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                             visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                             real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                             status int COMMENT '无效字段，兼容低版本导入',
                             process_status int COMMENT '无效字段，兼容低版本导入',
                             store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                             classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                             put_lib_time timestamp(6) COMMENT '入库时间',
                             oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                             tenant_id bigint NOT NULL COMMENT '租户id',
                             order_by int NOT NULL COMMENT '模板文件排序号',
                             create_time timestamp(6) NOT NULL COMMENT '创建时间',
                             create_by bigint NOT NULL COMMENT '创建人',
                             create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                             modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                             modified_by bigint NOT NULL COMMENT '修改人',
                             modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_9_put_lib_time_idx ON rc_record_9(put_lib_time);
CREATE INDEX rc_record_9_store_process_idx ON rc_record_9(store_process);

CREATE TABLE rc_record_10 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_10_put_lib_time_idx ON rc_record_10(put_lib_time);
CREATE INDEX rc_record_10_store_process_idx ON rc_record_10(store_process);

CREATE TABLE rc_record_11 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_11_put_lib_time_idx ON rc_record_11(put_lib_time);
CREATE INDEX rc_record_11_store_process_idx ON rc_record_11(store_process);

CREATE TABLE rc_record_12 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_12_put_lib_time_idx ON rc_record_12(put_lib_time);
CREATE INDEX rc_record_12_store_process_idx ON rc_record_12(store_process);

CREATE TABLE rc_record_13 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_13_put_lib_time_idx ON rc_record_13(put_lib_time);
CREATE INDEX rc_record_13_store_process_idx ON rc_record_13(store_process);

CREATE TABLE rc_record_14 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_14_put_lib_time_idx ON rc_record_14(put_lib_time);
CREATE INDEX rc_record_14_store_process_idx ON rc_record_14(store_process);

CREATE TABLE rc_record_15 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_15_put_lib_time_idx ON rc_record_15(put_lib_time);
CREATE INDEX rc_record_15_store_process_idx ON rc_record_15(store_process);

CREATE TABLE rc_record_16 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_16_put_lib_time_idx ON rc_record_16(put_lib_time);
CREATE INDEX rc_record_16_store_process_idx ON rc_record_16(store_process);

CREATE TABLE rc_record_17 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_17_put_lib_time_idx ON rc_record_17(put_lib_time);
CREATE INDEX rc_record_17_store_process_idx ON rc_record_17(store_process);

CREATE TABLE rc_record_18 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_18_put_lib_time_idx ON rc_record_18(put_lib_time);
CREATE INDEX rc_record_18_store_process_idx ON rc_record_18(store_process);

CREATE TABLE rc_record_19 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_19_put_lib_time_idx ON rc_record_19(put_lib_time);
CREATE INDEX rc_record_19_store_process_idx ON rc_record_19(store_process);

CREATE TABLE rc_record_20 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_20_put_lib_time_idx ON rc_record_20(put_lib_time);
CREATE INDEX rc_record_20_store_process_idx ON rc_record_20(store_process);

CREATE TABLE rc_record_21 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_21_put_lib_time_idx ON rc_record_21(put_lib_time);
CREATE INDEX rc_record_21_store_process_idx ON rc_record_21(store_process);

CREATE TABLE rc_record_22 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_22_put_lib_time_idx ON rc_record_22(put_lib_time);
CREATE INDEX rc_record_22_store_process_idx ON rc_record_22(store_process);

CREATE TABLE rc_record_23 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_23_put_lib_time_idx ON rc_record_23(put_lib_time);
CREATE INDEX rc_record_23_store_process_idx ON rc_record_23(store_process);

CREATE TABLE rc_record_24 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_24_put_lib_time_idx ON rc_record_24(put_lib_time);
CREATE INDEX rc_record_24_store_process_idx ON rc_record_24(store_process);

CREATE TABLE rc_record_25 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_25_put_lib_time_idx ON rc_record_25(put_lib_time);
CREATE INDEX rc_record_25_store_process_idx ON rc_record_25(store_process);

CREATE TABLE rc_record_26 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_26_put_lib_time_idx ON rc_record_26(put_lib_time);
CREATE INDEX rc_record_26_store_process_idx ON rc_record_26(store_process);

CREATE TABLE rc_record_27 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_27_put_lib_time_idx ON rc_record_27(put_lib_time);
CREATE INDEX rc_record_27_store_process_idx ON rc_record_27(store_process);

CREATE TABLE rc_record_28 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_28_put_lib_time_idx ON rc_record_28(put_lib_time);
CREATE INDEX rc_record_28_store_process_idx ON rc_record_28(store_process);

CREATE TABLE rc_record_29 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_29_put_lib_time_idx ON rc_record_29(put_lib_time);
CREATE INDEX rc_record_29_store_process_idx ON rc_record_29(store_process);

CREATE TABLE rc_record_30 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_30_put_lib_time_idx ON rc_record_30(put_lib_time);
CREATE INDEX rc_record_30_store_process_idx ON rc_record_30(store_process);

CREATE TABLE rc_record_31 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_31_put_lib_time_idx ON rc_record_31(put_lib_time);
CREATE INDEX rc_record_31_store_process_idx ON rc_record_31(store_process);

CREATE TABLE rc_record_32 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_32_put_lib_time_idx ON rc_record_32(put_lib_time);
CREATE INDEX rc_record_32_store_process_idx ON rc_record_32(store_process);

CREATE TABLE rc_record_33 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_33_put_lib_time_idx ON rc_record_33(put_lib_time);
CREATE INDEX rc_record_33_store_process_idx ON rc_record_33(store_process);

CREATE TABLE rc_record_34 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_34_put_lib_time_idx ON rc_record_34(put_lib_time);
CREATE INDEX rc_record_34_store_process_idx ON rc_record_34(store_process);

CREATE TABLE rc_record_35 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_35_put_lib_time_idx ON rc_record_35(put_lib_time);
CREATE INDEX rc_record_35_store_process_idx ON rc_record_35(store_process);

CREATE TABLE rc_record_36 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_36_put_lib_time_idx ON rc_record_36(put_lib_time);
CREATE INDEX rc_record_36_store_process_idx ON rc_record_36(store_process);

CREATE TABLE rc_record_37 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_37_put_lib_time_idx ON rc_record_37(put_lib_time);
CREATE INDEX rc_record_37_store_process_idx ON rc_record_37(store_process);

CREATE TABLE rc_record_38 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_38_put_lib_time_idx ON rc_record_38(put_lib_time);
CREATE INDEX rc_record_38_store_process_idx ON rc_record_38(store_process);

CREATE TABLE rc_record_39 (
                              id bigint NOT NULL PRIMARY KEY COMMENT '唯一标识',
                              recordtype_id bigint COMMENT '文件类型id',
                              name varchar(256) NOT NULL COMMENT '文件名称',
                              title varchar(512) COMMENT '标题',
                              digest varchar(4096) COMMENT '摘要',
                              origin int NOT NULL COMMENT '来源:接入方标识',
                              record_status int NOT NULL COMMENT '文件状态:100-待处理;200-处理中;300-审核中;400-已入库;500-异常文档',
                              visit_type int DEFAULT 1 COMMENT '1:继承，2：私有',
                              real_origin int COMMENT '细分文档来源：1-OA推送 2-用户上传 3-用户导入 999-外采 5-weboffice 6-personal 7-wps端上传 8-前台用户上传',
                              status int COMMENT '无效字段，兼容低版本导入',
                              process_status int COMMENT '无效字段，兼容低版本导入',
                              store_process int DEFAULT 1 COMMENT '1-没走入库流程  2-走了入库流程',
                              classified int COMMENT '文件密级：100-秘密  200-机密,字典表：rc_file_classified',
                              put_lib_time timestamp(6) COMMENT '入库时间',
                              oa_permission_json text COMMENT 'OA推送文件权限json：[{permissionGroupId:2,visitorId:1,vtype:1}]',
                              tenant_id bigint NOT NULL COMMENT '租户id',
                              order_by int NOT NULL COMMENT '模板文件排序号',
                              create_time timestamp(6) NOT NULL COMMENT '创建时间',
                              create_by bigint NOT NULL COMMENT '创建人',
                              create_by_name varchar(255) NOT NULL COMMENT '创建人姓名',
                              modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                              modified_by bigint NOT NULL COMMENT '修改人',
                              modified_by_name varchar(255) NOT NULL COMMENT '修改人姓名'
) COMMENT='文件表(文档合集)';

CREATE INDEX rc_record_39_put_lib_time_idx ON rc_record_39(put_lib_time);
CREATE INDEX rc_record_39_store_process_idx ON rc_record_39(store_process);

CREATE TABLE rc_task_0 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_0_batch_id_idx ON rc_task_0 (batch_id);
CREATE INDEX rc_task_0_plan_id_idx ON rc_task_0 (plan_id);

CREATE TABLE rc_task_1 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_1_batch_id_idx ON rc_task_1 (batch_id);
CREATE INDEX rc_task_1_plan_id_idx ON rc_task_1 (plan_id);

CREATE TABLE rc_task_2 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_2_batch_id_idx ON rc_task_2 (batch_id);
CREATE INDEX rc_task_2_plan_id_idx ON rc_task_2 (plan_id);

CREATE TABLE rc_task_3 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_3_batch_id_idx ON rc_task_3 (batch_id);
CREATE INDEX rc_task_3_plan_id_idx ON rc_task_3 (plan_id);

CREATE TABLE rc_task_4 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_4_batch_id_idx ON rc_task_4 (batch_id);
CREATE INDEX rc_task_4_plan_id_idx ON rc_task_4 (plan_id);

CREATE TABLE rc_task_5 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_5_batch_id_idx ON rc_task_5 (batch_id);
CREATE INDEX rc_task_5_plan_id_idx ON rc_task_5 (plan_id);

CREATE TABLE rc_task_6 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_6_batch_id_idx ON rc_task_6 (batch_id);
CREATE INDEX rc_task_6_plan_id_idx ON rc_task_6 (plan_id);

CREATE TABLE rc_task_7 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_7_batch_id_idx ON rc_task_7 (batch_id);
CREATE INDEX rc_task_7_plan_id_idx ON rc_task_7 (plan_id);

CREATE TABLE rc_task_8 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_8_batch_id_idx ON rc_task_8 (batch_id);
CREATE INDEX rc_task_8_plan_id_idx ON rc_task_8 (plan_id);

CREATE TABLE rc_task_9 (
                           id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                           name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                           batch_id bigint NOT NULL COMMENT '批次id',
                           plan_id bigint NOT NULL COMMENT '入库方案id',
                           create_time timestamp(6) NOT NULL COMMENT '创建时间',
                           modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_9_batch_id_idx ON rc_task_9 (batch_id);
CREATE INDEX rc_task_9_plan_id_idx ON rc_task_9 (plan_id);

CREATE TABLE rc_task_10 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_10_batch_id_idx ON rc_task_10 (batch_id);
CREATE INDEX rc_task_10_plan_id_idx ON rc_task_10 (plan_id);

CREATE TABLE rc_task_11 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_11_batch_id_idx ON rc_task_11 (batch_id);
CREATE INDEX rc_task_11_plan_id_idx ON rc_task_11 (plan_id);

CREATE TABLE rc_task_12 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_12_batch_id_idx ON rc_task_12 (batch_id);
CREATE INDEX rc_task_12_plan_id_idx ON rc_task_12 (plan_id);

CREATE TABLE rc_task_13 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_13_batch_id_idx ON rc_task_13 (batch_id);
CREATE INDEX rc_task_13_plan_id_idx ON rc_task_13 (plan_id);

CREATE TABLE rc_task_14 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_14_batch_id_idx ON rc_task_14 (batch_id);
CREATE INDEX rc_task_14_plan_id_idx ON rc_task_14 (plan_id);

CREATE TABLE rc_task_15 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_15_batch_id_idx ON rc_task_15 (batch_id);
CREATE INDEX rc_task_15_plan_id_idx ON rc_task_15 (plan_id);

CREATE TABLE rc_task_16 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_16_batch_id_idx ON rc_task_16 (batch_id);
CREATE INDEX rc_task_16_plan_id_idx ON rc_task_16 (plan_id);

CREATE TABLE rc_task_17 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_17_batch_id_idx ON rc_task_17 (batch_id);
CREATE INDEX rc_task_17_plan_id_idx ON rc_task_17 (plan_id);

CREATE TABLE rc_task_18 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_18_batch_id_idx ON rc_task_18 (batch_id);
CREATE INDEX rc_task_18_plan_id_idx ON rc_task_18 (plan_id);

CREATE TABLE rc_task_19 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_19_batch_id_idx ON rc_task_19 (batch_id);
CREATE INDEX rc_task_19_plan_id_idx ON rc_task_19 (plan_id);

CREATE TABLE rc_task_20 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_20_batch_id_idx ON rc_task_20 (batch_id);
CREATE INDEX rc_task_20_plan_id_idx ON rc_task_20 (plan_id);

CREATE TABLE rc_task_21 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_21_batch_id_idx ON rc_task_21 (batch_id);
CREATE INDEX rc_task_21_plan_id_idx ON rc_task_21 (plan_id);

CREATE TABLE rc_task_22 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_22_batch_id_idx ON rc_task_22 (batch_id);
CREATE INDEX rc_task_22_plan_id_idx ON rc_task_22 (plan_id);

CREATE TABLE rc_task_23 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_23_batch_id_idx ON rc_task_23 (batch_id);
CREATE INDEX rc_task_23_plan_id_idx ON rc_task_23 (plan_id);

CREATE TABLE rc_task_24 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_24_batch_id_idx ON rc_task_24 (batch_id);
CREATE INDEX rc_task_24_plan_id_idx ON rc_task_24 (plan_id);

CREATE TABLE rc_task_25 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_25_batch_id_idx ON rc_task_25 (batch_id);
CREATE INDEX rc_task_25_plan_id_idx ON rc_task_25 (plan_id);

CREATE TABLE rc_task_26 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_26_batch_id_idx ON rc_task_26 (batch_id);
CREATE INDEX rc_task_26_plan_id_idx ON rc_task_26 (plan_id);

CREATE TABLE rc_task_27 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_27_batch_id_idx ON rc_task_27 (batch_id);
CREATE INDEX rc_task_27_plan_id_idx ON rc_task_27 (plan_id);

CREATE TABLE rc_task_28 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_28_batch_id_idx ON rc_task_28 (batch_id);
CREATE INDEX rc_task_28_plan_id_idx ON rc_task_28 (plan_id);

CREATE TABLE rc_task_29 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_29_batch_id_idx ON rc_task_29 (batch_id);
CREATE INDEX rc_task_29_plan_id_idx ON rc_task_29 (plan_id);

CREATE TABLE rc_task_30 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_30_batch_id_idx ON rc_task_30 (batch_id);
CREATE INDEX rc_task_30_plan_id_idx ON rc_task_30 (plan_id);

CREATE TABLE rc_task_31 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_31_batch_id_idx ON rc_task_31 (batch_id);
CREATE INDEX rc_task_31_plan_id_idx ON rc_task_31 (plan_id);

CREATE TABLE rc_task_32 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_32_batch_id_idx ON rc_task_32 (batch_id);
CREATE INDEX rc_task_32_plan_id_idx ON rc_task_32 (plan_id);

CREATE TABLE rc_task_33 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_33_batch_id_idx ON rc_task_33 (batch_id);
CREATE INDEX rc_task_33_plan_id_idx ON rc_task_33 (plan_id);

CREATE TABLE rc_task_34 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_34_batch_id_idx ON rc_task_34 (batch_id);
CREATE INDEX rc_task_34_plan_id_idx ON rc_task_34 (plan_id);

CREATE TABLE rc_task_35 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_35_batch_id_idx ON rc_task_35 (batch_id);
CREATE INDEX rc_task_35_plan_id_idx ON rc_task_35 (plan_id);

CREATE TABLE rc_task_36 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_36_batch_id_idx ON rc_task_36 (batch_id);
CREATE INDEX rc_task_36_plan_id_idx ON rc_task_36 (plan_id);

CREATE TABLE rc_task_37 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_37_batch_id_idx ON rc_task_37 (batch_id);
CREATE INDEX rc_task_37_plan_id_idx ON rc_task_37 (plan_id);

CREATE TABLE rc_task_38 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_38_batch_id_idx ON rc_task_38 (batch_id);
CREATE INDEX rc_task_38_plan_id_idx ON rc_task_38 (plan_id);

CREATE TABLE rc_task_39 (
                            id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                            name varchar(512) NOT NULL COMMENT '任务名称（主文件名称）',
                            batch_id bigint NOT NULL COMMENT '批次id',
                            plan_id bigint NOT NULL COMMENT '入库方案id',
                            create_time timestamp(6) NOT NULL COMMENT '创建时间',
                            modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务表';
CREATE INDEX rc_task_39_batch_id_idx ON rc_task_39 (batch_id);
CREATE INDEX rc_task_39_plan_id_idx ON rc_task_39 (plan_id);
CREATE TABLE rc_task_batch_0 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_1 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_2 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_3 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_4 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_5 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_6 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_7 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_8 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_9 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 name varchar(256) NOT NULL COMMENT '批次名称',
                                 plan_id bigint NOT NULL COMMENT '入库方案id',
                                 record_type_id bigint NOT NULL COMMENT '文件类型id',
                                 origin int NOT NULL COMMENT '文档来源',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_10 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_11 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_12 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_13 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_14 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_15 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_16 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_17 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_18 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_19 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_20 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_21 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_22 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_23 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_24 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_25 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_26 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_27 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_28 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_29 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_30 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_31 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_32 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_33 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_34 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_35 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_36 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_37 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_38 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_batch_39 (
                                  id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                  name varchar(256) NOT NULL COMMENT '批次名称',
                                  plan_id bigint NOT NULL COMMENT '入库方案id',
                                  record_type_id bigint NOT NULL COMMENT '文件类型id',
                                  origin int NOT NULL COMMENT '文档来源',
                                  create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                  modified_time timestamp(6) NOT NULL COMMENT '修改时间'
);

CREATE TABLE rc_task_doc_0 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_0_record_id_idx ON rc_task_doc_0 (record_id);

CREATE TABLE rc_task_doc_1 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_1_record_id_idx ON rc_task_doc_1 (record_id);

CREATE TABLE rc_task_doc_2 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_2_record_id_idx ON rc_task_doc_2 (record_id);

CREATE TABLE rc_task_doc_3 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_3_record_id_idx ON rc_task_doc_3 (record_id);

CREATE TABLE rc_task_doc_4 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_4_record_id_idx ON rc_task_doc_4 (record_id);

CREATE TABLE rc_task_doc_5 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_5_record_id_idx ON rc_task_doc_5 (record_id);

CREATE TABLE rc_task_doc_6 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_6_record_id_idx ON rc_task_doc_6 (record_id);

CREATE TABLE rc_task_doc_7 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_7_record_id_idx ON rc_task_doc_7 (record_id);

CREATE TABLE rc_task_doc_8 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_8_record_id_idx ON rc_task_doc_8 (record_id);

CREATE TABLE rc_task_doc_9 (
                               id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                               name varchar(512) NOT NULL COMMENT '文件名称',
                               task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                               ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                               doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                               storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                               record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                               sub_record_ids text,
                               create_time timestamp(6) NOT NULL COMMENT '创建时间',
                               modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_9_record_id_idx ON rc_task_doc_9 (record_id);

CREATE TABLE rc_task_doc_10 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_10_record_id_idx ON rc_task_doc_10 (record_id);

CREATE TABLE rc_task_doc_11 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_11_record_id_idx ON rc_task_doc_11 (record_id);

CREATE TABLE rc_task_doc_12 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_12_record_id_idx ON rc_task_doc_12 (record_id);

CREATE TABLE rc_task_doc_13 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_13_record_id_idx ON rc_task_doc_13 (record_id);

CREATE TABLE rc_task_doc_14 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_14_record_id_idx ON rc_task_doc_14 (record_id);

CREATE TABLE rc_task_doc_15 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_15_record_id_idx ON rc_task_doc_15 (record_id);

CREATE TABLE rc_task_doc_16 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_16_record_id_idx ON rc_task_doc_16 (record_id);

CREATE TABLE rc_task_doc_17 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_17_record_id_idx ON rc_task_doc_17 (record_id);

CREATE TABLE rc_task_doc_18 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_18_record_id_idx ON rc_task_doc_18 (record_id);

CREATE TABLE rc_task_doc_19 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_19_record_id_idx ON rc_task_doc_19 (record_id);

CREATE TABLE rc_task_doc_20 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_20_record_id_idx ON rc_task_doc_20 (record_id);

CREATE TABLE rc_task_doc_21 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_21_record_id_idx ON rc_task_doc_21 (record_id);

CREATE TABLE rc_task_doc_22 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_22_record_id_idx ON rc_task_doc_22 (record_id);

CREATE TABLE rc_task_doc_23 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_23_record_id_idx ON rc_task_doc_23 (record_id);

CREATE TABLE rc_task_doc_24 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_24_record_id_idx ON rc_task_doc_24 (record_id);

CREATE TABLE rc_task_doc_25 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_25_record_id_idx ON rc_task_doc_25 (record_id);

CREATE TABLE rc_task_doc_26 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_26_record_id_idx ON rc_task_doc_26 (record_id);

CREATE TABLE rc_task_doc_27 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_27_record_id_idx ON rc_task_doc_27 (record_id);

CREATE TABLE rc_task_doc_28 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_28_record_id_idx ON rc_task_doc_28 (record_id);

CREATE TABLE rc_task_doc_29 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_29_record_id_idx ON rc_task_doc_29 (record_id);

CREATE TABLE rc_task_doc_30 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_30_record_id_idx ON rc_task_doc_30 (record_id);

CREATE TABLE rc_task_doc_31 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_31_record_id_idx ON rc_task_doc_31 (record_id);

CREATE TABLE rc_task_doc_32 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_32_record_id_idx ON rc_task_doc_32 (record_id);

CREATE TABLE rc_task_doc_33 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_33_record_id_idx ON rc_task_doc_33 (record_id);

CREATE TABLE rc_task_doc_34 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_34_record_id_idx ON rc_task_doc_34 (record_id);

CREATE TABLE rc_task_doc_35 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_35_record_id_idx ON rc_task_doc_35 (record_id);

CREATE TABLE rc_task_doc_36 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_36_record_id_idx ON rc_task_doc_36 (record_id);

CREATE TABLE rc_task_doc_37 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_37_record_id_idx ON rc_task_doc_37 (record_id);

CREATE TABLE rc_task_doc_38 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_38_record_id_idx ON rc_task_doc_38 (record_id);

CREATE TABLE rc_task_doc_39 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                name varchar(512) NOT NULL COMMENT '文件名称',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                doc_id bigint NOT NULL COMMENT '文档id，rc_document表的主键',
                                storage_info varchar(5120) NOT NULL COMMENT '上传至存储网关返回的信息：{"fileName":"","fileSize":,"fileMd5":"","objectName":"",}',
                                record_id bigint NOT NULL COMMENT '文件id，rc_record表的主键',
                                sub_record_ids text,
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间'
) COMMENT '任务文件表';

CREATE INDEX rc_task_doc_39_record_id_idx ON rc_task_doc_39 (record_id);

CREATE TABLE rc_task_flow_0 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_0_record_id_idx ON rc_task_flow_0 (record_id);

CREATE TABLE rc_task_flow_1 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_1_record_id_idx ON rc_task_flow_1 (record_id);

CREATE TABLE rc_task_flow_2 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_2_record_id_idx ON rc_task_flow_2 (record_id);

CREATE TABLE rc_task_flow_3 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_3_record_id_idx ON rc_task_flow_3 (record_id);

CREATE TABLE rc_task_flow_4 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_4_record_id_idx ON rc_task_flow_4 (record_id);

CREATE TABLE rc_task_flow_5 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_5_record_id_idx ON rc_task_flow_5 (record_id);

CREATE TABLE rc_task_flow_6 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_6_record_id_idx ON rc_task_flow_6 (record_id);

CREATE TABLE rc_task_flow_7 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_7_record_id_idx ON rc_task_flow_7 (record_id);

CREATE TABLE rc_task_flow_8 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_8_record_id_idx ON rc_task_flow_8 (record_id);

CREATE TABLE rc_task_flow_9 (
                                id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                node_id bigint NOT NULL COMMENT '所处节点id',
                                status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                deleted int NOT NULL COMMENT '1-正常 2-删除',
                                create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                record_id bigint NOT NULL COMMENT '文件id',
                                exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_9_record_id_idx ON rc_task_flow_9 (record_id);

CREATE TABLE rc_task_flow_10 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_10_record_id_idx ON rc_task_flow_10 (record_id);

CREATE TABLE rc_task_flow_11 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_11_record_id_idx ON rc_task_flow_11 (record_id);

CREATE TABLE rc_task_flow_12 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_12_record_id_idx ON rc_task_flow_12 (record_id);

CREATE TABLE rc_task_flow_13 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_13_record_id_idx ON rc_task_flow_13 (record_id);

CREATE TABLE rc_task_flow_14 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_14_record_id_idx ON rc_task_flow_14 (record_id);

CREATE TABLE rc_task_flow_15 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_15_record_id_idx ON rc_task_flow_15 (record_id);

CREATE TABLE rc_task_flow_16 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_16_record_id_idx ON rc_task_flow_16 (record_id);

CREATE TABLE rc_task_flow_17 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_17_record_id_idx ON rc_task_flow_17 (record_id);

CREATE TABLE rc_task_flow_18 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_18_record_id_idx ON rc_task_flow_18 (record_id);

CREATE TABLE rc_task_flow_19 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_19_record_id_idx ON rc_task_flow_19 (record_id);

CREATE TABLE rc_task_flow_20 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_20_record_id_idx ON rc_task_flow_20 (record_id);

CREATE TABLE rc_task_flow_21 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_21_record_id_idx ON rc_task_flow_21 (record_id);

CREATE TABLE rc_task_flow_22 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_22_record_id_idx ON rc_task_flow_22 (record_id);

CREATE TABLE rc_task_flow_23 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_23_record_id_idx ON rc_task_flow_23 (record_id);

CREATE TABLE rc_task_flow_24 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_24_record_id_idx ON rc_task_flow_24 (record_id);

CREATE TABLE rc_task_flow_25 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_25_record_id_idx ON rc_task_flow_25 (record_id);

CREATE TABLE rc_task_flow_26 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_26_record_id_idx ON rc_task_flow_26 (record_id);

CREATE TABLE rc_task_flow_27 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_27_record_id_idx ON rc_task_flow_27 (record_id);

CREATE TABLE rc_task_flow_28 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_28_record_id_idx ON rc_task_flow_28 (record_id);

CREATE TABLE rc_task_flow_29 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_29_record_id_idx ON rc_task_flow_29 (record_id);

CREATE TABLE rc_task_flow_30 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_30_record_id_idx ON rc_task_flow_30 (record_id);

CREATE TABLE rc_task_flow_31 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_31_record_id_idx ON rc_task_flow_31 (record_id);

CREATE TABLE rc_task_flow_32 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_32_record_id_idx ON rc_task_flow_32 (record_id);

CREATE TABLE rc_task_flow_33 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_33_record_id_idx ON rc_task_flow_33 (record_id);

CREATE TABLE rc_task_flow_34 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_34_record_id_idx ON rc_task_flow_34 (record_id);

CREATE TABLE rc_task_flow_35 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_35_record_id_idx ON rc_task_flow_35 (record_id);

CREATE TABLE rc_task_flow_36 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_36_record_id_idx ON rc_task_flow_36 (record_id);

CREATE TABLE rc_task_flow_37 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_37_record_id_idx ON rc_task_flow_37 (record_id);

CREATE TABLE rc_task_flow_38 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_38_record_id_idx ON rc_task_flow_38 (record_id);

CREATE TABLE rc_task_flow_39 (
                                 id bigint NOT NULL PRIMARY KEY COMMENT '主键',
                                 task_doc_id bigint NOT NULL COMMENT '任务文件表id，rc_task_doc表的主键',
                                 task_id bigint NOT NULL COMMENT '任务表id，rc_task表的主键',
                                 ctype int NOT NULL COMMENT '文件类型 1-主文件 2-附件  3-拆分子文件 4-上传子文件',
                                 node_id bigint NOT NULL COMMENT '所处节点id',
                                 status int NOT NULL COMMENT '所处节点时的状态 1-处理成功 2-处理失败 3-处理中 4-待处理',
                                 deleted int NOT NULL COMMENT '1-正常 2-删除',
                                 create_time timestamp(6) NOT NULL COMMENT '创建时间',
                                 modified_time timestamp(6) NOT NULL COMMENT '修改时间',
                                 node_type int NOT NULL COMMENT '节点类型1-文件上传2-添加附件3-文件脱敏4-文件转换 5-文件拆分6-元数据填充7-标引信息8-人工审核9-入库10-入内置库',
                                 record_id bigint NOT NULL COMMENT '文件id',
                                 exec_tag varchar(255) COMMENT '执行标识'
) COMMENT '任务文件流转表';
CREATE INDEX rc_task_flow_39_record_id_idx ON rc_task_flow_39 (record_id);

ALTER TABLE `plss_record`.`rc_document_0`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_1`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_2`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_3`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_4`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_5`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_6`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_7`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_8`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_9`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_10`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_11`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_12`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_13`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_14`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_15`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_16`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_17`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_18`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_19`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_20`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_21`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_22`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_23`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_24`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_25`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_26`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_27`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_28`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_29`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_30`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_31`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_32`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_33`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_34`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_35`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_36`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_37`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_38`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;

ALTER TABLE `plss_record`.`rc_document_39`
    ADD COLUMN `doc_att_status` int NOT NULL DEFAULT 2 COMMENT 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败' AFTER `classified`;


