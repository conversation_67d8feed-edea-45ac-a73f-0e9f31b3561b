ALTER TABLE "plss-record".rc_knowledge_concept_attribute
    ADD order_num int NULL DEFAULT 0;
COMMENT
ON COLUMN "plss-record".rc_knowledge_concept_attribute.order_num IS '排序';


ALTER TABLE "plss-record".rc_knowledge_relation
    ADD backward_name varchar(255) NULL;
COMMENT
ON COLUMN "plss-record".rc_knowledge_relation.backward_name IS '反向关系名称';

ALTER TABLE "plss-record".rc_knowledge_relation
    ADD symmetry char(1) NULL default '2';
COMMENT
ON COLUMN "plss-record".rc_knowledge_relation.symmetry IS '对称性 1对称  2非对称';
