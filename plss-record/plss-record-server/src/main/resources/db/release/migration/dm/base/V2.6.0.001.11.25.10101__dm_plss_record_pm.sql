-- 元数据新增别名字段
alter table "plss-record"."rc_metadata" add column("alias_name" VARCHAR(128));

alter table "plss-record"."rc_metadata" add column("alias_name_view" INT default (2) not null );

comment on column "plss-record"."rc_metadata"."alias_name" is '元数据项名称的别名';

comment on column "plss-record"."rc_metadata"."alias_name_view" is '元数据项名称的别名,是否使用别名展示,1-是，2-否';

alter table "plss-record"."rc_record_type_metadata" add column("alias_name" VARCHAR(128));

alter table "plss-record"."rc_record_type_metadata" add column("alias_name_view" INT default (2) not null );

comment on column "plss-record"."rc_record_type_metadata"."alias_name" is '元数据项名称的别名';

comment on column "plss-record"."rc_record_type_metadata"."alias_name_view" is '元数据项名称的别名,是否使用别名展示,1-是，2-否';

alter table "plss-record"."rc_record_share" ADD invalid_status SMALLINT DEFAULT 1;

COMMENT
ON COLUMN "plss-record"."rc_record_share".invalid_status IS '效用状态(1有效2无效)';

ALTER TABLE "plss-record".rc_metadata ADD opt_sort_standard_item smallint DEFAULT 2 NOT NULL;
COMMENT ON COLUMN "plss-record".rc_metadata.opt_sort_standard_item IS '是否排序基准项。1-是，2-否';
ALTER TABLE "plss-record".rc_record_type_metadata ADD opt_sort_standard_item smallint DEFAULT 2 NOT NULL;
COMMENT ON COLUMN "plss-record".rc_record_type_metadata.opt_sort_standard_item IS '是否排序基准项。1-是，2-否';