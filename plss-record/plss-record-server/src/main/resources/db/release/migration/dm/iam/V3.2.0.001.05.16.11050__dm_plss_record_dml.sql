update "plss-record".rc_node set status = 1 where node_type = 7;

-- 以下sql只能在金山IAM环境执行，数科项目环境千万不要执行！！！
UPDATE "plss-record".rc_resource_permission_log SET visitor_id = concat('org_', visitor_id) WHERE vtype = 1;
UPDATE "plss-record".rc_resource_permission_log SET visitor_id = concat('role_', visitor_id) WHERE vtype = 3;
UPDATE "plss-record".rc_resource_manager SET visitor_id = concat('org_', visitor_id) WHERE vtype = 1;
UPDATE "plss-record".rc_resource_manager SET visitor_id = concat('role_', visitor_id) WHERE vtype = 3;
UPDATE "plss-record".rc_resource_permission SET visitor_id = concat('org_', visitor_id) WHERE vtype = 1;
UPDATE "plss-record".rc_resource_permission SET visitor_id = concat('role_', visitor_id) WHERE vtype = 3;
-- 这里截止