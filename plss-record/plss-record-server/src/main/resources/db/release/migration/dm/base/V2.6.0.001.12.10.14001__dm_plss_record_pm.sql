-- zhiming.lei
insert into "plss-record"."rc_metadata" ("id", "name", "name_en", "definition", "value_type", "value_range", "short_name", "search_flag", "status", "orderby", "create_time", "create_by", "modified_time", "modified_by", "opt_edit", "opt_view", "value_type_range", "required", "value_rule", "pinyin", "mnemonic", "borrow_view", "details_view", "search_result_view", "search_field_way", "remark", "fixed_data", "alias_name", "alias_name_view", "opt_sort_standard_item") values (349260764008069, '提示词', '', null, 1, '^(.|\r|\n|\t){0,800}$', null, 1, 1, 0, '2024-12-06 10:52:50.694000', 313952851632453, '2024-12-06 10:52:50.694000', 313952851632453, 1, 1, null, 1, '{"formatType":"1","len":800,"selectItems":[],"unit":"","valueType":1}', 'tishici', 'tsc', 1, 1, 1, 1, 'AI仿写模板提示词', 2, '提示词', 2, 2);
insert into "plss-record"."rc_metadata_category_metadata" ("category_id", "md_id") values (2781328861957, 349260764008069);
insert into "plss-record"."rc_record_type_metadata" ("recordtype_id", "md_id", "value_type", "value_range", "search_flag", "mdcategory_id", "orderby", "required", "opt_edit", "opt_view", "value_type_range", "value_rule", "md_name", "pinyin", "mnemonic", "borrow_view", "details_view", "search_result_view", "search_field_way", "remark", "alias_name", "alias_name_view", "opt_sort_standard_item") values (187861709343429, 349260764008069, 1, '^(.|\r|\n|\t){0,800}$', 1, 2781328861957, 8, 2, 1, 1, null, '{"formatType":"1","len":800,"selectItems":[],"unit":"","valueType":1}', '提示词', 'tishici', 'tsc', 1, 1, 1, 1, null, null, 2, 2);

-- donghai.zhang
alter table "plss-record"."rc_record" add column("order_by" INT);
comment on column "plss-record"."rc_record"."order_by" is '模板文件排序号';