
-- 知识工程概念对齐字段
alter table "plss-record"."rc_knowledge_concept_attribute" add column("auto_align" TINYINT default (0));
comment on column "plss-record"."rc_knowledge_concept_attribute"."auto_align" is '自动对齐，0:未勾选,1：已勾选';

-- 入库方案表增加字段1
alter table "plss-record"."rc_plan" add column("fixed_data" SMALLINT);
comment on column "plss-record"."rc_plan"."fixed_data" is '是否内置固定数据1-内置2-非内置';

-- 入库重试请求表
CREATE TABLE "plss-record".rc_record_request (
                                                 record_id bigint NOT NULL, -- 主键
                                                 retry_count bigint NOT NULL, -- 重试次数
                                                 create_time timestamp NOT NULL, -- 创建时间
                                                 modified_time timestamp NOT NULL, -- 修改时间
                                                 CONSTRAINT rc_record_request_pkey PRIMARY KEY (record_id)
);

-- Column comments

COMMENT ON COLUMN "plss-record".rc_record_request.record_id IS '主键';
COMMENT ON COLUMN "plss-record".rc_record_request.retry_count IS '重试次数';
COMMENT ON COLUMN "plss-record".rc_record_request.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_record_request.modified_time IS '修改时间';

-- 入库重试日志表
CREATE TABLE "plss-record".rc_record_retry_log (
                                                   id bigint NOT NULL, -- 主键
                                                   record_id bigint NOT NULL,
                                                   node_type int NOT NULL, -- 节点类型
                                                   process_status int NOT NULL, -- 文档状态
                                                   process_sub_status int NOT NULL, -- 文档子状态
                                                   create_by bigint NULL, -- 创建人
                                                   create_time timestamp NOT NULL, -- 创建时间
                                                   CONSTRAINT rc_record_retry_log_pkey PRIMARY KEY (id)
);
CREATE INDEX rc_record_retry_log_record_id_idx ON "plss-record".rc_record_retry_log(record_id);

-- Column comments

COMMENT ON COLUMN "plss-record".rc_record_retry_log.id IS '主键';
COMMENT ON COLUMN "plss-record".rc_record_retry_log.node_type IS '节点类型';
COMMENT ON COLUMN "plss-record".rc_record_retry_log.process_status IS '文档状态';
COMMENT ON COLUMN "plss-record".rc_record_retry_log.process_sub_status IS '文档子状态';
COMMENT ON COLUMN "plss-record".rc_record_retry_log.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_record_retry_log.create_time IS '创建时间';

--模板生成表
CREATE TABLE "plss-record"."rc_template_generate"
(
    "id" BIGINT NOT NULL,
    "template_record_id" BIGINT NOT NULL,
    "generate_desc" VARCHAR(2048),
    "reference_record" VARCHAR(4096),
    "write_unit" VARCHAR(255),
    "name" VARCHAR(1024),
    "type" SMALLINT,
    "create_time" TIMESTAMP(6),
    "create_by" BIGINT,
    "update_time" TIMESTAMP(6),
    "update_by" BIGINT,
    NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "plss-record"."rc_template_generate" IS '模板生成表';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."create_by" IS '创建人';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."create_time" IS '创建时间';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."generate_desc" IS '生成要求的描述';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."id" IS '主键';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."name" IS '模板名称';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."reference_record" IS '参考文档 文档id列表 逗号分割';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."template_record_id" IS '引用模板id';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."type" IS '类型 1我的模板 2使用模板记录';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."update_by" IS '更新人';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."update_time" IS '更新时间';
COMMENT ON COLUMN "plss-record"."rc_template_generate"."write_unit" IS '拟文单位';


-- 增加字段1
ALTER TABLE "plss-record".rc_task_flow ADD exec_tag varchar(255) NULL;
COMMENT ON COLUMN "plss-record".rc_task_flow.exec_tag IS '执行标识';

-- 增加字段2
ALTER TABLE "plss-record".rc_record_type ADD file_type_json text NULL;
COMMENT ON COLUMN "plss-record".rc_record_type.file_type_json IS '文件类型json';

-- 文库新增字段组织机构id
ALTER TABLE "plss-record".rc_repository ADD COLUMN org_id BIGINT;
COMMENT ON COLUMN "plss-record"."rc_repository"."org_id" IS '所属单位id';

ALTER TABLE "plss-record"."rc_forbidden_searchinfo" MODIFY "forbidden_word" VARCHAR(255) NOT NULL;

update "plss-record".rc_knowledge_concept_attribute set auto_align = 1;

-- 初始化数据1
update "plss-record".rc_record_type set file_type_json = '{"list":[{"code":1,"name":"文本文件","typeList":[{"suffix":"doc"},{"suffix":"docx"},{"suffix":"wps"},{"suffix":"wpt"}]}]}'
where file_type_json is null and name in ('范文样例','模板');
-- 初始化数据2
update "plss-record".rc_record_type set file_type_json = '{"list":[{"code":1,"name":"文本文件","typeList":[{"suffix":"doc"},{"suffix":"docx"},{"suffix":"wps"},{"suffix":"wpt"},{"suffix":"rtf"},{"suffix":"dot"},{"suffix":"dotx"},{"suffix":"docm"},{"suffix":"dotm"},{"suffix":"uot"},{"suffix":"txt"}]},{"code":2,"name":"演示文件","typeList":[{"suffix":"dps"},{"suffix":"dpt"},{"suffix":"ppt"},{"suffix":"pptx"},{"suffix":"pot"},{"suffix":"pps"},{"suffix":"pptm"},{"suffix":"potm"},{"suffix":"ppsx"},{"suffix":"ppsm"},{"suffix":"uop"}]},{"code":3,"name":"表格文件","typeList":[{"suffix":"et"},{"suffix":"eet"},{"suffix":"xls"},{"suffix":"xlsx"},{"suffix":"xlt"},{"suffix":"xlsm"},{"suffix":"csv"},{"suffix":"xltx"},{"suffix":"dif"},{"suffix":"uos"},{"suffix":"dbf"}]},{"code":4,"name":"版式文件","typeList":[{"suffix":"pdf"},{"suffix":"ofd"},{"suffix":"ceb"},{"suffix":"sep"},{"suffix":"gd"},{"suffix":"gw"},{"suffix":"ps"},{"suffix":"s72"},{"suffix":"s92"},{"suffix":"s10"},{"suffix":"caj"},{"suffix":"cebx"},{"suffix":"xps"}]},{"code":5,"name":"图像文件","typeList":[{"suffix":"bmp"},{"suffix":"jpg"},{"suffix":"jpeg"},{"suffix":"png"},{"suffix":"gif"},{"suffix":"tif"},{"suffix":"tiff"}]},{"code":6,"name":"其他文件","typeList":[{"suffix":"html"},{"suffix":"htm"},{"suffix":"mht"},{"suffix":"mhtml"},{"suffix":"vsd"},{"suffix":"vsdx"},{"suffix":"eml"},{"suffix":"dwg"},{"suffix":"dwt"},{"suffix":"dwf"},{"suffix":"dwxf"}]}]}'
where file_type_json is null;

-- 入库方案表fixed_data初始化值
UPDATE "plss-record"."rc_plan"
SET fixed_data = 2 where fixed_data is null;

-- 新增模板和范文内置无审批方案
INSERT INTO "plss-record".rc_plan
(id, "name", record_type_id, remark, status, create_time, modified_time, del_flag, last_flag, tenant_id, common_flag, show_front, create_by, store_type, fixed_data)
VALUES(309633066446597, '模板-无审批', 187861709343429, '内置的模板无审批方案', 1, '2024-10-11 11:10:05.192', '2024-10-11 11:10:05.192', 1, 1, 0, 2, 2, 1, 1, 1);
INSERT INTO "plss-record".rc_plan
(id, "name", record_type_id, remark, status, create_time, modified_time, del_flag, last_flag, tenant_id, common_flag, show_front, create_by, store_type, fixed_data)
VALUES(309633249603333, '范文样例-无审批', 187862082546373, '内置的范文样例无审批方案', 1, '2024-10-11 11:10:27.541', '2024-10-11 11:10:27.541', 1, 1, 0, 2, 2, 1, 1, 1);

-- 方案关联节点表
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633066446597, 2490549604613, '{"checkBoxButtonList":[{"candidateCode":[1105,-1],"code":1105,"desc":"允许入库人员修改入库位置"}],"nodeType":1,"repoPositionButton":{"candidateCode":[1101,-1],"code":1101,"desc":"设置默认入库位置：","repoIdList":[]},"switchBoxButton":{"candidateCode":[1100,-1],"code":1100,"desc":"文件上传"}}', 0, 1, '2024-10-11 11:10:05.181', '2024-10-11 11:10:05.181', 1);
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633066446597, 2490549730565, '{"checkBoxButtonList":[{"candidateCode":[501,-1],"code":501,"desc":"转换成OFD格式"},{"candidateCode":[500,-1],"code":500,"desc":"扫描件双层OFD处理"},{"candidateCode":[600,-1],"code":600,"desc":"提取正文"}],"nodeType":4,"switchBoxButton":{"candidateCode":[500,-1],"code":500,"desc":"文件转换"}}', 1, 1, '2024-10-11 11:10:05.181', '2024-10-11 11:10:05.181', 4);
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633066446597, 2490550124037, '{"checkBoxButtonList":[{"candidateCode":[1501,-1],"code":1501,"desc":"系统自动生成封面"}],"nodeType":9,"switchBoxButton":{"candidateCode":[1500,-1],"code":1500,"desc":"入库"}}', 8, 1, '2024-10-11 11:10:05.181', '2024-10-11 11:10:05.181', 9);
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633249603333, 2490549604613, '{"checkBoxButtonList":[{"candidateCode":[1105,-1],"code":1105,"desc":"允许入库人员修改入库位置"}],"nodeType":1,"repoPositionButton":{"candidateCode":[1101,-1],"code":1101,"desc":"设置默认入库位置：","repoIdList":[]},"switchBoxButton":{"candidateCode":[1100,-1],"code":1100,"desc":"文件上传"}}', 0, 1, '2024-10-11 11:10:27.539', '2024-10-11 11:10:27.539', 1);
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633249603333, 2490549730565, '{"checkBoxButtonList":[{"candidateCode":[501,-1],"code":501,"desc":"转换成OFD格式"},{"candidateCode":[500,-1],"code":500,"desc":"扫描件双层OFD处理"},{"candidateCode":[600,-1],"code":600,"desc":"提取正文"}],"nodeType":4,"switchBoxButton":{"candidateCode":[500,-1],"code":500,"desc":"文件转换"}}', 1, 1, '2024-10-11 11:10:27.539', '2024-10-11 11:10:27.539', 4);
INSERT INTO "plss-record".rc_plan_node
(plan_id, node_id, defined_config_json, order_num, status, create_time, modified_time, node_type)
VALUES(309633249603333, 2490550124037, '{"checkBoxButtonList":[{"candidateCode":[1501,-1],"code":1501,"desc":"系统自动生成封面"}],"nodeType":9,"switchBoxButton":{"candidateCode":[1500,-1],"code":1500,"desc":"入库"}}', 8, 1, '2024-10-11 11:10:27.539', '2024-10-11 11:10:27.539', 9);



-- 历史数据英文名称
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Adjudication_Date'
WHERE "name" = '裁判日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Adoption_Date'
WHERE "name" = '通过日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Annotation'
WHERE "name" = '附注';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Approval_Date'
WHERE "name" = '批准日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Associated_Document'
WHERE "name" = '关联的政策文件';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Attachment'
WHERE "name" = '附件';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Belonging_Department'
WHERE "name" = '归属部门';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Case_Type'
WHERE "name" = '案件类型';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Conference_Title'
WHERE "name" = '会议名称';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Copy_To_Department'
WHERE "name" = '抄送机关';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Court_Level'
WHERE "name" = '法院层级';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Development_Planning_Document_Type'
WHERE "name" = '发展规划分类';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Document_Hierarchy'
WHERE "name" = '文件层级';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Document_Identifier'
WHERE "name" = '公文标识';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Document_Issuer'
WHERE "name" = '发文机关';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Document_Title'
WHERE "name" = '标题';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Document_Type'
WHERE "name" = '文种';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Emergency_Degree'
WHERE "name" = '紧急程度';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Enforcement_Date'
WHERE "name" = '施行日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Financial_Document_Type'
WHERE "name" = '财政信息分类';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Formulated_Department'
WHERE "name" = '制定机关';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Identification_Of_Document_Issuer'
WHERE "name" = '发文机关标志';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Issued_Date_Of_Document'
WHERE "name" = '成文日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Issued_Number_Of_Document'
WHERE "name" = '发文字号';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Judicial_Document_Type'
WHERE "name" = '文书类型';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Legal_Hierarchy'
WHERE "name" = '效力位阶';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Main_Points_Of_Case'
WHERE "name" = '案由';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Main_Receiver_Department'
WHERE "name" = '主送机关';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Number_Of_Case'
WHERE "name" = '案号';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Party_Discipline_Document_Type'
WHERE "name" = '党纪法规分类';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Planning_Period'
WHERE "name" = '规划时期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Printing_And Sending_Department'
WHERE "name" = '印发机关';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Printing_Date'
WHERE "name" = '印发日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Publication_Date'
WHERE "name" = '公布日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Release_Date'
WHERE "name" = '发文日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Release_Date'
WHERE "name" = '发布日期';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Release_Level'
WHERE "name" = '发布层次';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Report_Document_Type'
WHERE "name" = '工作报告分类';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Report_Time'
WHERE "name" = '报告时间';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Reporter_And_Position'
WHERE "name" = '报告人&职务';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Security_Classification'
WHERE "name" = '密级';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Security_Duration'
WHERE "name" = '保密期限';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Serial_Number_Of_Copies'
WHERE "name" = '份号';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Signer'
WHERE "name" = '签发人';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Source_Of_Current_Events'
WHERE "name" = '动态来源';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Source_Of_Interpretation'
WHERE "name" = '解读来源';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Source_Of_Statisticas'
WHERE "name" = '数据来源';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Speech_Document_Field'
WHERE "name" = '领域';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Speech_Document_Type'
WHERE "name" = '类型';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Statisticas_Document_Type'
WHERE "name" = '统计数据分类';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Subject_Of_Document'
WHERE "name" = '主题';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Territory_And_Court'
WHERE "name" = '地域及法院';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Timeliness_Of_Document'
WHERE "name" = '时效性';
UPDATE "plss-record"."rc_metadata"
SET "name_en" = 'Year_Of_Document'
WHERE "name" = '年份';