-- 标品内蒙需求

-- 方案审批人表
CREATE TABLE "plss-record".rc_plan_audit_user
(
    id          int8      NOT NULL, -- 主键
    plan_id     int8      NOT NULL, -- 方案id
    tenant_id   int8      NOT NULL, -- 租户id
    user_id     int8      NOT NULL, -- 用户id
    create_time timestamp NOT NULL, -- 创建时间
    CONSTRAINT rc_plan_audit_user_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX rc_plan_audit_user_plan_id_idx ON "plss-record".rc_plan_audit_user USING btree (plan_id, tenant_id, user_id);
CREATE INDEX rc_plan_audit_user_tenant_id_idx ON "plss-record".rc_plan_audit_user USING btree (user_id,tenant_id);

-- <PERSON><PERSON><PERSON> comments

COMMENT
ON COLUMN "plss-record".rc_plan_audit_user.id IS '主键';
COMMENT
ON COLUMN "plss-record".rc_plan_audit_user.plan_id IS '方案id';
COMMENT
ON COLUMN "plss-record".rc_plan_audit_user.tenant_id IS '租户id';
COMMENT
ON COLUMN "plss-record".rc_plan_audit_user.user_id IS '用户id';
COMMENT
ON COLUMN "plss-record".rc_plan_audit_user.create_time IS '创建时间';

-- 删除多余索引
DROP INDEX "plss-record".RC_DOC_PROCESS_TASK_ID_IDX;
DROP INDEX "plss-record"."rc_doc_process_audit_status_idx";
DROP INDEX "plss-record"."rc_doc_process_deleted_idx";
DROP INDEX "plss-record"."rc_doc_process_origin_idx";
DROP INDEX "plss-record"."rc_doc_process_batch_id_idx";
DROP INDEX "plss-record"."rc_doc_process_process_sub_status_idx";
DROP INDEX "plss-record"."rc_doc_process_real_origin_idx";
DROP INDEX "plss-record"."rc_doc_process_record_type_id_idx";

-- 添加索引

CREATE INDEX RC_DOC_PROCESS_CREATE_BY_IDX ON "plss-record".rc_doc_process (create_by);
CREATE INDEX "idx_deleted_ctype_task" ON "plss-record"."rc_doc_process" ("deleted", "ctype", "task_id");
CREATE INDEX "idx_deleted_sway_createby_process_rtype_rid" ON "plss-record"."rc_doc_process" ("deleted",
                                                                                              "store_way",
                                                                                              "create_by",
                                                                                              "process_sub_status",
                                                                                              "record_type_id",
                                                                                              "record_id");
CREATE INDEX "idx_t_d_p_p_rti_cb_rid" ON "plss-record"."rc_doc_process" ("tenant_id", "deleted",
                                                                         "process_status",
                                                                         "process_sub_status",
                                                                         "record_type_id",
                                                                         "create_by", "record_id");
CREATE INDEX "idx_tenant_deleted_create_process_batch" ON "plss-record"."rc_doc_process" ("tenant_id",
                                                                                          "deleted",
                                                                                          "create_by",
                                                                                          "process_status",
                                                                                          "batch_id");
