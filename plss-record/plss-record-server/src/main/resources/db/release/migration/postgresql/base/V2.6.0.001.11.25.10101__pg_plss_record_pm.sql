-- 元数据新增别名字段
ALTER TABLE "plss-record"."rc_metadata"
    ADD COLUMN "alias_name" varchar(128),
  ADD COLUMN "alias_name_view" int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record"."rc_metadata"."alias_name" IS '元数据项名称的别名';

COMMENT ON COLUMN "plss-record"."rc_metadata"."alias_name_view" IS '元数据项名称的别名,是否使用别名展示,1-是，2-否';

ALTER TABLE "plss-record"."rc_record_type_metadata"
    ADD COLUMN "alias_name" varchar(128),
  ADD COLUMN "alias_name_view" int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."alias_name" IS '元数据项名称的别名';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."alias_name_view" IS '元数据项名称的别名,是否使用别名展示,1-是，2-否';

alter table "plss-record"."rc_record_share" ADD invalid_status int2 DEFAULT 1;

COMMENT
ON COLUMN "plss-record"."rc_record_share".invalid_status IS '效用状态(1有效2无效)';
