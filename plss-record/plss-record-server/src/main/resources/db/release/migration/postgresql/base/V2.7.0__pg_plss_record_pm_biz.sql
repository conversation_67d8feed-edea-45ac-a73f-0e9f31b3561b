CREATE TABLE "plss-record".rc_data_lake (
                                            id INT8 NOT NULL,
                                            repo_id INT8 NOT NULL,
                                            data_name VARCHAR(64) NOT NULL,
                                            file_date TIMESTAMP(6) NOT NULL,
                                            file_size INT8 NOT NULL,
                                            file_md5 VARCHAR(64) NOT NULL,
                                            file_origin_name VARCHAR(64) NOT NULL,
                                            status INT2 NOT NULL,
                                            task_key VARCHAR(64),
                                            task_id INT8,
                                            CONSTRAINT rc_data_lake_pk PRIMARY KEY (id)
);
COMMENT ON COLUMN "plss-record".rc_data_lake.repo_id IS '文库id';
COMMENT ON COLUMN "plss-record".rc_data_lake.data_name IS '数据包名称';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_date IS '数据包日期';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_size IS '文件大小';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_md5 IS '文件md5';
COMMENT ON COLUMN "plss-record".rc_data_lake.file_origin_name IS '文件原始名';
COMMENT ON COLUMN "plss-record".rc_data_lake.status IS '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功';
COMMENT ON COLUMN "plss-record".rc_data_lake.task_key IS '任务标识，数据分发服务返回';
COMMENT ON COLUMN "plss-record".rc_data_lake.task_id IS '任务id';

UPDATE "plss-record".rc_dataload_task SET task_type =999 WHERE task_type IS NULL;

ALTER TABLE "plss-record".rc_record_process_detail
    ADD COLUMN ctype INT2 NOT NULL DEFAULT 1;

COMMENT ON COLUMN "plss-record".rc_record_process_detail.ctype IS '文件类型1-主件2-附件';

-- 本地消息重试表
CREATE TABLE "plss-record".rc_retry_msg (
                                            doc_id INT8 NOT NULL,
                                            record_id INT8 NOT NULL,
                                            biz_type INT2 NOT NULL,
                                            status INT2,
                                            create_by INT8,
                                            create_time TIMESTAMP(6) NOT NULL,
                                            modified_time TIMESTAMP(6) NOT NULL,
                                              CONSTRAINT "rc_record_retry_msg_pkey" PRIMARY KEY (doc_id)
)
;

CREATE INDEX "idx_bizType_status" ON "plss-record".rc_retry_msg USING btree (
                                                                             biz_type ASC NULLS LAST,
                                                                             status ASC NULLS LAST
    );

CREATE INDEX "idx_recordId" ON "plss-record".rc_retry_msg USING btree (
                                                                       record_id ASC NULLS LAST
    );

COMMENT ON COLUMN "plss-record".rc_retry_msg.doc_id IS '文档id';
COMMENT ON COLUMN "plss-record".rc_retry_msg.record_id IS '文件记录id';
COMMENT ON COLUMN "plss-record".rc_retry_msg.biz_type IS '业务类型:1-附件提取';
COMMENT ON COLUMN "plss-record".rc_retry_msg.status IS '1-成功2-失败';
COMMENT ON COLUMN "plss-record".rc_retry_msg.create_by IS '创建人';
COMMENT ON COLUMN "plss-record".rc_retry_msg.create_time IS '创建时间';
COMMENT ON COLUMN "plss-record".rc_retry_msg.modified_time IS '修改时间';
COMMENT ON TABLE "plss-record".rc_retry_msg IS '重试本地消息表';


--添加文档与库的关联表
CREATE TABLE "plss-record".rc_record_repo (
                                              id INT8 NOT NULL,
                                              record_id INT8 NOT NULL,
                                              repo_id INT8 NOT NULL,
                                              create_time TIMESTAMP(6) NOT NULL,
                                                CONSTRAINT "rc_record_borrow_repo_pkey" PRIMARY KEY (id)
)
;

CREATE INDEX "idx_record_repo_record_id" ON "plss-record".rc_record_repo USING btree (record_id);

COMMENT ON COLUMN "plss-record".rc_record_repo.record_id IS '文档id';

COMMENT ON COLUMN "plss-record".rc_record_repo.repo_id IS '文档所在库id';

COMMENT ON COLUMN "plss-record".rc_record_repo.create_time IS '创建时间';

COMMENT ON TABLE "plss-record".rc_record_repo IS '文档和文库的关系';

ALTER TABLE "plss-record".rc_export_filter ADD file_type INT DEFAULT 0;
COMMENT ON COLUMN "plss-record".rc_export_filter.file_type IS '文件类型';

ALTER TABLE "plss-record".rc_document_0
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_0.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_1
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_1.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_2
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_2.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_3
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_3.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_4
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_4.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_5
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_5.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_6
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_6.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_7
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_7.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_8
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_8.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_9
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_9.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_10
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_10.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_11
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_11.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_12
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_12.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_13
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_13.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_14
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_14.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_15
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_15.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_16
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_16.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_17
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_17.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_18
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_18.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_19
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_19.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_20
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_20.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_21
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_21.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_22
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_22.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_23
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_23.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_24
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_24.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_25
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_25.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_26
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_26.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_27
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_27.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_28
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_28.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_29
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_29.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_30
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_30.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_31
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_31.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_32
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_32.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_33
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_33.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_34
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_34.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_35
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_35.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_36
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_36.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_37
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_37.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_38
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_38.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

ALTER TABLE "plss-record".rc_document_39
    ADD COLUMN doc_att_status int2 NOT NULL DEFAULT 2;

COMMENT ON COLUMN "plss-record".rc_document_39.doc_att_status IS 'ctype=2时用此字段,文件附件状态1-正常 2-待处理,3-处理中,4-失败';

-- 修改字段长度
ALTER TABLE "plss-record"."rc_record_borrow"
ALTER COLUMN org_name TYPE varchar(1024);