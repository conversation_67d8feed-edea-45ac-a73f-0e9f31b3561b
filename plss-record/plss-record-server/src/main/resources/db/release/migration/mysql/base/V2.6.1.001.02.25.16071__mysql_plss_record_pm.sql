CREATE TABLE `plss_record`.`rc_third_file_exchange_file_id` (
  `id` bigint NOT NULL COMMENT '主键id',
  `manufacturer_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '厂商来源',
  `manufacturer_file_id` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '厂商来源的文件id',
  `file_id` bigint COMMENT '文件id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_rc_third_file_exchange_file_id` (`manufacturer_code`,`manufacturer_file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方厂商置换文件id的记录表';
