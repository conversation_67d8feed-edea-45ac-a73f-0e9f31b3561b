CREATE TABLE "plss-record".rc_third_file_exchange_file_id (
    id bigint NOT NULL,
    manufacturer_code character varying(64) NOT NULL,
    manufacturer_file_id character varying(128) NOT NULL, 
	file_id bigint,
	create_time timestamp without time zone NOT NULL
);


COMMENT ON TABLE "plss-record".rc_third_file_exchange_file_id IS '第三方厂商置换文件id的记录表';
COMMENT ON COLUMN "plss-record".rc_third_file_exchange_file_id.id IS '主键id';
COMMENT ON COLUMN "plss-record".rc_third_file_exchange_file_id.manufacturer_code IS '厂商来源';
COMMENT ON COLUMN "plss-record".rc_third_file_exchange_file_id.manufacturer_file_id IS '厂商来源的文件id';
COMMENT ON COLUMN "plss-record".rc_third_file_exchange_file_id.file_id IS '文件id';
COMMENT ON COLUMN "plss-record".rc_third_file_exchange_file_id.create_time IS '创建时间';

ALTER TABLE ONLY "plss-record".rc_third_file_exchange_file_id
    ADD CONSTRAINT rc_third_file_exchange_file_id_pkey PRIMARY KEY (id);
	
CREATE UNIQUE INDEX "idx_rc_third_file_exchange_file_id" ON "plss-record".rc_third_file_exchange_file_id USING btree (manufacturer_code, manufacturer_file_id);
