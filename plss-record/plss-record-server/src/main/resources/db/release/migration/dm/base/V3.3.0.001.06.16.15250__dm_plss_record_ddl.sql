CREATE TABLE "plss-record".rc_data_lake_custom (
                                                   id          BIGINT       NOT NULL,
                                                   task_id     BIGINT       NULL COMMENT '导入任务id',
                                                   custom_key  VARCHAR(64)  NOT NULL COMMENT '定制key',
                                                   data_name   VARCHAR(64)  NOT NULL COMMENT '数据包名称',
                                                   file_date   TIMESTAMP(6) NOT NULL COMMENT '数据包日期',
                                                   file_size   BIGINT       NOT NULL COMMENT '文件大小',
                                                   file_md5    VARCHAR(64)  NOT NULL COMMENT '文件md5',
                                                   file_origin_name VARCHAR(64) NOT NULL COMMENT '文件原始名',
                                                   status      SMALLINT     NOT NULL COMMENT '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功，5:数据准备中',
                                                   task_key    VARCHAR(255) NULL COMMENT '任务标识，数据分发服务返回',
                                                   data_dir    VARCHAR(255) NULL COMMENT '目录',
                                                   CONSTRAINT rc_data_lake_custom_pk PRIMARY KEY (id)
);

-- 添加列注释
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.task_id IS '导入任务id';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.custom_key IS '定制key';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.data_name IS '数据包名称';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_date IS '数据包日期';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_size IS '文件大小';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_md5 IS '文件md5';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.file_origin_name IS '文件原始名';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.status IS '数据状态.1:待导入,2:导入中,3:导入失败,4:导入成功，5:数据准备中';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.task_key IS '任务标识，数据分发服务返回';
COMMENT ON COLUMN "plss-record".rc_data_lake_custom.data_dir IS '目录';

-- 修改同一模式下的另一张表
ALTER TABLE "plss-record".rc_data_lake ADD data_dir VARCHAR(255) NULL;
COMMENT ON COLUMN "plss-record".rc_data_lake.data_dir IS '目录';