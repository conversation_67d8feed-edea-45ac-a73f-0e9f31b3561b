
spring:
  shardingsphere:
    rules:
      sharding:
        sharding-algorithms:
          LogRecordViewSharding:
            type: plss-log-record-view-sharding
          fileComplexKeySharding:
            type: plss-file-complex-key-sharding
          fileMD5hexComplexKeySharding:
            type: plss-file-md5hex-complex-key-sharding
          documentComplexKeySharding:
            type: plss-document-complex-key-sharding
          documentMd5ComplexKeySharding:
            type: plss-document-md5-complex-key-sharding
          mdValueComplexKeySharding:
            type: plss-md-value-complex-key-sharding
          taskBatchSharding:
            type: rc_task_batch_sharding
          taskSharding:
            type: rc_task_sharding
          recordSharding:
            type: rc_record_sharding
          docProcessSharding:
            type: rc_doc_process_sharding
          taskFlowSharding:
            type: rc_task_flow_sharding
          taskDocSharding:
            type: rc_task_doc_sharding
          folderRecordSharding:
            type: rc_folder_record_sharding
        tables:
          rc_log_recordview:
            actual-data-nodes: logic-record.rc_log_recordview_$->{2024..2027}_q$->{1..4}
            table-strategy:
              complex:
                sharding-columns: create_time
                shardingAlgorithmName: LogRecordViewSharding
          rc_file:
            actual-data-nodes: logic-record.rc_file_$->{0..99}
            table-strategy:
              complex:
                sharding-columns: id
                shardingAlgorithmName: fileComplexKeySharding
          rc_file_md5_rel:
            actual-data-nodes: logic-record.rc_file_md5_rel_$->{0..49}
            table-strategy:
              complex:
                sharding-columns: md5_hex
                shardingAlgorithmName: fileMD5hexComplexKeySharding
          rc_document:
            actual-data-nodes: logic-record.rc_document_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: record_id
                shardingAlgorithmName: documentComplexKeySharding
          rc_document_md5_rel:
            actual-data-nodes: logic-record.rc_document_md5_rel_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: md5_hex
                shardingAlgorithmName: documentMd5ComplexKeySharding
          rc_record_metadata_value:
            actual-data-nodes: logic-record.rc_record_metadata_value_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: record_id
                sharding-algorithm-name: mdValueComplexKeySharding
          rc_record:
            actual-data-nodes: logic-record.rc_record_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: id
                sharding-algorithm-name: recordSharding
          rc_task_batch:
            actual-data-nodes: logic-record.rc_task_batch_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: id
                sharding-algorithm-name: taskBatchSharding
          rc_task:
            actual-data-nodes: logic-record.rc_task_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: batch_id
                sharding-algorithm-name: taskSharding
          rc_doc_process:
            actual-data-nodes: logic-record.rc_doc_process_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: record_id
                sharding-algorithm-name: docProcessSharding
          rc_task_flow:
            actual-data-nodes: logic-record.rc_task_flow_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: record_id
                sharding-algorithm-name: taskFlowSharding
          rc_task_doc:
            actual-data-nodes: logic-record.rc_task_doc_$->{0..39}
            table-strategy:
              complex:
                sharding-columns: record_id
                sharding-algorithm-name: taskDocSharding
          rc_folder_record:
            actual-data-nodes: logic-record.rc_folder_record_$->{0..6}_$->{0..6}
            table-strategy:
              complex:
                sharding-columns: folder_id,record_id
                sharding-algorithm-name: folderRecordSharding




