<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.suwell.plss</groupId>
        <artifactId>plss-record</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>plss-record-server</artifactId>

    <description>
        plss-record-server文件基础服务
    </description>

    <dependencies>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-undertow</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.746</version>
        </dependency>

        <!-- 金山云 Kingsoft s3-->
        <dependency>
            <groupId>com.ksyun</groupId>
            <artifactId>ks3-kss-java-sdk</artifactId>
            <version>1.0.6</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- postgresql 数据库 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yashandb</groupId>
            <artifactId>yashandb-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.purgeteam</groupId>
            <artifactId>dynamic-config-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <!--            <version>9.0.0</version>-->
        </dependency>
        <dependency>
            <groupId>com.github.mengweijin</groupId>
            <artifactId>db-migration-dm</artifactId>
        </dependency>

        <!-- plss Common DataSource -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-datasource</artifactId>
        </dependency>

        <!-- plss Common DataScope -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-datapermission</artifactId>
        </dependency>

        <!-- plss Common mq -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- plss Common Log -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-record-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-system-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-record-standard</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-open-thirdparty</artifactId>
        </dependency>

        <!-- plss 测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-core</artifactId>
            <version>2.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 依赖服务api -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-document-process-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-search-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-plugin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.x-file-storage</groupId>
            <artifactId>x-file-storage-spring</artifactId>
        </dependency>
        <!-- Minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-nacos</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>akali</artifactId>
        </dependency>

        <dependency>
            <groupId>org.owasp.esapi</groupId>
            <artifactId>esapi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ahocorasick</groupId>
            <artifactId>ahocorasick</artifactId>
            <version>0.6.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.13.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <version>1.7.2</version>
            <scope>test</scope>
        </dependency>

        <!--对比重复依赖start-->
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-data-delivery-sdk</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-security</artifactId>
        </dependency>
        <!--对比重复依赖end-->

        <!-- 日志收集器 -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>