package com.suwell.plss.data.delivery.datapackage;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.suwell.plss.data.delivery.dto.req.dataPackage.DataPackageSizeReq;
import com.suwell.plss.data.delivery.dto.resp.dataPackage.DataPackageSizeResp;
import com.suwell.plss.data.delivery.entity.DataPackage;
import com.suwell.plss.data.delivery.entity.DpDownloadLog;
import com.suwell.plss.data.delivery.mapper.DataPackageMapper;
import com.suwell.plss.data.delivery.sdk.constant.DpDownloadLogConstant.DownStateEnum;
import com.suwell.plss.data.delivery.sdk.constant.DpDownloadLogConstant.DownTypeEnum;
import com.suwell.plss.data.delivery.service.DataPackageService;
import com.suwell.plss.data.delivery.service.DpDownloadLogService;
import com.suwell.plss.data.delivery.util.DataPackageUtil;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 在线下载流处理器
 *
 * <AUTHOR>
 * @date 2025/1/22
 */
@Slf4j
@Service
public class OnlineDataPackageDownHandler extends AbstractDataPackageDownHandler {

    @Resource
    private FileStorageService fileStorageService;

    @Resource
    private DataPackageService dataPackageService;

    @Resource
    private DpDownloadLogService dpDownloadLogService;

    @Override
    public DownTypeEnum getDownType() {
        return DownTypeEnum.DOWN_TYPE_ONLINE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishTask(DpDownloadLog downloadLog) {
        log.info("数据分发下载任务-下载在线包开始。downloadLog={}", JSON.toJSONString(downloadLog));
        Date dataStartDate = downloadLog.getDataStartDate();
        List<Long> repoIdList = JSON.parseArray(downloadLog.getRepoIdList(), Long.class);

        int n = dpDownloadLogService.updateDownState(downloadLog.getId(), DownStateEnum.PENDING,
                DownStateEnum.PROCESSING);
        if (n < 1) {
            return;
        }
        DataPackage dataPackage = dataPackageService.getOne(
                Wrappers.lambdaQuery(DataPackage.class).eq(DataPackage::getFileDate, dataStartDate)
                        .eq(DataPackage::getRepoId, repoIdList.get(0)));
        if (Objects.isNull(dataPackage) || !fileStorageService.exists(DataPackageUtil.getFileInfo(dataPackage))) {
            dpDownloadLogService.updateDownState(downloadLog.getId(), DownStateEnum.PROCESSING, DownStateEnum.FAIL);
            log.error("数据分发下载任务-下载在线包失败,数据包不存在。downloadLog={}", JSON.toJSONString(downloadLog));
            return;
        }

        try {
            DataPackageSizeReq sizeReq = new DataPackageSizeReq();
            sizeReq.setStartDate(dataStartDate);
            sizeReq.setEndDate(dataStartDate);
            sizeReq.setRepoIdList(repoIdList);
            DataPackageSizeResp packageSizeResp = dataPackageService.getPackageSize(sizeReq);
            dpDownloadLogService.updateDownStateAndPackageInfo(downloadLog.getId(), DownStateEnum.PROCESSING,
                    DownStateEnum.OK,
                    packageSizeResp.getPackageName(), packageSizeResp.getPackageSize(), dataPackage.getPlatform(),
                    dataPackage.getFilePath(), dataPackage.getFileName());
            log.info("数据分发下载任务-下载在线包成功。downloadLog={}", JSON.toJSONString(downloadLog));
        } catch (Exception e) {
            dpDownloadLogService.updateDownState(downloadLog.getId(), DownStateEnum.PROCESSING, DownStateEnum.FAIL);
            log.error("数据分发下载任务-下载在线包失败。downloadLog={},em={}", JSON.toJSONString(downloadLog),
                    e.getMessage(), e);
        }
    }
}
