package com.suwell.plss.data.delivery.dto.req.user;

import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/25 10:57
 */
@Data
public class UserEditReq {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 订阅数据
     */
    private List<Long> subscribeData;

    /**
     * 1：启用，2：禁用
     */
    private Integer status;

    /**
     * 重置密码
     */
    private Boolean resetPassword;
}
