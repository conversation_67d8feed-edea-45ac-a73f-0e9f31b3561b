package com.suwell.plss.test.service.impl;

import static com.suwell.plss.test.enums.EsIndexEnum.INDEX_NLP_RECORD;
import static com.suwell.plss.test.enums.EsIndexEnum.INDEX_NLP_RECORD_TEST;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.Refresh;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.google.common.collect.Lists;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.framework.es.service.ElasticsearchService;
import com.suwell.plss.test.service.AeAiAnalysisMsgService;
import com.suwell.plss.test.service.PlanService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DemoServiceImpl {

    @Resource
    private ElasticsearchService elasticsearchService;
    @Resource
    private ElasticsearchClient elasticsearchClient;
    @Resource
    private WFGIdGenerator wfgIdGenerator;
    @Resource
    private PlanService planService;

    @Resource
    private AeAiAnalysisMsgService aeAiAnalysisMsgService;

    @PostConstruct
    public void initialize() {
        // 测试数据 测试检索
        //初始化 es 索引配置 创建索引(不存在就创建，已存在索引就不创建)
//        elasticsearchService.createIndex(EsIndexEnum.INDEX_TEST_NLP_RECORD.getIndexName(),
//                EsIndexEnum.INDEX_TEST_NLP_RECORD.getIndexMappingConfiguration(), "createTestNlpRecordIndex", false);

//        List<NlpRecord> nlpRecords = queryEsRecordIdSplitIdTitleNotNullFilter(2050221001477L);
//        System.out.println(JSON.toJSONString(nlpRecords));
//        bulkCreate(INDEX_NLP_RECORD_TEST.getIndexName(), nlpRecords);
//        List<NlpRecord> nlpRecords2 = queryEsRecordIdSplitIdTitleNotNullFilter2(2050221001477L);
//        System.err.println(JSON.toJSONString(nlpRecords2));
////        NlpRecord nlpRecord = nlpRecords2.get(0);
////        nlpRecord.setTitle("测试更新");
//        NlpRecord nlpRecord = new NlpRecord();
//        nlpRecord.setUniqId(2050245058821L);
//        nlpRecord.setTitle("东海更新");
//        elasticsearchService.updateById(INDEX_NLP_RECORD_TEST.getIndexName(), nlpRecord.getUniqId(),
//                nlpRecord, NlpRecord.class);
//        List<NlpRecord> nlpRecords3 = queryEsRecordIdSplitIdTitleNotNullFilter2(2050221001477L);
//        System.err.println(JSON.toJSONString(nlpRecords3));
//        List<Plan> list = planService.list();
//        System.err.println(JSON.toJSONString(list));

//        AeAiAnalysisMsg byId = aeAiAnalysisMsgService.getById(1699967675065262081L);
//        System.err.println(JSON.toJSONString(byId));
//        List<NlpRecord> list = queryRecordOrgListAll(1, 1000);
////        metadataValueService.saveBatch();
//        List<MetadataValue> metadataValueList = Lists.newArrayList();
//        for (NlpRecord r : list) {
//            MetadataValue metadataValue = new MetadataValue();
//            metadataValue.setMdValue(r.getTitle());
//            metadataValue.setId(wfgIdGenerator.next());
//            metadataValue.setCreateTime(new Date());
//            metadataValue.setModifiedTime(new Date());
//            metadataValue.setRecordId(r.getRecordId());
//            metadataValue.setMdId(231903009797L);
//            metadataValueList.add(metadataValue);
//        }
//        metadataValueService.saveBatchMetadataValue(metadataValueList);
    }

    /**
     * 根据文件id 和全文及标题不为null 进行查询一个文件的数据
     *
     * @param recordId
     * @return
     */
    private List<NlpRecord> queryEsRecordId(Long recordId) {
        Query.Builder queryBuilder = new Query.Builder();
        queryBuilder.bool(b -> {
            b.must(m -> m.term(t -> t.field("recordId").value(recordId)));
            return b;
        });
        return elasticsearchService.search(INDEX_NLP_RECORD.getIndexName(),
                queryBuilder, 1, 1000, NlpRecord.class);
    }

    /**
     * 根据文件id 和全文及标题不为null 进行查询一个文件的数据
     *
     * @param recordId
     * @return
     */
    private List<NlpRecord> queryEsRecordIdSplitIdTitleNotNullFilter2(Long recordId) {
        Query.Builder queryBuilder = new Query.Builder();
        queryBuilder.bool(b -> {
            b.must(m -> m.term(t -> t.field("recordId").value(recordId)));
            b.must(m -> m.term(t -> t.field("splitId").value(-1)));
            return b;
        });
        return elasticsearchService.search(INDEX_NLP_RECORD_TEST.getIndexName(),
                queryBuilder, 1, 1000, NlpRecord.class);
    }

    /**
     * 批量增加文档
     *
     * @param idxName 索引名
     * @param documents 要增加的对象集合
     */
    private void bulkCreate(String idxName, List<NlpRecord> documents) {
        try {
            log.info("es req,idxName:{},entity:{}", idxName, JSONUtil.toJsonStr(documents));
            BulkRequest.Builder br = new BulkRequest.Builder();
            // 将每一个documents对象都放入builder中
            documents.forEach(doc -> br
                    .operations(op -> op
                            .index(idx -> idx
                                    .index(idxName)
                                    .id(doc.getUid())
                                    .document(doc))));
            BulkResponse bulkResponse = elasticsearchClient.bulk(br.refresh(Refresh.True).build());
            log.info("bulkCreate rsp:{}", bulkResponse.toString());
        } catch (Exception e) {
            log.error("es req: idxName:{},entity:{}", idxName, JSONUtil.toJsonStr(documents), e);
            throw new RuntimeException(e);
        }
    }

    public List<NlpRecord> queryRecordOrgListAll(Integer page, Integer pageSize) {
        //        Long userId = SecurityUtils.getLoginUser().getUserid();
        try {
//            R<List<Long>> listR = orgRpcService.queryOrgByUserId(userId);
            if (true) {
//            if (listR.isSuccess()) {
                List<Long> orgIds = null;
//                List<Long> orgIds = listR.getData();
                SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
                searchBuilder.index(INDEX_NLP_RECORD.getIndexName())
                        .from((page - 1) * pageSize)
                        .size(pageSize)
                        .query(q -> q.bool(
                                b -> {
                                    if (CollUtil.isNotEmpty(orgIds)) {
                                        b.must(m -> {
                                            List<FieldValue> orgIdsValues = orgIds.stream().map(FieldValue::of)
                                                    .collect(Collectors.toList());
                                            m.terms(tf -> tf.field("orgIds").terms(tv -> tv.value(orgIdsValues)));
                                            return m;
                                        });
                                    }
                                    return b;
                                }))
                        //排序
                        .sort(s -> s.field(f -> {
                            f.field("printedTime").order(SortOrder.Desc);
//                        f.field("printedTime").order(SortOrder.Desc);
                            return f;
                        }));
                SearchRequest searchRequest = searchBuilder.build();

                elasticsearchService.printEsBySearchRequest(searchRequest);
                SearchResponse<NlpRecord> searchResponse = elasticsearchClient.search(searchRequest, NlpRecord.class);
                List<Hit<NlpRecord>> hitList = searchResponse.hits().hits();
                List<NlpRecord> list = new ArrayList<>();
                for (Hit<NlpRecord> mapHit : hitList) {
                    list.add(mapHit.source());
                }
                return list;
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("es fail ", e);
            throw new RuntimeException(e);
        }
    }

}
