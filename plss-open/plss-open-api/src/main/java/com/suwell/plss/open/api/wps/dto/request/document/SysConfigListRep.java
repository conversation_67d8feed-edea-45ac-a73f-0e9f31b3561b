package com.suwell.plss.open.api.wps.dto.request.document;

import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class SysConfigListRep implements Serializable {

    private static final long serialVersionUID = -8191555258500914287L;

    /**
     * key集合
     */
    @Size(min = 1, message = "configKeys不能为空")
    private List<String> configKeys;

}
