package com.suwell.plss.open.api.record.http;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.record.standard.dto.request.RecordLogReq;
import com.suwell.plss.record.standard.dto.response.RecordLogResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024-05-11 16:57
 **/
@RequestMapping("/v1/log")
public interface RecordViewEntrance {

    /**
     * 我的收藏查询列表
     * @param req 参数
     * @return 列表
     */
    @PostMapping("/record/view")
    R<PageUtils<RecordLogResp>> queryRecordViewLog(@Valid @RequestBody RecordLogReq req);
}
