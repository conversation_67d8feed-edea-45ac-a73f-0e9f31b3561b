server:
  port: 8065

spring:
  application:
    name: plss-open
  profiles:
    active: prod

  cloud:
    nacos:
      discovery:
        heart-beat-timeout: 60000
      config:
        file-extension: yml
        name: ${spring.application.name}.${spring.cloud.nacos.config.file-extension}
        group: SERVICE
        refresh-enabled: true
        config-long-poll-timeout: 60000 # 默认30000;长轮询时间 1分钟
        config-retry-time: 5000 # 默认2000;重试5秒
        max-retry: 6000 # 默认3;最大重试次数
        shared-configs:
          - dataId: common-server.${spring.cloud.nacos.config.file-extension}
            group: COMMON
            refresh: true
          - dataId: common-redis.${spring.cloud.nacos.config.file-extension}
            group: COMMON
            refresh: true
          - dataId: plss-config.${spring.cloud.nacos.config.file-extension}
            group: COMMON
            refresh: true
          - dataId: common-mq.${spring.cloud.nacos.config.file-extension}
            group: COMMON
            refresh: true