ALTER TABLE "plss-open".op_abutment_doc_package_record
    ADD batch_no VARCHAR2(128);
comment
ON COLUMN "plss-open"."op_abutment_doc_package_record"."batch_no" IS '批次号';

ALTER TABLE "plss-open".op_abutment_doc_package_record
    ADD approach SMALLINT;
comment
ON COLUMN "plss-open"."op_abutment_doc_package_record"."approach" IS '对接方式：1-在线文件接口，2-离线文件接口，3-离线文件磁盘共享';

-- 创建应用权益信息表op_app_rights
CREATE TABLE "plss-open"."op_app_rights"
(
    "id"           BIGINT       NOT NULL,
    "app_id"       VARCHAR(64)  NOT NULL,
    "rights_value" VARCHAR(128) NOT NULL,
    "remark"       VARCHAR(64),
    CONSTRAINT op_app_rights_pk PRIMARY KEY (id)
);

COMMENT
ON TABLE "plss-open"."op_app_rights" IS '应用权益信息表';
COMMENT
ON COLUMN "plss-open"."op_app_rights"."app_id" IS 'appId';
COMMENT
ON COLUMN "plss-open"."op_app_rights"."id" IS '主键';
COMMENT
ON COLUMN "plss-open"."op_app_rights"."remark" IS '备用字段';
COMMENT
ON COLUMN "plss-open"."op_app_rights"."rights_value" IS '权益json串';

-- 应用表新增字段 机构名称，过期时间
alter table "plss-open"."op_app_info" add column("org_name" VARCHAR(64));
alter table "plss-open"."op_app_info" add column("expiry_time" VARCHAR(64));
comment
on column "plss-open"."op_app_info"."org_name" is '机构名称';
comment
on column "plss-open"."op_app_info"."expiry_time" is '过期时间';


-- 应用表过期时间字段修复之前的数据
update "plss-open".op_app_info
set expiry_time = '-1'
where expiry_time is null;

-- 应用表新增字段 用户名
alter table "plss-open"."op_app_info" add column("user_name" VARCHAR(128));
comment on column "plss-open"."op_app_info"."user_name" is '用户名称';