ALTER TABLE "plss-open".op_app_info ALTER COLUMN  org_id  TYPE varchar(255) USING org_id::varchar(255);
ALTER TABLE "plss-open".op_user_recommend_template ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-open".op_user_recommend_template ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-open".op_user_recommend_template ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-open".op_user_simulate_history ALTER COLUMN  user_id  TYPE varchar(255) USING user_id::varchar(255);
ALTER TABLE "plss-open".op_user_simulate_history ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-open".op_user_simulate_history ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-open".op_abutment_api_settings ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-open".op_abutment_api_settings ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
ALTER TABLE "plss-open".op_abutment_plan ALTER COLUMN  create_by  TYPE varchar(255) USING create_by::varchar(255);
ALTER TABLE "plss-open".op_abutment_plan ALTER COLUMN  update_by  TYPE varchar(255) USING update_by::varchar(255);
