--
-- PostgreSQL database dump
--

-- Dumped from database version 15.3 (Debian 15.3-1.pgdg120+1)
-- Dumped by pg_dump version 16.3

-- Started on 2024-08-15 11:17:21 CST

--
-- TOC entry 218 (class 1259 OID 16394)
-- Name: op_abutment_api_settings; Type: TABLE; Schema: plss-open; Owner: suwell
--
--
-- TOC entry 218 (class 1259 OID 16394)
-- Name: op_abutment_api_settings; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_doc_package_record
(
    id               int8                    NOT NULL,
    package_uid      varchar(100)            NULL,
    doc_package_info text                    NULL,
    "source"         varchar(50)             NULL,
    priority         int2                    NULL,
    operation_type   int2                    NULL,
    status           int2                    NOT NULL,
    create_time      timestamp DEFAULT now() NOT NULL,
    update_time      timestamp DEFAULT now() NOT NULL
);

ALTER TABLE "plss-open".op_abutment_doc_package_record OWNER TO suwell;
COMMENT ON TABLE "plss-open".op_abutment_doc_package_record IS '对接入库文档包记录表';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.id IS '数据id';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.package_uid IS '文件包标识';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.doc_package_info IS '对接入库文档包信息';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record."source" IS '文件包来源';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.priority IS '处理优先级：0-默认，1-高';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.operation_type IS '操作类型：1-新增，2-修改 ，3-删除';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.status IS '处理状态：0-未读取，1-读取成功，2-读取失败';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.create_time IS '创建时间';
COMMENT ON COLUMN "plss-open".op_abutment_doc_package_record.update_time IS '修改时间';

CREATE TABLE "plss-open".op_abutment_store_record
(
    id                int8                    NOT NULL,
    package_record_id int8                    NULL,
    doc_original_id   varchar(100)            NULL,
    doc_desc_info     text                    NULL,
    record_id         int8                    NULL,
    status            int2                    NOT NULL,
    create_time       timestamp DEFAULT now() NOT NULL,
    update_time       timestamp DEFAULT now() NOT NULL
);

ALTER TABLE "plss-open".op_abutment_store_record OWNER TO suwell;

COMMENT ON TABLE "plss-open".op_abutment_store_record IS '对接入库具体文档记录表';

-- Column comments

COMMENT ON COLUMN "plss-open".op_abutment_store_record.id IS '数据id';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.package_record_id IS '文档包记录id';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.doc_original_id IS '文档原id';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.doc_desc_info IS '对接入库文档信息';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.record_id IS '文档入库id';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.status IS '处理状态：1-成功上传，2-上传失败';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.create_time IS '创建时间';
COMMENT ON COLUMN "plss-open".op_abutment_store_record.update_time IS '修改时间';



CREATE TABLE "plss-open".op_abutment_api_settings (
                                                      id bigint NOT NULL,
                                                      config_code character varying(100) NOT NULL,
                                                      manufacturer_id bigint NOT NULL,
                                                      sketch character varying(150),
                                                      server_host character varying(300),
                                                      abutment_uri character varying(300),
                                                      version_num character varying(50),
                                                      flow_type smallint NOT NULL,
                                                      api_method character varying(20) NOT NULL,
                                                      data_transfer_format smallint NOT NULL,
                                                      input_args character varying(1000),
                                                      input_reflection character varying(1000),
                                                      output_reflection character varying(1000),
                                                      act_result_path character varying(300),
                                                      success_call_id bigint,
                                                      failed_call_id bigint,
                                                      additional character varying(1000),
                                                      enable smallint DEFAULT 1 NOT NULL,
                                                      spare1 character varying(300),
                                                      spare2 character varying(300),
                                                      spare3 character varying(300),
                                                      create_by bigint,
                                                      create_time timestamp without time zone NOT NULL,
                                                      update_by bigint,
                                                      update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_api_settings OWNER TO suwell;

--
-- TOC entry 4946 (class 0 OID 0)
-- Dependencies: 218
-- Name: TABLE op_abutment_api_settings; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_api_settings IS '对接接口配置表';


--
-- TOC entry 4947 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.id IS '数据id';


--
-- TOC entry 4948 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.config_code; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.config_code IS '数据code';


--
-- TOC entry 4949 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.manufacturer_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.manufacturer_id IS '厂商id';


--
-- TOC entry 4950 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.sketch; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.sketch IS '接口简述';


--
-- TOC entry 4951 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.server_host; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.server_host IS '服务地址(ip和端口或者域名)';


--
-- TOC entry 4952 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.abutment_uri; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.abutment_uri IS '接口uri';


--
-- TOC entry 4953 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.version_num; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.version_num IS '版本号';


--
-- TOC entry 4954 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.flow_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.flow_type IS '数据流向类型（推送/拉取） 1：产商推送；2：公文库拉取';


--
-- TOC entry 4955 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.api_method; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.api_method IS '接口方法类型 post/get';


--
-- TOC entry 4956 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.data_transfer_format; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.data_transfer_format IS '数据传递类型（xml/普通字符串json） 1：xml；2：普通入参（get对应query，post对应body的json） 3:表单';


--
-- TOC entry 4957 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.input_args; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.input_args IS '输入参数值';


--
-- TOC entry 4958 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.input_reflection; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.input_reflection IS '输入参数映射';


--
-- TOC entry 4959 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.output_reflection; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.output_reflection IS '输出参数映射';


--
-- TOC entry 4960 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.act_result_path; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.act_result_path IS '实际结果值链路';


--
-- TOC entry 4961 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.success_call_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.success_call_id IS '成功回调请求配置id';


--
-- TOC entry 4962 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.failed_call_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.failed_call_id IS '失败回调请求配置id';


--
-- TOC entry 4963 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.additional; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.additional IS '额外的配置条件';


--
-- TOC entry 4964 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.enable; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.enable IS '启用状态 0:关闭；1：启用';


--
-- TOC entry 4965 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare1 IS '备用1';


--
-- TOC entry 4966 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare2 IS '备用2';


--
-- TOC entry 4967 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.spare3 IS '备用3';


--
-- TOC entry 4968 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.create_by IS '创建人';


--
-- TOC entry 4969 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.create_time IS '创建时间';


--
-- TOC entry 4970 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.update_by IS '更新人';


--
-- TOC entry 4971 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN op_abutment_api_settings.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_api_settings.update_time IS '更新时间';


--
-- TOC entry 219 (class 1259 OID 16400)
-- Name: op_abutment_manufacturer; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_manufacturer (
                                                      id bigint NOT NULL,
                                                      app_id character varying(50),
                                                      manufacturer character varying(150) NOT NULL,
                                                      manufacturer_code character varying(150) NOT NULL,
                                                      business_type character varying(150) NOT NULL,
                                                      server_host character varying(300),
                                                      basic_condition character varying(1000),
                                                      spare1 character varying(300),
                                                      spare2 character varying(300),
                                                      spare3 character varying(300),
                                                      create_time timestamp without time zone NOT NULL,
                                                      update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_manufacturer OWNER TO suwell;

--
-- TOC entry 4972 (class 0 OID 0)
-- Dependencies: 219
-- Name: TABLE op_abutment_manufacturer; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_manufacturer IS '对接厂商表';


--
-- TOC entry 4973 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.id IS '数据id';


--
-- TOC entry 4974 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.app_id IS '应用id';


--
-- TOC entry 4975 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.manufacturer; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.manufacturer IS '厂商';


--
-- TOC entry 4976 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.manufacturer_code; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.manufacturer_code IS '厂商编号';


--
-- TOC entry 4977 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.business_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.business_type IS '业务类型';


--
-- TOC entry 4978 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.server_host; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.server_host IS '服务地址（ip端口或域名）';


--
-- TOC entry 4979 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.basic_condition; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.basic_condition IS '基础配置';


--
-- TOC entry 4980 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare1 IS '备用1';


--
-- TOC entry 4981 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare2 IS '备用2';


--
-- TOC entry 4982 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.spare3 IS '备用3';


--
-- TOC entry 4983 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.create_time IS '创建时间';


--
-- TOC entry 4984 (class 0 OID 0)
-- Dependencies: 219
-- Name: COLUMN op_abutment_manufacturer.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_manufacturer.update_time IS '更新时间';


--
-- TOC entry 220 (class 1259 OID 16405)
-- Name: op_abutment_plan; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_abutment_plan (
                                              id bigint NOT NULL,
                                              plan_name character varying(300) NOT NULL,
                                              manufacturer_config character varying(1000) NOT NULL,
                                              spare1 character varying(300),
                                              spare2 character varying(300),
                                              spare3 character varying(300),
                                              create_by bigint NOT NULL,
                                              create_time timestamp without time zone NOT NULL,
                                              update_by bigint NOT NULL,
                                              update_time timestamp without time zone NOT NULL
);


ALTER TABLE "plss-open".op_abutment_plan OWNER TO suwell;

--
-- TOC entry 4985 (class 0 OID 0)
-- Dependencies: 220
-- Name: TABLE op_abutment_plan; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_abutment_plan IS '对接预置计划表';


--
-- TOC entry 4986 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.id IS '数据id';


--
-- TOC entry 4987 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.plan_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.plan_name IS '集成对接方案名';


--
-- TOC entry 4988 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.manufacturer_config; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.manufacturer_config IS '具体集成厂商与接口设定';


--
-- TOC entry 4989 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.spare1; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare1 IS '备用1';


--
-- TOC entry 4990 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.spare2; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare2 IS '备用2';


--
-- TOC entry 4991 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.spare3; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.spare3 IS '备用3';


--
-- TOC entry 4992 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.create_by IS '创建人';


--
-- TOC entry 4993 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.create_time IS '创建时间';


--
-- TOC entry 4994 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.update_by IS '更新人';


--
-- TOC entry 4995 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN op_abutment_plan.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_abutment_plan.update_time IS '更新时间';


--
-- TOC entry 221 (class 1259 OID 16410)
-- Name: op_app_info; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_app_info (
                                         id bigint NOT NULL,
                                         app_name character varying(50) NOT NULL,
                                         app_id character varying(50) NOT NULL,
                                         org_id bigint DEFAULT 0 NOT NULL,
                                         type smallint DEFAULT 1 NOT NULL,
                                         status smallint DEFAULT 1 NOT NULL,
                                         del_flag character(1) DEFAULT 1 NOT NULL,
                                         create_by character varying DEFAULT ''::character varying NOT NULL,
                                         create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                         update_by character varying DEFAULT ''::character varying,
                                         update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                         remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_app_info OWNER TO suwell;

--
-- TOC entry 4996 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.app_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.app_name IS '应用名称';


--
-- TOC entry 4997 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.app_id IS '应用id';


--
-- TOC entry 4998 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.org_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.org_id IS '单位id';


--
-- TOC entry 4999 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.type IS '类型';


--
-- TOC entry 5000 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.status; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.status IS '状态';


--
-- TOC entry 5001 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.del_flag IS '删除标识';


--
-- TOC entry 5002 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.create_by IS '创建人';


--
-- TOC entry 5003 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.create_time IS '创建时间';


--
-- TOC entry 5004 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.update_by IS '修改人';


--
-- TOC entry 5005 (class 0 OID 0)
-- Dependencies: 221
-- Name: COLUMN op_app_info.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_info.update_time IS '修改时间';


--
-- TOC entry 222 (class 1259 OID 16424)
-- Name: op_app_ram; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_app_ram (
                                        id bigint NOT NULL,
                                        app_id character varying(64) NOT NULL,
                                        access_key character varying(255) NOT NULL,
                                        secret_key character varying(255) NOT NULL,
                                        del_flag character(1) DEFAULT 1 NOT NULL,
                                        create_by character varying DEFAULT ''::character varying NOT NULL,
                                        create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                        update_by character varying DEFAULT ''::character varying,
                                        update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                        remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_app_ram OWNER TO suwell;

--
-- TOC entry 5006 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.app_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.app_id IS 'appid';


--
-- TOC entry 5007 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.access_key IS '公钥';


--
-- TOC entry 5008 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.secret_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.secret_key IS '密钥';


--
-- TOC entry 5009 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.del_flag IS '删除标识';


--
-- TOC entry 5010 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.create_by IS '创建人';


--
-- TOC entry 5011 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.create_time IS '创建时间';


--
-- TOC entry 5012 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.update_by IS '修改人';


--
-- TOC entry 5013 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.update_time IS '修改时间';


--
-- TOC entry 5014 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN op_app_ram.remark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_app_ram.remark IS '备注';


--
-- TOC entry 223 (class 1259 OID 16435)
-- Name: op_preview; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_preview (
                                        id bigint NOT NULL,
                                        file_name character varying NOT NULL,
                                        file_url character varying NOT NULL,
                                        water_mark character varying NOT NULL,
                                        expire_date timestamp without time zone NOT NULL,
                                        permission character varying NOT NULL,
                                        create_time timestamp without time zone NOT NULL,
                                        app_id character varying NOT NULL
);


ALTER TABLE "plss-open".op_preview OWNER TO suwell;

--
-- TOC entry 5015 (class 0 OID 0)
-- Dependencies: 223
-- Name: TABLE op_preview; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON TABLE "plss-open".op_preview IS '生成轻阅读预览链接';


--
-- TOC entry 5016 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.file_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.file_name IS '预览文件名称';


--
-- TOC entry 5017 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.file_url; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.file_url IS '文件url';


--
-- TOC entry 5018 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.water_mark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.water_mark IS '水印';


--
-- TOC entry 5019 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.expire_date; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.expire_date IS '过期时间';


--
-- TOC entry 5020 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.permission; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.permission IS '权限';


--
-- TOC entry 5021 (class 0 OID 0)
-- Dependencies: 223
-- Name: COLUMN op_preview.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_preview.create_time IS '创建时间';


--
-- TOC entry 224 (class 1259 OID 16440)
-- Name: op_uniform_resource; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_uniform_resource (
                                                 id bigint NOT NULL,
                                                 module_name character varying(255) NOT NULL,
                                                 ur_name character varying(255) NOT NULL,
                                                 ur_path character varying(255) NOT NULL,
                                                 ur_desc character varying(255) DEFAULT ''::character varying NOT NULL,
                                                 status smallint DEFAULT 1 NOT NULL,
                                                 del_flag character(1) DEFAULT 1 NOT NULL,
                                                 create_by character varying DEFAULT ''::character varying NOT NULL,
                                                 create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                                 update_by character varying DEFAULT ''::character varying,
                                                 update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                                 remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_uniform_resource OWNER TO suwell;

--
-- TOC entry 5022 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.module_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.module_name IS '模块名称';


--
-- TOC entry 5023 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.ur_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_name IS '接口名称';


--
-- TOC entry 5024 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.ur_path; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_path IS '接口路径';


--
-- TOC entry 5025 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.ur_desc; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.ur_desc IS '接口描述';


--
-- TOC entry 5026 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.status; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.status IS '接口状态';


--
-- TOC entry 5027 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.del_flag IS '删除标识';


--
-- TOC entry 5028 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.create_by IS '创建人';


--
-- TOC entry 5029 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.create_time IS '创建时间';


--
-- TOC entry 5030 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.update_by IS '修改人';


--
-- TOC entry 5031 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN op_uniform_resource.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_uniform_resource.update_time IS '修改时间';


--
-- TOC entry 225 (class 1259 OID 16453)
-- Name: op_ur_log; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_ur_log (
                                       id bigint NOT NULL,
                                       user_id bigint NOT NULL,
                                       user_name character varying(64) NOT NULL,
                                       access_key character varying(64) NOT NULL,
                                       visit_ip character varying(64) DEFAULT ''::character varying NOT NULL,
                                       request_id character varying(64) NOT NULL,
                                       ur_id bigint NOT NULL,
                                       ur_name character varying(64) NOT NULL,
                                       ur_result character varying(255) NOT NULL,
                                       del_flag character(1) DEFAULT 1 NOT NULL,
                                       create_by character varying DEFAULT ''::character varying NOT NULL,
                                       create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                       update_by character varying DEFAULT ''::character varying,
                                       update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                       remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_ur_log OWNER TO suwell;

--
-- TOC entry 5032 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.user_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.user_id IS '用户id';


--
-- TOC entry 5033 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.user_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.user_name IS '用户名称';


--
-- TOC entry 5034 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.access_key IS '公钥';


--
-- TOC entry 5035 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.visit_ip; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.visit_ip IS '访问ip';


--
-- TOC entry 5036 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.request_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.request_id IS '请求id';


--
-- TOC entry 5037 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.ur_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_id IS '接口id';


--
-- TOC entry 5038 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.ur_name; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_name IS '接口名称';


--
-- TOC entry 5039 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.ur_result; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.ur_result IS '接口结果';


--
-- TOC entry 5040 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.del_flag IS '删除标识';


--
-- TOC entry 5041 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.create_by IS '创建人';


--
-- TOC entry 5042 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.create_time IS '创建时间';


--
-- TOC entry 5043 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.update_by IS '修改人';


--
-- TOC entry 5044 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.update_time IS '修改时间';


--
-- TOC entry 5045 (class 0 OID 0)
-- Dependencies: 225
-- Name: COLUMN op_ur_log.remark; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_log.remark IS '备注';


--
-- TOC entry 226 (class 1259 OID 16465)
-- Name: op_ur_permission; Type: TABLE; Schema: plss-open; Owner: suwell
--

CREATE TABLE "plss-open".op_ur_permission (
                                              id bigint NOT NULL,
                                              access_key character varying(64) NOT NULL,
                                              ur_id bigint NOT NULL,
                                              permission_type smallint NOT NULL,
                                              visit_num integer DEFAULT 0 NOT NULL,
                                              visit_days integer DEFAULT 0 NOT NULL,
                                              visit_ip character varying(64) DEFAULT ''::character varying NOT NULL,
                                              del_flag character(1) DEFAULT 1 NOT NULL,
                                              create_by character varying DEFAULT ''::character varying NOT NULL,
                                              create_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                              update_by character varying DEFAULT ''::character varying,
                                              update_time timestamp(6) without time zone DEFAULT now() NOT NULL,
                                              remark character varying(512) DEFAULT ''::character varying
);


ALTER TABLE "plss-open".op_ur_permission OWNER TO suwell;

--
-- TOC entry 5046 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.access_key; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.access_key IS 'ak';


--
-- TOC entry 5047 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.ur_id; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.ur_id IS '接口id';


--
-- TOC entry 5048 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.permission_type; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.permission_type IS '权限类型：1访问2测试3vip';


--
-- TOC entry 5049 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.visit_num; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_num IS '访问次数';


--
-- TOC entry 5050 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.visit_days; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_days IS '访问天数';


--
-- TOC entry 5051 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.visit_ip; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.visit_ip IS '访问ip';


--
-- TOC entry 5052 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.del_flag; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.del_flag IS '删除标识';


--
-- TOC entry 5053 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.create_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.create_by IS '创建人';


--
-- TOC entry 5054 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.create_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.create_time IS '创建时间';


--
-- TOC entry 5055 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.update_by; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.update_by IS '修改人';


--
-- TOC entry 5056 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN op_ur_permission.update_time; Type: COMMENT; Schema: plss-open; Owner: suwell
--

COMMENT ON COLUMN "plss-open".op_ur_permission.update_time IS '修改时间';

        --
-- TOC entry 4362 (class 2606 OID 17380)
-- Name: op_abutment_api_settings op_abutment_api_settings_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_doc_package_record
    ADD CONSTRAINT op_abutment_doc_package_record_pkey PRIMARY KEY (id);

ALTER TABLE ONLY "plss-open".op_abutment_store_record
    ADD CONSTRAINT op_abutment_store_record_pkey PRIMARY KEY (id);


ALTER TABLE ONLY "plss-open".op_abutment_api_settings
    ADD CONSTRAINT op_abutment_api_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4364 (class 2606 OID 17382)
-- Name: op_abutment_manufacturer op_abutment_manufacturer_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_manufacturer
    ADD CONSTRAINT op_abutment_manufacturer_pkey PRIMARY KEY (id);


--
-- TOC entry 4366 (class 2606 OID 17384)
-- Name: op_abutment_plan op_abutment_plan_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_abutment_plan
    ADD CONSTRAINT op_abutment_plan_pkey PRIMARY KEY (id);


--
-- TOC entry 4368 (class 2606 OID 17386)
-- Name: op_app_info op_app_info_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_app_info
    ADD CONSTRAINT op_app_info_pkey PRIMARY KEY (id);


--
-- TOC entry 4370 (class 2606 OID 17388)
-- Name: op_app_ram op_app_ram_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_app_ram
    ADD CONSTRAINT op_app_ram_pkey PRIMARY KEY (id);


--
-- TOC entry 4372 (class 2606 OID 17390)
-- Name: op_uniform_resource op_uniform_resource_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_uniform_resource
    ADD CONSTRAINT op_uniform_resource_pkey PRIMARY KEY (id);


--
-- TOC entry 4374 (class 2606 OID 17392)
-- Name: op_ur_log op_ur_log_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_ur_log
    ADD CONSTRAINT op_ur_log_pkey PRIMARY KEY (id);


--
-- TOC entry 4376 (class 2606 OID 17394)
-- Name: op_ur_permission op_ur_permission_pkey; Type: CONSTRAINT; Schema: plss-open; Owner: suwell
--

ALTER TABLE ONLY "plss-open".op_ur_permission
    ADD CONSTRAINT op_ur_permission_pkey PRIMARY KEY (id);


-- Completed on 2024-08-15 11:17:26 CST

--
-- PostgreSQL database dump complete
--

INSERT INTO "plss-open"."op_app_info"("id","app_name","app_id","org_id","type","status","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020538000709,'webofficeApp','200020537886021',1610564159749,1,1,'1','1612860642053',TO_DATE('2024-05-09 14:22:49.581000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:22:49.219358','YYYY-MM-DD HH24:MI:SS.FF'),'');
INSERT INTO "plss-open"."op_app_info"("id","app_name","app_id","org_id","type","status","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020866245957,'webofficeApp','200020866237765',1610564159749,1,1,'1','1612860642053',TO_DATE('2024-05-09 14:23:29.649000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:23:29.231276','YYYY-MM-DD HH24:MI:SS.FF'),'');
INSERT INTO "plss-open"."op_app_info"("id","app_name","app_id","org_id","type","status","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020885095750,'第三方对接','200020885095749',1610564159749,1,1,'1','1612860642053',TO_DATE('2024-05-09 14:23:31.951000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:23:31.532733','YYYY-MM-DD HH24:MI:SS.FF'),'');

INSERT INTO "plss-open"."op_app_ram"("id","app_id","access_key","secret_key","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020538770757,'200020537886021','e1454a2bf449d7be6c03aa3d86705ec36978e6ff','92877c5eb81b285038ee24bcc9534d20464589b66f14e225d0f967d529581f812fb470ab694570f5cfdefb7012b895ee4f920f7b9df6499f99c5a31da0d55716','1','1612860642053',TO_DATE('2024-05-09 14:22:49.674000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:22:49.310561','YYYY-MM-DD HH24:MI:SS.FF'),'');
INSERT INTO "plss-open"."op_app_ram"("id","app_id","access_key","secret_key","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020866475333,'200020866237765','581dd58ecf06b5a7e2ed8ad00e62405df299aa01','df24ce36217d52d4caffc238b3ce284705a5d90a02e32374839468ba3fb1780f9fb977f1be3de46e7360c40fcc9218077baece571b85e6a7ea8d5a1c80b78acb','1','1612860642053',TO_DATE('2024-05-09 14:23:29.677000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:23:29.259912','YYYY-MM-DD HH24:MI:SS.FF'),'');
INSERT INTO "plss-open"."op_app_ram"("id","app_id","access_key","secret_key","del_flag","create_by","create_time","update_by","update_time","remark") VALUES(200020885333317,'200020885095749','8080eb9bf3ebd30bc1639be28b54fd65128711b7','af72cd4667ce7d55feddfc465eb5c82f11ec0811eafad269c04d0392613a70143bb477bb1d1259a7fe025f3e5f1c174e3e228b7cf0c50d9c8338b9ed074d24f5','1','1612860642053',TO_DATE('2024-05-09 14:23:31.979000','YYYY-MM-DD HH24:MI:SS.FF'),'',TO_DATE('2024-05-09 14:23:31.561375','YYYY-MM-DD HH24:MI:SS.FF'),'');
