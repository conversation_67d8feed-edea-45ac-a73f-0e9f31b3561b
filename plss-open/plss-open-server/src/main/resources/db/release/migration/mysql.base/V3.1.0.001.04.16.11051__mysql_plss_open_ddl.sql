ALTER TABLE `plss_open`.op_app_info
MODIFY COLUMN org_id VARCHAR(256) ;
ALTER TABLE `plss_open`.op_user_recommend_template
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_open`.op_user_recommend_template
MODIFY COLUMN create_by VA<PERSON>HAR(256) ;
ALTER TABLE `plss_open`.op_user_recommend_template
MODIFY COLUMN update_by VA<PERSON>HAR(256) ;
ALTER TABLE `plss_open`.op_user_simulate_history
MODIFY COLUMN user_id VARCHAR(256) ;
ALTER TABLE `plss_open`.op_user_simulate_history
MODIFY COLUMN create_by VA<PERSON><PERSON>R(256) ;
ALTER TABLE `plss_open`.op_user_simulate_history
MODIFY COLUMN update_by VA<PERSON>HAR(256) ;
ALTER TABLE `plss_open`.op_abutment_api_settings
MODIFY COLUMN create_by VA<PERSON><PERSON><PERSON>(256) ;
ALTER TABLE `plss_open`.op_abutment_api_settings
MODIFY COLUMN update_by <PERSON><PERSON><PERSON><PERSON>(256) ;
<PERSON>TER TABLE `plss_open`.op_abutment_plan
MODIFY COLUMN create_by VARCHAR(256) ;
ALTER TABLE `plss_open`.op_abutment_plan
MODIFY COLUMN update_by VARCHAR(256) ;