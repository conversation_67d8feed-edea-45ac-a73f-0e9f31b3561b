package com.suwell.plss.open.system.service.decode;

import com.suwell.plss.open.constant.OpenConstants;
import com.suwell.plss.open.enums.PasswordTypeEnum;
import com.suwell.plss.open.utils.AESUtil;
import com.suwell.plss.open.utils.Md5Util;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/8/27 10:22
 */
@Service
public class AesDecodeService implements DecodePasswordService{
    @Override
    public String getPasswordType() {
        return PasswordTypeEnum.AES.getValue();
    }

    @Override
    public String decodePassword(String password, String appId) {
        String plainText = AESUtil.decrypt(password, appId);
        return Md5Util.md5(OpenConstants.MD5_SALT + plainText);
    }

    @Override
    public String encodePassword(String password, String appId) {
        return AESUtil.encrypt(password, appId);
    }
}
