package com.suwell.plss.open.integration.thirdparty.dreamit.ou;

import lombok.Data;

import java.io.Serializable;

/**
 * 梦创双杨消息响应结构
 * <AUTHOR>
 */
@Data
public class DreamItMessageResp implements Serializable {

    private static final long serialVersionUID = -84931444515115836L;
    /**
     * 消息代码
     */
    private String code;

    /**
     * 返回结果:success、error
     */
    private String type;

    /**
     * 提示消息
     */
    private String message;

    /**
     * api接口中请求的唯一串
     */
    private String token;

    /**
     * 返回结果数据
     */
    private Object data;

    public static DreamItMessageResp success(){
        DreamItMessageResp gztMessageResp = new DreamItMessageResp();
        gztMessageResp.setCode("200");
        gztMessageResp.setType("success");
        return gztMessageResp;
    }

    public static DreamItMessageResp success(String token, Object data){
        DreamItMessageResp gztMessageResp = new DreamItMessageResp();
        gztMessageResp.setCode("200");
        gztMessageResp.setType("success");
        gztMessageResp.setToken(token);
        gztMessageResp.setData(data);
        return gztMessageResp;
    }

    public static DreamItMessageResp fail(String msg) {
        DreamItMessageResp gztMessageResp = new DreamItMessageResp();
        gztMessageResp.setCode("500");
        gztMessageResp.setType("error");
        gztMessageResp.setMessage(msg);
        return gztMessageResp;
    }
}
