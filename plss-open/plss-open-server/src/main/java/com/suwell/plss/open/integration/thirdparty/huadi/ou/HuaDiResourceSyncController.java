package com.suwell.plss.open.integration.thirdparty.huadi.ou;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.open.integration.annotation.AccessRestriction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 华迪组织用户初始化同步
 * 初始化时需要手动调用接口
 * @date 2024/8/30 10:13
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/hua_di/si")
public class HuaDiResourceSyncController {

    @Resource
    private HuaDiOUService huaDiOUService;

    @AccessRestriction
    @PostMapping("/org/init")
    public R<Void> syncInitOrgResource() {
        huaDiOUService.pullAllOrg();
        return R.ok();
    }

    @AccessRestriction
    @PostMapping("/user/init")
    public R<Void> syncInitUserResource() {
        huaDiOUService.pullAllUser();
        return R.ok();
    }
}
