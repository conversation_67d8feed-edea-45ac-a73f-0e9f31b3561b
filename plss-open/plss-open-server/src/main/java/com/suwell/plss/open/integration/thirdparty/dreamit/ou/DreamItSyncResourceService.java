package com.suwell.plss.open.integration.thirdparty.dreamit.ou;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.suwell.plss.open.integration.enums.SipType;
import com.suwell.plss.open.integration.outline.SelectionSystemIntegrationStandardizationContext;
import com.suwell.plss.open.utils.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Objects;
import java.util.zip.CRC32;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Service
@Slf4j
public class DreamItSyncResourceService {


    public DreamItMessageResp syncResource(String syncResourceStr, HttpServletRequest request) {
        //校验crc32
        CRC32 crc32 = new CRC32();
        crc32.update(syncResourceStr.getBytes());
        long checkSum = crc32.getValue();
        String dataCrc32Code = request.getHeader("dataCrc32Code");
        if(!Objects.equals(Long.toString(checkSum),dataCrc32Code)){
            log.warn("校验crc32校验和不一致,dataCrc32Code:{},checkSum:{}",dataCrc32Code,checkSum);
            return DreamItMessageResp.fail("校验crc32校验和不一致");
        }
        XmlMapper xmlMapper = new XmlMapper();
        DreamItSyncResourceBody syncResourceBody = null;
        try {
            syncResourceBody = xmlMapper.readValue(syncResourceStr, DreamItSyncResourceBody.class);
        } catch (JsonProcessingException e) {
            log.error("同步资源解析xml失败:{}",syncResourceStr,e);
            throw new RuntimeException(e);
        }
        String data = syncResourceBody.getData();
        //md5校验
        String dataMd5 = Md5Util.md5(data);
        if(!Objects.equals(dataMd5,syncResourceBody.getDataCode())){
            log.error("同步资源md5校验失败,dataMd5:{},dataCode:{}",dataMd5,syncResourceBody.getDataCode());
            return DreamItMessageResp.fail("md5校验失败");
        }
        String dataType = syncResourceBody.getDataType();
        String dataOperateType = syncResourceBody.getDataOperateType();
        String pubToken = syncResourceBody.getPubToken();
        try {
            if(Objects.equals("dept",dataType)){
                //组织架构同步
                DreamItSyncOrgInfo orgInfo = JSON.parseObject(data, DreamItSyncOrgInfo.class);
                HashMap<String, Object>  orgMap = new HashMap<>();
                orgMap.put("orgInfo", orgInfo);
                orgMap.put("dataOperateType", dataOperateType);
                SelectionSystemIntegrationStandardizationContext.execManageOrg(SipType.DREAM_IT_1, orgMap);
            }else if(Objects.equals("user",dataType)){
                //用户信息同步
                DreamItSyncUserInfo userInfo = JSON.parseObject(data, DreamItSyncUserInfo.class);
                HashMap<String, Object> userMap = new HashMap<>();
                userMap.put("userInfo", userInfo);
                userMap.put("dataOperateType", dataOperateType);
                SelectionSystemIntegrationStandardizationContext.execManageUser(SipType.DREAM_IT_1, userMap);
            }
            DreamItMessageResp resp = DreamItMessageResp.success();
            resp.setToken(pubToken);
            return resp;
        }catch (Exception e){
            DreamItMessageResp resp = DreamItMessageResp.fail("同步双杨资源异常");
            resp.setToken(pubToken);
            return resp;
        }
    }
}
