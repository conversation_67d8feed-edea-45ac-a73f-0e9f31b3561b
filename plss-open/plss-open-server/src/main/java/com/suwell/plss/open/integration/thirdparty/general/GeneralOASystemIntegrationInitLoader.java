package com.suwell.plss.open.integration.thirdparty.general;

import com.suwell.plss.open.integration.enums.Manufacturer;
import com.suwell.plss.open.integration.outline.SystemIntegrationInitLoader;
import com.suwell.plss.open.integration.outline.store.StoreIntegrationInit;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class GeneralOASystemIntegrationInitLoader implements SystemIntegrationInitLoader {

    @Resource
    private StoreIntegrationInit storeIntegrationInit;

    @Override
    public String manufacturer() {
        return Manufacturer.STORE_DEFAULT_OA.getManufacturerCode();
    }

    @Override
    public void loadInitialConfiguration() {
        storeIntegrationInit.initialConfiguration(this.manufacturer());
    }
}
