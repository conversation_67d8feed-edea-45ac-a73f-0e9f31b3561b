package com.suwell.plss.open.integration.thirdparty.clinbrain.ou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suwell.plss.open.integration.enums.SipType;
import com.suwell.plss.open.integration.outline.SelectionSystemIntegrationStandardizationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ClinBrainSystemIntegrationScheduled {


    /**
     * 调度任务同步更新ClinBrain组织机构信息
     * @date 2024/5/23 13:55
     */
    public void syncUpdateOrgResource() {
        SelectionSystemIntegrationStandardizationContext.execManageOrg(SipType.CLIN_BRAIN_1, null);
    }

    /**
     * 调度任务同步更新ClinBrain用户信息
     * @date 2024/5/23 13:56
     * @param args
     */
    public void syncUpdateUserResource(Map<String, Object> args) {
        Map<String, Object> execRes = SelectionSystemIntegrationStandardizationContext.execManageUser(SipType.CLIN_BRAIN_1, args);
        if (CollectionUtils.isEmpty(execRes) || !"ok".equals(execRes.get("completion").toString())) {
            return;
        }
        JSONObject pageInfo = JSON.parseObject(JSON.toJSONString(execRes.get("pageInfo")));
        Integer pageCount = pageInfo.getInteger("PageCount");
        Integer currentPage = pageInfo.getInteger("Number");
        if (currentPage >= pageCount) {
            log.info("拉取ClinBrain用户信息并同步，已拉取到所有数据，同步更新结束");
        } else {
            try {
                Thread.sleep(10 * 1000L);
            } catch (InterruptedException e) {
                log.error("拉取ClinBrain分页的用户信息，休眠等待时发生异常", e);
            }
            Map<String, Object> newArgs = new HashMap<>(1);
            Integer cp = currentPage + 1;
            newArgs.put("pageNum", cp);
            log.info("拉取ClinBrain分页的用户信息，已拉取到的页码->{}，即将拉取下一页数据->{}", currentPage, cp);
            this.syncUpdateUserResource(newArgs);
        }
    }
}
