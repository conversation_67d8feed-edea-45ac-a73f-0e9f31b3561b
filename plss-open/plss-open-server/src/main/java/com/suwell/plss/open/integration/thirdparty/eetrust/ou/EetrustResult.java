package com.suwell.plss.open.integration.thirdparty.eetrust.ou;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.suwell.plss.framework.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Setter
@Getter
@ToString
@JacksonXmlRootElement(localName = "returnData")
public class EetrustResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 8728905039761165008L;

    private String status;

    private String errcode;

    private String errormsg;

    public static EetrustResult buildOk() {
        EetrustResult er = restResult("1", "success");
        er.setStatus("1");
        return er;
    }

    public static EetrustResult buildFailure(Exception e) {
        EetrustResult result;
        if (e instanceof BizException) {
            result = restResult(String.valueOf(((BizException) e).getErrorCode()), e.getMessage());
        } else {
            String message = e.getMessage();
            if (!StringUtils.hasLength(message)) {
                message = "服务异常";
            }
            result = restResult("99999", message);
        }
        result.setStatus("2");
        return result;
    }


    private static EetrustResult restResult(String code, String msg) {
        EetrustResult apiResult = new EetrustResult();
        apiResult.setErrcode(code);
        apiResult.setErrormsg(msg);
        return apiResult;
    }

}
