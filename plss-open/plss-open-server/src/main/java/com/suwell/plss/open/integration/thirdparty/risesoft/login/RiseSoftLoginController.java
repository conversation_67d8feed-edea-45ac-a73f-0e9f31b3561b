package com.suwell.plss.open.integration.thirdparty.risesoft.login;


import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.open.integration.annotation.AccessRestriction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 有声博大单点登录
 * @date 2024/8/8 16:06
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/v1/rise_soft/login")
@RequiredArgsConstructor
public class RiseSoftLoginController {

    @Resource
    private RiseSoftLoginService riseSoftLoginService;

    /**
     * 登录回传响应接口
     * @date 2024/8/8 16:06
     * @param loginName 登录名
     * @param timestamp 时间戳
     * @param state 接口认证参数
     */
    @AccessRestriction
    @GetMapping("/ack")
    public R<Map<String, Object>> webSingleLogin(@RequestParam("loginName") String loginName, @RequestParam("timestamp") Long timestamp,
                                                 @RequestParam("state") String state) {
        return R.ok(riseSoftLoginService.loginProcess(loginName, timestamp, state));
    }
}
