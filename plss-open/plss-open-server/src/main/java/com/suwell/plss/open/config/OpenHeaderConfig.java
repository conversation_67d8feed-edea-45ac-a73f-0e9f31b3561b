package com.suwell.plss.open.config;

import com.suwell.plss.framework.nacos.PlssConfigLoader;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.util.List;

/**
 * 透传header key配置
 * <AUTHOR>
 * @date 2024年11月21日
 */

@Configuration
@Data
@ConfigurationProperties(prefix = "open.header.config")
public class OpenHeaderConfig extends PlssConfigLoader {

    /**
     * key列表
     */
    private List<String> keys;

}
