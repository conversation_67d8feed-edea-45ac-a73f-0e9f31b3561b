package com.suwell.plss.open.integration.thirdparty.lanxin.login;

import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.open.integration.annotation.AccessRestriction;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 新疆蓝信单点登录回传控制器
 * <p>实际使用是放开被注释掉的部分，而注释掉25~47行
 * @date 2023/11/22 16:43
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/v1/si/login/lx")
@RequiredArgsConstructor
public class LanXinSingleLoginController {

    private final LanXinSingleLoginService lxSingleLogin;


    /**
     * 登录回传响应接口
     * @date 2023/11/22 16:43
     * @param response 响应体
     * @param answer 加密后的用户信息
     * @param token 调用token
     */
    @AccessRestriction
    @PostMapping("/ack")
    public R<String> singleLoginAck(HttpServletResponse response, @RequestParam("SAMLResponse") String answer, @RequestParam("token") String token) {
        return R.ok(lxSingleLogin.gainTokenForSingleLogin(answer));
    }
    /**
     * 蓝信客户端登录后跳转单点登录的响应接口
     * @date 2023/12/11 16:10
     * @param response 响应体
     * @param code 蓝信客户端登录后生成的用户code
     */
    @AccessRestriction
    @GetMapping("/take_along")
    public R<String> respondClientSingleLogin(HttpServletResponse response, @RequestParam("code") String code) {
        return R.ok(lxSingleLogin.gainTokenForClientSingLogin(code));
    }

//    /**
//     * 登录回传响应接口
//     * @date 2023/11/22 16:43
//     * @param response 响应体
//     */
//    @AccessRestriction
//    @PostMapping("/ack")
//    public void singleLoginAck(HttpServletResponse response, HttpServletRequest request) {
//        try {
//            String samlResponse = request.getParameter("SAMLResponse");
//            log.info("蓝信网页登录回传登录信息串-->{}", samlResponse);
//            response.sendRedirect(lxSingleLogin.gainTokenForSingleLogin(samlResponse));
//        } catch (IOException e) {
//            log.error("LanXinSingleLoginController#singleLoginAck重定向登录过渡页异常", e);
//        }
//    }
//    /**
//     * 蓝信客户端登录后跳转单点登录的响应接口
//     * @date 2023/12/11 16:10
//     * @param response 响应体
//     * @param code 蓝信客户端登录后生成的用户code
//     */
//    @AccessRestriction
//    @GetMapping("/take_along")
//    public void respondClientSingleLogin(HttpServletResponse response, @RequestParam("code") String code) {
//        try {
//            response.sendRedirect(lxSingleLogin.gainTokenForClientSingLogin(code));
//        } catch (IOException e) {
//            log.error("LanXinSingleLoginController#respondClientSingleLogin重定向登录过渡页异常", e);
//        }
//    }


}
