package com.suwell.plss.open.integration.thirdparty.zefu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.open.entity.OpAbutmentManufacturer;
import com.suwell.plss.open.integration.enums.Manufacturer;
import com.suwell.plss.open.integration.outline.SystemIntegrationInitLoader;
import com.suwell.plss.open.service.OpAbutmentManufacturerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ZefuSystemIntegrationInitLoader implements SystemIntegrationInitLoader {

    @Resource
    private OpAbutmentManufacturerService abutmentManufacturerService;

    @Resource
    private ZefuConfig zefuConfig;

    @Override
    public String manufacturer() {
        return Manufacturer.ZE_FU.getManufacturerCode();
    }

    @Override
    public void loadInitialConfiguration() {
        OpAbutmentManufacturer manufacturer = abutmentManufacturerService.getManufacturerByCode(this.manufacturer());
        if (manufacturer != null) {
            String serverHost = manufacturer.getServerHost();
            zefuConfig.setServerHost(serverHost);

            JSONObject basicConditionJson = JSON.parseObject(manufacturer.getBasicCondition());
            zefuConfig.setAppId(basicConditionJson.getString("appId"));
            zefuConfig.setSecret(basicConditionJson.getString("secret"));
        }
    }
}
