package com.suwell.plss.open.integration.thirdparty.eetrust.login;


import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.open.integration.annotation.AccessRestriction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 *
 * 对接eim系统单点登录（时代亿信）
 * @date 2025/4/30 18:40
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/v1/si/login/eet")
public class EetrustLoginController {

    @Resource
    private EetrustSingleLoginService eetSingleLoginService;


    /**
     *
     * 跳转至校验授权地址
     * @date 2025/4/30 18:41
     */
    @AccessRestriction
    @PostMapping("/authorization")
    public R<String> redirectionWebsite(){
        return R.ok(eetSingleLoginService.redirectAuthorization());
    }


    /**
     *
     * 单点登录响应
     * @date 2025/4/30 18:43
     * @param code 授权码
     * @param state 签名唯一值（固定随机码）
     */
    @AccessRestriction
    @GetMapping("/ack")
    public R<Map<String, Object>> webSingleLogin(@RequestParam String code, @RequestParam String state){
        return R.ok(eetSingleLoginService.loginProcess(code, state));
    }

}
