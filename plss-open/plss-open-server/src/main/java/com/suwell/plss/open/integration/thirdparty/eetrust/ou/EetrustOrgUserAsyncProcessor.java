package com.suwell.plss.open.integration.thirdparty.eetrust.ou;

import com.suwell.plss.open.dto.AbutmentApiSettingsDTO;
import com.suwell.plss.open.integration.constant.LocalCacheConstants;
import com.suwell.plss.open.integration.enums.IsiType;
import com.suwell.plss.open.integration.outline.SelectionInnerSystemIntegrationContext;
import com.suwell.plss.open.integration.thirdparty.eetrust.EetrustConfiguration;
import com.suwell.plss.open.service.OpAbutmentApiSettingsService;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class EetrustOrgUserAsyncProcessor {

    @Resource
    private EetrustConfiguration eetrustConfiguration;

    @Resource
    private OpAbutmentApiSettingsService apiSettingsService;

    public void asyncUpdateOrg() {
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("ee-trust-ou-org-change", "v1",
                (s1, s2) -> apiSettingsService.getAbutmentConfigByCode(s1, s2));
        SelectionInnerSystemIntegrationContext.asyncDisposeOrg(apiConfig.getInputReflection(),
                eetrustConfiguration.getTenantId(), IsiType.DEFAULT);
    }

    public void asyncUpdateUsers() {
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("ee-trust-ou-user-change", "v1",
                (s1, s2) -> apiSettingsService.getAbutmentConfigByCode(s1, s2));
        SelectionInnerSystemIntegrationContext.asyncDisposeUser(apiConfig.getInputReflection(),
                eetrustConfiguration.getTenantId(), IsiType.DEFAULT);
    }
}
