package com.suwell.plss.open.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * wps政务ai用户数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@Accessors(chain = true)
@TableName("op_wps_ai_user")
public class OpWpsAiUser {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 请求uuid
     */
    private String requestId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 用户源id
     */
    private String srcId;

    /**
     * 用户源名称
     */
    private String srcName;

    /**
     * 入参json
     */
    private String detailJson;

    /**
     * 操作类型(1:新增或修改、2:删除)
     */
    private Integer operateType;

    /**
     * 操作状态(1:待处理、2:已成功、3:已失败)
     */
    private Integer operateStatus;

    /**
     * 失败次数
     */
    private Integer failTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}
