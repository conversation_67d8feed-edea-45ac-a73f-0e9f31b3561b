package com.suwell.plss.open.integration.thirdparty.dreamit.ou.v2;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
public class DreamItV2SyncUserInfo {

    /**
     * 主键
     */
    private String id;

    /**
     * 删除标识 -1已删除；>=0正常
     */
    private Integer rowState;

    /**
     * 最后修改时间
     */
    private String lastUpdateTime;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 登录名
     */
    private String logName;

    /**
     * 密码
     */
    private String password;

    /**
     * 工号
     */
    private String cardId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 家庭住址
     */
    private String homeAdd;

    /**
     * 家庭电话
     */
    private String homeTel;

    /**
     * 家庭邮编
     */
    private String homeZipcode;

    /**
     * 传真
     */
    private String fax;

    /**
     * 拼音全拼
     */
    private String pinyin;

    /**
     * 拼音首字母
     */
    private String pinyinInitial;

    /**
     * 职级编码
     */
    private String careerRank;

    /**
     * 职级名称
     */
    private String careerRankText;

    /**
     * 性别编码  1男；2女
     */
    private Integer sex;

    /**
     * 性别
     */
    private String sexText;

    /**
     * 教育程度编码
     */
    private Integer education;

    /**
     * 教育程度
     */
    private String educationText;

    /**
     * 状态 1有效；-1封锁；-2离退休；-3调离
     */
    private Integer status;

    /**
     * 状态
     */
    private String statusText;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 参加工作时间
     */
    private String joinWork;

    /**
     * 进入本单位时间
     */
    private String joinCompany;

    /**
     * 显示顺序
     */
    private Long showOrder;

    /**
     * 密级等级
     */
    private Integer mjLevel;

    /**
     * 密级等级
     */
    private String mjLevelText;

    /**
     * 直接领导
     */
    private String leaderText;

    /**
     * 直接领导id
     */
    private String leader;

    /**
     * 备注
     */
    private String remark;

    /**
     * 头像
     */
    private String photo;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 聊天工具ID
     */
    private String imId;

    /**
     * 办公地点
     */
    private String workPlace;

    /**
     * CA证书序列号
     */
    private String certSN;

    /**
     * 签名图片
     */
    private String signPhoto;

    /**
     * 智能柜标识
     */
    private String cabinetCode;

    /**
     * 是否在编用户  -1否，1是
     */
    private Integer isFormation;

    /**
     * 是否在编用户
     */
    private String isFormationText;

    /**
     * 职称编码
     */
    private Integer professionalRank;

    /**
     * 职称
     */
    private String professionalRankText;

    /**
     * 机构id
     */
    private String userDn;

    /**
     * 部门列表
     */
    private List<DreamItV2SyncOrgInfo> orgList;
}
