package com.suwell.plss.open.integration.thirdparty.glaway;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.open.entity.OpAbutmentManufacturer;
import com.suwell.plss.open.integration.enums.Manufacturer;
import com.suwell.plss.open.integration.outline.SystemIntegrationInitLoader;
import com.suwell.plss.open.service.OpAbutmentManufacturerService;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.suwell.plss.open.integration.thirdparty.glaway.GlawayReqRespConstants.*;

@Component
public class GlawaySystemIntegrationInitLoader implements SystemIntegrationInitLoader {

    @Resource
    private OpAbutmentManufacturerService abutmentManufacturerService;
    @Resource
    private GlawayConfiguration masterDataMConfiguration;

    @Override
    public String manufacturer() {
        return Manufacturer.GLAWAY.getManufacturerCode();
    }

    @Override
    public void loadInitialConfiguration() {
        OpAbutmentManufacturer manufacturer = abutmentManufacturerService.getManufacturerByCode(this.manufacturer());
        if (manufacturer != null) {
            JSONObject parseObject = JSON.parseObject(manufacturer.getBasicCondition());
            List<String> orgTypesList = Arrays.stream(parseObject.getString(SYNC_ORG_TYPE).split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            List<String> duplicateName = Arrays.stream(parseObject.getString(DUPLICATE_NAME).split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            masterDataMConfiguration.setHost(manufacturer.getServerHost());
            masterDataMConfiguration.setSyncOrgType(orgTypesList);
            masterDataMConfiguration.setTopCode(parseObject.getString(TOP_CODE));
            masterDataMConfiguration.setDuplicateName(duplicateName);
        }
    }
}
