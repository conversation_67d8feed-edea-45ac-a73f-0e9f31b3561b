package com.suwell.plss.open.v2Eod.system;

import cn.hutool.core.collection.CollUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.utils.PageUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.common.utils.bean.DozerUtils;
import com.suwell.plss.open.api.system.dto.request.SysOrgAddApiReq;
import com.suwell.plss.open.api.system.dto.request.SysOrgDelApiReq;
import com.suwell.plss.open.api.system.dto.request.SysOrgListApiReq;
import com.suwell.plss.open.api.system.dto.request.SysOrgUpdateApiReq;
import com.suwell.plss.open.api.system.dto.response.OrgListResp;
import com.suwell.plss.open.api.system.dto.response.OrgLazyTreeResp;
import com.suwell.plss.system.api.domain.SysOrgDto;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.service.OrgRpcService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * v2版本-eod-org-controller
 * @date 2024-12-23
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eod/system/v2/org")
public class OrgController{

    @Resource
    private OrgRpcService orgRpcService;

    /**
     * 获取机构列表
     */
    @PostMapping("/list")
    public R<PageUtils<OrgListResp>> list(@Valid @RequestBody SysOrgListApiReq org) {
        SysOrgDto sysOrg = new SysOrgDto();
        sysOrg.setOrgName(org.getOrgName());
        sysOrg.setOrgType(org.getOrgType());
        HashMap<String, Object> params = new HashMap<>();
        params.put("page",org.getPage());
        params.put("pageSize",org.getPageSize());
        sysOrg.setParams(params);
        R<PageUtils<SysOrg>> orgPageR = orgRpcService.queryPage(sysOrg);
        if(orgPageR.isError()){
            return R.error();
        }
        PageUtils<OrgListResp> sysOrgRespPageUtils = new PageUtils<>();
        BeanUtils.copyProperties(orgPageR.getData(),sysOrgRespPageUtils);
        if(CollUtil.isNotEmpty(orgPageR.getData().getList())){
            List<SysOrg> orgList = orgPageR.getData().getList();
            List<OrgListResp> orgRespList = new ArrayList<>(orgList.size());
            for (SysOrg sysOrgResult : orgList) {
                OrgListResp sysOrgResp = new OrgListResp();
                BeanUtils.copyProperties(sysOrgResult,sysOrgResp);
                orgRespList.add(sysOrgResp);
            }
            sysOrgRespPageUtils.setList(orgRespList);
        }
        return R.ok(sysOrgRespPageUtils);
    }

    /**
     * 机构树懒加载
     */
    @PostMapping("/lazy/list")
    public R<OrgLazyTreeResp> getLazyList(@RequestParam(value = "orgId",required = false) String orgId) {
        if (StringUtils.isEmpty(orgId)){
            orgId = "0";
        }
        R<SysOrg> r = orgRpcService.getLazyList(orgId);
        if (r.isError()){
            return R.error("查询组织机构树失败");
        }
        if (Objects.nonNull(r.getData())){
            OrgLazyTreeResp sysOrgTreeResp = new OrgLazyTreeResp();
            BeanUtils.copyProperties(r.getData(),sysOrgTreeResp);
            List<OrgLazyTreeResp> sysOrgTreeResps = DozerUtils.convertListToNew(r.getData().getChildren(), OrgLazyTreeResp.class);
            sysOrgTreeResp.setChildren(sysOrgTreeResps);
            return R.ok(sysOrgTreeResp);
        }
        return R.ok();
    }

    /**
     * 新增机构
     */
    @PostMapping("/add")
    public R add(@Valid @RequestBody SysOrgAddApiReq sysOrgAddApiReq) {
        SysOrg sysOrg = new SysOrg();
        BeanUtils.copyProperties(sysOrgAddApiReq,sysOrg);
        return orgRpcService.add(sysOrg);
    }

    /**
     * 删除机构
     */
    @PostMapping("/del")
    public R remove(@Valid @RequestBody SysOrgDelApiReq req) {
        if (CollUtil.isEmpty(req.getOrgIdList())){
            return R.error("删除机构id不能为空");
        }
        return orgRpcService.batchDel(req.getOrgIdList());
    }

    /**
     * 修改机构
     * @param req 修改机构
     */
    @PostMapping("/update")
    public R edit(@Valid @RequestBody SysOrgUpdateApiReq req) {
        SysOrgDto sysOrg = new SysOrgDto();
        BeanUtils.copyProperties(req,sysOrg);
        return orgRpcService.update(sysOrg);
    }
}
