package com.suwell.plss.open.proofread.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.CollectionLambdaUtils;
import com.suwell.plss.framework.common.utils.bean.BeanUtils;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.open.api.proofread.domain.request.OpGetCheckRecordReq;
import com.suwell.plss.open.api.proofread.domain.request.OpGetCheckResultReq;
import com.suwell.plss.open.api.proofread.domain.request.OpSubmitCheckContentReq;
import com.suwell.plss.open.api.proofread.domain.response.OpCheckResultResp;
import com.suwell.plss.open.api.proofread.http.ProofreadEntrance;
import com.suwell.plss.open.api.wps.domain.RT;
import com.suwell.plss.open.constant.ExceptionConstants;
import com.suwell.plss.plugin.api.proofread.domain.request.GetCheckResultReq;
import com.suwell.plss.plugin.api.proofread.domain.request.SubmitCheckContentReq;
import com.suwell.plss.plugin.api.proofread.domain.response.CheckResultResp;
import com.suwell.plss.plugin.api.proofread.remote.ProofreadRpcService;
import com.suwell.plss.record.service.DocumentProofreadRecordRpcService;
import com.suwell.plss.record.standard.dto.request.RpcDocProofreadRecordReq;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * ==========================
 * 开发：wei.wang
 * 创建时间：2023-12-08 19:05
 * 版本：1.0
 * 描述：
 * ==========================
 */
@RestController
public class ProofreadEntranceController implements ProofreadEntrance {

    @Resource
    private ProofreadRpcService proofreadRpcService;

    @Resource
    private DocumentProofreadRecordRpcService documentProofreadRecordRpcService;

    @Override
    public RT<Void> submitCheck(OpSubmitCheckContentReq req) {
        SubmitCheckContentReq submitReq = new SubmitCheckContentReq();
        submitReq.setSeqId(req.getSeqId());
        submitReq.setBusinessNo(req.getBusinessNo());
        submitReq.setCheckType(req.getCheckType());
        submitReq.setList(CollectionLambdaUtils.listToList(req.getList(), o -> {
            SubmitCheckContentReq.CheckContentDTO contentReq = new SubmitCheckContentReq.CheckContentDTO();
            contentReq.setContent(o.getContent());
            contentReq.setRow(o.getRow());
            return contentReq;
        }));
        R<Void> r = proofreadRpcService.submitCheck(submitReq);
        if (r.isError()) {
            return RT.error(r.getCode(), r.getMsg());
        }
        return RT.ok();
    }

    @Override
    public RT<OpCheckResultResp> result(OpGetCheckResultReq req) {
        GetCheckResultReq getReq = new GetCheckResultReq();
        getReq.setBusinessNo(req.getBusinessNo());
        R<CheckResultResp> resultR = proofreadRpcService.result(getReq);
        if (resultR.isError()) {
            return RT.error(resultR.getCode(), resultR.getMsg());
        }
        return Optional.ofNullable(resultR.getData()).map(o -> {
            OpCheckResultResp resp = new OpCheckResultResp();
            resp.setBusinessNo(o.getBusinessNo());
            resp.setProgress(o.getProgress());
            resp.setItems(CollectionLambdaUtils.listToList(o.getItems(), item -> {
                OpCheckResultResp.OpCheckWordItem itemResp = new OpCheckResultResp.OpCheckWordItem();
                itemResp.setStartRow(item.getStartRow());
                itemResp.setPos(item.getPos());
                itemResp.setLevel(item.getLevel());
                itemResp.setErrWord(item.getErrWord());
                itemResp.setCorrectWord(item.getCorrectWord());
                return itemResp;
            }));
            return RT.ok(resp);
        }).orElse(RT.ok());
    }

    @Override
    public RT<Void> stopCheck(OpGetCheckResultReq req) {
        GetCheckResultReq getReq = new GetCheckResultReq();
        getReq.setBusinessNo(req.getBusinessNo());
        proofreadRpcService.stopCheck(getReq);
        return RT.ok();
    }

    @Override
    public R<List<Long>> record(List<OpGetCheckRecordReq> req) {
        ArrayList<RpcDocProofreadRecordReq> rpcReqList = new ArrayList<>();
        for (OpGetCheckRecordReq recordReq : req) {
            String jsonResult = recordReq.getResultJson();

            if (StrUtil.isNotBlank(jsonResult) && !JSONUtil.isJsonObj(jsonResult)) {
                throw new ServiceException(recordReq.getFileName() + ExceptionConstants.VALID_JSON_OBJ_EXCEPTION);
            }else if(StrUtil.isNotBlank(jsonResult) && !JSONUtil.parseObj(jsonResult).containsKey(ExceptionConstants.JSON_OBJ_KEY)){
                throw new ServiceException(recordReq.getFileName() + ExceptionConstants.VALID_JSON_OBJ_KEY_EXCEPTION);
            }

            RpcDocProofreadRecordReq rpcDocProofreadRecordReq = new RpcDocProofreadRecordReq();
            BeanUtils.copyProperties(recordReq,rpcDocProofreadRecordReq);
            rpcDocProofreadRecordReq.setCreateBy(SecurityUtils.getUserId());
//            rpcDocProofreadRecordReq.setCreateBy(6L);
            rpcReqList.add(rpcDocProofreadRecordReq);
        }

//
        return documentProofreadRecordRpcService.saveProofreadRecord(rpcReqList);
    }
}
