package com.suwell.plss.open.integration.thirdparty.bamboocloud.ou;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.hy.corecode.idgen.WFGIdGenerator;
import com.suwell.plss.framework.common.constant.UserConstants;
import com.suwell.plss.framework.common.enums.FigureEnum;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.open.dto.AbutmentApiSettingsDTO;
import com.suwell.plss.open.enums.OpenBizError;
import com.suwell.plss.open.integration.constant.LocalCacheConstants;
import com.suwell.plss.open.integration.enums.IsiType;
import com.suwell.plss.open.integration.enums.SipType;
import com.suwell.plss.open.integration.outline.SelectionInnerSystemIntegrationContext;
import com.suwell.plss.open.integration.outline.SystemIntegrationDataDisposeUtil;
import com.suwell.plss.open.integration.outline.SystemIntegrationOUPreProcessor;
import com.suwell.plss.open.service.OpAbutmentApiSettingsService;
import com.suwell.plss.system.api.domain.request.OrgUserSyncUpdateReq;
import com.suwell.plss.system.api.domain.request.SrcOrgUserInfoReq;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import com.suwell.plss.system.api.enums.IntegrationDataManageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BambooCloudSystemIntegrationService implements SystemIntegrationOUPreProcessor {

    @Resource
    private WFGIdGenerator idGenerator;

    @Resource
    private OpAbutmentApiSettingsService abutmentConfigService;


    @Override
    public String manufacturerVersion() {
        return SipType.BAMBOO_CLOUD_1.getManufacturerVersion();
    }

    @Override
    public Map<String, Object> manageOrg(Map<String, Object> args) {
        String dmt = Optional.ofNullable(args.get("dmt")).orElse("").toString();
        IntegrationDataManageType manageType = IntegrationDataManageType.getDataManageType(dmt);
        Map<String, Object> objectMap = Collections.emptyMap();
        args.remove("dmt");
        switch (manageType) {
            case CREATE:
                objectMap = this.createOrg(args);
                break;
            case UPDATE:
                this.modifyOrg(args);
                break;
            case READ:
                objectMap = this.queryOrg(args);
                break;
            case DELETE:
                this.removeOrg(args);
                break;
            default:
        }
        return objectMap;
    }

    private Map<String, Object> createOrg(Map<String, Object> args) {
        log.info("BambooCloud同步创建组织机构，参数->{}", args);
        Date dateNow = new Date();
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("bamboo-cloud-org-create", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        String orgCReflection = apiConfig.getInputReflection();
        Object parentIdObj = args.get("parentId");
        String parentId;
        if (parentIdObj == null) {
            parentId = "SPECIFIED-ROOT";
            args.put("parentId", parentId);
        } else {
            parentId = parentIdObj.toString();
        }
        String srcId = IdUtil.fastSimpleUUID();
        SrcOrgUserInfoReq srcOrgInfo = new SrcOrgUserInfoReq();
        srcOrgInfo.setUid(srcId);
        srcOrgInfo.setParentUid(parentId);
        srcOrgInfo.setDetailsJson(JSON.toJSONString(args));
        srcOrgInfo.setOperationStatus(1);
        SysOrg sysOrg = SystemIntegrationDataDisposeUtil.gainOrgByOriginal(args, orgCReflection);
        sysOrg.setSrcId(srcId);
        sysOrg.setOrgType(FigureEnum.TWO.getNumChar());
        OrgUserSyncUpdateReq syncOrgAddReq = new OrgUserSyncUpdateReq();
        syncOrgAddReq.setOaData(Collections.singletonList(srcOrgInfo));
        syncOrgAddReq.setTarOrgList(Collections.singletonList(sysOrg));
        SelectionInnerSystemIntegrationContext.saveOrgInfos(syncOrgAddReq, IsiType.DEFAULT);
        Map<String, Object> map = new HashMap<>(1);
        map.put("uid", srcId);
        return map;
    }

    private void modifyOrg(Map<String, Object> args) {
        log.info("BambooCloud同步修改组织机构，参数->{}", args);
        String notChange = "notChange";
        Date dateNow = new Date();
        String bimOrgId = args.get("bimOrgId").toString();
        List<Map<String, Object>> orgInfos = SelectionInnerSystemIntegrationContext
                .findOrgInfos(Collections.singletonList(bimOrgId), null, Boolean.FALSE, IsiType.DEFAULT);
        if (CollectionUtils.isEmpty(orgInfos)) {
            throw new BizException(OpenBizError.ORG_INFO_NOT_EXIST);
        }
        Map<String, Object> oaDbOrg = orgInfos.get(0);
        oaDbOrg.remove("lid");
        args.forEach((k, v) -> {
            if (oaDbOrg.containsKey(k)) {
                oaDbOrg.put(k, v);
            }
        });
        Boolean enable = Boolean.parseBoolean(Optional.ofNullable(args.get("__ENABLE__")).orElse("").toString());
        // 如果没有传parentId 说明没有改动parentId
        // 如果有传parentId 但是值为空串 说明调整为顶层
        // 如果有传parentId 但是值不为空串 说明是普通的调整
        String currentParentId = Optional.ofNullable(args.get("parentId")).orElse(notChange).toString();
        if (notChange.equals(currentParentId)) {
            currentParentId = oaDbOrg.get("parentId").toString();
        } else if (!StringUtils.hasLength(currentParentId)) {
            currentParentId = "SPECIFIED-ROOT";
            oaDbOrg.put("parentId", currentParentId);
        }
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("bamboo-cloud-org-update", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        String orgUReflection = apiConfig.getInputReflection();
        SysOrg updateOrg = SystemIntegrationDataDisposeUtil.gainOrgByOriginal(args, orgUReflection);
        updateOrg.setSrcId(bimOrgId);
        if (Boolean.FALSE.equals(enable)) {
            updateOrg.setStatus(UserConstants.DEPT_DISABLE);
        }
        SrcOrgUserInfoReq srcOrgUpdateInfo = new SrcOrgUserInfoReq();
        srcOrgUpdateInfo.setUid(bimOrgId);
        srcOrgUpdateInfo.setParentUid(currentParentId);
        srcOrgUpdateInfo.setDetailsJson(JSON.toJSONString(oaDbOrg));
        OrgUserSyncUpdateReq modifyReq = new OrgUserSyncUpdateReq();
        modifyReq.setOaData(Collections.singletonList(srcOrgUpdateInfo));
        modifyReq.setTarOrgList(Collections.singletonList(updateOrg));
        SelectionInnerSystemIntegrationContext.modifyOrgInfo(modifyReq, IsiType.DEFAULT);
    }

    private Map<String, Object> queryOrg(Map<String, Object> args) {
        String queryOrgId = Optional.ofNullable(args.get("bimOrgId")).orElse("").toString();
        List<Map<String, Object>> orgInfos;
        if (StringUtils.hasLength(queryOrgId)) {
            orgInfos = SelectionInnerSystemIntegrationContext
                    .findOrgInfos(Collections.singletonList(queryOrgId), null, Boolean.FALSE, IsiType.DEFAULT);
        } else {
            orgInfos = SelectionInnerSystemIntegrationContext
                    .findOrgInfos(null, null, Boolean.FALSE, IsiType.DEFAULT);
        }
        Map<String, Object> hashMap = new HashMap<>(1);
        if (StringUtils.hasLength(queryOrgId) && !CollectionUtils.isEmpty(orgInfos)) {
            Map<String, Object> actData = new HashMap<>(3);
            actData.put("orgName", orgInfos.get(0).get("orgName"));
            actData.put("orgUid", orgInfos.get(0).get("orgUid"));
            actData.put("parentId", orgInfos.get(0).get("parentId"));
            hashMap.put("organization", actData);
        }
        if (!StringUtils.hasLength(queryOrgId) && !CollectionUtils.isEmpty(orgInfos)) {
            List<String> orgIdList = orgInfos.stream().map(m -> m.get("lid").toString()).collect(Collectors.toList());
            hashMap.put("orgIdList", orgIdList);
        }
        return hashMap;
    }

    private void removeOrg(Map<String, Object> args) {
        log.info("BambooCloud同步删除组织机构，参数->{}", args);
        String removeOrgId = args.get("bimOrgId").toString();
        List<Map<String, Object>> orgInfos = SelectionInnerSystemIntegrationContext
                .findOrgInfos(Collections.singletonList(removeOrgId), null, Boolean.FALSE, IsiType.DEFAULT);
        if (CollectionUtils.isEmpty(orgInfos)) {
            throw new BizException(OpenBizError.ORG_INFO_NOT_EXIST);
        }
        SelectionInnerSystemIntegrationContext.removeOrgInfos(Collections.singletonList(removeOrgId), IsiType.DEFAULT);
    }

    @Override
    public Map<String, Object> manageUser(Map<String, Object> args) {
        String dmt = Optional.ofNullable(args.get("dmt")).orElse("").toString();
        IntegrationDataManageType manageType = IntegrationDataManageType.getDataManageType(dmt);
        Map<String, Object> manageResult = Collections.emptyMap();
        args.remove("dmt");
        switch (manageType) {
            case CREATE:
                manageResult = this.createUser(args);
                break;
            case UPDATE:
                this.modifyUser(args);
                break;
            case READ:
                manageResult = this.queryUser(args);
                break;
            case DELETE:
                this.removeUser(args);
                break;
            default:
        }
        return manageResult;
    }

    private Map<String, Object> createUser(Map<String, Object> args) {
        log.info("BambooCloud同步创建用户，参数->{}", args);
        Date dateNow = new Date();
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("bamboo-cloud-user-create", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        String userCReflection = apiConfig.getInputReflection();
        SysUser addUser = SystemIntegrationDataDisposeUtil.gainUserByOriginal(args, userCReflection);
        String srcId = IdUtil.fastSimpleUUID();
        addUser.setSrcId(srcId);
        String orgId = args.get("orgId").toString();
        SrcOrgUserInfoReq srcUserInfo = new SrcOrgUserInfoReq();
        srcUserInfo.setUid(srcId);
        srcUserInfo.setCheckedOrgList(Collections.singletonList(orgId));
        srcUserInfo.setDetailsJson(JSON.toJSONString(args));
        srcUserInfo.setOperationStatus(1);
        OrgUserSyncUpdateReq syncUserAddReq = new OrgUserSyncUpdateReq();
        syncUserAddReq.setOaData(Collections.singletonList(srcUserInfo));
        syncUserAddReq.setTarUserList(Collections.singletonList(addUser));
        SelectionInnerSystemIntegrationContext.saveUserInfos(syncUserAddReq, IsiType.DEFAULT);
        Map<String, Object> map = new HashMap<>(1);
        map.put("uid", srcId);
        return map;
    }

    private void modifyUser(Map<String, Object> args) {
        log.info("BambooCloud同步修改用户，参数->{}", args);
        Date dateNow = new Date();
        String bimUid = args.get("bimUid").toString();
        List<Map<String, Object>> userInfos = SelectionInnerSystemIntegrationContext.findUserInfos(Collections.singletonList(bimUid), null, IsiType.DEFAULT);
        if (CollectionUtils.isEmpty(userInfos)) {
            throw new BizException(OpenBizError.USER_INFO_NOT_EXIST);
        }
        Map<String, Object> oaDbUser = userInfos.get(0);
        oaDbUser.remove("lid");
        args.forEach((k, v) -> {
            if (oaDbUser.containsKey(k)) {
                oaDbUser.put(k, v);
            }
        });
        Boolean enable = Boolean.parseBoolean(Optional.ofNullable(args.get("__ENABLE__")).orElse("").toString());
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("bamboo-cloud-user-update", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        String userUReflection = apiConfig.getInputReflection();
        SysUser modifyUser = SystemIntegrationDataDisposeUtil.gainUserByOriginal(args, userUReflection);
        modifyUser.setSrcId(bimUid);
        if (Boolean.FALSE.equals(enable)) {
            modifyUser.setStatus(UserConstants.NO_STATUS);
            modifyUser.setDelFlag(FigureEnum.TWO.getNumChar());
        }
        OrgUserSyncUpdateReq syncUserModifyReq = new OrgUserSyncUpdateReq();
        SrcOrgUserInfoReq userInfoReq = new SrcOrgUserInfoReq();
        userInfoReq.setUid(bimUid);
        userInfoReq.setCheckedOrgList(Collections.singletonList(oaDbUser.get("orgId").toString()));
        userInfoReq.setDetailsJson(JSON.toJSONString(oaDbUser));
        syncUserModifyReq.setOaData(Collections.singletonList(userInfoReq));
        syncUserModifyReq.setTarUserList(Collections.singletonList(modifyUser));
        SelectionInnerSystemIntegrationContext.modifyUserInfos(syncUserModifyReq, IsiType.DEFAULT);
    }

    private Map<String, Object> queryUser(Map<String, Object> args) {
        log.info("BambooCloud同步查询用户，参数->{}", args);
        String queryUserId = Optional.ofNullable(args.get("bimUid")).orElse("").toString();
        List<Map<String, Object>> userInfos;
        if (StringUtils.hasLength(queryUserId)) {
            userInfos = SelectionInnerSystemIntegrationContext.findUserInfos(Collections.singletonList(queryUserId), null, IsiType.DEFAULT);
        } else {
            userInfos = SelectionInnerSystemIntegrationContext.findUserInfos(null, null, IsiType.DEFAULT);
        }
        Map<String, Object> hashMap = new HashMap<>(1);
        if (StringUtils.hasLength(queryUserId) && !CollectionUtils.isEmpty(userInfos)) {
            Map<String, Object> actData = new HashMap<>(3);
            actData.put("userName", userInfos.get(0).get("userName"));
            actData.put("userUid", userInfos.get(0).get("userUid"));
            actData.put("fullName", userInfos.get(0).get("fullName"));
            // 组织id
            actData.put("orgId", userInfos.get(0).get("orgIds"));
            hashMap.put("account", actData);
        }
        if (!StringUtils.hasLength(queryUserId) && !CollectionUtils.isEmpty(userInfos)) {
            List<String> userIdList = userInfos.stream().map(m -> m.get("lid").toString()).collect(Collectors.toList());
            hashMap.put("userIdList", userIdList);
        }
        return hashMap;
    }

    private void removeUser(Map<String, Object> args) {
        log.info("BambooCloud同步删除用户，参数->{}", args);
        String removeUserId = args.get("bimUid").toString();
        List<Map<String, Object>> userInfos = SelectionInnerSystemIntegrationContext.findUserInfos(Collections.singletonList(removeUserId), null, IsiType.DEFAULT);
        if (CollectionUtils.isEmpty(userInfos)) {
            throw new BizException(OpenBizError.USER_INFO_NOT_EXIST);
        }
        SelectionInnerSystemIntegrationContext.removeUserInfos(Collections.singletonList(removeUserId), IsiType.DEFAULT);
    }
}
