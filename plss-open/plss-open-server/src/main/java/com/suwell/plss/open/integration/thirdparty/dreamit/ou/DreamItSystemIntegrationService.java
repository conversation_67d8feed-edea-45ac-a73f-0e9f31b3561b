package com.suwell.plss.open.integration.thirdparty.dreamit.ou;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.open.dto.AbutmentApiSettingsDTO;
import com.suwell.plss.open.integration.constant.LocalCacheConstants;
import com.suwell.plss.open.integration.enums.IsiType;
import com.suwell.plss.open.integration.enums.SipType;
import com.suwell.plss.open.integration.outline.SelectionInnerSystemIntegrationContext;
import com.suwell.plss.open.integration.outline.SystemIntegrationDataDisposeUtil;
import com.suwell.plss.open.integration.outline.SystemIntegrationOUPreProcessor;
import com.suwell.plss.open.service.OpAbutmentApiSettingsService;
import com.suwell.plss.system.api.domain.request.OrgUserSyncUpdateReq;
import com.suwell.plss.system.api.domain.request.SrcOrgUserInfoReq;
import com.suwell.plss.system.api.entity.SysOrg;
import com.suwell.plss.system.api.entity.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 双杨通集成服务
 * @date 2024/4/5 15:03
 * <AUTHOR>
 */
@Slf4j
@Service
public class DreamItSystemIntegrationService implements SystemIntegrationOUPreProcessor {

    @Resource
    private OpAbutmentApiSettingsService abutmentConfigService;

    @Override
    public String manufacturerVersion() {
        return SipType.DREAM_IT_1.getManufacturerVersion();
    }

    @Override
    public Map<String, Object> manageOrg(Map<String, Object> args) {
        changeOrgOfSingle(args);
        Map<String, Object> resMap = new HashMap<>(1);
        resMap.put("completion", "ok");
        return resMap;
    }


    private void changeOrgOfSingle(Map<String, Object> args) {
        String dataOperateType = args.get("dataOperateType").toString();
        DreamItSyncOrgInfo syncOrgInfo = JSON.parseObject(JSON.toJSONString(args.get("orgInfo")), DreamItSyncOrgInfo.class);
        JSONObject orgInfoJson = JSON.parseObject(JSON.toJSONString(syncOrgInfo));
        String srcOrgId = syncOrgInfo.getId();
        String srcParentId = syncOrgInfo.getFId();
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("dream-it-org-change", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        List<String> updataStatusList = new ArrayList<>(Arrays.asList("mod", "move"));
        if(Objects.equals(dataOperateType,"add")){
            // 新增组织机构
            SysOrg sysOrg = SystemIntegrationDataDisposeUtil.gainOrgByOriginal(orgInfoJson, apiConfig.getInputReflection());
            Integer rowState = syncOrgInfo.getRowState();
            sysOrg.setDelFlag(rowState != null && rowState >=0 ? "1" : "2");
            sysOrg.setStatus(Objects.equals(1,syncOrgInfo.getStatus()) ? "1" : "2");
            SrcOrgUserInfoReq userInfoReq = new SrcOrgUserInfoReq();
            userInfoReq.setUid(srcOrgId);
            userInfoReq.setParentUid(srcParentId);
            userInfoReq.setDetailsJson(JSON.toJSONString(args));
            userInfoReq.setOperationStatus(1);
            OrgUserSyncUpdateReq syncOrgAddReq = new OrgUserSyncUpdateReq();
            syncOrgAddReq.setOaData(Collections.singletonList(userInfoReq));
            syncOrgAddReq.setTarOrgList(Collections.singletonList(sysOrg));
            SelectionInnerSystemIntegrationContext.saveOrgInfos(syncOrgAddReq, IsiType.DEFAULT);
        }else if(updataStatusList.contains(dataOperateType)){
            SysOrg modifyOrg = SystemIntegrationDataDisposeUtil.gainOrgByOriginal(orgInfoJson, apiConfig.getInputReflection());
            modifyOrg.setDelFlag(Objects.equals(-1,syncOrgInfo.getRowState()) ? "2" : "1");
            modifyOrg.setStatus(Objects.equals(1,syncOrgInfo.getStatus()) ? "1" : "2");
            SrcOrgUserInfoReq srcOrgModifyInfo = new SrcOrgUserInfoReq();
            srcOrgModifyInfo.setUid(srcOrgId);
            srcOrgModifyInfo.setParentUid(srcParentId);
            srcOrgModifyInfo.setDetailsJson(JSON.toJSONString(args));
            srcOrgModifyInfo.setOperationStatus(1);
            OrgUserSyncUpdateReq syncOrgModifyReq = new OrgUserSyncUpdateReq();
            syncOrgModifyReq.setOaData(Collections.singletonList(srcOrgModifyInfo));
            syncOrgModifyReq.setTarOrgList(Collections.singletonList(modifyOrg));
            SelectionInnerSystemIntegrationContext.modifyOrgInfo(syncOrgModifyReq, IsiType.DEFAULT);
        }else if(Objects.equals(dataOperateType,"del")){
            //删除
            SelectionInnerSystemIntegrationContext.removeOrgInfos(Collections.singletonList(srcOrgId), IsiType.DEFAULT);
        }
    }

    @Override
    public Map<String, Object> manageUser(Map<String, Object> args) {
        changeUserOfSingle(args);
        Map<String, Object> resMap = new HashMap<>(1);
        resMap.put("completion", "ok");
        return resMap;
    }

    private void changeUserOfSingle(Map<String, Object> args) {
        String dataOperateType = args.get("dataOperateType").toString();
        String userInfoStr = JSON.toJSONString(args.get("userInfo"));
        JSONObject useInfoJson = JSON.parseObject(userInfoStr);
        DreamItSyncUserInfo syncUserInfo = JSON.parseObject(userInfoStr, DreamItSyncUserInfo.class);
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("shuang-yang-user-change", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        String srcUserId = syncUserInfo.getId();
        List<String> updataStatusList = new ArrayList<>(Arrays.asList("mod", "mov"));
        if (Objects.equals(dataOperateType, "add")) {
            //新增
            SysUser sysUser = SystemIntegrationDataDisposeUtil.gainUserByOriginal(useInfoJson, apiConfig.getInputReflection());
            sysUser.setStatus(Objects.equals(1,syncUserInfo.getStatus()) ? "1" : "2");
            Integer rowState = syncUserInfo.getRowState();
            sysUser.setDelFlag(rowState != null && rowState >=0 ? "1" : "2");
            OrgUserSyncUpdateReq syncUserAddReq = new OrgUserSyncUpdateReq();
            SrcOrgUserInfoReq userInfoReq = new SrcOrgUserInfoReq();
            userInfoReq.setUid(sysUser.getSrcId());
            userInfoReq.setCheckedOrgList(Collections.singletonList(srcUserId));
            userInfoReq.setDetailsJson(JSON.toJSONString(args));
            userInfoReq.setOperationStatus(1);
            syncUserAddReq.setOaData(Collections.singletonList(userInfoReq));
            syncUserAddReq.setTarUserList(Collections.singletonList(sysUser));
            SelectionInnerSystemIntegrationContext.saveUserInfos(syncUserAddReq, IsiType.DEFAULT);
        } else if (updataStatusList.contains(dataOperateType)) {
            //修改
            SysUser modifyUser = SystemIntegrationDataDisposeUtil.gainUserByOriginal(useInfoJson, apiConfig.getInputReflection());
            modifyUser.setStatus(Objects.equals(1,syncUserInfo.getStatus()) ? "1" : "2");
            Integer rowState = syncUserInfo.getRowState();
            modifyUser.setDelFlag(rowState != null && rowState >=0 ? "1" : "2");
            OrgUserSyncUpdateReq syncUserModifyReq = new OrgUserSyncUpdateReq();
            SrcOrgUserInfoReq userInfoModifyReq = new SrcOrgUserInfoReq();
            userInfoModifyReq.setUid(modifyUser.getSrcId());
            userInfoModifyReq.setCheckedOrgList(Collections.singletonList(srcUserId));
            userInfoModifyReq.setDetailsJson(JSON.toJSONString(args));
            userInfoModifyReq.setOperationStatus(1);
            syncUserModifyReq.setOaData(Collections.singletonList(userInfoModifyReq));
            syncUserModifyReq.setTarUserList(Collections.singletonList(modifyUser));
            SelectionInnerSystemIntegrationContext.modifyUserInfos(syncUserModifyReq, IsiType.DEFAULT);
        } else if (Objects.equals(dataOperateType, "del")) {
            //删除
            SelectionInnerSystemIntegrationContext.removeOrgInfos(Collections.singletonList(srcUserId), IsiType.DEFAULT);
        }
    }
}
