package com.suwell.plss.open.integration.thirdparty.dreamit.ou.v2;


import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.open.filter.RequestWrapper;
import com.suwell.plss.open.integration.thirdparty.dreamit.ou.DreamItMessageResp;
import com.suwell.plss.open.integration.thirdparty.gsafety.ou.GSafetySyncResourceService;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-09-02
 */
@RestController
@Slf4j
@RequestMapping("/v2/si/dreamIt/resource")
public class DreamItV2ResourceController {

    @Resource
    private DreamItV2SyncResourceService syncResourceService;

    /**
     * 同步资源
     */
    @PostMapping("/sync")
    public DreamItMessageResp syncResource(RequestWrapper request) {
        String syncResourceStr = request.getBody();
        log.info("梦创双杨V2-接收到同步资源请求。syncResourceStr={}", syncResourceStr);

        DreamItMessageResp resp;
        String paramValue = "";
        try {
            String paramName = "data=";
            paramValue = URLDecoder.decode(
                    syncResourceStr.substring(syncResourceStr.indexOf(paramName) + paramName.length()),
                    StandardCharsets.UTF_8);
            log.info("梦创双杨V2-解析参数成功。paramValue={}", paramValue);
            resp = syncResourceService.syncResource(paramValue);
        } catch (Exception e) {
            log.error("梦创双杨V2-同步资源请求异常。paramValue={},em={}", paramValue, e.getMessage(), e);
            resp = DreamItMessageResp.fail(e.getMessage());
        }
        return resp;
    }

//    @Resource
//    private GSafetySyncResourceService gSafetySyncResourceService;
//
//    /**
//     * 测试
//     */
//    @PostMapping("/yjglSyncResourceTask")
//    public R<Object> yjglSyncResourceTask(@RequestBody(required = false) String jobParam) {
//        log.info("应急管理部同步资源-任务执行开始。jobParam={}", jobParam);
//
//        try {
//            gSafetySyncResourceService.syncResource(jobParam);
//        } catch (Exception e) {
//            log.error("应急管理部同步资源-任务执行异常。jobParam={},em={}", jobParam, e.getMessage(), e);
//            return R.error();
//        }
//        return R.ok();
//    }
}
