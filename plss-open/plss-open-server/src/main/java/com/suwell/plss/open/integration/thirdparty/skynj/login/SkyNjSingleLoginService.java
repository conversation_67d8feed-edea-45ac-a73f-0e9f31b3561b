package com.suwell.plss.open.integration.thirdparty.skynj.login;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.common.enums.FigureEnum;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.open.dto.AbutmentApiSettingsDTO;
import com.suwell.plss.open.enums.OpenBizError;
import com.suwell.plss.open.integration.constant.LocalCacheConstants;
import com.suwell.plss.open.integration.model.SendRequestSupplement;
import com.suwell.plss.open.integration.outline.SystemIntegrationDataDisposeUtil;
import com.suwell.plss.open.integration.thirdparty.skynj.SkyNjConfiguration;
import com.suwell.plss.open.service.OpAbutmentApiSettingsService;
import com.suwell.plss.system.api.domain.request.LoginAbutmentReq;
import com.suwell.plss.system.api.service.UserRpcService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SkyNjSingleLoginService {

    @Resource
    private OpAbutmentApiSettingsService abutmentConfigService;

    @Resource
    private UserRpcService userRpcService;

    @Resource
    private SkyNjConfiguration skyNjConfiguration;


    public Map<String, Object> loginProcess(String ticket, String state) {
        log.info("skyNj用户登录回传参数ticket->{},state->{}", ticket, state);
        if (!StringUtils.hasLength(state) || !state.equals(skyNjConfiguration.getState())) {
            throw new BizException(OpenBizError.SPECIFIED_SIGNATURE_ERROR);
        }
        JSONObject jo = this.exchangeAccessTokenWithTicket(ticket);
        String accessToken = jo.getString("accessToken");
        String userId = jo.getString("userId");
        if (!StringUtils.hasLength(userId) || !StringUtils.hasLength(accessToken)) {
            throw new BizException(OpenBizError.EXTERNAL_SINGLE_LOGIN_ACCESS_TOKEN_EMPTY);
        }
        String loginName = this.getUserInfo(accessToken, userId);
        LoginAbutmentReq loginAbutmentReq = new LoginAbutmentReq();
        loginAbutmentReq.setSrcUserName(loginName);
        R<Map<String, Object>> abutmentInfo = userRpcService.loginByAbutmentInfo(loginAbutmentReq);
        log.info("skyNj用户登录，内部登录结果->{}", abutmentInfo);
        if (abutmentInfo.getCode() != R.SUCCESS) {
            throw new BizException(OpenBizError.SINGLE_LOGIN_RESULT_ERROR);
        }
        String plToken = abutmentInfo.getData().get("access_token").toString();
        if (!StringUtils.hasLength(plToken)) {
            throw new BizException(OpenBizError.SINGLE_LOGIN_TOKEN_EMPTY);
        }
        return abutmentInfo.getData();
    }

    private JSONObject exchangeAccessTokenWithTicket(String ticket) {
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("sky-nj-access-token", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        SendRequestSupplement requestSupplement = new SendRequestSupplement();
        Map<String, Object> supParameters = new HashMap<>();
        supParameters.put("appCode", skyNjConfiguration.getAppCode());
        String digestHex = SmUtil.sm3WithSalt(skyNjConfiguration.getAppSecret().getBytes())
                .digestHex(skyNjConfiguration.getAppCode() + ticket);
        supParameters.put("sign", digestHex);
        supParameters.put("ticket", ticket);
        requestSupplement.setSupParameters(supParameters);
        return SystemIntegrationDataDisposeUtil.readDataAssembleSend(skyNjConfiguration.getHost(), apiConfig, requestSupplement, res -> {
            JSONObject jsonObject = JSON.parseObject(res);
            return jsonObject.getJSONObject("data");
        });
    }

    private String getUserInfo(String accessToken, String userId) {
        AbutmentApiSettingsDTO apiConfig = LocalCacheConstants.getAbutmentApiConfig("sky-nj-user-info", "v1",
                (s1, s2) -> abutmentConfigService.getAbutmentConfigByCode(s1, s2));
        SendRequestSupplement requestSupplement = new SendRequestSupplement();
        Map<String, Object> supParameters = new HashMap<>();
        supParameters.put("appCode", skyNjConfiguration.getAppCode());
        supParameters.put("accessToken", accessToken);
        supParameters.put("userId", userId);
        String digestHex = SmUtil.sm3WithSalt(skyNjConfiguration.getAppSecret().getBytes())
                .digestHex(skyNjConfiguration.getAppCode() + accessToken + userId);
        supParameters.put("sign", digestHex);
        requestSupplement.setSupParameters(supParameters);
        return SystemIntegrationDataDisposeUtil.readDataAssembleSend(skyNjConfiguration.getHost(), apiConfig, requestSupplement, res -> {
            JSONObject jsonObject = JSON.parseObject(res);
            String un = null;
            if (FigureEnum.ZERO.getNumChar().equals(jsonObject.getString("code"))) {
                SM2 sm2 = SmUtil.sm2(skyNjConfiguration.getSm2PrivateKey(), null);
                byte[] decrypt = sm2.decrypt(jsonObject.getString("data"), KeyType.PrivateKey);
                String us = new String(decrypt, StandardCharsets.UTF_8);
                JSONObject userJson = JSON.parseObject(us);
                // 有三种不同的人员类型，对应三种不同的数据结构，工作用户的账户是loginId，自然人和法人的账户都是loginname
                // 但是对于公文库来说，只有工作用户
                un = userJson.getString("loginId");
            }
            return un;
        });
    }
}
