package com.suwell.plss.open.system.service.sign;

import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.framework.common.exception.BizException;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.open.config.OpenBaseConfig;
import com.suwell.plss.open.constant.OpenConstants;
import com.suwell.plss.open.dto.request.AppInfo;
import com.suwell.plss.open.enums.AppTypeEnum;
import com.suwell.plss.open.enums.OpenBizError;
import com.suwell.plss.open.utils.AppInfoDateUtil;
import com.suwell.plss.open.utils.AppInfoUtil;
import com.suwell.plss.open.utils.Sm3Util;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @description 签名工厂
 * @date 2024/8/27 10:22
 */

@Service
public class SignFactory {

    @Resource
    private AppInfoUtil appInfoUtil;

    @Resource
    private OpenBaseConfig baseConfig;

    @Resource
    private AppInfoDateUtil appInfoDateUtil;

    @Resource
    private Sm3Util sm3Util;


    private final Map<String, AbstractSignService> abstractSignServiceMaps;

    public SignFactory(List<AbstractSignService> abstractSignServices) {
        abstractSignServiceMaps = new TreeMap<>();
        for (AbstractSignService abstractSignService : abstractSignServices) {
            abstractSignServiceMaps.put(abstractSignService.signType(), abstractSignService);
        }
    }

    /**
     * appId 校验
     * @param requestUrl 请求url
     * @return
     */
    public String checkApp(String requestUrl, String appId, String xUserQuery){
        String tenantId = "";
        if (requestUrl.startsWith(OpenConstants.WPS_URL)) {
            if (StringUtils.isEmpty(xUserQuery)) {
                throw new BizException(OpenBizError.APP_ID_ERROR);
            }
            String[] parts = xUserQuery.split("&");
            boolean flag = false;
            for (String part : parts) {
                if (!flag) {
                    if (part.startsWith("appId=")) {
                        flag = true;
                        appId = part.substring(6);
                        tenantId = getAppTenantId(appId);
                        if (StringUtils.isBlank(tenantId)) {
                            throw new BizException(OpenBizError.APP_ID_ERROR);
                        }
                    }
                }
            }
            if (!flag) {
                throw new BizException(OpenBizError.APP_ID_ERROR);
            }
        }else {
            if (!baseConfig.appPass(requestUrl)) {
                tenantId = getAppTenantId(appId);
                if (StringUtils.isBlank(tenantId)) {
                    throw new BizException(OpenBizError.APP_ID_ERROR);
                }
            } else {
                tenantId = getAppTenantId(appId);
            }
        }
        return tenantId;
    }

    /**
     * 获取appId对应的租户id,如果appId为空则返回空字符串
     * @param appId
     * @return
     */
    private String getAppTenantId(String appId){
        String tenantId = "";
        if (StringUtils.isNotBlank(appId)){
            tenantId = appInfoUtil.getAppInfo(appId).getTenantId();
        }
        return tenantId;
    }

    /**
     * 默认的配置免签
     * @param requestUrl 请求url
     * @return 是否签名
     */
    public Boolean sign(String requestUrl){
        if (!baseConfig.getVerifyAk()) {
            return true;
        }
        //是否放行
        if (baseConfig.verifyPass(requestUrl)) {
            return true;
        }
        return false;
    }
    /**
     * 验签前置校验 应用id是否存在，是否启用，是否过期
     * @return true 免验签 false 需验签
     */
    public Boolean preSign(String ak, String appId,AppInfo appInfo) {
        if (Objects.isNull(ak)) {
            throw new BizException(OpenBizError.AK_NOT_EXIST);
        }
        if (Objects.isNull(appId)) {
            throw new BizException(OpenBizError.APP_ID_ERROR);
        }
        // 校验appId是否启用
        if (appInfo.getStatus().equals(AppTypeEnum.DISABLE.getCode())) {
            throw new BizException(OpenBizError.APP_ID_ERROR_OLD);
        }
        // 校验appId是否过期
        if ("已过期".equals(appInfoDateUtil.ckeckDate(appInfo.getExpiryTime()))) {
            throw new BizException(OpenBizError.APP_ID_ERROR_OLD_DATE);
        }
        // 免验签
        if (appInfo.getType().equals(AppTypeEnum.DISABLE.getCode())) {
            return true;
        }
        return false;
    }

    public void checkTimestamp(String timestamp){
        if (!StringUtils.isNumeric(timestamp)) {
            throw new BizException(OpenBizError.AK_TIMESTAMP_ERROR);
        }
        sm3Util.checkTime(Long.parseLong(timestamp));
    }
    public Boolean CheckSign(String signType,String sk,String sign, JSONObject jsonObject) {
        AbstractSignService abstractSignService = abstractSignServiceMaps.get(signType);
        return abstractSignService.projectSign(sign,jsonObject,sk);
    }

    public String getSign(String signType,String sk,JSONObject jsonObject) {
        AbstractSignService abstractSignService = abstractSignServiceMaps.get(signType);
        return abstractSignService.getSign(jsonObject,sk);
    }
}
