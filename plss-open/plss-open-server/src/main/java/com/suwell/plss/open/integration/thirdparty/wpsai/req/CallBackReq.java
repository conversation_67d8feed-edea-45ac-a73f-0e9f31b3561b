package com.suwell.plss.open.integration.thirdparty.wpsai.req;

import com.suwell.plss.open.integration.thirdparty.wpsai.resp.WpsAiCommonResp.FailInfo;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/5/20 17:41
 */
@Data
@Accessors(chain = true)
public class CallBackReq {

    /**
     * 接口响应状态码（非HTTP状态码）
     * 0表示成功，1表示部分成功，其他值表示失败或异常
     */
    private Integer code;

    /**
     * 响应提示信息
     */
    private String msg;

    /**
     * 异常错误详情清单列表
     */
    private List<FailInfo> detailList;

}
