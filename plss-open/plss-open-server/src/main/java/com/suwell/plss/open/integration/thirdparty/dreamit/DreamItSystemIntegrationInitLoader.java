package com.suwell.plss.open.integration.thirdparty.dreamit;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.suwell.plss.open.entity.OpAbutmentManufacturer;
import com.suwell.plss.open.integration.enums.Manufacturer;
import com.suwell.plss.open.integration.outline.SystemIntegrationInitLoader;
import com.suwell.plss.open.service.OpAbutmentManufacturerService;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 双杨oa初始配置加载处理
 * @date 2024/4/11, 14:05
 * <AUTHOR>
 */
@Component
public class DreamItSystemIntegrationInitLoader implements SystemIntegrationInitLoader {

    @Resource
    private OpAbutmentManufacturerService abutmentManufacturerService;

    @Resource
    private DreamItConfig dreamItConfig;

    @Override
    public String manufacturer() {
        return Manufacturer.DREAM_IT.getManufacturerCode();
    }


    @Override
    public void loadInitialConfiguration() {
        OpAbutmentManufacturer manufacturer = abutmentManufacturerService.getManufacturerByCode(this.manufacturer());
        if (manufacturer != null) {
            JSONObject basicConditionObj = JSON.parseObject(manufacturer.getBasicCondition());
            String serverHost = manufacturer.getServerHost();
            dreamItConfig.setAppId(basicConditionObj.getString("appId"));
            dreamItConfig.setAppKey(basicConditionObj.getString("appKey"));
            dreamItConfig.setAppTag(basicConditionObj.getString("appTag"));
            dreamItConfig.setState(basicConditionObj.getString("state"));
            dreamItConfig.setHost(serverHost);
            dreamItConfig.setSecretKey(basicConditionObj.getString("secretKey"));
            dreamItConfig.setAuthorizeLoginUrl(basicConditionObj.getString("authorizeLoginUrl"));
        }
    }
}
