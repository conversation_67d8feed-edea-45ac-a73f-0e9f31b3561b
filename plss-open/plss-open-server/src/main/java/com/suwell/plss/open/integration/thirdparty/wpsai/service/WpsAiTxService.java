package com.suwell.plss.open.integration.thirdparty.wpsai.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.suwell.plss.framework.common.utils.CollectionUtil;
import com.suwell.plss.open.entity.OpAppInfo;
import com.suwell.plss.open.entity.OpWpsAiOrg;
import com.suwell.plss.open.entity.OpWpsAiUser;
import com.suwell.plss.open.enums.WpsAiEnums.OperateStatus;
import com.suwell.plss.open.enums.WpsAiEnums.OperateType;
import com.suwell.plss.open.integration.thirdparty.wpsai.req.CallBackReq;
import com.suwell.plss.open.integration.thirdparty.wpsai.resp.WpsAiCommonResp;
import com.suwell.plss.open.service.OpAppInfoService;
import com.suwell.plss.open.service.OpWpsAiOrgService;
import com.suwell.plss.open.service.OpWpsAiUserService;
import com.suwell.plss.open.utils.OkHttpUtils;
import com.suwell.plss.open.utils.OkHttpUtils.ParamForms;
import com.suwell.plss.open.utils.WpsAiUtil;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/5/19 15:41
 */
@Service
@Slf4j
public class WpsAiTxService {

    @Resource
    private OpWpsAiOrgService opWpsAiOrgService;

    @Resource
    private OpWpsAiUserService opWpsAiUserService;

    @Resource
    private OpAppInfoService opAppInfoService;

    public void triggerWpsAiAsyncTask() {
        int pageSize = 100;
        handleOrg(pageSize);
        handleUser(pageSize);
    }

    /**
     * 处理机构
     *
     * @param pageSize
     */
    private void handleOrg(int pageSize) {
        Long minId = 0L;
        int total = 0;
        while (true) {
            List<OpWpsAiOrg> aiOrgList = opWpsAiOrgService.lambdaQuery()
                    .eq(OpWpsAiOrg::getOperateStatus, OperateStatus.PENDING.getCode())
                    .gt(OpWpsAiOrg::getId, minId).orderByAsc(OpWpsAiOrg::getId)
                    .list(new Page<>(1, pageSize));

            int size = aiOrgList.size();
            if (size == 0) {//已至最后一页
                log.info("触发wps ai异步任务-机构数据已处理完成。total={}", total);
                break;
            }

            minId = aiOrgList.get(size - 1).getId();
            total += size;

            Map<Integer, List<OpWpsAiOrg>> map = aiOrgList.stream()
                    .collect(Collectors.groupingBy(OpWpsAiOrg::getOperateType));

            List<OpWpsAiOrg> addList = map.get(OperateType.ADD.getCode());
            if (CollectionUtils.isNotEmpty(addList)) {
                String appId = addList.get(0).getAppId();
                //分批处理
                List<List<OpWpsAiOrg>> lists = CollectionUtil.splitBySize(addList, 20);
                lists.forEach(list -> {
                    WpsAiCommonResp resp = new WpsAiCommonResp();
                    WpsAiUtil.callOrgAddRpc(list, resp, true);
                    opWpsAiOrgService.updateBatchById(list);
                    callback(resp, appId);
                    log.info("触发wps ai异步任务-调用机构新增成功。list={},resp={}", JSON.toJSONString(list),
                            JSON.toJSONString(resp));
                });
            }

            List<OpWpsAiOrg> removeList = map.get(OperateType.DELETE.getCode());
            if (CollectionUtils.isNotEmpty(removeList)) {
                String appId = removeList.get(0).getAppId();
                //分批处理
                List<List<OpWpsAiOrg>> lists = CollectionUtil.splitBySize(removeList, 20);
                lists.forEach(list -> {
                    WpsAiCommonResp resp = new WpsAiCommonResp();
                    WpsAiUtil.callOrgRemoveRpc(list, resp, true);
                    opWpsAiOrgService.updateBatchById(list);
                    callback(resp, appId);
                    log.info("触发wps ai异步任务-调用机构删除成功。list={},resp={}", JSON.toJSONString(list),
                            JSON.toJSONString(resp));
                });

            }

            if (size < pageSize) {//已至最后一页
                log.info("触发wps ai异步任务-机构数据已处理完成。total={}", total);
                break;
            }
        }
    }


    /**
     * 处理用户
     *
     * @param pageSize
     */
    private void handleUser(int pageSize) {
        Long minId = 0L;
        int total = 0;
        while (true) {
            List<OpWpsAiUser> aiUserList = opWpsAiUserService.lambdaQuery()
                    .eq(OpWpsAiUser::getOperateStatus, OperateStatus.PENDING.getCode())
                    .gt(OpWpsAiUser::getId, minId).orderByAsc(OpWpsAiUser::getId)
                    .list(new Page<>(1, pageSize));

            int size = aiUserList.size();
            if (size == 0) {//已至最后一页
                log.info("触发wps ai异步任务-用户数据已处理完成。total={}", total);
                break;
            }

            minId = aiUserList.get(size - 1).getId();
            total += size;

            Map<Integer, List<OpWpsAiUser>> map = aiUserList.stream()
                    .collect(Collectors.groupingBy(OpWpsAiUser::getOperateType));

            List<OpWpsAiUser> addList = map.get(OperateType.ADD.getCode());
            if (CollectionUtils.isNotEmpty(addList)) {
                String appId = addList.get(0).getAppId();
                //分批处理
                List<List<OpWpsAiUser>> lists = CollectionUtil.splitBySize(addList, 20);
                lists.forEach(list -> {
                    WpsAiCommonResp resp = new WpsAiCommonResp();
                    WpsAiUtil.callUserAddRpc(list, resp, true);
                    opWpsAiUserService.updateBatchById(list);
                    callback(resp, appId);
                    log.info("触发wps ai异步任务-调用用户新增成功。list={},resp={}", JSON.toJSONString(list),
                            JSON.toJSONString(resp));
                });
            }

            List<OpWpsAiUser> removeList = map.get(OperateType.DELETE.getCode());
            if (CollectionUtils.isNotEmpty(removeList)) {
                String appId = removeList.get(0).getAppId();
                //分批处理
                List<List<OpWpsAiUser>> lists = CollectionUtil.splitBySize(removeList, 20);
                lists.forEach(list -> {
                    WpsAiCommonResp resp = new WpsAiCommonResp();
                    WpsAiUtil.callUserRemoveRpc(list, resp, true);
                    opWpsAiUserService.updateBatchById(list);
                    callback(resp, appId);
                    log.info("触发wps ai异步任务-调用用户删除成功。list={},resp={}", JSON.toJSONString(list),
                            JSON.toJSONString(resp));
                });
            }

            if (size < pageSize) {//已至最后一页
                log.info("触发wps ai异步任务-用户数据已处理完成。total={}", total);
                break;
            }
        }
    }

    private void callback(WpsAiCommonResp resp, String appId) {
        //无失败不回调
        if (CollectionUtils.isEmpty(resp.getFailInfoList())) {
            return;
        }

        OpAppInfo opAppInfo = opAppInfoService.lambdaQuery().eq(OpAppInfo::getAppId, appId).one();
        CallBackReq req = new CallBackReq();
        req.setCode(resp.getCode()).setMsg(resp.getMsg()).setDetailList(resp.getFailInfoList());

        String respStr;
        try {
            respStr = OkHttpUtils.builder().url(opAppInfo.getCallbackUrl())
                    .setJsonParam(JSON.toJSONString(req))
                    .post(ParamForms.JSON)
                    .sync();
            log.info("触发wps ai异步任务-调用接口成功。url={},req={},respStr={}", opAppInfo.getCallbackUrl(),
                    JSON.toJSONString(req), respStr);
        } catch (Exception e) {
            log.error("触发wps ai异步任务-调用接口失败。url={},req={},em={}", opAppInfo.getCallbackUrl(),
                    JSON.toJSONString(req), e.getMessage(), e);
        }
    }
}
