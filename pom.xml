<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.suwell.plss</groupId>
    <artifactId>plss</artifactId>
<!--    <version>${revision}</version>-->
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>plss</name>
    <url>http://www.suwell.cn</url>
    <description>Privatization LargeLanguageModule Service System 标品</description>

    <modules>
        <module>plss-document-process</module>
        <module>plss-framework</module>
        <module>plss-gateway</module>
        <module>plss-open</module>
        <module>plss-record</module>
        <module>plss-search</module>
        <module>plss-system</module>
<!--        <module>plss-test</module>-->
        <module>plss-plugin</module>
        <module>plss-data-delivery</module>
    </modules>


    <!-- 统一 基础 bom 锁定版本 -->
    <properties>
        <revision>1.0-SNAPSHOT</revision>
        <plss.version>1.0-SNAPSHOT</plss.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <spring-boot.version>3.4.3</spring-boot.version>
        <spring-boot-admin.version>3.4.3</spring-boot-admin.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
<!--        <spring-cloud-alibaba.version>2023.0.3.2</spring-cloud-alibaba.version>-->
        <!-- DB 相关 -->
        <hikari.version>6.0.0</hikari.version>
        <mysql-db.version>8.4.0</mysql-db.version>
        <postgresql.version>42.6.2</postgresql.version>
        <dameng.version>8.1.2.192</dameng.version>
        <kingbase.version>8.6.0</kingbase.version>
        <yashandb.version>1.4.21</yashandb.version>
        <mybatis-plus.version>3.5.10.1</mybatis-plus.version>
        <mybatis-spring.version>3.0.4</mybatis-spring.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <idgenerator.boot.version>1.1.2-RELEASE</idgenerator.boot.version>
        <sharding-jdbc.version>5.2.0</sharding-jdbc.version>
        <snakeyaml.version>2.3</snakeyaml.version>
        <flywaydb.version>9.0.0</flywaydb.version>
        <db-migration-dm.version>1.1.7.1</db-migration-dm.version>
        <!-- Job 定时任务相关 -->
        <xxljob.version>plss-2.4.0-ext.1.0</xxljob.version>
        <reflections.version>0.10.2</reflections.version>
        <!-- 缓存相关 -->
        <akali.version>1.1.4-plss-2.0</akali.version>
        <redisson-boot.version>3.41.0</redisson-boot.version>
        <!-- es相关 -->
        <elasticsearch.version>8.12.2</elasticsearch.version>
        <!-- Config 配置中心相关 -->
        <dynamic.purgeteam.config.version>0.1.0.RELEASE</dynamic.purgeteam.config.version>
        <jasypt.version>3.0.5</jasypt.version>
        <!-- 工具类相关 -->
        <kaptcha.version>2.3.3</kaptcha.version>
        <commons.io.version>2.17.0</commons.io.version>
        <hutool-all.version>5.8.35</hutool-all.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <jmimemagic.version>0.1.5</jmimemagic.version>
        <fastjson.version>2.0.53</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>5.4.0</poi.version>
        <lombok.version>1.18.36</lombok.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <hanlp.version>portable-1.8.4</hanlp.version>
        <aviator.version>5.3.3</aviator.version>
        <httpclient5.version>5.4.3</httpclient5.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <retrofit-boot.version>3.1.3</retrofit-boot.version>
        <dozermapper.version>7.0.0</dozermapper.version>
        <forest.version>1.6.4</forest.version>
        <guava.version>32.0.0-android</guava.version>
        <forest.version>1.6.4</forest.version>
        <okhttp3.version>4.12.0</okhttp3.version>
        <logstash-logback.version>8.1</logstash-logback.version>
        <!-- 著作权 License相关-->
        <slc-client.version>1.2.240929</slc-client.version>
        <maven.compiler.version>3.11.0</maven.compiler.version>

        <!--  begin 后期干掉-->
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>2.2.28</swagger.core.version>
        <druid.version>1.2.23</druid.version>
        <velocity.version>2.3</velocity.version>
        <!--  end 后期干掉-->
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.purgeteam</groupId>
                <artifactId>dynamic-config-spring-boot-starter</artifactId>
                <version>${dynamic.purgeteam.config.version}</version>
            </dependency>

            <!-- Mysql Connector -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-db.version}</version>
            </dependency>

            <!-- postgresql postgresql数据库 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dameng.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yashandb</groupId>
                <artifactId>yashandb-jdbc</artifactId>
                <version>${yashandb.version}</version>
            </dependency>

            <!-- Elasticsearch -->
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.lianjiatech</groupId>
                <artifactId>retrofit-spring-boot-starter</artifactId>
                <version>${retrofit-boot.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jsqlparser</artifactId>
                        <groupId>com.github.jsqlparser</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.pagehelper</groupId>
                        <artifactId>pagehelper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>6.1.0-plss-1.0</version>
            </dependency>

            <!-- mybatis-plus 增强CRUD -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <!--雪花算法配置   -->
            <dependency>
                <groupId>io.github.lmlx66</groupId>
                <artifactId>yitter-idgenerator-spring-boot-starter</artifactId>
                <version>${idgenerator.boot.version}</version>
            </dependency>

            <!-- hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <!--dozer-->
            <dependency>
                <groupId>com.github.dozermapper</groupId>
                <artifactId>dozer-core</artifactId>
                <version>${dozermapper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 日志收集器工具 -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback.version}</version>
            </dependency>

            <!-- 解析文件魔数 mimeType类型库 -->
            <dependency>
                <groupId>net.sf.jmimemagic</groupId>
                <artifactId>jmimemagic</artifactId>
                <version>${jmimemagic.version}</version>
            </dependency>

            <!-- nlp 工具库 -->
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>${hanlp.version}</version>
            </dependency>

            <!-- Java 轻量级表达式规则引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dtflys.forest</groupId>
                <artifactId>forest-spring-boot3-starter</artifactId>
                <version>${forest.version}</version>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-common</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-security</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-datapermission</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-datasource</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- HikariCP -->
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikari.version}</version>
            </dependency>

            <!-- Druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-log</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-redis</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- es 组件 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-es</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- mq 组件 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-mq</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- AI 组件 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-spring-boot-starter-ai</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-system-api</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-document-process-api</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-plugin-api</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-record-api</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-record-standard</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-search-api</artifactId>
                <version>${plss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-search-standard</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.suwell.plss</groupId>
                <artifactId>plss-open-thirdparty</artifactId>
                <version>${plss.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>akali</artifactId>
                <version>${akali.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxljob.version}</version>
            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>

            <!-- ShardingSphere-jdbc  -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>${sharding-jdbc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>calcite-core</artifactId>
                        <groupId>org.apache.calcite</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.yaml</groupId>
                        <artifactId>snakeyaml</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <!-- Dynamic DataSource -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flywaydb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.mengweijin</groupId>
                <artifactId>db-migration-dm</artifactId>
                <version>${db-migration-dm.version}</version>
            </dependency>

            <dependency>
                <groupId>org.owasp.esapi</groupId>
                <artifactId>esapi</artifactId>
                <version>2.6.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.x-file-storage</groupId>
                <artifactId>x-file-storage-spring</artifactId>
<!--                <version>plss-ext.2.3</version>-->
                <version>plss-ext-2.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>


<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>release</id>-->
<!--            <url>http://172.16.14.48:5080/nexus/content/groups/public</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>snapshot</id>-->
<!--            <url>http://172.16.14.48:5080/nexus/content/repositories/snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->

    <distributionManagement>
        <!--<repository>-->
        <!--<id>nexus-release</id>-->
        <!--<url>http://xxxxxx/repository/maven-releases/</url>-->
        <!--</repository>-->
        <snapshotRepository>
            <id>private-repo</id>
            <url>https://mirrors.wps.cn/maven/repository/iciba/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun-nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>

<!--        <repository>-->
<!--            <id>nexus-48</id>-->
<!--            <url>http://172.16.14.48:5080/nexus/content/groups/public</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->

<!--        <repository>-->
<!--            <id>nexus-48-thirdparty</id>-->
<!--            <url>http://172.16.14.48:5080/nexus/content/repositories/thirdparty/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun-nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>