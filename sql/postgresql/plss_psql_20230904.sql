-- Adminer 4.8.1 PostgreSQL 15.3 (Debian 15.3-1.pgdg120+1) dump

CREATE ROLE suwell WITH
	LOGIN
	NOSUPERUSER
	NOCREATEDB
	NOCREATEROLE
	INHERIT
	NOREPLICATION
	CONNECTION LIMIT -1
	PASSWORD 'ptset0$581E@lWVZ';

CREATE DATABASE plss
    WITH
    OWNER = suwell
    ENCODING = 'UTF8'
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;

\connect "plss";

-- \connect "plss";
-- CREATE SCHEMA "plss-record";
-- ALTER SCHEMA "pplss-record" OWNER TO suwell;
CREATE SCHEMA "plss-record" AUTHORIZATION suwell;

CREATE TABLE "plss-record"."rc_document" (
    "id" bigint NOT NULL,
    "record_id" bigint NOT NULL,
    "name" character varying(256) NOT NULL,
    "ctype" smallint NOT NULL,
    "file_size" bigint,
    "file_md5" character varying(256),
    "bucket_name" character varying(256) NOT NULL,
    "object_name" character varying(256) NOT NULL,
    "ofd_file_size" bigint,
    "ofd_file_md5" character varying(256),
    "ofd_bucket_name" character varying(256),
    "ofd_object_name" character varying(256),
    "txt_file_size" bigint,
    "txt_file_md5" character varying(256),
    "txt_bucket_name" character varying(256),
    "txt_object_name" character varying(256),
    "status" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_document_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_document" IS '文档表';

COMMENT ON COLUMN "plss-record"."rc_document"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_document"."record_id" IS '文件id';

COMMENT ON COLUMN "plss-record"."rc_document"."name" IS '文档名称';

COMMENT ON COLUMN "plss-record"."rc_document"."ctype" IS '文档类型(1:主文档, 2:附件)';

COMMENT ON COLUMN "plss-record"."rc_document"."file_size" IS '文档大小';

COMMENT ON COLUMN "plss-record"."rc_document"."file_md5" IS '文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document"."bucket_name" IS '存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document"."object_name" IS '存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document"."ofd_file_size" IS 'ofd文档大小';

COMMENT ON COLUMN "plss-record"."rc_document"."ofd_file_md5" IS 'ofd文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document"."ofd_bucket_name" IS 'ofd存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document"."ofd_object_name" IS 'ofd存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document"."txt_file_size" IS 'txt文档大小';

COMMENT ON COLUMN "plss-record"."rc_document"."txt_file_md5" IS 'txt文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document"."txt_bucket_name" IS 'txt存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document"."txt_object_name" IS 'txt存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_document"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_document"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_document"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_document"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_document_history" (
    "id" bigint NOT NULL,
    "doc_id" bigint NOT NULL,
    "name" character varying(256) NOT NULL,
    "file_size" bigint,
    "file_md5" character varying(256),
    "bucket_name" character varying(256) NOT NULL,
    "object_name" character varying(256) NOT NULL,
    "ofd_file_size" bigint,
    "ofd_file_md5" character varying(256),
    "ofd_bucket_name" character varying(256),
    "ofd_object_name" character varying(256),
    "txt_file_size" bigint,
    "txt_file_md5" character varying(256),
    "txt_bucket_name" character varying(256),
    "txt_object_name" character varying(256),
    "status" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_document_history_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_document_history" IS '文档表';

COMMENT ON COLUMN "plss-record"."rc_document_history"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_document_history"."doc_id" IS '文件id';

COMMENT ON COLUMN "plss-record"."rc_document_history"."name" IS '文档名称';

COMMENT ON COLUMN "plss-record"."rc_document_history"."file_size" IS '文档大小';

COMMENT ON COLUMN "plss-record"."rc_document_history"."file_md5" IS '文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document_history"."bucket_name" IS '存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document_history"."object_name" IS '存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document_history"."ofd_file_size" IS 'ofd文档大小';

COMMENT ON COLUMN "plss-record"."rc_document_history"."ofd_file_md5" IS 'ofd文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document_history"."ofd_bucket_name" IS 'ofd存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document_history"."ofd_object_name" IS 'ofd存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document_history"."txt_file_size" IS 'txt文档大小';

COMMENT ON COLUMN "plss-record"."rc_document_history"."txt_file_md5" IS 'txt文档MD5值';

COMMENT ON COLUMN "plss-record"."rc_document_history"."txt_bucket_name" IS 'txt存储桶名称';

COMMENT ON COLUMN "plss-record"."rc_document_history"."txt_object_name" IS 'txt存储对象名';

COMMENT ON COLUMN "plss-record"."rc_document_history"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_document_history"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_document_history"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_document_history"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_document_history"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_folder" (
    "id" bigint NOT NULL,
    "repo_id" bigint,
    "name" character varying(256) NOT NULL,
    "status" smallint NOT NULL,
    "image_url" character varying(1024),
    "visit_type" smallint NOT NULL,
    "org_id" bigint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_folder_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_folder" IS '目录表';

COMMENT ON COLUMN "plss-record"."rc_folder"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_folder"."repo_id" IS '库id';

COMMENT ON COLUMN "plss-record"."rc_folder"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_folder"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_folder"."image_url" IS '图标url';

COMMENT ON COLUMN "plss-record"."rc_folder"."visit_type" IS '访问类型(1:public, 2:private, 3:protect)';

COMMENT ON COLUMN "plss-record"."rc_folder"."org_id" IS '组织机构id';

COMMENT ON COLUMN "plss-record"."rc_folder"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_folder"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_folder"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_folder"."modified_by" IS '修改人';



CREATE TABLE "plss-record"."rc_folder_record" (
    "folder_id" bigint NOT NULL,
    "record_id" bigint NOT NULL,
    CONSTRAINT "rc_fold_record_pkey" PRIMARY KEY ("folder_id", "record_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_folder_record" IS '目录-文件关联表';

COMMENT ON COLUMN "plss-record"."rc_folder_record"."folder_id" IS '目录id';

COMMENT ON COLUMN "plss-record"."rc_folder_record"."record_id" IS '文件id';



CREATE TABLE "plss-record"."rc_folder_rel" (
    "ancestor_id" bigint NOT NULL,
    "descendant_id" bigint NOT NULL,
    "distance" integer NOT NULL,
    CONSTRAINT "rc_fold_rel_pkey" PRIMARY KEY ("ancestor_id", "descendant_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_folder_rel" IS '目录-目录关联表';

COMMENT ON COLUMN "plss-record"."rc_folder_rel"."ancestor_id" IS '父辈id';

COMMENT ON COLUMN "plss-record"."rc_folder_rel"."descendant_id" IS '后代id';

COMMENT ON COLUMN "plss-record"."rc_folder_rel"."distance" IS '层级步数';



CREATE TABLE "plss-record"."rc_metadata" (
    "id" bigint NOT NULL,
    "name" character varying(64) NOT NULL,
    "name_en" character varying(64),
    "definition" character varying(128),
    "value_type" smallint,
    "value_range" character varying(256),
    "short_name" character varying(32),
    "comment" character varying(128),
    "search_flag" smallint,
    "status" smallint NOT NULL,
    "orderby" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_metadata_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_metadata" IS '元数据项表';

COMMENT ON COLUMN "plss-record"."rc_metadata"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_metadata"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_metadata"."name_en" IS '英文名';

COMMENT ON COLUMN "plss-record"."rc_metadata"."definition" IS '定义';

COMMENT ON COLUMN "plss-record"."rc_metadata"."value_type" IS '值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)';

COMMENT ON COLUMN "plss-record"."rc_metadata"."value_range" IS '值域';

COMMENT ON COLUMN "plss-record"."rc_metadata"."short_name" IS '短名';

COMMENT ON COLUMN "plss-record"."rc_metadata"."comment" IS '注解';

COMMENT ON COLUMN "plss-record"."rc_metadata"."search_flag" IS '是否可作为检索条件(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_metadata"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_metadata"."orderby" IS '排序码';

COMMENT ON COLUMN "plss-record"."rc_metadata"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_metadata"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_metadata"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_metadata"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_metadata_category" (
    "id" bigint NOT NULL,
    "name" character varying(64) NOT NULL,
    "status" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_metadata_category_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_metadata_category" IS '元数据分类表';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_metadata_category"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_metadata_category_metadata" (
    "category_id" bigint NOT NULL,
    "md_id" bigint NOT NULL,
    CONSTRAINT "rc_metadata_category_metadata_pkey" PRIMARY KEY ("category_id", "md_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_metadata_category_metadata" IS '元数据分类-元数据关联表';

COMMENT ON COLUMN "plss-record"."rc_metadata_category_metadata"."category_id" IS '元数据分类id';

COMMENT ON COLUMN "plss-record"."rc_metadata_category_metadata"."md_id" IS '元数据id';


CREATE TABLE "plss-record"."rc_metadata_value" (
    "id" bigint NOT NULL,
    "md_id" bigint NOT NULL,
    "md_value" character varying(512),
    "create_time" timestamp(6) NOT NULL,
    "modified_time" timestamp(6) NOT NULL,
    "record_id" bigint NOT NULL,
    CONSTRAINT "rc_metadata_keyvalue_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_metadata_value" IS '元数据键值对表';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."md_id" IS '元数据项id';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."md_value" IS '元数据值';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_metadata_value"."record_id" IS '文件id';


CREATE TABLE "plss-record"."rc_permission" (
    "id" bigint NOT NULL,
    "name" character varying(64) NOT NULL,
    "ctype" smallint NOT NULL,
    "pvalue" integer NOT NULL,
    "memo" character varying(64),
    CONSTRAINT "rc_permission_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_permission" IS '权限表';

COMMENT ON COLUMN "plss-record"."rc_permission"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_permission"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_permission"."ctype" IS '权限类型(1:库，2:目录，3:文件)';

COMMENT ON COLUMN "plss-record"."rc_permission"."pvalue" IS '权限值(2的n次方 )';

COMMENT ON COLUMN "plss-record"."rc_permission"."memo" IS '备注';

INSERT INTO "plss-record"."rc_permission" ("id", "name", "ctype", "pvalue", "memo") VALUES
(4,	'查阅(库)',	1,	8,	'查阅(库)'),
(5,	'权限设置(目录)',	2,	1,	'权限设置(目录)'),
(6,	'查阅(目录)',	2,	2,	'查阅(目录)'),
(7,	'权限设置(文件)',	3,	1,	'权限设置(文件)'),
(8,	'查看',	3,	2,	'查看'),
(9,	'仅看标题',	3,	4,	'仅看标题'),
(10,	'复制内容',	3,	8,	'复制内容'),
(11,	'打印',	3,	16,	'打印'),
(12,	'下载/另存',	3,	32,	'下载/另存'),
(13,	'评论',	3,	64,	'评论'),
(14,	'分享',	3,	128,	'分享'),
(3,	'库新增(删除)文件',	1,	4,	'库新增(删除)文件'),
(2,	'库新增(删除)目录',	1,	2,	'库新增(删除)目录'),
(1,	'权限设置(库)',	1,	1,	'权限设置(库)');

CREATE TABLE "plss-record"."rc_permission_group" (
    "id" bigint NOT NULL,
    "name" character varying(64) NOT NULL,
    "ctype" smallint NOT NULL,
    "pvalue" integer NOT NULL,
    "memo" character varying(64),
    CONSTRAINT "rc_permission_group_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_permission_group" IS '权限组表';

COMMENT ON COLUMN "plss-record"."rc_permission_group"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_permission_group"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_permission_group"."ctype" IS '权限类型(1:库, 2:目录, 3:文件)';

COMMENT ON COLUMN "plss-record"."rc_permission_group"."pvalue" IS '权限值组合';

COMMENT ON COLUMN "plss-record"."rc_permission_group"."memo" IS '备注';


CREATE TABLE "plss-record"."rc_record" (
    "id" bigint NOT NULL,
    "recordtype_id" bigint,
    "name" character varying(256) NOT NULL,
    "title" character varying(512),
    "digest" character varying(1024),
    "status" smallint NOT NULL,
    "origin" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_record_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_record" IS '文件表(文档合集)';

COMMENT ON COLUMN "plss-record"."rc_record"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_record"."recordtype_id" IS '文件类型id';

COMMENT ON COLUMN "plss-record"."rc_record"."name" IS '文件名称';

COMMENT ON COLUMN "plss-record"."rc_record"."title" IS '标题';

COMMENT ON COLUMN "plss-record"."rc_record"."digest" IS '摘要';

COMMENT ON COLUMN "plss-record"."rc_record"."status" IS '状态(1:启用, 2:禁用3:元数据待检测,4:元数据检测异常,5:元数据检测通过)';

COMMENT ON COLUMN "plss-record"."rc_record"."origin" IS '来源:接入方标识';

COMMENT ON COLUMN "plss-record"."rc_record"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_record"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_record"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_record_rel" (
    "refer_id" bigint NOT NULL,
    "record_id" bigint NOT NULL,
    "ctype" smallint NOT NULL,
    "distance" integer NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_record_rel_pkey" PRIMARY KEY ("refer_id", "record_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_record_rel" IS '文件-文件关联表';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."refer_id" IS '引用文件id';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."record_id" IS '文件id';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."ctype" IS '引用类型(1:依托,  2:沿革, 3:参考)';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."distance" IS '层级步数';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_record_rel"."modified_by" IS '修改人';



CREATE TABLE "plss-record"."rc_record_type" (
    "id" bigint NOT NULL,
    "name" character varying(64) NOT NULL,
    "status" smallint NOT NULL,
    "create_time" timestamp NOT NULL,
    "create_by" bigint NOT NULL,
    "modified_time" timestamp NOT NULL,
    "modified_by" bigint NOT NULL,
    CONSTRAINT "rc_record_type_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_record_type" IS '文件类型表';

COMMENT ON COLUMN "plss-record"."rc_record_type"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_record_type"."name" IS '名称';

COMMENT ON COLUMN "plss-record"."rc_record_type"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_record_type"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_record_type"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_record_type"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_record_type"."modified_by" IS '修改人';


CREATE TABLE "plss-record"."rc_record_type_metadata" (
    "recordtype_id" bigint NOT NULL,
    "md_id" bigint NOT NULL,
    "value_type" smallint,
    "value_range" character varying(256),
    "search_flag" smallint,
    CONSTRAINT "rc_rdcate_metadata_pkey" PRIMARY KEY ("recordtype_id", "md_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_record_type_metadata" IS '文件类型-元数据表';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."recordtype_id" IS '文件类型id';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."md_id" IS '元数据id';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."value_type" IS '值类型(1:字符, 2:日期, 3:日期时间, 4:日期范围, 5:数值, 6:列表, 7:boolean)';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."value_range" IS '值域';

COMMENT ON COLUMN "plss-record"."rc_record_type_metadata"."search_flag" IS '是否可作为检索条件(1:启用, 2:禁用)';


CREATE TABLE "plss-record"."rc_repo_metadata" (
    "id" bigint NOT NULL,
    "repo_id" bigint NOT NULL,
    "md_id" bigint NOT NULL,
    "md_op" character varying(16) NOT NULL,
    "md_value" character varying(512) NOT NULL,
    "create_time" timestamp NOT NULL,
    "modified_time" timestamp NOT NULL,
    CONSTRAINT "rc_repo_metadata_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_repo_metadata" IS '库-元数据项值表';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."repo_id" IS '库id';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."md_id" IS '元数据项id';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."md_op" IS '元数据操作符';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."md_value" IS '元数据值';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_repo_metadata"."modified_time" IS '修改时间';


CREATE TABLE "plss-record"."rc_repo_record" (
    "repo_id" bigint NOT NULL,
    "record_id" bigint NOT NULL,
    CONSTRAINT "rc_repo_record_pkey" PRIMARY KEY ("repo_id", "record_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_repo_record" IS '库-文件关联表';

COMMENT ON COLUMN "plss-record"."rc_repo_record"."repo_id" IS '库id';

COMMENT ON COLUMN "plss-record"."rc_repo_record"."record_id" IS '文件id';


CREATE TABLE "plss-record"."rc_repository" (
    "id" bigint NOT NULL,
    "name" character varying(256) NOT NULL,
    "ctype" smallint NOT NULL,
    "share_type" smallint,
    "status" smallint,
    "image_url" character varying(1024),
    "create_time" timestamp,
    "create_by" bigint,
    "modified_time" timestamp,
    "modified_by" bigint,
    "owner_id" bigint,
    CONSTRAINT "rc_repository_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_repository" IS '库';

COMMENT ON COLUMN "plss-record"."rc_repository"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_repository"."name" IS '库名称';

COMMENT ON COLUMN "plss-record"."rc_repository"."ctype" IS '库类型(1:内部库, 2:公共库，3:个人库)';

COMMENT ON COLUMN "plss-record"."rc_repository"."share_type" IS '共享类型(1:无条件共享, 2:有条件共享, 3:不共享)';

COMMENT ON COLUMN "plss-record"."rc_repository"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-record"."rc_repository"."image_url" IS '图标url';

COMMENT ON COLUMN "plss-record"."rc_repository"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-record"."rc_repository"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-record"."rc_repository"."modified_time" IS '修改时间';

COMMENT ON COLUMN "plss-record"."rc_repository"."modified_by" IS '修改人';

COMMENT ON COLUMN "plss-record"."rc_repository"."owner_id" IS '所有者id';


CREATE TABLE "plss-record"."rc_resource_permission" (
    "id" bigint NOT NULL,
    "visitor_id" bigint NOT NULL,
    "vtype" smallint NOT NULL,
    "resource_id" bigint NOT NULL,
    "rtype" smallint NOT NULL,
    "pvalue" integer NOT NULL,
    CONSTRAINT "rc_resource_permission_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "unique_v_r_index" UNIQUE ("visitor_id", "resource_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-record"."rc_resource_permission" IS '库-目录-文件权限表';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."visitor_id" IS '访问者id';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."vtype" IS '访问者类型(1:组织机构, 2:用户)';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."resource_id" IS '资源id (库-目录-文件)';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."rtype" IS '资源类型(1:库, 2:目录, 3:文件)';

COMMENT ON COLUMN "plss-record"."rc_resource_permission"."pvalue" IS '权限值';


-- \connect "plss";
-- CREATE SCHEMA "plss-system";
-- ALTER SCHEMA "pplss-system" OWNER TO suwell;
CREATE SCHEMA "plss-system" AUTHORIZATION suwell;

CREATE TABLE "plss-system"."sys_category" (
    "id" bigint NOT NULL,
    "name" character varying(64) DEFAULT '' NOT NULL,
    "status" smallint NOT NULL,
    "order_by" smallint NOT NULL,
    "create_time" timestamp(6) DEFAULT now() NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp(6) DEFAULT now() NOT NULL,
    "ctype" smallint NOT NULL,
    "remark" character varying,
    "visit_type" smallint NOT NULL,
    "org_id" bigint NOT NULL,
    "image_url" character varying,
    CONSTRAINT "sys_category_pkey" PRIMARY KEY ("id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_category" IS '分类表';

COMMENT ON COLUMN "plss-system"."sys_category"."id" IS '唯一标识';

COMMENT ON COLUMN "plss-system"."sys_category"."name" IS '名称';

COMMENT ON COLUMN "plss-system"."sys_category"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-system"."sys_category"."order_by" IS '排序码';

COMMENT ON COLUMN "plss-system"."sys_category"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_category"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-system"."sys_category"."update_by" IS '更新人';

COMMENT ON COLUMN "plss-system"."sys_category"."update_time" IS '修改时间';

COMMENT ON COLUMN "plss-system"."sys_category"."ctype" IS '分类(0:文件, 1:库)';

COMMENT ON COLUMN "plss-system"."sys_category"."remark" IS '备注';

COMMENT ON COLUMN "plss-system"."sys_category"."visit_type" IS '访问类型(1:public, 2:private, 3:protect)';

COMMENT ON COLUMN "plss-system"."sys_category"."org_id" IS '组织机构id';

COMMENT ON COLUMN "plss-system"."sys_category"."image_url" IS '图标url';


CREATE TABLE "plss-system"."sys_category_relation" (
    "descendant_id" bigint NOT NULL,
    "ancestor_id" bigint DEFAULT '0' NOT NULL,
    "distance" smallint NOT NULL
) WITH (oids = false);

COMMENT ON COLUMN "plss-system"."sys_category_relation"."descendant_id" IS '后代id';

COMMENT ON COLUMN "plss-system"."sys_category_relation"."ancestor_id" IS '父级id';

COMMENT ON COLUMN "plss-system"."sys_category_relation"."distance" IS '层级步数';


CREATE TABLE "plss-system"."sys_config" (
    "config_id" bigint NOT NULL,
    "config_name" character varying(128) DEFAULT '' NOT NULL,
    "config_key" character varying(128) DEFAULT '' NOT NULL,
    "config_value" character varying(512) DEFAULT '' NOT NULL,
    "config_type" character(1) DEFAULT 'N' NOT NULL,
    "status" smallint DEFAULT '1' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    CONSTRAINT "sys_config_pkey" PRIMARY KEY ("config_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_config" IS '参数配置表';

COMMENT ON COLUMN "plss-system"."sys_config"."config_id" IS '参数主键';

COMMENT ON COLUMN "plss-system"."sys_config"."config_name" IS '参数名称';

COMMENT ON COLUMN "plss-system"."sys_config"."config_key" IS '参数键名';

COMMENT ON COLUMN "plss-system"."sys_config"."config_value" IS '参数键值';

COMMENT ON COLUMN "plss-system"."sys_config"."config_type" IS '系统内置（Y是 N否）';

COMMENT ON COLUMN "plss-system"."sys_config"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-system"."sys_config"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_config"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-system"."sys_config"."update_time" IS '修改时间';

COMMENT ON COLUMN "plss-system"."sys_config"."update_by" IS '修改人';

COMMENT ON COLUMN "plss-system"."sys_config"."remark" IS '备注';

INSERT INTO "plss-system"."sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES
(1,	'主框架页-默认皮肤样式名称',	'sys.index.skinName',	'skin-blue',	'Y',	1,	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:08:06.219514',	'1',	'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),
(2,	'用户管理-账号初始密码',	'sys.user.initPassword',	'123456',	'Y',	1,	'2023-06-29 09:09:22.145263',	'1',	'2023-06-29 09:09:22.145263',	'1',	'初始化密码 123456'),
(3,	'主框架页-侧边栏主题',	'sys.index.sideTheme',	'theme-dark',	'Y',	1,	'2023-06-29 09:10:39.412813',	'1',	'2023-06-29 09:10:39.412813',	'1',	'深色主题theme-dark，浅色主题theme-light'),
(4,	'账号自助-是否开启用户注册功能',	'sys.account.registerUser',	'false',	'Y',	1,	'2023-06-29 09:11:04.595302',	'1',	'2023-06-29 09:11:04.595302',	'1',	'是否开启注册用户功能（true开启，false关闭）'),
(5,	'用户登录-黑名单列表',	'sys.login.blackIPList',	'',	'Y',	1,	'2023-06-29 09:11:27.340841',	'1',	'2023-06-29 09:11:27.340841',	'1',	'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

CREATE TABLE "plss-system"."sys_dict_data" (
    "dict_code" bigint NOT NULL,
    "dict_sort" integer DEFAULT '0' NOT NULL,
    "dict_label" character varying(128) DEFAULT '' NOT NULL,
    "dict_value" character varying(128) DEFAULT '' NOT NULL,
    "dict_type" character varying(128) DEFAULT '' NOT NULL,
    "css_class" character varying(128) DEFAULT '' NOT NULL,
    "list_class" character varying(128) DEFAULT '' NOT NULL,
    "is_default" character(1) DEFAULT 'N' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    CONSTRAINT "sys_dict_data_pkey" PRIMARY KEY ("dict_code")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_dict_data" IS '字典数据表';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."dict_code" IS '字典编码';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."dict_sort" IS '字典排序';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."dict_label" IS '字典标签';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."dict_value" IS '字典键值';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."dict_type" IS '字典类型';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."css_class" IS '样式属性（其他样式扩展）';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."list_class" IS '表格回显样式';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."is_default" IS '是否默认（Y是 N否）';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_dict_data"."remark" IS '备注';

INSERT INTO "plss-system"."sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class", "list_class", "is_default", "status", "create_by", "create_time", "update_by", "update_time", "remark") VALUES
(21,	3,	'删除',	'3',	'sys_oper_type',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.716879',	'删除操作'),
(22,	4,	'授权',	'4',	'sys_oper_type',	'',	'primary',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.865783',	'授权操作'),
(23,	5,	'导出',	'5',	'sys_oper_type',	'',	'warning',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.985232',	'导出操作'),
(24,	6,	'导入',	'6',	'sys_oper_type',	'',	'warning',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.114574',	'导入操作'),
(1,	1,	'男',	'0',	'sys_user_sex',	'',	'',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:43.474487',	'性别男'),
(2,	2,	'女',	'1',	'sys_user_sex',	'',	'',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:43.561878',	'性别女'),
(3,	3,	'未知',	'2',	'sys_user_sex',	'',	'',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:43.674115',	'性别未知'),
(4,	1,	'显示',	'0',	'sys_show_hide',	'',	'primary',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:43.849125',	'显示菜单'),
(5,	2,	'隐藏',	'1',	'sys_show_hide',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:44.062991',	'隐藏菜单'),
(6,	1,	'正常',	'0',	'sys_normal_disable',	'',	'primary',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:44.310683',	'正常状态'),
(7,	2,	'停用',	'1',	'sys_normal_disable',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:44.496599',	'停用状态'),
(8,	1,	'正常',	'0',	'sys_job_status',	'',	'primary',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:44.690362',	'正常状态'),
(9,	2,	'暂停',	'1',	'sys_job_status',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:44.865613',	'停用状态'),
(10,	1,	'默认',	'DEFAULT',	'sys_job_group',	'',	'',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.050818',	'默认分组'),
(11,	2,	'系统',	'SYSTEM',	'sys_job_group',	'',	'',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.163988',	'系统分组'),
(12,	1,	'是',	'Y',	'sys_yes_no',	'',	'primary',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.250223',	'系统默认是'),
(13,	2,	'否',	'N',	'sys_yes_no',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.275146',	'系统默认否'),
(14,	1,	'通知',	'1',	'sys_notice_type',	'',	'warning',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.279961',	'通知'),
(15,	2,	'公告',	'2',	'sys_notice_type',	'',	'success',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.287256',	'公告'),
(16,	1,	'正常',	'0',	'sys_notice_status',	'',	'primary',	'Y',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.294071',	'正常状态'),
(17,	2,	'关闭',	'1',	'sys_notice_status',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.381523',	'关闭状态'),
(18,	99,	'其他',	'0',	'sys_oper_type',	'',	'info',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.473397',	'其他操作'),
(19,	1,	'新增',	'1',	'sys_oper_type',	'',	'info',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.539621',	'新增操作'),
(20,	2,	'修改',	'2',	'sys_oper_type',	'',	'info',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:45.627596',	'修改操作'),
(25,	7,	'强退',	'7',	'sys_oper_type',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.22073',	'强退操作'),
(26,	8,	'生成代码',	'8',	'sys_oper_type',	'',	'warning',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.333155',	'生成操作'),
(27,	9,	'清空数据',	'9',	'sys_oper_type',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.401502',	'清空操作'),
(28,	1,	'成功',	'0',	'sys_common_status',	'',	'primary',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.487357',	'正常状态'),
(29,	2,	'失败',	'1',	'sys_common_status',	'',	'danger',	'N',	'0',	'1',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:43:46.564897',	'停用状态');

CREATE TABLE "plss-system"."sys_dict_type" (
    "dict_id" bigint NOT NULL,
    "dict_name" character varying(128) DEFAULT '' NOT NULL,
    "dict_type" character varying(128) DEFAULT '' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    CONSTRAINT "rc_metadata_category_pkey" UNIQUE ("dict_id"),
    CONSTRAINT "sys_dict_type_pkey" PRIMARY KEY ("dict_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_dict_type" IS '字典类型表';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."dict_id" IS '字典主键';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."dict_name" IS '字典名称';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."dict_type" IS '字典类型';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."create_by" IS '创建人';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."update_time" IS '修改时间';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."update_by" IS '修改人';

COMMENT ON COLUMN "plss-system"."sys_dict_type"."remark" IS '备注';

INSERT INTO "plss-system"."sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_time", "create_by", "update_time", "update_by", "remark") VALUES
(1,	'用户性别',	'sys_user_sex',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:34.506973',	'1',	'用户性别列表'),
(2,	'菜单状态',	'sys_show_hide',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:34.81077',	'1',	'菜单状态列表'),
(3,	'系统开关',	'sys_normal_disable',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:34.952499',	'1',	'系统开关列表'),
(4,	'任务状态',	'sys_job_status',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:35.208334',	'1',	'任务状态列表'),
(5,	'任务分组',	'sys_job_group',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:35.593448',	'1',	'任务分组列表'),
(6,	'系统是否',	'sys_yes_no',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:36.032991',	'1',	'系统是否列表'),
(7,	'通知类型',	'sys_notice_type',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:36.393123',	'1',	'通知类型列表'),
(8,	'通知状态',	'sys_notice_status',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:36.786381',	'1',	'通知状态列表'),
(9,	'操作类型',	'sys_oper_type',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:36.830708',	'1',	'操作类型列表'),
(10,	'系统状态',	'sys_common_status',	'0',	'2023-05-19 00:40:19',	'1',	'2023-06-29 09:48:37.033331',	'1',	'登录状态列表'),
(6333676916613,	'test_test',	'test',	'1',	'2023-07-27 16:57:30.856582',	'1',	'2023-07-27 16:58:11.621963',	'1',	'test');

CREATE TABLE "plss-system"."sys_logininfor" (
    "info_id" bigint NOT NULL,
    "user_name" character varying(64) DEFAULT '' NOT NULL,
    "ipaddr" character varying(128) DEFAULT '' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "msg" character varying(256) DEFAULT '' NOT NULL,
    "access_time" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "sys_logininfor_pkey" PRIMARY KEY ("info_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_logininfor" IS '系统访问记录';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."info_id" IS '访问ID';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."user_name" IS '用户账号';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."ipaddr" IS '登录IP地址';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."status" IS '状态(1:成功，2:失败)';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."msg" IS '提示信息';

COMMENT ON COLUMN "plss-system"."sys_logininfor"."access_time" IS '访问时间';


CREATE TABLE "plss-system"."sys_menu" (
    "menu_id" bigint NOT NULL,
    "parent_id" bigint DEFAULT '0' NOT NULL,
    "menu_name" character varying(64) DEFAULT '' NOT NULL,
    "order_num" integer DEFAULT '0' NOT NULL,
    "path" character varying(256) DEFAULT '' NOT NULL,
    "component" character varying(256) DEFAULT '' NOT NULL,
    "query" character varying(256) DEFAULT '' NOT NULL,
    "is_frame" bpchar DEFAULT '1' NOT NULL,
    "is_cache" smallint DEFAULT '1' NOT NULL,
    "menu_type" character(1) DEFAULT '' NOT NULL,
    "visible" character(1) DEFAULT '1' NOT NULL,
    "status" character(1) DEFAULT '1' NOT NULL,
    "perms" character varying(128) DEFAULT '' NOT NULL,
    "icon" character varying(128) DEFAULT '#' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    CONSTRAINT "sys_menu_pkey" PRIMARY KEY ("menu_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_menu" IS '菜单表';

COMMENT ON COLUMN "plss-system"."sys_menu"."menu_id" IS '菜单ID';

COMMENT ON COLUMN "plss-system"."sys_menu"."parent_id" IS '父菜单ID';

COMMENT ON COLUMN "plss-system"."sys_menu"."menu_name" IS '菜单名称';

COMMENT ON COLUMN "plss-system"."sys_menu"."order_num" IS '显示顺序';

COMMENT ON COLUMN "plss-system"."sys_menu"."path" IS '路由地址';

COMMENT ON COLUMN "plss-system"."sys_menu"."component" IS '组件路径';

COMMENT ON COLUMN "plss-system"."sys_menu"."query" IS '路由参数';

COMMENT ON COLUMN "plss-system"."sys_menu"."is_frame" IS '是否为外链（1是 2否）';

COMMENT ON COLUMN "plss-system"."sys_menu"."is_cache" IS '是否缓存（1缓存 2不缓存）';

COMMENT ON COLUMN "plss-system"."sys_menu"."menu_type" IS '菜单类型（M目录 C菜单 F按钮）';

COMMENT ON COLUMN "plss-system"."sys_menu"."visible" IS '菜单状态（1显示 2隐藏）';

COMMENT ON COLUMN "plss-system"."sys_menu"."status" IS '状态(1:启用, 2:禁用)';

COMMENT ON COLUMN "plss-system"."sys_menu"."perms" IS '权限标识';

COMMENT ON COLUMN "plss-system"."sys_menu"."icon" IS '菜单图标';

COMMENT ON COLUMN "plss-system"."sys_menu"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_menu"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_menu"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_menu"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_menu"."remark" IS '备注';

INSERT INTO "plss-system"."sys_menu" ("menu_id", "parent_id", "menu_name", "order_num", "path", "component", "query", "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by", "create_time", "update_by", "update_time", "remark") VALUES
(103,	1,	'机构管理',	4,	'org',	'dept/index',	'',	'1',	1,	'C',	'1',	'1',	'system:org:list',	'tree',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.063104',	'机构管理菜单'),
(2,	0,	'系统监控',	2,	'monitor',	'',	'',	'1',	1,	'M',	'1',	'1',	'',	'monitor',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:20.943021',	'系统监控目录'),
(3,	0,	'系统工具',	3,	'tool',	'',	'',	'1',	1,	'M',	'1',	'1',	'',	'tool',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:20.978839',	'系统工具目录'),
(102,	1,	'菜单管理',	3,	'menu',	'system/menu/index',	'',	'1',	1,	'C',	'1',	'1',	'system:menu:list',	'tree-table',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.036283',	'菜单管理菜单'),
(104,	1,	'岗位管理',	5,	'post',	'system/post/index',	'',	'1',	1,	'C',	'1',	'1',	'system:post:list',	'post',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.091519',	'岗位管理菜单'),
(105,	1,	'字典管理',	6,	'dict',	'system/dict/index',	'',	'1',	1,	'C',	'1',	'1',	'system:dict:list',	'dict',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.097229',	'字典管理菜单'),
(106,	1,	'参数设置',	7,	'config',	'system/config/index',	'',	'1',	1,	'C',	'1',	'1',	'system:config:list',	'edit',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.101259',	'参数设置菜单'),
(107,	1,	'通知公告',	8,	'notice',	'system/notice/index',	'',	'1',	1,	'C',	'1',	'1',	'system:notice:list',	'message',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.10623',	'通知公告菜单'),
(108,	1,	'日志管理',	9,	'log',	'',	'',	'1',	1,	'M',	'1',	'1',	'',	'log',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.131944',	'日志管理菜单'),
(109,	2,	'在线用户',	1,	'online',	'monitor/online/index',	'',	'1',	1,	'C',	'1',	'1',	'monitor:online:list',	'online',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.171421',	'在线用户菜单'),
(110,	2,	'定时任务',	2,	'job',	'monitor/job/index',	'',	'1',	1,	'C',	'1',	'1',	'monitor:job:list',	'job',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.18151',	'定时任务菜单'),
(111,	2,	'Sentinel控制台',	3,	'http://localhost:8718',	'',	'',	'0',	1,	'C',	'1',	'1',	'monitor:sentinel:list',	'sentinel',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.185077',	'流量控制菜单'),
(112,	2,	'Nacos控制台',	4,	'http://localhost:8848/nacos',	'',	'',	'0',	1,	'C',	'1',	'1',	'monitor:nacos:list',	'nacos',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.194557',	'服务治理菜单'),
(113,	2,	'Admin控制台',	5,	'http://localhost:9100/login',	'',	'',	'0',	1,	'C',	'1',	'1',	'monitor:server:list',	'server',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.198827',	'服务监控菜单'),
(114,	3,	'表单构建',	1,	'build',	'tool/build/index',	'',	'1',	1,	'C',	'1',	'1',	'tool:build:list',	'build',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.202163',	'表单构建菜单'),
(115,	3,	'代码生成',	2,	'gen',	'tool/gen/index',	'',	'1',	1,	'C',	'1',	'1',	'tool:gen:list',	'code',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.205947',	'代码生成菜单'),
(116,	3,	'系统接口',	3,	'http://localhost:8080/swagger-ui/index.html',	'',	'',	'0',	1,	'C',	'1',	'1',	'tool:swagger:list',	'swagger',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.235662',	'系统接口菜单'),
(500,	108,	'操作日志',	1,	'operlog',	'system/operlog/index',	'',	'1',	1,	'C',	'1',	'1',	'system:operlog:list',	'form',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.243315',	'操作日志菜单'),
(501,	108,	'登录日志',	2,	'logininfor',	'system/logininfor/index',	'',	'1',	1,	'C',	'1',	'1',	'system:logininfor:list',	'logininfor',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.271248',	'登录日志菜单'),
(1000,	100,	'用户查询',	1,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.277174',	''),
(1001,	100,	'用户新增',	2,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.283739',	''),
(1002,	100,	'用户修改',	3,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.319563',	''),
(1003,	100,	'用户删除',	4,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.355527',	''),
(1004,	100,	'用户导出',	5,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.397637',	''),
(1005,	100,	'用户导入',	6,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:import',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.427137',	''),
(1006,	100,	'重置密码',	7,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:user:resetPwd',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.432262',	''),
(1007,	101,	'角色查询',	1,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:role:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.43813',	''),
(1008,	101,	'角色新增',	2,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:role:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.473992',	''),
(1009,	101,	'角色修改',	3,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:role:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.48241',	''),
(1010,	101,	'角色删除',	4,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:role:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.498642',	''),
(1011,	101,	'角色导出',	5,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:role:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.507445',	''),
(1012,	102,	'菜单查询',	1,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:menu:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.543383',	''),
(1013,	102,	'菜单新增',	2,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:menu:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.549503',	''),
(1014,	102,	'菜单修改',	3,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:menu:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.590168',	''),
(1015,	102,	'菜单删除',	4,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:menu:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.594636',	''),
(1016,	103,	'机构查询',	1,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:org:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.598418',	''),
(1017,	103,	'机构新增',	2,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:org:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.657007',	''),
(1018,	103,	'机构修改',	3,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:org:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.702775',	''),
(1019,	103,	'机构删除',	4,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:org:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.758327',	''),
(1020,	104,	'岗位查询',	1,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:post:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.849082',	''),
(1021,	104,	'岗位新增',	2,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:post:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.913009',	''),
(1022,	104,	'岗位修改',	3,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:post:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.965194',	''),
(1023,	104,	'岗位删除',	4,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:post:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.98428',	''),
(1024,	104,	'岗位导出',	5,	'',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:post:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.988342',	''),
(1025,	105,	'字典查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:dict:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.040195',	''),
(1026,	105,	'字典新增',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:dict:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.059841',	''),
(1027,	105,	'字典修改',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:dict:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.108843',	''),
(1028,	105,	'字典删除',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:dict:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.112625',	''),
(1046,	109,	'在线查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:online:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.458785',	''),
(1047,	109,	'批量强退',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:online:batchLogout',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.473126',	''),
(100,	1,	'用户管理',	1,	'user',	'users/user/index',	'',	'1',	1,	'C',	'1',	'1',	'system:user:list',	'user',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:20.986552',	'用户管理菜单'),
(4,	0,	'数科网维官网',	4,	'https://suwell.com',	'',	'',	'0',	1,	'M',	'1',	'1',	'',	'guide',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:20.982685',	'数科网维官网地址'),
(1,	0,	'系统管理',	1,	'system',	'',	'',	'1',	1,	'M',	'1',	'1',	'',	'system',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:20.934434',	'系统管理目录'),
(101,	1,	'角色管理',	2,	'role',	'system/role/index',	'',	'1',	1,	'C',	'1',	'1',	'system:role:list',	'peoples',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:21.020267',	'角色管理菜单'),
(1029,	105,	'字典导出',	5,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:dict:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.152578',	''),
(1030,	106,	'参数查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:config:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.209568',	''),
(1031,	106,	'参数新增',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:config:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.224037',	''),
(1032,	106,	'参数修改',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:config:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.234329',	''),
(1033,	106,	'参数删除',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:config:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.238205',	''),
(1034,	106,	'参数导出',	5,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:config:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.243622',	''),
(1035,	107,	'公告查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:notice:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.25333',	''),
(1036,	107,	'公告新增',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:notice:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.26173',	''),
(1037,	107,	'公告修改',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:notice:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.29981',	''),
(1038,	107,	'公告删除',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:notice:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.308303',	''),
(1039,	500,	'操作查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:operlog:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.314056',	''),
(1040,	500,	'操作删除',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:operlog:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.321029',	''),
(1041,	500,	'日志导出',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:operlog:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.325248',	''),
(1042,	501,	'登录查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:logininfor:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.33034',	''),
(1043,	501,	'登录删除',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:logininfor:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.334269',	''),
(1044,	501,	'日志导出',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:logininfor:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.39025',	''),
(1045,	501,	'账户解锁',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'system:logininfor:unlock',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.441006',	''),
(1048,	109,	'单条强退',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:online:forceLogout',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.479516',	''),
(1049,	110,	'任务查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.52517',	''),
(1050,	110,	'任务新增',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:add',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.544218',	''),
(1051,	110,	'任务修改',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.551547',	''),
(1052,	110,	'任务删除',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.611891',	''),
(1053,	110,	'状态修改',	5,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:changeStatus',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.658509',	''),
(1054,	110,	'任务导出',	6,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'monitor:job:export',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.67755',	''),
(1055,	115,	'生成查询',	1,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'tool:gen:query',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.682254',	''),
(1056,	115,	'生成修改',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'tool:gen:edit',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.692121',	''),
(1057,	115,	'生成删除',	3,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'tool:gen:remove',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.737997',	''),
(1058,	115,	'导入代码',	2,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'tool:gen:import',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.745524',	''),
(1059,	115,	'预览代码',	4,	'#',	'',	'',	'1',	1,	'F',	'1',	'1',	'tool:gen:preview',	'#',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:02:22.752576',	'');

CREATE TABLE "plss-system"."sys_notice" (
    "notice_id" bigint NOT NULL,
    "notice_title" character varying(64) DEFAULT '' NOT NULL,
    "notice_type" character(1) DEFAULT '1' NOT NULL,
    "notice_content" character varying(1024) DEFAULT '' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(256) DEFAULT '' NOT NULL,
    CONSTRAINT "sys_notice_pkey" PRIMARY KEY ("notice_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_notice" IS '通知公告表';

COMMENT ON COLUMN "plss-system"."sys_notice"."notice_id" IS '公告ID';

COMMENT ON COLUMN "plss-system"."sys_notice"."notice_title" IS '公告标题';

COMMENT ON COLUMN "plss-system"."sys_notice"."notice_type" IS '公告类型（1通知 2公告）';

COMMENT ON COLUMN "plss-system"."sys_notice"."notice_content" IS '公告内容';

COMMENT ON COLUMN "plss-system"."sys_notice"."status" IS '公告状态（1正常，2:关闭 ）';

COMMENT ON COLUMN "plss-system"."sys_notice"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_notice"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_notice"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_notice"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_notice"."remark" IS '备注';


CREATE TABLE "plss-system"."sys_oper_log" (
    "oper_id" bigint NOT NULL,
    "title" character varying(64) DEFAULT '' NOT NULL,
    "business_type" smallint DEFAULT '0' NOT NULL,
    "method" character varying(128) DEFAULT '' NOT NULL,
    "request_method" character varying(16) DEFAULT '' NOT NULL,
    "operator_type" smallint DEFAULT '0' NOT NULL,
    "oper_name" character varying(64) DEFAULT '' NOT NULL,
    "org_name" character varying(64) DEFAULT '' NOT NULL,
    "oper_url" character varying(256) DEFAULT '' NOT NULL,
    "oper_ip" character varying(128) DEFAULT '' NOT NULL,
    "oper_location" character varying(256) DEFAULT '' NOT NULL,
    "oper_param" character varying(2048) DEFAULT '' NOT NULL,
    "json_result" character varying(2048) DEFAULT '' NOT NULL,
    "status" smallint DEFAULT '0' NOT NULL,
    "error_msg" character varying(2048) DEFAULT '' NOT NULL,
    "oper_time" timestamp DEFAULT now() NOT NULL,
    "cost_time" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "sys_oper_log_pkey" PRIMARY KEY ("oper_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_oper_log" IS '操作日志记录';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_id" IS '日志主键';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."title" IS '模块标题';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."business_type" IS '业务类型（0其它 1新增 2修改 3删除）';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."method" IS '方法名称';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."request_method" IS '请求方式';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."operator_type" IS '操作类别（0其它 1后台用户 2手机端用户）';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_name" IS '操作人员';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."org_name" IS '机构名称';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_url" IS '请求URL';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_ip" IS '主机地址';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_location" IS '操作地点';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_param" IS '请求参数';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."json_result" IS '返回参数';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."status" IS '操作状态（1：正常，2：异常 ）';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."error_msg" IS '错误消息';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."oper_time" IS '操作时间';

COMMENT ON COLUMN "plss-system"."sys_oper_log"."cost_time" IS '消耗时间';


CREATE TABLE "plss-system"."sys_org" (
    "org_id" bigint NOT NULL,
    "parent_id" bigint DEFAULT '0' NOT NULL,
    "ancestors" character varying(1024) DEFAULT '' NOT NULL,
    "org_name" character varying(64) DEFAULT '' NOT NULL,
    "order_num" integer DEFAULT '0' NOT NULL,
    "saying" character varying(64) DEFAULT '' NOT NULL,
    "phone" character varying(11) DEFAULT '' NOT NULL,
    "email" character varying(64) DEFAULT '' NOT NULL,
    "status" character(1) DEFAULT '1' NOT NULL,
    "del_flag" character(1) DEFAULT '1' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying,
    "org_type" bpchar,
    "org_code" character varying,
    CONSTRAINT "sys_org_pkey" PRIMARY KEY ("org_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_org" IS '机构表';

COMMENT ON COLUMN "plss-system"."sys_org"."org_id" IS '机构id';

COMMENT ON COLUMN "plss-system"."sys_org"."parent_id" IS '父机构id';

COMMENT ON COLUMN "plss-system"."sys_org"."ancestors" IS '祖级列表';

COMMENT ON COLUMN "plss-system"."sys_org"."org_name" IS '机构名称';

COMMENT ON COLUMN "plss-system"."sys_org"."order_num" IS '显示顺序';

COMMENT ON COLUMN "plss-system"."sys_org"."saying" IS '负责人';

COMMENT ON COLUMN "plss-system"."sys_org"."phone" IS '联系电话';

COMMENT ON COLUMN "plss-system"."sys_org"."email" IS '邮箱';

COMMENT ON COLUMN "plss-system"."sys_org"."status" IS '机构状态（1：正常，2：停用）';

COMMENT ON COLUMN "plss-system"."sys_org"."del_flag" IS '删除标志（1：代表存在，2：代表删除）';

COMMENT ON COLUMN "plss-system"."sys_org"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_org"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_org"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_org"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_org"."org_type" IS '组织机构类型，1-单位，2-部门';

COMMENT ON COLUMN "plss-system"."sys_org"."org_code" IS '导入组织机构时的组织机构编码';


CREATE TABLE "plss-system"."sys_post" (
    "post_id" bigint NOT NULL,
    "post_code" character varying(64) DEFAULT '' NOT NULL,
    "post_name" character varying(64) DEFAULT '' NOT NULL,
    "post_sort" integer DEFAULT '0' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    CONSTRAINT "sys_post_pkey" PRIMARY KEY ("post_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_post" IS '岗位信息表';

COMMENT ON COLUMN "plss-system"."sys_post"."post_id" IS '岗位ID';

COMMENT ON COLUMN "plss-system"."sys_post"."post_code" IS '岗位编码';

COMMENT ON COLUMN "plss-system"."sys_post"."post_name" IS '岗位名称';

COMMENT ON COLUMN "plss-system"."sys_post"."post_sort" IS '显示顺序';

COMMENT ON COLUMN "plss-system"."sys_post"."status" IS '状态（ 1：正常，2：停用）';

COMMENT ON COLUMN "plss-system"."sys_post"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_post"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_post"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_post"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_post"."remark" IS '备注';


CREATE TABLE "plss-system"."sys_role" (
    "role_id" bigint NOT NULL,
    "role_name" character varying(36) DEFAULT '' NOT NULL,
    "role_key" character varying(128) DEFAULT '' NOT NULL,
    "role_sort" integer DEFAULT '0' NOT NULL,
    "data_scope" character(1) DEFAULT '1' NOT NULL,
    "menu_check_strictly" smallint DEFAULT '1' NOT NULL,
    "org_check_strictly" smallint DEFAULT '1' NOT NULL,
    "status" character(1) DEFAULT '0' NOT NULL,
    "del_flag" character(1) DEFAULT '0' NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(512) DEFAULT '',
    CONSTRAINT "sys_role_pkey" PRIMARY KEY ("role_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_role" IS '角色表';

COMMENT ON COLUMN "plss-system"."sys_role"."role_id" IS '角色ID';

COMMENT ON COLUMN "plss-system"."sys_role"."role_name" IS '角色名称';

COMMENT ON COLUMN "plss-system"."sys_role"."role_key" IS '角色权限字符串';

COMMENT ON COLUMN "plss-system"."sys_role"."role_sort" IS '显示顺序';

COMMENT ON COLUMN "plss-system"."sys_role"."data_scope" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';

COMMENT ON COLUMN "plss-system"."sys_role"."menu_check_strictly" IS '菜单树选择项是否关联显示';

COMMENT ON COLUMN "plss-system"."sys_role"."org_check_strictly" IS '机构树选择项是否关联显示';

COMMENT ON COLUMN "plss-system"."sys_role"."status" IS '角色状态（1：正常， 2：停用）';

COMMENT ON COLUMN "plss-system"."sys_role"."del_flag" IS '删除标志（ 1代表存在，2代表删除）';

COMMENT ON COLUMN "plss-system"."sys_role"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_role"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_role"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_role"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_role"."remark" IS '备注';

INSERT INTO "plss-system"."sys_role" ("role_id", "role_name", "role_key", "role_sort", "data_scope", "menu_check_strictly", "org_check_strictly", "status", "del_flag", "create_by", "create_time", "update_by", "update_time", "remark") VALUES
(1,	'超级管理员',	'admin',	1,	'1',	1,	1,	'0',	'0',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:15:16.94377',	'超级管理员'),
(2,	'普通角色',	'common',	2,	'2',	1,	1,	'0',	'0',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:15:17.005945',	'普通角色');

CREATE TABLE "plss-system"."sys_role_menu" (
    "role_id" bigint NOT NULL,
    "menu_id" bigint NOT NULL,
    CONSTRAINT "sys_role_menu_pkey" PRIMARY KEY ("role_id", "menu_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_role_menu" IS '角色-菜单关联表';

COMMENT ON COLUMN "plss-system"."sys_role_menu"."role_id" IS '角色id';

COMMENT ON COLUMN "plss-system"."sys_role_menu"."menu_id" IS '菜单id';

INSERT INTO "plss-system"."sys_role_menu" ("role_id", "menu_id") VALUES
(2,	1),
(2,	2),
(2,	3),
(2,	4),
(2,	100),
(2,	101),
(2,	102),
(2,	103),
(2,	104),
(2,	105),
(2,	106),
(2,	107),
(2,	108),
(2,	109),
(2,	110),
(2,	111),
(2,	112),
(2,	113),
(2,	114),
(2,	115),
(2,	116),
(2,	500),
(2,	501),
(2,	1000),
(2,	1001),
(2,	1002),
(2,	1003),
(2,	1004),
(2,	1005),
(2,	1006),
(2,	1007),
(2,	1008),
(2,	1009),
(2,	1010),
(2,	1011),
(2,	1012),
(2,	1013),
(2,	1014),
(2,	1015),
(2,	1016),
(2,	1017),
(2,	1018),
(2,	1019),
(2,	1020),
(2,	1021),
(2,	1022),
(2,	1023),
(2,	1024),
(2,	1025),
(2,	1026),
(2,	1027),
(2,	1028),
(2,	1029),
(2,	1030),
(2,	1031),
(2,	1032),
(2,	1033),
(2,	1034),
(2,	1035),
(2,	1036),
(2,	1037),
(2,	1038),
(2,	1039),
(2,	1040),
(2,	1041),
(2,	1042),
(2,	1043),
(2,	1044),
(2,	1045),
(2,	1046),
(2,	1047),
(2,	1048),
(2,	1049),
(2,	1050),
(2,	1051),
(2,	1052),
(2,	1053),
(2,	1054),
(2,	1055),
(2,	1056),
(2,	1057),
(2,	1058),
(2,	1059),
(2,	1060);

CREATE TABLE "plss-system"."sys_role_org" (
    "role_id" bigint NOT NULL,
    "org_id" bigint NOT NULL,
    CONSTRAINT "sys_role_org_pkey" PRIMARY KEY ("role_id", "org_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_role_org" IS '角色和机构关联表';

COMMENT ON COLUMN "plss-system"."sys_role_org"."role_id" IS '角色ID';

COMMENT ON COLUMN "plss-system"."sys_role_org"."org_id" IS '机构ID';


CREATE TABLE "plss-system"."sys_user" (
    "user_id" bigint NOT NULL,
    "user_name" character varying(64) NOT NULL,
    "nick_name" character varying(64) DEFAULT '' NOT NULL,
    "user_type" character varying(4) DEFAULT '00' NOT NULL,
    "email" character varying(64) DEFAULT '' NOT NULL,
    "phonenumber" character varying(11) DEFAULT '' NOT NULL,
    "sex" character(1) DEFAULT '1' NOT NULL,
    "avatar" character varying(128) DEFAULT '' NOT NULL,
    "password" character varying(128) DEFAULT '' NOT NULL,
    "status" character(1) DEFAULT '1' NOT NULL,
    "del_flag" character(1) DEFAULT '1' NOT NULL,
    "login_ip" character varying(128) DEFAULT '' NOT NULL,
    "login_date" timestamp DEFAULT now() NOT NULL,
    "create_by" character varying DEFAULT '' NOT NULL,
    "create_time" timestamp DEFAULT now() NOT NULL,
    "update_by" character varying DEFAULT '' NOT NULL,
    "update_time" timestamp DEFAULT now() NOT NULL,
    "remark" character varying(512) DEFAULT '' NOT NULL,
    "org_id" bigint DEFAULT '0' NOT NULL,
    "pwd_update_time" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "sys_user_pkey" PRIMARY KEY ("user_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_user" IS '用户表';

COMMENT ON COLUMN "plss-system"."sys_user"."user_id" IS '用户ID';

COMMENT ON COLUMN "plss-system"."sys_user"."user_name" IS '用户账号';

COMMENT ON COLUMN "plss-system"."sys_user"."nick_name" IS '用户昵称';

COMMENT ON COLUMN "plss-system"."sys_user"."user_type" IS '用户类型（00系统用户）';

COMMENT ON COLUMN "plss-system"."sys_user"."email" IS '用户邮箱';

COMMENT ON COLUMN "plss-system"."sys_user"."phonenumber" IS '手机号码';

COMMENT ON COLUMN "plss-system"."sys_user"."sex" IS '用户性别（1男 2女 3未知）';

COMMENT ON COLUMN "plss-system"."sys_user"."avatar" IS '头像地址';

COMMENT ON COLUMN "plss-system"."sys_user"."password" IS '密码';

COMMENT ON COLUMN "plss-system"."sys_user"."status" IS '帐号状态（ 1：正常，2：停用）';

COMMENT ON COLUMN "plss-system"."sys_user"."del_flag" IS '删除标志（1：代表存在，2：代表删除）';

COMMENT ON COLUMN "plss-system"."sys_user"."login_ip" IS '最后登录IP';

COMMENT ON COLUMN "plss-system"."sys_user"."login_date" IS '最后登录时间';

COMMENT ON COLUMN "plss-system"."sys_user"."create_by" IS '创建者';

COMMENT ON COLUMN "plss-system"."sys_user"."create_time" IS '创建时间';

COMMENT ON COLUMN "plss-system"."sys_user"."update_by" IS '更新者';

COMMENT ON COLUMN "plss-system"."sys_user"."update_time" IS '更新时间';

COMMENT ON COLUMN "plss-system"."sys_user"."remark" IS '备注';

COMMENT ON COLUMN "plss-system"."sys_user"."org_id" IS '机构ID';

COMMENT ON COLUMN "plss-system"."sys_user"."pwd_update_time" IS '密码修改时间';

INSERT INTO "plss-system"."sys_user" ("user_id", "user_name", "nick_name", "user_type", "email", "phonenumber", "sex", "avatar", "password", "status", "del_flag", "login_ip", "login_date", "create_by", "create_time", "update_by", "update_time", "remark", "org_id", "pwd_update_time") VALUES
(1,	'admin',	'系统管理员',	'00',	'<EMAIL>',	'15888888888',	'1',	'',	'$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2',	'1',	'1',	'127.0.0.1',	'2023-05-19 00:40:18',	'1',	'2023-05-19 00:40:18',	'1',	'2023-06-29 10:23:17.20224',	'管理员',	103,	'2023-08-24 10:11:38.9558');

CREATE TABLE "plss-system"."sys_user_org" (
    "org_id" bigint NOT NULL,
    "user_id" bigint NOT NULL,
    CONSTRAINT "rc_repo_fold_pkey" UNIQUE ("user_id", "org_id"),
    CONSTRAINT "sys_user_org_pkey" PRIMARY KEY ("org_id", "user_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_user_org" IS '用户-机构关联表';

COMMENT ON COLUMN "plss-system"."sys_user_org"."org_id" IS '组织机构id';

COMMENT ON COLUMN "plss-system"."sys_user_org"."user_id" IS '用户id';


CREATE TABLE "plss-system"."sys_user_post" (
    "user_id" bigint NOT NULL,
    "post_id" bigint NOT NULL,
    CONSTRAINT "sys_user_post_pkey" PRIMARY KEY ("user_id", "post_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_user_post" IS '用户与岗位关联表';

COMMENT ON COLUMN "plss-system"."sys_user_post"."user_id" IS '用户ID';

COMMENT ON COLUMN "plss-system"."sys_user_post"."post_id" IS '岗位ID';


CREATE TABLE "plss-system"."sys_user_role" (
    "role_id" bigint NOT NULL,
    "user_id" bigint NOT NULL,
    CONSTRAINT "sys_user_role_pkey" PRIMARY KEY ("role_id", "user_id")
) WITH (oids = false);

COMMENT ON TABLE "plss-system"."sys_user_role" IS '用户-角色关联表';

COMMENT ON COLUMN "plss-system"."sys_user_role"."role_id" IS '角色id';

COMMENT ON COLUMN "plss-system"."sys_user_role"."user_id" IS '用户id';

INSERT INTO "plss-system"."sys_user_role" ("role_id", "user_id") VALUES
(1,	1);

commit;