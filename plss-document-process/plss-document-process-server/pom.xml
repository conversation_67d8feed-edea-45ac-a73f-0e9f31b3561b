<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.suwell.plss</groupId>
        <artifactId>plss-document-process</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>plss-document-process-server</artifactId>
    <description>
        plss-document-process-server 文件处理服务
    </description>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-core</artifactId>
            <version>2.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-undertow</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!--            <exclusions>-->
            <!--                <exclusion>-->
            <!--                    <groupId>org.springframework.boot</groupId>-->
            <!--                    <artifactId>spring-boot-starter-tomcat</artifactId>-->
            <!--                </exclusion>-->
            <!--            </exclusions>-->
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!--         SpringBoot 动态配置变更通知事件 -->
        <dependency>
            <groupId>com.purgeteam</groupId>
            <artifactId>dynamic-config-spring-boot-starter</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- postgresql 数据库 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!--        dm数据库-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yashandb</groupId>
            <artifactId>yashandb-jdbc</artifactId>
        </dependency>

        <!-- plss Common DataScope -->

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-datapermission</artifactId>
        </dependency>

        <!-- plss Common Security -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- plss Common Log -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-log</artifactId>
        </dependency>

        <!-- plss Common mq -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- plss Common es -->
        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-es</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>2.0.1</version>
        </dependency>


        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-document-process-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.5.RELEASE</version>
        </dependency>

        <!--   转换服务引用的jar包  start        -->
        <dependency>
            <groupId>com.suwell.library.custom</groupId>
            <artifactId>suwell-packet-wrapper</artifactId>
            <version>1.21.240423</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.suwell.library.custom</groupId>
            <artifactId>suwell-agent-wrapper</artifactId>
            <version>1.6.220830</version>
            <exclusions>
                <exclusion>
                    <groupId>com.suwell.library.custom</groupId>
                    <artifactId>suwell-packet-wrapper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.suwell.library.custom</groupId>
            <artifactId>agent-boot</artifactId>
            <version>1.21.220309</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.suwell.library.custom</groupId>-->
        <!--            <artifactId>suwell-packet-wrapper</artifactId>-->
        <!--            <version>1.21.220831</version>-->
        <!--            <type>pom</type>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.suwell.plugin</groupId>
            <artifactId>ofd-simple-tool</artifactId>
            <version>1.0</version>
        </dependency>
        <!--   转换服务引用的jar包  end        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>
        <!--poi-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.1</version>
        </dependency>

        <!-- nlp 工具库 -->
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.owasp.esapi</groupId>
            <artifactId>esapi</artifactId>
        </dependency>

        <!-- Java 轻量级表达式规则引擎-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-record-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.suwell.plss</groupId>
            <artifactId>plss-spring-boot-starter-ai</artifactId>
        </dependency>

        <!-- api5引用jar start -->
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-config</artifactId>
            <version>1.8.220714</version>
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-native-base</artifactId>
            <version>1.14.220531</version>
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-rpc-base</artifactId>
            <version>1.33.241119</version>
            <scope>system</scope>
            <!--            <systemPath>${project.basedir}/src/main/lib/suwell-rpc-base-1.31.240111-t.jar-->
            <!--            </systemPath>-->
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-rpc-base</artifactId>
            <!--            <version>1.31.230629</version>-->
            <version>1.33.241119</version>
            <exclusions>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-rpc-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-native-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-rpc-pipe</artifactId>
            <version>1.1.230713</version>
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-sdk-base</artifactId>
            <version>1.2.220922</version>
            <exclusions>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-rpc-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>suwell-sdk-standard</artifactId>
            <version>1.0.0.230531</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-rpc-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.suwell.library</groupId>
                    <artifactId>suwell-sdk-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- api5引用jar end -->
        <!-- update begin  20241018 扫描依赖漏洞 -->
        <!--        <dependency>-->
        <!--            <groupId>commons-collections</groupId>-->
        <!--            <artifactId>commons-collections</artifactId>-->
        <!--            <version>3.2.2</version>-->
        <!--        </dependency>-->
        <!-- update end  20241018 扫描依赖漏洞 -->

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <!-- api5落盘文件加密 -->
        <dependency>
            <groupId>com.suwell.library</groupId>
            <artifactId>safe-stream</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 日志收集器 -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <!-- 设定主资源目录  -->
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <excludes>
                    <exclude>**/*.yaml</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <targetPath>BOOT-INF/lib/</targetPath>
                <directory>${pom.basedir}/src/main/lib/</directory>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>