package com.suwell.plss.document.process.convert;

import static com.suwell.plss.document.process.feature.handler.ai.FeatureAiService.AI_MIDDLE_CLASSIFIED;
import static com.suwell.plss.document.process.feature.handler.ai.FeatureAiService.AI_MIDDLE_ENCRYPT_FIELDS;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.framework.es.entity.NlpRecord.MetadataIdValue;
import com.suwell.plss.framework.es.entity.NlpRecord.RepoFolder;
import com.suwell.plss.framework.mq.dto.base.MqMetadataIdValueDTO;
import com.suwell.plss.framework.mq.dto.base.MqRecordInfoDTO;
import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO;
import com.suwell.plss.framework.mq.dto.base.MqRecordPathDTO.RepoFolderRel;
import com.suwell.plss.framework.mq.dto.base.MqRecordPermDTO;
import com.suwell.plss.record.standard.enums.RecordEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
public class NlpRecordConvertUtils {

    // 保存附件信息
    public static NlpRecord buildAttNlpRecord(NlpRecord masterRecord, MqRecordInfoDTO attMqRecordInfoDTO) {
        // 新增附件信息 主键 recordId+docId
        NlpRecord attRecord = new NlpRecord();
        attRecord.setUid(attMqRecordInfoDTO.getRecordId().toString() + attMqRecordInfoDTO.getDocId().toString());
        attRecord.setRecordId(attMqRecordInfoDTO.getRecordId());
        attRecord.setDocId(attMqRecordInfoDTO.getDocId());
        attRecord.setFileType(attMqRecordInfoDTO.getFileType());
        attRecord.setRecordName(attMqRecordInfoDTO.getRecordName());
        attRecord.setTitle(attMqRecordInfoDTO.getTitle());
        attRecord.setFileMd5(attMqRecordInfoDTO.getFileMd5());
        attRecord.setSuffix(attMqRecordInfoDTO.getSuffix());
        attRecord.setRecordCreateTime(attMqRecordInfoDTO.getRecordCreateTime());
        attRecord.setRecordUpdateTime(attMqRecordInfoDTO.getRecordUpdateTime());
        return setAttNlpRecordMasterInfo(masterRecord, attRecord);
    }

    /**
     * 主件相关信息同步附件上,更新附件信息
     * @param masterRecord
     * @param attRecord
     * @return
     */
    public static NlpRecord buildUpdateAttNlpRecord(NlpRecord masterRecord, MqRecordInfoDTO attRecord) {
        // 新增附件信息 主键 recordId+docId
        NlpRecord attUpdateRecord = new NlpRecord();
        attUpdateRecord.setUid(attRecord.getRecordId().toString() + attRecord.getDocId().toString());
        attUpdateRecord.setRecordId(attRecord.getRecordId());
        attUpdateRecord.setDocId(attRecord.getDocId());
        attUpdateRecord.setFileType(attRecord.getFileType());
        attUpdateRecord.setRecordName(attRecord.getRecordName());
        attUpdateRecord.setTitle(attRecord.getTitle());
        attUpdateRecord.setFileMd5(attRecord.getFileMd5());
        attUpdateRecord.setSuffix(attRecord.getSuffix());
        attUpdateRecord.setRecordCreateTime(attRecord.getRecordCreateTime());
        attUpdateRecord.setRecordUpdateTime(attRecord.getRecordUpdateTime());
        return setAttNlpRecordMasterInfo(masterRecord, attUpdateRecord);
    }


    /**
     * 主件相关信息同步附件上,更新附件信息
     * @param masterRecord
     * @param attRecord
     * @return
     */
    public static NlpRecord buildUpdateAttNlpRecord(NlpRecord masterRecord, NlpRecord attRecord) {
        // 新增附件信息 主键 recordId+docId
        NlpRecord attUpdateRecord = new NlpRecord();
        attUpdateRecord.setUid(attRecord.getRecordId().toString() + attRecord.getDocId().toString());
        attUpdateRecord.setRecordId(attRecord.getRecordId());
        attUpdateRecord.setDocId(attRecord.getDocId());
        attUpdateRecord.setFileType(attRecord.getFileType());
        attUpdateRecord.setRecordName(attRecord.getRecordName());
        attUpdateRecord.setTitle(attRecord.getTitle());
        attUpdateRecord.setFileMd5(attRecord.getFileMd5());
        attUpdateRecord.setSuffix(attRecord.getSuffix());
        attUpdateRecord.setRecordCreateTime(attRecord.getRecordCreateTime());
        attUpdateRecord.setRecordUpdateTime(attRecord.getRecordUpdateTime());
        return setAttNlpRecordMasterInfo(masterRecord, attUpdateRecord);
    }
    /**
     * 构建ai中台的nlpRecord
     *
     * @param mqRecordInfoDTO
     * @return
     */
    public static NlpRecord mqRecordInfoDTO2AiPlatformNlpRecord(MqRecordInfoDTO mqRecordInfoDTO) {
        NlpRecord nlpRecord = new NlpRecord();
        nlpRecord.setRecordId(mqRecordInfoDTO.getRecordId());
        nlpRecord.setRecordPic(mqRecordInfoDTO.getRecordPic());
        nlpRecord.setSplitId(-1L);
        nlpRecord.setRecordName(mqRecordInfoDTO.getRecordName());
        nlpRecord.setTitle(mqRecordInfoDTO.getTitle());
        nlpRecord.setDigest(mqRecordInfoDTO.getDigest());
        nlpRecord.setCategoryIds(mqRecordInfoDTO.getCategoryIds());

//        nlpRecord.setContent("");
//        nlpRecord.setRecordStatus(0);
        nlpRecord.setDocId(mqRecordInfoDTO.getDocId());
        nlpRecord.setRealOrigin(mqRecordInfoDTO.getRealOrigin());
        nlpRecord.setFileType(mqRecordInfoDTO.getFileType());
        nlpRecord.setFileMd5(mqRecordInfoDTO.getFileMd5());
        nlpRecord.setUserIds(mqRecordInfoDTO.getUserIds());
        nlpRecord.setCategoryLeafIds(mqRecordInfoDTO.getCategoryLeafIds());

        nlpRecord.setRecordTypeId(mqRecordInfoDTO.getRecordTypeId());
        nlpRecord.setOrigin(mqRecordInfoDTO.getOrigin());
        nlpRecord.setRecordCreateTime(mqRecordInfoDTO.getRecordCreateTime());
        nlpRecord.setRecordUpdateTime(mqRecordInfoDTO.getRecordUpdateTime());
        nlpRecord.setOrgIds(mqRecordInfoDTO.getOrgIds());
        nlpRecord.setTenantId(mqRecordInfoDTO.getTenantId());
        nlpRecord.setReleaseTime(mqRecordInfoDTO.getReleaseTime());
//        nlpRecord.setVisitNum(0L);
//        nlpRecord.setCreateTime(0L);
//        nlpRecord.setUpdateTime(0L);

        // 元数据
        List<MqMetadataIdValueDTO> metadataIdValueDTOList = mqRecordInfoDTO.getMetadataIdValueDTOList();
        if (CollUtil.isNotEmpty(metadataIdValueDTOList)) {
            List<MetadataIdValue> metadataIdValueList = metadataIdValueDTOList.stream().map(x -> {
                MetadataIdValue metadataIdValue = new MetadataIdValue();
                metadataIdValue.setMetadataId(x.getMetadataId());
                metadataIdValue.setMetadataName(x.getMetadataName());
                metadataIdValue.setMetadataValue(x.getMetadataValue());
                metadataIdValue.setMetadataValueType(x.getMetadataValueType());
                metadataIdValue.setMetadataValues(CollUtil.isNotEmpty(x.getMetadataValues())
                        ? Lists.newArrayList(x.getMetadataValues()) : Lists.newArrayList());
                metadataIdValue.setMetadataValueList(x.getMetadataValueList());
                return metadataIdValue;
            }).toList();
            nlpRecord.setMetadataIdValue(metadataIdValueList);
        }
        // 库目录
        nlpRecord.setRepoFolders(NlpRecordConvertUtils.buildRepoFolders(mqRecordInfoDTO.getMqRecordPathDTOList()));

        // 权限
        MqRecordPermDTO recordPermDTO = NlpRecordConvertUtils.buildRecordPerms(mqRecordInfoDTO.getMqRecordPermDTOList());
        if (Objects.nonNull(recordPermDTO)) {
            nlpRecord.setVisitType(recordPermDTO.getVisitType());
            nlpRecord.setTextVisitorIds(recordPermDTO.getTextVisitorIds());
            nlpRecord.setVisitorIds(recordPermDTO.getVisitorIds());
        }

        nlpRecord.setOrderBy(mqRecordInfoDTO.getOrderBy());
        nlpRecord.setStoreProcess(mqRecordInfoDTO.getStoreProcess());
        nlpRecord.setSuffix(mqRecordInfoDTO.getSuffix());
        return nlpRecord;
    }

    /**
     * 构建同步到AI中台的参数
     * @param indexName
     * @param nlpRecord
     * @param updateRepoFolder
     * @param classified
     * @param encryptFields
     * @return
     */
    public static JSONObject buildNlpRecord2AiPlatformParams(String indexName,NlpRecord nlpRecord, boolean updateRepoFolder,
            Integer classified,  List<String> encryptFields) {
        Long recordId = nlpRecord.getRecordId();
        JSONObject params = new JSONObject();
        params.put("indexName", indexName);
        Map<String, Object> extension = new HashMap<>();
        if (Objects.nonNull(classified)) {
            extension.put(AI_MIDDLE_CLASSIFIED, classified);
            extension.put(AI_MIDDLE_ENCRYPT_FIELDS, encryptFields);
        }
        extension.put("recordId", recordId);
        if (Objects.nonNull(nlpRecord.getOrigin())) {
            extension.put("origin", nlpRecord.getOrigin());
        }
        if (Objects.nonNull(nlpRecord.getDocId())) {
            extension.put("docId", nlpRecord.getDocId());
        }
        if (Objects.nonNull(nlpRecord.getRealOrigin())) {
            extension.put("realOrigin", nlpRecord.getRealOrigin());
        }
        if (nlpRecord.getCategoryLeafIds() != null) {
            extension.put("categoryLeafIds", nlpRecord.getCategoryLeafIds());
        }
        if (Objects.nonNull(nlpRecord.getFileType())) {
            extension.put("fileType", nlpRecord.getFileType());
        }
        if (StringUtils.isNotBlank(nlpRecord.getFileMd5())) {
            extension.put("fileMd5", nlpRecord.getFileMd5());
        }
        if (Objects.nonNull(nlpRecord.getRecordTypeId())) {
            extension.put("recordTypeId", nlpRecord.getRecordTypeId());
        }
        if (Objects.nonNull(nlpRecord.getRecordCreateTime())) {
            extension.put("recordCreateTime", nlpRecord.getRecordCreateTime());
        }
        if (Objects.nonNull(nlpRecord.getRecordUpdateTime())) {
            extension.put("recordUpdateTime", nlpRecord.getRecordUpdateTime());
        }
        if (Objects.nonNull(nlpRecord.getOrderBy())) {
            extension.put("orderBy", nlpRecord.getOrderBy());
        }
        if (StringUtils.isNotBlank(nlpRecord.getSuffix())) {
            extension.put("suffix", nlpRecord.getSuffix());
        }
        if (Objects.nonNull(nlpRecord.getStoreProcess())) {
            extension.put("storeProcess", nlpRecord.getStoreProcess());
        }
        if (nlpRecord.getOrgIds() != null){
            extension.put("orgIds", nlpRecord.getOrgIds());
        }
        if (nlpRecord.getTitle() != null) {
            extension.put("title", nlpRecord.getTitle());
        }
        if (nlpRecord.getDigest() != null) {
            extension.put("summary", nlpRecord.getDigest());
        }
        extension.put("releaseTime", Objects.nonNull(nlpRecord.getReleaseTime()) ? nlpRecord.getReleaseTime() : 0);
        if (nlpRecord.getTenantId() != null) {
            extension.put("tenantId", nlpRecord.getTenantId());
        }
        if (nlpRecord.getCategoryIds() != null) {
            extension.put("categoryIds", nlpRecord.getCategoryIds());
        }
        // 库目录
        if (nlpRecord.getRecordStatus() != null) {
            extension.put("recordStatus", nlpRecord.getRecordStatus());
        }
        // 文件权限
        if (Objects.nonNull(nlpRecord.getVisitType())) {
            extension.put("visitType", nlpRecord.getVisitType());
        }
        if(nlpRecord.getTextVisitorIds() != null) {
            extension.put("textVisitorIds", nlpRecord.getTextVisitorIds());
        }
        if(nlpRecord.getVisitorIds() != null) {
            extension.put("visitorIds", nlpRecord.getVisitorIds());
        }
        if (updateRepoFolder) {
            List<RepoFolder> repoFolders = nlpRecord.getRepoFolders();
            // repoFolderList 收集 转换FolderIds集合
            if (CollUtil.isNotEmpty(repoFolders)) {
                List<Long> repoIds = new ArrayList<>();
                List<Long> folderIds = new ArrayList<>();
                for (RepoFolder repoFolder : repoFolders) {
                    repoIds.add(repoFolder.getRepoId());
                    folderIds.addAll(repoFolder.getFolderIds());
                }
                extension.put("repoIds", repoIds);
                extension.put("folderIds", folderIds);
                extension.put("searchFlag", 1);
            } else {
                //没有库目录 同步到AI中台
                extension.put("repoIds", Collections.emptyList());
                extension.put("folderIds", Collections.emptyList());
                extension.put("searchFlag", 0);
            }
        }
        // 元数据
        List<MetadataIdValue> metadataIdValueList = nlpRecord.getMetadataIdValue();
        if (!CollectionUtils.isEmpty(metadataIdValueList)) {
            for (MetadataIdValue metadata : metadataIdValueList) {
                if (Objects.equals(metadata.getMetadataValueType(),
                        RecordEnum.RecordMetadataValueTypeEnum.RECORD_METADATA_VALUE_TYPE_STRING_LIST.getCode())) {
                    extension.put(metadata.getMetadataName(), metadata.getMetadataValues());
                } else {
                    extension.put(metadata.getMetadataName(), metadata.getMetadataValue());
                }
            }
        }
        extension.put("updateTime", nlpRecord.getUpdateTime());
        if (StringUtils.isNotEmpty(nlpRecord.getRecordPic())) {
            extension.put("recordPic", nlpRecord.getRecordPic());
        }
        if (StringUtils.isNotEmpty(nlpRecord.getRecordName())) {
            extension.put("recordName", nlpRecord.getRecordName());
        }
        if (nlpRecord.getVisitNum() != null) {
            extension.put("visitNum", nlpRecord.getVisitNum());
        }
        params.put("extension", extension);
        return params;
    }


    // 设置附件 同步主件的信息
    private static NlpRecord setAttNlpRecordMasterInfo(NlpRecord masterRecord, NlpRecord attRecord) {
        // ----附件同步主件信息
        attRecord.setRepoFolders(masterRecord.getRepoFolders());
        attRecord.setRecordStatus(masterRecord.getRecordStatus());
        attRecord.setRecordTypeId(masterRecord.getRecordTypeId());
        attRecord.setOrigin(masterRecord.getOrigin());
        attRecord.setRealOrigin(masterRecord.getRealOrigin());
        attRecord.setCategoryLeafIds(masterRecord.getCategoryLeafIds());
        attRecord.setUserIds(masterRecord.getUserIds());
        attRecord.setOrgIds(masterRecord.getOrgIds());
        attRecord.setTenantId(masterRecord.getTenantId());
        attRecord.setVisitType(masterRecord.getVisitType());
        attRecord.setTextVisitorIds(masterRecord.getTextVisitorIds());
        attRecord.setVisitorIds(masterRecord.getVisitorIds());
        attRecord.setStoreProcess(masterRecord.getStoreProcess());
        attRecord.setVisitNum(masterRecord.getVisitNum());
        attRecord.setReleaseTime(masterRecord.getReleaseTime());
        return attRecord;
    }




    public static NlpRecord buildNlpRecord(MqRecordInfoDTO mqRecordInfoInsert) {
        // 基本信息
        NlpRecord nlpRecord = new NlpRecord();
        nlpRecord.setUid(mqRecordInfoInsert.getRecordId().toString());
        nlpRecord.setRecordId(mqRecordInfoInsert.getRecordId());
        nlpRecord.setSplitId(-1L);
        nlpRecord.setRecordName(mqRecordInfoInsert.getRecordName());
        nlpRecord.setTitle(mqRecordInfoInsert.getTitle());
        nlpRecord.setDigest(mqRecordInfoInsert.getDigest());
        nlpRecord.setReleaseTime(mqRecordInfoInsert.getReleaseTime());
        nlpRecord.setTenantId(mqRecordInfoInsert.getTenantId());
        nlpRecord.setCreateTime(System.currentTimeMillis());
        nlpRecord.setUpdateTime(System.currentTimeMillis());
        nlpRecord.setCategoryIds(mqRecordInfoInsert.getCategoryIds());
        nlpRecord.setRecordPic(mqRecordInfoInsert.getRecordPic());
        nlpRecord.setOrigin(mqRecordInfoInsert.getOrigin());
        nlpRecord.setRecordTypeId(mqRecordInfoInsert.getRecordTypeId());
        nlpRecord.setRealOrigin(mqRecordInfoInsert.getRealOrigin());
        nlpRecord.setCategoryLeafIds(mqRecordInfoInsert.getCategoryLeafIds());
        nlpRecord.setFileMd5(mqRecordInfoInsert.getFileMd5());
        nlpRecord.setFileType(mqRecordInfoInsert.getFileType());
        nlpRecord.setUserIds(mqRecordInfoInsert.getUserIds());
        nlpRecord.setDocId(mqRecordInfoInsert.getDocId());
        nlpRecord.setRecordCreateTime(mqRecordInfoInsert.getRecordCreateTime());
        nlpRecord.setRecordUpdateTime(mqRecordInfoInsert.getRecordUpdateTime());
        nlpRecord.setSuffix(mqRecordInfoInsert.getSuffix());
        nlpRecord.setStoreProcess(mqRecordInfoInsert.getStoreProcess());
        nlpRecord.setOrgIds(mqRecordInfoInsert.getOrgIds());
        nlpRecord.setOrderBy(mqRecordInfoInsert.getOrderBy());

        // 元数据
        nlpRecord.setMetadataIdValue(buildMetadataIdValue(mqRecordInfoInsert.getMetadataIdValueDTOList()));
        // 库目录信息
        nlpRecord.setRepoFolders(CollUtil.isNotEmpty(mqRecordInfoInsert.getMqRecordPathDTOList())
                ? buildRepoFolders(mqRecordInfoInsert.getMqRecordPathDTOList()) : Lists.newArrayList());

        // 文件权限信息
        MqRecordPermDTO recordPermDTO = buildRecordPerms(mqRecordInfoInsert.getMqRecordPermDTOList());
        if (Objects.nonNull(recordPermDTO)) {
            nlpRecord.setVisitType(recordPermDTO.getVisitType());
            nlpRecord.setTextVisitorIds(recordPermDTO.getTextVisitorIds());
            nlpRecord.setVisitorIds(recordPermDTO.getVisitorIds());
        }
        return nlpRecord;
    }
    public static List<RepoFolder> buildRepoFolders(List<MqRecordPathDTO> mqRecordPathDTOList) {
        List<RepoFolder> repoFolderListAll = Lists.newArrayList();
        if (CollUtil.isNotEmpty(mqRecordPathDTOList)) {
            for (MqRecordPathDTO mqRecordPathDTO : mqRecordPathDTOList) {
                if (CollUtil.isNotEmpty(mqRecordPathDTO.getRelList())) {
                    List<RepoFolder> repoFolderList = Lists.newArrayList();
                    for (RepoFolderRel repoFolderRel : mqRecordPathDTO.getRelList()) {
                        RepoFolder repoFolder = new RepoFolder();
                        repoFolder.setRepoStatus(repoFolderRel.getRepoStatus());
                        repoFolder.setRepoId(repoFolderRel.getRepoId());
                        repoFolder.setFolderIds(repoFolderRel.getFolderIds());
                        repoFolderList.add(repoFolder);
                    }
                    repoFolderListAll.addAll(repoFolderList);
                }
            }
        }
        return repoFolderListAll;
    }
    public static List<RepoFolder> buildRepoFolders(MqRecordPathDTO mqRecordPathDTO) {
        List<RepoFolder> repoFolderList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(mqRecordPathDTO.getRelList())) {
            for (RepoFolderRel repoFolderRel : mqRecordPathDTO.getRelList()) {
                RepoFolder repoFolder = new RepoFolder();
                repoFolder.setRepoStatus(repoFolderRel.getRepoStatus());
                repoFolder.setRepoId(repoFolderRel.getRepoId());
                repoFolder.setFolderIds(repoFolderRel.getFolderIds());
                repoFolderList.add(repoFolder);
            }
        }
        return repoFolderList;
    }
    /**
     * 文件权限信息
     *
     * @param mqRecordPermDTOList
     */
    public static MqRecordPermDTO buildRecordPerms(List<MqRecordPermDTO> mqRecordPermDTOList) {
        if (CollUtil.isNotEmpty(mqRecordPermDTOList)) {
            return mqRecordPermDTOList.get(0);
        }
        return null;
    }

    /**
     * 构建 元数据实体
     *
     * @param metadataIdValueDTOList 元数据对象
     */
    public static List<MetadataIdValue> buildMetadataIdValue(List<MqMetadataIdValueDTO> metadataIdValueDTOList) {
        if (CollUtil.isNotEmpty(metadataIdValueDTOList)) {
            return metadataIdValueDTOList.stream().map(y -> {
                MetadataIdValue metadataIdValue = new MetadataIdValue();
                metadataIdValue.setMetadataId(y.getMetadataId());
                metadataIdValue.setMetadataValueType(y.getMetadataValueType());
                metadataIdValue.setMetadataValueList(y.getMetadataValueList());
                metadataIdValue.setMetadataValues(CollUtil.isNotEmpty(y.getMetadataValues())
                        ? Lists.newArrayList(y.getMetadataValues()) : Lists.newArrayList());
                metadataIdValue.setMetadataName(y.getMetadataName());
                metadataIdValue.setMetadataValue(y.getMetadataValue());
                return metadataIdValue;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
