package com.suwell.plss.document.process.listeners;

import com.suwell.plss.document.process.util.AutomAgentInstancePool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;


/**
 * 服务关闭时关掉转换服务
 *
 * <AUTHOR>
 * @date 2023-08-16 15:58
 **/
@Slf4j
@Component
public class ApplicationCloseListener implements ApplicationListener<ContextClosedEvent> {

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        try {
            AutomAgentInstancePool.getHa().close();
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>AtomAgent关闭成功<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        } catch (Exception e) {
            log.error("关闭失败，AtomAgent关闭失败", e);
        }
    }

}

