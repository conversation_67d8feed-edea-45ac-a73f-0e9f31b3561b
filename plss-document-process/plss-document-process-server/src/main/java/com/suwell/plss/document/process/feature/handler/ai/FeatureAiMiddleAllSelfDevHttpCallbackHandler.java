package com.suwell.plss.document.process.feature.handler.ai;

import static com.suwell.plss.framework.mq.enums.EventType.GET_AI_ALL;
import static com.suwell.plss.record.standard.enums.RecordEnum.FeatureImplInvokeWayEnum.FEATURE_IMPL_INVOKE_WAY_HTTP_CALLBACK;
import static com.suwell.plss.record.standard.enums.RecordEnum.FeatureImplWayEnum.FEATURE_IMPL_WAY_SELF_DEV;
import static com.suwell.plss.record.standard.enums.RecordEnum.FeaturePlatformWayEnum.PLATFORM_WAY_AI_MIDDLE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.suwell.plss.document.process.config.ButtJointOtherProperties;
import com.suwell.plss.document.process.config.FeatureImplNewConfig;
import com.suwell.plss.document.process.dto.request.HandleCallbackDTO;
import com.suwell.plss.document.process.dto.request.HandleCallbackReq;
import com.suwell.plss.document.process.enums.DocumentRedisKeyEnum;
import com.suwell.plss.document.process.feature.AbstractFeatureHandler;
import com.suwell.plss.framework.common.config.ServerBasePathConfig;
import com.suwell.plss.framework.common.domain.R;
import com.suwell.plss.framework.mq.dto.base.MqExtendParamDTO;
import com.suwell.plss.framework.mq.dto.process.HandleDocumentReq;
import com.suwell.plss.framework.mq.dto.process.MqAiMiddleDTO;
import com.suwell.plss.framework.mq.enums.EventType;
import com.suwell.plss.record.service.FileManageRpcService;
import com.suwell.plss.record.standard.dto.request.FileTempUrlReq;
import com.suwell.plss.record.standard.enums.RecordEnum.FeatureImplInvokeWayEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.FeatureImplWayEnum;
import com.suwell.plss.record.standard.enums.RecordEnum.FeaturePlatformWayEnum;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * ai中台 全量提取 http 回调的方式
 *
 * <AUTHOR>
 * @date 2024/1/2
 */
@RefreshScope
@Slf4j
@Service
public class FeatureAiMiddleAllSelfDevHttpCallbackHandler extends AbstractFeatureHandler {

    @Resource
    private ServerBasePathConfig serverBasePathConfig;
    @Resource
    private FileManageRpcService fileManageRpcService;
    @Resource
    private FeatureImplNewConfig featureImplNewConfig;
    @Resource
    private FeatureAiService featureAiService;

    @Override
    protected DocumentRedisKeyEnum getKeyEnum() {
        return DocumentRedisKeyEnum.DOCUMENT_AI_MIDDLE_SEMAPHORE_CAPACITY;
    }

    @Override
    public Object invokeSubmitFeatureImpl(HandleDocumentReq req) {
        log.info("AI中台提交http任务对接 req:{}", JSON.toJSONString(req));
        try {
            Long recordId = req.getRecordId();
            FileTempUrlReq fileTempUrlReq = new FileTempUrlReq();
            fileTempUrlReq.setFileId(req.getFileId());
            fileTempUrlReq.setDuration(0);
            R<String> r = fileManageRpcService.tempUrl(fileTempUrlReq);
            if (r.isSuccess()) {
                String webUrl = r.getData();
                MqAiMiddleDTO mqAiMiddleDTO = new MqAiMiddleDTO();
                mqAiMiddleDTO.setIndexName(FeatureAiService.AI_MIDDLE_RECORD_VECTOR);
                mqAiMiddleDTO.setFileUrl(webUrl);
                ButtJointOtherProperties buttJointProperties =
                        featureImplNewConfig.getButtJointProperties(featureImplNewConfig.getButtJointOtherKey());
                String serverEndpointPrefix = serverBasePathConfig.getSysHostPort()
                        + serverBasePathConfig.getSysHostContextPath() + serverBasePathConfig.getSysApiPrefix();
                String callbackAddress = serverEndpointPrefix + buttJointProperties.getAiCallbackAddress();
                mqAiMiddleDTO.setCallBackUrl(callbackAddress);

                // 主键
                Map<String, Object> extension = Maps.newHashMap();
                extension.put(FeatureAiService.AI_MIDDLE_RECORD_ID, req.getRecordId());
                extension.put(FeatureAiService.AI_MIDDLE_PK_DOC_ID, req.getDocId());
                extension.put(FeatureAiService.AI_MIDDLE_DOC_TYPE, req.getCtype());
                mqAiMiddleDTO.setExtension(extension);

                // 业务扩展字段
                HandleCallbackDTO handleCallbackDTO = new HandleCallbackDTO();
                handleCallbackDTO.setRecordId(recordId);
                handleCallbackDTO.setEventCode(eventType().getCode());
                handleCallbackDTO.setSkipState(req.getSkipState());
                handleCallbackDTO.setNodeId(req.getNodeId());
                handleCallbackDTO.setOrigin(Objects.nonNull(req.getExtendParam())
                        ? req.getExtendParam().getOrigin() : null);

                MqExtendParamDTO extendParam = req.getExtendParam();
                if (Objects.nonNull(extendParam) && CollUtil.isNotEmpty(extendParam.getEventTypeList())) {
                    List<Integer> eventCodeList = extendParam.getEventTypeList().stream().map(EventType::getCode)
                            .collect(Collectors.toList());
                    handleCallbackDTO.setEventCodeList(eventCodeList);
                }
                final String callbackJson = JSON.toJSONString(handleCallbackDTO);
                log.info("提交ai回调携带参数:{}", callbackJson);
                mqAiMiddleDTO.setExtendParam(callbackJson);

                String aiMiddleSubmitPath = serverBasePathConfig.getAiMiddleHostPort()
                        + buttJointProperties.getAiMiddleSubmitAddress();
                String result = HttpUtil.post(aiMiddleSubmitPath, JSON.toJSONString(mqAiMiddleDTO));
                log.info("{},ai中台提交任务返回的结果:{}", eventType(), result);
                pushCustomCallBackMsg(recordId, req.getNodeId(), callbackJson, req.getPriorityValue());
            }
            return null;
        } catch (Exception e) {
            log.error("提交AI中台失败，req={}", req, e);
            additionRateCapacity();
            featureAiService.quickFail(req);
            triggerNode(req.getSkipState(), req.getNodeId(), req.getRecordId(), eventType(), req.getPriorityValue());
            return null;
        }
    }

    @Override
    public Object invokeCallbackFeatureImpl(HandleCallbackReq req) {
        return featureAiService.invokeCallbackFeatured(req, this);
    }

    public void invokeExportFeatureImpl(List<Long> recordIds, String dirname) {
        log.info("AI中台提交http任务export对接 req_size:{}", recordIds.size());
        try {
            ButtJointOtherProperties buttJointProperties =
                    featureImplNewConfig.getButtJointProperties(featureImplNewConfig.getButtJointOtherKey());

            String aiMiddleExportPath = serverBasePathConfig.getAiMiddleHostPort()
                    + buttJointProperties.getAiMiddleExportAddress();
            if (StringUtils.isBlank(aiMiddleExportPath)) {
                log.info("ai中台提交任务export地址不存在，aiMiddleExportAddress为空");
                return;
            }

            Map<String, Object> param = Maps.newHashMap();
            param.put("indexName", FeatureAiService.AI_MIDDLE_RECORD_VECTOR);
            param.put("listRecordId", recordIds);
            param.put("dirname", dirname);

            String result = HttpUtil.post(aiMiddleExportPath, JSON.toJSONString(param));
            log.info("ai中台提交任务export返回的结果:{}", result);

        } catch (Exception e) {
            log.error("提交AI中台export失败，{}", e);
        }
    }

    public void invokeImportFeatureImpl(String dirname) {
        log.info("AI中台提交http任务import对接 dir:{}", dirname);
        try {
            ButtJointOtherProperties buttJointProperties =
                    featureImplNewConfig.getButtJointProperties(featureImplNewConfig.getButtJointOtherKey());
            String aiMiddleImportPath = serverBasePathConfig.getAiMiddleHostPort()
                    + buttJointProperties.getAiMiddleImportAddress();
            if (StringUtils.isBlank(aiMiddleImportPath)) {
                log.info("ai中台提交任务import地址不存在，aiMiddleImportAddress为空");
                return;
            }

            Map<String, Object> param = Maps.newHashMap();
            param.put("indexName", FeatureAiService.AI_MIDDLE_RECORD_VECTOR);
            param.put("dirname", dirname);

            String result = HttpUtil.post(aiMiddleImportPath, JSON.toJSONString(param));
            log.info("ai中台提交任务import返回的结果:{}", result);

        } catch (
                Exception e) {
            log.error("提交AI中台import失败，{}", e);
        }

    }

    @Override
    public EventType eventType() {
        return GET_AI_ALL;
    }

    @Override
    public FeatureImplWayEnum implWay() {
        return FEATURE_IMPL_WAY_SELF_DEV;
    }

    @Override
    public FeatureImplInvokeWayEnum invokeWay() {
        return FEATURE_IMPL_INVOKE_WAY_HTTP_CALLBACK;
    }

    @Override
    public FeaturePlatformWayEnum platformWay() {
        return PLATFORM_WAY_AI_MIDDLE;
    }
}
