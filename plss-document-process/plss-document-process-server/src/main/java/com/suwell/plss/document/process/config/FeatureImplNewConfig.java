package com.suwell.plss.document.process.config;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "dp.feature.impl")
public class FeatureImplNewConfig {

    /**
     * json字符串,示例
     * [{"eventType":600,"implWay":1,"invokeWay":2},{"eventType":500,"implWay":1,"invokeWay":2}]
     */
    private String configKey;

    /**
     * com.suwell.plss.document.process.config.ButtJointOtherProperties
     */
    private String buttJointOtherKey;

    /**
     * 通过事件获取对接平台其他配置的信息
     */
    public ButtJointOtherProperties getButtJointProperties(String buttJointKey) {
        return JSON.parseObject(buttJointKey, ButtJointOtherProperties.class);
    }

    /**
     * 通过事件获取配置的信息
     *
     * @param eventType 事件类型 @link com.suwell.plss.framework.mq.enums.EventType
     */
    public FeatureImplProperties getFeatureImplProperties(String configKey, Integer eventType) {
        List<FeatureImplProperties> featureImplPropertiesList = JSON.parseObject(configKey,
                new TypeReference<List<FeatureImplProperties>>() {
                });
        return featureImplPropertiesList.stream()
                .filter(o -> o.getEventType().equals(eventType))
                .findAny().orElse(null);
    }

}
