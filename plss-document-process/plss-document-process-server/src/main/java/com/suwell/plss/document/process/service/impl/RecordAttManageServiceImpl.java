package com.suwell.plss.document.process.service.impl;

import static com.suwell.plss.framework.es.enums.EsIndexEnum.INDEX_NLP_RECORD;
import static com.suwell.plss.record.standard.enums.RecordEnum.RecordDocumentEnum.RECORD_DOCUMENT_TYPE_ATTACHMENT;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.suwell.plss.document.process.config.ButtJointOtherProperties;
import com.suwell.plss.document.process.config.FeatureImplNewConfig;
import com.suwell.plss.document.process.convert.NlpRecordConvertUtils;
import com.suwell.plss.document.process.feature.handler.ai.FeatureAiManageService;
import com.suwell.plss.document.process.service.BizCommonService;
import com.suwell.plss.document.process.service.RecordAttManageService;
import com.suwell.plss.framework.common.enums.CommonData.NormalState;
import com.suwell.plss.framework.es.entity.NlpRecord;
import com.suwell.plss.framework.es.enums.RefreshEnum;
import com.suwell.plss.framework.es.service.ElasticsearchService;
import com.suwell.plss.framework.mq.dto.base.MqRecordInfoDTO;
import com.suwell.plss.framework.mq.dto.base.MqRecordRemoveDTO;
import com.suwell.plss.record.standard.enums.RecordEnum.EnableSwitchEnum;
import java.util.List;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
@Service
public class RecordAttManageServiceImpl implements RecordAttManageService {

    @Resource
    private ElasticsearchService elasticsearchService;
    @Resource
    private BizCommonService bizCommonService;

    @Resource
    private FeatureAiManageService featureAiManageService;
    @Resource
    private FeatureImplNewConfig featureImplNewConfig;


    @Override
    public void saveAttNlpRecord(MqRecordInfoDTO mqRecordInfoDTO) {
        log.info("入库附件写入es数据,req:{}", JSON.toJSONString(mqRecordInfoDTO));
        // 主件查询
        NlpRecord byId = bizCommonService.queryMasterFile(mqRecordInfoDTO.getRecordId());
        if (Objects.nonNull(byId) && RECORD_DOCUMENT_TYPE_ATTACHMENT.getCode().equals(mqRecordInfoDTO.getFileType())) {
            NlpRecord attRecord = NlpRecordConvertUtils.buildAttNlpRecord(byId, mqRecordInfoDTO);
            if (Objects.nonNull(mqRecordInfoDTO.getTxtFileId())) {
                attRecord.setContent(bizCommonService.getTextByFileId(mqRecordInfoDTO.getRecordId(),
                        mqRecordInfoDTO.getTxtFileId(),attRecord.getTitle()));
            }
            attRecord.setCreateTime(System.currentTimeMillis());
            attRecord.setUpdateTime(System.currentTimeMillis());

            if (Objects.isNull(mqRecordInfoDTO.getClassified())) {
                log.info("附件 非密文件:es 写入主件数据操作 req:{}", attRecord);
                elasticsearchService.bulkAddOrUpdateDocument(INDEX_NLP_RECORD.getIndexName(),
                        INDEX_NLP_RECORD.getIndexName(), Lists.newArrayList(attRecord),
                        mqRecordInfoDTO.isRefresh() ?RefreshEnum.TRUE : RefreshEnum.FALSE );
            } else {
                log.info("附件 安全件:ai中台Classified:{}", mqRecordInfoDTO.getClassified());
            }

            ButtJointOtherProperties buttJointProperties = featureImplNewConfig.getButtJointProperties(
                            featureImplNewConfig.getButtJointOtherKey());
            if (EnableSwitchEnum.ENABLE.getCode().equals(buttJointProperties.getAiMiddleSwitch())) {
                log.info("启用同步ai中台操作同步附件:{}", attRecord);
                Integer classified = Objects.nonNull(mqRecordInfoDTO.getExtendParam())
                        ? mqRecordInfoDTO.getExtendParam().getClassified() : null;
                featureAiManageService.updateNlpRecord2AiPlatform(attRecord,
                        true, classified);
            }
        } else {
            log.error("recordId:{}, 数据不一致导致的异常数据,es 写入主件失败...", mqRecordInfoDTO.getRecordId());
            bizCommonService.exceptionCallback(mqRecordInfoDTO.getRecordId(), NormalState.FORBIDDEN.getCode());
        }
    }

    /**
     * 获取附件更新的NlpRecord
     *
     * @param masterRecord 主件
     * @return 附件更新的NlpRecord
     */
    @Override
    public List<NlpRecord> getAttUpdateNlpRecord(NlpRecord masterRecord, List<MqRecordInfoDTO> attList) {
        List<NlpRecord> attFiles = bizCommonService.queryAttFile(masterRecord.getRecordId());
        List<NlpRecord> updateAttList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(attFiles)) {
            for (NlpRecord attFile : attFiles) {
                NlpRecord attRecord = NlpRecordConvertUtils.buildUpdateAttNlpRecord(masterRecord, attFile);
                attRecord.setCreateTime(attFile.getCreateTime());
                attRecord.setUpdateTime(attFile.getUpdateTime());
                attRecord.setContent(attFile.getContent());
                updateAttList.add(attRecord);
            }
        }
        return updateAttList;
    }

    @Override
    public void deleteRecordAtt(MqRecordRemoveDTO mqRecordRemoveDTO) {
        log.info("删除附件操作:{}", JSON.toJSONString(mqRecordRemoveDTO));
        bizCommonService.removeBatchByRecordId(mqRecordRemoveDTO.getRecordId(), mqRecordRemoveDTO.getDocIdList());
        featureAiManageService.deleteAiEsRecord(mqRecordRemoveDTO.getRecordId(),
                mqRecordRemoveDTO.getDocIdList(),  null);
        featureAiManageService.deleteAiEsRecord(mqRecordRemoveDTO.getRecordId(),
                mqRecordRemoveDTO.getDocIdList(),  -1);
    }
}
