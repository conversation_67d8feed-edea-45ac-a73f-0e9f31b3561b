package com.suwell.plss.framework.common.config;

import com.suwell.plss.framework.common.enums.ProjectTypeEnum;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 行业类型配置
 * <AUTHOR>
 */
@Data
@RefreshScope
@Component
public class ProjectTypeConfig{

    /**
     * 行业类型 公文："gw" 医疗："med"
     */
    @Value("${sys.project.type:}")
    private String projectType;


    /**
     * 是否医疗行业
     */
    public boolean isMed(){
        return ProjectTypeEnum.MED.getType().equals(projectType);
    }

    /**
     * 是否医疗行业
     */
    public boolean isGw(){
        return ProjectTypeEnum.GW.getType().equals(projectType);
    }
}
