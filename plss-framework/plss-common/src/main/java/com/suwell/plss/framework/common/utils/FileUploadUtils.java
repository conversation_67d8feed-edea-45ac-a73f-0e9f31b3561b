package com.suwell.plss.framework.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.unit.DataSizeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.suwell.plss.framework.common.config.multipart.PlssMultipartFile;
import com.suwell.plss.framework.common.domain.TemporaryMultipartFile;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.exception.file.FileException;
import com.suwell.plss.framework.common.exception.file.FileNameLengthLimitExceededException;
import com.suwell.plss.framework.common.exception.file.FileSizeLimitExceededException;
import com.suwell.plss.framework.common.exception.file.InvalidExtensionException;
import com.suwell.plss.framework.common.utils.bean.BeanValidators;
import com.suwell.plss.framework.common.utils.file.FileTypeUtils;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import com.suwell.plss.framework.common.utils.file.MimeTypeUtils;
import com.suwell.plss.framework.common.utils.uuid.Seq;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.Comparator;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import lombok.Data;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import com.suwell.plss.framework.common.utils.file.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.hibernate.validator.constraints.Range;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 * @date 2023/8/1
 */
public class FileUploadUtils {

    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;
    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;
    private static final Logger log = LoggerFactory.getLogger(FileUploadUtils.class);
    private static final String PART_SUFFIX = "part";

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static String upload(String baseDir, MultipartFile file) throws IOException {
        try {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (FileException fe) {
            throw new IOException(fe.getDefaultMessage(), fe);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件 mb大小转换 字节大小
     *
     * @param mbSize MB大小
     * @return 字节大小
     */
    public static long sizeMB2Bytes(int mbSize) {
        return DataSizeUtil.parse(mbSize + "MB");
    }

    /**
     * 文件上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException 比如读写文件出错时
     * @throws InvalidExtensionException 文件校验异常
     */
    public static String upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException {
        int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file);

        String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
        file.transferTo(Paths.get(absPath));
        return getPathFileName(fileName);
    }

    /**
     * 解析 来源的临时文件的有效链接地址
     *
     * @param domain 来源的域名，例如: http://*************:9000
     * @param tempLink 来源的有效临时连接。
     *         例如:
     *         http://*************:9000/cma/2023/08/28/cmafile062394bb-17b4-483c-a8df-8bcca59b99e4_20230828225250A015.doc
     *         例如:
     *         http://*************:9000/cma/2023/08/28/cmafile062394bb-17b4-483c-a8df-8bcca59b99e4_20230828225250A015.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=admin%2F20230830%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230830T093343Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=2f40960e63764ffb09755010a82ae79dacfd4782739f17ccdb8f9383f64d0871
     */
    public static ParseSourceObjectNameDTO parseSourceObjectName(String domain, String tempLink) {
        if (-1 != tempLink.indexOf(StringPool.QUESTION_MARK)) {
            tempLink = StrUtil.subPre(tempLink, tempLink.indexOf(StringPool.QUESTION_MARK));
        }
        ParseSourceObjectNameDTO parseSourceObjectNameDTO = new ParseSourceObjectNameDTO();
        if (tempLink.contains(domain)) {
            String sourceObjectName = tempLink.replaceAll(domain + StringPool.SLASH, StringPool.EMPTY);
            String sourceBucketName = StrUtil.subPre(sourceObjectName, sourceObjectName.indexOf(StringPool.SLASH));
            sourceObjectName = StrUtil.subSuf(sourceObjectName, sourceObjectName.indexOf(StringPool.SLASH) + 1);

            parseSourceObjectNameDTO.setSourceBucketName(sourceBucketName);
            parseSourceObjectNameDTO.setSourceObjectName(sourceObjectName);
            parseSourceObjectNameDTO.setDomainSame(true);
            parseSourceObjectNameDTO.setSuffix(FileUtil.getSuffix(tempLink));
        } else {
            parseSourceObjectNameDTO.setDomainSame(false);
        }
        return parseSourceObjectNameDTO;
    }

    /**
     * 编码文件名
     */
    public static String extractFilename(MultipartFile file) {
        return StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getId(Seq.uploadSeqType),
                FileTypeUtils.getExtension(file));
    }

    /**
     * 编码文件名
     *
     * @param fileName 文件名称,桶中文件名称，不带后缀名称,限制最长100
     * @param suffix 后缀名称不带”.“ 例如: png/jpg/doc
     */
    public static String extractFilename(String fileName, String suffix) {
        int fileNameLength = Objects.requireNonNull(FilenameUtils.getBaseName(fileName)).length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            log.error("默认的文件名最大长度 100");
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }
        return StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                FilenameUtils.getBaseName(fileName), Seq.getId(Seq.uploadSeqType), suffix);
    }

    private static File getAbsoluteFile(String uploadDir, String fileName) {
        File desc = FileUtils.getFile(uploadDir + File.separator + fileName);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc.isAbsolute() ? desc : desc.getAbsoluteFile();
    }

    private static String getPathFileName(String fileName) {
        return "/" + fileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException 文件校验异常
     */
    public static void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, InvalidExtensionException {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE) {
            throw new FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = FileTypeUtils.getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                        fileName);
            } else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                        fileName);
            } else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                        fileName);
            } else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION) {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension,
                        fileName);
            } else {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension 上传文件类型
     * @param allowedExtension 允许上传文件类型
     * @return true/false
     */
    public static boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }


    /**
     * url转变为 MultipartFile对象
     *
     * @param url 网络地址链接,支持https、http
     * @param fileName 文件名.扩展名
     */
    public static MultipartFile createMultipartFile(String url, String fileName) {
        FileItem item = null;
        PlssMultipartFile commonsMultipartFile = null;
        try {
            //HttpURLconnection是基于http协议的，支持get，post，put，delete等各种请求方式，最常用的就是get和post  这里是根据url发起一个HTTP请求
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            //设置从主机读取数据超时（单位：毫秒）
            conn.setReadTimeout(30000);
            //设置连接主机超时（单位：毫秒）
            conn.setConnectTimeout(30000);
            //设置应用程序要从网络连接读取数据,允许读入
            conn.setDoInput(true);
            //设置请求方式为GET
            conn.setRequestMethod("GET");
            //发起请求并获取响应码比对。
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                //得到响应流
                InputStream is = conn.getInputStream();
                //采用参数指定临界值和系统临时文件夹构造文件项工厂对象。
                FileItemFactory factory = new DiskFileItemFactory(16, null);
                String textFieldName = "uploadfile" + UUID.randomUUID();
                //根据DiskFileItemFactory相关配置将每一个请求消息实体项目创建成DiskFileItem 实例
                item = factory.createItem(textFieldName, "application/octet-stream", false, fileName);
//                item = factory.createItem(textFieldName, ContentType.APPLICATION_OCTET_STREAM.toString(),
//                        false, fileName);
                OutputStream os = item.getOutputStream();

                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = is.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }

                // 转换为MultipartFile对象
                commonsMultipartFile = new PlssMultipartFile(item);
                os.close();
                is.close();
            }
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败", e);
        }
        return commonsMultipartFile;
    }

    /**
     * 创建一个临时的spring的文件流
     *
     * @param fileName
     * @param contentType
     * @param fileContent
     * @return
     */
    public static TemporaryMultipartFile createMultipartFile(String fileName, String contentType, byte[] fileContent) {
        return new TemporaryMultipartFile(fileName, fileName, contentType, fileContent);
    }

    /**
     * 读取文件内容
     *
     * @param inputStream 文件流
     * @return 文件内容
     */
    public static String readFile(InputStream inputStream) {
        try (InputStream is = inputStream) {
            return IOUtils.toString(is, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void uploadPart(UploadPartReq partReq, Consumer<? super File> mergeCallback) {
        BeanValidators.defaultValidate(partReq);
        try {
            // 分片上传
            File folder = findRealFolder(partReq);
            moveTempChunk(partReq, folder);
            Collection<File> files = FileUtils.listFiles(folder, false, PART_SUFFIX);
            if (Objects.equals(files.size(), partReq.getChunkedCount())) {
                // 合并分片
                try {
                    File originFile = mergeChunks(partReq, folder, files);
                    if (originFile.length() == 0) {
                        log.warn("文件内容为空，分片文件失效，忽略后续执行。完整请求参数: {}", partReq);
                        return;
                    }
                    mergeCallback.accept(originFile);
                } finally {
                    FileUtil.del(folder);
                }
            }
        } catch (IOException e) {
            throw new ServiceException("文件上传失败", e);
        }
    }

    private static File mergeChunks(UploadPartReq req, File folder, Collection<File> files) throws IOException {
        if (CollectionUtil.isEmpty(files) || files.size() != req.getChunkedCount()) {
            throw new IOException("分片文件数量不匹配");
        }
        File targetFile = new File(Paths.get(folder.getPath(), req.getOriginName()).toString());
        OutputStream outputStream = Files.newOutputStream(targetFile.toPath());
        files.stream().sorted(Comparator.comparing(p -> Integer.parseInt(FileUtils.getMainName(p.getName()))))
                .forEach(file -> {
                    try (InputStream inputStream = Files.newInputStream(file.toPath())) {
                        IOUtils.copy(inputStream, outputStream);
                    } catch (IOException e) {
                        throw new ServiceException("合并分片文件失败", e);
                    } finally {
                        FileUtil.del(file);
                    }
                });
        outputStream.close();
        return targetFile;
    }

    private static void moveTempChunk(UploadPartReq req, File folder) throws IOException {
        File partFile = new File(Paths.get(folder.getPath(),
                req.getChunkedId() + StringPool.DOT + PART_SUFFIX).toString());
        if (!partFile.exists() && !partFile.createNewFile()) {
            throw new IOException("创建分片文件失败");
        }
        req.getFileData().transferTo(partFile);
    }

    private static File findRealFolder(UploadPartReq req) {
        String folderPath = Paths.get(req.getSharedPath(), generateDataDir(req)).toString();
        File folder = new File(folderPath);
        if (!folder.exists() && !folder.mkdirs()) {
            throw new ServiceException("创建文件夹失败");
        }
        return folder;
    }

    private static String generateDataDir(UploadPartReq req) {
        return Paths.get(req.getFileId() + "$$" + req.getOriginName()) + "$$" + req.getChunkedCount().toString();
    }

    @Data
    public static class UploadPartReq implements Serializable {

        @NotNull(message = "文件数据不能为空")
        private MultipartFile fileData;

        /**
         * 当前分片的id
         * 当分片id和分片总数相等时，代表当前分片是最后一个分片
         */
        @NotNull(message = "文件分片id不能为空")
        @Range(min = 1, message = "文件分片id必须大于0")
        private Integer chunkedId;

        /**
         * 分片总数，代表当前文件的总分片数
         */
        @NotNull(message = "文件分片总数不能为空")
        @Range(min = 1, message = "文件分片总数必须大于0")
        private Integer chunkedCount;

        /**
         * 原始名称，作为文件指定，若不指定，则按照默认规则生成
         */
        @NotEmpty(message = "文件名称不能为空")
        private String originName;

        /**
         * 当前分片的id，建议用uuid来代表本次上传的唯一性
         */
        @NotEmpty(message = "文件唯一标识不能为空")
        private String fileId;

        @NotBlank(message = "文件父目录不能为空")
        private String sharedPath;

        @Override
        public String toString() {
            return "UploadPartReq{" +
                   "file originName=" + fileData.getOriginalFilename() +
                   "file tempName=" + fileData.getName() +
                   "file size=" + fileData.getSize() +
                   ", chunkedId=" + chunkedId +
                   ", chunkedCount=" + chunkedCount +
                   ", request originName='" + originName + '\'' +
                   ", fileId='" + fileId + '\'' +
                   ", sharedPath='" + sharedPath + '\'' +
                   '}';
        }
    }


    @Data
    public static class ParseSourceObjectNameDTO implements Serializable {

        private static final long serialVersionUID = -3471297084385076036L;
        /**
         * 解析来源桶的名称
         */
        private String sourceBucketName;
        /**
         * 解析来源桶的相对路径
         */
        private String sourceObjectName;

        /**
         * 解析来源 使用的同一个 对象存储，true: 同一个对象存储，false 不同的对象存储
         */
        private Boolean domainSame;
        /**
         * 解析来源 文件后缀，不带 “.” ,例如:png /jpg/ofd
         */
        private String suffix;

    }
}
