package com.suwell.plss.framework.common.utils.file;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ZipUtil;
import com.suwell.plss.framework.common.config.multipart.PlssMultipartFile;
import com.suwell.plss.framework.common.config.multipart.PlssMultipartFileConfig.PlssDiskFileItemFactory;
import com.suwell.plss.framework.common.constant.SymbolPool;
import com.suwell.plss.framework.common.exception.ServiceException;
import com.suwell.plss.framework.common.utils.StringUtils;
import feign.Response;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 * @date 2023年4月20日
 */
public class FileUtils {
    private static Logger logger = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 字符常量：斜杠 {@code '/'}
     */
    public static final char SLASH = '/';

    /**
     * 字符常量：反斜杠 {@code '\\'}
     */
    public static final char BACKSLASH = '\\';

    public static String FILENAME_PATTERN = "[a-zA-Z0-9_\\-\\|\\.\\u4e00-\\u9fa5]+";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os 输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        FileInputStream fis = null;
        try {
            File file = getFile(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = getFile(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            flag = file.delete();
        }
        return flag;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 检查文件是否可下载
     *
     * @param resource 需要下载的文件
     * @return true 正常 false 非法
     */
    public static boolean checkAllowDownload(String resource) {
        // 禁止目录上跳级别
        if (StringUtils.contains(resource, "..")) {
            return false;
        }
        // 判断是否在允许下载的文件规则内
        return ArrayUtils.contains(MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, FileTypeUtils.getFileType(resource));
    }

    /**
     * 下载文件名重新编码
     *
     * @param request 请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName)
            throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    /**
     * 返回文件名
     *
     * @param filePath 文件
     * @return 文件名
     */
    public static String getName(String filePath) {
        if (null == filePath) {
            return null;
        }
        int len = filePath.length();
        if (0 == len) {
            return filePath;
        }
        if (isFileSeparator(filePath.charAt(len - 1))) {
            // 以分隔符结尾的去掉结尾分隔符
            len--;
        }

        int begin = 0;
        char c;
        for (int i = len - 1; i > -1; i--) {
            c = filePath.charAt(i);
            if (isFileSeparator(c)) {
                // 查找最后一个路径分隔符（/或者\）
                begin = i + 1;
                break;
            }
        }

        return filePath.substring(begin, len);
    }

    /**
     * 以行为单位读取文件，常用于读面向行的格式化文件
     *
     * @param filePath 文件绝对路径
     * @return 返回文件内容
     */
    public static String readFileByLines(String filePath) {
        filePath = StringUtils.filterSpecialCharacters(filePath);
        File file = org.apache.commons.io.FileUtils.getFile(filePath);
        if (!file.exists()) {
            logger.error("文件不存在，路径为：{}", filePath);
            return null;
        }
        BufferedReader reader = null;
        try {
            StringBuilder str = new StringBuilder();
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                // 显示行号
                if (tempString.startsWith("----") && tempString.endsWith("----")) {
                    line++;
                    continue;
                }
                str.append(tempString);
                line++;
            }
            reader.close();
            return str.toString();
        } catch (IOException e) {
            throw new RuntimeException("读取文件失败", e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }

    /**
     * 从路径中获取文件名的扩展名
     *
     * @param filePath 文件路径 入参： "/seal/2021/10/11/aasa.pdf"  返回：
     * @return "pdf"
     */
    public static String getFileExtSuffix(String filePath) throws IllegalArgumentException {
        if (StringUtils.isEmpty(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空。");
        }
        if (!filePath.contains(".")) {
            throw new IllegalArgumentException("文件路径不正确");
        }
        return filePath.substring(filePath.lastIndexOf(".") + 1);
    }

    /**
     * 创建文件夹路径
     *
     * @param dirPath
     * @return
     */
    public static boolean createdir(String dirPath) {
        boolean flag = true;
        dirPath = StringUtils.filterSpecialCharacters(dirPath);
        File dirFile = org.apache.commons.io.FileUtils.getFile(dirPath);
        if (!dirFile.exists()) {
            logger.debug(dirPath + "目录不存在将创建之。");
            try {
                flag = dirFile.mkdirs();
            } catch (Exception e) {
                e.printStackTrace();
                logger.error(dirPath + "创建目录出现异常，详情：" + e.getMessage());
            }
            logger.info("创建目录" + dirPath + ((flag == true) ? "成功" : "失败"));
        }
        return flag;
    }

    /**
     * 判断文件是否存在
     *
     * @param filePath 文件绝对路径
     * @return boolean
     */
    public static boolean isFileExist(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            logger.error("filePath empty .");
            return false;
        }
        filePath = filePath.trim();
        filePath = StringUtils.filterSpecialCharacters(filePath);
        File tmpFile = org.apache.commons.io.FileUtils.getFile(filePath);
        if (!tmpFile.exists()) {
            logger.error("file not exist:" + filePath);
            return false;
        }
        return true;
    }

    /**
     * 删除文件夹
     *
     * @param filePath 文档路绝对路径
     */
    public static boolean deleteFolders(String filePath) {
        Path path = Paths.get(filePath);
        try {
            Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException {
                    Files.delete(file);
                    logger.info("删除文件: {}", file);
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult postVisitDirectory(Path dir,
                                                          IOException exc) throws IOException {
                    Files.delete(dir);
                    logger.info("文件夹被删除: {}", dir);
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            logger.info("文件夹删除失败: {}", e);
            return false;
        }
        return true;
    }

    /**
     * 写出文件数据
     *
     * @param data 文件数据
     * @param path 文件绝对路径
     **/
    public static boolean writeFile(byte[] data, String path) {
        if (null == data) {
            return false;
        }
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        boolean res = true;
        try {
            path = StringUtils.filterSpecialCharacters(path);
            File destFile = org.apache.commons.io.FileUtils.getFile(path);
            File destFolder = destFile.getParentFile();
            if (!destFolder.exists()) {
                logger.info("文件夹不存在，创建文件夹:{}", path);
                boolean b1 = destFolder.mkdirs();
                logger.info("文件夹，创建结果:{}", b1 == true ? "成功" : "失败");
            }
            fos = new FileOutputStream(destFile);
            bos = new BufferedOutputStream(fos);
            bos.write(data);
        } catch (Exception e) {
            logger.error("写文件异常", e);
            res = false;
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (Exception e) {
                    logger.error("关闭文件流程异常", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (Exception e) {
                    logger.error("关闭文件流程异常", e);
                }
            }
        }
        return res;
    }

    /**
     * 查询该目录下的所有文件信息并返回
     *
     * @return List
     */
    public static List<File> searchFiles(String directoryPath) {
        File directory = org.apache.commons.io.FileUtils.getFile(directoryPath);
        List<File> fileList = new ArrayList<>();
        if (directory.exists() && directory.isDirectory()) {
            logger.info("所有文件：");
            searchFilesRecursive(directory, fileList);
        } else {
            logger.info("目录不存在或不是一个目录。");
        }
        return fileList;
    }

    private static void searchFilesRecursive(File directory, List<File> fileList) {

        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归处理子目录
                    searchFilesRecursive(file, fileList);
                } else {
                    fileList.add(file);
                }
            }
        }
    }


    public static String getMainName(String filename) {
        return FileNameUtil.mainName(filename);
    }
    /**
     * 是否为Windows或者Linux（Unix）文件分隔符<br>
     * Windows平台下分隔符为\，Linux（Unix）为/
     *
     * @param c 字符
     * @return 是否为Windows或者Linux（Unix）文件分隔符
     */
    public static boolean isFileSeparator(char c) {
        return SLASH == c || BACKSLASH == c;
    }

    /**
     * 下载文件名重新编码
     *
     * @param response 响应对象
     * @param realFileName 真实文件名
     * @return
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName)
            throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        StringBuilder contentDispositionValue = new StringBuilder();
        contentDispositionValue.append("attachment; filename=").append(percentEncodedFileName).append(";")
                .append("filename*=").append("utf-8''").append(percentEncodedFileName);

        response.setHeader("Content-disposition", contentDispositionValue.toString());
        response.setHeader("download-filename", percentEncodedFileName);
    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    // 第二种方式
    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file", MediaType.APPLICATION_OCTET_STREAM_VALUE, true,
                "cmafile" + UUID.randomUUID() + "." + FileTypeUtils.getFileType(file));
        try (InputStream input = new FileInputStream(file); OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }
        return new PlssMultipartFile(item);
    }


    /**
     * byte 转换为 MultipartFile
     *
     * @param data 数据
     * @param fileName 文件名
     * @return
     */
    public static MultipartFile getMultipartFile(byte[] data, String fileName) {
        OutputStream outputStream = null;
        MultipartFile multipartFile;
        StringJoiner joiner = new StringJoiner(File.separator);
        String suffix = FileUtil.getSuffix(fileName);
        joiner.add("/home/<USER>").add("multipartFile").add(System.currentTimeMillis() + SymbolPool.DOT + suffix);
        String absolutePath = joiner.toString();
        File tempFile = FileUtil.touch(absolutePath);
        try (InputStream input = Files.newInputStream(tempFile.toPath())) {
            // 把 byte 转换为 File 文件
            FileUtil.writeBytes(data, tempFile);
            //转换为diskFileItem
            FileItem fileItem = new DiskFileItem(fileName, Files.probeContentType(tempFile.toPath()), false,
                    tempFile.getName(), (int) tempFile.length(), tempFile.getParentFile());
            outputStream = fileItem.getOutputStream();
            IOUtils.copy(input, outputStream);
            multipartFile = new PlssMultipartFile(fileItem);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + fileName);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception ignored) {
                }
            }
            // 删除这个 File
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }

        return multipartFile;
    }

    public static List<File> getAllFileAndChildrenFile(String dirFilePath, String filename, String[] suffix) {
        List<File> files = Lists.newArrayList();
        if (StringUtils.isBlank(dirFilePath)) {
            return null;
        }

        return getAllFileAndChildrenFile(FileUtils.getFile(dirFilePath), filename, suffix, files);
    }

    public static List<File> getAllFileAndChildrenFile(String dirFilePath, String filename) {
        List<File> files = Lists.newArrayList();
        if (StringUtils.isBlank(dirFilePath)) {
            return null;
        }

        return getAllFileAndChildrenFile(FileUtils.getFile(dirFilePath), filename, null, files);
    }

    private static List<File> getAllFileAndChildrenFile(File dirFile, String filename, String[] suffix,
            List<File> files) {
        // 如果文件夹不存在或着不是文件夹，则返回 null
        if (Objects.isNull(dirFile) || !dirFile.exists() || dirFile.isFile()) {
            return null;
        }

        File[] childrenFiles = dirFile.listFiles();
        if (Objects.isNull(childrenFiles) || childrenFiles.length == 0) {
            return null;
        }

        for (File childFile : childrenFiles) {
            // 如果是文件，直接添加到结果集合
            if (childFile.isFile()) {
                if (ObjectUtil.isEmpty(suffix) && StringUtils.isBlank(filename)) {
                    files.add(childFile);
                } else {
                    if (suffix != null && suffix.length > 0) {
                        for (int i = 0; i < suffix.length; i++) {
                            if (childFile.getName().contains(suffix[i]) && childFile.getName().contains(filename)) {
                                files.add(childFile);
                            }
                        }
                    } else {
                        if (childFile.getName().contains(filename)) {
                            files.add(childFile);
                        }
                    }

                }
            }
            //以下几行代码取消注释后可以将所有子文件夹里的文件也获取到列表里
            else {
                // 如果是文件夹，则将其内部文件添加进结果集合
                getAllFileAndChildrenFile(childFile, filename, suffix, files);

            }
        }
        return files;
    }

    public static void unZipDir(File dir) {
        if (dir.exists()) {
            //抽象路径名数组，这些路径名表示此抽象路径名表示的目录中的文件和目录。
            File[] files = dir.listFiles();
            if (null != files) {
                for (int i = 0; i < files.length; i++) {
                    if (files[i].isDirectory()) {
                        unZipDir(files[i]);
                    } else {
                        System.out.println(files[i]);
                        if (files[i].getName().contains(".zip")) {
                            ZipUtil.unzip(files[i], Charset.forName("GBK"));
                        }
                        unZipDir(FileUtils.getFile(files[i].getPath().replace(".zip", "")));
                    }
                }
            }
        } else {
            System.out.println("文件不存在！");
        }
    }

    /**
     * 检查指定目录及其子目录下是否存在文件。
     *
     * @param directoryPath 目录路径
     * @return true 如果存在文件，false 否则
     */
    public static boolean checkFilesExist(String directoryPath) {
//        if (!ValidatorUtils.isValidPath(directoryPath)) {
//            return false;
//        }
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            return false;
        }

        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    // 找到文件，立即返回true
                    return true;
                }
                if (file.isDirectory() && (checkFilesExist(file.getAbsolutePath()))) {
                    // 递归遍历子目录
                    return true;
                }
            }
        }
        // 没有找到文件，返回false
        return false;
    }

    private static final PlssDiskFileItemFactory FILE_ITEM_FACTORY = new PlssDiskFileItemFactory();

    public static FileItem getFileItem(Response response) {
        return getFileItem(response, null);
    }

    public static FileItem getFileItem(InputStream inputStream, String filename) {
        FileItem item = FILE_ITEM_FACTORY.createItem(filename);
        try {
            IOUtils.copy(inputStream, item.getOutputStream());
            return item;
        } catch (IOException e) {
            throw new ServiceException("下载文件失败", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 创建空的fileItem（后续自行写入内容）
     * @param filename
     * @return
     */
    public static FileItem createEmptyFileItem(String filename){
        if(StringUtils.isEmpty(filename)){
            throw new ServiceException("请传入文件名");
        }
        return FILE_ITEM_FACTORY.createItem(filename);
    }

    public static FileItem getFileItem(Response response, String filename) {
        checkResponse(response);
        if (StringUtils.isEmpty(filename)) {
            filename = attachFilename(response);
        }
        FileItem item = FILE_ITEM_FACTORY.createItem(filename);
        try (InputStream inputStream = response.body().asInputStream()){
            IOUtils.copy(inputStream, item.getOutputStream());
            return item;
        } catch (IOException e) {
            throw new ServiceException("下载文件失败", e);
        }
    }

    private static void checkResponse(Response response) {
        if (Objects.isNull(response) || response.status() != 200) {
            throw new ServiceException("下载文件失败，响应为空或者响应状态码不为200");
        }
        Collection<String> strings = response.headers().get("Content-Type");
        if (CollectionUtil.isEmpty(strings) || strings.contains(MediaType.APPLICATION_JSON_VALUE)) {
            throw new ServiceException("下载文件失败");
        }
    }

    public static   String attachFilename(Response response) {
        String filename = null;
        Map<String, Collection<String>> headers = response.headers();
        Collection<String> plssFileName = headers.get("Plss-File-Name");
        if (CollectionUtil.isNotEmpty(plssFileName)) {
            for (String s : plssFileName) {
                filename = s;
                break;
            }
        }
        if (StringUtils.isNotEmpty(filename)) {
            return filename;
        }
        Collection<String> strings = headers.get("Content-Disposition");
        filename = attachFilenameFromHeaderValues(strings);
        if (StringUtils.isEmpty(filename)) {
            return null;
        }
        return URLDecoder.decode(filename, StandardCharsets.UTF_8);
    }

    private static String attachFilenameFromHeaderValues(Collection<String> strings) {
        if (CollectionUtil.isEmpty(strings)) {
            return null;
        }
        String str = "filename=";
        for (String string : strings) {
            int i = string.indexOf(str);
            if (i >= 0) {
                return string.substring(i + str.length());
            }
        }
        return null;
    }

    public static File getFile(File directory, String... names) {
//        for (String name : names) {
//            if (!ValidatorUtils.isValidFileName(name)) {
//                throw new IllegalArgumentException("Names is invalid");
//            }
//        }
        return org.apache.commons.io.FileUtils.getFile(directory, names);
    }
    public static File getFile(String... names) {
//        for (String name : names) {
//            if (!ValidatorUtils.isValidFileName(name)) {
//                throw new IllegalArgumentException("Names is invalid");
//            }
//        }
        return org.apache.commons.io.FileUtils.getFile(names);
    }
    public static FileInputStream openInputStream(File file) throws IOException {
        return org.apache.commons.io.FileUtils.openInputStream(file);
    }
    public static FileOutputStream openOutputStream(File file) throws IOException {
        return org.apache.commons.io.FileUtils.openOutputStream(file);
    }
    public static void copyInputStreamToFile(InputStream source, File destination) throws IOException {
        org.apache.commons.io.FileUtils.copyInputStreamToFile(source, destination);
    }

    public static Collection<File> listFiles(File directory, boolean recursive, String... extensions) {
        return org.apache.commons.io.FileUtils.listFiles(directory, extensions, recursive);
    }

    /**
     * 将字节数组写入文件，并返回File对象。
     *
     * @param bytes 字节数组
     * @param fileName 文件名
     * @return 返回创建的File对象
     * @throws IOException 如果写入文件时发生IO异常
     */
    public static File createFileFromBytes(byte[] bytes, String fileName) throws IOException {
        // 确保文件名包含路径，如果没有，则使用当前目录
        File file = new File(fileName);
        // 使用FileOutputStream将字节写入文件
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(bytes);
        }
        // 返回创建的File对象
        return file;
    }

}
