package com.suwell.plss.framework.security.interceptor;

import com.suwell.plss.framework.common.constant.SecurityConstants;
import com.suwell.plss.framework.common.constant.TokenConstants;
import com.suwell.plss.framework.common.context.SecurityContextHolder;
import com.suwell.plss.framework.common.utils.ServletUtils;
import com.suwell.plss.framework.common.utils.StringUtils;
import com.suwell.plss.framework.security.auth.AuthUtil;
import com.suwell.plss.framework.security.utils.SecurityUtils;
import com.suwell.plss.system.api.domain.LoginUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 * @date 2023年4月20日
 */
@Slf4j
public class HeaderInterceptor implements AsyncHandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));
        SecurityContextHolder.setTenantId(ServletUtils.getHeader(request, SecurityConstants.TENANT_ID));
        SecurityContextHolder.setSecretKey(ServletUtils.getHeader(request, SecurityConstants.SECRET_KEY));
        SecurityContextHolder.setToken(ServletUtils.getHeader(request, TokenConstants.AUTHENTICATION));
        String token = SecurityUtils.getToken();
        if (StringUtils.isNotEmpty(token)) {
            LoginUser loginUser = AuthUtil.getLoginUser(token);
            if (Objects.nonNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        SecurityContextHolder.remove();
    }
}
